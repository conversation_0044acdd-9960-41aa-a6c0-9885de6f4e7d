"""
Gacha系統升星服務
實現卡片升星功能，包括計算消耗資源、成功率、升星操作等
"""

import random
from typing import Any, Dict

from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import (
    CardNotFoundError,
    GachaRuntimeError,
    InsufficientBalanceError,
    InsufficientCardQuantityError,
    MaxStarLevelReachedError,
)
from gacha.models.models import Card
from gacha.repositories.card import card_encyclopedia_repository
from gacha.repositories.collection import user_collection_repository
from gacha.services import economy_service, market_stats_maintenance_service
from utils.logger import logger

# --- 模組級常量 ---

BASE_OIL_COST = 50
OIL_COST_PER_STAR = 50
HIGH_STAR_COST_MULTIPLIER = 2.5
DUPLICATE_COST_PER_ENHANCE = 1
RARITY_COST_MODIFIER: Dict[str, Dict[int, float]] = {
    "main": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0},
    "special": {1: 1.5, 2: 2.0, 3: 3.0, 4: 5.0, 5: 8.0},
    "summer": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0},
    "vd": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0},
    "special_maid": {1: 1.5, 2: 2.0, 3: 3.0, 4: 5.0, 5: 8.0},
    "hololive": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0},
    "ua": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0},
    "ptcg": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0},
    "wixoss": {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0},
}
SUCCESS_RATE_CONFIG = {
    (0, 5): 90.0,
    (6, 10): 80.0,
    (11, 15): 70.0,
    (16, 20): 60.0,
    (21, 24): 40.0,
    (25, 27): 25.0,
    (28, 30): 15.0,
    (31, 35): 8.0,
}

# --- 模組級函數 ---


def _calculate_enhancement_cost(
    current_star_level: int, rarity: int, pool_type: str
) -> Dict[str, int]:
    """計算升級成本 (使用數字稀有度和卡池類型)"""
    base_oil_cost = BASE_OIL_COST + current_star_level * OIL_COST_PER_STAR
    if current_star_level >= 25:
        base_oil_cost = int(base_oil_cost * HIGH_STAR_COST_MULTIPLIER)
    pool_modifiers = RARITY_COST_MODIFIER.get(
        pool_type, RARITY_COST_MODIFIER.get("main", {})
    )
    rarity_modifier = pool_modifiers.get(rarity, 1.0)
    oil_cost = int(base_oil_cost * rarity_modifier)
    duplicate_cost = DUPLICATE_COST_PER_ENHANCE
    return {"oil": oil_cost, "duplicates": duplicate_cost}


def _calculate_success_rate(current_star_level: int) -> float:
    """計算成功率"""
    for level_range, rate in SUCCESS_RATE_CONFIG.items():
        if level_range[0] <= current_star_level <= level_range[1]:
            return rate
    max_defined_level = max(
        (level_range[1] for level_range in SUCCESS_RATE_CONFIG.keys())
    )
    if current_star_level > max_defined_level:
        for level_range, rate in SUCCESS_RATE_CONFIG.items():
            if level_range[1] == max_defined_level:
                return rate
    return 5.0


async def check_enhancement_possibility(user_id: int, card_id: int) -> Dict[str, Any]:
    """檢查卡片升星的可能性，如果不可能則拋出異常

    Returns:
        Dict[str, Any]: 包含卡片數據、當前星級、成本和成功率的字典

    Raises:
        CardNotFoundError: 找不到指定卡片
        MaxStarLevelReachedError: 卡片已達到最高星級
        InsufficientBalanceError: 油幣不足
        InsufficientCardQuantityError: 重複卡不足
    """
    card_data = await user_collection_repository.get_user_card(user_id, card_id)
    if not card_data:
        logger.warning("找不到用戶 %s 的卡片 %s", user_id, card_id)
        raise CardNotFoundError(f"找不到指定卡片 (ID: {card_id})", card_id=card_id)

    current_star_level = card_data.star_level
    max_star_level = getattr(Card, "MAX_STAR_LEVEL", 35)
    if current_star_level >= max_star_level:
        raise MaxStarLevelReachedError(max_level=max_star_level)

    if not card_data.card:
        # This case should ideally not happen if get_user_card works correctly
        raise CardNotFoundError(f"找不到卡片詳細資料 (ID: {card_id})", card_id=card_id)

    pool_type = (
        card_data.card.pool_type if card_data.card.pool_type is not None else "main"
    )

    costs = _calculate_enhancement_cost(
        current_star_level, card_data.card.rarity, pool_type
    )
    balance_info = await economy_service.get_balance(user_id)
    oil_balance = balance_info.get("balance", 0)
    if oil_balance < costs["oil"]:
        raise InsufficientBalanceError(
            message=f"油幣餘額不足，升星需要 {costs['oil']:,}，但您只有 {oil_balance:,}。",
            required=costs["oil"],
            current=oil_balance,
        )

    required_total_cards = costs["duplicates"] + 1
    if card_data.quantity < required_total_cards:
        raise InsufficientCardQuantityError(
            message=f"重複卡不足，升星需要消耗 {costs['duplicates']} 張重複卡（共需 {required_total_cards} 張），但你只有 {card_data.quantity} 張。",
            required=required_total_cards,
            current=card_data.quantity,
        )

    success_rate = _calculate_success_rate(current_star_level)
    return {
        "current_star_level": current_star_level,
        "costs": costs,
        "success_rate": success_rate,
        "card_data": card_data,
    }


async def enhance_card(user_id: int, card_id: int) -> Dict[str, Any]:
    """執行卡片升星操作

    Returns:
        Dict[str, Any]: 包含升星結果的字典

    Raises:
        CardNotFoundError: 找不到指定卡片
        MaxStarLevelReachedError: 卡片已達到最高星級
        InsufficientBalanceError: 油幣不足
        InsufficientCardQuantityError: 重複卡不足
        GachaRuntimeError: 發生非預期的運行時錯誤
    """
    # 檢查升星可能性，如果不可能會拋出異常
    enhancement_info = await check_enhancement_possibility(user_id, card_id)

    card_data = enhancement_info["card_data"]
    current_star_level = enhancement_info["current_star_level"]
    costs = enhancement_info["costs"]
    success_rate = enhancement_info["success_rate"]
    is_star_enhanced = False
    new_star_level = current_star_level
    remaining_oil = 0

    pool = get_pool()
    if pool is None:
        raise GachaRuntimeError("資料庫連線池未初始化")
    redis_client = get_redis_client()
    if redis_client is None:
        raise GachaRuntimeError("Redis client is not initialized.")

    async with pool.acquire() as conn:
        async with conn.transaction():
            # 1. 扣除資源
            remaining_oil = await economy_service.award_oil(
                user_id=user_id,
                amount=-costs["oil"],
                transaction_type="card:enhance_star",
                reason=f"Enhance star for card {card_id}",
                connection=conn,
            )
            remove_card_result = await user_collection_repository.remove_card(
                redis_client, user_id, card_id, conn, costs["duplicates"]
            )
            actual_remaining_quantity = remove_card_result["remaining_quantity"]

            # 2. 防禦性檢查
            if actual_remaining_quantity < 1:
                logger.error(
                    "star_enhancement_service: Card %s (user %s) quantity became %s after removing duplicates for enhancement. It should be >= 1.",
                    card_id,
                    user_id,
                    actual_remaining_quantity,
                )
                # 雖然理論上 check_enhancement_possibility 已經擋住，但這裡多一層保護
                raise GachaRuntimeError(
                    f"升星失敗：消耗素材後，目標卡片 {card_id} 數量不足 (剩餘 {actual_remaining_quantity})。"
                )

            # 3. 執行升星
            roll = random.random() * 100
            is_star_enhanced = roll < success_rate

            if is_star_enhanced:
                new_star_level = current_star_level + 1
                await user_collection_repository.update_star_level(
                    user_id, card_id, new_star_level, connection=conn
                )
                await card_encyclopedia_repository.update_highest_star(
                    card_id, user_id, new_star_level, connection=conn
                )

    # 4. 更新市場統計 (異步)
    if costs["duplicates"] > 0 and market_stats_maintenance_service:
        updates_payload = [(card_id, -costs["duplicates"])]
        await market_stats_maintenance_service.schedule_total_owned_update(
            updates_payload
        )

    # 5. 準備回傳結果
    status_text = "成功" if is_star_enhanced else "失敗"
    level_change_text = (
        f"從 {current_star_level} 星升級到 {new_star_level} 星！"
        if is_star_enhanced
        else f"星級保持在 {current_star_level} 星。"
    )
    message = (
        f"升星{status_text}！{card_data.card.name} {level_change_text}"
        f"消耗了 {costs['oil']:,} 油幣和 {costs['duplicates']} 張重複卡。"
    )

    return {
        "message": message,
        "star_enhanced": is_star_enhanced,
        "old_star_level": current_star_level,
        "new_star_level": new_star_level,
        "costs": costs,
        "remaining_oil": remaining_oil,
    }
