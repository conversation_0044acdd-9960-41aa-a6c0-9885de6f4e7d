"""
Pioneer System Discord Cog
開拓者指令系統的 Discord 介面
"""

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.modules import offline_module
from pioneer.views.pioneer_views import PioneerMainView
from utils.logger import logger


class PioneerCog(commands.Cog):
    """開拓者指令系統 Discord Cog"""

    def __init__(self, bot):
        self.bot = bot
        logger.info("PioneerCog initialized")

    async def cog_load(self):
        """Cog 載入時初始化"""
        logger.info("Pioneer System Cog 載入完成")

    async def cog_unload(self):
        """Cog 卸載時清理"""
        logger.info("Pioneer System Cog 卸載")

    @app_commands.command(name="pioneer", description="開啟開拓者指令主面板")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def pioneer(self, interaction: discord.Interaction):
        """開拓者指令主面板"""
        assert game_data is not None
        await interaction.response.defer(thinking=False)
        assert game_data is not None
        user_id = interaction.user.id

        # 初始化用戶資料
        await repositories.create_pioneer_profile(user_id)
        profile = await repositories.get_pioneer_profile(user_id)
        is_new_player = await self._is_new_player(user_id)

        # 處理離線產出並顯示摘要
        await self._handle_offline_production(interaction, user_id)

        # 創建主面板
        view = PioneerMainView(user_id, game_data, self.bot)
        embed = (
            await self._create_newbie_guide_embed(interaction, profile)
            if is_new_player
            else await view.create_main_embed(interaction)
        )

        await interaction.followup.send(embed=embed, view=view)

    async def _handle_offline_production(
        self, interaction: discord.Interaction, user_id: int
    ) -> None:
        """處理離線產出並顯示摘要"""
        assert game_data is not None
        assert game_data is not None
        offline_result = await offline_module.process_user_offline_production(user_id)

        # 如果有離線產出，顯示摘要
        if offline_result["facilities_processed"] > 0:
            summary_parts = [
                "🕐 **離線產出摘要**",
                f"處理設施: {offline_result['facilities_processed']} 個",
                f"執行輪次: {offline_result['total_cycles']} 次",
            ]

            if offline_result["items_produced"]:
                summary_parts.append("**物品產出:**")
                for item_id, quantity in offline_result["items_produced"].items():
                    item_config = game_data.get_item_config(item_id)
                    item_name = item_config.name if item_config else item_id
                    summary_parts.append(f"• {item_name}: +{quantity}")

            if offline_result["earnings_generated"] > 0:
                summary_parts.append(
                    f"**收益產生:** +{offline_result['earnings_generated']:,} 油幣"
                )

            summary_embed = discord.Embed(
                title="🌟 歡迎回來！",
                description="\n".join(summary_parts),
                color=0x00FF00,
            )
            await interaction.followup.send(embed=summary_embed)

    async def _is_new_player(self, user_id: int) -> bool:
        """檢查是否為新手玩家"""
        # 檢查倉庫物品數量（最快的指標）
        warehouse_items = await repositories.get_user_warehouse(user_id)
        if warehouse_items:
            return False

        # 檢查設施數量
        facilities = await repositories.get_user_facilities(user_id)
        if facilities:
            return False

        # 檢查技能進度
        skills = await repositories.get_user_skills(user_id)
        return not any(skill.level > 1 or skill.xp > 0 for skill in skills)

    async def _create_newbie_guide_embed(
        self, interaction: discord.Interaction, profile
    ) -> discord.Embed:
        """創建新手引導面板（已重構為任務驅動）"""
        assert game_data is not None
        user = interaction.user
        newbie_story = game_data.get_config("stories").get("newbie_story", {})

        embed = discord.Embed(
            title=newbie_story.get("title", "🌟 歡迎來到開拓者世界！"), color=0x4A90E2
        )

        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        description_parts = await self._build_newbie_description(
            user, profile, newbie_story
        )
        embed.description = "\n\n".join(description_parts)
        embed.set_footer(text=newbie_story.get("footer", "💪 從零開始，重新崛起！"))

        return embed

    async def _build_newbie_description(
        self, user, profile, newbie_story: dict
    ) -> list:
        """構建新手引導描述內容（已重構為任務驅動）"""
        assert game_data is not None
        parts = []

        # 故事背景
        background = newbie_story.get("background", "").format(
            player_name=user.display_name
        )
        if background:
            parts.append(background)

        # 動態獲取下一個新手任務
        active_quests = await repositories.get_user_active_quests(user.id)
        newbie_quests = [q for q in active_quests if q.quest_id.startswith("newbie_")]

        next_task_text = ""
        if newbie_quests:
            # 假設新手任務是按順序完成的，取第一個
            quest, config = (
                newbie_quests[0],
                game_data.get_task_config(newbie_quests[0].quest_id),
            )
            if quest and config:
                from pioneer.cogs.task_cog import TaskCog  # 借用格式化工具

                progress_bar = TaskCog._format_progress_bar(
                    quest.progress, quest.target
                )
                reward_str = TaskCog._format_rewards(config.rewards)

                next_task_text = (
                    f"🎯 **你的下一個目標: {config.name}**\n"
                    f"*{config.description}*\n"
                    f"`{quest.progress:,} / {quest.target:,}` {progress_bar}\n"
                    f"獎勵: {reward_str}"
                )
        else:
            next_task_text = (
                "🎉 **新手引導完成！**\n"
                "你已經掌握了基本生存技能，現在可以自由探索了！\n"
                "試試 `/tasks` 查看每日任務，或者使用 `/manage` 來管理你的設施。"
            )

        parts.append(next_task_text)

        # 小提示
        tips = newbie_story.get("tips", [])
        if tips:
            parts.append("💡 **小提示**\n" + "\n".join(tips))

        return parts


async def setup(bot):
    """設置 Cog"""
    await bot.add_cog(PioneerCog(bot))
