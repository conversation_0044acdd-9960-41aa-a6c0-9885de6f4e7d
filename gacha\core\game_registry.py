"""
遊戲註冊系統
統一管理所有小遊戲的配置和元數據
"""

from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional


def _get_minigame_emoji(game_type: str, fallback: str = "❓") -> str:
    """安全獲取小遊戲emoji，避免循環導入"""
    try:
        from config.app_config import get_minigame_emoji

        return get_minigame_emoji(game_type, fallback)
    except ImportError:
        # 如果配置還沒載入，使用fallback
        return fallback


@dataclass
class GameStatConfig:
    """遊戲統計配置"""

    value: str
    name: str
    emoji: str
    query_builder: Optional[Callable] = None
    order_by_builder: Optional[Callable] = None


@dataclass
class GameConfig:
    """遊戲配置"""

    game_type: str
    name: str
    emoji: str
    color: int
    description: str
    category: str = "games"

    # 統計相關
    stats_updater: Optional[Callable] = None
    custom_stats: Optional[List[GameStatConfig]] = None

    def __post_init__(self):
        if self.custom_stats is None:
            self.custom_stats = []


class GameRegistry:
    """遊戲註冊中心"""

    _games: Dict[str, GameConfig] = {}
    _common_stats: List[GameStatConfig] = [
        GameStatConfig("total_profit_loss", "總盈虧", "💰"),
        GameStatConfig("total_wins", "總勝場", "🏆"),
        GameStatConfig("total_games", "總場次", "🎮"),
        GameStatConfig("win_rate", "勝率", "📊"),
        GameStatConfig("consecutive_wins", "最高連勝", "🔥"),
        GameStatConfig("max_win", "單場最高獲利", "💎"),
        GameStatConfig("max_loss", "單場最大虧損", "💸"),
    ]

    @classmethod
    def register_game(cls, config: GameConfig):
        """註冊遊戲"""
        cls._games[config.game_type] = config

    @classmethod
    def get_game(cls, game_type: str) -> Optional[GameConfig]:
        """獲取遊戲配置"""
        return cls._games.get(game_type)

    @classmethod
    def get_all_games(cls) -> Dict[str, GameConfig]:
        """獲取所有遊戲配置"""
        return cls._games.copy()

    @classmethod
    def get_games_list(cls) -> List[Dict[str, str]]:
        """獲取遊戲列表（用於UI）"""
        return [
            {"value": game.game_type, "name": game.name, "emoji": game.emoji}
            for game in cls._games.values()
        ]

    @classmethod
    def get_game_stats(cls, game_type: str) -> List[Dict[str, str]]:
        """獲取遊戲統計列表"""
        game = cls.get_game(game_type)
        if not game:
            return []

        # 組合通用統計和遊戲特定統計
        all_stats = cls._common_stats + (game.custom_stats or [])

        return [
            {"value": stat.value, "name": stat.name, "emoji": stat.emoji}
            for stat in all_stats
        ]

    @classmethod
    def get_game_types(cls) -> List[str]:
        """獲取所有遊戲類型"""
        return list(cls._games.keys())

    @classmethod
    def is_valid_game_type(cls, game_type: str) -> bool:
        """檢查是否為有效的遊戲類型"""
        return game_type in cls._games


# 統計更新函數
def _initialize_baccarat_stats(stats: Dict[str, Any]) -> None:
    """初始化百家樂統計"""
    stats.setdefault("banker_wins", 0)
    stats.setdefault("player_wins", 0)
    stats.setdefault("tie_wins", 0)
    stats.setdefault("banker_bets", 0)
    stats.setdefault("player_bets", 0)
    stats.setdefault("tie_bets", 0)


def _update_baccarat_bets(stats: Dict[str, Any], bet_on: Optional[str]) -> None:
    """更新百家樂下注次數"""
    if bet_on == "莊家":
        stats["banker_bets"] += 1
    elif bet_on == "閒家":
        stats["player_bets"] += 1
    elif bet_on == "和局":
        stats["tie_bets"] += 1


def _update_baccarat_wins(
    stats: Dict[str, Any], bet_on: Optional[str], winner: Optional[str]
) -> None:
    """更新百家樂獲勝次數"""
    if bet_on == winner:
        if winner == "莊家":
            stats["banker_wins"] += 1
        elif winner == "閒家":
            stats["player_wins"] += 1
        elif winner == "和局":
            stats["tie_wins"] += 1


def _calculate_baccarat_win_rates(stats: Dict[str, Any]) -> None:
    """計算百家樂勝率"""
    if stats["banker_bets"] > 0:
        stats["banker_win_rate"] = round(
            (stats["banker_wins"] / stats["banker_bets"]) * 100, 2
        )
    else:
        stats["banker_win_rate"] = 0.0

    if stats["player_bets"] > 0:
        stats["player_win_rate"] = round(
            (stats["player_wins"] / stats["player_bets"]) * 100, 2
        )
    else:
        stats["player_win_rate"] = 0.0

    if stats["tie_bets"] > 0:
        stats["tie_win_rate"] = round((stats["tie_wins"] / stats["tie_bets"]) * 100, 2)
    else:
        stats["tie_win_rate"] = 0.0


def update_baccarat_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新百家樂統計【只關心百家樂特定數據】"""
    _initialize_baccarat_stats(stats)

    bet_on = game_data.get("bet_on")
    winner = game_data.get("original_result")  # 使用原始賽果來判斷誰贏

    _update_baccarat_bets(stats, bet_on)
    _update_baccarat_wins(stats, bet_on, winner)
    _calculate_baccarat_win_rates(stats)

    return stats


def update_blackjack_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新21點統計【只關心21點特定數據】"""
    # 初始化21點特定統計
    stats.setdefault("blackjack_count", 0)
    stats.setdefault("bust_count", 0)
    stats.setdefault("dealer_bust_wins", 0)
    stats.setdefault("push_count", 0)
    stats.setdefault("double_down_count", 0)
    stats.setdefault("hit_count", 0)
    stats.setdefault("stand_count", 0)
    stats.setdefault("max_hand_value", 0)
    stats.setdefault("five_card_trick_count", 0)
    # 注意：連勝/連敗統計由通用系統自動處理，無需在這裡初始化

    # 更新基於遊戲結果的統計 - 使用詳細的原始結果
    original_result = game_data.get("original_result", game_data.get("result", ""))
    if original_result == "blackjack":
        stats["blackjack_count"] += 1
    elif original_result == "bust":
        stats["bust_count"] += 1
    elif original_result == "dealer_bust":
        stats["dealer_bust_wins"] += 1
    elif original_result == "push":
        stats["push_count"] += 1
    elif original_result == "five_card_trick":
        stats["five_card_trick_count"] += 1

    # 更新手牌點數
    player_hand_value = game_data.get("player_hand_value", 0)
    if player_hand_value > 0:
        stats["max_hand_value"] = max(stats["max_hand_value"], player_hand_value)

    # 更新動作統計
    hit_count = game_data.get("hit_count", 0)
    stand_count = game_data.get("stand_count", 0)
    if hit_count > 0:
        stats["hit_count"] += hit_count
    if stand_count > 0:
        stats["stand_count"] += stand_count

    return stats


def _initialize_dice_stats(stats: Dict[str, Any]) -> None:
    """初始化三骰子遊戲統計"""
    stats.setdefault("big_choice_count", 0)
    stats.setdefault("small_choice_count", 0)
    stats.setdefault("triple_choice_count", 0)
    stats.setdefault("big_win_count", 0)
    stats.setdefault("small_win_count", 0)
    stats.setdefault("triple_win_count", 0)
    stats.setdefault("dice_frequency", {str(i): 0 for i in range(1, 7)})
    stats.setdefault(
        "total_frequency", {str(i): 0 for i in range(3, 19)}
    )  # 3骰子總和3-18
    stats.setdefault("triple_count", 0)  # 圍骰次數


def _get_dice_choice_key(game_type: str, choice: str) -> Optional[str]:
    """獲取骰子遊戲選擇鍵"""
    if game_type == "size":
        if choice == "big":
            return "big"
        elif choice == "small":
            return "small"
    elif game_type == "triple":
        return "triple"
    return None


def _update_dice_choice_stats(
    stats: Dict[str, Any], choice_key: str, is_win: bool
) -> None:
    """更新骰子選擇統計"""
    stats[f"{choice_key}_choice_count"] += 1
    if is_win:
        stats[f"{choice_key}_win_count"] += 1


def _update_dice_frequency_stats(
    stats: Dict[str, Any], dice_rolls: List[int], dice_total: int, is_triple: bool
) -> None:
    """更新骰子頻率統計"""
    # 更新骰子點數頻率（每個骰子）
    for dice_value in dice_rolls:
        if 1 <= dice_value <= 6:
            stats["dice_frequency"][str(dice_value)] += 1

    # 更新總點數頻率
    if 3 <= dice_total <= 18:
        stats["total_frequency"][str(dice_total)] += 1

    # 更新圍骰統計
    if is_triple:
        stats["triple_count"] += 1


def _calculate_dice_win_rates(stats: Dict[str, Any]) -> None:
    """計算骰子遊戲勝率"""
    if stats["big_choice_count"] > 0:
        stats["big_choice_win_rate"] = round(
            (stats["big_win_count"] / stats["big_choice_count"]) * 100, 2
        )
    else:
        stats["big_choice_win_rate"] = 0.0

    if stats["small_choice_count"] > 0:
        stats["small_choice_win_rate"] = round(
            (stats["small_win_count"] / stats["small_choice_count"]) * 100, 2
        )
    else:
        stats["small_choice_win_rate"] = 0.0

    if stats["triple_choice_count"] > 0:
        stats["triple_win_rate"] = round(
            (stats["triple_win_count"] / stats["triple_choice_count"]) * 100, 2
        )
    else:
        stats["triple_win_rate"] = 0.0


def update_dice_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新三骰子遊戲統計"""
    _initialize_dice_stats(stats)

    # 獲取遊戲數據
    game_type = game_data.get("game_type", "")
    choice = game_data.get("choice", "")
    dice_rolls = game_data.get("dice_rolls", [])
    dice_total = game_data.get("dice_total", 0)
    is_triple = game_data.get("is_triple", False)
    result = game_data.get("result", "lose")
    is_win = result == "win"

    # 更新選擇統計
    choice_key = _get_dice_choice_key(game_type, choice)
    if choice_key:
        _update_dice_choice_stats(stats, choice_key, is_win)

    # 更新頻率統計
    _update_dice_frequency_stats(stats, dice_rolls, dice_total, is_triple)

    # 計算勝率
    _calculate_dice_win_rates(stats)

    return stats


def update_mines_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新挖礦統計【只關心挖礦特定數據】"""
    # 初始化挖礦特定統計
    stats.setdefault("mine_configs", {})
    stats.setdefault("max_multiplier", 0)
    stats.setdefault("avg_tiles_revealed", 0)
    stats.setdefault("max_tiles_revealed", 0)
    stats.setdefault("special_coin_found", 0)
    stats.setdefault("special_star_found", 0)
    stats.setdefault("early_cashout_count", 0)
    stats.setdefault("total_games_for_avg", 0)
    stats.setdefault("total_tiles_revealed", 0)
    # 注意：連勝/連敗統計由通用系統自動處理，無需在這裡初始化

    mine_count = str(game_data.get("mine_count", 3))
    tiles_revealed = game_data.get("tiles_revealed", 0)
    multiplier = game_data.get("multiplier", 1.0)
    result = game_data.get("result", "lose")
    is_win = result == "win"

    # 更新地雷配置統計
    if mine_count not in stats["mine_configs"]:
        stats["mine_configs"][mine_count] = {"wins": 0, "games": 0, "avg_multiplier": 0}

    config = stats["mine_configs"][mine_count]
    config["games"] += 1
    if is_win:
        config["wins"] += 1

    # 更新平均倍數
    if config["games"] > 0:
        config["avg_multiplier"] = round(
            (config["avg_multiplier"] * (config["games"] - 1) + multiplier)
            / config["games"],
            2,
        )

    # 更新挖掘格數統計
    stats["total_tiles_revealed"] += tiles_revealed
    stats["total_games_for_avg"] += 1
    stats["avg_tiles_revealed"] = round(
        stats["total_tiles_revealed"] / stats["total_games_for_avg"], 2
    )
    stats["max_tiles_revealed"] = max(stats["max_tiles_revealed"], tiles_revealed)

    # 更新最高倍數
    stats["max_multiplier"] = max(stats["max_multiplier"], multiplier)

    # 更新特殊物品發現次數
    if game_data.get("special_coin_found", False):
        stats["special_coin_found"] += 1
    if game_data.get("special_star_found", False):
        stats["special_star_found"] += 1

    # 更新提前結算次數
    if game_data.get("early_cashout", False):
        stats["early_cashout_count"] += 1

    return stats


def update_tower_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新爬塔統計【只關心爬塔特定數據】"""
    # 初始化爬塔特定統計
    stats.setdefault("difficulty_stats", {})
    stats.setdefault("max_level_reached", 0)
    stats.setdefault("avg_levels_completed", 0.0)
    stats.setdefault("cashout_count", 0)
    stats.setdefault("tnt_hit_count", 0)
    stats.setdefault("full_completion_count", 0)
    stats.setdefault("total_games_for_avg", 0)
    stats.setdefault("total_levels_completed", 0)
    # 注意：連勝/連敗統計由通用系統自動處理，無需在這裡初始化

    # 獲取遊戲數據
    difficulty = game_data.get("difficulty", "easy")
    levels_completed = game_data.get("levels_completed", 0)
    max_level_reached = game_data.get("max_level_reached", 0)
    hit_tnt = game_data.get("hit_tnt", False)
    early_cashout = game_data.get("early_cashout", False)
    result = game_data.get("result", "lose")
    is_win = result == "win"

    # 判斷是否完成整塔：直接使用 early_cashout 數據
    full_tower_completion = not early_cashout and not hit_tnt

    # 更新難度統計
    if difficulty not in stats["difficulty_stats"]:
        stats["difficulty_stats"][difficulty] = {
            "games": 0,
            "wins": 0,
            "cashouts": 0,
            "full_completions": 0,
            "avg_levels": 0.0,
            "max_level": 0,
            "total_levels": 0,
        }

    difficulty_stat = stats["difficulty_stats"][difficulty]
    difficulty_stat["games"] += 1
    difficulty_stat["total_levels"] += levels_completed
    difficulty_stat["max_level"] = max(difficulty_stat["max_level"], max_level_reached)

    if is_win:
        difficulty_stat["wins"] += 1
    if full_tower_completion:
        difficulty_stat["full_completions"] += 1
    # 統一使用 early_cashout 字段
    if early_cashout:
        difficulty_stat["cashouts"] += 1

    # 計算該難度的平均完成層數
    if difficulty_stat["games"] > 0:
        difficulty_stat["avg_levels"] = round(
            difficulty_stat["total_levels"] / difficulty_stat["games"], 2
        )

    # 更新全局統計
    stats["max_level_reached"] = max(stats["max_level_reached"], max_level_reached)
    stats["total_levels_completed"] += levels_completed
    stats["total_games_for_avg"] += 1

    # 計算平均完成層數
    if stats["total_games_for_avg"] > 0:
        stats["avg_levels_completed"] = round(
            stats["total_levels_completed"] / stats["total_games_for_avg"], 2
        )

    # 更新特殊事件統計
    if hit_tnt:
        stats["tnt_hit_count"] += 1
    if full_tower_completion:
        stats["full_completion_count"] += 1
    # 統一使用 early_cashout 字段（與 mines 遊戲一致）
    if early_cashout:
        stats["cashout_count"] += 1

    return stats


def update_slot_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新拉霸機遊戲統計【只關心拉霸機特定數據】"""

    # 初始化拉霸機特定統計
    stats.setdefault("jackpot_count", 0)  # 全螢幕 (9個相同符號) 次數
    stats.setdefault("max_consecutive_symbols", 0)  # 最大連續符號數 (最多3個)
    stats.setdefault("winning_lines_total", 0)  # 總獲勝線數
    stats.setdefault("rtp_percentage", 0.0)  # RTP 百分比
    stats.setdefault("rtp_total_bet", 0)  # RTP 計算用總下注
    stats.setdefault("rtp_total_payout", 0)  # RTP 計算用總賠付
    # 注意：連勝/連敗統計由通用系統自動處理，無需在這裡初始化

    # 符號統計 (基於當前 8 個符號)
    stats.setdefault(
        "symbol_hits",
        {
            "seven": 0,
            "clover": 0,
            "diamond": 0,
            "bell": 0,
            "cherry": 0,
            "grapes": 0,
            "watermelon": 0,
            "lemon": 0,
        },
    )

    # 獲勝類型統計 (移除重複，只保留這個)
    stats.setdefault(
        "win_types",
        {
            "3_diag": 0,  # 3個對角線
            "3_straight": 0,  # 3個直線
            "full_screen": 0,  # 全螢幕
        },
    )

    # 獲取遊戲數據
    bet = game_data.get("bet", 0)
    payout = game_data.get("payout", 0)
    winning_lines = game_data.get("winning_lines", 0)
    max_consecutive = game_data.get("max_consecutive", 0)
    is_jackpot = game_data.get("is_jackpot", False)
    diagonal_wins = game_data.get("diagonal_wins", 0)
    straight_wins = game_data.get("straight_wins", 0)
    symbol_counts = game_data.get("symbol_counts", {})

    # 更新基本統計
    stats["rtp_total_bet"] += bet
    stats["rtp_total_payout"] += payout
    stats["winning_lines_total"] += winning_lines
    stats["max_consecutive_symbols"] = max(
        stats["max_consecutive_symbols"], max_consecutive
    )

    # 更新獲勝類型統計 (統一在 win_types 中，避免重複)
    if is_jackpot:
        stats["jackpot_count"] += 1
        stats["win_types"]["full_screen"] += 1

    # 只在 win_types 中統計，不重複
    stats["win_types"]["3_diag"] += diagonal_wins
    stats["win_types"]["3_straight"] += straight_wins

    # 更新符號統計 (使用新的 symbol_counts 數據)
    if symbol_counts:
        for symbol, count in symbol_counts.items():
            if symbol in stats["symbol_hits"]:
                stats["symbol_hits"][symbol] += count

    # 計算 RTP 百分比
    if stats["rtp_total_bet"] > 0:
        stats["rtp_percentage"] = round(
            (stats["rtp_total_payout"] / stats["rtp_total_bet"]) * 100, 2
        )

    return stats


def update_spin_wheel_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新轉盤遊戲統計【只關心轉盤特定數據】"""
    # 初始化轉盤特定統計
    stats.setdefault("risk_stats", {})
    stats.setdefault(
        "result_type_counts", {"small_win": 0, "medium_win": 0, "big_win": 0, "lose": 0}
    )

    # 獲取遊戲數據
    risk_level = game_data.get("risk", "未知")  # 統一使用 'risk'
    result_type = game_data.get("result_type", "lose")
    is_win = game_data.get("profit", 0) > 0

    # 更新風險等級統計
    if risk_level not in stats["risk_stats"]:
        stats["risk_stats"][risk_level] = {"games": 0, "wins": 0}

    risk_stat = stats["risk_stats"][risk_level]
    risk_stat["games"] += 1
    if is_win:
        risk_stat["wins"] += 1

    # 更新結果類型統計 (單一事實來源)
    if result_type in stats["result_type_counts"]:
        stats["result_type_counts"][result_type] += 1

    return stats


# 註冊所有遊戲
GameRegistry.register_game(
    GameConfig(
        game_type="baccarat",
        name="百家樂",
        emoji=_get_minigame_emoji("baccarat", "🎴"),
        color=********,  # 0xC82536 (Red)
        description="按照百家樂遊戲統計數據排名的玩家",
        stats_updater=update_baccarat_stats,
        custom_stats=[
            GameStatConfig("banker_win_rate", "莊家勝率", "📈"),
            GameStatConfig("player_win_rate", "閒家勝率", "📉"),
            GameStatConfig("tie_win_rate", "和局勝率", "📊"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="blackjack",
        name="21點 (Blackjack)",
        emoji=_get_minigame_emoji("blackjack", "🃏"),
        color=9442302,  # 0x8FBC8F (Dark Sea Green)
        description="按照21點遊戲統計數據排名的玩家",
        stats_updater=update_blackjack_stats,
        custom_stats=[
            GameStatConfig("blackjack_count", "Blackjack次數", "🃏"),
            GameStatConfig("five_card_trick_count", "過五關次數", "👑"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="dice",
        name="三骰子大小",
        emoji=_get_minigame_emoji("dice", "🎲"),
        color=********,  # 0xF1C40F (Yellow)
        description="按照三骰子大小遊戲統計數據排名的玩家",
        stats_updater=update_dice_stats,
        custom_stats=[
            GameStatConfig("big_choice_win_rate", "選大勝率", "📈"),
            GameStatConfig("small_choice_win_rate", "選小勝率", "📉"),
            GameStatConfig("triple_win_rate", "押圍骰勝率", "🎲"),
            GameStatConfig("triple_count", "開出圍骰次數", "🎯"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="mines",
        name="尋寶礦區",
        emoji=_get_minigame_emoji("mines", "💎"),
        color=16711935,  # 0xFF00FF (Magenta)
        description="按照尋寶礦區遊戲統計數據排名的玩家",
        stats_updater=update_mines_stats,
        custom_stats=[
            GameStatConfig("avg_tiles_revealed", "平均挖掘格數", "⛏️"),
            GameStatConfig("special_coin_found", "發現金幣次數", "🪙"),
            GameStatConfig("special_star_found", "發現星星次數", "⭐"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="tower",
        name="爬塔遊戲",
        emoji=_get_minigame_emoji("tower", "🏗️"),
        color=16753920,  # 0xFF8C00 (Dark Orange)
        description="按照爬塔遊戲統計排名的玩家",
        stats_updater=update_tower_stats,
        custom_stats=[
            GameStatConfig("max_level_reached", "最高到達層數", "🏗️"),
            GameStatConfig("avg_levels_completed", "平均完成層數", "📊"),
            GameStatConfig("cashout_rate", "提現率", "💰"),
            GameStatConfig("tnt_hit_rate", "TNT踩中率", "💥"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="slot",
        name="拉霸機 (3x3)",
        emoji=_get_minigame_emoji("slot", "🎰"),
        color=16776960,  # 0xFFD700 (Gold)
        description="按照拉霸機遊戲統計數據排名的玩家 (3x3網格版本)",
        stats_updater=update_slot_stats,
        custom_stats=[
            GameStatConfig("jackpot_count", "全螢幕頭獎次數", "💎"),
            GameStatConfig("max_consecutive_symbols", "最大連線數", "🔗"),
            GameStatConfig("rtp_percentage", "RTP百分比", "🎯"),
        ],
    )
)


def update_poker1v1_stats(
    stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新德州撲克1v1統計【只關心撲克特定數據】"""
    # 初始化撲克特定統計
    stats.setdefault("total_hands_played", 0)
    stats.setdefault("total_rake_paid", 0)
    stats.setdefault("max_hands_per_game", 0)
    stats.setdefault("total_all_in_count", 0)
    stats.setdefault("fold_count", 0)
    stats.setdefault("stake_tier_stats", {})
    # 注意：連勝/連敗統計由通用系統自動處理，無需在這裡初始化

    # 獲取遊戲數據
    hands_played = game_data.get("hands_played", 0)
    rake_amount = game_data.get("rake_amount", 0)
    result = game_data.get("result", "lose")
    stake_tier = game_data.get("stake_tier", "newbie")
    # win_reason = game_data.get("win_reason", "")  # 目前統計更新不直接使用

    # 更新基礎統計
    stats["total_hands_played"] += hands_played
    stats["total_rake_paid"] += rake_amount
    stats["max_hands_per_game"] = max(stats["max_hands_per_game"], hands_played)

    # 更新全下統計（直接使用 is_all_in 字段）
    is_all_in = game_data.get("is_all_in", False)
    if is_all_in:
        stats["total_all_in_count"] += 1

    # 更新棄牌統計（記錄自己棄牌的情況）
    is_fold = game_data.get("is_fold", False)
    if is_fold:
        stats["fold_count"] += 1

    # 獲取勝負結果（用於場次統計）
    is_win = result == "win"

    # 更新場次統計
    if stake_tier not in stats["stake_tier_stats"]:
        stats["stake_tier_stats"][stake_tier] = {
            "games": 0,
            "wins": 0,
            "total_hands": 0,
        }

    stats["stake_tier_stats"][stake_tier]["games"] += 1
    stats["stake_tier_stats"][stake_tier]["total_hands"] += hands_played
    if is_win:
        stats["stake_tier_stats"][stake_tier]["wins"] += 1

    # 計算平均值和比率
    total_games = sum(
        tier_stats["games"] for tier_stats in stats["stake_tier_stats"].values()
    )
    stats["avg_hands_per_game"] = stats["total_hands_played"] / max(total_games, 1)
    stats["fold_rate"] = (
        (stats["fold_count"] / max(stats["total_hands_played"], 1)) * 100
        if stats["total_hands_played"] > 0
        else 0
    )

    return stats


GameRegistry.register_game(
    GameConfig(
        game_type="poker1v1",
        name="德州撲克 1v1",
        emoji=_get_minigame_emoji("poker1v1", "🃏"),
        color=0x8B4513,  # SaddleBrown
        description="按照德州撲克1v1遊戲統計數據排名的玩家",
        stats_updater=update_poker1v1_stats,
        custom_stats=[
            GameStatConfig("total_hands_played", "總手牌數", "🃏"),
            GameStatConfig("fold_rate", "棄牌率", "🃏"),
            GameStatConfig("total_all_in_count", "全下次數", "💥"),
        ],
    )
)

GameRegistry.register_game(
    GameConfig(
        game_type="spin_wheel",
        name="轉盤遊戲",
        emoji=_get_minigame_emoji("spin_wheel", "🎡"),
        color=0x5865F2,  # Discord Blurple
        description="按照轉盤遊戲統計數據排名的玩家",
        stats_updater=update_spin_wheel_stats,
        custom_stats=[
            GameStatConfig("total_big_wins", "總大獎次數", "🎉"),
            # 更多自訂統計可以在此添加，例如 'high_risk_win_rate'
        ],
    )
)
