--
-- Name: story_chunk_summaries; Type: TABLE; Schema: public; Owner: -
--
-- 新增 story_chunk_summaries 表以支援區塊記憶總結系統
-- 此表儲存對故事歷史區塊的 AI 生成總結

CREATE TABLE public.story_chunk_summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL,
    start_turn_number INTEGER NOT NULL,
    end_turn_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- 確保總結必須關聯到一個真實的故事
    -- 當故事被刪除時，其所有關聯的總結也會被自動刪除
    CONSTRAINT fk_chunk_summary_story
        FOREIGN KEY(story_id) 
        REFERENCES public.stories(id)
        ON DELETE CASCADE,

    -- 確保結束回合號必須大於或等於起始回合號
    CONSTRAINT chk_turn_range 
        CHECK (end_turn_number >= start_turn_number),

    -- 確保同一故事的相同回合範圍只能有一個總結
    CONSTRAINT uq_story_turn_range 
        UNIQUE (story_id, start_turn_number, end_turn_number)
);

-- 為快速查詢特定故事的所有總結創建索引
CREATE INDEX idx_story_chunk_summaries_story_id ON public.story_chunk_summaries(story_id);

-- 應用您現有的 updated_at 觸發器到新表
CREATE TRIGGER update_story_chunk_summaries_updated_at
BEFORE UPDATE ON public.story_chunk_summaries
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- 可選的頂級優化：防止區間重疊
-- 如果您想在資料庫層面徹底防止為同一個故事創建重疊的總結區間
-- （例如，已存在 1-40 的總結，就不允許再創建 30-60 的總結）
-- 您可以執行以下命令。這需要 btree_gist 擴展。

-- CREATE EXTENSION IF NOT EXISTS btree_gist;
-- ALTER TABLE public.story_chunk_summaries
-- ADD CONSTRAINT no_overlapping_summaries
-- EXCLUDE USING gist (
--     story_id WITH =,
--     int4range(start_turn_number, end_turn_number, '[]') WITH &&
-- );