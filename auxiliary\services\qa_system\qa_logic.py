# auxiliary/services/qa_system/qa_logic.py
"""
QA系統服務模組 - 處理問答系統的主要邏輯
"""

import io
import logging
import re
from datetime import datetime
from typing import List, Optional

import discord

from auxiliary.data.prompts.prompts.unified_prompts import QA_SYSTEM_PROMPT
from auxiliary.exceptions import AIConnectionError

# 引入通用組件
from ..ai_core import ai_service

# 設置日誌
logger = logging.getLogger("QALogic")


class QASystem:
    """問答系統處理類"""

    def __init__(self):
        """初始化QA系統"""
        self.system_prompt = QA_SYSTEM_PROMPT

    async def process_qa_request(
        self,
        question: str,
        user_id: Optional[str],
        response_message: discord.WebhookMessage,
        request_id: str,
    ):
        """處理純文本問答請求"""
        await response_message.edit(content=f"💬 正在思考問題: {question[:30]}...")

        api_response = await ai_service.process_text(
            prompt=question, system_prompt=self.system_prompt, request_id=request_id
        )

        answer_text = self._sanitize_ai_response(
            ai_service.extract_response_text(api_response)
        )
        embed = self.format_qa_embed(question, answer_text)
        await response_message.edit(content=None, embed=embed)

    async def process_image_qa_request(
        self,
        image_data: bytes,
        user_id: Optional[str],
        response_message: discord.WebhookMessage,
        request_id: str,
        question: Optional[str] = None,
    ):
        """處理包含圖像的問答請求"""
        if not question:
            question = "請描述這張圖片，並回答圖片中可能的問題。"

        try:
            await response_message.edit(
                content=f"🔍 正在分析圖片和問題: {question[:30]}..."
            )
        except Exception as e:
            logger.warning("更新圖像問答處理消息失敗: %s", e)

        api_response = await ai_service.process_with_image(
            image_data=image_data,
            prompt=question,
            system_prompt=self.system_prompt,
            request_id=request_id,
        )

        answer_text = self._sanitize_ai_response(
            ai_service.extract_response_text(api_response)
        )
        embed = self.format_qa_embed(question, answer_text, is_image_qa=True)

        try:
            image_file = discord.File(io.BytesIO(image_data), filename="image_full.jpg")
            embed.set_image(url="attachment://image_full.jpg")
            await response_message.edit(
                content=None, embed=embed, attachments=[image_file]
            )
        except Exception as e:
            logger.error("設置圖片時出錯: %s", str(e))
            await response_message.edit(content=None, embed=embed)

    def format_qa_embed(
        self, question: str, answer_text: str, is_image_qa: bool = False
    ) -> discord.Embed:
        """格式化問答嵌入消息"""
        if not answer_text or not answer_text.strip():
            answer_text = "AI未能提供有效回答，請嘗試其他問題。"

        answer_text = self._format_callout_boxes(answer_text)
        features = self._analyze_answer_features(answer_text)
        category = self._guess_answer_category(question, answer_text)

        color = self._determine_embed_color(is_image_qa, features, category)
        embed = discord.Embed(color=color, timestamp=discord.utils.utcnow())

        self._set_embed_title(embed, question, is_image_qa)

        if (
            not features["has_code"]
            and not features["has_table"]
            and not features["has_markdown_headers"]
        ):
            answer_text = self._enhance_text_formatting(answer_text)

        self._add_paged_description(embed, answer_text)
        self._add_metadata_fields(embed, category, features)
        self._set_embed_author(embed, is_image_qa, features)

        embed.set_footer(text="回答由AI生成 • APEX智能助手")
        return embed

    def _analyze_answer_features(self, answer_text: str) -> dict:
        """分析答案文本的特徵"""
        return {
            "has_code": "```" in answer_text,
            "has_table": "|" in answer_text and "-|-" in answer_text.replace(" ", ""),
            "has_list": any(
                line.strip().startswith(("- ", "• ", "* ", "1. ", "2. "))
                for line in answer_text.split("\n")
            ),
            "has_markdown_headers": any(
                line.strip().startswith("#") for line in answer_text.split("\n")
            ),
            "has_callouts": any(
                marker in answer_text for marker in ["📌", "💡", "⚠️", "❗"]
            ),
        }

    def _determine_embed_color(
        self, is_image_qa: bool, features: dict, category: str
    ) -> discord.Color:
        """根據內容特徵決定嵌入顏色"""
        if is_image_qa:
            return discord.Color.purple()
        if features["has_code"]:
            return discord.Color.dark_gold()
        if features["has_table"]:
            return discord.Color.teal()
        if features["has_callouts"]:
            return discord.Color.orange()
        if category == "技術":
            return discord.Color.from_rgb(114, 137, 218)
        if category == "生活":
            return discord.Color.from_rgb(67, 181, 129)
        if category == "教育":
            return discord.Color.from_rgb(240, 71, 71)
        return discord.Color.blue()

    def _set_embed_title(self, embed: discord.Embed, question: str, is_image_qa: bool):
        """設定嵌入標題"""
        max_title_length = 256
        title_icon = "📷 " if is_image_qa else "❓ "
        if len(question) > max_title_length - len(title_icon):
            title = (
                f"{title_icon}{question[: max_title_length - len(title_icon) - 3]}..."
            )
        else:
            title = f"{title_icon}{question}"
        embed.title = title

    def _enhance_text_formatting(self, text: str) -> str:
        """增強文本格式，例如關鍵字加粗"""
        paragraphs = text.split("\n\n")
        enhanced_paragraphs = []

        for para in paragraphs:
            if any(
                marker in para
                for marker in [
                    "📌 **提示**",
                    "💡 **小貼士**",
                    "⚠️ **警告**",
                    "❗ **重要**",
                ]
            ):
                enhanced_paragraphs.append(para)
            elif not para.strip().startswith(("- ", "• ", "* ", "1. ", "2. ")):
                enhanced_paragraphs.append(self._enhance_keywords(para))
            else:
                lines = para.split("\n")
                enhanced_lines = []
                for line in lines:
                    if line.strip().startswith(("- ", "• ", "* ")):
                        prefix = line[: line.find(" ") + 1]
                        content = line[line.find(" ") + 1 :]
                        if ": " in content and len(content.split(": ")[0]) < 20:
                            parts = content.split(": ", 1)
                            line = f"{prefix}**{parts[0]}**: {parts[1]}"
                        else:
                            line = prefix + self._enhance_keywords(content)
                    enhanced_lines.append(line)
                enhanced_paragraphs.append("\n".join(enhanced_lines))

        return "\n\n".join(enhanced_paragraphs)

    def _add_paged_description(self, embed: discord.Embed, text: str):
        """處理超過長度限制的描述，並確保總長度不超過Discord限制"""
        max_total_length = 6000
        max_description_length = 4096
        max_field_value_length = 1024

        current_length = (
            len(embed.title or "")
            + len(embed.footer.text or "")
            + len(embed.author.name or "")
        )

        if len(text) + current_length > max_total_length:
            text = (
                text[: max_total_length - current_length - 200]
                + "\n... (內容過長，已被截斷)"
            )
            logger.warning("Embed 總長度超限，已截斷內容。")

        if len(text) > max_description_length:
            embed.description = text[:max_description_length]
            remaining_text = text[max_description_length:]

            field_count = 1
            while remaining_text and len(embed.fields) < 25:
                chunk = remaining_text[:max_field_value_length]
                remaining_text = remaining_text[max_field_value_length:]

                if not chunk.strip():
                    continue

                field_name = f"續 ({field_count})"

                # 檢查加入後是否會超長
                if (
                    current_length
                    + len(embed.description)
                    + len(field_name)
                    + len(chunk)
                    > max_total_length
                ):
                    chunk = (
                        chunk[
                            : max_total_length
                            - current_length
                            - len(embed.description)
                            - len(field_name)
                            - 20
                        ]
                        + "..."
                    )
                    embed.add_field(name=field_name, value=chunk, inline=False)
                    break

                embed.add_field(name=field_name, value=chunk, inline=False)
                field_count += 1
        else:
            embed.description = text

    def _add_metadata_fields(self, embed: discord.Embed, category: str, features: dict):
        """添加分類、格式等元數據欄位"""
        embed.add_field(name="📂 分類", value=f"**{category}**", inline=True)

        format_value = "一般文本"
        if features["has_code"]:
            format_value = "程式碼"
        elif features["has_table"]:
            format_value = "表格數據"
        elif features["has_list"]:
            format_value = "列表項目"
        elif features["has_markdown_headers"]:
            format_value = "結構化文本"
        elif features["has_callouts"]:
            format_value = "包含提示框"

        embed.add_field(name="📄 格式", value=format_value, inline=True)

        current_time = self._get_current_time()
        embed.add_field(name="⏱️ 生成時間", value=current_time, inline=True)

    def _set_embed_author(
        self, embed: discord.Embed, is_image_qa: bool, features: dict
    ):
        """根據內容設定作者資訊"""
        author_name = "AI 助手"
        author_icon = "💡"

        if is_image_qa:
            author_name = "圖像問答助手"
            author_icon = "🖼️"
        elif features["has_code"]:
            author_name = "程式碼助手"
            author_icon = "💻"
        elif features["has_table"]:
            author_name = "數據助手"
            author_icon = "📊"

        embed.set_author(name=f"{author_icon} {author_name}")

    def _chunk_text(self, text: str, chunk_size: int) -> List[str]:
        return [text[i : i + chunk_size] for i in range(0, len(text), chunk_size)]

    def _format_callout_boxes(self, text: str) -> str:
        if any(
            marker in text
            for marker in ["📌 **提示**", "💡 **小貼士**", "⚠️ **警告**", "❗ **重要**"]
        ):
            return text

        lines = text.split("\n")
        formatted_lines = []
        i = 0

        while i < len(lines):
            current_line = lines[i].strip()
            callout_type = self._get_callout_type(current_line)

            if callout_type:
                icon, title = self._get_callout_details(callout_type)
                content = self._strip_callout_prefix(current_line)

                box_start = f"{icon} **{title}**："
                if len(content) < 20:
                    formatted_lines.append(f"{box_start} {content}")
                else:
                    formatted_lines.append(box_start)
                    formatted_lines.append(f"> {content}")

                if i + 1 < len(lines) and not lines[i + 1].strip():
                    i += 1
            else:
                formatted_lines.append(lines[i])

            i += 1

        return "\n".join(formatted_lines)

    def _get_callout_type(self, line: str) -> Optional[str]:
        line = line.strip()
        if line.startswith(
            ("提示:", "提示：", "小貼士:", "小貼士：", "提示", "小貼士")
        ):
            return "tip"
        if line.startswith(
            (
                "注意:",
                "注意：",
                "說明:",
                "說明：",
                "備註:",
                "備註：",
                "注意",
                "說明",
                "備註",
            )
        ):
            return "note"
        if line.startswith(
            ("警告:", "警告：", "注意事項:", "注意事項：", "警告", "注意事項")
        ):
            return "warning"
        if line.startswith(("重要:", "重要：", "關鍵:", "關鍵：", "重要", "關鍵")):
            return "important"
        return None

    def _get_callout_details(self, callout_type: str) -> tuple[str, str]:
        if callout_type == "tip":
            return "💡", "小貼士"
        if callout_type == "note":
            return "📌", "提示"
        if callout_type == "warning":
            return "⚠️", "警告"
        if callout_type == "important":
            return "❗", "重要"
        return "", ""

    def _strip_callout_prefix(self, line: str) -> str:
        prefixes = [
            "提示:",
            "提示：",
            "小貼士:",
            "小貼士：",
            "注意:",
            "注意：",
            "警告:",
            "警告：",
            "說明:",
            "說明：",
            "備註:",
            "備註：",
            "重要:",
            "重要：",
            "關鍵:",
            "關鍵：",
            "注意事項:",
            "注意事項：",
            "提示",
            "小貼士",
            "注意",
            "說明",
            "備註",
            "警告",
            "注意事項",
            "重要",
            "關鍵",
        ]
        for prefix in prefixes:
            if line.startswith(prefix):
                content = line[len(prefix) :].strip()
                if content.startswith((":", "：")):
                    content = content[1:].strip()
                return content
        return line

    def _enhance_keywords(self, text: str) -> str:
        if "**" in text or "__" in text or "*" in text or "_" in text:
            return text

        important_terms = [
            "Python",
            "Java",
            "JavaScript",
            "TypeScript",
            "HTML",
            "CSS",
            "SQL",
            "API",
            "Docker",
            "Kubernetes",
            "Git",
            "React",
            "Vue",
            "Angular",
            "Node.js",
            "Django",
            "Flask",
            "Spring",
            "MongoDB",
            "MySQL",
            "PostgreSQL",
            "Redis",
            "AWS",
            "Azure",
            "Google Cloud",
            "Firebase",
            "Linux",
            "Windows",
            "macOS",
            "iOS",
            "Android",
            "注意",
            "警告",
            "重要",
            "關鍵",
            "必須",
            "不要",
            "切記",
            "建議",
            "推薦",
            "最佳實踐",
            "原理",
            "基本概念",
            "核心",
            "框架",
            "結構",
            "模式",
            "設計模式",
            "函數",
            "方法",
            "類",
            "對象",
            "實例",
            "繼承",
            "封裝",
            "多態",
            "接口",
            "異常處理",
            "線程",
            "進程",
            "同步",
            "異步",
            "回調",
            "Promise",
            "async",
            "await",
            "數組",
            "列表",
            "字典",
            "映射",
            "集合",
            "堆棧",
            "隊列",
            "樹",
            "圖",
            "排序",
            "搜索",
            "遍歷",
            "深度優先",
            "廣度優先",
            "動態規劃",
            "貪心算法",
        ]

        enhanced_text = text
        for term in important_terms:
            pattern = rf"\b{term}\b"
            if re.search(pattern, enhanced_text, re.IGNORECASE):
                enhanced_text = re.sub(
                    pattern,
                    lambda m: f"**{m.group(0)}**",
                    enhanced_text,
                    flags=re.IGNORECASE,
                )

        return enhanced_text

    def _guess_answer_category(self, question: str, answer: str) -> str:
        combined_text = f"{question} {answer}".lower()

        tech_keywords = [
            "程式",
            "編程",
            "代碼",
            "程式碼",
            "api",
            "函數",
            "方法",
            "類",
            "對象",
            "數據庫",
            "sql",
            "html",
            "css",
            "javascript",
            "python",
            "java",
            "c++",
            "服務器",
            "伺服器",
            "網路",
            "算法",
            "數據結構",
            "框架",
            "開發",
            "運行",
            "執行",
            "bug",
            "錯誤",
            "調試",
            "測試",
            "部署",
            "git",
            "linux",
            "windows",
            "mac",
            "命令行",
            "指令",
            "系統",
            "軟體",
            "硬體",
            "網站",
            "應用",
            "app",
            "手機",
            "電腦",
            "技術",
        ]

        life_keywords = [
            "健康",
            "飲食",
            "運動",
            "旅遊",
            "旅行",
            "美食",
            "料理",
            "烹飪",
            "食譜",
            "減肥",
            "健身",
            "瑜伽",
            "購物",
            "娛樂",
            "電影",
            "音樂",
            "書籍",
            "閱讀",
            "生活",
            "日常",
            "家居",
            "裝修",
            "親子",
            "育兒",
            "寵物",
            "感情",
            "愛情",
            "婚姻",
            "情緒",
            "心理",
            "溝通",
            "交流",
            "時尚",
            "穿搭",
            "美容",
            "護膚",
        ]

        edu_keywords = [
            "學習",
            "教育",
            "考試",
            "課程",
            "知識",
            "學校",
            "大學",
            "高中",
            "初中",
            "小學",
            "幼兒園",
            "教師",
            "學生",
            "作業",
            "論文",
            "研究",
            "科學",
            "歷史",
            "地理",
            "數學",
            "物理",
            "化學",
            "生物",
            "語文",
            "英語",
            "外語",
            "文學",
        ]

        tech_count = sum(1 for word in tech_keywords if word in combined_text)
        life_count = sum(1 for word in life_keywords if word in combined_text)
        edu_count = sum(1 for word in edu_keywords if word in combined_text)

        if tech_count > life_count and tech_count > edu_count:
            return "技術"
        elif life_count > tech_count and life_count > edu_count:
            return "生活"
        elif edu_count > tech_count and edu_count > life_count:
            return "教育"
        else:
            return "一般知識"

    def _get_current_time(self) -> str:
        now = datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")

    def _sanitize_ai_response(self, text: str) -> str:
        if not text:
            return text

        text = re.sub(r"\*思考[:：].*?\*", "", text, flags=re.DOTALL)
        text = re.sub(r"\*[Tt]hinking[:：].*?\*", "", text, flags=re.DOTALL)
        text = re.sub(r"\*[Nn]ote[:：].*?\*", "", text, flags=re.DOTALL)
        text = re.sub(r"\*.*?思考.*?\*", "", text, flags=re.DOTALL)
        text = re.sub(r"\*.*?[Tt]hinking.*?\*", "", text, flags=re.DOTALL)
        text = re.sub(
            r"步驟\s*\d+\s*[:：].*?(?=步驟\s*\d+\s*[:：]|$)", "", text, flags=re.DOTALL
        )
        text = re.sub(
            r"[Ss]tep\s*\d+\s*[:：].*?(?=[Ss]tep\s*\d+\s*[:：]|$)",
            "",
            text,
            flags=re.DOTALL,
        )
        text = re.sub(r"草稿[:：].*?(?=草稿[:：]|$)", "", text, flags=re.DOTALL)
        text = re.sub(r"校閱[:：].*?(?=校閱[:：]|$)", "", text, flags=re.DOTALL)
        text = re.sub(r"[Dd]raft[:：].*?(?=[Dd]raft[:：]|$)", "", text, flags=re.DOTALL)
        text = re.sub(
            r"[Rr]eview[:：].*?(?=[Rr]eview[:：]|$)", "", text, flags=re.DOTALL
        )
        text = re.sub(r"根據指示.*?(?=\n|$)", "", text)
        text = re.sub(r"按照要求.*?(?=\n|$)", "", text)
        text = re.sub(r"[Aa]s instructed.*?(?=\n|$)", "", text)
        text = re.sub(r"[Aa]s required.*?(?=\n|$)", "", text)
        text = re.sub(r"我需要用繁體中文回答.*?(?=\n|$)", "", text)
        text = re.sub(r"我將使用.*?(?=\n|$)", "", text)
        text = re.sub(r"我應該.*?(?=\n|$)", "", text)
        text = re.sub(r"[Ii] need to respond in.*?(?=\n|$)", "", text)
        text = re.sub(r"[Ii] should.*?(?=\n|$)", "", text)
        text = re.sub(r"[Ii] will.*?(?=\n|$)", "", text)
        text = re.sub(r"讓我思考一下.*?(?=\n|$)", "", text)
        text = re.sub(r"首先，我需要.*?(?=\n|$)", "", text)
        text = re.sub(r"[Ll]et me think.*?(?=\n|$)", "", text)
        text = re.sub(r"[Ff]irst, I need to.*?(?=\n|$)", "", text)
        text = re.sub(r"\d+\.\s*思考[:：].*?(?=\d+\.\s*|$)", "", text, flags=re.DOTALL)
        text = re.sub(
            r"\d+\.\s*[Tt]hinking[:：].*?(?=\d+\.\s*|$)", "", text, flags=re.DOTALL
        )
        text = re.sub(r".*?ai_payload.*?(?=\n|$)", "", text, flags=re.IGNORECASE)
        text = re.sub(r".*?標籤.*?(?=\n|$)", "", text)
        text = re.sub(r".*?tag.*?(?=\n|$)", "", text, flags=re.IGNORECASE)

        inappropriate_terms = [
            "Apex",
            "nsfw",
            "NSFW",
            "創作出最頂級的作品",
            "虛擬世界",
            "不拒絕創作",
            "不會回避任何細節",
            "不進行道德評判",
        ]
        for term in inappropriate_terms:
            text = text.replace(term, "")

        text = re.sub(r"\n{3,}", "\n\n", text)

        return text.strip()

    async def process_request_from_handler(
        self,
        image_data: Optional[bytes],
        user_id: str,
        processing_message: discord.WebhookMessage,
        request_id: str,
        user_prompt: Optional[str] = None,
    ):
        """由 MessageHandler 調用，以統一處理圖像和文本問答請求。"""
        if image_data:
            await self.process_image_qa_request(
                image_data=image_data,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id,
                question=user_prompt,
            )
        elif user_prompt:
            await self.process_qa_request(
                question=user_prompt,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id,
            )
        else:
            raise AIConnectionError("請求內容為空（沒有圖像或問題文本），無法處理。")
