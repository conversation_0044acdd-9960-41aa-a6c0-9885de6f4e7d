# verify_image_completeness.py
import asyncio
import os
import re
import sys

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# --- 配置部分 ---
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)

IMAGE_BASE_PATH = "downloaded_gacha_master_cards"
CARD_TABLE_NAME = "gacha_master_cards"

RARITY_MAP = {1: "C", 2: "R", 3: "SR", 4: "SSR", 5: "UR", 6: "LR", 7: "EX"}
DEFAULT_RARITY_NAME = "UnknownRarity"
VALID_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp"}


def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其适合作为文件名的一部分（最终统一标准）"""
    if not text:
        return "unknown"
    text = text.replace("\r", "").replace("\n", "")
    text = text.replace("//", "__")
    text = text.replace(", ", ",_")
    text = text.replace(":", "_")
    text = text.replace(" ", "_")
    text = re.sub(r'[\\/*?"<>|]', "", text)
    return text[:max_length]


def _check_card_file(card, base_path):
    """檢查單張卡片的圖片檔案是否存在。"""
    rarity_name = RARITY_MAP.get(card["rarity"], DEFAULT_RARITY_NAME)
    name_part = sanitize_filename_part(card["name"] or card["series"])
    for ext in VALID_EXTENSIONS:
        filename = f"{card['card_id']}_{name_part}{ext}"
        filepath = os.path.join(base_path, rarity_name, filename)
        if os.path.exists(filepath):
            return True
    return {
        "card_id": card["card_id"],
        "name": card["name"],
        "base_filename": f"{card['card_id']}_{name_part}",
    }


def _print_results(missing_files, total_cards):
    """打印驗證結果。"""
    print("\n--- 驗證完畢 ---", flush=True)
    missing_count = len(missing_files)
    if missing_count == 0:
        print(f"🎉 恭喜！所有 {total_cards} 張卡片的圖片檔案都存在！", flush=True)
    else:
        print(
            f"🚨 發現 {missing_count} 個缺失的圖片檔案 (共 {total_cards} 張)。",
            flush=True,
        )
        print("缺失文件列表 (最多顯示前 20 条):", flush=True)
        for missing in missing_files[:20]:
            print(
                f"  - Card ID: {missing['card_id']}, Name: '{missing['name']}', "
                f"Expected Base: '{missing['base_filename']}'",
                flush=True,
            )
        if missing_count > 20:
            print(f"  ... 以及其他 {missing_count - 20} 個文件。", flush=True)
        print(
            "\n提示：可以嘗試再次運行下載腳本 "
            "`pre_download_master_card_images.py` 來獲取它們。",
            flush=True,
        )


async def main():
    """主執行函數。"""
    print("--- 開始精确驗證圖片完整性 (純本地匹配) ---", flush=True)
    await setup_connections()
    try:
        pool = get_pool()
        if not pool:
            print("[DB-Error] 資料庫連接池初始化失敗。")
            return

        async with pool.acquire() as conn:
            print("[DB] 成功從連接池獲取連接。", flush=True)

            query = (
                f"SELECT card_id, name, series, rarity FROM {CARD_TABLE_NAME} "
                "WHERE image_url IS NOT NULL AND image_url != ''"
            )
            all_cards = await conn.fetch(query)
            total_cards = len(all_cards)
            print(f"[DB] 資料庫中共有 {total_cards} 條應有圖片的卡片記錄。", flush=True)

            missing_files = []
            for i, card in enumerate(all_cards):
                if (i + 1) % 1000 == 0:
                    print(f"  ...已檢查 {i + 1}/{total_cards} 條記錄...", flush=True)
                result = _check_card_file(card, IMAGE_BASE_PATH)
                if isinstance(result, dict):
                    missing_files.append(result)

            _print_results(missing_files, total_cards)

    except Exception as e:
        print(f"[Error] 發生意外錯誤: {e}", flush=True)
        import traceback

        traceback.print_exc()
    finally:
        await close_connections()
        print("[DB] 資料庫連接已關閉。", flush=True)


if __name__ == "__main__":
    if os.name == "nt":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
