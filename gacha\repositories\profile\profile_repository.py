"""
Profile Repository - 處理用戶檔案相關的資料庫操作
使用純異常模式進行錯誤處理
"""

import os
from typing import Any, Dict, List, Optional

import asyncpg

from gacha.exceptions import (
    DatabaseOperationError,
    EntityNotFoundError,
    InvalidSlotError,
    UserNotFoundError,
    UserProfileNotFoundError,
)
from gacha.models.profile_models import ProfileCardInfo, UserProfile
from gacha.repositories import _base_repo
from utils.logger import logger

# Define the base directory for the project
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))

_TABLE_NAME = "user_profiles"


def _record_to_user_profile(record: asyncpg.Record) -> UserProfile:
    """將 asyncpg.Record 轉換為 UserProfile dataclass"""
    return UserProfile(
        user_id=record["user_id"],
        showcased_card_collection_id=record.get("showcased_card_collection_id"),
        sub_card_1_collection_id=record.get("sub_card_1_collection_id"),
        sub_card_2_collection_id=record.get("sub_card_2_collection_id"),
        sub_card_3_collection_id=record.get("sub_card_3_collection_id"),
        sub_card_4_collection_id=record.get("sub_card_4_collection_id"),
        background_image_url=record.get("background_image_url"),
        like_count=record.get("like_count", 0),
        created_at=record.get("created_at"),
        updated_at=record.get("updated_at"),
        user_status=record.get("user_status"),
        showcased_card_frame_number=record.get("showcased_card_frame_number"),
        sub_card_1_frame_number=record.get("sub_card_1_frame_number"),
        sub_card_2_frame_number=record.get("sub_card_2_frame_number"),
        sub_card_3_frame_number=record.get("sub_card_3_frame_number"),
        sub_card_4_frame_number=record.get("sub_card_4_frame_number"),
        profile_image_url=record.get("profile_image_url"),
    )


async def get_user_profile(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> UserProfile:
    """獲取用戶檔案"""
    query = f"SELECT * FROM {_TABLE_NAME} WHERE user_id = $1"
    try:
        result = await _base_repo.fetch_one(query, (user_id,), connection=connection)
        if not result:
            raise UserProfileNotFoundError(
                f"User profile not found for user_id: {user_id}"
            )
        return _record_to_user_profile(result)
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取用戶檔案失敗: user_id={user_id}, error={e}"
        ) from e


async def create_user_profile(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> UserProfile:
    """建立新用戶檔案"""
    query = f"""
        INSERT INTO {_TABLE_NAME} (user_id, like_count, created_at, updated_at)
        VALUES ($1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
    """
    try:
        result = await _base_repo.fetch_one(query, (user_id,), connection=connection)
        if not result:
            raise DatabaseOperationError(
                f"建立用戶檔案失敗，未返回記錄: user_id={user_id}"
            )
        return _record_to_user_profile(result)
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"建立用戶檔案失敗: user_id={user_id}, error={e}"
        ) from e


async def get_or_create_user_profile(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> UserProfile:
    """獲取或建立用戶檔案"""
    try:
        return await get_user_profile(user_id, connection)
    except UserProfileNotFoundError:
        return await create_user_profile(user_id, connection)


async def update_showcased_card(
    user_id: int,
    collection_id: Optional[int],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新主展示卡片"""
    query = f"""
        UPDATE {_TABLE_NAME} 
        SET showcased_card_collection_id = $1, 
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status = await _base_repo.execute_query(
            query, (collection_id, user_id), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"更新主展示卡片失敗，用戶可能不存在: user_id={user_id}",
                user_id=user_id,
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新主展示卡片失敗: user_id={user_id}, error={e}"
        ) from e


async def update_sub_card(
    user_id: int,
    slot_index: int,
    collection_id: Optional[int],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新副展示卡片"""
    if slot_index not in [1, 2, 3, 4]:
        raise InvalidSlotError(f"無效的槽位索引: {slot_index}")

    column_name = f"sub_card_{slot_index}_collection_id"
    query = f"""
        UPDATE {_TABLE_NAME} 
        SET {column_name} = $1, 
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status = await _base_repo.execute_query(
            query, (collection_id, user_id), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"更新副展示卡片失敗，用戶可能不存在: user_id={user_id}, slot={slot_index}",
                user_id=user_id,
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新副展示卡片失敗: user_id={user_id}, slot={slot_index}, error={e}"
        ) from e


async def clear_sub_card_slot(
    user_id: int, slot_index: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """清除指定位置的副展示卡片"""
    if slot_index not in [1, 2, 3, 4]:
        raise InvalidSlotError(f"無效的槽位索引: {slot_index}")

    column_name = f"sub_card_{slot_index}_collection_id"
    query = f"""
        UPDATE {_TABLE_NAME} 
        SET {column_name} = NULL, 
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
    """
    try:
        status = await _base_repo.execute_query(
            query, (user_id,), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"清除副展示卡片失敗，用戶可能不存在: user_id={user_id}, slot={slot_index}",
                user_id=user_id,
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"清除副展示卡片失敗: user_id={user_id}, slot={slot_index}, error={e}"
        ) from e


async def update_background_image(
    user_id: int,
    background_url: Optional[str],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新背景圖片，並確保路徑為相對路徑"""

    relative_path = background_url
    if background_url and os.path.isabs(background_url):
        try:
            relative_path = os.path.relpath(background_url, BASE_DIR)
        except ValueError:
            # 如果在不同磁碟機，保留原始路徑
            pass

    # 將路徑分隔符統一為 /
    if relative_path:
        relative_path = relative_path.replace("\\", "/")

    query = f"""
        UPDATE {_TABLE_NAME}
        SET background_image_url = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status = await _base_repo.execute_query(
            query, (relative_path, user_id), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"更新背景圖片失敗，用戶可能不存在: user_id={user_id}", user_id=user_id
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新背景圖片失敗: user_id={user_id}, error={e}"
        ) from e


async def increment_like_count(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """增加按讚數"""
    query = f"""
        UPDATE {_TABLE_NAME} 
        SET like_count = like_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
    """
    try:
        status = await _base_repo.execute_query(
            query, (user_id,), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"增加按讚數失敗，用戶可能不存在: user_id={user_id}", user_id=user_id
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"增加按讚數失敗: user_id={user_id}, error={e}"
        ) from e


async def get_card_info_by_collection_id(
    collection_id: int, connection: Optional[asyncpg.Connection] = None
) -> ProfileCardInfo:
    """根據收藏ID獲取卡片資訊"""
    query = """
        SELECT
            uc.id as collection_id,
            uc.card_id,
            mc.name,
            mc.series,
            mc.image_url,
            mc.rarity,
            uc.star_level,
            uc.quantity
        FROM gacha_user_collections uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.id = $1
    """
    try:
        result = await _base_repo.fetch_one(
            query, (collection_id,), connection=connection
        )

        if not result:
            raise EntityNotFoundError(f"找不到收藏ID為 {collection_id} 的卡片資訊")

        return ProfileCardInfo(
            collection_id=result["collection_id"],
            card_id=result["card_id"],
            name=result["name"],
            series=result["series"],
            image_url=result["image_url"],
            rarity=result["rarity"],
            star_level=result["star_level"],
            quantity=result["quantity"],
        )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取卡片資訊失敗: collection_id={collection_id}, error={e}"
        ) from e


async def get_user_statistics(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """獲取用戶統計資料"""
    # 計算圖鑑完成度
    completion_query = """
        WITH total_cards AS (
            SELECT COUNT(DISTINCT card_id) as total
            FROM gacha_master_cards
        ),
        owned_cards AS (
            SELECT COUNT(DISTINCT card_id) as owned
            FROM gacha_user_collections
            WHERE user_id = $1
        )
        SELECT
            COALESCE(owned_cards.owned, 0) as owned,
            total_cards.total,
            CASE
                WHEN total_cards.total > 0 THEN
                    ROUND((COALESCE(owned_cards.owned, 0)::numeric / total_cards.total::numeric) * 100, 2)
                ELSE 0
            END as completion_rate
        FROM total_cards, owned_cards
    """

    # 計算總持有數和稀有度分佈
    stats_query = """
        SELECT 
            COALESCE(SUM(uc.quantity), 0) as total_owned,
            COALESCE(SUM(CASE WHEN mc.rarity = 1 THEN uc.quantity ELSE 0 END), 0) as rarity_1_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 2 THEN uc.quantity ELSE 0 END), 0) as rarity_2_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 3 THEN uc.quantity ELSE 0 END), 0) as rarity_3_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 4 THEN uc.quantity ELSE 0 END), 0) as rarity_4_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 5 THEN uc.quantity ELSE 0 END), 0) as rarity_5_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 6 THEN uc.quantity ELSE 0 END), 0) as rarity_6_count,
            COALESCE(SUM(CASE WHEN mc.rarity = 7 THEN uc.quantity ELSE 0 END), 0) as rarity_7_count
        FROM gacha_user_collections uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1
    """

    try:
        completion_result = await _base_repo.fetch_one(
            completion_query, (user_id,), connection=connection
        )
        stats_result = await _base_repo.fetch_one(
            stats_query, (user_id,), connection=connection
        )

        if not completion_result or not stats_result:
            # 如果任何一個查詢沒有結果，返回一個預設的統計字典
            return {
                "completion_rate": 0.0,
                "total_owned": 0,
                "rarity_counts": dict.fromkeys(range(1, 8), 0),
            }

        return {
            "completion_rate": float(completion_result.get("completion_rate") or 0),
            "total_owned": int(stats_result.get("total_owned") or 0),
            "rarity_counts": {
                1: int(stats_result.get("rarity_1_count") or 0),
                2: int(stats_result.get("rarity_2_count") or 0),
                3: int(stats_result.get("rarity_3_count") or 0),
                4: int(stats_result.get("rarity_4_count") or 0),
                5: int(stats_result.get("rarity_5_count") or 0),
                6: int(stats_result.get("rarity_6_count") or 0),
                7: int(stats_result.get("rarity_7_count") or 0),
            },
        }
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取用戶統計資料失敗: user_id={user_id}, error={e}"
        ) from e


async def update_user_status(
    user_id: int, status: Optional[str], connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶個性簽名"""
    query = f"""
        UPDATE {_TABLE_NAME} 
        SET user_status = $1, 
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status_result = await _base_repo.execute_query(
            query, (status, user_id), connection=connection
        )
        if status_result != 1:
            raise UserNotFoundError(
                f"更新用戶簽名失敗，用戶可能不存在: user_id={user_id}", user_id=user_id
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新用戶簽名失敗: user_id={user_id}, error={e}"
        ) from e


async def update_frame_offset(
    user_id: int,
    slot: str,
    frame_number: Optional[int],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新指定卡槽的預覽影格編號"""

    valid_slots = {
        "main": "showcased_card_frame_number",
        "sub_1": "sub_card_1_frame_number",
        "sub_2": "sub_card_2_frame_number",
        "sub_3": "sub_card_3_frame_number",
        "sub_4": "sub_card_4_frame_number",
    }

    if slot not in valid_slots:
        raise InvalidSlotError(f"無效的卡槽標識符: {slot}")

    column_name = valid_slots[slot]

    query = f"""
        UPDATE {_TABLE_NAME}
        SET {column_name} = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status = await _base_repo.execute_query(
            query, (frame_number, user_id), connection=connection
        )
        if status != 1:
            # 如果用戶檔案不存在，這不會引發錯誤，只會更新0行。
            # 由於此功能總是在用戶已存在的情況下使用，我們可以假設用戶存在。
            # 如果需要更嚴格的檢查，可以先獲取用戶檔案。
            logger.warning(
                f"更新預覽影格編號時，用戶 {user_id} 的記錄未找到或未被更新。"
            )
            # 不在此處引發 UserNotFoundError，因為可能是用戶剛創建檔案，這是正常情況。
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新預覽影格編號失敗: user_id={user_id}, slot={slot}, error={e}"
        ) from e


async def get_showcased_cards_for_settings(
    collection_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Dict[int, Dict[str, Any]]:
    """
    為設定視圖批量獲取展示卡片的輕量級資訊 (ID 和名稱)。
    返回一個以 collection_id 為鍵的字典。
    """
    if not collection_ids:
        return {}

    query = """
        SELECT
            uc.id as collection_id,
            uc.card_id,
            mc.name
        FROM gacha_user_collections uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.id = ANY($1::int[])
    """
    try:
        results = await _base_repo.fetch_all(
            query, (collection_ids,), connection=connection
        )
        return {record["collection_id"]: dict(record) for record in results}
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"為設定視圖獲取展示卡片資訊失敗: collection_ids={collection_ids}, error={e}"
        ) from e


async def get_profile_image_url(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[str]:
    """獲取用戶檔案圖片 CDN URL"""
    try:
        # This can still return None if the URL is not set, which is a valid state.
        # The goal is to avoid returning None when the *profile* is not found.
        # We rely on the caller to handle a None URL, but not a None profile.
        profile = await get_user_profile(user_id, connection)
        return profile.profile_image_url
    except UserProfileNotFoundError:
        # If the profile itself doesn't exist, we can return None,
        # as there's no URL to speak of.
        return None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取檔案圖片 URL 失敗: user_id={user_id}, error={e}"
        ) from e


async def update_profile_image_url(
    user_id: int,
    image_url: Optional[str],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新用戶檔案圖片 CDN URL"""
    query = f"""
        UPDATE {_TABLE_NAME}
        SET profile_image_url = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    """
    try:
        status = await _base_repo.execute_query(
            query, (image_url, user_id), connection=connection
        )
        if status != 1:
            raise UserNotFoundError(
                f"更新檔案圖片 URL 失敗，用戶可能不存在: user_id={user_id}",
                user_id=user_id,
            )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新檔案圖片 URL 失敗: user_id={user_id}, error={e}"
        ) from e


async def clear_profile_image_url(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """清除用戶檔案圖片 CDN URL（當需要重新生成時）"""
    await update_profile_image_url(user_id, None, connection)
