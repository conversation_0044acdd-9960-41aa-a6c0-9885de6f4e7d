"""
驗證碼生成工具
"""

import random
from io import BytesIO

from captcha.image import ImageCaptcha

# 初始化驗證碼產生器，可以自訂字體、寬高
image = ImageCaptcha(
    width=280, height=90, fonts=["fonts/Pixelcraft - Personal Use.ttf"]
)


def generate_captcha(length: int = 4) -> tuple[str, BytesIO]:
    """
    產生一個指定長度的驗證碼。

    Args:
        length (int): 驗證碼的長度，預設為 4。

    Returns:
        tuple[str, BytesIO]: 一個包含 (驗證碼文字, 圖片的 BytesIO 物件) 的元組。
    """
    # 產生隨機字元，移除容易混淆的字元 (O, 0, I, 1)
    chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
    captcha_text = "".join(random.choices(chars, k=length))

    # 產生圖片數據
    data = image.generate(captcha_text)

    # 將圖片數據轉換為 BytesIO 物件
    image_io = BytesIO(data.read())
    image_io.seek(0)

    return captcha_text, image_io
