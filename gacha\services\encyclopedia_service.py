from typing import Any, Dict, List, Optional, Union

import bleach

from database.postgresql.async_manager import get_redis_client
from gacha.exceptions import (
    CardDescriptionTooLongError,
    CardDescriptionTooShortError,
    CardNotFoundError,
    CardPageNotFoundError,
    DatabaseOperationError,
    EntityNotFoundError,
    InsufficientCardStarError,
    UpdateCardDescriptionError,
)
from gacha.repositories.card import card_encyclopedia_repository, master_card_repository
from gacha.repositories.collection import user_collection_repository
from utils.logger import logger


async def get_card_for_page(
    page: int = 1,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    pool_type: Optional[str] = None,
    rarity: Optional[Union[int, List[int]]] = None,
    series_name: Optional[str] = None,
    card_id: Optional[int] = None,
    card_name: Optional[str] = None,
    user_id: Optional[int] = None,
    ownership_filter: str = "all",
) -> Dict[str, Any]:
    """獲取當前頁面的卡片圖鑑數據

    Args:
        page: 頁碼
        sort_by: 排序欄位
        sort_order: 排序順序
        pool_type: 卡池類型
        rarity: 稀有度 (數字)，可以是單一數字或數字列表
        series_name: 系列名稱
        card_id: 卡片ID (用於直接跳轉到該卡片頁面)
        card_name: 卡片名稱 (用於篩選)
        user_id: 用戶ID (用於擁有狀態篩選)
        ownership_filter: 擁有狀態篩選 ("all", "owned", "not_owned")

    Returns:
        包含卡片數據、擁有者數量以及分頁信息的字典。
        如果找不到符合條件的卡片，返回的字典中 'has_card' 為 False。

    Raises:
        DatabaseOperationError: 當數據庫操作失敗時
    """
    try:
        page_data = await card_encyclopedia_repository.get_paginated_card_id(
            page=page,
            sort_by=sort_by,
            sort_order=sort_order,
            pool_type=pool_type,
            rarity=rarity,
            series_name=series_name,
            card_id=card_id,
            card_name=card_name,
            user_id=user_id,
            ownership_filter=ownership_filter,
        )

        card_id_from_page = page_data.get(
            "card_id"
        )  # This will exist if repo didn't raise RecordNotFound
        if card_id_from_page is None:
            raise EntityNotFoundError("無法從分頁數據中獲取卡片ID")

        total_cards = page_data.get("total_cards", 0)
        total_pages = page_data.get("total_pages", 1)
        current_page_from_repo = page_data.get("current_page", 1)

        # card_id_from_page should always be present if get_paginated_card_id succeeded.
        # The repo now raises RecordNotFoundError if no card is found for the page/filters.

        # 使用組合策略：分離查詢基本數據和額外數據
        card_data = await _get_enhanced_card_data(card_id_from_page)
        # _get_enhanced_card_data also raises RecordNotFoundError if card_id_from_page is invalid (should not happen here)

        # 獲取市場統計數據
        owner_count, wishlist_count = await _get_market_stats(card_id_from_page)

        # 檢查用戶擁有狀態（如果提供了 user_id）
        user_owns_card = False
        if user_id is not None:
            from gacha.repositories.collection import user_collection_repository

            ownership_result = await user_collection_repository.check_cards_owned_batch(
                user_id, [card_id_from_page]
            )
            user_owns_card = ownership_result.get(card_id_from_page, False)

        # 將許願數據添加到卡片數據中
        if card_data and wishlist_count > 0:
            card_data["wishlist_count"] = wishlist_count

        return {
            "has_card": True,
            "card_data": card_data,
            "owner_count": owner_count,
            "total_cards": total_cards,
            "total_pages": total_pages,
            "current_page": current_page_from_repo,
            "user_owns_card": user_owns_card,
        }

    except EntityNotFoundError:  # Catch from encyclopedia_repo.get_paginated_card_id or get_card_encyclopedia_data
        # This means no card matches the filters or the specific page/card_id doesn't exist.
        # We need to return a structure indicating no card, but also total_cards/pages if they were found before the error.
        # Let's try to get total_cards/pages again with broader filters if card_id was specified
        # to give some context to the UI.

        # If the initial call to get_paginated_card_id failed with EntityNotFoundError,
        # it means no cards matched ANY filters. Total cards is 0.
        # If card_id was given and get_paginated_card_id for THAT card_id failed, total_cards might still be non-zero for the general filters.

        # Simplified: if EntityNotFoundError, it means no specific card for the current view. UI expects this.
        # The `page_data` might not be fully populated if EntityNotFoundError occurred early in `get_paginated_card_id`.
        # We can try to get a general count if `card_id` was specified, to show total pages for base filters.

        # Default to 0/1 if we can't get a better count after EntityNotFoundError
        final_total_cards = 0
        final_total_pages = 1
        final_current_page = page  # Use the requested page

        if (
            card_id is not None
        ):  # If search was for a specific card ID, try getting total counts for base filters
            try:
                # Get total counts without the specific card_id that wasn't found
                base_page_data = (
                    await card_encyclopedia_repository.get_paginated_card_id(
                        page=1,
                        sort_by=sort_by,
                        sort_order=sort_order,
                        pool_type=pool_type,
                        rarity=rarity,
                        series_name=series_name,
                        card_id=None,
                        card_name=card_name,  # No specific card_id here
                        user_id=user_id,
                        ownership_filter=ownership_filter,
                    )
                )
                final_total_cards = base_page_data.get("total_cards", 0)
                final_total_pages = base_page_data.get("total_pages", 1)
                # current_page remains the initially requested page, or 1 if out of new bounds
                final_current_page = max(
                    1, min(page, final_total_pages if final_total_pages > 0 else 1)
                )
            except EntityNotFoundError:  # No cards even with base filters
                pass  # final_total_cards/pages remain 0/1
            except DatabaseOperationError as db_err_fallback:
                logger.warning(
                    "Failed to get fallback total counts for encyclopedia: %s",
                    db_err_fallback,
                )
                # Let it propagate if severe, or keep 0/1 if just for UI display.
                # For now, keep 0/1 and the main error is already logged.
                pass

        return {
            "has_card": False,
            "card_data": None,  # Explicitly None
            "owner_count": 0,
            "total_cards": final_total_cards,
            "total_pages": final_total_pages,
            "current_page": final_current_page,
        }

    except DatabaseOperationError:  # Re-raise if it's a DB error not handled above
        raise
    except Exception as e:
        logger.error(
            "[GACHA][SERVICE] 獲取圖鑑卡片數據時發生未知錯誤: %s", str(e), exc_info=True
        )
        # Convert unexpected errors to DatabaseOperationError for consistent service-level error type
        raise DatabaseOperationError(
            f"獲取圖鑑卡片數據時發生服務層未知錯誤: {str(e)}"
        ) from e


async def get_card_series_list() -> List[str]:
    """獲取全部系列列表 - 委託給 MasterCardRepository"""
    try:
        return await master_card_repository.get_all_series()
    except Exception as e:
        logger.error(
            "[ENCYCLOPEDIA_SERVICE] 獲取系列列表失敗: %s", str(e), exc_info=True
        )
        raise DatabaseOperationError(f"獲取系列列表失敗: {str(e)}") from e


async def get_total_card_count() -> int:
    """獲取總卡片數量 - 委託給 MasterCardRepository"""
    try:
        return await master_card_repository.get_total_cards_count()
    except Exception as e:
        logger.error(
            "[ENCYCLOPEDIA_SERVICE] 獲取卡片總數失敗: %s", str(e), exc_info=True
        )
        raise DatabaseOperationError(f"獲取卡片總數失敗: {str(e)}") from e


async def update_card_description(
    user_id: int, card_id: int, description: str, min_star: int = 10
) -> None:
    """更新卡片描述

    Args:
        user_id: 用戶ID
        card_id: 卡片ID
        description: 卡片描述
        min_star: 允許更新描述的最低星級

    Raises:
        InsufficientCardStarError: 當卡片星級不足時
        CardDescriptionTooShortError: 當描述太短時
        CardDescriptionTooLongError: 當描述太長時
        UpdateCardDescriptionError: 當更新描述失敗時
        CardNotFoundError: 如果用戶根本沒有這張卡
    """
    # 1. 檢查使用者是否擁有該卡，並獲取其星級
    user_card = await user_collection_repository.get_user_card(user_id, card_id)
    if not user_card:
        raise CardNotFoundError(
            f"用戶 {user_id} 並不擁有卡片 {card_id}", card_id=card_id
        )

    current_star = user_card.star_level
    if current_star < min_star:
        raise InsufficientCardStarError(
            required_star=min_star, current_star=current_star
        )

    # 2. 驗證描述長度
    if not description or len(description.strip()) < 5:
        raise CardDescriptionTooShortError()

    if len(description.strip()) > 200:
        raise CardDescriptionTooLongError()

    # 3. 清理描述內容
    cleaned_description = bleach.clean(description.strip(), tags=[], strip=True)

    # 4. 執行更新
    try:
        await card_encyclopedia_repository.update_card_description(
            user_id=user_id, card_id=card_id, description=cleaned_description
        )
    except UpdateCardDescriptionError:
        # 直接向上傳遞已知的特定異常
        raise
    except Exception as e:
        logger.error(
            "[GACHA] 更新卡片描述時發生未預期的資料庫錯誤 (Service): %s",
            str(e),
            exc_info=True,
        )
        raise UpdateCardDescriptionError(f"更新卡片描述時發生錯誤: {str(e)}") from e


async def find_card_page_info_by_id(
    target_card_id: int,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    pool_type: Optional[str] = None,
    rarity: Optional[Union[int, List[int]]] = None,
    series_name: Optional[str] = None,
    user_id: Optional[int] = None,
    ownership_filter: str = "all",
) -> int:
    """找出指定卡片在圖鑑中的頁碼

    Args:
        target_card_id: 要查找的卡片ID
        sort_by: 排序欄位
        sort_order: 排序順序
        pool_type: 卡池類型過濾
        rarity: 稀有度過濾
        series_name: 系列名稱過濾

    Returns:
        卡片所在的頁碼

    Raises:
        CardPageNotFoundError: 如果卡片不存在或不符合過濾條件 (可能包含更詳細的原因)
        DatabaseOperationError: 如果底層數據庫操作失敗
    """
    try:
        # get_paginated_card_id will raise RecordNotFoundError if card is not found under current filters
        # or if the card_id itself implies a non-match to other active filters.
        page_info_from_repo = await card_encyclopedia_repository.get_paginated_card_id(
            page=1,  # Not used when card_id is present, but required by repo method signature
            sort_by=sort_by,
            sort_order=sort_order,
            pool_type=pool_type,
            rarity=rarity,
            series_name=series_name,
            card_id=target_card_id,  # This is the key: repo will find this card's page
            card_name=None,
            user_id=user_id,
            ownership_filter=ownership_filter,
        )

        # If get_paginated_card_id did not raise an error, it found the card and its page.
        found_page = page_info_from_repo.get("current_page")
        returned_card_id_check = page_info_from_repo.get("card_id")

        # Double check, though repo logic should ensure this if no error was raised
        if found_page is None or returned_card_id_check != target_card_id:
            # This case should ideally be covered by RecordNotFoundError from the repo
            logger.warning(
                "[GACHA][SERVICE] find_card_page_info_by_id: Repo returned page data for card %s but data seems inconsistent.",
                target_card_id,
            )
            filters_desc = _build_filters_description(
                sort_by, sort_order, pool_type, rarity, series_name, ownership_filter
            )
            raise CardPageNotFoundError(
                card_id=target_card_id,
                message=f"無法精確定位ID為 {target_card_id} 的卡片頁面（{filters_desc}）。資料可能不一致。",
            )

        return found_page

    except EntityNotFoundError as e:
        # EntityNotFoundError from repo means the card was not found with the given filters/ID.
        # Convert to a service-level exception CardPageNotFoundError, preserving the original more detailed message if possible.
        logger.info(
            "[GACHA][SERVICE] find_card_page_info_by_id: Card %s not found by repository. Original error: %s",
            target_card_id,
            str(e),
        )

        # 如果是擁有狀態篩選導致的問題，提供更清楚的錯誤訊息
        if ownership_filter != "all" and user_id is not None:
            # 檢查卡片是否存在（不考慮擁有狀態）
            try:
                await card_encyclopedia_repository.get_paginated_card_id(
                    page=1,
                    sort_by=sort_by,
                    sort_order=sort_order,
                    pool_type=pool_type,
                    rarity=rarity,
                    series_name=series_name,
                    card_id=target_card_id,
                    card_name=None,
                    user_id=None,  # 不考慮擁有狀態
                    ownership_filter="all",
                )
                # 如果卡片存在但不符合擁有狀態篩選
                ownership_desc = {"owned": "已擁有", "not_owned": "未擁有"}.get(
                    ownership_filter, ownership_filter
                )
                raise CardPageNotFoundError(
                    card_id=target_card_id,
                    message=f"找不到 ID 為 {target_card_id} 的卡片，或該卡片不符合擁有狀態篩選（{ownership_desc}）。",
                ) from e
            except EntityNotFoundError:
                # 卡片本身不存在
                pass

        filters_desc = _build_filters_description(
            sort_by, sort_order, pool_type, rarity, series_name, ownership_filter
        )
        # Use the message from EntityNotFoundError as it's more specific
        raise CardPageNotFoundError(
            card_id=target_card_id,
            message=f"找不到 ID 為 {target_card_id} 的卡片，或該卡片不符合目前的篩選條件。 (篩選: {filters_desc})",
        ) from e

    except DatabaseOperationError:  # Re-raise DatabaseOperationError directly
        raise
    except (
        Exception
    ) as e:  # Catch any other unexpected error from the repo or this service method
        logger.error(
            "[GACHA][SERVICE] Unexpected error in find_card_page_info_by_id for card_id %s: %s",
            target_card_id,
            str(e),
            exc_info=True,
        )
        filters_desc = _build_filters_description(
            sort_by, sort_order, pool_type, rarity, series_name, ownership_filter
        )
        raise CardPageNotFoundError(
            card_id=target_card_id,
            message=f"查詢卡片 {target_card_id} 頁面時發生未知服務錯誤（{filters_desc}）。",
        ) from e


def _build_filters_description(
    sort_by: str,
    sort_order: str,
    pool_type: Optional[str],
    rarity: Optional[Union[int, List[int]]],
    series_name: Optional[str],
    ownership_filter: str = "all",
) -> str:
    """Helper to build a string representation of filters for error messages."""
    filters_parts = [f"排序={sort_by}", f"順序={sort_order}"]
    if pool_type:
        filters_parts.append(f"卡池={pool_type}")
    if rarity:
        rarity_str = (
            ",".join(map(str, rarity)) if isinstance(rarity, list) else str(rarity)
        )
        filters_parts.append(f"稀有度={rarity_str}")
    if series_name:
        filters_parts.append(f"系列={series_name}")
    if ownership_filter != "all":
        ownership_desc = {"owned": "僅已擁有", "not_owned": "僅未擁有"}.get(
            ownership_filter, ownership_filter
        )
        filters_parts.append(f"擁有狀態={ownership_desc}")
    return ", ".join(filters_parts)


async def _get_enhanced_card_data(card_id: int) -> Dict[str, Any]:
    """獲取增強的卡片數據 - 組合基本數據和圖鑑額外數據

    使用組合策略：
    1. 從 MASTER 緩存獲取基本卡片數據
    2. 從數據庫獲取圖鑑額外數據
    3. 在內存中組合

    Args:
        card_id: 卡片ID

    Returns:
        組合後的卡片數據字典

    Raises:
        EntityNotFoundError: 當卡片不存在時
        DatabaseOperationError: 當數據庫操作失敗時
    """
    try:
        # 第一步：從 MASTER 緩存獲取基本卡片數據
        card = await master_card_repository.get_card(card_id)
        if card is None:
            raise CardNotFoundError(f"找不到卡片: {card_id}")
        base_card_data = {
            "card_id": card.card_id,
            "name": card.name,
            "series": card.series,
            "rarity": card.rarity.value if card.rarity else None,
            "image_url": card.image_url,
            "description": card.description,
            "current_market_sell_price": card.current_market_sell_price,
            "creation_date": card.creation_date,
            "pool_type": card.pool_type,
            "original_id": card.original_id,
        }

        # 第二步：獲取圖鑑額外數據
        extra_data = await _get_encyclopedia_extra_data(card_id)

        # 第三步：組合數據
        enhanced_data = {**base_card_data, **extra_data}

        logger.debug("[ENCYCLOPEDIA_SERVICE] 增強卡片數據組合完成: %s", card_id)
        return enhanced_data

    except CardNotFoundError as e:
        # 轉換為 EntityNotFoundError 以保持與原有邏輯一致
        logger.warning("[ENCYCLOPEDIA_SERVICE] 卡片不存在: %s", card_id)
        raise EntityNotFoundError(f"卡片 {card_id} 不存在") from e
    except Exception as e:
        logger.error(
            "[ENCYCLOPEDIA_SERVICE] 獲取增強卡片數據失敗: %s, 錯誤: %s",
            card_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"獲取增強卡片數據失敗: {e}") from e


async def _get_encyclopedia_extra_data(card_id: int) -> Dict[str, Any]:
    """獲取圖鑑額外數據 - 委託給 Repository

    Args:
        card_id: 卡片ID

    Returns:
        圖鑑額外數據字典
    """
    try:
        result = await card_encyclopedia_repository.get_encyclopedia_extra_data(card_id)
        if result:
            return result
        else:
            # 沒有最高星級記錄，返回空數據
            return _get_empty_extra_data()
    except Exception as e:
        logger.error(
            "[ENCYCLOPEDIA_SERVICE] 獲取圖鑑額外數據失敗: %s, 錯誤: %s",
            card_id,
            e,
            exc_info=True,
        )
        # 返回空數據作為降級處理
        return _get_empty_extra_data()


def _get_empty_extra_data() -> Dict[str, Any]:
    """獲取空的圖鑑額外數據"""
    return {
        "highest_star_user_id": None,
        "highest_star_level": 0,
        "achieved_at": None,
        "nickname": None,
        "custom_description": None,
        "description_user_id": None,
    }


async def _get_market_stats(card_id: int) -> tuple[int, int]:
    """獲取市場統計數據

    返回:
        tuple[int, int]: (owner_count, wishlist_count)
    """
    owner_count = 0
    wishlist_count = 0

    try:
        # 使用 market_stats 表查詢，比實時查詢更快
        from gacha.services.card_info_service import get_card_market_stats

        market_stats = await get_card_market_stats(card_id)

        if market_stats:
            owner_count = market_stats.unique_owner_count or 0
            wishlist_count = market_stats.wishlist_count or 0

        # 如果沒有從 market_stats 獲取到擁有者數量，降級到實時查詢
        if owner_count == 0 and (
            not market_stats or market_stats.unique_owner_count is None
        ):
            redis_client = get_redis_client()
            from gacha.repositories.collection import user_collection_repository

            if redis_client is None:
                logger.warning(
                    "Redis client is not initialized, skipping owner count fallback."
                )
            else:
                owner_counts_dict = (
                    await user_collection_repository.get_card_owner_counts_batch(
                        redis_client, [card_id]
                    )
                )
                owner_count = owner_counts_dict.get(card_id, 0)

    except Exception as e:
        logger.error(
            f"[GACHA][ENCYCLOPEDIA] Failed to fetch market stats for card {card_id}: {e}",
            exc_info=True,
        )
        # 保持默認值 0

    return owner_count, wishlist_count
