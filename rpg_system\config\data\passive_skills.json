{"PASSIVE_TRNEND_ATK_DBF_R1": {"name": "力量枯竭", "description_template": "回合結束時，降低敵方攻擊力", "skill_rarity": 1, "max_level": 7, "base_effects": [{"trigger_condition": {"type": "ON_TURN_END", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "APPLY_ATK_DEBUFF", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.46 + skill_level * 0.021))", "value_overrides": {"atk": "-(18.0 + skill_level * 3.24) / 100"}}]}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 55, "multiplier": 1.2}, "tags": ["PASSIVE", "TURN_BASED", "DEBUFF"]}, "PASSIVE_STSAPP_PHYS_DMG_R1": {"name": "快速斬擊", "description_template": "狀態效果生效時，造成物理傷害", "skill_rarity": 1, "max_level": 7, "base_effects": [{"trigger_condition": {"type": "ON_STATUS_EFFECT_APPLIED", "chance_formula": "0.24"}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": "(0.78 + skill_level * 0.11)", "can_crit": true}]}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 55, "multiplier": 1.2}, "tags": ["PASSIVE", "DAMAGE"]}, "PASSIVE_HLRCV_SPD_DBF_R2": {"name": "快速遲緩", "description_template": "受到治療時，降低敵方速度", "skill_rarity": 2, "max_level": 9, "base_effects": [{"trigger_condition": {"type": "ON_HEAL_RECEIVED", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "APPLY_SPD_DEBUFF", "duration_turns": "(1 + floor(skill_level / 3))", "chance": "(min(1.0, 0.67 + skill_level * 0.020))", "value_overrides": {"spd": "-(6.0 + skill_level * 0.96) / 100"}}]}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.25}, "tags": ["PASSIVE", "DEBUFF"]}, "PASSIVE_TRNSTR_SPD_BST_R2": {"name": "速度激發", "description_template": "回合開始時，提升速度", "skill_rarity": 2, "max_level": 9, "base_effects": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "APPLY_SPD_BOOST", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.87 + skill_level * 0.025))", "value_overrides": {"spd": "(10.0 + skill_level * 1.60) / 100"}}]}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.25}, "tags": ["PASSIVE", "TURN_BASED", "BUFF"]}, "PASSIVE_HPTHR_HL_PERCE_R3": {"name": "強力魔法回復", "description_template": "生命值低於閾值時，恢復基於魔攻的生命值", "skill_rarity": 3, "max_level": 11, "base_effects": [{"trigger_condition": {"type": "ON_HP_THRESHOLD_REACHED", "chance_formula": "1.0", "params": {"threshold_percent_formula": "0.5", "check_direction": "below"}, "trigger_once_per_battle": true}, "effect_definitions": [{"effect_template": "BASIC_HEAL_PERCENT_CASTER_MATK", "value": "(0.87 + skill_level * 0.11)"}]}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 85, "multiplier": 1.3}, "tags": ["PASSIVE", "HEAL"]}, "PASSIVE_TRNEND_PHYS_DMG_R3": {"name": "高級物理衝擊·極", "description_template": "回合結束時，造成物理傷害", "skill_rarity": 3, "max_level": 11, "base_effects": [{"trigger_condition": {"type": "ON_TURN_END", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": "(1.48 + skill_level * 0.30)", "can_crit": true}]}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 85, "multiplier": 1.3}, "tags": ["PASSIVE", "TURN_BASED", "DAMAGE"]}, "PASSIVE_TRNSTR_CONDITIO_R4": {"name": "強力條件傷害", "description_template": "回合開始時，在特定條件下造成額外傷害", "skill_rarity": 4, "max_level": 13, "base_effects": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "CONDITIONAL_DAMAGE_BOOST", "multiplier": "(1.07 + skill_level * 0.14)", "can_crit": true}]}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 100, "multiplier": 1.35}, "tags": ["PASSIVE", "TURN_BASED", "BUFF", "RARE"]}, "PASSIVE_HLRCV_RESOURCE_R4": {"name": "高級資源壓制", "description_template": "受到治療時，利用資源優勢攻擊", "skill_rarity": 4, "max_level": 13, "base_effects": [{"trigger_condition": {"type": "ON_HEAL_RECEIVED", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "RESOURCE_ADVANTAGE_BLAST", "multiplier": "(1.08 + skill_level * 0.13)", "can_crit": true}], "target_override": {"selector_type": "ALL_ENEMIES", "max_targets": 2}}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 100, "multiplier": 1.35}, "tags": ["PASSIVE", "RARE"]}, "PASSIVE_STSAPP_HL_PERCE_R5": {"name": "究極魔力治療", "description_template": "狀態效果生效時，恢復基於魔攻的生命值", "skill_rarity": 5, "max_level": 15, "base_effects": [{"trigger_condition": {"type": "ON_STATUS_EFFECT_APPLIED", "chance_formula": "0.70"}, "effect_definitions": [{"effect_template": "BASIC_HEAL_PERCENT_CASTER_MATK", "value": "(0.76 + skill_level * 0.09)"}]}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 115, "multiplier": 1.4}, "tags": ["PASSIVE", "HEAL", "RARE"]}, "PASSIVE_HPTHR_CONDITIO_R5": {"name": "究極快速條傷", "description_template": "生命值低於閾值時，在特定條件下造成額外傷害", "skill_rarity": 5, "max_level": 15, "base_effects": [{"trigger_condition": {"type": "ON_HP_THRESHOLD_REACHED", "chance_formula": "1.0", "params": {"threshold_percent_formula": "0.75", "check_direction": "below"}, "trigger_once_per_battle": true}, "effect_definitions": [{"effect_template": "CONDITIONAL_DAMAGE_BOOST", "multiplier": "(0.59 + skill_level * 0.10)", "can_crit": true}], "target_override": {"selector_type": "ALL_ENEMIES", "max_targets": 3}}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 115, "multiplier": 1.4}, "tags": ["PASSIVE", "BUFF", "RARE"]}, "PASSIVE_STSAPP_MAG_DMG_R6": {"name": "傳說魔力衝擊", "description_template": "狀態效果生效時，造成魔法傷害", "skill_rarity": 6, "max_level": 17, "base_effects": [{"trigger_condition": {"type": "ON_STATUS_EFFECT_APPLIED", "chance_formula": "0.80"}, "effect_definitions": [{"effect_template": "BASIC_MAGICAL_DAMAGE", "multiplier": "(1.03 + skill_level * 0.18)", "can_crit": true}]}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.45}, "tags": ["PASSIVE", "DAMAGE", "LEGENDARY"]}, "PASSIVE_STSAPP_ATK_BST_R6": {"name": "傳說武力覺醒", "description_template": "狀態效果生效時，提升攻擊力", "skill_rarity": 6, "max_level": 17, "base_effects": [{"trigger_condition": {"type": "ON_STATUS_EFFECT_APPLIED", "chance_formula": "0.80"}, "effect_definitions": [{"effect_template": "APPLY_ATK_BOOST", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.84 + skill_level * 0.026))", "stack_count": 3, "value_overrides": {"patk": "(20.0 + skill_level * 3.30) / 100", "matk": "(20.0 + skill_level * 3.30) / 100"}}]}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.45}, "tags": ["PASSIVE", "BUFF", "LEGENDARY"]}, "PASSIVE_TRNSTR_EXECUTE__R7": {"name": "至尊處決之刃·極", "description_template": "回合開始時，對低血量敵人造成斬殺傷害", "skill_rarity": 7, "max_level": 19, "base_effects": [{"trigger_condition": {"type": "ON_TURN_START", "chance_formula": "1.0"}, "effect_definitions": [{"effect_template": "EXECUTE_DAMAGE", "multiplier": "(1.73 + skill_level * 0.32)", "can_crit": true}], "target_override": {"selector_type": "ALL_ENEMIES", "max_targets": 2}}], "xp_gain_on_sacrifice": 29, "xp_to_next_level_config": {"base_xp": 145, "multiplier": 1.5}, "tags": ["PASSIVE", "TURN_BASED", "DAMAGE", "LEGENDARY"]}, "PASSIVE_HPTHR_DRAIN_PH_R7": {"name": "神聖吸血攻擊", "description_template": "生命值低於閾值時，造成傷害並吸血", "skill_rarity": 7, "max_level": 19, "base_effects": [{"trigger_condition": {"type": "ON_HP_THRESHOLD_REACHED", "chance_formula": "1.0", "params": {"threshold_percent_formula": "0.5", "check_direction": "below"}, "trigger_once_per_battle": true}, "effect_definitions": [{"effect_template": "DRAIN_PHYSICAL", "multiplier": "(1.10 + skill_level * 0.12)", "can_crit": true}]}], "xp_gain_on_sacrifice": 29, "xp_to_next_level_config": {"base_xp": 145, "multiplier": 1.5}, "tags": ["PASSIVE", "LEGENDARY"]}}