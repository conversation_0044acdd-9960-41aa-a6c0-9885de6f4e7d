# 🏗️ Pioneer System - 開拓者指令系統

一個完全數據驅動的經濟模擬遊戲系統，採用統一規則引擎架構，支援熱重載配置和無限擴展。

## ✨ 核心特性

### 🎯 數據驅動設計
- **統一規則引擎**: 所有遊戲邏輯由配置文件驅動，無需修改代碼
- **熱重載配置**: 支援運行時重新載入配置文件，無需重啟
- **極致可維護性**: 遊戲內容與程式邏輯完全分離

### 🏭 萬物皆設施架構
- **統一設施系統**: 商店、工廠、研究室都是設施的不同配置
- **通用槽位系統**: 支援輸入、輸出、存儲、燃料等多種槽位類型
- **模組化升級**: 每個設施都有獨立的升級系統

### 🔬 無限升級研究
- **指數成本公式**: 支援複雜的成本計算公式
- **統一油幣系統**: 所有升級和研究統一使用油幣
- **效果疊加**: 研究效果可以疊加到各種遊戲機制上

### ⚖️ 經濟平衡機制
- **負期望值設計**: 確保玩家無法無限獲利
- **通脹控制**: 內建經濟平衡參數
- **資源稀缺性**: 通過技能等級和時間限制控制產出

## 📁 系統架構

```
pioneer/
├── core/                    # 核心引擎
│   ├── game_data_loader.py  # 配置載入器
│   ├── rule_engine.py       # 統一規則引擎
│   └── processors/          # 動作處理器
├── config/                  # 配置文件
│   ├── economy.yaml         # 經濟參數
│   ├── items.yaml          # 物品定義
│   ├── recipes.yaml        # 配方定義
│   ├── facilities.yaml     # 設施定義
│   ├── actions.yaml        # 動作定義
│   ├── eras.yaml           # 時代配置
│   ├── research_projects.yaml # 研究項目
│   └── tasks.yaml          # 任務配置
├── repositories/           # 數據存取層
├── views/                  # Discord UI 組件
├── cogs/                   # Discord Cog
└── models/                 # 數據模型
```

## 🚀 快速開始

### 1. 系統檢查
```bash
python -m pioneer.setup check
```

### 2. 初始化系統
```bash
python -m pioneer.setup init
```

### 3. 載入 Discord Cog
```python
# 在您的 Discord bot 中
await bot.load_extension('pioneer.cogs.pioneer_cog')
```

### 4. 使用指令
- `/pioneer` - 開啟主面板
- `/pioneer_profile` - 查看個人資料
- `/pioneer_warehouse` - 查看倉庫
- `/pioneer_gather <action>` - 執行採集
- `/pioneer_craft <recipe> [quantity]` - 執行製作

## 🎮 遊戲機制

### 時代進程系統
1. **拓荒時代** - 學習基本採集和製作
2. **工業萌芽** - 建造基礎設施
3. **重工業時代** - 發展冶金和研究
4. **自動化革命** - 大規模自動化
5. **科技飛躍** - 最先進技術
6. **未來展望** - 預留擴展空間

### 技能系統
- **經驗累積**: 每級需要 `level * 100` 經驗
- **技能加成**: 每級提供 1% 產出加成
- **等級門檻**: 高級內容需要對應技能等級

### 設施系統
- **生產設施**: 無中生有型（伐木小屋、採石場）
- **加工設施**: 轉化型（熔爐、木工台）
- **銷售設施**: 商店型（開拓者商店）
- **研究設施**: 研究型（研究實驗室）
- **存儲設施**: 倉庫型（倉庫）

### 研究系統
- **無限升級**: 大部分項目無等級上限
- **指數成本**: 使用公式 `base_cost * (multiplier ** level)`
- **效果疊加**: 研究效果影響各種遊戲機制

## ⚙️ 配置系統

### 物品配置範例
```yaml
wood:
  name: "木材"
  description: "最基本的建築材料"
  category: "resource"
  tier: 1
  base_sell_price: 10
  stack_size: 1000
  effects: []
```

### 設施配置範例
```yaml
lumber_mill:
  name: "伐木小屋"
  process_type: "generate"
  process_time: 3600
  build_cost: 2000000
  inputs: []
  outputs:
    - item_id: "wood"
      quantity: 20
  upgrades:
    level:
      cost_formula: "500000 * (1.25 ** level)"
      effect: "output * 1.15"
```

### 動作配置範例
```yaml
gather_wood:
  name: "伐木"
  type: "gather"
  energy_cost: 10
  requirements:
    - type: "skill_level"
      skill: "woodcutting"
      min_level: 1
  outputs:
    - skill: "woodcutting"
      level_range: [1, 9]
      items:
        wood:
          amount: [1, 3]
          chance: 1.0
          xp: 1
```

## 🔧 開發指南

### 添加新物品
1. 在 `config/items.yaml` 中定義物品
2. 系統會自動識別並載入
3. 使用 `/pioneer_reload items` 熱重載

### 添加新設施
1. 在 `config/facilities.yaml` 中定義設施
2. 在 `config/actions.yaml` 中添加建造動作
3. 系統會自動處理設施邏輯

### 添加新動作
1. 在 `config/actions.yaml` 中定義動作
2. 如需特殊邏輯，在對應處理器中實現
3. 統一規則引擎會自動路由

### 自訂處理器
```python
class CustomProcessor(BaseProcessor):
    async def execute(self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]) -> ActionResult:
        # 實現自訂邏輯
        return ActionResult.success_result("成功執行自訂動作")
```

## 📊 經濟設計理念

### 負期望值原則
- 所有遊戲機制都設計為長期負期望值
- 防止玩家無限獲利
- 保持經濟平衡

### 油幣稀缺性
- 油幣主要來源於外部系統（抽卡）
- 用於質量性升級而非線性升級
- 創造稀缺性和價值感

### 時間價值
- 離線收益有限制
- 鼓勵定期互動
- 平衡休閒和硬核玩家

## 🛠️ 維護指南

### 配置熱重載
```bash
# 重載單個配置
/pioneer_reload items

# 重載所有配置
/pioneer_reload
```

### 系統診斷
```bash
# 檢查系統狀態
/pioneer_debug

# 驗證配置完整性
python -m pioneer.setup validate
```

### 數據庫維護
- 所有表都有適當的索引
- 支援大量用戶並發操作
- 定期清理無效數據

## 📈 性能優化

### 數據庫優化
- 複合索引支援複雜查詢
- 分頁查詢避免大量數據載入
- 連接池管理數據庫連接

### 記憶體優化
- 配置文件按需載入
- 視圖組件及時清理
- 避免循環引用

### 網路優化
- Discord 互動響應時間 < 3秒
- 批量操作減少 API 調用
- 適當的錯誤處理和重試

## 🔮 未來規劃

### 短期目標
- [ ] 完善任務系統
- [ ] 添加更多設施類型
- [ ] 優化 UI 體驗

### 中期目標
- [ ] 多人合作功能
- [ ] 公會系統
- [ ] 市場交易

### 長期目標
- [ ] 跨服務器支援
- [ ] 移動端適配
- [ ] AI 驅動的動態平衡

## 📝 版本歷史

### v1.0.0 (2025-07-01)
- ✨ 初始版本發布
- 🏗️ 完整的核心系統架構
- 📁 所有基礎配置文件
- 🎮 Discord 介面完成
- 📊 經濟平衡機制

## 🤝 貢獻指南

歡迎提交 Issue 和 Pull Request！

### 開發環境設置
1. Clone 專案
2. 安裝依賴
3. 運行系統檢查
4. 開始開發

### 代碼規範
- 遵循 PEP 8
- 添加適當的註釋
- 編寫單元測試
- 更新文檔

## 📄 授權

本專案採用 MIT 授權條款。

---

**Pioneer System** - 讓數據驅動遊戲的未來 🚀
