"""
每日獎勵系統 COG
處理 /daily 指令，完全內置邏輯，無需服務層
"""

import random
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, Any, Dict, Union, cast

import discord
import pytz
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from config.app_config import get_config
from database.postgresql.async_manager import get_pool
from gacha.exceptions import BusinessError
from gacha.services import (
    captcha_service,
    economy_service,
    user_service,
    validation_service,
)
from gacha.services.activity_service import get_captcha_chance
from gacha.views.captcha_view import CaptchaView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# 導入 BotType 從 bot.py

# 台灣時區常量
TW_TIMEZONE = pytz.timezone("Asia/Taipei")


class DailyCog(commands.Cog, name="每日獎勵"):
    """處理每日獎勵領取指令，內置所有邏輯"""

    def __init__(self, bot: BotType):
        self.bot = bot
        # 從配置獲取獎勵金額
        self.DAILY_REWARD = get_config("gacha_core_settings.economy_daily_reward", 5000)
        max_fails_config = get_config("gacha_core_settings.daily_captcha_max_fails", 3)
        try:
            self.CAPTCHA_MAX_FAILS = int(cast(Union[str, int], max_fails_config))
        except (ValueError, TypeError):
            self.CAPTCHA_MAX_FAILS = 3
        logger.info("DailyCog initialized.")

    async def cog_load(self):
        logger.info("DailyCog has been loaded.")

    def _calculate_next_daily_reset(self) -> int:
        """計算下次每日重置時間戳"""
        current_time_utc = datetime.now(timezone.utc)
        current_time_tw = current_time_utc.astimezone(TW_TIMEZONE)

        # 計算台灣時間下一個日期的午夜時間
        next_day_tw = current_time_tw.date() + timedelta(days=1)
        next_midnight_tw = datetime.combine(next_day_tw, datetime.min.time())
        next_midnight_tw = TW_TIMEZONE.localize(next_midnight_tw)

        return int(next_midnight_tw.timestamp())

    async def _check_daily_cooldown(self, user_id: int):
        """檢查使用者是否可以領取每日獎勵"""
        pool = get_pool()
        async with pool.acquire() as conn:
            # 只讀取用戶數據，不需要鎖定
            user = await user_service.get_user(user_id, connection=conn)
            if user and not user.can_claim_daily:
                next_claim_timestamp = self._calculate_next_daily_reset()
                timestamp_msg = f"<t:{next_claim_timestamp}:R>"
                raise BusinessError(f"今天已經領取過了，請在 {timestamp_msg} 後再來！")

    async def _ensure_user_exists(self, user_id: int, nickname: str) -> None:
        """確保用戶存在，如果不存在則創建"""
        # 使用 validation_service 來處理用戶創建
        await validation_service.ensure_user_exists(
            user_id, nickname=nickname, create_if_missing=True
        )

        # 使用 user_service 來更新暱稱
        await user_service.ensure_nickname_updated(user_id, nickname)

    async def _claim_daily_reward(self, user_id: int) -> Dict[str, Any]:
        """處理每日獎勵領取邏輯"""
        pool = get_pool()
        async with pool.acquire() as conn:
            async with conn.transaction():
                # 所有用戶都獲得滿額獎勵

                try:
                    if isinstance(self.DAILY_REWARD, (int, float)):
                        base_reward = int(self.DAILY_REWARD)
                    elif isinstance(self.DAILY_REWARD, str):
                        base_reward = int(self.DAILY_REWARD)
                    else:
                        base_reward = 5000
                except (ValueError, TypeError, AttributeError):
                    base_reward = 5000

                # 計算最終獎勵（滿額獎勵）
                final_reward = base_reward

                # 使用 economy_service 來發放獎勵
                new_balance = await economy_service.award_oil(
                    user_id=user_id,
                    amount=final_reward,
                    transaction_type="reward:daily",
                    reason="Daily claim",
                    connection=conn,
                )
                await user_service.update_daily_claim(user_id, connection=conn)

                return {
                    "new_balance": new_balance,
                    "reward": final_reward,
                }

    async def _send_success_message(
        self, interaction: discord.Interaction, result: Dict[str, Any]
    ):
        """發送成功的獎勵訊息"""
        from config.app_config import get_oil_emoji

        embed = SuccessEmbed(description=f"成功領取 **{result['reward']:,}** 油幣！")
        embed.title = "每日獎勵"
        embed.add_field(
            name="當前餘額",
            value=f"{get_oil_emoji()} `{result['new_balance']:,}`",
            inline=False,
        )

        if interaction.user.display_avatar:
            embed.set_thumbnail(url=interaction.user.display_avatar.url)
        await interaction.followup.send(embed=embed, ephemeral=False)

    @app_commands.command(name="daily", description="領取每日獎勵油幣")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def daily(self, interaction: discord.Interaction):
        """處理每日獎勵領取命令"""
        await interaction.response.defer(thinking=True, ephemeral=False)

        user_id = interaction.user.id

        # [CRITICAL-1] 修復：在指令開頭檢查是否有待處理的驗證碼
        # 建立一個臨時的 service 實例來做檢查
        # 注意：這裡的回呼函式用 lambda: None 替代，因為我們只關心 pending 狀態
        temp_captcha_service = captcha_service.CaptchaService(
            bot=self.bot,
            cog_name="daily",
            user_id=user_id,
            on_correct=lambda i: None,  # type: ignore
            on_final_fail=lambda i: None,  # type: ignore
            on_timeout=lambda: None,  # type: ignore
        )
        if await temp_captcha_service.is_captcha_pending():
            from gacha.exceptions import CaptchaPendingError

            raise CaptchaPendingError()

        current_nickname = interaction.user.display_name

        await self._ensure_user_exists(user_id, current_nickname)
        await self._check_daily_cooldown(user_id)

        captcha_chance = await get_captcha_chance(user_id)
        if random.random() < captcha_chance:
            await self._handle_captcha(interaction)
        else:
            result = await self._claim_daily_reward(user_id)
            await self._send_success_message(interaction, result)

    async def _handle_captcha(self, interaction: discord.Interaction):
        """處理驗證碼流程"""

        async def on_correct(inner_interaction: discord.Interaction) -> bool:
            await self._check_daily_cooldown(interaction.user.id)
            result = await self._claim_daily_reward(interaction.user.id)

            from config.app_config import get_oil_emoji

            embed = SuccessEmbed(
                description=f"✅ 驗證成功！\n\n成功領取 **{result['reward']:,}** 油幣！"
            )
            embed.title = "每日獎勵"
            embed.add_field(
                name="當前餘額",
                value=f"{get_oil_emoji()} `{result['new_balance']:,}`",
                inline=False,
            )

            if inner_interaction.user.display_avatar:
                embed.set_thumbnail(url=inner_interaction.user.display_avatar.url)

            await inner_interaction.response.edit_message(
                embed=embed, view=None, attachments=[]
            )
            return True

        async def on_final_fail(inner_interaction: discord.Interaction) -> bool:
            # 懲罰：將今天的每日獎勵標記為已領取
            await user_service.update_daily_claim(interaction.user.id)
            embed = discord.Embed(
                title="❌ 驗證失敗",
                description="驗證失敗次數過多，今天的每日獎勵已無法領取。",
                color=discord.Color.red(),
            )
            await inner_interaction.response.edit_message(
                embed=embed, view=None, attachments=[]
            )
            return True  # 停止 View

        async def on_timeout():
            # 懲罰：將今天的每日獎勵標記為已領取
            await user_service.update_daily_claim(interaction.user.id)
            embed = discord.Embed(
                title="⌛️ 驗證超時",
                description="安全驗證已超時，今天的每日獎勵已無法領取。",
                color=discord.Color.orange(),
            )
            try:
                await interaction.edit_original_response(
                    embed=embed, view=None, attachments=[]
                )
            except discord.NotFound:
                pass  # 訊息可能已被使用者刪除

        async def on_refresh(
            inner_interaction: discord.Interaction, view: "CaptchaView"
        ) -> None:
            """處理更換驗證碼的邏輯（目前由 Service 和 View 處理）。"""
            pass

        captcha_handler = captcha_service.CaptchaService(
            bot=self.bot,
            cog_name="daily",
            user_id=interaction.user.id,
            on_correct=on_correct,
            on_final_fail=on_final_fail,
            on_timeout=on_timeout,
            on_refresh=on_refresh,
            max_fails=self.CAPTCHA_MAX_FAILS,
        )
        await captcha_handler.handle_captcha(interaction)


async def setup(bot: BotType):
    """載入 DailyCog"""
    await bot.add_cog(DailyCog(bot))
    logger.info("DailyCog has been added to the bot.")
