# 卡片匯入系統

這個資料夾包含所有與卡片匯入、導出和處理相關的檔案和數據。

## 資料夾結構

```
card_import/
├── scripts/           # 卡片處理腳本
│   ├── import_cards.py                    # 通用卡片匯入腳本
│   ├── import_wixoss_cards.py            # WIXOSS 專用卡片匯入腳本
│   ├── export_cards_by_pool.py           # 按卡池導出卡片腳本
│   └── pre_download_master_card_images.py # 預下載卡片圖片腳本
├── data/              # 卡片數據檔案
│   ├── hololive_card.json                # Hololive 卡片數據
│   ├── pokemon_cards_jp_updated.json     # 寶可夢卡片數據
│   ├── union_arena_data.json             # Union Arena 卡片數據
│   └── wixoss_simplified.json            # WIXOSS 卡片數據
└── README.md          # 本說明檔案
```

## 腳本說明

### import_cards.py
通用卡片匯入腳本，支援多種卡片格式：
- mazoku
- valentine  
- hololive
- ua (Union Arena)
- ptcg (Pokemon TCG)

**使用方法：**
```bash
python scripts/card_import/scripts/import_cards.py <格式類型> <檔案路徑> <卡池類型>
```

**範例：**
```bash
python scripts/card_import/scripts/import_cards.py hololive scripts/card_import/data/hololive_card.json hololive
python scripts/card_import/scripts/import_cards.py ptcg scripts/card_import/data/pokemon_cards_jp_updated.json ptcg
python scripts/card_import/scripts/import_cards.py ua scripts/card_import/data/union_arena_data.json ua
```

### import_wixoss_cards.py
WIXOSS 專用卡片匯入腳本，針對 WIXOSS 卡池優化。

**使用方法：**
```bash
python scripts/card_import/scripts/import_wixoss_cards.py <檔案路徑>
```

**範例：**
```bash
python scripts/card_import/scripts/import_wixoss_cards.py scripts/card_import/data/wixoss_simplified.json
```

### export_cards_by_pool.py
按卡池導出卡片數據的腳本，可以將資料庫中的卡片按卡池分類導出。

**使用方法：**
```bash
python scripts/card_import/scripts/export_cards_by_pool.py
```

### pre_download_master_card_images.py
預下載主卡片圖片的腳本，用於批量下載卡片圖片到本地。

**使用方法：**
```bash
python scripts/card_import/scripts/pre_download_master_card_images.py
```

## 數據檔案說明

### hololive_card.json
包含 Hololive 卡片的完整數據，包括：
- 卡片名稱 (cardName)
- 系列名稱 (seriesName)
- 稀有度 (rarityName, rarityLevel)
- 圖片 URL (mediaUrl)
- 原始 ID (original_id)

### pokemon_cards_jp_updated.json
包含日版寶可夢卡片的數據，格式與 Hololive 相同。

### union_arena_data.json
包含 Union Arena 卡片的數據，格式與 Hololive 相同。

### wixoss_simplified.json
包含 WIXOSS 卡片的簡化數據，專門為 WIXOSS 卡池設計。

## 注意事項

1. 執行腳本前請確保 `.env` 檔案已正確設定資料庫連接資訊
2. 需要的環境變數：
   - `GACHA_DB_NAME` - 資料庫名稱
   - `PG_USER` - PostgreSQL 使用者名稱
   - `PG_PASSWORD` - PostgreSQL 密碼
   - `PG_HOST` - PostgreSQL 主機位址
   - `PG_PORT` - PostgreSQL 連接埠

3. 匯入卡片後可能需要手動執行系統卡池更新調用

## 相關檔案路徑更新

由於檔案已移動到新的資料夾結構，如果有其他腳本或設定檔案引用這些檔案，請更新路徑：

**舊路徑 → 新路徑：**
- `scripts/import_cards.py` → `scripts/card_import/scripts/import_cards.py`
- `scripts/import_wixoss_cards.py` → `scripts/card_import/scripts/import_wixoss_cards.py`
- `scripts/export_cards_by_pool.py` → `scripts/card_import/scripts/export_cards_by_pool.py`
- `scripts/pre_download_master_card_images.py` → `scripts/card_import/scripts/pre_download_master_card_images.py`
- `scripts/hololive_card.json` → `scripts/card_import/data/hololive_card.json`
- `scripts/pokemon_cards_jp_updated.json` → `scripts/card_import/data/pokemon_cards_jp_updated.json`
- `scripts/union_arena_data.json` → `scripts/card_import/data/union_arena_data.json`
- `scripts/wixoss_simplified.json` → `scripts/card_import/data/wixoss_simplified.json`
