{"PREDICTIVE_DEFENSE_ACTIVE": {"name": "預判防禦激活", "description": "受到物理傷害時觸發防禦增強", "icon_key": "predict_defense", "is_buff": true, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 1, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [{"trigger_event": "ON_DAMAGE_TAKEN", "conditions_to_trigger": [{"formula": "event_data.damage_type == 'PHYSICAL'"}], "effects_to_apply": [{"effect_template": "TEMPORARY_HIGH_DEFENSE_BUFF_TEMPLATE"}]}], "special_flags": ["DISPELLABLE", "NON_STACKABLE_IGNORE"]}, "WEAK_LIFESTEAL_STATUS": {"name": "微弱吸血", "description": "攻擊時回復少量生命值", "icon_key": "lifesteal", "is_buff": true, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 1, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [{"trigger_event": "ON_DAMAGE_DEALT", "conditions_to_trigger": [{"formula": "event_data.damage_amount > 0"}], "effects_to_apply": [{"effect_type": "HEAL", "heal_type": "PERCENTAGE", "value_formula": "event_data.damage_amount * 0.15"}]}], "special_flags": ["DISPELLABLE", "NON_STACKABLE_REFRESH"]}}