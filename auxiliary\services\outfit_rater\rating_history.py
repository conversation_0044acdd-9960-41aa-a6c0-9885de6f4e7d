"""
評分歷史管理模塊 - 負責存儲和檢索用戶評分歷史
"""

import asyncio
import json
import logging
import os
import time
from typing import Any, Dict, List

# 設置日誌
logger = logging.getLogger("RatingHistory")

# 定義用戶歷史記錄的基礎目錄
USER_HISTORY_DIR = os.path.join(os.path.dirname(__file__), "user_history")


class RatingHistory:
    """處理用戶評分歷史的類"""

    def __init__(self):  # 移除 history_file_path 參數
        """
        初始化評分歷史管理器
        """
        # 不再預加載所有歷史記錄
        # self.history_file = history_file_path or os.path.join(
        #     os.path.dirname(__file__), "data", "history.json"
        # )
        # self.history_data = self._load_history()
        self._ensure_user_history_dir()  # 更改為確保 user_history 目錄

    def _ensure_user_history_dir(self):  # 重命名並修改
        """確保用戶歷史記錄目錄存在"""
        # data_dir = os.path.dirname(self.history_file)
        if not os.path.exists(USER_HISTORY_DIR):
            os.makedirs(USER_HISTORY_DIR, exist_ok=True)
            logger.info("創建用戶歷史記錄目錄: %s", USER_HISTORY_DIR)

    def _get_user_history_file_path(self, user_id: str) -> str:
        """獲取特定用戶的歷史文件路徑"""
        return os.path.join(USER_HISTORY_DIR, f"{user_id}.json")

    def _sync_load_history(self, user_id: str) -> List[Dict[str, Any]]:
        """【新增】這是一個純同步的讀取函數"""
        user_file = self._get_user_history_file_path(user_id)
        try:
            if os.path.exists(user_file):
                with open(user_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
                    return []  # 格式錯誤返回空
            return []
        except (json.JSONDecodeError, Exception) as e:
            logger.error("讀取歷史文件 %s 時出錯: %s", user_file, e)
            return []

    async def _load_history_async(self, user_id: str) -> List[Dict[str, Any]]:
        """【新增】異步版本的讀取函數"""
        return await asyncio.to_thread(self._sync_load_history, user_id)

    def _sync_save_history(self, user_id: str, data: List[Dict[str, Any]]):
        """【新增】純同步的保存函數"""
        user_file = self._get_user_history_file_path(user_id)
        try:
            with open(user_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error("保存歷史文件 %s 時出錯: %s", user_file, e)

    async def _save_history_async(self, user_id: str, data: List[Dict[str, Any]]):
        """【新增】異步版本的保存函數"""
        await asyncio.to_thread(self._sync_save_history, user_id, data)

    async def add_rating(self, user_id: str, rating_result: Dict[str, Any]):
        """
        【修改】添加評分記錄的異步版本
        """
        user_history = await self._load_history_async(user_id)
        rating_with_timestamp = {**rating_result, "timestamp": time.time()}
        user_history.append(rating_with_timestamp)

        # 限制歷史記錄數量
        if len(user_history) > 5:
            user_history = user_history[-5:]

        await self._save_history_async(user_id, user_history)
        logger.info("已為用戶 %s 異步添加評分記錄", user_id)

    async def get_user_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        【修改】獲取用戶歷史的異步版本
        """
        return await self._load_history_async(user_id)

    async def clear_user_history(self, user_id: str) -> bool:
        """
        【修改】清除用戶評分歷史的異步版本
        """
        user_file = self._get_user_history_file_path(user_id)
        if os.path.exists(user_file):
            try:
                await self._save_history_async(user_id, [])  # 保存空列表以清除
                logger.info("已清除用戶 %s 的評分歷史: %s", user_id, user_file)
                return True
            except Exception as e:
                logger.error(
                    "清除用戶 %s 的歷史記錄時出錯 (%s): %s", user_id, user_file, str(e)
                )
                return False
        else:
            logger.info("用戶 %s 的歷史文件不存在，無需清除: %s", user_id, user_file)
            return True

    async def format_history_for_prompt(self, user_id: str) -> str:
        """
        【修改】格式化提示詞的異步版本
        """
        history = await self.get_user_history(user_id)

        if not history:
            return ""

        history_entries = []

        # 按時間排序，最新的排在前面
        sorted_history = sorted(
            [
                entry
                for entry in history
                if isinstance(entry.get("timestamp"), (int, float))
            ],
            key=lambda x: x["timestamp"],
            reverse=True,
        )

        # 只取最近的三條記錄
        recent_history = sorted_history[:3]

        for i, entry in enumerate(recent_history):
            # 格式化每條歷史記錄
            entry_str = (
                f"歷史評分 {i + 1}:\\n"
                f"評分: {entry.get('score', 'N/A')}\\n"
                f"評價: {entry.get('review', '無評價')}\\n"
                f"建議: {entry.get('suggestion', '無建議')}\\n"
                f"結論: {entry.get('conclusion', '無結論')}\\n"
            )
            history_entries.append(entry_str)

        return "\\n\\n".join(history_entries)
