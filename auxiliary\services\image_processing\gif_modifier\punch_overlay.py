"""
打拳GIF疊加模塊
將用戶頭像疊加到打拳GIF上，頭像會隨著拳頭動作產生位移和縮放效果。
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import List, Optional, Tuple

from PIL import Image, ImageSequence

from utils.logger import logger

from .avatar_gif_overlay import (
    create_global_palette_and_convert,
    fetch_avatar,
    save_gif,
)
from .config import get_gif_config


def get_resampling_filter(filter_name: str):
    """獲取 Pillow 重採樣濾鏡，兼容新舊版本"""
    try:
        # Pillow 10.0.0+ 使用 Image.Resampling
        return getattr(Image.Resampling, filter_name)
    except AttributeError:
        # 舊版本 Pillow 直接從 Image 獲取
        return getattr(Image, filter_name, 1)  # 1 是 LANCZOS 的默認值


PUNCH_DISPLACEMENTS = [-3, 0, 3, 8, 5, 0, -3, -8]
PUNCH_SCALES = [1.0, 1.0, 1.05, 1.1, 1.05, 1.0, 1.05, 1.1]


def determine_punch_direction(frame_index: int) -> int:
    return PUNCH_DISPLACEMENTS[frame_index % 8]


def determine_punch_scale(frame_index: int) -> float:
    return PUNCH_SCALES[frame_index % 8]


def _process_punch_frame(frame_data: Tuple) -> Image.Image:
    frame_rgba, scaled_avatar, position, gif_width, gif_height = frame_data
    new_frame = Image.new("RGBA", (gif_width, gif_height), (0, 0, 0, 0))
    new_frame.paste(scaled_avatar, position, scaled_avatar)
    new_frame.alpha_composite(frame_rgba)
    return new_frame


def _sync_overlay_processing(
    gif_path: str,
    avatar: Image.Image,
    avatar_size: Tuple[int, int],
    offset_y: int,
    fixed_size: Tuple[int, int],
) -> Tuple[List[Image.Image], int, int]:
    """同步處理所有圖像操作以降低異步函數的複雜性"""
    resized_avatar = avatar.resize(avatar_size, get_resampling_filter("LANCZOS"))
    gif_frames = []
    gif_info = {}

    with Image.open(gif_path) as original_gif:
        gif_info = {
            "duration": original_gif.info.get("duration", 100),
            "loop": original_gif.info.get("loop", 0),
            "size": original_gif.size,
        }
        for frame in ImageSequence.Iterator(original_gif):
            gif_frames.append(frame.convert("RGBA").copy())

    gif_width, gif_height = gif_info["size"]
    num_frames = len(gif_frames)

    punch_effects = []
    avatar_variants = {}
    for i in range(num_frames):
        punch_scale = determine_punch_scale(i)
        scaled_size = (
            int(avatar_size[0] * punch_scale),
            int(avatar_size[1] * punch_scale),
        )
        if scaled_size not in avatar_variants:
            avatar_variants[scaled_size] = resized_avatar.resize(
                scaled_size, get_resampling_filter("LANCZOS")
            )

        punch_direction = determine_punch_direction(i)
        position = (
            (gif_width - scaled_size[0]) // 2 + punch_direction,
            (gif_height - scaled_size[1] - offset_y) // 2,
        )
        punch_effects.append((scaled_size, position))

    frame_data_list = [
        (
            frame_rgba,
            avatar_variants[punch_effects[i][0]],
            punch_effects[i][1],
            gif_width,
            gif_height,
        )
        for i, frame_rgba in enumerate(gif_frames)
    ]

    with ThreadPoolExecutor(max_workers=min(4, num_frames)) as executor:
        processed_frames = list(executor.map(_process_punch_frame, frame_data_list))

    if fixed_size != (0, 0):
        processed_frames = [
            frame.resize(fixed_size, get_resampling_filter("LANCZOS"))
            for frame in processed_frames
        ]

    converted_frames = create_global_palette_and_convert(processed_frames)
    duration = gif_info.get("duration", 100)
    loop = gif_info.get("loop", 0)
    # Ensure they are integers
    if not isinstance(duration, int):
        duration = 100
    if not isinstance(loop, int):
        loop = 0
    return converted_frames, duration, loop


async def overlay_punch_gif(
    gif_path: str,
    user,
    output_path: Optional[str] = None,
    fixed_size: Tuple[int, int] = (0, 0),
) -> str:
    """將用戶頭像疊加到打拳GIF上"""
    try:
        gif_config = get_gif_config(gif_path)
        avatar = await fetch_avatar(user)
        if not avatar:
            raise ValueError("無法獲取用戶頭像")

        if not output_path:
            try:
                from auxiliary.services.image_processing.temp_file_manager import (
                    create_temp_filename,
                )

                gif_name = Path(gif_path).stem
                output_path = create_temp_filename(gif_name, user.id)
            except ImportError:
                output_dir = Path("image_processing") / "temp"
                output_dir.mkdir(parents=True, exist_ok=True)
                gif_name = Path(gif_path).stem
                output_path = str(output_dir / f"{gif_name}_{user.id}.gif")

        avatar_size = gif_config.get("avatar_size", (80, 80))
        if not isinstance(avatar_size, (tuple, list)) or len(avatar_size) != 2:
            avatar_size = (80, 80)
        # Ensure avatar_size is a tuple with exactly 2 elements
        avatar_size = (int(avatar_size[0]), int(avatar_size[1]))

        offset_y = gif_config.get("offset_y", 0)
        if not isinstance(offset_y, int):
            offset_y = 0

        converted_frames, duration, loop = await asyncio.to_thread(
            _sync_overlay_processing,
            gif_path,
            avatar,
            avatar_size,
            offset_y,
            fixed_size,
        )

        await asyncio.to_thread(save_gif, converted_frames, output_path, duration, loop)

        logger.info("打拳GIF處理完成，返回路徑: %s", output_path)
        return output_path

    except Exception as e:
        logger.error("打拳GIF處理失敗: %s", e, exc_info=True)
        raise ValueError(f"打拳GIF處理失敗: {e}") from e
