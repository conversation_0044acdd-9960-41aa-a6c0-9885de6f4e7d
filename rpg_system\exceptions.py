"""
RPG 系統的自訂異常。
"""

from gacha.exceptions import BusinessError


class RPGSystemError(BusinessError):
    """RPG 系統中所有業務邏輯異常的基類。"""

    def __init__(self, message: str):
        super().__init__(f"⚔️ RPG 系統錯誤: {message}")


class BattleError(RPGSystemError):
    """戰鬥相關的錯誤。"""

    def __init__(self, message: str):
        super().__init__(f"戰鬥失敗 - {message}")


class SkillError(RPGSystemError):
    """技能相關的錯誤。"""

    def __init__(self, message: str):
        super().__init__(f"技能錯誤 - {message}")


class StatusEffectError(BattleError):
    """狀態效果相關的錯誤。"""

    def __init__(self, message: str):
        super().__init__(f"狀態效果處理失敗 - {message}")


class PlayerStateError(RPGSystemError):
    """玩家狀態相關的錯誤（例如，找不到玩家資料）。"""

    def __init__(self, message: str):
        super().__init__(f"玩家狀態錯誤 - {message}")


class TeamSizeExceededError(RPGSystemError):
    """隊伍人數超過限制異常"""

    def __init__(self, current_size: int, max_size: int):
        self.current_size = current_size
        self.max_size = max_size
        super().__init__(f"隊伍人數 {current_size} 超過限制 {max_size}")


class CardNotOwnedError(RPGSystemError):
    """卡牌不屬於用戶異常"""

    def __init__(self, card_id: int, user_id: int):
        self.user_id = user_id
        super().__init__(f"卡牌 {card_id} 不屬於用戶 {user_id}")


class FloorNotUnlockedError(RPGSystemError):
    """樓層未解鎖異常"""

    def __init__(self, floor_id: int, unlocked_floor: int):
        self.floor_id = floor_id
        self.unlocked_floor = unlocked_floor
        super().__init__(f"樓層 {floor_id} 未解鎖，當前解鎖樓層為 {unlocked_floor}")


class InsufficientWinsError(RPGSystemError):
    """勝利次數不足異常"""

    def __init__(self, current_wins: int, required_wins: int):
        self.current_wins = current_wins
        self.required_wins = required_wins
        super().__init__(
            f"勝利次數不足，當前 {current_wins} 次，需要 {required_wins} 次"
        )


class FloorConfigNotFoundError(RPGSystemError):
    """樓層配置未找到異常"""

    def __init__(self, floor_id: int):
        self.floor_id = floor_id
        super().__init__(f"樓層 {floor_id} 的配置未找到")


class UserProgressNotFoundError(PlayerStateError):
    """當找不到特定使用者的進度時引發。"""

    def __init__(self, user_id: int):
        self.user_id = user_id
        super().__init__(f"找不到玩家 {user_id} 的進度資料。")


class GlobalSkillNotFoundError(SkillError):
    """當在全域技能池中找不到特定技能時引發。"""

    def __init__(self, skill_id: str):
        self.skill_id = skill_id
        super().__init__(f"找不到全域技能 {skill_id}。")
