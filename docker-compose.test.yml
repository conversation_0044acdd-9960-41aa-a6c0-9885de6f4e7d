services:
  # Bot 測試環境配置（連接到主機現有服務）
  bot-test:
    build:
      context: .
      target: dependencies  # 只建置到依賴階段，代碼通過volume掛載
    container_name: dickpk_bot_test
    env_file: []  # 禁止讀取 .env 文件，避免被本機設定覆蓋
    volumes:
      # 測試環境：完整項目掛載（修改任何代碼文件，重啟容器即可更新，無需重新建置）
      - .:/app

      # 重要：確保這些目錄使用主機版本（數據持久化）
      - ./logs:/app/logs
      - ./database/gacha_waifu:/app/database/gacha_waifu
      - ./fonts:/app/fonts
      - ./auxiliary/services/image_processing:/app/auxiliary/services/image_processing
      - ./downloaded_gacha_master_cards:/app/downloaded_gacha_master_cards:ro

      # 配置文件掛載
      - ./config/config.yaml:/app/config/config.yaml
      - ./config/gacha_settings.yaml:/app/config/gacha_settings.yaml
      - ./config/ui_settings.yaml:/app/config/ui_settings.yaml
    environment:
      # 從測試.bat轉移的完整環境變數設定
      - DISCORD_TOKEN=MTMzNzEzMDg4MTY4NDM0NDg0Mw.GiN58S.8dXetrG80Us6_LEDbNesT7yqm7sD07FUjdPPrM
      - GACHA_DB_NAME=TEST
      - LOG_LEVEL=DEBUG
      - DATABASE_URL=postgresql://postgres:<EMAIL>:5432/TEST
      - PG_HOST=host.docker.internal  # Docker標準主機訪問方式，不受VPN影響
      - PG_PORT=5432
      - PG_USER=postgres
      - PG_PASSWORD=26015792
      - PVE_DB_TYPE=postgresql
      - LADDER_DB_TYPE=postgresql
      - LADDER_DATABASE=ladder_database
      - DEV_MODE=true
      - PVE_DATABASE=pve_database
      - ENABLE_PVE_SYSTEM=false
      - ENABLE_LADDER_SYSTEM=true
      - ENABLE_GACHA_SYSTEM=true
      - AI_API_KEY=26015792
      - REDIS_URL=redis://host.docker.internal:6379/1
      - REDIS_HOST=host.docker.internal  # Docker標準主機訪問方式，不受VPN影響
      - REDIS_PORT=6379
      - REDIS_DB=1
      - REDIS_PASSWORD=
      - REDIS_ENABLED=True
      - RPG=FALSE
      - WEBHOOK_NOTIFICATIONS_ENABLED=false
      - SHARD_COUNT=1
      # 字體相關環境變數（確保Docker環境正確讀取字體）
      - FONTCONFIG_PATH=/etc/fonts
    restart: unless-stopped

volumes:
  postgres_test_data:
  redis_test_data: 