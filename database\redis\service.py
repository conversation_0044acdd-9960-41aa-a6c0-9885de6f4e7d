"""
Redis 服務管理模塊
用於自動啟動和管理 Redis 服務
"""

import asyncio
import os
import platform
from typing import Optional, Tuple

from utils.logger import logger


class RedisService:
    """Redis 服務管理類，提供自動啟動和管理 Redis 服務的功能"""

    def __init__(self):
        """初始化 Redis 服務管理器"""
        self.system = platform.system().lower()
        self.redis_server_process = None
        self.is_running = False

    async def check_redis_running(self) -> bool:
        """檢查 Redis 服務是否正在運行

        Returns:
            是否正在運行
        """
        try:
            # 使用異步subprocess執行 redis-cli ping 命令
            proc = await asyncio.create_subprocess_exec(
                "redis-cli", "ping",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await asyncio.wait_for(proc.communicate(), timeout=2.0)
            return stdout.decode().strip() == "PONG"
        except (asyncio.TimeoutError, FileNotFoundError, Exception):
            return False

    async def check_redis_installed(self) -> bool:
        """檢查 Redis 是否已安裝

        Returns:
            是否已安裝
        """
        try:
            # 使用異步subprocess執行 redis-cli --version 命令
            proc = await asyncio.create_subprocess_exec(
                "redis-cli", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(proc.communicate(), timeout=2.0)
            return proc.returncode == 0
        except (asyncio.TimeoutError, FileNotFoundError, Exception):
            return False

    async def get_redis_server_path(self) -> Optional[str]:
        """獲取 Redis 服務器可執行文件路徑

        Returns:
            Redis 服務器路徑，如果找不到則返回 None
        """
        if self.system == "windows":
            # Windows 上 Redis 服務器可能的路徑
            possible_paths = [
                "C:\\Program Files\\Redis\\redis-server.exe",
                "C:\\Program Files (x86)\\Redis\\redis-server.exe",
                os.path.join(
                    os.environ.get("ProgramFiles", "C:\\Program Files"),
                    "Redis",
                    "redis-server.exe",
                ),
                os.path.join(
                    os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)"),
                    "Redis",
                    "redis-server.exe",
                ),
            ]

            # 檢查 PATH 環境變量
            if "PATH" in os.environ:
                for path_dir in os.environ["PATH"].split(os.pathsep):
                    possible_paths.append(os.path.join(path_dir, "redis-server.exe"))

            # 檢查每個可能的路徑
            for path in possible_paths:
                if os.path.exists(path):
                    return path

            # 如果找不到，嘗試使用 where 命令
            try:
                proc = await asyncio.create_subprocess_exec(
                    "where", "redis-server",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, _ = await asyncio.wait_for(proc.communicate(), timeout=2.0)
                if proc.returncode == 0 and stdout:
                    return stdout.decode().strip().split("\n")[0]
            except (asyncio.TimeoutError, FileNotFoundError, Exception):
                pass
        else:
            # Linux/macOS 上使用 which 命令
            try:
                proc = await asyncio.create_subprocess_exec(
                    "which", "redis-server",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, _ = await asyncio.wait_for(proc.communicate(), timeout=2.0)
                if proc.returncode == 0 and stdout:
                    return stdout.decode().strip()
            except (asyncio.TimeoutError, FileNotFoundError, Exception):
                pass

        return None

    async def start_redis_service(self) -> Tuple[bool, str]:
        """啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 檢查 Redis 是否已經在運行
        if await self.check_redis_running():
            logger.info("Redis 服務已經在運行")
            self.is_running = True
            return True, "Redis 服務已經在運行"

        # 檢查 Redis 是否已安裝
        if not await self.check_redis_installed():
            logger.warning("Redis 未安裝，無法啟動服務")
            return False, "Redis 未安裝，請先安裝 Redis"

        # 根據操作系統啟動 Redis 服務
        if self.system == "windows":
            return await self._start_redis_windows()
        elif self.system == "linux":
            return await self._start_redis_linux()
        elif self.system == "darwin":  # macOS
            return await self._start_redis_mac()
        else:
            logger.error("不支持的操作系統: %s", self.system)
            return False, f"不支持的操作系統: {self.system}"

    async def _start_redis_windows(self) -> Tuple[bool, str]:
        """在 Windows 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 嘗試啟動 Windows 服務
        try:
            proc = await asyncio.create_subprocess_exec(
                "net", "start", "Redis",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(proc.communicate(), timeout=5.0)

            # 檢查服務是否成功啟動
            await asyncio.sleep(1)
            if await self.check_redis_running():
                logger.info("Redis Windows 服務已啟動")
                self.is_running = True
                return True, "Redis Windows 服務已啟動"
        except (asyncio.TimeoutError, Exception) as e:
            logger.warning("啟動 Redis Windows 服務失敗: %s", str(e))

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = await self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用異步subprocess啟動 Redis 服務器
            import subprocess
            self.redis_server_process = await asyncio.create_subprocess_exec(
                redis_server_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if self.system == "windows" else 0
            )

            # 等待服務啟動
            await asyncio.sleep(2)

            # 檢查服務是否成功啟動
            if await self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    await self.redis_server_process.wait()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error("啟動 Redis 服務器時發生錯誤: %s", str(e))
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    async def _start_redis_linux(self) -> Tuple[bool, str]:
        """在 Linux 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        try:
            # 嘗試使用 systemctl 啟動 Redis 服務
            proc = await asyncio.create_subprocess_exec(
                "sudo", "systemctl", "start", "redis",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(proc.communicate(), timeout=5.0)

            # 檢查服務是否成功啟動
            await asyncio.sleep(1)
            if await self.check_redis_running():
                logger.info("Redis 服務已通過 systemctl 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 systemctl 啟動"
        except (asyncio.TimeoutError, Exception):
            logger.warning("通過 systemctl 啟動 Redis 服務失敗，嘗試使用 service")

        try:
            # 嘗試使用 service 啟動 Redis 服務
            proc = await asyncio.create_subprocess_exec(
                "sudo", "service", "redis-server", "start",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(proc.communicate(), timeout=5.0)

            # 檢查服務是否成功啟動
            await asyncio.sleep(1)
            if await self.check_redis_running():
                logger.info("Redis 服務已通過 service 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 service 啟動"
        except (asyncio.TimeoutError, Exception):
            logger.warning(
                "通過 service 啟動 Redis 服務失敗，嘗試直接啟動 redis-server"
            )

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = await self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用異步subprocess啟動 Redis 服務器
            self.redis_server_process = await asyncio.create_subprocess_exec(
                redis_server_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 等待服務啟動
            await asyncio.sleep(2)

            # 檢查服務是否成功啟動
            if await self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    await self.redis_server_process.wait()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error("啟動 Redis 服務器時發生錯誤: %s", str(e))
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    async def _start_redis_mac(self) -> Tuple[bool, str]:
        """在 macOS 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        try:
            # 嘗試使用 brew services 啟動 Redis 服務
            proc = await asyncio.create_subprocess_exec(
                "brew", "services", "start", "redis",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(proc.communicate(), timeout=5.0)

            # 檢查服務是否成功啟動
            await asyncio.sleep(1)
            if await self.check_redis_running():
                logger.info("Redis 服務已通過 brew services 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 brew services 啟動"
        except (asyncio.TimeoutError, Exception):
            logger.warning(
                "通過 brew services 啟動 Redis 服務失敗，嘗試直接啟動 redis-server"
            )

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = await self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用異步subprocess啟動 Redis 服務器
            self.redis_server_process = await asyncio.create_subprocess_exec(
                redis_server_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 等待服務啟動
            await asyncio.sleep(2)

            # 檢查服務是否成功啟動
            if await self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    await self.redis_server_process.wait()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error("啟動 Redis 服務器時發生錯誤: %s", str(e))
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    async def stop_redis_service(self) -> Tuple[bool, str]:
        """停止 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 如果是由本程序啟動的 Redis 服務器，直接終止進程
        if self.redis_server_process:
            try:
                self.redis_server_process.terminate()
                await self.redis_server_process.wait()
                self.redis_server_process = None
                self.is_running = False
                logger.info("Redis 服務器已停止")
                return True, "Redis 服務器已停止"
            except Exception as e:
                logger.error("停止 Redis 服務器時發生錯誤: %s", str(e))
                return False, f"停止 Redis 服務器時發生錯誤: {str(e)}"

        # 根據操作系統停止 Redis 服務
        if self.system == "windows":
            try:
                proc = await asyncio.create_subprocess_exec(
                    "net", "stop", "Redis",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.wait_for(proc.communicate(), timeout=5.0)
                self.is_running = False
                logger.info("Redis Windows 服務已停止")
                return True, "Redis Windows 服務已停止"
            except (asyncio.TimeoutError, Exception) as e:
                logger.error("停止 Redis Windows 服務失敗: %s", str(e))
                return False, f"停止 Redis Windows 服務失敗: {str(e)}"
        elif self.system == "linux":
            try:
                proc = await asyncio.create_subprocess_exec(
                    "sudo", "systemctl", "stop", "redis",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.wait_for(proc.communicate(), timeout=5.0)
                self.is_running = False
                logger.info("Redis 服務已通過 systemctl 停止")
                return True, "Redis 服務已通過 systemctl 停止"
            except (asyncio.TimeoutError, Exception):
                try:
                    proc = await asyncio.create_subprocess_exec(
                        "sudo", "service", "redis-server", "stop",
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await asyncio.wait_for(proc.communicate(), timeout=5.0)
                    self.is_running = False
                    logger.info("Redis 服務已通過 service 停止")
                    return True, "Redis 服務已通過 service 停止"
                except (asyncio.TimeoutError, Exception) as e:
                    logger.error("停止 Redis 服務失敗: %s", str(e))
                    return False, f"停止 Redis 服務失敗: {str(e)}"
        elif self.system == "darwin":  # macOS
            try:
                proc = await asyncio.create_subprocess_exec(
                    "brew", "services", "stop", "redis",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.wait_for(proc.communicate(), timeout=5.0)
                self.is_running = False
                logger.info("Redis 服務已通過 brew services 停止")
                return True, "Redis 服務已通過 brew services 停止"
            except (asyncio.TimeoutError, Exception) as e:
                logger.error("停止 Redis 服務失敗: %s", str(e))
                return False, f"停止 Redis 服務失敗: {str(e)}"
        else:
            logger.error("不支持的操作系統: %s", self.system)
            return False, f"不支持的操作系統: {self.system}"

    def __del__(self):
        """析構函數，確保在對象被銷毀時停止 Redis 服務"""
        if self.redis_server_process:
            try:
                self.redis_server_process.terminate()
            except Exception:
                pass


# 創建全局 Redis 服務管理器實例
redis_service = RedisService()