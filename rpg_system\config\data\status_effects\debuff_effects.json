{"SLOW_STATUS": {"name": "緩速", "description": "速度降低10%", "icon_key": "slow", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.10"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ARMOR_BROKEN_STATUS": {"name": "破甲", "description": "物理防禦力降低15%", "icon_key": "armor_broken", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.15"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "PHYSICAL_VULNERABILITY": {"name": "物理易傷", "description": "受到物理傷害時傷害增加", "icon_key": "physical_vuln", "is_buff": false, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 2, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "NON_STACKABLE_REFRESH"]}, "COOLDOWN_INCREASED_DEBUFF": {"name": "技能冷却延長", "description": "技能冷却時間增加", "icon_key": "cooldown_increase", "is_buff": false, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 1, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "NON_STACKABLE_REFRESH"]}, "PRESSURE_DEBUFF": {"name": "壓力", "description": "速度降低5%，閃避率降低3%", "icon_key": "pressure", "is_buff": false, "max_stacks": 5, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.05"}, {"stat_name": "evasion", "modification_type": "FLAT_ADD", "value_formula": "-0.03"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ATK_DEBUFF": {"name": "攻擊力削弱", "description": "物理攻擊力和魔法攻擊力降低", "icon_key": "atk_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.15"}, {"stat_name": "matk", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.15"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "DEF_DEBUFF": {"name": "防禦力削弱", "description": "物理防禦力和魔法防禦力降低", "icon_key": "def_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.20"}, {"stat_name": "mdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "SPD_DEBUFF": {"name": "速度削弱", "description": "速度降低", "icon_key": "spd_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.25"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ACCURACY_DEBUFF": {"name": "命中率削弱", "description": "命中率降低", "icon_key": "accuracy_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "accuracy", "modification_type": "FLAT_ADD", "value_formula": "-0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "EVASION_DEBUFF": {"name": "閃避率削弱", "description": "閃避率降低", "icon_key": "evasion_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "evasion", "modification_type": "FLAT_ADD", "value_formula": "-0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "HP_DEBUFF": {"name": "生命力削弱", "description": "最大生命值降低", "icon_key": "hp_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "max_hp", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.15"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "MP_DEBUFF": {"name": "法力值削弱", "description": "最大法力值降低", "icon_key": "mp_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "max_mp", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "CRIT_DEBUFF": {"name": "暴擊削弱", "description": "暴擊率和暴擊傷害降低", "icon_key": "crit_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "-0.10"}, {"stat_name": "crit_dmg_multiplier", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ALL_STATS_DEBUFF": {"name": "全能力削弱", "description": "所有基礎屬性降低", "icon_key": "all_stats_debuff", "is_buff": false, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.08"}, {"stat_name": "matk", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.08"}, {"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.08"}, {"stat_name": "mdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.08"}, {"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "-0.08"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}}