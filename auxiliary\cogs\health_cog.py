"""
機器人健康狀態檢查相關Discord命令
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

import discord
import psutil
from discord import app_commands
from discord.ext import commands

from database.postgresql.async_manager import get_pool, get_redis_client
from utils.base_view import BaseView
from utils.logger import logger


class HealthCog(commands.Cog, name="系統健康"):
    """處理機器人健康狀態檢查相關指令"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("HealthCog 已初始化")

    @app_commands.command(name="機器人資訊", description="查看機器人和服務的資訊狀態")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def robot_info(self, interaction: discord.Interaction):
        """
        查看機器人和各項服務的資訊狀態

        參數:
            interaction: Discord交互對象
        """
        await interaction.response.defer(thinking=True)

        # 收集健康狀態信息
        health_data = await self._collect_health_data()

        # 創建健康狀態嵌入消息（默認頁面）
        embed = self._create_health_embed(health_data)

        # 創建帶有切頁功能的視圖
        view = HealthView(self, health_data, interaction.user.id)

        await interaction.followup.send(embed=embed, view=view)

    async def _collect_health_data(self) -> Dict[str, Any]:
        """收集所有健康狀態數據"""
        health_data = {}

        # 機器人基本狀態
        try:
            health_data["bot"] = await self._check_bot_status()
        except Exception as e:
            logger.error("檢查機器人狀態時發生錯誤: %s", e)
            health_data["bot"] = {"online": False, "error": str(e)}

        # 數據庫狀態
        try:
            health_data["database"] = await self._check_database_status()
        except Exception as e:
            logger.error("檢查數據庫狀態時發生錯誤: %s", e)
            health_data["database"] = {
                "postgresql": {"status": "❌", "details": f"檢查失敗: {e}"},
                "redis": {"status": "❌", "details": f"檢查失敗: {e}"},
            }

        # 服務狀態
        try:
            health_data["services"] = await self._check_services_status()
        except Exception as e:
            logger.error("檢查服務狀態時發生錯誤: %s", e)
            health_data["services"] = {
                "architecture": {"status": "❌", "details": f"檢查失敗: {e}"},
                "playwright_manager": {"status": "❌", "details": f"檢查失敗: {e}"},
            }

        # 系統資源
        try:
            health_data["system"] = self._check_system_resources()
        except Exception as e:
            logger.error("檢查系統資源時發生錯誤: %s", e)
            health_data["system"] = {"status": "❌", "error": str(e)}

        # 指令註冊狀態
        try:
            health_data["commands"] = self._check_command_registry_status()
        except Exception as e:
            logger.error("檢查指令狀態時發生錯誤: %s", e)
            health_data["commands"] = {"status": "❌", "error": str(e)}

        # 緩存狀態
        try:
            health_data["cache"] = await self._check_cache_status()
        except Exception as e:
            logger.error("檢查緩存狀態時發生錯誤: %s", e)
            health_data["cache"] = {"redis_enabled": False, "error": str(e)}

        # 連接狀態
        try:
            health_data["connection"] = self._check_connection_status()
        except Exception as e:
            logger.error("檢查連接狀態時發生錯誤: %s", e)
            health_data["connection"] = {"manager_available": False, "error": str(e)}

        # Gacha系統統計
        try:
            health_data["gacha_stats"] = await self._collect_gacha_stats()
        except Exception as e:
            logger.error("收集Gacha統計時發生錯誤: %s", e)
            health_data["gacha_stats"] = {"status": "❌", "error": str(e)}

        # AI指令使用統計
        try:
            health_data["ai_command_stats"] = await self._collect_ai_command_stats()
        except Exception as e:
            logger.error("收集AI指令統計時發生錯誤: %s", e)
            health_data["ai_command_stats"] = {"status": "❌", "error": str(e)}

        # 餘額變動統計
        try:
            health_data["balance_stats"] = await self._collect_balance_stats()
        except Exception as e:
            logger.error("收集餘額統計時發生錯誤: %s", e)
            health_data["balance_stats"] = {"status": "❌", "error": str(e)}

        # 轉帳行為統計
        try:
            health_data["transfer_stats"] = await self._collect_transfer_stats()
        except Exception as e:
            logger.error("收集轉帳統計時發生錯誤: %s", e)
            health_data["transfer_stats"] = {"status": "❌", "error": str(e)}

        # AI 劇情冒險統計
        try:
            health_data["story_stats"] = await self._collect_story_stats()
        except Exception as e:
            logger.error("收集AI劇情冒險統計時發生錯誤: %s", e)
            health_data["story_stats"] = {"status": "❌", "error": str(e)}

        # 股市系統統計
        try:
            health_data["stock_market_stats"] = await self._collect_stock_market_stats()
        except Exception as e:
            logger.error("收集股市系統統計時發生錯誤: %s", e)
            health_data["stock_market_stats"] = {"status": "❌", "error": str(e)}

        return health_data

    async def _get_cached_ai_stats(self) -> Optional[Dict[str, Any]]:
        """從Redis緩存獲取AI指令統計數據"""
        try:
            redis_client = get_redis_client()
            if not redis_client:
                return None

            cached_data = await redis_client.get("health_check:ai_stats")
            if cached_data:
                import json

                logger.debug("使用Redis緩存的AI指令統計數據")
                return json.loads(cached_data)
        except Exception as e:
            logger.warning("從Redis獲取AI指令統計緩存失敗: %s", e)
        return None

    async def _fetch_and_cache_ai_stats(self) -> Dict[str, Any]:
        """從數據庫獲取AI指令統計數據並緩存"""
        stats = {
            "outfit_rating_count": 0,
            "battle_analysis_count": 0,
            "total_command_usage": 0,
        }
        try:
            service = getattr(self.bot, "command_usage_service", None)
            if service:
                # 獲取穿搭評分統計
                outfit_stats_slash = await service.get_command_stats("rate")
                outfit_stats_context = await service.get_command_stats("穿搭評分")
                stats["outfit_rating_count"] = (
                    outfit_stats_slash.total_uses if outfit_stats_slash else 0
                ) + (outfit_stats_context.total_uses if outfit_stats_context else 0)

                # 獲取戰力分析統計
                battle_stats_slash = await service.get_command_stats("rateb")
                battle_stats_context = await service.get_command_stats("戰力分析")
                stats["battle_analysis_count"] = (
                    battle_stats_slash.total_uses if battle_stats_slash else 0
                ) + (battle_stats_context.total_uses if battle_stats_context else 0)

                # 獲取總體統計
                total_stats = await service.get_total_stats()
                stats["total_command_usage"] = total_stats.get("total_commands", 0)
            else:
                logger.warning("數據庫指令統計服務不可用")
        except Exception as e:
            logger.warning("無法讀取AI功能使用統計: %s", e)

        # 保存到Redis緩存
        try:
            redis_client = get_redis_client()
            if redis_client:
                import json

                serialized_data = json.dumps(stats, ensure_ascii=False, default=str)
                await redis_client.setex("health_check:ai_stats", 300, serialized_data)
                logger.debug("AI指令統計數據已保存到Redis緩存（TTL: 5分鐘）")
        except Exception as e:
            logger.warning("保存AI指令統計數據到Redis緩存失敗: %s", e)

        return stats

    async def _collect_ai_command_stats(self) -> Dict[str, Any]:
        """收集AI指令使用統計（支援Redis緩存）"""
        cached_stats = await self._get_cached_ai_stats()
        if cached_stats:
            return cached_stats
        return await self._fetch_and_cache_ai_stats()

    async def _fetch_balance_db_stats(self, conn) -> Dict[str, Any]:
        """從資料庫獲取餘額變動統計數據"""
        query = """
        WITH overall_stats AS (
            SELECT
                SUM(CASE WHEN change_amount > 0 THEN change_amount ELSE 0 END) as total_income,
                SUM(CASE WHEN change_amount < 0 THEN change_amount ELSE 0 END) as total_expense,
                SUM(change_amount) as net_profit,
                COUNT(*) as total_transactions
            FROM balance_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
        ),
        type_stats AS (
            SELECT
                transaction_type,
                SUM(change_amount) as net_profit,
                SUM(CASE WHEN change_amount > 0 THEN change_amount ELSE 0 END) as total_income,
                SUM(CASE WHEN change_amount < 0 THEN change_amount ELSE 0 END) as total_expense,
                COUNT(*) as transaction_count,
                AVG(CASE WHEN change_amount > 0 THEN change_amount END) as avg_income,
                AVG(CASE WHEN change_amount < 0 THEN change_amount END) as avg_expense
            FROM balance_history
            WHERE created_at >= NOW() - INTERVAL '30 days' AND transaction_type IS NOT NULL
            GROUP BY transaction_type
            ORDER BY net_profit DESC
        )
        SELECT
            (SELECT to_jsonb(t) FROM overall_stats t) as overall,
            (SELECT jsonb_agg(to_jsonb(t)) FROM type_stats t) as by_type;
        """
        record = await conn.fetchrow(query)
        if not record:
            return {}

        stats = {
            "overall": record["overall"] or {},
            "by_type": record["by_type"] or [],
        }
        return stats

    async def _collect_balance_stats(self) -> Dict[str, Any]:
        """收集餘額變動統計資料（支援Redis緩存）"""
        cached_stats = await self._get_cached_balance_stats()
        if cached_stats:
            return cached_stats

        stats = {"status": "❌", "error": None}
        try:
            pool = get_pool()
            if not pool:
                stats["error"] = "數據庫連接池不可用"
                return stats

            async with pool.acquire() as conn:
                db_stats = await self._fetch_balance_db_stats(conn)
                stats.update(db_stats)

            stats["status"] = "✅"
            await self._cache_balance_stats(stats)

        except Exception as e:
            logger.error("收集餘額統計失敗: %s", e, exc_info=True)
            stats["error"] = str(e)

        return stats

    async def _get_cached_balance_stats(self) -> Optional[Dict[str, Any]]:
        """從Redis緩存獲取餘額統計數據"""
        try:
            redis_client = get_redis_client()
            if not redis_client:
                return None
            cached_data = await redis_client.get("health_check:balance_stats")
            if cached_data:
                import json

                return json.loads(cached_data)
        except Exception as e:
            logger.warning("從Redis獲取餘額統計緩存失敗: %s", e)
        return None

    async def _cache_balance_stats(self, stats: Dict[str, Any]) -> None:
        """將餘額統計數據保存到Redis緩存（TTL 1小時）"""
        try:
            redis_client = get_redis_client()
            if not redis_client:
                return
            import json

            serialized_data = json.dumps(stats, ensure_ascii=False, default=str)
            await redis_client.setex(
                "health_check:balance_stats", 3600, serialized_data
            )
            logger.debug("餘額統計數據已保存到Redis緩存（TTL: 1小時）")
        except Exception as e:
            logger.warning("保存餘額統計數據到Redis緩存失敗: %s", e)

    async def _fetch_transfer_stats(self, conn) -> Dict[str, Any]:
        """從資料庫獲取轉帳與卡片交易統計數據"""
        query = """
        WITH oil_transfers AS (
            SELECT
                sender_id AS user_id,
                -gross_amount AS amount,
                1 AS transaction_count
            FROM user_transaction_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
            UNION ALL
            SELECT
                receiver_id AS user_id,
                net_amount AS amount,
                1 AS transaction_count
            FROM user_transaction_history
            WHERE created_at >= NOW() - INTERVAL '30 days'
        ),
        card_trades AS (
            -- Seller (receiver of oil)
            SELECT
                initiator_user_id AS user_id,
                price_amount AS amount,
                1 AS transaction_count
            FROM card_trade_history
            WHERE trade_type = 'CARD_FOR_OIL' AND completed_at >= NOW() - INTERVAL '30 days' AND price_amount IS NOT NULL
            UNION ALL
            -- Buyer (sender of oil)
            SELECT
                receiver_user_id AS user_id,
                -price_amount AS amount,
                1 AS transaction_count
            FROM card_trade_history
            WHERE trade_type = 'CARD_FOR_OIL' AND completed_at >= NOW() - INTERVAL '30 days' AND price_amount IS NOT NULL
        ),
        all_transactions AS (
            SELECT * FROM oil_transfers
            UNION ALL
            SELECT * FROM card_trades
        ),
        aggregated_stats AS (
            SELECT
                user_id,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_received,
                SUM(CASE WHEN amount < 0 THEN amount ELSE 0 END) as total_sent,
                SUM(CASE WHEN amount > 0 THEN transaction_count ELSE 0 END) as received_count,
                SUM(CASE WHEN amount < 0 THEN transaction_count ELSE 0 END) as sent_count
            FROM all_transactions
            GROUP BY user_id
        )
        SELECT
            agg.user_id,
            u.nickname,
            agg.total_received,
            agg.total_sent,
            agg.received_count,
            agg.sent_count
        FROM aggregated_stats agg
        JOIN gacha_users u ON u.user_id = agg.user_id;
        """
        records = await conn.fetch(query)
        return {"by_user": [dict(r) for r in records]}

    async def _collect_transfer_stats(self) -> Dict[str, Any]:
        """收集轉帳行為統計資料（支援Redis緩存）"""
        cache_key = "health_check:transfer_stats"
        redis_client = get_redis_client()

        # 嘗試從快取讀取
        if redis_client:
            try:
                cached_data = await redis_client.get(cache_key)
                if cached_data:
                    import json

                    return json.loads(cached_data)
            except Exception as e:
                logger.warning("從Redis獲取轉帳統計緩存失敗: %s", e)

        # 從資料庫讀取
        stats = {"status": "❌", "error": None}
        try:
            pool = get_pool()
            if not pool:
                stats["error"] = "數據庫連接池不可用"
                return stats

            async with pool.acquire() as conn:
                db_stats = await self._fetch_transfer_stats(conn)
                stats.update(db_stats)

            stats["status"] = "✅"
            # 寫入快取
            if redis_client:
                try:
                    import json

                    serialized_data = json.dumps(stats, ensure_ascii=False, default=str)
                    await redis_client.setex(
                        cache_key, 3600, serialized_data
                    )  # TTL 1小時
                except Exception as e:
                    logger.warning("保存轉帳統計數據到Redis緩存失敗: %s", e)

        except Exception as e:
            logger.error("收集轉帳統計失敗: %s", e, exc_info=True)
            stats["error"] = str(e)

        return stats

    async def _fetch_gacha_db_stats(self, conn) -> Dict[str, Any]:
        """使用單一優化查詢從資料庫獲取所有Gacha相關統計數據"""
        # 主要統計數據的單一查詢
        main_query = """
        WITH user_stats_cte AS (
            SELECT
                COUNT(*) as total_users,
                COALESCE(SUM(total_draws), 0) as total_draws,
                COALESCE(SUM(oil_balance), 0) as total_oil_balance
            FROM gacha_users
        ),
        collection_stats_cte AS (
            SELECT
                COUNT(*) as total_collections,
                COALESCE(SUM(quantity), 0) as total_cards_collected
            FROM gacha_user_collections
        ),
        recent_activity_cte AS (
            SELECT
                COUNT(DISTINCT user_id) as active_users_24h,
                COUNT(*) as total_draws_24h,
                COUNT(*) FILTER (WHERE is_new) as new_cards_24h,
                COUNT(*) FILTER (WHERE is_wish) as wish_cards_24h
            FROM gacha_draw_history
            WHERE created_at >= NOW() - INTERVAL '24 hours'
        ),
        favorite_wishlist_stats_cte AS (
            SELECT
                (SELECT COUNT(*) FROM gacha_user_collections WHERE is_favorite = TRUE) as total_favorites,
                (SELECT COUNT(*) FROM gacha_user_wishes) as total_wishlists
        ),
        rarity_distribution_cte AS (
            SELECT jsonb_object_agg(rarity, total) as distribution
            FROM (
                SELECT mc.rarity, COUNT(uc.id) as total
                FROM gacha_user_collections uc
                JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
                GROUP BY mc.rarity
            ) as rarity_counts
        ),
        pool_stats_24h_cte AS (
            SELECT jsonb_object_agg(pool_type, draws) as pool_draws
            FROM (
                SELECT pool_type, COUNT(id) as draws
                FROM gacha_draw_history
                WHERE created_at >= NOW() - INTERVAL '24 hours' AND pool_type IS NOT NULL
                GROUP BY pool_type
            ) as pool_counts
        )
        SELECT
            (SELECT to_jsonb(t) FROM user_stats_cte t) as user_stats,
            (SELECT to_jsonb(t) FROM collection_stats_cte t) as collection_stats,
            (SELECT to_jsonb(t) FROM recent_activity_cte t) as recent_activity,
            (SELECT to_jsonb(t) FROM favorite_wishlist_stats_cte t) as favorite_wishlist_stats,
            (SELECT distribution FROM rarity_distribution_cte) as rarity_distribution,
            (SELECT pool_draws FROM pool_stats_24h_cte) as pool_draw_stats_24h;
        """
        record = await conn.fetchrow(main_query)
        if not record:
            return {}

        # 解析從 JSONB 返回的數據
        stats = {}
        stats.update(record["user_stats"] or {})
        stats.update(record["collection_stats"] or {})
        stats["recent_activity"] = record["recent_activity"] or {}
        stats["favorite_wishlist_stats"] = record["favorite_wishlist_stats"] or {}

        # 解析稀有度分佈
        rarity_dist_json = record["rarity_distribution"]
        if rarity_dist_json:
            stats["rarity_distribution"] = {
                str(k): {"rarity": k, "total": v} for k, v in rarity_dist_json.items()
            }
        else:
            stats["rarity_distribution"] = {}

        # 解析24小時卡池統計
        pool_stats_24h_json = record["pool_draw_stats_24h"]
        if pool_stats_24h_json:
            from config.app_config import get_pool_type_names

            pool_type_names = get_pool_type_names()
            stats["pool_draw_stats_24h"] = {
                pool_type: {
                    "pool_type": pool_type,
                    "name": pool_type_names.get(pool_type, pool_type),
                    "draws": draws,
                }
                for pool_type, draws in pool_stats_24h_json.items()
            }
        else:
            stats["pool_draw_stats_24h"] = {}

        # 遊戲統計作為第二次查詢
        game_stats_records = await conn.fetch(
            """
            SELECT
                game_type,
                COUNT(*) as total_players,
                SUM(total_games) as total_games,
                SUM(total_wins) as total_wins,
                SUM(total_losses) as total_losses,
                SUM(total_bet_amount) as total_bet_amount,
                SUM(total_payout_amount) as total_payout_amount,
                SUM(total_profit_loss) as total_profit_loss
            FROM user_game_stats
            GROUP BY game_type
            ORDER BY game_type
        """
        )
        stats["game_stats"] = {}
        for r in game_stats_records:
            total_games = r["total_games"] or 0
            win_rate = (r["total_wins"] / total_games * 100) if total_games > 0 else 0
            stats["game_stats"][r["game_type"]] = {
                **dict(r),
                "win_rate": round(win_rate, 2),
            }

        return stats

    async def _collect_gacha_stats(self) -> Dict[str, Any]:
        """收集Gacha系統統計資料（支援Redis緩存）"""
        cached_stats = await self._get_cached_gacha_stats()
        if cached_stats:
            return cached_stats

        stats = {"status": "❌", "error": None}
        try:
            pool = get_pool()
            if not pool:
                stats["error"] = "數據庫連接池不可用"
                return stats

            async with pool.acquire() as conn:
                db_stats = await self._fetch_gacha_db_stats(conn)
                stats.update(db_stats)

                # ... (其他統計邏輯可以進一步拆分) ...

            stats["status"] = "✅"
            await self._cache_gacha_stats(stats)

        except Exception as e:
            logger.error("收集Gacha統計失敗: %s", e, exc_info=True)
            stats["error"] = str(e)

        return stats

    async def _get_cached_gacha_stats(self) -> Optional[Dict[str, Any]]:
        """從Redis緩存獲取Gacha統計數據"""
        try:
            # 檢查Redis客戶端是否可用
            redis_client = get_redis_client()
            if not redis_client:
                return None

            cache_key = "health_check:gacha_stats"

            # 從Redis獲取緩存數據
            cached_data = await redis_client.get(cache_key)
            if cached_data:
                import json

                return json.loads(cached_data)

            return None

        except Exception as e:
            logger.warning("從Redis獲取Gacha統計緩存失敗: %s", e)
            return None

    async def _cache_gacha_stats(self, stats: Dict[str, Any]) -> None:
        """將Gacha統計數據保存到Redis緩存（TTL 3小時）"""
        try:
            # 檢查Redis客戶端是否可用
            redis_client = get_redis_client()
            if not redis_client:
                logger.debug("Redis客戶端不可用，跳過緩存保存")
                return

            cache_key = "health_check:gacha_stats"

            # 序列化數據並保存到Redis（TTL 3小時 = 10800秒）
            import json

            serialized_data = json.dumps(stats, ensure_ascii=False, default=str)
            await redis_client.setex(cache_key, 10800, serialized_data)

            logger.debug("Gacha統計數據已保存到Redis緩存（TTL: 3小時）")

        except Exception as e:
            logger.warning("保存Gacha統計數據到Redis緩存失敗: %s", e)

    async def _check_bot_status(self) -> Dict[str, Any]:
        """檢查機器人基本狀態"""
        bot_status = {
            "online": True,
            "latency": round(self.bot.latency * 1000, 2),  # 轉換為毫秒
            "shard_count": getattr(self.bot, "shard_count", 1),
            "guild_count": len(self.bot.guilds),
            "user_count": len(self.bot.users),
            "uptime": self._get_uptime(),
            "bot_id": self.bot.user.id if self.bot.user else None,
            "bot_name": self.bot.user.name if self.bot.user else "未知",
            "discord_py_version": discord.__version__,
        }

        # 計算總成員數和總頻道數（統計用）
        total_members = 0
        total_channels = 0

        for guild in self.bot.guilds:
            try:
                # 只計算統計數據，不保存詳細資訊
                total_members += guild.member_count or 0
                total_channels += len(guild.channels)
            except Exception:
                # 如果獲取某個伺服器資訊失敗，跳過但不中斷
                continue

        bot_status["total_members"] = total_members
        bot_status["total_channels"] = total_channels

        # 檢查分片狀態（如果是分片機器人）
        shards = getattr(self.bot, "shards", None)
        if shards:
            shard_status = {}
            for shard_id, shard in shards.items():
                shard_status[shard_id] = {
                    "latency": (
                        round(shard.latency * 1000, 2) if shard.latency else None
                    ),
                    "is_closed": shard.is_closed(),
                }
            bot_status["shards"] = shard_status

        return bot_status

    async def _check_database_status(self) -> Dict[str, Any]:
        """檢查數據庫連接狀態"""
        db_status = {
            "postgresql": {"status": "❌", "details": "未初始化"},
            "redis": {"status": "❌", "details": "未初始化"},
        }

        # 檢查 PostgreSQL
        pool = get_pool()
        redis_client = get_redis_client()
        if pool:
            try:
                async with pool.acquire() as conn:
                    await conn.execute("SELECT 1")
                db_status["postgresql"] = {
                    "status": "✅",
                    "details": f"連接池大小: {pool.get_size()}",
                }
            except Exception as e:
                db_status["postgresql"] = {
                    "status": "❌",
                    "details": f"連接失敗: {str(e)}",
                }

        # 檢查 Redis
        if redis_client:
            try:
                # 測試 Redis 連接
                await redis_client.ping()
                db_status["redis"] = {"status": "✅", "details": "連接正常"}
            except Exception as e:
                db_status["redis"] = {"status": "❌", "details": f"連接失敗: {str(e)}"}
        else:
            db_status["redis"] = {"status": "❌", "details": "Redis 客戶端未初始化"}

        return db_status

    async def _check_services_status(self) -> Dict[str, Any]:
        """檢查各項服務狀態"""
        # 初始化默認狀態，確保所有必需的鍵都存在
        services_status = {
            "architecture": {"status": "❌", "details": "檢查失敗"},
            "playwright_manager": {"status": "❌", "details": "未初始化"},
        }

        # 檢查架構狀態 - 新的模組化架構
        try:
            # 檢查核心數據庫連接是否可用（模組化架構的核心）
            pool = get_pool()
            if pool:
                services_status["architecture"] = {"status": "✅", "details": "正常"}
            else:
                services_status["architecture"] = {
                    "status": "⚠️",
                    "details": "數據庫連接未初始化",
                }
        except Exception as e:
            services_status["architecture"] = {
                "status": "❌",
                "details": f"檢查失敗: {e}",
            }

        # 檢查 PlaywrightManager
        try:
            from utils import playwright_manager

            if playwright_manager and playwright_manager.is_initialized():
                services_status["playwright_manager"] = {
                    "status": "✅",
                    "details": "已初始化",
                }
            else:
                services_status["playwright_manager"] = {
                    "status": "⚠️",
                    "details": "未初始化或已關閉",
                }
        except Exception as e:
            services_status["playwright_manager"] = {
                "status": "❌",
                "details": f"檢查失敗: {e}",
            }

        return services_status

    def _check_system_resources(self) -> Dict[str, Any]:
        """檢查系統資源使用情況"""
        try:
            # 獲取當前進程的資源使用情況
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "memory_usage_mb": round(memory_info.rss / 1024 / 1024, 2),
                "cpu_percent": round(process.cpu_percent(), 2),
                "threads": process.num_threads(),
                "status": "✅",
            }
        except Exception as e:
            return {"status": "❌", "error": str(e)}

    def _check_command_registry_status(self) -> Dict[str, Any]:
        """檢查指令註冊狀態"""
        command_status = {
            "loaded_cogs": list(self.bot.cogs.keys()),
            "cog_count": len(self.bot.cogs),
            "slash_commands": len(self.bot.tree.get_commands()),
            "status": "✅",
        }

        # 檢查 CommandRegistry 狀態（如果可用）
        try:
            from command_registry import get_command_registry

            registry = get_command_registry(self.bot)
            if hasattr(registry, "registration_status"):
                reg_status = getattr(registry, "registration_status", {})
                if isinstance(reg_status, dict):
                    command_status["registration_status"] = str(reg_status)
        except Exception:
            # 如果無法獲取 CommandRegistry 狀態，不影響整體檢查
            pass

        return command_status

    async def _check_cache_status(self) -> Dict[str, Any]:
        """檢查緩存狀態"""
        cache_status = {
            "redis_enabled": False,
            "profile_cache": {"status": "❌", "details": "未啟用"},
            "encyclopedia_cache": {"status": "❌", "details": "未啟用"},
        }

        # 檢查 Redis 是否可用
        redis_client = get_redis_client()
        if redis_client:
            cache_status["redis_enabled"] = True

            # 檢查排行榜緩存
            try:
                leaderboard_cog = self.bot.get_cog("LeaderboardCog")
                get_cache_stats = getattr(leaderboard_cog, "get_cache_stats", None)
                if get_cache_stats:
                    lb_stats = await get_cache_stats()
                    if lb_stats.get("cache_enabled"):
                        cache_status["leaderboard_cache"] = {
                            "status": "✅",
                            "details": (
                                f"已緩存 {lb_stats.get('total_cached_keys', 0)} 個鍵"
                            ),
                            "stats": lb_stats,
                        }
                    else:
                        cache_status["leaderboard_cache"] = {
                            "status": "❌",
                            "details": "緩存未啟用",
                        }
                else:
                    cache_status["leaderboard_cache"] = {
                        "status": "⚠️",
                        "details": "LeaderboardCog 未載入或無統計功能",
                    }
            except Exception as e:
                cache_status["leaderboard_cache"] = {
                    "status": "❌",
                    "details": f"檢查失敗: {str(e)}",
                }

            # 檢查其他緩存服務（如果有的話）
            # 這裡可以添加更多緩存服務的檢查

        return cache_status

    def _check_connection_status(self) -> Dict[str, Any]:
        """檢查連接狀態"""
        connection_status = {"manager_available": False, "health": "未知", "stats": {}}

        # 檢查連接管理器是否可用
        connection_manager = getattr(self.bot, "connection_manager", None)
        if connection_manager:
            connection_status["manager_available"] = True
            connection_status["health"] = connection_manager.get_connection_health()
            connection_status["stats"] = connection_manager.get_connection_stats()
            connection_status["is_stable"] = connection_manager.is_connection_stable()

        return connection_status

    def _get_uptime(self) -> str:
        """獲取機器人運行時間"""
        start_time = getattr(self.bot, "start_time", None)
        if start_time:
            uptime_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
        else:
            # 如果沒有記錄啟動時間，返回未知
            return "未知"

        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)

        if days > 0:
            return f"{days}天 {hours}小時 {minutes}分鐘"
        elif hours > 0:
            return f"{hours}小時 {minutes}分鐘"
        else:
            return f"{minutes}分鐘"

    def _create_health_embed(self, health_data: Dict[str, Any]) -> discord.Embed:
        """創建健康狀態嵌入消息"""
        # 判斷整體健康狀態
        overall_healthy = self._is_overall_healthy(health_data)

        embed = discord.Embed(
            title="🤖 機器人資訊狀態",
            color=discord.Color.green() if overall_healthy else discord.Color.orange(),
            timestamp=datetime.now(timezone.utc),
        )

        # 機器人狀態
        bot_data = health_data.get("bot", {})
        embed.add_field(
            name="🤖 機器人狀態",
            value=f"**名稱**: {bot_data.get('bot_name', 'N/A')}\n"
            f"**ID**: {bot_data.get('bot_id', 'N/A')}\n"
            f"**延遲**: {bot_data.get('latency', 'N/A')}ms\n"
            f"**Discord.py**: {bot_data.get('discord_py_version', 'N/A')}\n"
            f"**運行時間**: {bot_data.get('uptime', 'N/A')}",
            inline=True,
        )

        # 伺服器統計
        embed.add_field(
            name="🏰 伺服器統計",
            value=f"**伺服器數**: {bot_data.get('guild_count', 'N/A')}\n"
            f"**總成員數**: {bot_data.get('total_members', 0):,}\n"
            f"**總頻道數**: {bot_data.get('total_channels', 'N/A')}\n"
            f"**分片數**: {bot_data.get('shard_count', 'N/A')}",
            inline=True,
        )

        # 數據庫狀態
        db_data = health_data.get("database", {})
        pg_data = db_data.get("postgresql", {"status": "❌", "details": "數據缺失"})
        redis_data = db_data.get("redis", {"status": "❌", "details": "數據缺失"})

        embed.add_field(
            name="🗄️ 數據庫狀態",
            value=f"**PostgreSQL**: {pg_data['status']}\n"
            f"{pg_data['details']}\n"
            f"**Redis**: {redis_data['status']}\n"
            f"{redis_data['details']}",
            inline=True,
        )

        # 系統資源
        sys_data = health_data.get("system", {"status": "❌", "error": "數據缺失"})
        if sys_data.get("status") == "✅":
            embed.add_field(
                name="💻 系統資源",
                value=f"**記憶體**: {sys_data.get('memory_usage_mb', 'N/A')}MB\n"
                f"**CPU**: {sys_data.get('cpu_percent', 'N/A')}%\n"
                f"**執行緒**: {sys_data.get('threads', 'N/A')}",
                inline=True,
            )
        else:
            embed.add_field(
                name="💻 系統資源",
                value=f"❌ 檢查失敗: {sys_data.get('error', '未知錯誤')}",
                inline=True,
            )

        # 緩存狀態
        cache_data = health_data.get("cache", {})
        redis_status = (
            "✅ 已啟用" if cache_data.get("redis_enabled", False) else "❌ 未啟用"
        )
        cache_value = f"**Redis**: {redis_status}\n"
        if "leaderboard_cache" in cache_data:
            lb_cache_data = cache_data["leaderboard_cache"]
            cache_value += (
                f"**排行榜緩存**: {lb_cache_data.get('status', 'N/A')} "
                f"{lb_cache_data.get('details', 'N/A')}"
            )

        embed.add_field(name="💾 緩存狀態", value=cache_value, inline=True)

        # 連接狀態
        connection_data = health_data["connection"]
        if connection_data["manager_available"]:
            stats = connection_data["stats"]
            connection_value = f"**健康度**: {connection_data['health']}\n"
            connection_value += (
                f"**穩定性**: "
                f"{'✅ 穩定' if connection_data['is_stable'] else '⚠️ 不穩定'}\n"
            )
            connection_value += f"**斷線次數**: {stats.get('total_disconnects', 0)}\n"
            connection_value += f"**恢復次數**: {stats.get('total_resumes', 0)}\n"
            connection_value += f"**互動超時**: {stats.get('interaction_timeouts', 0)}"

            if stats.get("uptime_seconds", 0) > 0:
                uptime_hours = stats["uptime_seconds"] / 3600
                connection_value += f"\n**連接時間**: {uptime_hours:.1f} 小時"
        else:
            connection_value = "❌ 連接管理器未初始化"

        embed.add_field(name="🌐 連接狀態", value=connection_value, inline=True)

        embed.set_footer(text="機器人資訊查詢完成")

        return embed

    def _create_materials_embed(self) -> discord.Embed:
        """創建使用素材資訊嵌入消息"""
        embed = discord.Embed(
            title="📋 機器人使用素材資訊",
            description="",
            color=discord.Color.blue(),
            timestamp=datetime.now(timezone.utc),
        )

        # 機器人頭貼
        embed.add_field(
            name="🤖 機器人頭貼",
            value="[circlecan.blogspot.com](https://circlecan.blogspot.com/)",
            inline=False,
        )

        # 卡池素材來源
        embed.add_field(
            name="🎴 卡池素材來源",
            value="**主、情人節卡池**: [snoob.gg](https://snoob.gg)\n"
            "**典藏、女僕卡池**: mazoku\n"
            "**HoloTCG**: [hololive-official-cardgame.com](https://hololive-official-cardgame.com/)\n"
            "**Union Arena**: [unionarena-tcg.com](https://www.unionarena-tcg.com/jp/)\n"
            "**Pokémon TCG**: [pokemon-card.com](https://www.pokemon-card.com/)\n"
            "**WIXOSS**: [takaratomy.co.jp](https://www.takaratomy.co.jp/products/en.wixoss/)\n"
            "**音擊**: [ongeki.sega.jp](https://ongeki.sega.jp/)",
            inline=False,
        )

        embed.set_footer(text="")

        return embed

    def _create_gacha_stats_embed(self, health_data: Dict[str, Any]) -> discord.Embed:
        """創建Gacha系統統計嵌入消息"""
        gacha_data = health_data.get("gacha_stats", {})

        # 判斷Gacha系統狀態
        gacha_healthy = gacha_data.get("status") == "✅"

        embed = discord.Embed(
            title="📊 Gacha系統統計資訊",
            color=discord.Color.green() if gacha_healthy else discord.Color.red(),
            timestamp=datetime.now(timezone.utc),
        )

        if not gacha_healthy:
            embed.add_field(
                name="❌ 系統狀態",
                value=f"錯誤: {gacha_data.get('error', '未知錯誤')}",
                inline=False,
            )
            return embed

        # 用戶和抽卡統計
        embed.add_field(
            name="👥 用戶統計",
            value=f"**總用戶數**: `{gacha_data.get('total_users', 0):,}`\n"
            f"**總抽卡次數**: `{gacha_data.get('total_draws', 0):,}`\n"
            f"**總油幣餘額**: `{gacha_data.get('total_oil_balance', 0):,}`",
            inline=True,
        )

        # 收藏統計
        embed.add_field(
            name="🃏 收藏統計",
            value=f"**總收藏記錄**: `{gacha_data.get('total_collections', 0):,}`\n"
            f"**總卡片數量**: `{gacha_data.get('total_cards_collected', 0):,}`",
            inline=True,
        )

        # AI功能使用統計
        ai_stats_data = health_data.get("ai_command_stats", {})
        embed.add_field(
            name="🤖 AI功能統計",
            value=(
                f"**穿搭評分次數**: `{ai_stats_data.get('outfit_rating_count', 0):,}`\n"
                f"**戰力分析次數**: `{ai_stats_data.get('battle_analysis_count', 0):,}`"
            ),
            inline=True,
        )

        # 過去24小時活動統計
        recent_activity = gacha_data.get("recent_activity", {})
        activity_value = (
            f"**活躍用戶**: `{recent_activity.get('active_users_24h', 0):,}`\n"
            f"**總抽卡**: `{recent_activity.get('total_draws_24h', 0):,}` 次\n"
            f"**抽中新卡**: `{recent_activity.get('new_cards_24h', 0):,}` 次\n"
            f"**命中許願**: `{recent_activity.get('wish_cards_24h', 0):,}` 次"
        )
        embed.add_field(name="📈 過去24小時活動", value=activity_value, inline=True)

        # 收藏和許願統計
        favorite_wishlist_stats = gacha_data.get("favorite_wishlist_stats", {})
        wish_value = (
            f"**收藏總數**: `{favorite_wishlist_stats.get('total_favorites', 0):,}`\n"
            f"**許願總數**: `{favorite_wishlist_stats.get('total_wishlists', 0):,}`"
        )
        embed.add_field(name="💖 收藏許願統計", value=wish_value, inline=True)

        # 遊戲統計總覽
        game_stats = gacha_data.get("game_stats", {})
        if game_stats:
            total_games_all = sum(
                stats.get("total_games", 0) for stats in game_stats.values()
            )
            total_profit_all = sum(
                float(stats.get("total_profit_loss", 0))
                for stats in game_stats.values()
            )

            game_overview_value = (
                f"**總局數**: `{total_games_all:,}`\n"
                f"**總盈虧**: `{int(total_profit_all):+,}` 油幣\n"
                f"**平均每局**: "
                f"`{total_profit_all / max(total_games_all, 1):+.2f}` 油幣"
            )
            embed.add_field(
                name="🎮 遊戲統計總覽", value=game_overview_value, inline=True
            )

            # 詳細遊戲統計（分兩欄顯示）
            game_summary = []
            for game_type, stats in game_stats.items():
                game_name = {
                    "blackjack": "21點",
                    "dice": "骰子大小",
                    "mines": "踩地雷",
                    "tower": "爬塔",
                    "slot_machine": "老虎機",
                    "spin_wheel": "轉盤遊戲",
                    "baccarat": "百家樂",
                }.get(game_type, game_type.title())

                total_games = stats.get("total_games", 0)
                win_rate = stats.get("win_rate", 0)
                total_profit = float(stats.get("total_profit_loss", 0))

                game_summary.append(
                    f"**{game_name}**: `{total_games:,}`局 | "
                    f"勝率`{win_rate:.1f}`% | 總盈虧`{int(total_profit):+,}`"
                )
            if game_summary:
                embed.add_field(
                    name="🎯 遊戲詳細統計", value="\n".join(game_summary), inline=False
                )

        # 卡池24小時統計 和 稀有度分布 放在同一排
        pool_stats_24h = gacha_data.get("pool_draw_stats_24h", {})
        pool_summary_str = ""
        if pool_stats_24h:
            pool_summary_list = []
            for pool_type, pool_data in pool_stats_24h.items():
                pool_name = pool_data.get("name", pool_type)
                draws = pool_data.get("draws", 0)
                if draws > 0:  # 只顯示有抽卡紀錄的卡池
                    pool_summary_list.append(f"**{pool_name}**: `{draws:,}` 次")
            pool_summary_str = (
                "\n".join(pool_summary_list) if pool_summary_list else "暫無數據"
            )

        embed.add_field(name="🎰 24小時卡池統計", value=pool_summary_str, inline=True)

        # 稀有度分布統計
        rarity_distribution = gacha_data.get("rarity_distribution", {})
        rarity_summary_str = ""
        if rarity_distribution:
            rarity_summary_list = []
            rarity_names = {
                7: "EX",
                6: "LR",
                5: "UR",
                4: "SSR",
                3: "SR",
                2: "R",
                1: "C",
            }

            for rarity_val, rarity_data in sorted(
                rarity_distribution.items(), key=lambda item: int(item[0]), reverse=True
            ):
                rarity_name = rarity_names.get(int(rarity_val), f"R{rarity_val}")
                total_cards = rarity_data["total"]
                rarity_summary_list.append(f"**{rarity_name}**: `{total_cards:,}`張")

            rarity_summary_str = (
                "\n".join(rarity_summary_list) if rarity_summary_list else "暫無數據"
            )

        embed.add_field(name="💎 卡片稀有度分布", value=rarity_summary_str, inline=True)

        # 添加空白字段以確保三欄對齊
        embed.add_field(name="\u200b", value="\u200b", inline=True)

        embed.set_footer(text="統計資料更新時間")

        return embed

    def _create_balance_stats_embed(self, health_data: Dict[str, Any]) -> discord.Embed:
        """創建餘額變動統計嵌入消息"""
        balance_data = health_data.get("balance_stats", {})
        is_healthy = balance_data.get("status") == "✅"

        embed = discord.Embed(
            title="💰 近30天餘額變動統計",
            color=discord.Color.gold() if is_healthy else discord.Color.red(),
            timestamp=datetime.now(timezone.utc),
        )

        if not is_healthy:
            embed.add_field(
                name="❌ 系統狀態",
                value=f"錯誤: {balance_data.get('error', '未知錯誤')}",
                inline=False,
            )
            return embed

        overall = balance_data.get("overall", {})
        total_income = float(overall.get("total_income", 0) or 0)
        total_expense = float(overall.get("total_expense", 0) or 0)
        net_profit = float(overall.get("net_profit", 0) or 0)

        embed.description = (
            f"**總收入**: `{total_income:,.0f}` 油幣\n"
            f"**總支出**: `{abs(total_expense):,.0f}` 油幣\n"
            f"**總淨利**: `{net_profit:,.0f}` 油幣\n"
            f"**總交易數**: `{overall.get('total_transactions', 0):,}` 筆"
        )

        by_type = balance_data.get("by_type", [])
        if by_type:
            income_sources = sorted(
                [t for t in by_type if float(t.get("net_profit", 0) or 0) > 0],
                key=lambda x: float(x.get("net_profit", 0) or 0),
                reverse=True,
            )[:5]

            expense_sources = sorted(
                [t for t in by_type if float(t.get("net_profit", 0) or 0) < 0],
                key=lambda x: float(x.get("net_profit", 0) or 0),
            )[:5]

            if income_sources:
                income_str = "\n".join(
                    [
                        f"`{t.get('transaction_type', 'N/A'):<12}`: "
                        f"`{float(t.get('net_profit', 0) or 0):>+10,.0f}` 油幣 "
                        f"(`{t.get('transaction_count', 0)}`筆)"
                        for t in income_sources
                    ]
                )
                embed.add_field(name="📈 主要收入來源", value=income_str, inline=False)

            if expense_sources:
                expense_str = "\n".join(
                    [
                        f"`{t.get('transaction_type', 'N/A'):<12}`: "
                        f"`{float(t.get('net_profit', 0) or 0):>+10,.0f}` 油幣 "
                        f"(`{t.get('transaction_count', 0)}`筆)"
                        for t in expense_sources
                    ]
                )
                embed.add_field(name="📉 主要支出來源", value=expense_str, inline=False)

        embed.set_footer(text="統計資料更新時間")
        return embed

    def _create_transfer_stats_embed(
        self, health_data: Dict[str, Any]
    ) -> discord.Embed:
        """創建轉帳與交易紀錄嵌入消息"""
        transfer_data = health_data.get("transfer_stats", {})
        is_healthy = transfer_data.get("status") == "✅"

        embed = discord.Embed(
            title="🔄 近30天交易紀錄",
            color=discord.Color.blue() if is_healthy else discord.Color.red(),
            timestamp=datetime.now(timezone.utc),
        )

        if not is_healthy:
            embed.add_field(
                name="❌ 系統狀態",
                value=f"錯誤: {transfer_data.get('error', '未知錯誤')}",
                inline=False,
            )
            return embed

        by_user = transfer_data.get("by_user", [])
        if not by_user:
            embed.description = "最近30天內沒有轉帳或卡片交易紀錄。"
            return embed

        # 接收排行榜
        top_receivers = sorted(
            by_user, key=lambda x: float(x.get("total_received", 0)), reverse=True
        )[:10]
        receiver_str = ""
        for i, user in enumerate(top_receivers):
            nickname = user.get("nickname", "未知用戶")
            amount = float(user.get("total_received", 0))
            count = user.get("received_count", 0)
            receiver_str += (
                f"`{i + 1}.` **{nickname}**: `{amount:,.0f}` 油幣 ({count}次)\n"
            )
        if not receiver_str:
            receiver_str = "無接收紀錄"
        embed.add_field(
            name="🏆 總收入排行榜 (Top 10)", value=receiver_str, inline=False
        )

        # 發送排行榜
        top_senders = sorted(
            by_user, key=lambda x: abs(float(x.get("total_sent", 0))), reverse=True
        )[:10]
        sender_str = ""
        for i, user in enumerate(top_senders):
            nickname = user.get("nickname", "未知用戶")
            amount = abs(float(user.get("total_sent", 0)))
            count = user.get("sent_count", 0)
            sender_str += (
                f"`{i + 1}.` **{nickname}**: `{amount:,.0f}` 油幣 ({count}次)\n"
            )
        if not sender_str:
            sender_str = "無發送紀錄"
        embed.add_field(name="💸 總支出排行榜 (Top 10)", value=sender_str, inline=False)

        embed.set_footer(text="統計資料更新時間 (包含油幣轉帳與卡片油幣交易)")
        return embed

    async def _collect_story_stats(self) -> Dict[str, Any]:
        """收集AI劇情冒G險統計資料"""
        stats = {"status": "❌", "error": None}
        try:
            pool = get_pool()
            if not pool:
                stats["error"] = "數據庫連接池不可用"
                return stats

            async with pool.acquire() as conn:
                active_stories_query = (
                    "SELECT COUNT(*) FROM stories WHERE status = 'active';"
                )
                active_stories_count = await conn.fetchval(active_stories_query)

                popular_themes_query = """
                    SELECT theme_title, COUNT(*) as story_count
                    FROM stories
                    WHERE theme_title IS NOT NULL
                    GROUP BY theme_title
                    ORDER BY story_count DESC
                    LIMIT 5;
                """
                popular_themes = await conn.fetch(popular_themes_query)

                stats.update(
                    {
                        "active_stories_count": active_stories_count,
                        "popular_themes": [dict(row) for row in popular_themes],
                    }
                )
            stats["status"] = "✅"
        except Exception as e:
            logger.error("收集AI劇情冒險統計失敗: %s", e, exc_info=True)
            stats["error"] = str(e)
        return stats

    async def _collect_stock_market_stats(self) -> Dict[str, Any]:
        """收集股市系統統計資料"""
        stats = {"status": "❌", "error": None}
        try:
            pool = get_pool()
            if not pool:
                stats["error"] = "數據庫連接池不可用"
                return stats

            async with pool.acquire() as conn:
                # 總市值
                total_market_cap_query = """
                    SELECT SUM(va.current_price * pp.quantity)
                    FROM player_portfolios pp
                    JOIN virtual_assets va ON pp.asset_id = va.asset_id;
                """
                total_market_cap = await conn.fetchval(total_market_cap_query)

                # 交易量 (近7天)
                trade_volume_query = """
                    SELECT
                        SUM(total_amount) as total_volume,
                        COUNT(*) as transaction_count
                    FROM market_transactions
                    WHERE "timestamp" >= NOW() - INTERVAL '7 days';
                """
                trade_volume = await conn.fetchrow(trade_volume_query)

                # 熱門資產 (近7天)
                popular_assets_query = """
                    SELECT
                        va.asset_name,
                        COUNT(mt.id) as transaction_count
                    FROM market_transactions mt
                    JOIN virtual_assets va ON mt.asset_id = va.asset_id
                    WHERE mt."timestamp" >= NOW() - INTERVAL '7 days'
                    GROUP BY va.asset_name
                    ORDER BY transaction_count DESC
                    LIMIT 5;
                """
                popular_assets = await conn.fetch(popular_assets_query)

                stats.update(
                    {
                        "total_market_cap": total_market_cap or 0,
                        "trade_volume_7d": dict(trade_volume)
                        if trade_volume
                        else {"total_volume": 0, "transaction_count": 0},
                        "popular_assets_7d": [dict(row) for row in popular_assets],
                    }
                )
            stats["status"] = "✅"
        except Exception as e:
            logger.error("收集股市系統統計失敗: %s", e, exc_info=True)
            stats["error"] = str(e)
        return stats

    def _create_story_stats_embed(self, health_data: Dict[str, Any]) -> discord.Embed:
        """創建AI劇情冒險統計嵌入消息"""
        story_data = health_data.get("story_stats", {})
        is_healthy = story_data.get("status") == "✅"

        embed = discord.Embed(
            title="🎭 AI劇情冒險統計",
            color=discord.Color.purple() if is_healthy else discord.Color.red(),
            timestamp=datetime.now(timezone.utc),
        )

        if not is_healthy:
            embed.add_field(
                name="❌ 系統狀態",
                value=f"錯誤: {story_data.get('error', '未知錯誤')}",
                inline=False,
            )
            return embed

        embed.description = (
            f"**目前活躍冒險數**: `{story_data.get('active_stories_count', 0):,}`"
        )

        popular_themes = story_data.get("popular_themes", [])
        if popular_themes:
            themes_str = "\n".join(
                [
                    f"`{i + 1}.` **{theme.get('theme_title', 'N/A')}**: `{theme.get('story_count', 0):,}` 個故事"
                    for i, theme in enumerate(popular_themes)
                ]
            )
            embed.add_field(name="🏆 熱門主題 Top 5", value=themes_str, inline=False)
        else:
            embed.add_field(name="🏆 熱門主題 Top 5", value="暫無數據", inline=False)

        embed.set_footer(text="統計資料更新時間")
        return embed

    def _create_stock_market_stats_embed(
        self, health_data: Dict[str, Any]
    ) -> discord.Embed:
        """創建股市系統統計嵌入消息"""
        stock_data = health_data.get("stock_market_stats", {})
        is_healthy = stock_data.get("status") == "✅"

        embed = discord.Embed(
            title="📈 股市系統統計",
            color=discord.Color.dark_green() if is_healthy else discord.Color.red(),
            timestamp=datetime.now(timezone.utc),
        )

        if not is_healthy:
            embed.add_field(
                name="❌ 系統狀態",
                value=f"錯誤: {stock_data.get('error', '未知錯誤')}",
                inline=False,
            )
            return embed

        total_cap = stock_data.get("total_market_cap", 0)
        volume_7d = stock_data.get("trade_volume_7d", {})
        total_volume = float(volume_7d.get("total_volume") or 0)
        transaction_count = volume_7d.get("transaction_count", 0)

        embed.add_field(
            name="📊 市場總覽",
            value=f"**總市值**: `{total_cap:,.0f}` 油幣\n"
            f"**近7日交易額**: `{total_volume:,.0f}` 油幣\n"
            f"**近7日交易筆數**: `{transaction_count:,}` 筆",
            inline=False,
        )

        popular_assets = stock_data.get("popular_assets_7d", [])
        if popular_assets:
            assets_str = "\n".join(
                [
                    f"`{i + 1}.` **{asset.get('asset_name', 'N/A')}**: `{asset.get('transaction_count', 0):,}` 筆交易"
                    for i, asset in enumerate(popular_assets)
                ]
            )
            embed.add_field(
                name="🔥 近7日熱門資產 Top 5", value=assets_str, inline=False
            )
        else:
            embed.add_field(
                name="🔥 近7日熱門資產 Top 5", value="暫無數據", inline=False
            )

        embed.set_footer(text="統計資料更新時間")
        return embed

    def _is_overall_healthy(self, health_data: Dict[str, Any]) -> bool:
        """判斷整體健康狀態"""
        # 檢查關鍵組件是否正常，使用安全的字典訪問
        try:
            critical_checks = [
                health_data.get("bot", {}).get("online", False),
                health_data.get("database", {}).get("postgresql", {}).get("status")
                == "✅",
                health_data.get("system", {}).get("status") == "✅",
            ]
            return all(critical_checks)
        except Exception:
            # 如果檢查過程中發生任何錯誤，認為系統不健康
            return False


class HealthView(BaseView):
    """機器人資訊的切頁視圖"""

    def __init__(
        self, health_cog: HealthCog, health_data: Dict[str, Any], user_id: int
    ):
        super().__init__(bot=health_cog.bot, user_id=user_id, timeout=300)
        self.health_cog = health_cog
        self.health_data = health_data
        self.current_page = "health"  # 當前頁面：health, materials, gacha_stats, balance_stats, transfer_stats, story_stats, stock_market_stats

        # 添加支援伺服器連結按鈕
        support_button = discord.ui.Button(
            label="支援伺服器",
            style=discord.ButtonStyle.link,
            url="https://discord.gg/kDua5dDt4v",
            emoji="🤝",
            row=0,
        )
        self.add_item(support_button)

        # 設置初始按鈕狀態
        self._update_button_styles()

    def _update_button_styles(self):
        """更新按鈕樣式以反映當前頁面"""
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                if item.custom_id == "health_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "health"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "materials_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "materials"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "gacha_stats_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "gacha_stats"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "balance_stats_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "balance_stats"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "transfer_stats_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "transfer_stats"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "story_stats_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "story_stats"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "stock_market_stats_page":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.current_page == "stock_market_stats"
                        else discord.ButtonStyle.secondary
                    )

    @discord.ui.button(
        label="資訊狀態",
        style=discord.ButtonStyle.primary,
        custom_id="health_page",
        emoji="🤖",
    )
    async def health_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到資訊狀態頁面"""
        await interaction.response.defer()

        self.current_page = "health"
        self._update_button_styles()

        # 創建資訊狀態embed
        embed = self.health_cog._create_health_embed(self.health_data)

        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="使用素材",
        style=discord.ButtonStyle.secondary,
        custom_id="materials_page",
        emoji="📋",
    )
    async def materials_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到使用素材頁面"""
        await interaction.response.defer()

        self.current_page = "materials"
        self._update_button_styles()

        # 創建使用素材embed
        embed = self.health_cog._create_materials_embed()

        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="Gacha統計",
        style=discord.ButtonStyle.secondary,
        custom_id="gacha_stats_page",
        emoji="📊",
    )
    async def gacha_stats_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到Gacha統計頁面"""
        await interaction.response.defer()

        self.current_page = "gacha_stats"
        self._update_button_styles()

        # 創建Gacha統計embed
        embed = self.health_cog._create_gacha_stats_embed(self.health_data)

        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="餘額統計",
        style=discord.ButtonStyle.secondary,
        custom_id="balance_stats_page",
        emoji="💰",
        row=2,
    )
    async def balance_stats_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到餘額統計頁面"""
        await interaction.response.defer()

        self.current_page = "balance_stats"
        self._update_button_styles()

        embed = self.health_cog._create_balance_stats_embed(self.health_data)

        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="轉帳紀錄",
        style=discord.ButtonStyle.secondary,
        custom_id="transfer_stats_page",
        emoji="🔄",
        row=2,
    )
    async def transfer_stats_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到轉帳紀錄頁面"""
        await interaction.response.defer()

        self.current_page = "transfer_stats"
        self._update_button_styles()

        embed = self.health_cog._create_transfer_stats_embed(self.health_data)

        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="劇情冒險",
        style=discord.ButtonStyle.secondary,
        custom_id="story_stats_page",
        emoji="🎭",
        row=2,
    )
    async def story_stats_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到AI劇情冒險統計頁面"""
        await interaction.response.defer()
        self.current_page = "story_stats"
        self._update_button_styles()
        embed = self.health_cog._create_story_stats_embed(self.health_data)
        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="股市系統",
        style=discord.ButtonStyle.secondary,
        custom_id="stock_market_stats_page",
        emoji="📈",
        row=2,
    )
    async def stock_market_stats_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """切換到股市系統統計頁面"""
        await interaction.response.defer()
        self.current_page = "stock_market_stats"
        self._update_button_styles()
        embed = self.health_cog._create_stock_market_stats_embed(self.health_data)
        await interaction.edit_original_response(embed=embed, view=self)


async def setup(bot: commands.Bot):
    """註冊機器人資訊相關命令"""
    await bot.add_cog(HealthCog(bot))
    logger.info("HealthCog 已成功載入")
