"""
Pioneer System 研究中心視圖
讓玩家投資研究項目以獲得永久加成
"""

from typing import TYPE_CHECKING

import discord

from gacha.exceptions import GameError
from pioneer import repositories
from pioneer.exceptions import (
    PioneerError,
    PioneerResearchMaxLevelError,
)
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader
    from utils.base_view import BotType


class ResearchView(BaseView):
    """研究中心視圖"""

    def __init__(self, user_id: int, game_data: "GameDataLoader", bot: "BotType"):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.available_projects = []
        self.current_page = 0
        self.projects_per_page = 3

    async def create_research_embed(self) -> discord.Embed:
        """創建研究中心 Embed"""
        try:
            embed = discord.Embed(
                title="🔬 研究中心",
                description="投資油幣來解鎖永久加成！\n研究效果會應用到所有相關活動中。",
                color=0x4A90E2,
            )

            # 獲取可用研究項目
            await self._load_available_projects()

            if self.available_projects:
                start_idx = self.current_page * self.projects_per_page
                end_idx = start_idx + self.projects_per_page
                page_projects = self.available_projects[start_idx:end_idx]

                for i, (_, project_config, current_level) in enumerate(page_projects):
                    # 計算下一級的成本和效果
                    next_level = current_level + 1
                    try:
                        from pioneer.core.processors.research_processor import (
                            ResearchProcessor,
                        )

                        processor = ResearchProcessor(self.game_data, repositories)
                        cost = await processor._calculate_research_cost(
                            project_config, next_level
                        )
                    except Exception as e:
                        logger.error("計算研究成本失敗: %s", e, exc_info=True)
                        cost = 0
                    current_effect = (
                        current_level * project_config.effect_per_level * 100
                    )
                    next_effect = next_level * project_config.effect_per_level * 100

                    # 統一使用油幣
                    currency_emoji = "🛢️"
                    currency_name = "油幣"

                    field_value = [
                        f"**當前等級:** {current_level}",
                        f"**當前效果:** +{current_effect:.1f}%",
                        f"**下一級效果:** +{next_effect:.1f}%",
                        f"**升級成本:** {currency_emoji} {cost:,} {currency_name}",
                    ]

                    # 檢查是否達到上限
                    max_level = getattr(project_config, "max_level", None)
                    if max_level and current_level >= max_level:
                        field_value.append("**狀態:** ✅ 已達最大等級")
                    else:
                        field_value.append(f"**按鈕:** {i + 1}️⃣ 升級")

                    embed.add_field(
                        name=f"{i + 1}. {project_config.name}",
                        value="\n".join(field_value),
                        inline=False,
                    )

                # 分頁信息
                total_pages = (
                    len(self.available_projects) + self.projects_per_page - 1
                ) // self.projects_per_page
                embed.set_footer(
                    text=f"第 {self.current_page + 1}/{total_pages} 頁 • 總共 {len(self.available_projects)} 個研究項目"
                )
            else:
                embed.add_field(
                    name="📋 研究項目", value="暫無可用的研究項目", inline=False
                )

            return embed

        except Exception as e:
            logger.error("創建研究中心 Embed 失敗: %s", e, exc_info=True)
            raise GameError("無法載入研究中心信息") from e

    async def _load_available_projects(self):
        """載入可用的研究項目"""
        try:
            # 獲取所有研究項目配置
            research_configs = self.game_data._configs.get("research_projects", {})

            self.available_projects = []
            for project_id, project_config in research_configs.items():
                # 獲取用戶當前的研究等級
                current_research = await repositories.get_research_level(
                    self.user_id, project_id
                )
                current_level = current_research.level if current_research else 0

                self.available_projects.append(
                    (project_id, project_config, current_level)
                )

            # 按項目名稱排序
            self.available_projects.sort(key=lambda x: x[1].name)

        except Exception as e:
            logger.error("載入研究項目失敗: %s", e, exc_info=True)
            self.available_projects = []

    @discord.ui.button(
        label="上一頁", style=discord.ButtonStyle.secondary, emoji="◀️", row=0
    )
    async def previous_page(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """上一頁按鈕"""
        if self.current_page > 0:
            self.current_page -= 1
            embed = await self.create_research_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(
        label="下一頁", style=discord.ButtonStyle.secondary, emoji="▶️", row=0
    )
    async def next_page(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """下一頁按鈕"""
        total_pages = (
            len(self.available_projects) + self.projects_per_page - 1
        ) // self.projects_per_page
        if self.current_page < total_pages - 1:
            self.current_page += 1
            embed = await self.create_research_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label=None, style=discord.ButtonStyle.primary, emoji="1️⃣", row=1)
    async def project_1_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """研究項目1按鈕"""
        await self._upgrade_project(interaction, 0)

    @discord.ui.button(label=None, style=discord.ButtonStyle.primary, emoji="2️⃣", row=1)
    async def project_2_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """研究項目2按鈕"""
        await self._upgrade_project(interaction, 1)

    @discord.ui.button(label=None, style=discord.ButtonStyle.primary, emoji="3️⃣", row=1)
    async def project_3_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """研究項目3按鈕"""
        await self._upgrade_project(interaction, 2)

    async def _upgrade_project(
        self, interaction: discord.Interaction, project_index: int
    ):
        """升級研究項目"""
        try:
            start_idx = self.current_page * self.projects_per_page
            actual_index = start_idx + project_index

            if actual_index >= len(self.available_projects):
                raise PioneerError("無效的研究項目選擇")

            project_id, project_config, current_level = self.available_projects[
                actual_index
            ]

            # 檢查是否達到最大等級
            max_level = getattr(project_config, "max_level", None)
            if max_level and current_level >= max_level:
                raise PioneerResearchMaxLevelError(project_config.name, max_level)

            # 執行研究升級
            from pioneer.modules import action_module

            result = await action_module.execute_action(
                self.user_id,
                "research",  # 使用通用的研究動作
                {"project_id": project_id},
            )

            if result.success:
                embed = SuccessEmbed(
                    title="✅ 研究升級成功", description=result.message
                )

                # 刷新研究中心視圖
                research_embed = await self.create_research_embed()
                await interaction.response.edit_message(embed=research_embed, view=self)

                # 發送成功消息
                await interaction.followup.send(embed=embed)
            else:
                # 如果 action_module 返回失敗，通常是業務邏輯問題
                raise PioneerError(result.message)

        except PioneerError:
            # 業務邏輯異常會被 BaseView.on_error 處理
            raise

    @discord.ui.button(
        label="返回主頁", style=discord.ButtonStyle.secondary, emoji="🔙", row=2
    )
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """返回主頁按鈕"""
        from pioneer.views.pioneer_views import PioneerMainView

        view = PioneerMainView(self.user_id, self.game_data, self.bot)
        embed = await view.create_main_embed(interaction)
        await interaction.response.edit_message(embed=embed, view=view)
