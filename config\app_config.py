import os
import sys
from decimal import Decimal
from functools import lru_cache
from typing import Any, Dict, List, Optional, Tuple, Type, Union

import yaml
from dotenv import load_dotenv
from pydantic import BaseModel, Field, ValidationError, field_validator
from pydantic_settings import (
    BaseSettings,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)

# 將專案根目錄加到 sys.path，以解決模組導入問題
# 由於此文件已移至 config/，需要將上層目錄 (專案根目錄) 加入路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from gacha.models.shop_models import ShopItemDefinition  # noqa: E402
from utils.logger import logger  # noqa: E402

# 設定文件路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
dotenv_path = os.path.join(project_root, ".env")

# 立即載入環境變數，確保所有後續讀取操作都能獲取到正確的值
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path, override=True)
    logger.info("環境變數從 %s 載入完成", dotenv_path)
    # 輸出幾個關鍵環境變數的值，方便除錯
    logger.info("GACHA_DB_NAME = %s", os.getenv("GACHA_DB_NAME", "未設置"))
    logger.info("PG_HOST = %s", os.getenv("PG_HOST", "未設置"))
    logger.info("PG_PORT = %s", os.getenv("PG_PORT", "未設置"))
    logger.info("PG_USER = %s", os.getenv("PG_USER", "未設置"))
else:
    logger.warning("找不到環境變數文件: %s", dotenv_path)

CONFIG_FILE_PATH = os.path.join(project_root, "config", "config.yaml")
GACHA_SETTINGS_FILE_PATH = os.path.join(project_root, "config", "gacha_settings.yaml")
UI_SETTINGS_FILE_PATH = os.path.join(project_root, "config", "ui_settings.yaml")

# 推遲載入環境變數和配置 logger，確保其他模組先完成環境變數設定
# 將在 load_settings() 中正式初始化


# 新增 GachaNotificationSettings 模型
class GachaNotificationSettings(BaseModel):
    webhook_url: Optional[str] = None
    notify_rarities: Dict[str, List[int]] = Field(default_factory=dict)


class PoolRarityConfigItem(BaseModel):
    """Represents an item in the pool rarity config list."""

    rarity_level: int
    probability: float


class PoolConfigItem(BaseModel):
    """Represents an item in the pool config list."""

    pool_name: str
    probability: float


class PoolConfigurationDetail(BaseModel):
    name: str
    description: str
    pools: List[str]


class LeaderboardConfigDetail(BaseModel):
    title: str
    label: Optional[str] = None
    description: str
    color: int
    category: str
    default_for_category: Optional[bool] = False
    pool_filterable: Optional[bool] = False


class ProfileSettings(BaseModel):
    image_local_base_path: str = "downloaded_gacha_master_cards"
    # Add other profile-specific settings here if needed in the future


class PreciseTimingDetectionSettings(BaseModel):
    enabled: bool = True
    target_interval: int = 3600  # 1 hour in seconds
    tolerance: int = 20  # ±20 seconds tolerance
    consecutive_required: int = 2  # Number of consecutive precise intervals required
    command_name: str = "hourly"  # Command to monitor


class ActivityMonitoringSettings(BaseModel):
    global_blacklist: List[int] = Field(
        default_factory=list,
        description="List of user IDs banned from all commands.",
    )
    trading_blacklist: List[int] = Field(
        default_factory=list,
        description="List of user IDs banned from trading commands (transfer, trade, poker1v1).",
    )
    precise_timing_detection: PreciseTimingDetectionSettings = Field(
        default_factory=PreciseTimingDetectionSettings,
        description="Settings for detecting precise timing automation patterns.",
    )


class GachaCoreSettings(BaseModel):
    """Settings related to the core gacha mechanics, prices, pools etc."""

    activity_monitoring: ActivityMonitoringSettings = Field(
        default_factory=ActivityMonitoringSettings
    )
    rarity_sort_values: Dict[int, int] = Field(default_factory=dict)
    pool_rarity_prices: Dict[str, Dict[int, int]] = Field(default_factory=dict)
    pool_costs: Dict[str, int] = Field(default_factory=dict)
    all_pool_rarity_configs: Dict[str, List[PoolRarityConfigItem]] = Field(
        default_factory=dict
    )
    pool_config: List[PoolConfigItem] = Field(default_factory=list)
    max_star_level: int = 35
    stars_per_tier: int = 5
    pool_type_names: Dict[str, str] = Field(default_factory=dict)
    pool_type_prefixes: Dict[str, str] = Field(default_factory=dict)
    pool_configurations: Dict[str, PoolConfigurationDetail] = Field(
        default_factory=dict
    )
    rarity_mapping: Dict[str, List[int]] = Field(default_factory=dict)
    leaderboard_config: Dict[str, LeaderboardConfigDetail] = Field(default_factory=dict)
    leaderboard_items_per_page: int = 5
    mixed_pool_draw_config: List[PoolConfigItem] = Field(default_factory=list)
    trade_fee_percentage: float = 0.03
    min_trade_fee: int = 10
    ticket_shop_items: Dict[str, ShopItemDefinition] = Field(default_factory=dict)
    wish_max_slots: int = Field(
        default=10, description="Maximum number of wish slots a user can have."
    )
    wish_max_power_level: int = Field(
        default=10, description="Maximum wish power level a user can reach."
    )
    default_wish_slots: int = Field(
        default=1, description="Default number of wish slots for new users."
    )
    default_wish_power_level: int = Field(
        default=1, description="Default wish power level for new users."
    )
    default_wish_multiplier: float = Field(
        default=3.0, description="Default wish chance multiplier for power level 1."
    )
    wish_slot_costs: Dict[int, int] = Field(
        default_factory=lambda: {
            1: 10000,
            2: 25000,
            3: 50000,
            4: 100000,
            5: 250000,
            6: 500000,
            7: 1000000,
            8: 2000000,
            9: 5000000,
        },
        description=(
            "Costs to upgrade to the next wish slot (key is current_slots, "
            "value is cost for next)."
        ),
    )
    wish_power_costs: Dict[int, int] = Field(
        default_factory=lambda: {
            1: 20000,
            2: 40000,
            3: 80000,
            4: 160000,
            5: 300000,
            6: 500000,
            7: 800000,
            8: 1200000,
            9: 2000000,
        },
        description=(
            "Costs to upgrade to the next wish power level "
            "(key is current_level, value is cost for next)."
        ),
    )
    wish_power_multipliers: Dict[int, float] = Field(
        default_factory=lambda: {
            1: 3.0,
            2: 4.0,
            3: 5.0,
            4: 6.0,
            5: 8.0,
            6: 10.0,
            7: 12.0,
            8: 15.0,
            9: 18.0,
            10: 20.0,
        },
        description="Wish chance multipliers for each power level.",
    )
    economy_daily_reward: int = Field(
        default=5000, description="Amount of oil for daily reward."
    )
    economy_hourly_reward: int = Field(
        default=2000, description="Amount of oil for hourly reward."
    )
    economy_hourly_claim_key_prefix: str = Field(
        default="gacha:hourly_claim:", description="Redis key prefix for hourly claims."
    )
    profile: Optional[ProfileSettings] = Field(default_factory=ProfileSettings)

    @field_validator("ticket_shop_items", mode="before")
    @classmethod
    def convert_ticket_shop_items_to_model(
        cls, v: Any
    ) -> Dict[str, ShopItemDefinition]:
        if isinstance(v, dict):
            validated_items: Dict[str, ShopItemDefinition] = {}
            for item_id, item_data in v.items():
                if isinstance(item_data, dict):
                    try:
                        validated_items[item_id] = ShopItemDefinition(
                            **{"id": item_id, **item_data}
                        )
                    except ValidationError as e:
                        logger.error(
                            "Validation error for ticket_shop_item '%s': %s. Data: %s",
                            item_id,
                            e,
                            item_data,
                        )
                else:
                    logger.warning(
                        (
                            "Skipping ticket_shop_item '%s' as its data is not a "
                            "dictionary. Data: %s"
                        ),
                        item_id,
                        item_data,
                    )
            return validated_items
        elif v is None:
            return {}
        logger.warning(
            "ticket_shop_items in YAML is not a dictionary, received type: %s. "
            "Value: %s",
            type(v),
            v,
        )
        return {}

    @field_validator("all_pool_rarity_configs", mode="before")
    @classmethod
    def convert_tuple_list_to_model_list(cls, v):
        if isinstance(v, dict):
            new_v = {}
            for pool_name, config_list in v.items():
                if isinstance(config_list, list):
                    new_v[pool_name] = [
                        PoolRarityConfigItem(rarity_level=item[0], probability=item[1])
                        for item in config_list
                        if isinstance(item, (list, tuple)) and len(item) == 2
                    ]
                else:
                    new_v[pool_name] = []
            return new_v
        return v

    @field_validator("pool_config", mode="before")
    @classmethod
    def convert_tuple_list_to_pool_config_list(cls, v):
        if isinstance(v, list):
            return [
                PoolConfigItem(pool_name=item[0], probability=item[1])
                for item in v
                if isinstance(item, (list, tuple)) and len(item) == 2
            ]
        return v

    @field_validator("mixed_pool_draw_config", mode="before")
    @classmethod
    def convert_tuple_list_to_mixed_pool_config_list(cls, v):
        if isinstance(v, list):
            return [
                PoolConfigItem(pool_name=item[0], probability=item[1])
                for item in v
                if isinstance(item, (list, tuple)) and len(item) == 2
            ]
        return v


class UIButtonEmojis(BaseModel):
    """Holds UI button emojis."""

    old_card: Optional[str] = "<a:sw:1365447243863429273>"
    favorite: Optional[str] = "<a:pu:1365482490478989353>"
    new_card: Optional[str] = "<a:sr:1357714854244515936>"
    sell: Optional[str] = "<:sellall:1366135697941205163>"
    next_page: Optional[str] = "➡️"
    prev_page: Optional[str] = "⬅️"
    first_page: Optional[str] = "⏪"
    last_page: Optional[str] = "⏩"
    confirm: Optional[str] = "✅"
    cancel: Optional[str] = "❌"


class UISettings(BaseModel):
    """Settings related to UI elements like emojis, colors, display names."""

    default_rarity_emojis: Dict[int, str] = Field(default_factory=dict)
    rarity_emojis: Dict[str, Dict[int, str]] = Field(default_factory=dict)
    pool_specific_rarity_emojis: Dict[str, Dict[int, str]] = Field(default_factory=dict)
    default_encyclopedia_rarity_emojis: Dict[int, str] = Field(default_factory=dict)
    encyclopedia_rarity_emojis: Dict[str, Dict[int, str]] = Field(
        default_factory=dict, alias="pool_specific_encyclopedia_rarity_emojis"
    )
    rarity_images: Dict[str, Dict[int, str]] = Field(default_factory=dict)
    star_emojis: Dict[int, str] = Field(default_factory=dict)
    ui_button_emojis: UIButtonEmojis = Field(default_factory=UIButtonEmojis)
    pool_type_emojis: Dict[str, str] = Field(default_factory=dict)
    user_friendly_display_names: Dict[str, str] = Field(
        default_factory=dict, alias="user_friendly_rarity_display_names"
    )
    rarity_display_codes: Dict[int, str] = Field(default_factory=dict)
    rarity_colors_int: Dict[str, Dict[int, int]] = Field(default_factory=dict)
    completion_indicator_emojis: Dict[str, str] = Field(default_factory=dict)
    oil_emoji: str = "<:oi:1382174314811363470>"
    ticket_emoji: str = "🎟️"
    minigame_emojis: Dict[str, str] = Field(default_factory=dict)
    news_type_readable_names: Dict[str, str] = Field(default_factory=dict)
    character_archetype_readable_names: Dict[str, str] = Field(default_factory=dict)
    news_type_colors_int: Dict[str, int] = Field(default_factory=dict)
    common_ui_colors: Dict[str, int] = Field(default_factory=dict)
    form_validation: Dict[str, int] = Field(default_factory=dict)
    ui_timeouts: Dict[str, int] = Field(default_factory=dict)
    card_settings: Dict[str, int] = Field(default_factory=dict)


class TaskFrequencies(BaseModel):
    calculate_supply_demand_modifier_hours: int = 24  # 從1小時改為24小時
    update_stock_prices_minutes: int = 10  # 股票價格更新保持10分鐘（股票系統需要）
    calculate_gacha_category_stock_influence_minutes: int = (
        1440  # 從15分鐘改為1440分鐘（24小時）
    )
    cleanup_expired_news_effects_minutes: int = 5  # 清理過期新聞效果保持5分鐘
    full_price_recalculation_hours: int = 24
    news_scheduler_manager_minutes: int = 5  # 新聞調度保持5分鐘（股票系統需要）
    calculate_daily_anchor_prices_hours: int = 24
    check_stock_lifecycle_minutes: int = 15  # 檢查股票生命週期保持15分鐘


class TaskInitialDelays(BaseModel):
    calculate_supply_demand_modifier_seconds: int = 10
    update_stock_prices_seconds: int = 300  # 延遲5分鐘啟動，避免反覆重啟時瘋狂更新
    news_scheduler_manager_seconds: int = 60  # 延遲1分鐘啟動
    calculate_gacha_category_stock_influence_seconds: int = 20
    cleanup_expired_news_effects_seconds: int = 25
    full_price_recalculation_seconds: int = 600  # 延遲10分鐘
    calculate_daily_anchor_prices_seconds: int = 180  # 延遲3分鐘後執行，避免啟動時衝突
    check_stock_lifecycle_seconds: int = 120  # 延遲2分鐘，讓其他服務先穩定


class SupplyDemandWeights(BaseModel):
    supply: float = 0.5
    owner_diversity_effect_on_supply: float = 0.2
    wish: float = 0.3
    fav: float = 0.2


class SupplyDemandModifierDefaults(BaseModel):
    min: float = 0.1
    max: float = 10.0


class SupplyDemandConfig(BaseModel):
    weights: SupplyDemandWeights = Field(default_factory=SupplyDemandWeights)
    smooth_factor: float = 0.1
    modifier_defaults: SupplyDemandModifierDefaults = Field(
        default_factory=SupplyDemandModifierDefaults
    )


class StockMarketNewsImpact(BaseModel):
    positive_min: float = 0.015
    positive_max: float = 0.05
    negative_min: float = -0.05
    negative_max: float = -0.015
    neutral: float = 0.0


class StockMarketTempNewsEffect(BaseModel):
    offset_positive: float = 0.05
    offset_negative: float = -0.05
    duration_hours_min: int = 2
    duration_hours_max: int = 5


class StockMarketCategoryInfluenceClamp(BaseModel):
    min: float = 0.5
    max: float = 2.0


class BottomPriceSuppressionConfig(BaseModel):
    threshold_factor: float = 1.1
    upward_momentum_suppression_factor: float = 0.3
    downward_pressure_chance: float = 0.15
    downward_pressure_percent: float = 0.003


class StockMarketConfig(BaseModel):
    global_market_volatility_factor: float = 0.7
    default_base_volatility: float = 0.01
    default_volatility_factor: float = 1.5
    news_impact: StockMarketNewsImpact = Field(default_factory=StockMarketNewsImpact)
    temp_news_effect: StockMarketTempNewsEffect = Field(
        default_factory=StockMarketTempNewsEffect
    )
    price_change_to_modifier_offset_factor: float = 0.5
    category_influence_clamp: StockMarketCategoryInfluenceClamp = Field(
        default_factory=StockMarketCategoryInfluenceClamp
    )
    asset_news_cooldown_minutes: int = 30
    default_news_type_generation_interval_minutes: int = 120
    news_type_generation_intervals_minutes: Dict[str, int] = Field(default_factory=dict)
    strong_news_impact_threshold: float = 0.035
    sentiment_inertia_window_minutes: int = 60
    conflicting_news_dampening_factor: float = 0.5
    transaction_fee_rate: float = 0.015
    minimum_transaction_fee: float = 5.0
    per_share_fee_amount: float = 0.05
    chart_thread_pool_workers: int = 3  # 增加線程池大小以提高並發處理
    chart_figsize_width: float = 10.0  # 平衡視覺效果和生成速度
    chart_figsize_height: float = 5.5  # 優化比例
    chart_date_format: str = "%m/%d %H:%M"  # 簡潔的日期格式
    chart_max_xticks: int = 6  # 減少刻度數量以提高速度
    chart_dpi: int = 120  # 平衡清晰度和生成速度
    price_history_days: int = 7
    default_initial_anchor_price: Decimal = Field(default=Decimal("300.00"))
    default_stock_influence_weight: Decimal = Field(default=Decimal("1.0"))
    news_relevance_window_hours: int = 24
    anchor_price_fallback_days_history: int = 3
    anchor_price_max_stale_days: int = 2
    anchor_price_min_trading_days_for_avg: int = 1
    global_min_asset_price: float = 1.0
    global_max_asset_price: float = 1000000.0
    max_price_change_percent: float = 0.05
    min_initial_total_shares: int = 15000
    max_initial_total_shares: int = 100000
    st_trigger_consecutive_checks_at_min_price: int = 72
    st_trigger_min_market_cap: int = 150000
    st_trigger_consecutive_checks_below_market_cap: int = 72
    st_trigger_low_price_threshold: float = 1.5
    st_recovery_min_price: float = 1.5
    st_recovery_min_market_cap: int = 100000
    delist_trigger_consecutive_checks_in_st: int = (
        288  # 3天 = 72小時 = 288次檢查 (每15分鐘檢查一次)
    )
    delist_trigger_min_market_cap: int = 20000
    delist_trigger_consecutive_checks_below_delist_market_cap: int = 72
    delisted_buyback_avg_price_checks: int = 10
    delisted_buyback_price_percentage: float = 0.1
    delisted_buyback_min_price: float = 0.01
    delisted_buyback_max_price: float = 0.1
    min_active_companies: int = 10
    st_bottom_price_suppression: Optional[BottomPriceSuppressionConfig] = None
    bottom_price_suppression: BottomPriceSuppressionConfig = Field(
        default_factory=BottomPriceSuppressionConfig
    )


class GachaStockIntegrationConfig(BaseModel):
    tasks: TaskFrequencies = Field(default_factory=TaskFrequencies)
    supply_demand: SupplyDemandConfig = Field(default_factory=SupplyDemandConfig)
    stock_market: StockMarketConfig = Field(default_factory=StockMarketConfig)
    task_initial_delays: TaskInitialDelays = Field(default_factory=TaskInitialDelays)


class MarketStatsUpdaterConfig(BaseModel):
    batch_size: int = 500
    batch_interval_seconds: float = 1.0
    single_event_queue_size: int = 50000
    favorite_batch_size: int = 1000


class PriceUpdateServiceConfig(BaseModel):
    queue_max_size: int = 10000
    batch_size: int = 100
    batch_interval_seconds: float = 5.0


class HighestStarMaintenanceServiceConfig(BaseModel):
    queue_max_size: int = 5000
    batch_size: int = 50
    batch_interval_seconds: float = 3.0


class ApiConfig(BaseModel):
    """單個API配置"""

    priority: int
    name: str
    endpoint: str
    model: str
    api_key: str  # 直接硬編碼的API金鑰


class AiAssistantConfig(BaseModel):
    # API配置列表
    apis: List[ApiConfig] = []

    class Config:
        extra = "ignore"  # 確保 Pydantic 會忽略 YAML 中未在模型中定義的額外字段


# Pioneer System Pydantic Models
class PioneerRequirement(BaseModel):
    type: str
    skill: Optional[str] = None
    min_level: Optional[int] = None
    currency: Optional[str] = None
    amount: Optional[int] = None
    item_id: Optional[str] = None
    quantity: Optional[int] = None
    facility_type: Optional[str] = None
    min_count: Optional[int] = None
    project_id: Optional[str] = None
    min_era: Optional[int] = None
    min_amount: Optional[int] = None
    total_facility_upgrades: Optional[int] = None


class PioneerActionItem(BaseModel):
    amount: List[int]
    chance: float
    xp: int


class PioneerActionOutput(BaseModel):
    skill: Optional[str] = None
    level_range: Optional[List[int]] = None
    items: Optional[Dict[str, PioneerActionItem]] = None
    type: Optional[str] = None
    upgrade_type: Optional[str] = None
    project_id: Optional[str] = None


class PioneerAction(BaseModel):
    name: str
    type: str
    energy_cost: int
    requirements: List[PioneerRequirement]
    inputs: List[Dict]  # Can be empty
    outputs: List[PioneerActionOutput]


class PioneerEconomyConfig(BaseModel):
    base_production: Dict[str, float] = Field(default_factory=dict)
    offline_limits: Dict[str, Union[int, float]] = Field(default_factory=dict)
    energy_system: Dict[str, int] = Field(default_factory=dict)
    skill_system: Dict[str, Union[int, float]] = Field(default_factory=dict)
    facility_system: Dict[str, Union[int, float]] = Field(default_factory=dict)
    warehouse_system: Dict[str, Union[int, float]] = Field(default_factory=dict)
    research_system: Dict[str, Union[int, float, None]] = Field(default_factory=dict)
    balance_parameters: Dict[str, Union[bool, float]] = Field(default_factory=dict)


class PioneerEraUnlock(BaseModel):
    type: str
    items: List[str]


class PioneerEra(BaseModel):
    era_id: int
    name: str
    description: str
    requirements: List[PioneerRequirement]
    costs: List[PioneerRequirement]
    unlocks: List[PioneerEraUnlock]


class PioneerFacilityUpgrade(BaseModel):
    cost_formula: Optional[str] = None
    effect: Optional[str] = None
    description: str
    cost_oil: Optional[int] = None


class PioneerFacilityIO(BaseModel):
    item_id: Optional[str] = None
    quantity: Optional[int] = None
    slot_type: Optional[str] = None
    capacity: Optional[int] = None
    filter: Optional[str] = None
    currency: Optional[str] = None
    formula: Optional[str] = None


class PioneerFacility(BaseModel):
    name: str
    is_buildable: Optional[bool] = None
    cost_oil: int
    required_items: Optional[List[Dict[str, Union[str, int]]]] = None
    process_type: str
    process_time: int
    inputs: List[PioneerFacilityIO]
    outputs: List[PioneerFacilityIO]
    upgrades: Dict[str, PioneerFacilityUpgrade]


class PioneerItemEffect(BaseModel):
    type: str
    amount: Optional[int] = None
    action: Optional[str] = None
    multiplier: Optional[float] = None
    skill: Optional[str] = None
    facility_type: Optional[str] = None


class PioneerItem(BaseModel):
    name: str
    description: str
    category: str
    tier: int
    base_sell_price: int
    stack_size: int
    effects: List[PioneerItemEffect]


class PioneerRecipeIO(BaseModel):
    item_id: str
    quantity: int
    xp: Optional[int] = None


class PioneerRecipe(BaseModel):
    name: str
    category: str
    skill_required: str
    min_skill_level: int
    craft_time: int
    inputs: List[PioneerRecipeIO]
    outputs: List[PioneerRecipeIO]
    facility_required: Optional[str] = None


class PioneerResearchProject(BaseModel):
    name: str
    description: str
    effect_per_level: float
    cost_formula: str
    max_level: Optional[int]
    currency: str


class PioneerSkill(BaseModel):
    name: str
    description: str


class PioneerStoryGuideStep(BaseModel):
    title: str
    description: str


class PioneerNewbieStory(BaseModel):
    title: str
    background: str
    guide_steps: List[PioneerStoryGuideStep]
    tips: List[str]
    footer: str


class PioneerEraStory(BaseModel):
    name: str
    description: str
    story: str
    goals: List[str]
    next_era_hint: str


class PioneerAchievementStory(BaseModel):
    title: str
    story: str


class PioneerStoryConfig(BaseModel):
    newbie_story: Optional[PioneerNewbieStory] = None
    era_stories: Dict[int, PioneerEraStory] = Field(default_factory=dict)
    achievement_stories: Dict[str, PioneerAchievementStory] = Field(
        default_factory=dict
    )
    progress_hints: Dict[str, Dict[str, str]] = Field(default_factory=dict)


class PioneerTaskObjective(BaseModel):
    type: str
    target: int
    action: Optional[str] = None
    recipe: Optional[str] = None
    facility_type: Optional[str] = None
    upgrade_type: Optional[str] = None


class PioneerTaskReward(BaseModel):
    type: str
    currency: Optional[str] = None
    amount: Optional[int] = None
    skill: Optional[str] = None
    item_id: Optional[str] = None
    quantity: Optional[int] = None


class PioneerTask(BaseModel):
    name: str
    type: str
    description: str
    unlock_era: int
    objectives: List[PioneerTaskObjective]
    rewards: List[PioneerTaskReward]
    reset_time: Optional[str] = None


class PioneerConfig(BaseModel):
    actions: Dict[str, PioneerAction] = Field(default_factory=dict)
    economy: PioneerEconomyConfig = Field(default_factory=PioneerEconomyConfig)
    eras: List[PioneerEra] = Field(default_factory=list)
    facilities: Dict[str, PioneerFacility] = Field(default_factory=dict)
    items: Dict[str, PioneerItem] = Field(default_factory=dict)
    recipes: Dict[str, PioneerRecipe] = Field(default_factory=dict)
    research_projects: Dict[str, PioneerResearchProject] = Field(default_factory=dict)
    skills: Dict[str, PioneerSkill] = Field(default_factory=dict)
    stories: PioneerStoryConfig = Field(default_factory=PioneerStoryConfig)
    tasks: Dict[str, PioneerTask] = Field(default_factory=dict)


class ApplicationConfig(BaseModel):
    """空的配置類，僅作為佔位符"""

    pass


class DatabaseConfig(BaseModel):
    pool_min_size: int = 10
    pool_max_size: int = 100


class DiscordConfig(BaseModel):
    """Discord相關配置"""

    private_channel_id: Optional[int] = None


# 重構後的YAML配置源，支持深度合併
class YamlConfigSettingsSource(PydanticBaseSettingsSource):
    """從一個或多個YAML文件加載配置，並進行深度合併"""

    def __init__(
        self, settings_cls: Type[BaseSettings], yaml_files: Union[str, List[str]]
    ):
        super().__init__(settings_cls)
        self.yaml_files = [yaml_files] if isinstance(yaml_files, str) else yaml_files
        self.yaml_data = self._load_and_merge_yamls()
        logger.info("YAML配置源已初始化，合併了 %s 個文件。", len(self.yaml_files))

    def _deep_merge(self, source, destination):
        """遞歸合併字典"""
        for key, value in source.items():
            if (
                isinstance(value, dict)
                and key in destination
                and isinstance(destination[key], dict)
            ):
                self._deep_merge(value, destination[key])
            else:
                destination[key] = value
        return destination

    def _load_and_merge_yamls(self) -> Dict[str, Any]:
        """加載所有YAML文件或目錄並深度合併"""
        merged_data = {}

        paths_to_process = list(self.yaml_files)

        for path in paths_to_process:
            if os.path.isdir(path):
                # 如果是目錄，遍歷其中的YAML文件
                for filename in sorted(os.listdir(path)):
                    if filename.endswith((".yaml", ".yml")):
                        file_path = os.path.join(path, filename)
                        try:
                            with open(file_path, "r", encoding="utf-8") as f:
                                data = yaml.safe_load(f)
                                key_name = os.path.splitext(filename)[0]
                                if isinstance(data, dict):
                                    if key_name in merged_data and isinstance(
                                        merged_data.get(key_name), dict
                                    ):
                                        self._deep_merge(data, merged_data[key_name])
                                    else:
                                        merged_data = self._deep_merge(
                                            {key_name: data}, merged_data
                                        )
                                elif isinstance(data, list):
                                    # 如果YAML內容是列表，直接賦值
                                    if key_name in merged_data and isinstance(
                                        merged_data.get(key_name), list
                                    ):
                                        merged_data[key_name].extend(data)
                                    else:
                                        merged_data[key_name] = data
                                else:
                                    logger.warning(
                                        "YAML文件 %s 內容不是字典或列表，已忽略。",
                                        file_path,
                                    )
                        except Exception as e:
                            logger.error("YAML文件加載錯誤 %s: %s", file_path, e)
            elif os.path.isfile(path):
                # 如果是文件，直接加載
                try:
                    with open(path, "r", encoding="utf-8") as f:
                        data = yaml.safe_load(f)
                        if isinstance(data, dict):
                            merged_data = self._deep_merge(data, merged_data)
                        else:
                            logger.warning("YAML文件 %s 內容不是字典，已忽略。", path)
                except Exception as e:
                    logger.error("YAML文件加載錯誤 %s: %s", path, e)
            else:
                logger.warning("找不到指定的YAML配置文件或目錄: %s", path)
        return merged_data

    def get_field_value(self, field: Any, field_name: str) -> Tuple[Any, str, bool]:
        """獲取字段值，支持嵌套"""
        field_value = self.yaml_data.get(field_name)
        is_complex = isinstance(field_value, dict)
        return (field_value, field_name, is_complex)

    def prepare_field_value(
        self, field_name: str, field: Any, value: Any, value_is_complex: bool
    ) -> Any:
        return value

    def __call__(self) -> Dict[str, Any]:
        return self.yaml_data


class AppSettings(BaseSettings):
    DISCORD_TOKEN: Optional[str] = None
    AI_API_KEY: Optional[str] = None
    DATABASE_URL: Optional[str] = None
    REDIS_URL: Optional[str] = None
    PG_HOST: str = "localhost"
    PG_PORT: int = 5432
    PG_USER: str = "postgres"
    PG_PASSWORD: str = "postgres"
    GACHA_DB_NAME: str = "gacha_database"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_ENABLED: bool = False
    # Redis Stream 相關配置已移除，現在使用隊列模式
    RPG: bool = True
    application: ApplicationConfig = Field(default_factory=ApplicationConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    discord: DiscordConfig = Field(default_factory=DiscordConfig)
    gacha_stock_integration: GachaStockIntegrationConfig = Field(
        default_factory=GachaStockIntegrationConfig
    )
    market_stats_updater: MarketStatsUpdaterConfig = Field(
        default_factory=MarketStatsUpdaterConfig
    )
    price_update_service: PriceUpdateServiceConfig = Field(
        default_factory=PriceUpdateServiceConfig
    )
    highest_star_maintenance_service: HighestStarMaintenanceServiceConfig = Field(
        default_factory=HighestStarMaintenanceServiceConfig
    )
    ai_assistant: AiAssistantConfig = Field(default_factory=AiAssistantConfig)
    gacha_core_settings: GachaCoreSettings = Field(default_factory=GachaCoreSettings)
    ui_settings: UISettings = Field(default_factory=UISettings)
    gacha_notification_settings: GachaNotificationSettings = Field(
        default_factory=GachaNotificationSettings
    )
    pioneer: PioneerConfig = Field(default_factory=PioneerConfig)

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        """自定義設定源優先級，確保環境變數優先於YAML"""
        logger.info("設置配置源優先級: 環境變數 > .env > 合併後的YAML")

        # 將所有YAML文件路徑放在一個列表中，注意順序，後面的會覆蓋前面的
        pioneer_config_path = os.path.join(project_root, "config", "pioneer")
        yaml_files_to_load = [
            CONFIG_FILE_PATH,
            GACHA_SETTINGS_FILE_PATH,
            UI_SETTINGS_FILE_PATH,
            pioneer_config_path,
        ]

        return (
            init_settings,
            env_settings,
            dotenv_settings,
            # 使用重構後的單一YAML源，它會處理所有文件的加載和合併
            YamlConfigSettingsSource(settings_cls, yaml_files_to_load),
            file_secret_settings,
        )

    model_config = SettingsConfigDict(
        env_file=dotenv_path,
        env_file_encoding="utf-8",
        extra="ignore",
        validate_assignment=True,
    )

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_url(cls, v, info=None):
        """
        組裝數據庫連接URL - 強制使用環境變數

        我們的原則是：數據庫連接信息應該只從環境變數讀取，不應該存在於YAML配置文件中
        這確保了敏感信息的安全性，並允許在不同環境中輕鬆切換數據庫
        """
        # 從環境變數中獲取組件，確保優先使用環境變數
        host = os.getenv("PG_HOST", "localhost")
        port_str = os.getenv("PG_PORT", "5432")
        user = os.getenv("PG_USER", "postgres")
        password = os.getenv("PG_PASSWORD", "postgres")
        db_name = os.getenv("GACHA_DB_NAME", "gacha_database")
        port = int(port_str) if port_str and port_str.isdigit() else 5432

        logger.info(
            "組裝數據庫URL (從環境變數): 主機=%s, 端口=%s, 用戶=%s, 數據庫=%s",
            host,
            port,
            user,
            db_name,
        )

        # 組裝URL - 注意這裡我們直接使用環境變數的值，忽略任何可能從YAML中讀取的值
        return f"postgresql://{user}:{password}@{host}:{port}/{db_name}"

    @field_validator("REDIS_URL", mode="before")
    @classmethod
    def assemble_redis_url(cls, v, info=None):
        """
        組裝Redis URL - 同樣只使用環境變數

        與數據庫連接類似，Redis連接也應該只從環境變數讀取
        """
        # 從環境變數獲取Redis配置
        host = os.getenv("REDIS_HOST", "localhost")
        port = os.getenv("REDIS_PORT", "6379")
        db = os.getenv("REDIS_DB", "0")
        password = os.getenv("REDIS_PASSWORD", "")
        redis_enabled = os.getenv("REDIS_ENABLED", "False").lower() in (
            "true",
            "1",
            "yes",
        )

        # 如果Redis未啟用，返回None
        if not redis_enabled:
            logger.info("Redis未啟用，不生成連接URL")
            return None

        # 組裝URL
        if password:
            url = f"redis://:{password}@{host}:{port}/{db}"
        else:
            url = f"redis://{host}:{port}/{db}"

        logger.info("組裝Redis URL (從環境變數): %s", url)
        return url


@lru_cache(maxsize=None)
def get_settings() -> AppSettings:
    """
    獲取應用程式設定的唯一入口點。
    使用 lru_cache 實現單例模式，確保 AppSettings 只被實例化一次。
    """
    logger.info("--- 正在實例化 AppSettings (這個訊息應該只出現一次) ---")
    try:
        settings_instance = AppSettings()
        logger.info("最終使用的數據庫URL: %s", settings_instance.DATABASE_URL)
        return settings_instance
    except Exception as e:
        logger.critical("無法載入初始設定: %s", str(e), exc_info=True)
        # 在嚴重錯誤時，終止程式是合理的
        sys.exit("無法載入應用程式設定，機器人無法啟動。")


def reload_settings() -> AppSettings:
    """
    重新載入應用程式設定。
    清除 lru_cache 並重新載入所有配置文件。
    """
    logger.info("清除設定快取並重新載入...")
    get_settings.cache_clear()  # 清除 lru_cache 的快取
    return get_settings()


# --- 提供模組級的、方便的訪問函數 ---
# 這些是其他模組應該主要使用的接口


def get_gacha_core_settings() -> GachaCoreSettings:
    """獲取核心Gacha機制的設定。"""
    return get_settings().gacha_core_settings


def get_ui_settings() -> UISettings:
    """獲取UI相關的設定 (表情符號, 顏色等)。"""
    return get_settings().ui_settings


def get_oil_emoji() -> str:
    """獲取油幣的表情符號。"""
    return get_ui_settings().oil_emoji


def get_ticket_emoji() -> str:
    """獲取油票的表情符號。"""
    return get_ui_settings().ticket_emoji


def get_pool_type_names() -> Dict[str, str]:
    """獲取卡池類型的顯示名稱映射。"""
    return get_gacha_core_settings().pool_type_names


def get_rarity_colors_int() -> Dict[str, Dict[int, int]]:
    """獲取稀有度顏色的整數值映射。"""
    return get_ui_settings().rarity_colors_int


def get_completion_indicator_emojis() -> Dict[str, str]:
    """獲取完成度指示器的表情符號。"""
    return get_ui_settings().completion_indicator_emojis


def get_config(config_path: str, default=None):
    """
    通用配置獲取函數，用於替代舊的 config_service.get_config()
    支持點分隔的路徑，例如 'gacha_core_settings.pool_type_prefixes'
    """
    settings = get_settings()

    # 分割路徑
    path_parts = config_path.split(".")
    current = settings

    try:
        for part in path_parts:
            if hasattr(current, part):
                current = getattr(current, part)
            else:
                return default
        return current
    except Exception:
        return default


def get_gacha_stock_integration_config():
    """獲取股票整合相關的設定。"""
    return get_settings().gacha_stock_integration


def get_character_archetype_readable_names() -> Dict[str, str]:
    """獲取角色原型的可讀名稱映射。"""
    return get_ui_settings().character_archetype_readable_names


def get_rarity_images_url() -> Dict[str, Any]:
    """獲取稀有度圖片URL配置。"""
    return get_ui_settings().rarity_images


def get_wish_max_slots(default_value: int = 10) -> int:
    """獲取許願最大槽位數。"""
    return get_gacha_core_settings().wish_max_slots or default_value


def get_wish_slot_costs() -> Dict[int, int]:
    """獲取許願槽位成本配置。"""
    return get_gacha_core_settings().wish_slot_costs


def get_wish_max_power_level(default_value: int = 10) -> int:
    """獲取許願最大力度等級。"""
    return get_gacha_core_settings().wish_max_power_level or default_value


def get_wish_power_costs() -> Dict[int, int]:
    """獲取許願力度成本配置。"""
    return get_gacha_core_settings().wish_power_costs


def get_pool_type_emojis() -> Dict[str, str]:
    """獲取卡池類型表情符號映射。"""
    return get_ui_settings().pool_type_emojis


def get_default_wish_slots(default_value: int = 1) -> int:
    """獲取預設許願槽位數。"""
    return get_gacha_core_settings().default_wish_slots or default_value


def get_default_wish_power_level(default_value: int = 1) -> int:
    """獲取預設許願力度等級。"""
    return get_gacha_core_settings().default_wish_power_level or default_value


def get_default_wish_multiplier(default_value: float = 3.0) -> float:
    """獲取預設許願倍數。"""
    return get_gacha_core_settings().default_wish_multiplier or default_value


def get_wish_power_multipliers() -> Dict[int, float]:
    """獲取許願力度倍數配置。"""
    return get_gacha_core_settings().wish_power_multipliers


def get_pool_specific_rarity_emojis() -> Dict[str, Dict[int, str]]:
    """獲取卡池特定稀有度表情符號配置。"""
    return get_ui_settings().pool_specific_rarity_emojis


def get_default_rarity_emojis() -> Dict[int, str]:
    """獲取預設稀有度表情符號配置。"""
    return get_ui_settings().default_rarity_emojis


def get_pool_specific_encyclopedia_rarity_emojis() -> Dict[str, Dict[int, str]]:
    """獲取卡池特定全圖鑑稀有度表情符號配置。"""
    return get_ui_settings().encyclopedia_rarity_emojis


def get_default_encyclopedia_rarity_emojis() -> Dict[int, str]:
    """獲取預設全圖鑑稀有度表情符號配置。"""
    return get_ui_settings().default_encyclopedia_rarity_emojis


def get_rarity_display_codes() -> Dict[int, str]:
    """獲取稀有度顯示代碼配置。"""
    return get_ui_settings().rarity_display_codes


def get_user_friendly_rarity_display_names() -> Dict[str, str]:
    """獲取用戶友好的稀有度顯示名稱配置。"""
    return get_ui_settings().user_friendly_display_names


def get_star_emojis() -> Dict[int, str]:
    """獲取星級表情符號配置。"""
    return get_ui_settings().star_emojis


def get_ui_button_emojis():
    """獲取UI按鈕表情符號配置。"""
    return get_ui_settings().ui_button_emojis


def get_news_type_colors_int() -> Dict[str, int]:
    """獲取新聞類型顏色配置。"""
    return get_ui_settings().news_type_colors_int


def get_mixed_pool_draw_config():
    """獲取混合池抽卡配置。"""
    return get_gacha_core_settings().mixed_pool_draw_config


def get_all_pool_rarity_configs():
    """獲取所有卡池稀有度配置。"""
    return get_gacha_core_settings().all_pool_rarity_configs


def get_minigame_emojis() -> Dict[str, str]:
    """獲取小遊戲表情符號配置。"""
    return get_ui_settings().minigame_emojis


def get_minigame_emoji(game_type: str, default: str = "❓") -> str:
    """獲取指定小遊戲的表情符號。

    參數:
        game_type: 遊戲類型 (如 'blackjack', 'baccarat' 等)
        default: 找不到時的預設表情符號

    返回:
        str: 表情符號字串
    """
    return get_minigame_emojis().get(game_type, default)


def get_common_ui_colors() -> Dict[str, int]:
    """獲取通用UI顏色配置。"""
    return get_ui_settings().common_ui_colors


def get_form_validation_config() -> Dict[str, int]:
    """獲取表單驗證配置。"""
    return get_ui_settings().form_validation


def get_ui_timeouts() -> Dict[str, int]:
    """獲取UI超時設定。"""
    return get_ui_settings().ui_timeouts


def get_card_settings() -> Dict[str, int]:
    """獲取卡片相關設定。"""
    return get_ui_settings().card_settings


if __name__ == "__main__":
    logger.info("測試新的設定載入機制...")
    try:
        # 首次調用，應該會觸發載入
        loaded_settings = get_settings()
        logger.info("數據庫名稱: %s", loaded_settings.GACHA_DB_NAME)
        logger.info("數據庫URL: %s", loaded_settings.DATABASE_URL)

        # 第二次調用，應該直接返回已載入的實例
        logger.info("第二次獲取設定...")
        same_settings = get_settings()

        if id(loaded_settings) == id(same_settings):
            logger.info("成功：兩次獲取到的是同一個設定實例 (單例模式驗證成功)")
        else:
            logger.error("失敗：兩次獲取到的是不同的設定實例")

        logger.info("透過輔助函數獲取油幣表情符號: %s", get_oil_emoji())

    except Exception as e:
        logger.error("測試過程中發生錯誤: %s", e)
