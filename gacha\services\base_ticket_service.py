import asyncio
import math
from decimal import Decimal
from functools import wraps
from typing import (
    Any,
    Awaitable,
    Callable,
    Dict,
    List,
    Optional,
    Sequence,
    TypeVar,
    Union,
)

# gacha repositories
# database
# gacha config
from config.app_config import get_gacha_core_settings
from database.postgresql.async_manager import get_redis_client

# gacha exceptions
from gacha.exceptions import (
    InsufficientOilTicketsError,
    ShopCardDetailsNotFoundError,
    ShopSystemError,
    TransactionError,
)
from gacha.models.models import Card

# gacha models
from gacha.models.shop_models import ShopItemDefinition
from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import collection_repository

# gacha services
from gacha.services import user_service

# utils
from utils.logger import logger

T = TypeVar("T")


def get_available_oil_tickets(decimal_balance: Optional[Decimal]) -> int:
    """將小數油票餘額轉換為可用整數油票"""
    return math.floor(decimal_balance) if decimal_balance is not None else 0


async def get_user_data_for_exchange(
    user_id: int, connection: Optional[Any] = None
) -> Dict[str, Any]:
    """一次性並行獲取用戶兌換所需的多種數據。"""
    # 根據規範，移除 try-except，讓底層錯誤自然冒泡
    user_balance_task = user_service.get_user_balance(user_id)
    # 確保自動創建用戶並獲取油票餘額
    oil_ticket_balance_task = user_service.get_oil_ticket_balance(
        user_id, create_if_missing=True
    )
    user_balance, oil_ticket_balance = await asyncio.gather(
        user_balance_task, oil_ticket_balance_task
    )
    available_tickets = get_available_oil_tickets(oil_ticket_balance)

    return {
        "user_balance": user_balance,
        "oil_ticket_balance": oil_ticket_balance,
        "available_tickets_for_display": available_tickets,
    }


async def get_ticket_shop_definitions() -> List[ShopItemDefinition]:
    """從設定檔讀取可兌換的券道具定義，並轉換為 ShopItemDefinition 物件列表。"""
    definitions = list(get_gacha_core_settings().ticket_shop_items.values())
    if not definitions:
        raise ShopSystemError("抱歉，油票商店目前沒有商品。")
    return sorted(
        definitions, key=lambda x: x.sort_order if x.sort_order is not None else 0
    )


def with_sufficient_balance(
    func: Callable[..., Awaitable[T]],
) -> Callable[..., Awaitable[T]]:
    """裝飾器：確保用戶有足夠的油票餘額"""

    @wraps(func)
    async def wrapper(
        user_id: int,
        ticket_definition: ShopItemDefinition,
        quantity: int,
        *args,
        **kwargs,
    ) -> T:
        total_oil_ticket_cost = ticket_definition.cost_oil_tickets * quantity
        # 確保自動創建用戶並獲取油票餘額
        current_balance_decimal = await user_service.get_oil_ticket_balance(
            user_id, create_if_missing=True
        )
        current_balance_tickets = get_available_oil_tickets(current_balance_decimal)

        if current_balance_tickets < total_oil_ticket_cost:
            raise InsufficientOilTicketsError(
                required_amount=total_oil_ticket_cost,
                current_balance=current_balance_tickets,
            )

        return await func(user_id, ticket_definition, quantity, *args, **kwargs)

    return wrapper


async def _get_card_ids_from_grant_list(
    cards_to_grant: Sequence[Union[int, Card]],
) -> List[int]:
    """從授予列表中提取卡片ID。"""
    if not cards_to_grant:
        return []

    card_ids: List[int] = []
    for item in cards_to_grant:
        if isinstance(item, int):
            card_ids.append(item)
        elif hasattr(item, "card_id") and isinstance(item.card_id, int):
            card_ids.append(item.card_id)
        else:
            logger.error("無效的 cards_to_grant 項目類型: %s", type(item))
            raise TransactionError("無效的卡片數據類型。")
    return card_ids


async def _fetch_and_map_card_details(
    card_ids: List[int],
) -> Dict[int, Card]:
    """獲取並映射卡片詳細信息。"""
    if not card_ids:
        return {}
    unique_ids = list(set(card_ids))
    details = await master_card_repository.get_cards_details_by_ids(unique_ids)
    if not details:
        logger.error("無法獲取卡片ID的詳細信息: %s", unique_ids)
        raise ShopCardDetailsNotFoundError(
            card_ids=unique_ids, missing_count=len(unique_ids)
        )
    return {card.card_id: card for card in details}


async def execute_exchange_transaction(
    conn: Any,
    user_id: int,
    session_id: str,
    ticket_definition: ShopItemDefinition,
    quantity: int,
    cards_to_grant: Sequence[Union[int, Card]],
) -> Dict[str, Any]:
    """
    在單一資料庫交易中執行完整的票券兌換流程。

    Args:
        conn: 資料庫連線物件。
        user_id: 用戶 ID。
        session_id: 會話 ID。
        ticket_definition: 票券定義。
        quantity: 兌換數量。
        cards_to_grant: 要授予的卡片列表 (可以是 Card 物件或 card_id)。

    Returns:
        一個包含交易結果的字典。
    """
    total_cost = ticket_definition.cost_oil_tickets * quantity
    card_ids_to_add = await _get_card_ids_from_grant_list(cards_to_grant)

    if not card_ids_to_add:
        raise TransactionError("沒有要授予的卡片。")

    try:
        async with conn.transaction():
            # 1. 扣除油票
            await user_service.update_oil_ticket_balance(
                user_id, -Decimal(str(total_cost)), conn
            )
            logger.info(
                "Session %s: User %s spent %s oil tickets.",
                session_id,
                user_id,
                total_cost,
            )

            # 2. 授予卡片
            redis_client = get_redis_client()
            if not redis_client:
                raise TransactionError("Redis client is not available.")
            exchange_result = await collection_repository.bulk_add_cards_and_check_new(
                redis_client, user_id, card_ids_to_add, conn
            )
            logger.info(
                "Session %s: User %s granted %s cards.",
                session_id,
                user_id,
                len(card_ids_to_add),
            )

            # 3. 【新增】在同一事務中更新市場統計
            from gacha.services.direct_market_stats_updater import (
                update_market_stats_for_ticket_exchange_in_transaction,
            )

            # 準備新擁有者卡片ID列表
            new_owner_card_ids = [
                card_id
                for card_id, result in exchange_result.items()
                if result.get("is_new", False)
            ]

            await update_market_stats_for_ticket_exchange_in_transaction(
                conn=conn,
                granted_card_ids=card_ids_to_add,
                new_owner_card_ids=new_owner_card_ids,
            )

            logger.info(
                "Session %s: 票券兌換事務完成 - 用戶: %s, 授予: %s, 新擁有者: %s, 統計更新: 完成",
                session_id,
                user_id,
                len(card_ids_to_add),
                len(new_owner_card_ids),
            )

        # 4. 獲取授予卡片的詳細資訊以供顯示
        granted_card_details = await _fetch_and_map_card_details(card_ids_to_add)

        # 5. 統計更新已在主事務中完成，無需額外處理
        logger.info(
            "Session %s: 統計更新已在主事務中完成，跳過異步更新",
            session_id,
        )

        return {
            "exchange_result": exchange_result,
            "granted_cards_info_for_embed": [
                granted_card_details[cid]
                for cid in card_ids_to_add
                if cid in granted_card_details
            ],
        }

    except Exception as e:
        logger.error(
            "Session %s: Transaction failed for user %s. Error: %s",
            session_id,
            user_id,
            e,
            exc_info=True,
        )
        raise TransactionError(f"兌換過程中發生錯誤: {e}") from e
