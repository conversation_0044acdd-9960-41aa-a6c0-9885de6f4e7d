"""
冷卻管理器 - 防止API垃圾訊息的智能冷卻處理
"""

import time
from collections import defaultdict
from typing import Dict, Set


class CooldownMessageManager:
    """管理冷卻訊息的發送，防止API垃圾訊息"""

    def __init__(self):
        # 追蹤用戶最後收到冷卻訊息的時間
        self.last_cooldown_message: Dict[int, float] = {}

        # 追蹤用戶在短時間內的冷卻觸發次數
        self.cooldown_triggers: Dict[int, list] = defaultdict(list)

        # 被靜默的用戶（觸發太多冷卻）
        self.silenced_users: Set[int] = set()

    def should_send_cooldown_message(self, user_id: int, retry_after: float) -> bool:
        """
        判斷是否應該發送冷卻訊息

        Args:
            user_id: 用戶ID
            retry_after: 冷卻剩餘時間

        Returns:
            bool: 是否應該發送冷卻訊息
        """
        current_time = time.time()

        # 清理過期的觸發記錄（超過30秒的記錄）
        self._cleanup_old_triggers(user_id, current_time)

        # 記錄這次觸發
        self.cooldown_triggers[user_id].append(current_time)

        # 如果用戶被靜默，不發送訊息
        if user_id in self.silenced_users:
            return False

        # 如果冷卻時間很短（小於1秒），不發送訊息
        if retry_after < 1.0:
            return False

        # 檢查用戶是否在短時間內觸發太多冷卻（30秒內超過5次）
        if len(self.cooldown_triggers[user_id]) > 5:
            self.silenced_users.add(user_id)
            # 10分鐘後自動解除靜默
            self._schedule_unsilence(user_id, current_time + 600)
            return False

        # 檢查距離上次冷卻訊息是否太近（5秒內不重複發送）
        last_message_time = self.last_cooldown_message.get(user_id, 0)
        if current_time - last_message_time < 5.0:
            return False

        # 更新最後發送訊息的時間
        self.last_cooldown_message[user_id] = current_time
        return True

    def _cleanup_old_triggers(self, user_id: int, current_time: float):
        """清理30秒前的觸發記錄"""
        if user_id in self.cooldown_triggers:
            self.cooldown_triggers[user_id] = [
                trigger_time
                for trigger_time in self.cooldown_triggers[user_id]
                if current_time - trigger_time < 30.0
            ]

    def _schedule_unsilence(self, user_id: int, unsilence_time: float):
        """安排用戶解除靜默（簡單實作，實際可用asyncio.create_task）"""
        # 這裡可以用更複雜的調度機制，現在先簡單處理
        pass

    def unsilence_user(self, user_id: int):
        """手動解除用戶靜默"""
        self.silenced_users.discard(user_id)
        if user_id in self.cooldown_triggers:
            del self.cooldown_triggers[user_id]
        if user_id in self.last_cooldown_message:
            del self.last_cooldown_message[user_id]

    def is_user_silenced(self, user_id: int) -> bool:
        """檢查用戶是否被靜默"""
        return user_id in self.silenced_users


# 全局實例
cooldown_manager = CooldownMessageManager()
