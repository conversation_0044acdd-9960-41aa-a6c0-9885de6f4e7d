from typing import Optional

import discord


class SuccessEmbed(discord.Embed):
    """
    一個標準化的成功訊息 Embed，自動包含預設的作者和顏色。
    """

    def __init__(
        self,
        *,
        title: Optional[str] = None,
        description: Optional[str] = None,
        author_name: str = "操作成功！",
        **kwargs,
    ):
        super().__init__(
            title=title,
            description=description,
            color=discord.Color.green(),
            **kwargs,
        )
        if author_name:
            self.set_author(
                name=author_name,
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1395549704762429600/yes.png?ex=687ada71&is=687988f1&hm=5055f87add18780bb3678a5b895bbcd016112cc2f2cd4c849ec8ff8ecdee624a&",
            )


class InfoEmbed(discord.Embed):
    """
    一個標準化的資訊訊息 Embed。
    """

    def __init__(
        self,
        *,
        title: Optional[str] = None,
        description: Optional[str] = None,
        author_name: str = "資訊",
        **kwargs,
    ):
        super().__init__(
            title=title,
            description=description,
            color=discord.Color.blue(),
            **kwargs,
        )
        if author_name:
            self.set_author(
                name=author_name,
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1395549704762429600/info.png?ex=687ada71&is=687988f1&hm=1234567890abcdef",  # Placeholder icon
            )


class WarningEmbed(discord.Embed):
    """
    一個標準化的警告訊息 Embed。
    """

    def __init__(
        self,
        *,
        title: Optional[str] = None,
        description: Optional[str] = None,
        author_name: str = "警告",
        **kwargs,
    ):
        super().__init__(
            title=title,
            description=description,
            color=discord.Color.orange(),
            **kwargs,
        )
        if author_name:
            self.set_author(
                name=author_name,
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1395549704762429600/warning.png?ex=687ada71&is=687988f1&hm=abcdef1234567890",  # Placeholder icon
            )


class ErrorEmbed(discord.Embed):
    """
    一個標準化的錯誤訊息 Embed。
    """

    def __init__(
        self,
        *,
        title: Optional[str] = None,
        description: Optional[str] = None,
        author_name: str = "操作失敗",
        **kwargs,
    ):
        super().__init__(
            title=title,
            description=description,
            color=discord.Color.red(),
            **kwargs,
        )
        if author_name:
            self.set_author(
                name=author_name,
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1395549704762429600/no.png?ex=687ada71&is=687988f1&hm=abcdef1234567890",  # Placeholder icon
            )
