-- ========================================
-- 完整RPG系統數據庫遷移腳本
-- 從零開始為gacha系統添加所有RPG功能
-- 版本：最終版 (2024-12-01)
-- ========================================

BEGIN;

-- ========================================
-- 1. 安全備份現有數據
-- ========================================
DO $$
BEGIN
    -- 檢查並備份gacha_user_collections表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gacha_user_collections') THEN
        EXECUTE 'CREATE TABLE gacha_user_collections_backup_' || to_char(CURRENT_DATE, 'YYYYMMDD') || ' AS SELECT * FROM gacha_user_collections';
        RAISE NOTICE '已備份gacha_user_collections表';
    END IF;
END $$;

-- ========================================
-- 2. 為gacha_user_collections添加RPG技能字段
-- ========================================
DO $$
BEGIN
    -- 添加主動技能字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'equipped_active_skill_ids'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN equipped_active_skill_ids JSONB DEFAULT '[]'::jsonb NOT NULL;
        RAISE NOTICE '已添加equipped_active_skill_ids字段';
    END IF;

    -- 添加被動技能字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'equipped_common_passives'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN equipped_common_passives JSONB DEFAULT '{}'::jsonb NOT NULL;
        RAISE NOTICE '已添加equipped_common_passives字段';
    END IF;
END $$;

-- ========================================
-- 3. 創建RPG特殊卡表（獨立卡牌實例）
-- ========================================
CREATE TABLE IF NOT EXISTS rpg_card_special_configs (
    id SERIAL PRIMARY KEY,
    card_id BIGINT NOT NULL UNIQUE,  -- 超大數字：1000000+
    user_id BIGINT NOT NULL,
    
    -- 卡牌基本信息（完全獨立）
    name VARCHAR(255) NOT NULL,
    series VARCHAR(255),
    rarity INTEGER NOT NULL,
    image_url TEXT,
    pool_type VARCHAR(50),
    
    -- RPG字段
    rpg_level INTEGER DEFAULT 1 NOT NULL,
    rpg_xp INTEGER DEFAULT 0 NOT NULL,
    equipped_active_skill_ids JSONB DEFAULT '[]'::jsonb NOT NULL,
    equipped_common_passives JSONB DEFAULT '{}'::jsonb NOT NULL,
    
    -- 其他字段
    star_level INTEGER DEFAULT 0 NOT NULL,
    is_favorite BOOLEAN DEFAULT false NOT NULL,
    quantity INTEGER DEFAULT 1 NOT NULL,  -- 特殊卡固定為1
    
    -- 時間戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 約束
    CONSTRAINT fk_rpg_special_user_id FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT check_rpg_level_positive CHECK (rpg_level >= 1),
    CONSTRAINT check_rpg_xp_non_negative CHECK (rpg_xp >= 0),
    CONSTRAINT check_special_card_id_range CHECK (card_id >= 1000000)
);

-- ========================================
-- 4. 創建全局技能熟練度表
-- ========================================
CREATE TABLE IF NOT EXISTS gacha_user_learned_global_skills (
    user_id BIGINT NOT NULL,
    skill_id VARCHAR(255) NOT NULL,
    skill_type VARCHAR(20) NOT NULL,
    skill_level INTEGER DEFAULT 1 NOT NULL,
    skill_xp INTEGER DEFAULT 0 NOT NULL,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT gacha_user_learned_global_skills_pkey PRIMARY KEY (user_id, skill_id, skill_type),
    CONSTRAINT check_skill_type CHECK (skill_type IN ('ACTIVE', 'PASSIVE')),
    CONSTRAINT check_skill_level_positive CHECK (skill_level >= 1),
    CONSTRAINT check_skill_xp_non_negative CHECK (skill_xp >= 0),
    CONSTRAINT fk_learned_skills_user FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE
);

-- ========================================
-- 5. 創建用戶RPG進度表
-- ========================================
CREATE TABLE IF NOT EXISTS rpg_user_progress (
    user_id BIGINT NOT NULL,
    current_floor_unlocked INTEGER DEFAULT 1 NOT NULL,
    current_floor_wins INTEGER DEFAULT 0 NOT NULL,
    max_floor_cleared INTEGER DEFAULT 0 NOT NULL,
    current_team_formation JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT rpg_user_progress_pkey PRIMARY KEY (user_id),
    CONSTRAINT fk_rpg_progress_user FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT check_floor_unlocked_positive CHECK (current_floor_unlocked >= 1),
    CONSTRAINT check_floor_wins_non_negative CHECK (current_floor_wins >= 0),
    CONSTRAINT check_max_floor_non_negative CHECK (max_floor_cleared >= 0)
);

-- ========================================
-- 6. 創建索引優化查詢性能
-- ========================================

-- gacha_user_collections表的技能索引
CREATE INDEX IF NOT EXISTS idx_gacha_collections_active_skills
ON gacha_user_collections USING GIN (equipped_active_skill_ids);

CREATE INDEX IF NOT EXISTS idx_gacha_collections_passive_skills
ON gacha_user_collections USING GIN (equipped_common_passives);

CREATE INDEX IF NOT EXISTS idx_gacha_collections_user_card
ON gacha_user_collections (user_id, card_id);

-- rpg_card_special_configs表的索引
CREATE INDEX IF NOT EXISTS idx_rpg_special_user_id
ON rpg_card_special_configs (user_id);

CREATE INDEX IF NOT EXISTS idx_rpg_special_card_id
ON rpg_card_special_configs (card_id);

CREATE INDEX IF NOT EXISTS idx_rpg_special_active_skills
ON rpg_card_special_configs USING GIN (equipped_active_skill_ids);

CREATE INDEX IF NOT EXISTS idx_rpg_special_passive_skills
ON rpg_card_special_configs USING GIN (equipped_common_passives);

CREATE INDEX IF NOT EXISTS idx_rpg_special_rarity
ON rpg_card_special_configs (user_id, rarity);

CREATE INDEX IF NOT EXISTS idx_rpg_special_level
ON rpg_card_special_configs (user_id, rpg_level);

-- 全局技能表的索引
CREATE INDEX IF NOT EXISTS idx_learned_global_skills_user_type
ON gacha_user_learned_global_skills (user_id, skill_type);

CREATE INDEX IF NOT EXISTS idx_learned_global_skills_skill_id
ON gacha_user_learned_global_skills (skill_id, skill_type);

-- 用戶進度表的索引
CREATE INDEX IF NOT EXISTS idx_rpg_user_progress_floor
ON rpg_user_progress (current_floor_unlocked, max_floor_cleared);

-- ========================================
-- 7. 創建特殊卡ID生成序列和函數
-- ========================================

-- 創建序列（從1000000開始）
CREATE SEQUENCE IF NOT EXISTS rpg_special_card_id_seq START 1000000;

-- 生成下一個特殊卡ID的函數
CREATE OR REPLACE FUNCTION generate_next_special_card_id()
RETURNS BIGINT AS $$
DECLARE
    next_id BIGINT;
BEGIN
    SELECT COALESCE(MAX(card_id), 999999) + 1 INTO next_id
    FROM rpg_card_special_configs;

    -- 確保ID至少從1000000開始
    IF next_id < 1000000 THEN
        next_id := 1000000;
    END IF;

    RETURN next_id;
END;
$$ LANGUAGE plpgsql;

-- 判斷是否為特殊卡的函數
CREATE OR REPLACE FUNCTION is_special_card(card_id BIGINT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN card_id >= 1000000;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 8. 創建自動更新時間戳的觸發器
-- ========================================

-- 特殊卡表的更新時間戳觸發器
CREATE OR REPLACE FUNCTION update_rpg_special_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_rpg_special_updated_at ON rpg_card_special_configs;
CREATE TRIGGER trigger_rpg_special_updated_at
    BEFORE UPDATE ON rpg_card_special_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_rpg_special_updated_at();

-- 用戶進度表的更新時間戳觸發器
CREATE OR REPLACE FUNCTION update_rpg_user_progress_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_rpg_user_progress_updated_at ON rpg_user_progress;
CREATE TRIGGER trigger_update_rpg_user_progress_updated_at
    BEFORE UPDATE ON rpg_user_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_rpg_user_progress_updated_at();

-- ========================================
-- 9. 創建統一卡牌視圖（默認卡+特殊卡）
-- ========================================
CREATE OR REPLACE VIEW v_user_all_cards AS
SELECT
    guc.card_id,
    guc.user_id,
    guc.quantity,
    1 as rpg_level,  -- 默認卡固定等級1
    0 as rpg_xp,     -- 默認卡固定經驗0
    guc.equipped_active_skill_ids,
    guc.equipped_common_passives,
    guc.star_level,
    guc.is_favorite,
    gmc.name,
    gmc.series,
    gmc.rarity,
    gmc.image_url,
    gmc.pool_type,
    'default' as card_type,
    guc.first_acquired as created_at,
    guc.first_acquired as updated_at
FROM gacha_user_collections guc
JOIN gacha_master_cards gmc ON guc.card_id = gmc.card_id
WHERE guc.quantity > 0

UNION ALL

SELECT
    rsc.card_id,
    rsc.user_id,
    rsc.quantity,
    rsc.rpg_level,  -- 特殊卡的實際等級
    rsc.rpg_xp,     -- 特殊卡的實際經驗
    rsc.equipped_active_skill_ids,
    rsc.equipped_common_passives,
    rsc.star_level,
    rsc.is_favorite,
    rsc.name,
    rsc.series,
    rsc.rarity,
    rsc.image_url,
    rsc.pool_type,
    'special' as card_type,
    rsc.created_at,
    rsc.updated_at
FROM rpg_card_special_configs rsc;


-- ========================================
-- 11. 添加表和字段註釋
-- ========================================

-- gacha_user_collections表的新字段註釋
COMMENT ON COLUMN gacha_user_collections.equipped_active_skill_ids IS '裝備的主動技能ID數組，JSON格式';
COMMENT ON COLUMN gacha_user_collections.equipped_common_passives IS '裝備的被動技能配置對象，JSON格式';

-- rpg_card_special_configs表註釋
COMMENT ON TABLE rpg_card_special_configs IS 'RPG特殊卡配置表，存儲從默認卡分離出來的獨立卡牌實例';
COMMENT ON COLUMN rpg_card_special_configs.card_id IS '特殊卡ID，使用超大數字(1000000+)避免與默認卡衝突';
COMMENT ON COLUMN rpg_card_special_configs.user_id IS '卡牌所屬用戶ID';
COMMENT ON COLUMN rpg_card_special_configs.name IS '卡牌名稱（從原卡複製）';
COMMENT ON COLUMN rpg_card_special_configs.rpg_level IS 'RPG等級，通過戰鬥經驗提升';
COMMENT ON COLUMN rpg_card_special_configs.rpg_xp IS 'RPG經驗值';
COMMENT ON COLUMN rpg_card_special_configs.equipped_active_skill_ids IS '裝備的主動技能ID數組';
COMMENT ON COLUMN rpg_card_special_configs.equipped_common_passives IS '裝備的被動技能配置對象';

-- gacha_user_learned_global_skills表註釋
COMMENT ON TABLE gacha_user_learned_global_skills IS '存儲用戶對各個通用主動技能和通用被動技能的全局熟練度等級';
COMMENT ON COLUMN gacha_user_learned_global_skills.user_id IS '用戶ID，引用gacha_users表';
COMMENT ON COLUMN gacha_user_learned_global_skills.skill_id IS '技能ID，指向active_skills.json或passive_skills.json的鍵';
COMMENT ON COLUMN gacha_user_learned_global_skills.skill_type IS '技能類型：ACTIVE或PASSIVE';
COMMENT ON COLUMN gacha_user_learned_global_skills.skill_level IS '玩家對該技能的全局熟練度等級';
COMMENT ON COLUMN gacha_user_learned_global_skills.skill_xp IS '該全局熟練度等級當前獲得的經驗值';

-- rpg_user_progress表註釋
COMMENT ON TABLE rpg_user_progress IS '存儲用戶在RPG PVE模式下的進度';
COMMENT ON COLUMN rpg_user_progress.user_id IS '用戶ID，引用gacha_users表';
COMMENT ON COLUMN rpg_user_progress.current_floor_unlocked IS '玩家當前已解鎖到的最高樓層';
COMMENT ON COLUMN rpg_user_progress.current_floor_wins IS '玩家在當前已解鎖樓層的勝利次數';
COMMENT ON COLUMN rpg_user_progress.max_floor_cleared IS '玩家歷史上成功通關的最高樓層';
COMMENT ON COLUMN rpg_user_progress.current_team_formation IS '存儲玩家當前PVE的出戰隊伍配置';

-- v_user_all_cards視圖註釋
COMMENT ON VIEW v_user_all_cards IS '統一卡牌視圖，合併默認卡和特殊卡的查詢結果';

-- ========================================
-- 12. 驗證遷移結果
-- ========================================
DO $$
BEGIN
    -- 檢查gacha_user_collections表的新字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'gacha_user_collections'
        AND column_name = 'equipped_active_skill_ids'
    ) THEN
        RAISE EXCEPTION '遷移失敗：gacha_user_collections表缺少equipped_active_skill_ids字段';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'gacha_user_collections'
        AND column_name = 'equipped_common_passives'
    ) THEN
        RAISE EXCEPTION '遷移失敗：gacha_user_collections表缺少equipped_common_passives字段';
    END IF;

    -- 檢查特殊卡表
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'rpg_card_special_configs'
    ) THEN
        RAISE EXCEPTION '遷移失敗：rpg_card_special_configs表未創建';
    END IF;

    -- 檢查全局技能表
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'gacha_user_learned_global_skills'
    ) THEN
        RAISE EXCEPTION '遷移失敗：gacha_user_learned_global_skills表未創建';
    END IF;

    -- 檢查用戶進度表
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'rpg_user_progress'
    ) THEN
        RAISE EXCEPTION '遷移失敗：rpg_user_progress表未創建';
    END IF;

    -- 檢查視圖
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_name = 'v_user_all_cards'
    ) THEN
        RAISE EXCEPTION '遷移失敗：v_user_all_cards視圖未創建';
    END IF;

    RAISE NOTICE '✅ RPG系統數據庫遷移驗證通過！';
END $$;

COMMIT;

-- ========================================
-- 遷移完成報告
-- ========================================
SELECT
    '🎉 RPG系統數據庫遷移成功完成！' as status,
    '默認卡ID範圍: 1-999999' as default_card_range,
    '特殊卡ID範圍: 1000000+' as special_card_range,
    '已添加技能字段到gacha_user_collections表' as gacha_table_update,
    '已創建rpg_card_special_configs表' as special_table_created,
    '已創建gacha_user_learned_global_skills表' as global_skills_table_created,
    '已創建rpg_user_progress表' as progress_table_created,
    '已創建v_user_all_cards統一視圖' as unified_view_created;
