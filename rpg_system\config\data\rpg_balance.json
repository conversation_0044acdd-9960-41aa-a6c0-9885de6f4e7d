{"card_experience": {"battle_victory_base_exp": 50, "battle_victory_level_multiplier": 1.2, "sacrifice_base_exp": 10, "sacrifice_level_multiplier": 5, "sacrifice_star_multiplier": 10, "sacrifice_rarity_multiplier": 3, "max_rpg_level": 50, "rpg_level_exp_formula": {"base_exp": 100, "multiplier": 1.5, "description": "每级所需经验 = base_exp * (level ^ multiplier)"}}, "skill_experience": {"sacrifice_base_exp": 10, "sacrifice_level_multiplier": 5, "sacrifice_star_multiplier": 10, "sacrifice_rarity_multiplier": 3, "skill_rarity_requirements": {"1": {"cards_needed_per_level": 10, "description": "低稀有度技能需要大量卡牌献祭"}, "2": {"cards_needed_per_level": 8, "description": "中等稀有度技能需要较多卡牌献祭"}, "3": {"cards_needed_per_level": 6, "description": "高稀有度技能需要中等数量卡牌献祭"}, "4": {"cards_needed_per_level": 4, "description": "超高稀有度技能需要较少卡牌献祭"}, "5": {"cards_needed_per_level": 2, "description": "极高稀有度技能需要很少卡牌献祭"}}, "skill_level_exp_formula": {"base_exp": 100, "multiplier": 1.2, "rarity_multiplier": {"1": 1.0, "2": 1.5, "3": 2.0, "4": 3.0, "5": 5.0}, "description": "技能升级所需经验 = base_exp * (level ^ multiplier) * rarity_multiplier"}}, "rarity_multipliers": {"1": 1.0, "2": 1.2, "3": 1.5, "4": 2.0, "5": 2.5, "6": 3.0, "7": 3.5, "description": "稀有度对基础属性的倍数影响 (1=C, 2=R, 3=SR, 4=SSR, 5=UR, 6=LR, 7=EX)"}, "base_stats_template": {"max_hp": 100.0, "max_mp": 50.0, "mp_regen_per_turn": 5.0, "atk": 19.0, "def": 13.5, "spd": 10.0, "crit_rate": 0.05, "crit_dmg_multiplier": 1.5, "accuracy": 0.95, "evasion": 0.05, "description": "基础属性模板，会根据稀有度倍数调整。atk为原patk和matk的平均值，def为原pdef和mdef的平均值"}, "card_type_level_up_weights": {"ATTACK": {"atk": 0.4, "def": 0.15, "max_hp": 0.15, "spd": 0.15, "max_mp": 0.075, "mp_regen_per_turn": 0.075, "description": "攻击型升级时的属性权重分配"}, "DEFENSE": {"def": 0.35, "max_hp": 0.3, "atk": 0.15, "spd": 0.075, "max_mp": 0.075, "mp_regen_per_turn": 0.05, "description": "防御型升级时的属性权重分配"}, "SUPPORT": {"max_mp": 0.3, "mp_regen_per_turn": 0.25, "atk": 0.2, "def": 0.15, "max_hp": 0.05, "spd": 0.05, "description": "支援型升级时的属性权重分配"}, "BALANCED": {"atk": 0.2, "def": 0.2, "max_hp": 0.2, "max_mp": 0.15, "mp_regen_per_turn": 0.125, "spd": 0.125, "description": "平衡型升级时的属性权重分配"}}, "growth_stats_percentage": {"max_hp": 0.05, "max_mp": 0.05, "mp_regen_per_turn": 0.05, "atk": 0.05, "def": 0.05, "spd": 0.05, "description": "每级成长值 = 基础属性 * 成长百分比"}, "battle_rewards": {"victory_exp_base": 50, "victory_exp_floor_multiplier": 1.1, "first_clear_bonus_multiplier": 2.0, "team_size_exp_division": true, "description": "战斗胜利后的经验奖励配置"}, "card_level_up": {"stat_distribution": {"randomization_enabled": true, "total_points_guaranteed": true, "variance_percentage": 0.2, "min_stat_percentage": 0.05, "max_stat_percentage": 0.4, "description": "卡牌升级时属性分配的随机性配置"}, "stat_weights": {"max_hp": 0.25, "max_mp": 0.15, "mp_regen_per_turn": 0.05, "atk": 0.25, "def": 0.2, "spd": 0.1, "description": "各属性的基础权重，用于随机分配时的参考"}, "level_up_formula": {"base_exp": 100, "multiplier": 1.5, "description": "卡牌升级所需经验 = base_exp * (level ^ multiplier)"}}}