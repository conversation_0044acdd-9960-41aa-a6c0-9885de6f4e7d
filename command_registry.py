"""
命令註冊器 - 負責統一管理所有Discord命令的註冊流程
將命令註冊與系統初始化分離，明確職責分工
"""

import os
import traceback
from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict, List, Union

from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = Any

# 舊的導入已移除，現在使用統一的輔助功能設置


class CommandRegistry:
    """命令註冊器類 - 負責統一管理所有Discord命令的註冊流程"""

    def __init__(self, bot: "BotType", verbose: bool = True):
        """初始化命令註冊器

        Args:
            bot: Discord機器人實例
            verbose: 是否輸出詳細日誌
        """
        self.bot = bot
        self.verbose = verbose
        self.registration_status = {
            "auxiliary": False,  # 輔助功能 (AI助手、健康檢查、圖像處理、問題回報)
            "gacha": False,  # Gacha 系統註冊狀態
            "rpg": False,  # RPG 系統註冊狀態
            "pioneer": <PERSON>als<PERSON>,  # Pioneer 系統註冊狀態
        }

        # 初始化日誌
        self.log("CommandRegistry 初始化完成")

    def _scan_cogs_in_directory(
        self, directory: str, exclude_files: List[str] | None = None
    ) -> List[str]:
        """掃描指定目錄中的 cog 文件

        Args:
            directory: 要掃描的目錄路徑
            exclude_files: 要排除的文件名列表 (不含 .py)

        Returns:
            List[str]: cog 名稱列表
        """
        cogs = []
        exclude_files = exclude_files or []
        if os.path.exists(directory):
            for file_path in Path(directory).glob("*.py"):
                cog_name = file_path.stem
                if file_path.name != "__init__.py" and cog_name not in exclude_files:
                    cogs.append(cog_name)
        return sorted(cogs)

    def log(self, message: str, level: str = "info") -> None:
        """統一日誌輸出

        Args:
            message: 日誌消息
            level: 日誌級別
        """
        from utils.logger import logger

        if level == "error":
            logger.error(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "success":
            logger.info(message)
        else:
            logger.info(message)

    async def register_all_commands(self, force_reload: bool = False) -> bool:
        """註冊所有命令

        Args:
            force_reload: 是否強制重載（用於開發時重載）

        Returns:
            bool: 是否全部成功
        """
        # 只有在強制重載時才執行清理操作
        if force_reload:
            await self._cleanup_existing_commands()

        overall_success = True
        self.log("\n== 註冊 CommandRegistry 管理的命令 ==")

        # 1. 註冊輔助功能系統命令 (AI助手、健康檢查、圖像處理、問題回報)
        success = await self._register_auxiliary_commands()
        overall_success = overall_success and success

        # 2. 註冊 Gacha 系統命令
        success = await self._register_gacha_commands()
        overall_success = overall_success and success

        # 3. 註冊 RPG 系統命令
        success = await self._register_rpg_commands()
        overall_success = overall_success and success

        # 4. 註冊 Pioneer 系統命令
        success = await self._register_pioneer_commands()
        overall_success = overall_success and success

        # 命令註冊完成提示
        self.log("\n== 命令註冊完成 ==")
        self.log("✅ 所有擴展已載入完成")

        # 同步功能已完全交由 /sync 指令處理，此處不再需要檢查
        # await self._ensure_sync_command()

        return overall_success

    async def _cleanup_existing_commands(self) -> None:
        """清理現有命令和擴展（僅用於重載）"""
        self.log("\n== 清理舊命令和擴展 ==")
        try:
            # 1. 卸載所有已載入的擴展
            loaded_extensions = list(self.bot.extensions.keys())
            if loaded_extensions:
                self.log(f"➤ 準備卸載 {len(loaded_extensions)} 個已載入的擴展...")
                for extension_name in loaded_extensions:
                    try:
                        await self.bot.unload_extension(extension_name)
                        self.log(f"✓ 已卸載擴展: {extension_name}", "success")
                    except Exception as e:
                        self.log(
                            f"⚠️ 卸載擴展 {extension_name} 時發生錯誤: {str(e)}",
                            "warning",
                        )
            else:
                self.log("➤ 沒有偵測到預先載入的擴展。")

            # 2. 清空全局應用程式命令樹
            self.log("➤ 正在清空全局應用程式命令樹...")
            if self.bot.tree is None:
                self.log("❌ CRITICAL: self.bot.tree is None!", "error")
                return

            self.bot.tree.clear_commands(guild=None)
            self.log("✓ 已清空全局應用程式命令樹。", "success")

        except Exception as e:
            self.log(f"❌ 清理舊命令和擴展時發生錯誤: {str(e)}", "error")
            if self.verbose:
                traceback.print_exc()

    async def _register_auxiliary_commands(self) -> bool:
        """註冊輔助功能系統命令 (AI助手、健康檢查、圖像處理、問題回報)

        Returns:
            bool: 是否成功
        """
        try:
            self.log("➤ 註冊輔助功能系統命令...")

            # 導入並調用輔助功能設置函數
            from auxiliary import setup_auxiliary_cogs

            # Type assertion for setup_auxiliary_cogs
            success = await setup_auxiliary_cogs(self.bot)  # type: ignore

            if success:
                self.log("✓ 輔助功能系統命令已註冊", "success")
                self.registration_status["auxiliary"] = True
                return True
            else:
                self.log("❌ 部分輔助功能命令註冊失敗", "error")
                return False

        except Exception as e:
            self.log(f"❌ 註冊輔助功能系統命令時發生錯誤: {str(e)}", "error")
            if self.verbose:
                traceback.print_exc()
            return False

    async def _register_gacha_commands(self) -> bool:
        """註冊 Gacha 系統命令

        Returns:
            bool: 是否成功
        """
        # 檢查 BOT 服務系統是否已初始化
        if not hasattr(self.bot, "is_services_initialized") or not getattr(
            self.bot, "is_services_initialized", False
        ):
            self.log("❌ BOT 服務系統未初始化，無法註冊 Gacha 命令", "error")
            return False

        # 動態掃描 gacha cogs 目錄，並排除 decorators
        gacha_cogs = self._scan_cogs_in_directory(
            "gacha/cogs", exclude_files=["decorators"]
        )

        if not gacha_cogs:
            self.log("⚠️ 沒有找到任何 Gacha Cog 文件", "warning")
            return True  # 返回 True 因為這不是錯誤，只是沒有 cog 需要載入

        self.log(f"📁 找到 {len(gacha_cogs)} 個 Gacha Cog: {', '.join(gacha_cogs)}")

        success = True
        self.log("\n== 註冊 Gacha 系統命令 ==")

        for cog_name in gacha_cogs:
            try:
                self.log(f"➤ 註冊 {cog_name}...")

                # 使用標準的 bot.load_extension 方式載入
                module_path = f"gacha.cogs.{cog_name}"
                try:
                    await self.bot.load_extension(module_path)
                    self.log(f"✓ {cog_name} 已註冊", "success")
                except commands.errors.ExtensionAlreadyLoaded:
                    self.log(f"ℹ️ {cog_name} 已載入，跳過。", "info")

            except Exception as e:
                self.log(f"❌ 處理 {cog_name} 註冊時發生未預期錯誤: {str(e)}", "error")
                if self.verbose:
                    traceback.print_exc()
                success = False

        # 更新 gacha 系統整體註冊狀態
        self.registration_status["gacha"] = success
        return success

    async def _register_rpg_commands(self) -> bool:
        """註冊 RPG 系統命令

        Returns:
            bool: 是否成功
        """
        # 檢查 RPG 系統是否啟用
        import os

        rpg_enabled = os.getenv("RPG", "TRUE").upper() in ("TRUE", "1", "YES")
        if not rpg_enabled:
            self.log("ℹ️ RPG 系統已停用，跳過 RPG 命令註冊", "info")
            return True  # 返回 True 因為這是預期的行為，不是錯誤

        # 檢查 BOT 服務系統是否已初始化（RPG服務通過BOT服務系統管理）
        if not hasattr(self.bot, "is_services_initialized") or not getattr(
            self.bot, "is_services_initialized", False
        ):
            self.log("❌ BOT 服務系統未初始化，無法註冊 RPG 命令", "error")
            return False

        # 動態掃描 RPG cogs 目錄
        rpg_cogs = self._scan_cogs_in_directory("rpg_system/cogs")

        if not rpg_cogs:
            self.log("⚠️ 沒有找到任何 RPG Cog 文件", "warning")
            return True  # 返回 True 因為這不是錯誤，只是沒有 cog 需要載入

        self.log(f"📁 找到 {len(rpg_cogs)} 個 RPG Cog: {', '.join(rpg_cogs)}")

        success = True
        self.log("\n== 註冊 RPG 系統命令 ==")

        for cog_name in rpg_cogs:
            try:
                self.log(f"➤ 註冊 {cog_name}...")

                # 使用標準的 bot.load_extension 方式載入
                module_path = f"rpg_system.cogs.{cog_name}"
                try:
                    await self.bot.load_extension(module_path)
                    self.log(f"✓ {cog_name} 已註冊", "success")
                except commands.errors.ExtensionAlreadyLoaded:
                    self.log(f"ℹ️ {cog_name} 已載入，跳過。", "info")
                except Exception as e:
                    self.log(f"❌ 載入 {module_path} 時發生錯誤: {str(e)}", "error")
                    if self.verbose:
                        traceback.print_exc()
                    success = False

            except Exception as e:
                self.log(f"❌ 處理 {cog_name} 註冊時發生未預期錯誤: {str(e)}", "error")
                if self.verbose:
                    traceback.print_exc()
                success = False

        # 更新 RPG 系統整體註冊狀態
        self.registration_status["rpg"] = success
        return success

    async def _register_pioneer_commands(self) -> bool:
        """註冊 Pioneer 系統命令

        Returns:
            bool: 是否成功
        """
        # 檢查 Pioneer 系統是否啟用
        pioneer_enabled = os.getenv("PIONEER_ENABLED", "FALSE").upper() in (
            "TRUE",
            "1",
            "YES",
        )
        if not pioneer_enabled:
            self.log("ℹ️ Pioneer 系統已停用，跳過 Pioneer 命令註冊", "info")
            return True

        # 檢查 BOT 服務系統是否已初始化（Pioneer服務通過BOT服務系統管理）
        if not hasattr(self.bot, "is_services_initialized") or not getattr(
            self.bot, "is_services_initialized", False
        ):
            self.log("❌ BOT 服務系統未初始化，無法註冊 Pioneer 命令", "error")
            return False

        # 動態掃描 Pioneer cogs 目錄
        pioneer_cogs = self._scan_cogs_in_directory("pioneer/cogs")

        if not pioneer_cogs:
            self.log("⚠️ 沒有找到任何 Pioneer Cog 文件", "warning")
            return True  # 返回 True 因為這不是錯誤，只是沒有 cog 需要載入

        self.log(
            f"📁 找到 {len(pioneer_cogs)} 個 Pioneer Cog: {', '.join(pioneer_cogs)}"
        )

        success = True
        self.log("\n== 註冊 Pioneer 系統命令 ==")

        for cog_name in pioneer_cogs:
            try:
                self.log(f"➤ 註冊 {cog_name}...")

                # 使用標準的 bot.load_extension 方式載入
                module_path = f"pioneer.cogs.{cog_name}"
                try:
                    await self.bot.load_extension(module_path)
                    self.log(f"✓ {cog_name} 已註冊", "success")
                except commands.errors.ExtensionAlreadyLoaded:
                    self.log(f"ℹ️ {cog_name} 已載入，跳過。", "info")
                except Exception as e:
                    self.log(f"❌ 載入 {module_path} 時發生錯誤: {str(e)}", "error")
                    if self.verbose:
                        traceback.print_exc()
                    success = False

            except Exception as e:
                self.log(f"❌ 處理 {cog_name} 註冊時發生未預期錯誤: {str(e)}", "error")
                if self.verbose:
                    traceback.print_exc()
                success = False

        # 更新 Pioneer 系統整體註冊狀態
        self.registration_status["pioneer"] = success
        return success

    # 此函式已不再需要，因為 /sync 的可用性由 dev_tools_cog 的載入來保證
    # async def _ensure_sync_command(self) -> None:
    #     ...

    def get_registration_report(self) -> Dict[str, Any]:
        """獲取命令註冊狀態報告

        Returns:
            Dict[str, Any]: 註冊狀態報告
        """
        return {
            "status": self.registration_status,
            "overall_success": all(self.registration_status.values()),
        }


# 全局命令註冊器實例
_global_command_registry: CommandRegistry | None = None


def get_command_registry(bot: BotType) -> CommandRegistry:
    """獲取命令註冊器實例（單例模式）

    Args:
        bot: Discord機器人實例

    Returns:
        CommandRegistry: 命令註冊器實例
    """
    global _global_command_registry
    # 使用全局單例模式，避免重複創建實例
    if _global_command_registry is None:
        _global_command_registry = CommandRegistry(bot)
    return _global_command_registry
