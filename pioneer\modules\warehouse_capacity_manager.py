"""
倉庫容量管理器
純模組化設計，負責倉庫容量計算和檢查
"""

from typing import Optional

import asyncpg

from pioneer.exceptions import PioneerDatabaseError
from utils.logger import logger


class WarehouseCapacityManager:
    """倉庫容量管理器 - 純模組化設計"""

    def __init__(self, game_data_loader, repository):
        """初始化管理器

        Args:
            game_data_loader: 遊戲數據載入器
            repository: 數據庫存取層
        """
        self.game_data = game_data_loader
        self.repository = repository

    @property
    def _config(self):
        """獲取倉庫系統配置"""
        return self.game_data.economy.warehouse_system

    @property
    def base_capacity(self) -> int:
        """基礎倉庫容量"""
        return self._config.base_capacity

    @property
    def facility_base_capacity(self) -> int:
        """倉庫設施基礎容量"""
        return self._config.facility_base_capacity

    @property
    def level_multiplier(self) -> float:
        """等級容量倍率"""
        return self._config.level_multiplier

    @property
    def upgrade_multiplier(self) -> float:
        """升級容量倍率"""
        return self._config.upgrade_multiplier

    async def calculate_total_capacity(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> int:
        """計算用戶倉庫總容量"""
        try:
            # 基礎容量
            total_capacity = self.base_capacity

            # 獲取用戶的倉庫設施
            warehouse_facilities = await self.repository.get_user_facilities_by_type(
                user_id, "warehouse", connection=connection
            )

            # 計算每個倉庫設施提供的容量
            for facility in warehouse_facilities:
                facility_capacity = self._calculate_facility_capacity(facility)
                total_capacity += facility_capacity

            return total_capacity

        except Exception as e:
            logger.error("計算倉庫容量失敗 user_id=%s: %s", user_id, e)
            return self.base_capacity  # 返回基礎容量作為後備

    def _calculate_facility_capacity(self, facility) -> int:
        """計算單個倉庫設施的容量"""
        # 基礎容量
        capacity = self.facility_base_capacity

        # 應用等級加成
        capacity = int(capacity * (self.level_multiplier**facility.level))

        # 應用升級加成
        if facility.upgrades and facility.upgrades.get("level"):
            level_upgrades = facility.upgrades.get("level", 0)
            if isinstance(level_upgrades, bool) and level_upgrades:
                level_upgrades = 1  # 布爾值轉換為數值
            capacity = int(capacity * (self.upgrade_multiplier**level_upgrades))

        return capacity

    async def get_current_usage(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> int:
        """獲取當前倉庫使用量"""
        return await self.repository.get_warehouse_total_quantity(
            user_id, connection=connection
        )

    async def check_capacity(
        self,
        user_id: int,
        additional_quantity: int,
        connection: Optional[asyncpg.Connection] = None,
    ) -> bool:
        """檢查是否有足夠容量"""
        current_usage, total_capacity = await self._get_usage_and_capacity(
            user_id, connection
        )
        return current_usage + additional_quantity <= total_capacity

    async def get_capacity_info(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> dict:
        """獲取容量信息"""
        current_usage, total_capacity = await self._get_usage_and_capacity(
            user_id, connection
        )

        return {
            "current_usage": current_usage,
            "total_capacity": total_capacity,
            "available_space": total_capacity - current_usage,
            "usage_percentage": (
                (current_usage / total_capacity * 100) if total_capacity > 0 else 0
            ),
        }

    async def _get_usage_and_capacity(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> tuple:
        """獲取使用量和總容量（避免重複查詢）"""
        current_usage = await self.get_current_usage(user_id, connection)
        total_capacity = await self.calculate_total_capacity(user_id, connection)
        return current_usage, total_capacity

    async def validate_add_item(
        self,
        user_id: int,
        item_id: str,
        quantity: int,
        connection: Optional[asyncpg.Connection] = None,
    ) -> None:
        """驗證是否可以添加物品（拋出異常如果不行）

        Args:
            user_id: 用戶ID
            item_id: 物品ID
            quantity: 數量
            connection: 可選的數據庫連接

        Raises:
            PioneerDatabaseError: 如果容量不足
        """
        if not await self.check_capacity(user_id, quantity, connection):
            capacity_info = await self.get_capacity_info(user_id, connection)
            raise PioneerDatabaseError(
                "add_warehouse_item",
                f"倉庫容量不足！當前: {capacity_info['current_usage']:,}/{capacity_info['total_capacity']:,}，"
                f"嘗試添加: {quantity:,}",
                None,
            )


# 全局實例（延遲初始化）
_warehouse_capacity_manager = None


def get_warehouse_capacity_manager():
    """獲取倉庫容量管理器實例"""
    global _warehouse_capacity_manager
    if _warehouse_capacity_manager is None:
        from pioneer import repositories
        from pioneer.core.game_data_loader import game_data

        _warehouse_capacity_manager = WarehouseCapacityManager(game_data, repositories)
    return _warehouse_capacity_manager


# 便利函數
async def calculate_warehouse_capacity(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    return await get_warehouse_capacity_manager().calculate_total_capacity(
        user_id, connection
    )


async def get_warehouse_capacity_info(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> dict:
    return await get_warehouse_capacity_manager().get_capacity_info(user_id, connection)


async def validate_warehouse_capacity(
    user_id: int,
    item_id: str,
    quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    await get_warehouse_capacity_manager().validate_add_item(
        user_id, item_id, quantity, connection
    )
