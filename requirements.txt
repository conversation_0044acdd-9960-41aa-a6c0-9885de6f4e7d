# Core Discord Bot Dependencies
discord.py>=2.5.2  # Updated to latest stable version (March 2025)
asyncpg>=0.30.0  # Updated to latest version
redis[hiredis]>=5.2.0  # Used for caching and hourly rewards system
dill>=0.3.9  # Used for object serialization in cache system

# Configuration and Environment
PyYAML>=6.0.2  # Updated to latest version
python-dotenv>=1.0.1  # Updated to latest version

# HTTP and Web Dependencies
aiohttp>=3.12.12  # Updated to latest version

# Image Processing and Scientific Computing
pillow>=11.0.0  # Updated to latest version
matplotlib>=3.10.0  # Updated to latest version
numpy>=2.3.1  # Required by matplotlib and imageio
imageio>=2.37.0  # Used for GIF processing in image_processing module

# Data Processing and Validation
pydantic>=2.10.3  # Updated to latest v2 version
pydantic-settings>=2.7.0  # Updated to latest version
bleach>=6.1.0  # For HTML sanitization to prevent XSS

# System Monitoring
psutil>=6.1.0  # Updated to latest version
prometheus_client>=0.21.0  # Prometheus metrics collection
discord-ext-prometheus>=0.2.1  # Discord.py Prometheus integration

# Terminal Colors
colorama>=0.4.6  # Latest version

# Time Zone Support
pytz>=2024.2  # Updated to latest version

# Retry Logic for AI Services
tenacity>=8.2.3,<9.0.0  # Pinned for google-genai compatibility

# OpenAI API Client
openai>=1.86.0  # Updated to latest version

# Web Automation
playwright>=1.49.0  # Updated to latest version

# Formula Evaluation Engine (for RPG system)
asteval>=1.0.6  # Used in RPG formula evaluator

# Development and Testing (optional for production)
# pytest>=8.3.4  # Uncomment if running tests
# service-manager>=0.1.41  # Only used in RPG system tests

# Captcha Generation
captcha>=0.5  # Used in gacha system
