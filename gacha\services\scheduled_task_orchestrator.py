import asyncio

from discord.ext import tasks

from auxiliary.services import db_command_usage_service
from config.app_config import get_gacha_stock_integration_config
from gacha.services import (
    anchor_price_service,
    price_update_service,
    stock_lifecycle_service,
    stock_news_service,
    stock_price_update_engine,
)
from utils.logger import logger

# region Task Loops


@tasks.loop(minutes=10)
async def update_stock_prices_loop():
    try:
        await stock_price_update_engine.update_stock_prices()
    except Exception as e:
        logger.error("Error in update_stock_prices_loop: %s", e, exc_info=True)


@update_stock_prices_loop.before_loop
async def before_update_stock_prices_loop():
    gacha_stock_config = get_gacha_stock_integration_config()
    await asyncio.sleep(
        gacha_stock_config.task_initial_delays.update_stock_prices_seconds
    )


@tasks.loop(minutes=1)
async def news_scheduler_manager_loop():
    try:
        await stock_news_service.news_scheduler_manager()
    except Exception as e:
        logger.error("Error in news_scheduler_manager_loop: %s", e, exc_info=True)


@news_scheduler_manager_loop.before_loop
async def before_news_scheduler_manager_loop():
    gacha_stock_config = get_gacha_stock_integration_config()
    await asyncio.sleep(
        gacha_stock_config.task_initial_delays.news_scheduler_manager_seconds
    )


@tasks.loop(hours=24)
async def full_price_recalculation_loop():
    try:
        await price_update_service.schedule_full_price_recalculation()
        logger.info("全卡價格重新計算已成功通過調度器排程。")
    except Exception as e:
        logger.error("全卡價格重新計算循環發生錯誤: %s", e, exc_info=True)


@full_price_recalculation_loop.before_loop
async def before_full_price_recalculation_loop():
    gacha_stock_config = get_gacha_stock_integration_config()
    await asyncio.sleep(
        gacha_stock_config.task_initial_delays.full_price_recalculation_seconds
    )


@tasks.loop(hours=24)
async def calculate_daily_anchor_prices_loop():
    try:
        await anchor_price_service.calculate_daily_anchor_prices()
    except Exception as e:
        logger.error(
            "Error in calculate_daily_anchor_prices_loop (orchestrator level): %s",
            e,
            exc_info=True,
        )


@calculate_daily_anchor_prices_loop.before_loop
async def before_calculate_daily_anchor_prices_loop():
    gacha_stock_config = get_gacha_stock_integration_config()
    await asyncio.sleep(
        gacha_stock_config.task_initial_delays.calculate_daily_anchor_prices_seconds
    )


@tasks.loop(minutes=10)
async def update_command_stats_summary_loop():
    """定期更新指令使用統計摘要表"""
    try:
        service = await db_command_usage_service.get_db_command_usage_service()
        if service:
            await service.update_summary_table()
        else:
            logger.warning("無法獲取 db_command_usage_service 實例，跳過摘要更新。")
    except Exception as e:
        logger.error("更新指令統計摘要表時發生錯誤: %s", e, exc_info=True)


@tasks.loop(minutes=15)
async def check_stock_lifecycle_loop():
    try:
        await stock_lifecycle_service.check_and_update_all_stocks_lifecycle()
        logger.info(
            "ScheduledTaskOrchestrator: Finished check_and_update_all_stocks_lifecycle. Now triggering new company creation check."
        )
        created_count = (
            await stock_lifecycle_service.trigger_new_company_creation_if_needed()
        )
        logger.info(
            "ScheduledTaskOrchestrator: Triggered new company creation check. Created %s new companies.",
            created_count,
        )
    except Exception as e:
        logger.error(
            "Error in check_stock_lifecycle_loop or subsequent new company creation trigger: %s",
            e,
            exc_info=True,
        )


@check_stock_lifecycle_loop.before_loop
async def before_check_stock_lifecycle_loop():
    gacha_stock_config = get_gacha_stock_integration_config()
    delay_seconds = getattr(
        gacha_stock_config.task_initial_delays, "check_stock_lifecycle_seconds", 75
    )
    await asyncio.sleep(delay_seconds)


# endregion

# region Task Management

_all_tasks = [
    update_stock_prices_loop,
    news_scheduler_manager_loop,
    full_price_recalculation_loop,
    calculate_daily_anchor_prices_loop,
    check_stock_lifecycle_loop,
    update_command_stats_summary_loop,
]


def _configure_tasks():
    from config.app_config import get_gacha_stock_integration_config

    gacha_stock_config = get_gacha_stock_integration_config()
    update_stock_prices_loop.change_interval(
        minutes=gacha_stock_config.tasks.update_stock_prices_minutes
    )
    news_scheduler_manager_loop.change_interval(
        minutes=gacha_stock_config.tasks.news_scheduler_manager_minutes
    )
    full_price_recalculation_loop.change_interval(
        hours=gacha_stock_config.tasks.full_price_recalculation_hours
    )
    calculate_daily_anchor_prices_loop.change_interval(
        hours=gacha_stock_config.tasks.calculate_daily_anchor_prices_hours
    )
    calculate_daily_anchor_prices_loop.add_exception_type(Exception)
    check_stock_lifecycle_loop.change_interval(
        minutes=gacha_stock_config.tasks.check_stock_lifecycle_minutes
    )
    check_stock_lifecycle_loop.add_exception_type(Exception)

    # 指令統計摘要更新任務的設定 (如果需要的話)
    # 目前使用固定的10分鐘間隔，如果需要從設定檔讀取，可以在此處添加
    update_command_stats_summary_loop.add_exception_type(Exception)


def _start_all_tasks():
    for task in _all_tasks:
        if not task.is_running():
            task.start()
    logger.info("All scheduled tasks started by Orchestrator.")


def initialize_tasks():
    """Initializes and starts all scheduled tasks."""
    _configure_tasks()
    _start_all_tasks()


def cancel_tasks():
    """Cancels all scheduled tasks."""
    for task in _all_tasks:
        task.cancel()
    logger.info("All scheduled tasks cancelled by Orchestrator.")


# endregion
