"""
RPG配置生成腳本
從現有的卡牌數據生成RPG系統所需的配置文件

重構改進：
- 統一技能生成邏輯，消除重複代碼
- 統一配置獲取方法，避免重複的回退邏輯
- 統一屬性分配邏輯，減少代碼重複
- 統一CPR閾值查找邏輯，消除重複模式
- 統一技能生成協調器，簡化技能分配流程
- 統一屬性生成模板處理，減少重複步驟
- 嚴格遵循DRY原則，提高代碼可維護性

進一步簡化與重構：
- 使用配置驱动替代硬编码的判断逻辑
- 預編譯正則表達式提高性能
- 通用化技能分配邏輯，使用_assign_initial_skills取代多个重复的技能分配方法
- 增强buff类型判断函数，基于配置而非硬编码
- 优化技能兼容性判断逻辑，加入更清晰的日志
- 将技能分类逻辑与配置关联，减少硬编码规则
- 简化属性修改性质分析函数，提高执行效率
"""

import asyncio
import json
import os
import random
import re
import sys
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

# 導入數據庫相關模組
from database.postgresql.async_manager import setup_connections
from utils.logger import logger


class RPGConfigGenerator:
    """RPG配置生成器"""

    def __init__(self):
        """初始化生成器"""
        # 設置正確的輸出目錄路徑
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))
        self.output_dir = os.path.join(project_root, "rpg_system", "config", "data")

        self.db_initialized = False

        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 加載生成配置
        self._load_generation_configs()

        # 預編譯正則表達式以提高性能
        self._number_regex = re.compile(r"-?\d+\.?\d*")

    def _load_generation_configs(self):
        """加載生成配置文件"""
        try:
            # 新路徑：在 scripts/generate 目錄下（當前文件在 scripts 目錄）
            config_path = os.path.join(
                os.path.dirname(__file__), "generate", "generation_config.json"
            )
            if not os.path.exists(config_path):
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

            with open(config_path, "r", encoding="utf-8") as f:
                self.generation_config = json.load(f)
            logger.info("生成配置加載成功: %s", config_path)
        except Exception as e:
            logger.error("加載生成配置失敗: %s", e)
            raise Exception("generation_config.json 文件加載失敗") from e

    async def _initialize_database(self):
        """初始化數據庫連接"""
        if self.db_initialized:
            return

        try:
            # 初始化數據庫連接池
            await setup_connections()

            # 數據庫連接池已初始化，可以直接使用 master_card_repository 模組函數
            # 不需要創建實例，因為 master_card_repository 是模組級函數

            self.db_initialized = True
            logger.info("數據庫連接初始化成功")

        except Exception as e:
            logger.error("數據庫連接初始化失敗: %s", e)
            raise

    async def _get_cards_from_database(
        self, cards_per_rarity: int = 3
    ) -> List[Dict[str, Any]]:
        """從數據庫獲取卡牌數據（每個稀有度各取N張卡牌）"""
        await self._initialize_database()

        try:
            # 每個稀有度各取N張卡牌，確保測試覆蓋所有稀有度
            query = """
                WITH ranked_cards AS (
                    SELECT
                        card_id,
                        name,
                        series,
                        rarity,
                        pool_type,
                        description,
                        image_url,
                        ROW_NUMBER() OVER (PARTITION BY rarity ORDER BY card_id) as rn
                    FROM gacha_master_cards
                )
                SELECT
                    card_id,
                    name,
                    series,
                    rarity,
                    pool_type,
                    description,
                    image_url
                FROM ranked_cards
                WHERE rn <= $1
                ORDER BY rarity, card_id
            """

            if not self.db_initialized:
                raise ConnectionError("Database not initialized")

            # 使用 master_card_repository 模組函數獲取卡牌數據
            from gacha.repositories._base_repo import fetch_all

            rows = await fetch_all(query, (cards_per_rarity,))

            cards = []
            for row in rows:
                # 計算CPR（綜合強度評級）
                cpr = self._calculate_cpr(row["rarity"], row["pool_type"])

                # 根據CPR和pool_type推測卡牌類型
                card_type = self._infer_card_type_from_cpr_and_pool(
                    row["name"], row["series"], cpr, row["pool_type"]
                )

                cards.append(
                    {
                        "card_id": row["card_id"],
                        "name": row["name"],
                        "series": row["series"],
                        "rarity": row["rarity"],  # 保持原始數字稀有度
                        "rarity_string": self._convert_rarity_to_string(row["rarity"]),
                        "pool_type": row["pool_type"],
                        "description": row["description"] or f"{row['name']}的RPG配置",
                        "image_url": row["image_url"],
                        "cpr": cpr,
                        "card_type": card_type,
                    }
                )

            logger.info("從數據庫獲取了 %s 張卡牌", len(cards))
            return cards

        except Exception as e:
            logger.error("從數據庫獲取卡牌失敗: %s", e)
            raise

    def _calculate_cpr(self, rarity: int, pool_type: str) -> int:
        """
        計算綜合強度評級（CPR）
        根據文檔設計：解決卡牌名義稀有度與實際獲取難度不符的問題

        Args:
            rarity: 名義稀有度（1-7）
            pool_type: 卡池類型

        Returns:
            CPR值（1-100）
        """
        # 從配置獲取基礎CPR映射
        base_cpr_map = self._get_config_value(
            "cpr_calculation", "base_cpr_by_rarity", default={}
        )
        base_cpr = int((base_cpr_map or {}).get(str(rarity), 10))

        # 從配置獲取卡池修飾符
        pool_modifiers = self._get_config_value(
            "cpr_calculation", "pool_type_modifiers", default={}
        )
        modifier = (pool_modifiers or {}).get(pool_type, 1.0)

        final_cpr = min(100, int(base_cpr * modifier))
        return final_cpr

    def _get_stat_budget_template(self, cpr: int) -> Dict[str, Any]:
        """
        根據CPR獲取屬性預算模板

        Args:
            cpr: 綜合強度評級

        Returns:
            預算模板字典
        """
        if cpr <= 20:
            # 低級模板（C-R級）
            return {
                "base_stats_total_range": (80, 120),
                "growth_stats_total_range": (4, 8),
                "passive_skill_slots_range": (0, 1),
                "skill_point_budget": 10,
                "stat_distribution_type": "balanced",
            }
        elif cpr <= 40:
            # 中低級模板（SR級）
            return {
                "base_stats_total_range": (120, 180),
                "growth_stats_total_range": (8, 12),
                "passive_skill_slots_range": (1, 2),
                "skill_point_budget": 20,
                "stat_distribution_type": "specialized",
            }
        elif cpr <= 60:
            # 中級模板（SSR級）
            return {
                "base_stats_total_range": (180, 250),
                "growth_stats_total_range": (12, 18),
                "passive_skill_slots_range": (2, 3),
                "skill_point_budget": 35,
                "stat_distribution_type": "specialized",
            }
        elif cpr <= 80:
            # 高級模板（UR級）
            return {
                "base_stats_total_range": (250, 320),
                "growth_stats_total_range": (18, 25),
                "passive_skill_slots_range": (3, 4),
                "skill_point_budget": 50,
                "stat_distribution_type": "highly_specialized",
            }
        else:
            # 頂級模板（LR-EX級）
            return {
                "base_stats_total_range": (320, 400),
                "growth_stats_total_range": (25, 35),
                "passive_skill_slots_range": (4, 5),
                "skill_point_budget": 75,
                "stat_distribution_type": "highly_specialized",
            }

    def _infer_card_type_from_cpr_and_pool(
        self, name: str, series: str, cpr: int, pool_type: str
    ) -> str:
        """
        根據CPR和卡池類型隨機分配卡牌類型

        Args:
            name: 卡牌名稱（用作隨機種子）
            series: 卡牌系列（暫未使用，保留用於未來擴展）
            cpr: 綜合強度評級
            pool_type: 卡池類型

        Returns:
            卡牌類型
        """
        # 使用卡牌名稱作為種子，確保一致性
        self._set_deterministic_seed(name, str(series))

        # 根據卡池類型和CPR調整類型分配權重
        type_weights = self._get_card_type_weights(cpr, pool_type)

        # 根據權重隨機選擇類型
        types = list(type_weights.keys())
        weights = list(type_weights.values())

        return random.choices(types, weights=weights)[0]

    def _get_card_type_weights(self, cpr: int, pool_type: str) -> Dict[str, float]:
        """
        根據CPR和卡池類型獲取卡牌類型分配權重

        Args:
            cpr: 綜合強度評級
            pool_type: 卡池類型

        Returns:
            類型權重字典
        """
        # 获取基础权重
        base_weights = self._get_config_value(
            "card_type_weights", "base_weights", default={}
        )

        # 基于CPR获取调整后的权重
        weights = (base_weights or {}).copy()

        # 从CPR调整配置中查找适合的调整
        cpr_adjustments = self._get_config_value(
            "card_type_weights", "cpr_adjustments", default={}
        )
        for _, adjustment in (cpr_adjustments or {}).items():
            if "min_cpr" in adjustment and "max_cpr" in adjustment:
                min_cpr = adjustment.get("min_cpr", 0)
                max_cpr = adjustment.get("max_cpr", 100)
                if min_cpr <= cpr <= max_cpr:
                    adjustment_weights = adjustment.get("weights", {})
                    if adjustment_weights:
                        weights = adjustment_weights
                        break

        # 应用卡池修正
        pool_modifiers_dict = self._get_config_value(
            "card_type_weights", "pool_type_modifiers", default={}
        )
        pool_modifiers = (pool_modifiers_dict or {}).get(
            pool_type, (pool_modifiers_dict or {}).get("main", {})
        )

        # 生成最终权重
        final_weights = {}
        for card_type in weights:
            modifier = pool_modifiers.get(card_type, 1.0)
            final_weights[card_type] = weights[card_type] * modifier

        return final_weights

    def _get_stat_distribution_weights(self, card_type: str) -> Dict[str, float]:
        """
        根據卡牌類型獲取屬性分配權重

        Args:
            card_type: 卡牌類型

        Returns:
            屬性權重字典
        """
        # 使用統一的屬性權重
        weights_map = {
            "ATTACK": {
                "atk": 0.4,
                "def": 0.15,
                "max_hp": 0.15,
                "spd": 0.15,
                "max_mp": 0.075,
                "mp_regen_per_turn": 0.075,
            },
            "DEFENSE": {
                "def": 0.35,
                "max_hp": 0.3,
                "atk": 0.15,
                "spd": 0.075,
                "max_mp": 0.075,
                "mp_regen_per_turn": 0.05,
            },
            "SUPPORT": {
                "max_mp": 0.3,
                "mp_regen_per_turn": 0.25,
                "atk": 0.2,
                "def": 0.15,
                "max_hp": 0.05,
                "spd": 0.05,
            },
            "BALANCED": {
                "atk": 0.2,
                "def": 0.2,
                "max_hp": 0.2,
                "max_mp": 0.15,
                "mp_regen_per_turn": 0.125,
                "spd": 0.125,
            },
        }

        return weights_map.get(card_type, weights_map["BALANCED"])

    def _generate_stats_from_template(
        self,
        template: Dict[str, Any],
        card_type: str,
        range_key: str,
        min_value: float,
        is_growth: bool,
    ) -> Dict[str, float]:
        """
        統一的屬性生成方法

        Args:
            template: 預算模板
            card_type: 卡牌類型
            range_key: 範圍配置鍵名
            min_value: 最小值
            is_growth: 是否為成長屬性

        Returns:
            屬性字典
        """
        # 獲取總點數範圍
        min_total, max_total = template[range_key]
        total_points = random.randint(min_total, max_total)

        # 根據卡牌類型獲取屬性分配權重
        weights = self._get_stat_distribution_weights(card_type)

        return self._allocate_stats_by_weight(
            total_points, weights, min_value=min_value, is_growth=is_growth
        )

    def _generate_base_stats_from_template(
        self, template: Dict[str, Any], card_type: str
    ) -> Dict[str, float]:
        """根據預算模板和卡牌類型生成基礎屬性"""
        return self._generate_stats_from_template(
            template,
            card_type,
            "base_stats_total_range",
            min_value=1.0,
            is_growth=False,
        )

    def _generate_growth_stats_from_template(
        self, template: Dict[str, Any], card_type: str
    ) -> Dict[str, float]:
        """根據預算模板和卡牌類型生成成長屬性"""
        return self._generate_stats_from_template(
            template,
            card_type,
            "growth_stats_total_range",
            min_value=0.1,
            is_growth=True,
        )

    def _generate_star_effects_key(self, card: Dict[str, Any]) -> str:
        """
        生成星級效果鍵

        Args:
            card: 卡牌數據

        Returns:
            星級效果鍵
        """
        rarity_string = self._convert_rarity_to_string(card["rarity"]).lower()
        return f"star_effects_{rarity_string}"

    def _get_cpr_threshold_value(
        self, cpr: int, config_path: List[str], value_key: str, default_value
    ):
        """
        統一的CPR閾值配置查找方法

        Args:
            cpr: 綜合強度評級
            config_path: 配置路徑列表
            value_key: 要獲取的值的鍵名
            default_value: 默認值

        Returns:
            配置值或默認值
        """
        thresholds = self._get_config_value(*config_path, "cpr_thresholds", default=[])

        for threshold in thresholds or []:
            if threshold["min_cpr"] <= cpr <= threshold["max_cpr"]:
                return threshold[value_key]

        return default_value

    def _get_innate_skill_chance(self, cpr: int) -> float:
        """根據CPR獲取天賦技能獲得機率"""
        return self._get_cpr_threshold_value(
            cpr, ["skill_generation", "innate_skill_chances"], "chance", 0.05
        )

    def _calculate_passive_skill_slots(self, cpr: int) -> int:
        """根據CPR計算被動技能槽數量"""
        return self._get_cpr_threshold_value(
            cpr, ["passive_slot_generation"], "slots", 0
        )

    def _get_max_preinstalled_skills(self, cpr: int) -> int:
        """根據CPR獲取最大預裝技能數量"""
        return self._get_cpr_threshold_value(
            cpr,
            ["skill_generation", "preinstalled_skill_thresholds"],
            "max_preinstalled",
            0,
        )

    def _assign_initial_skills(
        self,
        card: Dict[str, Any],
        skill_type: str,
        max_slots: int,
        preinstall_count: int,
        seed_suffix: str,
        is_dict_output: bool = False,
    ) -> Union[List[Optional[str]], Dict[str, Optional[str]]]:
        """
        通用技能分配方法，用于分配初始技能到指定槽位

        Args:
            card: 卡片数据
            skill_type: 技能类型 ("active", "passive", "innate")
            max_slots: 最大槽位数量
            preinstall_count: 要预安装的技能数量
            seed_suffix: 随机种子后缀
            is_dict_output: 是否以字典形式输出（key为"slot_X"）

        Returns:
            根据is_dict_output返回列表或字典，包含槽位映射的技能ID
        """
        # 设置随机种子
        self._set_deterministic_seed(card["name"], seed_suffix)

        # 初始化结果容器
        result: Union[Dict[str, Optional[str]], List[Optional[str]]]
        if is_dict_output:
            result = {f"slot_{i}": None for i in range(max_slots)}  # type: ignore
        else:
            result = [None] * max_slots  # type: ignore

        # 如果没有预安装数量，直接返回空槽位
        if preinstall_count <= 0:
            return result

        # 实际预安装数量（不超过最大槽位）
        actual_preinstall = min(preinstall_count, max_slots)
        used_skills = []

        # 为前actual_preinstall个槽位分配技能
        for slot_idx in range(actual_preinstall):
            skill_id = None

            # 第一个技能保证与卡牌稀有度匹配
            if slot_idx == 0:
                skill_id = self._get_guaranteed_skill_by_rarity(
                    card["rarity"], skill_type, card["card_type"], exclude=used_skills
                )
            else:
                # 其余技能随机分配1~card_rarity稀有度的技能
                skill_id = self._get_random_skill_up_to_rarity(
                    card["rarity"], skill_type, card["card_type"], exclude=used_skills
                )

            # 如果找到了技能，添加到已使用列表
            if skill_id:
                used_skills.append(skill_id)

                # 根据输出类型添加到结果
                if is_dict_output:
                    result[f"slot_{slot_idx}"] = skill_id  # type: ignore
                else:
                    result[slot_idx] = skill_id  # type: ignore

        return result

    def _set_deterministic_seed(self, card_name: str, suffix: str = ""):
        """設置確定性隨機種子（確保一致性）"""
        random.seed(hash(card_name + suffix))

    def _get_config_value(self, *keys, default=None):
        """
        統一的配置值獲取方法

        Args:
            *keys: 配置鍵路徑
            default: 默認值

        Returns:
            配置值
        """
        config = self.generation_config
        for key in keys:
            if isinstance(config, dict) and key in config:
                config = config[key]
            else:
                return default
        return config

    def _get_preinstalled_skill_counts_by_rarity(self, rarity: int) -> Dict[str, int]:
        """根據稀有度獲取固定的預裝技能數量"""
        # 尝试从配置中获取
        skill_counts = self._get_config_value(
            "skill_generation",
            "preinstalled_skill_counts_by_rarity",
            str(rarity),
            default=None,
        )
        if skill_counts:
            return skill_counts

        # 如果配置中没有，使用默认值
        rarity_skill_counts = {
            1: {"active": 1, "passive": 1, "innate": 0},  # C级：1主动 + 1被动
            2: {"active": 1, "passive": 1, "innate": 0},  # R级：1主动 + 1被动
            3: {"active": 2, "passive": 2, "innate": 0},  # SR级：2主动 + 2被动
            4: {"active": 2, "passive": 2, "innate": 1},  # SSR级：2主动 + 2被动 + 1天赋
            5: {"active": 3, "passive": 3, "innate": 1},  # UR级：3主动 + 3被动 + 1天赋
            6: {"active": 3, "passive": 4, "innate": 1},  # LR级：3主动 + 4被动 + 1天赋
            7: {"active": 3, "passive": 4, "innate": 1},  # EX级：3主动 + 4被动 + 1天赋
        }

        return rarity_skill_counts.get(rarity, {"active": 1, "passive": 1, "innate": 0})

    def _load_skill_files(self) -> Dict[str, Dict[str, Any]]:
        """
        加載所有技能JSON文件、效果模板文件和狀態效果文件。
        並按稀有度分類技能，同時提供一個技能ID到完整配置的映射。

        Returns:
            一個字典，包含:
            - 'active', 'passive', 'innate': 各自按稀有度分類的技能ID列表。
            - 'all_skill_configs': 一個從 skill_id 到其完整技能配置的字典。
            - 'effect_templates': effect_templates.json 的內容。
            - 'status_effects': status_effects.json 的內容。
        """
        skill_files = {
            "active": "active_skills.json",
            "passive": "passive_skills.json",
            "innate": "innate_passive_skills.json",
        }

        # 新增：狀態效果文件名（支持新的分類結構）
        definition_files = {
            "status_effects": "status_effects"  # 現在是資料夾而不是單一文件
        }

        # 初始化返回結構
        skill_data = {
            "active": {},
            "passive": {},
            "innate": {},
            "all_skill_configs": {},  # 存儲所有技能ID到其配置的映射
            "effect_templates": {},  # 新增
            "status_effects": {},  # 新增
        }

        # 加載技能文件 (active, passive, innate)
        for skill_type, filename in skill_files.items():
            try:
                file_path = os.path.join(
                    os.path.dirname(__file__), "..", "config", "data", filename
                )
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as f:
                        skills_from_file = json.load(f)

                    rarity_mapping = {}
                    for skill_id, skill_config_item in skills_from_file.items():
                        skill_data["all_skill_configs"][skill_id] = skill_config_item

                        rarity = skill_config_item.get("skill_rarity", 1)
                        rarity_str = str(rarity)
                        if rarity_str not in rarity_mapping:
                            rarity_mapping[rarity_str] = []
                        rarity_mapping[rarity_str].append(skill_id)

                    skill_data[skill_type] = rarity_mapping
                    logger.info(
                        "加載 %s 技能文件成功: %s 個技能",
                        skill_type,
                        len(skills_from_file),
                    )
                else:
                    logger.warning("技能文件不存在: %s", filename)

            except Exception as e:
                logger.error("加載技能文件 %s 失敗: %s", filename, e)

        # 加載效果模板和狀態效果文件
        for key, filename in definition_files.items():
            try:
                if key == "status_effects":
                    # 動態讀取狀態效果目錄下所有 JSON 文件
                    status_effects = {}
                    status_effects_dir = os.path.join(
                        os.path.dirname(__file__), "..", "config", "data", filename
                    )

                    if not os.path.exists(status_effects_dir):
                        logger.error("狀態效果目錄不存在: %s", status_effects_dir)
                        raise FileNotFoundError(
                            f"狀態效果目錄不存在: {status_effects_dir}"
                        )

                    # 動態讀取目錄下所有 JSON 文件
                    json_files = [
                        f for f in os.listdir(status_effects_dir) if f.endswith(".json")
                    ]

                    if not json_files:
                        logger.error(
                            "狀態效果目錄中沒有找到任何 JSON 文件: %s",
                            status_effects_dir,
                        )
                        raise FileNotFoundError(
                            f"狀態效果目錄中沒有找到任何 JSON 文件: {status_effects_dir}"
                        )

                    for status_file in json_files:
                        status_path = os.path.join(status_effects_dir, status_file)
                        try:
                            with open(status_path, "r", encoding="utf-8") as f:
                                status_data = json.load(f)
                            status_effects.update(status_data)
                            logger.info("成功載入狀態效果文件: %s", status_file)
                        except Exception as e:
                            logger.error(
                                "載入狀態效果文件失敗: %s, 錯誤: %s", status_file, e
                            )
                            raise

                    logger.info(
                        "總共載入了 %s 個狀態效果，來自 %s 個文件",
                        len(status_effects),
                        len(json_files),
                    )
                    skill_data[key] = status_effects
                else:
                    # 其他定義文件的原有邏輯
                    file_path = os.path.join(
                        os.path.dirname(__file__), "..", "config", "data", filename
                    )
                    if os.path.exists(file_path):
                        with open(file_path, "r", encoding="utf-8") as f:
                            skill_data[key] = json.load(f)
                        logger.info("加載 %s 文件成功: %s", key, filename)
                    else:
                        logger.warning("定義文件不存在: %s", filename)
                        skill_data[key] = {}  # 如果文件不存在，確保有一個空字典

            except Exception as e:
                logger.error("加載定義文件 %s 失敗: %s", filename, e)
                skill_data[key] = {}  # 出錯時也確保有一個空字典

        # 加載效果模板（從多個文件合併）
        skill_data["effect_templates"] = self._load_effect_templates_from_directory()

        return skill_data

    def _load_effect_templates_from_directory(self) -> Dict[str, Any]:
        """從效果模板目錄動態載入所有 JSON 文件"""
        effect_templates = {}
        templates_dir = os.path.join(
            os.path.dirname(__file__), "..", "config", "data", "effect_templates"
        )

        if not os.path.exists(templates_dir):
            logger.error("效果模板目錄不存在: %s", templates_dir)
            raise FileNotFoundError(f"效果模板目錄不存在: {templates_dir}")

        # 動態讀取目錄下所有 JSON 文件
        json_files = [f for f in os.listdir(templates_dir) if f.endswith(".json")]

        if not json_files:
            logger.error("效果模板目錄中沒有找到任何 JSON 文件: %s", templates_dir)
            raise FileNotFoundError(
                f"效果模板目錄中沒有找到任何 JSON 文件: {templates_dir}"
            )

        for template_file in json_files:
            template_path = os.path.join(templates_dir, template_file)
            try:
                with open(template_path, "r", encoding="utf-8") as f:
                    template_data = json.load(f)
                effect_templates.update(template_data)
                logger.info("成功載入效果模板文件: %s", template_file)
            except Exception as e:
                logger.error("載入效果模板文件失敗: %s, 錯誤: %s", template_file, e)
                raise

        logger.info(
            "總共載入了 %s 個效果模板，來自 %s 個文件",
            len(effect_templates),
            len(json_files),
        )
        return effect_templates

    def _get_skill_rarity_mapping(self, skill_type: str) -> Dict[str, List[str]]:
        """
        獲取技能稀有度映射（完全基於JSON文件的稀有度）

        Args:
            skill_type: 技能類型（"active", "passive", "innate"）

        Returns:
            技能稀有度映射字典
        """
        if not hasattr(self, "_skill_data"):
            self._skill_data = self._load_skill_files()

        return self._skill_data.get(skill_type, {})

    def _infer_damage_type_from_skill_config(self, skill_config: Dict[str, Any]) -> str:
        """
        從技能配置推斷傷害類型，主要依賴 effect_templates.json。

        Args:
            skill_config: 技能的配置字典。

        Returns:
            推斷出的傷害類型 ("DAMAGE", "TRUE_DAMAGE", "NEUTRAL", "UNKNOWN")。
        """
        if not skill_config:
            return "UNKNOWN"

        effect_definitions = skill_config.get("base_effect_definitions")
        if (
            not effect_definitions
            or not isinstance(effect_definitions, list)
            or not effect_definitions[0]
        ):
            # 如果沒有 base_effect_definitions，可以考慮檢查技能本身的 tags 或 id (作為後備)
            # 但優先級應低於 effect_template
            skill_id_upper = skill_config.get("id", "").upper()
            if "DAMAGE" in skill_id_upper or "_ATK" in skill_id_upper:
                return "DAMAGE"
            tags_upper = [str(tag).upper() for tag in skill_config.get("tags", [])]
            if "DAMAGE" in tags_upper:
                return "DAMAGE"
            return "UNKNOWN"

        main_effect_def = effect_definitions[0]
        effect_template_id = main_effect_def.get("effect_template")

        if not effect_template_id or not isinstance(effect_template_id, str):
            logger.debug(
                "Skill %s: effect_template ID missing or invalid. Cannot determine damage type from template.",
                skill_config.get("id"),
            )
            return "UNKNOWN"

        # 確保 effect_templates 已加載
        if (
            not hasattr(self, "_skill_data")
            or "effect_templates" not in self._skill_data
        ):
            logger.error(
                "effect_templates not found in _skill_data. Cannot infer damage type."
            )
            # 嘗試加載 (儘管正常流程中應該已加載)
            self._skill_data = self._load_skill_files()
            if "effect_templates" not in self._skill_data:
                logger.error(
                    "Failed to load effect_templates. Returning UNKNOWN for damage type."
                )
                return "UNKNOWN"

        effect_template_config = self._skill_data["effect_templates"].get(
            effect_template_id
        )

        if not effect_template_config:
            logger.warning(
                "Skill %s: effect_template_id '%s' not found in effect_templates.json.",
                skill_config.get("id"),
                effect_template_id,
            )
            # 如果模板ID未找到，可以嘗試基於ID本身推斷，但不推薦作為主要邏輯
            if "DAMAGE" in effect_template_id.upper():
                return "DAMAGE"
            return "UNKNOWN"

        # 1. 直接從效果模板獲取 damage_type
        if "damage_type" in effect_template_config:
            dt = effect_template_config["damage_type"]
            # 標準化常見的 true damage 命名
            if dt and dt.upper() in ["TRUE", "PURE", "TRUE_DAMAGE", "PURE_DAMAGE"]:
                return "TRUE_DAMAGE"
            return dt if dt else "UNKNOWN"  # 如果 damage_type 為空或 None

        # 2. 如果 effect_type 是 DAMAGE 但沒有 damage_type，則可能是配置不完整
        if effect_template_config.get("effect_type") == "DAMAGE":
            logger.warning(
                "Skill %s, template '%s': effect_type is DAMAGE but no explicit damage_type defined. Returning NEUTRAL as fallback.",
                skill_config.get("id"),
                effect_template_id,
            )
            return "NEUTRAL"  # 或者 "UNKNOWN"

        # 3. 處理 APPLY_STATUS_EFFECT，檢查其 tick damage
        # (這部分可以根據需要進一步細化，目前僅作簡單示例)
        if effect_template_config.get("effect_type") == "APPLY_STATUS_EFFECT":
            status_effect_id = effect_template_config.get("status_effect_id")
            if (
                status_effect_id
                and hasattr(self, "_skill_data")
                and "status_effects" in self._skill_data
            ):
                status_config = self._skill_data["status_effects"].get(status_effect_id)
                if status_config:
                    for effect_def_list_key in [
                        "effect_definitions_per_tick",
                        "effect_definitions_on_apply",
                    ]:
                        if effect_def_list_key in status_config:
                            for status_effect_def in status_config[effect_def_list_key]:
                                if (
                                    status_effect_def.get("effect_type") == "DAMAGE"
                                    and "damage_type" in status_effect_def
                                ):
                                    dt = status_effect_def["damage_type"]
                                    if dt and dt.upper() in [
                                        "TRUE",
                                        "PURE",
                                        "TRUE_DAMAGE",
                                        "PURE_DAMAGE",
                                    ]:
                                        return "TRUE_DAMAGE"
                                    return (
                                        dt if dt else "UNKNOWN"
                                    )  # 返回第一個找到的傷害狀態效果的傷害類型

        # 4. 後備：基於 skill_id 或 tags (如果前面的邏輯都沒有命中)
        # 這部分可以保留舊邏輯中的部分內容作為最終的猜測
        skill_id_upper = skill_config.get("id", "").upper()
        if "DAMAGE" in skill_id_upper or "_ATK" in skill_id_upper:
            return "DAMAGE"

        tags_upper = [str(tag).upper() for tag in skill_config.get("tags", [])]
        if "DAMAGE" in tags_upper:
            return "DAMAGE"

        logger.debug(
            "Could not determine specific damage type for skill %s (template: '%s'). Defaulting to UNKNOWN.",
            skill_config.get("id"),
            effect_template_id,
        )
        return "UNKNOWN"

    def _analyze_modification_formula(self, value_formula: Any) -> str:
        """
        分析屬性修改公式並返回其性質（"POSITIVE", "NEGATIVE", "NEUTRAL", "MIXED"）

        替代原有的 _is_positive_modification 和 _is_negative_modification 函數

        Args:
            value_formula: 可以是數字或字符串公式

        Returns:
            字符串表示公式的性質："POSITIVE", "NEGATIVE", "NEUTRAL" 或 "MIXED"
        """
        try:
            # 直接數值判斷 (整數或浮點數)
            if isinstance(value_formula, (int, float)):
                return (
                    "POSITIVE"
                    if value_formula > 0
                    else ("NEGATIVE" if value_formula < 0 else "NEUTRAL")
                )

            # 字符串公式判斷
            if isinstance(value_formula, str):
                # 空字符串或全空白字符串視為中性
                if not value_formula.strip():
                    return "NEUTRAL"

                # 如果以負號開頭，快速判斷為減益
                if value_formula.startswith("-"):
                    return "NEGATIVE"

                # 提取公式中的數字
                numbers_in_formula = self._number_regex.findall(value_formula)

                # 如果沒有找到數字，返回中性
                if not numbers_in_formula:
                    return "NEUTRAL"

                # 檢查是否有正數和負數
                has_positive = False
                has_negative = False

                for num_str in numbers_in_formula:
                    num_val = float(num_str)
                    if num_val > 0:
                        has_positive = True
                    elif num_val < 0:
                        has_negative = True

                    # 如果已經同時發現正數和負數，可以提前返回
                    if has_positive and has_negative:
                        return "MIXED"

                if has_positive:
                    return "POSITIVE" if not has_negative else "MIXED"
                elif has_negative:
                    return "NEGATIVE"

            return "NEUTRAL"  # 默認為中性
        except Exception as e:
            logger.debug("分析公式 '%s' 時出錯: %s", value_formula, e)
            return "NEUTRAL"  # 解析出錯，默認為中性

    def _get_stat_modification_nature(self, modifications: List[Dict[str, Any]]) -> str:
        """
        判斷屬性修改的本質（BUFF、DEBUFF、SPECIAL）

        Args:
            modifications: 属性修改列表

        Returns:
            修改本質类型："BUFF", "DEBUFF", 或 "SPECIAL"
        """
        if not modifications:
            return "SPECIAL"

        # 优化：不需要计数器，只需要记录是否有正面/负面修改
        has_positive = False
        has_negative = False

        for mod in modifications:
            value_formula = mod.get("value_formula", "0")
            formula_nature = self._analyze_modification_formula(value_formula)

            if formula_nature == "POSITIVE":
                has_positive = True
                if has_negative:  # 如果已经发现负面修改，可以提前判断为SPECIAL
                    return "SPECIAL"
            elif formula_nature == "NEGATIVE":
                has_negative = True
                if has_positive:  # 如果已经发现正面修改，可以提前判断为SPECIAL
                    return "SPECIAL"
            elif formula_nature == "MIXED":
                return "SPECIAL"  # 混合修改直接判断为SPECIAL

        # 根据修改性质返回结果
        if has_positive:
            return "BUFF"
        elif has_negative:
            return "DEBUFF"
        else:
            return "SPECIAL"  # 没有明确的正面或负面修改

    def _is_stat_buff_for_attribute(
        self,
        template_config: Dict[str, Any],
        status_effects_data: Dict[str, Any],
        stat_name: str,
        keywords: List[str],
    ) -> bool:
        """
        通用函數：判斷技能是否為指定屬性的增益

        Args:
            template_config: 技能模板配置
            status_effects_data: 狀態效果數據
            stat_name: 需要檢查的屬性名（如 "patk", "matk"）
            keywords: 在描述或名稱中搜索的關鍵詞列表

        Returns:
            如果是指定屬性的增益，返回True；否則返回False
        """
        if not template_config:
            return False

        effect_type = template_config.get("effect_type", "")

        # 1. 检查配置中的显式标记（优先级最高）
        if template_config.get("buff_attributes", {}).get(stat_name) is True:
            return True

        # 2. 检查描述中是否包含关键词
        description = template_config.get("description", "").lower()
        if description and any(kw in description for kw in keywords):
            return True

        # 3. 检查STAT_MODIFICATION类型
        if effect_type == "STAT_MODIFICATION":
            for mod in template_config.get("modifications", []):
                if (
                    mod.get("stat_name") == stat_name
                    and self._analyze_modification_formula(mod.get("value_formula"))
                    == "POSITIVE"
                ):
                    return True

        # 4. 检查状态效果
        if effect_type == "APPLY_STATUS_EFFECT":
            status_id = template_config.get("status_effect_id")
            if status_id and status_id in status_effects_data:
                status_info = status_effects_data[status_id]

                # 检查状态名称中的关键词
                status_name = status_info.get("name", "").lower()
                if status_name and any(kw in status_name for kw in keywords):
                    return True

                # 检查状态效果的buff_attributes标记
                if status_info.get("buff_attributes", {}).get(stat_name) is True:
                    return True

                # 检查状态效果的修改列表
                for effect_list_key in [
                    "effect_definitions_per_tick",
                    "effect_definitions_on_apply",
                ]:
                    for effect_def in status_info.get(effect_list_key, []):
                        if effect_def.get("effect_type") == "STAT_MODIFICATION":
                            for mod in effect_def.get("modifications", []):
                                if (
                                    mod.get("stat_name") == stat_name
                                    and self._analyze_modification_formula(
                                        mod.get("value_formula")
                                    )
                                    == "POSITIVE"
                                ):
                                    return True

        return False

    def _get_buff_classification_config(self, buff_type: str) -> Dict[str, Any]:
        """
        从配置获取buff分类信息，使用配置驱动替代硬编码

        Args:
            buff_type: buff类型标识符，如'physical_attack', 'magical_attack', 'defensive'

        Returns:
            包含stats和keywords的配置字典
        """
        default_configs = {
            "physical_attack": {
                "stats": ["patk"],
                "keywords": ["patk", "物攻", "物理攻擊", "物理傷害"],
            },
            "magical_attack": {
                "stats": ["matk"],
                "keywords": ["matk", "魔攻", "魔法攻擊", "魔法傷害"],
            },
            "defensive": {
                "stats": ["pdef", "mdef", "max_hp"],
                "keywords": [
                    "shield",
                    "防禦",
                    "屏障",
                    "護盾",
                    "減傷",
                    "hp",
                    "血量",
                    "生命",
                    "生命值",
                ],
            },
        }

        # 从配置获取，如果没有则使用默认值
        config = self._get_config_value(
            "skill_compatibility", "buff_classification_keywords", buff_type, default={}
        )

        if not config and buff_type in default_configs:
            return default_configs[buff_type]
        return config or {}

    def _is_attack_buff(
        self, template_config: Dict[str, Any], status_effects_data: Dict[str, Any]
    ) -> bool:
        """判斷是否為攻擊增益技能"""
        config = self._get_buff_classification_config("attack")

        # 如果配置为空，使用默認配置
        if not config:
            config = {"stats": ["atk"], "keywords": ["attack", "攻擊", "傷害"]}

        # 对配置中的每个属性进行检查
        for stat in config.get("stats", []):
            if self._is_stat_buff_for_attribute(
                template_config, status_effects_data, stat, config.get("keywords", [])
            ):
                return True
        return False

    def _is_defensive_buff(
        self, template_config: Dict[str, Any], status_effects_data: Dict[str, Any]
    ) -> bool:
        """判斷是否為防禦型增益技能"""
        # 直接检查是否为护盾效果（这是明确的防御增益）
        effect_type = template_config.get("effect_type")
        if effect_type == "APPLY_SHIELD":
            return True

        # 检查是否被标记为防御型增益
        if template_config.get("is_defensive_buff", False):
            return True

        # 检查状态效果是否为防御相关
        if effect_type == "APPLY_STATUS_EFFECT":
            status_id = template_config.get("status_effect_id")
            if status_id and status_id in status_effects_data:
                status_info = status_effects_data[status_id]

                # 检查是否为增益状态
                if status_info.get("is_buff", False):
                    # 检查特殊标记
                    if any(
                        flag.upper() == "SHIELD_EFFECT"
                        for flag in status_info.get("special_flags", [])
                    ):
                        return True

                    # 检查状态名称中是否包含防御相关关键字
                    name = status_info.get("name", "").lower()
                    if name and any(
                        kw in name for kw in ["shield", "防禦", "屏障", "護盾", "減傷"]
                    ):
                        return True

        # 使用配置驱动的属性检查
        config = self._get_buff_classification_config("defensive")
        if config:
            for stat in config.get("stats", []):
                if self._is_stat_buff_for_attribute(
                    template_config,
                    status_effects_data,
                    stat,
                    config.get("keywords", []),
                ):
                    return True

        return False

    def _determine_skill_category(
        self,
        skill_id: str,
        skill_config: Dict[str, Any],
        template_config: Dict[str, Any],
        status_effects_data: Dict[str, Any],
        effect_type: str,
    ) -> Tuple[str, str]:
        """
        確定技能的大類別（DAMAGE, HEAL, BUFF, DEBUFF, CONTROL, SPECIAL）和具體的傷害類型（如果適用）

        Args:
            skill_id: 技能ID
            skill_config: 技能配置
            template_config: 效果模板配置
            status_effects_data: 状态效果数据
            effect_type: 效果类型

        Returns:
            (skill_category, specific_damage_type): 技能类别和具体伤害类型
        """
        # 默认值
        specific_damage_type = "UNKNOWN"

        # 1. 检查模板中的显式分类标记 (配置驱动优先)
        if "skill_category" in template_config:
            category = template_config["skill_category"]
            specific_type = template_config.get("skill_specific_type", "_default_")
            logger.debug(
                "Skill %s: 使用模板显式分类 %s/%s", skill_id, category, specific_type
            )
            return category, specific_type

        # 2. 根据effect_type和其他属性推断
        effect_category_mapping = self._get_config_value(
            "effect_type_to_category_mapping", default={}
        )

        # 2.1 如果配置中存在此effect_type的映射，直接使用
        if effect_category_mapping and effect_type in effect_category_mapping:
            category = effect_category_mapping[effect_type]

            # 对于DAMAGE类型，需要确定具体伤害类型
            if category == "DAMAGE":
                damage_type = template_config.get("damage_type")
                if damage_type:
                    specific_damage_type = damage_type
                else:
                    specific_damage_type = self._infer_damage_type_from_skill_config(
                        skill_config
                    )

                # 标准化真实伤害命名
                if specific_damage_type and specific_damage_type.upper() in [
                    "TRUE",
                    "PURE",
                ]:
                    specific_damage_type = "TRUE_DAMAGE"

            logger.debug(
                "Skill %s: 根据effect_type映射分类为 %s/%s",
                skill_id,
                category,
                specific_damage_type,
            )
            return category, specific_damage_type

        # 3. 基于特定效果类型的详细逻辑 (保留必要的硬编码逻辑)
        if effect_type == "DAMAGE":
            damage_type = template_config.get("damage_type")
            specific_damage_type = (
                damage_type
                if damage_type
                else self._infer_damage_type_from_skill_config(skill_config)
            )

            if specific_damage_type and specific_damage_type.upper() in [
                "TRUE",
                "PURE",
            ]:
                specific_damage_type = "TRUE_DAMAGE"

            return "DAMAGE", specific_damage_type

        elif effect_type == "HEAL":
            return "HEAL", specific_damage_type

        elif effect_type == "APPLY_STATUS_EFFECT":
            status_id = template_config.get("status_effect_id")
            if status_id and status_id in status_effects_data:
                status_info = status_effects_data[status_id]

                # 首先检查状态效果是否有显式分类
                if "skill_category" in status_info:
                    category = status_info["skill_category"]
                    specific_type = status_info.get("skill_specific_type", "_default_")
                    return category, specific_type

                # 检查状态效果中的伤害组件
                for effect_list_key in [
                    "effect_definitions_per_tick",
                    "effect_definitions_on_apply",
                ]:
                    if effect_list_key in status_info:
                        for effect_def in status_info[effect_list_key]:
                            if (
                                effect_def.get("effect_type") == "DAMAGE"
                                and "damage_type" in effect_def
                            ):
                                dt = effect_def["damage_type"]
                                if dt and dt.upper() in ["TRUE", "PURE"]:
                                    specific_damage_type = "TRUE_DAMAGE"
                                else:
                                    specific_damage_type = dt

                # 基于状态标记判断
                if "CONTROL" in status_info.get("special_flags", []):
                    return "CONTROL", specific_damage_type
                elif status_info.get("is_buff", False):
                    return "BUFF", specific_damage_type
                else:
                    return "DEBUFF", specific_damage_type

            # 如果没有找到状态效果或无法确定分类
            return "SPECIAL", specific_damage_type

        elif effect_type == "STAT_MODIFICATION":
            # 判断属性修改的性质
            modifications = template_config.get("modifications", [])
            nature = self._get_stat_modification_nature(modifications)
            return nature, specific_damage_type

        # 4. 基于effect_type的简单归类
        simple_effect_mapping = {
            "APPLY_SHIELD": "BUFF",
            "DISPEL_DEBUFF": "BUFF",
            "REMOVE_BUFF": "BUFF",
            "SHIELD_REMOVAL": "DEBUFF",
            "LOSE_MP": "DEBUFF",
            "INCREASE_COOLDOWN": "DEBUFF",
        }

        if effect_type in simple_effect_mapping:
            return simple_effect_mapping[effect_type], specific_damage_type

        # 5. 默认分类
        logger.debug("Skill %s: 无法确定分类，使用默认值SPECIAL", skill_id)
        return "SPECIAL", specific_damage_type

    def _check_damage_compatibility(
        self, card_type: str, specific_damage_type: str
    ) -> bool:
        """檢查傷害技能與卡牌類型的兼容性"""
        # 从配置中获取DAMAGE类型的兼容卡牌类型列表
        compatible_types = self._get_config_value(
            "skill_generation",
            "skill_category_to_card_type_compatibility",
            "DAMAGE",
            default=["ATTACK", "BALANCED"],
        )

        # 首先检查卡牌类型是否在兼容列表中
        if compatible_types and card_type not in compatible_types:
            return False

        # 然后检查具体的伤害类型与卡牌类型的匹配关系
        if card_type == "ATTACK":
            return specific_damage_type in ["DAMAGE", "NEUTRAL", "TRUE_DAMAGE"]
        elif card_type == "BALANCED":
            return specific_damage_type in ["DAMAGE", "NEUTRAL", "TRUE_DAMAGE"]

        # 其他情况默认不兼容
        return False

    def _check_buff_compatibility(
        self,
        card_type: str,
        template_config: Dict[str, Any],
        status_effects_data: Dict[str, Any],
    ) -> bool:
        """檢查增益技能與卡牌類型的兼容性"""
        # 从配置中获取BUFF类型的兼容卡牌类型列表
        compatible_types = self._get_config_value(
            "skill_generation",
            "skill_category_to_card_type_compatibility",
            "BUFF",
            default=["DEFENSE", "SUPPORT", "BALANCED"],
        )

        # 如果卡牌类型直接在兼容列表中，快速返回兼容
        if compatible_types and card_type in compatible_types:
            return True

        # 针对特殊卡牌类型的增益检查
        if card_type == "ATTACK" and self._is_attack_buff(
            template_config, status_effects_data
        ):
            return True

        return False

    def _determine_skill_compatibility(
        self, skill_id: str, skill_config: Dict[str, Any], card_type: str
    ) -> bool:
        """
        判断一个技能是否与给定的卡牌类型兼容，使用配置文件规则

        Args:
            skill_id: 技能的ID
            skill_config: 技能的配置字典
            card_type: 卡牌类型字符串

        Returns:
            如果兼容则为True，否则为False
        """
        # 1. 检查空配置
        if not skill_config:
            logger.warning(
                "Skill config for ID '%s' is unexpectedly None/empty. Skipping compatibility check.",
                skill_id,
            )
            return False

        # 2. 特殊技能ID处理
        if skill_id == "BASIC_ATTACK":
            compatible_types = self._get_config_value(
                "skill_generation",
                "skill_type_compatibility",
                "basic_attack",
                default=["ATTACK", "BALANCED", "DEFENSE", "SUPPORT"],
            )
            return card_type in (compatible_types or [])

        # 3. 通过技能ID进行直接匹配
        type_compatibility = self._get_config_value(
            "skill_generation", "skill_type_compatibility", default={}
        )
        for skill_type, compatible_card_types in (type_compatibility or {}).items():
            if skill_id.upper() == skill_type.upper() or skill_id.upper().endswith(
                f"_{skill_type.upper()}"
            ):
                if compatible_card_types:
                    return card_type in compatible_card_types

        # 4. 提取技能效果和类別
        effect_definitions = skill_config.get("base_effect_definitions", [])
        if (
            not effect_definitions
            or not isinstance(effect_definitions, list)
            or not effect_definitions[0]
        ):
            logger.debug(
                "Skill %s: No base_effect_definitions. Cannot determine compatibility.",
                skill_id,
            )
            return False

        main_effect_def = effect_definitions[0]
        effect_template_id = main_effect_def.get("effect_template")

        if not effect_template_id or not isinstance(effect_template_id, str):
            logger.debug("Skill %s: effect_template ID missing or invalid.", skill_id)
            return False

        # 5. 获取效果模板
        effect_templates = self._skill_data.get("effect_templates", {})
        status_effects_data = self._skill_data.get("status_effects", {})

        template_config = effect_templates.get(effect_template_id)
        if not template_config:
            logger.warning(
                "Skill %s: effect_template_id '%s' not found in effect_templates.json.",
                skill_id,
                effect_template_id,
            )
            return False

        # 6. 获取技能类别
        effect_type = template_config.get("effect_type")
        skill_category, specific_type = self._determine_skill_category(
            skill_id, skill_config, template_config, status_effects_data, effect_type
        )

        # 7. 使用技能类别兼容性矩阵
        category_compatibility = self._get_config_value(
            "skill_generation", "skill_category_to_card_type_compatibility", default={}
        )

        if category_compatibility and skill_category in category_compatibility:
            compatible_types = category_compatibility.get(skill_category, [])
            if compatible_types and card_type in compatible_types:
                return True

        # 8. 特殊类别检查
        if skill_category == "DAMAGE":
            return self._check_damage_compatibility(card_type, specific_type)

        if skill_category == "BUFF":
            return self._check_buff_compatibility(
                card_type, template_config, status_effects_data
            )

        # 9. 通用规则
        # 平衡型卡牌兼容大部分技能
        if card_type == "BALANCED":
            return True

        # 辅助型卡牌兼容大部分非伤害技能
        if card_type == "SUPPORT" and skill_category != "DAMAGE":
            return True

        # 默认不兼容
        return False

    def _get_skills_by_rarity_range(
        self,
        min_rarity: int,
        max_rarity: int,
        skill_type: str,
        card_type: str,
        exclude: Optional[List[str]] = None,
    ) -> List[str]:
        """
        獲取指定稀有度範圍內的技能（統一技能獲取邏輯）

        Args:
            min_rarity: 最小稀有度
            max_rarity: 最大稀有度
            skill_type: 技能類型 ("active", "passive", "innate")
            card_type: 卡牌類型
            exclude: 要排除的技能ID列表

        Returns:
            符合條件的技能ID列表
        """
        if exclude is None:
            exclude = []

        # 如果是獲取主動技能，確保 "BASIC_ATTACK" 不會被選中
        additional_exclude = []
        if skill_type == "active":
            additional_exclude.append("BASIC_ATTACK")

        combined_exclude = list(set(exclude + additional_exclude))

        skill_rarity_mapping = self._get_skill_rarity_mapping(skill_type)
        all_skills_in_range = []

        for rarity_val in range(min_rarity, max_rarity + 1):
            skills_of_rarity = skill_rarity_mapping.get(str(rarity_val), [])
            all_skills_in_range.extend(skills_of_rarity)

        # 去重並排除指定技能
        unique_skills = list(set(all_skills_in_range))
        # 使用 combined_exclude 進行過濾
        filtered_by_exclude = [
            skill_id for skill_id in unique_skills if skill_id not in combined_exclude
        ]

        # 根據卡牌類型篩選兼容技能
        return self._get_compatible_skills(filtered_by_exclude, card_type)

    def _get_guaranteed_skill_by_rarity(
        self,
        card_rarity: int,
        skill_type: str,
        card_type: str,
        exclude: Optional[List[str]] = None,
    ) -> Optional[str]:
        """
        獲取與卡牌稀有度完全匹配的技能（保證邏輯）

        Args:
            card_rarity: 卡牌稀有度（1-7）
            skill_type: 技能類型（"active", "passive", "innate"）
            card_type: 卡牌類型
            exclude: 要排除的技能ID列表，默认为None

        Returns:
            技能ID，如果沒有找到則返回None
        """
        # 嘗試獲取精確稀有度匹配的技能
        for rarity in range(card_rarity, 0, -1):
            skills = self._get_skills_by_rarity_range(
                rarity, rarity, skill_type, card_type, exclude
            )
            if skills:
                return random.choice(skills)

        return None

    def _get_random_skill_up_to_rarity(
        self,
        max_rarity: int,
        skill_type: str,
        card_type: str,
        exclude: Optional[List[str]] = None,
    ) -> Optional[str]:
        """
        獲取1~max_rarity稀有度範圍內的隨機技能

        Args:
            max_rarity: 最大稀有度
            skill_type: 技能類型（"active", "passive", "innate"）
            card_type: 卡牌類型
            exclude: 要排除的技能ID列表

        Returns:
            技能ID，如果沒有找到則返回None
        """
        skills = self._get_skills_by_rarity_range(
            1, max_rarity, skill_type, card_type, exclude
        )
        return random.choice(skills) if skills else None

    def _convert_rarity_to_string(self, rarity: int) -> str:
        """
        將數字稀有度轉換為字符串

        Args:
            rarity: 數字稀有度（1-7）

        Returns:
            稀有度字符串
        """
        rarity_map = self._get_config_value("rarity_conversion", default={})
        return rarity_map.get(str(rarity), "N") if rarity_map else "N"

    def _get_compatible_skills(
        self, skills_ids_to_check: List[str], card_type: str
    ) -> List[str]:
        """
        获取与指定卡牌类型兼容的技能列表

        Args:
            skills_ids_to_check: 要检查的技能ID列表
            card_type: 卡牌类型

        Returns:
            兼容的技能ID列表
        """
        compatible_skills = []
        if not skills_ids_to_check:  # 防御性编程，如果传入空列表
            return compatible_skills

        # 确保 _skill_data 已加载
        if (
            not hasattr(self, "_skill_data")
            or not self._skill_data
            or "all_skill_configs" not in self._skill_data
        ):
            logger.error(
                "'_skill_data' or 'all_skill_configs' not initialized. Cannot determine skill compatibility."
            )
            # 尝试加载一次，以防万一 (虽然正常流程中应该已经加载)
            logger.info("Attempting to load skill files for compatibility check...")
            try:
                self._skill_data = self._load_skill_files()
                if (
                    not self._skill_data or "all_skill_configs" not in self._skill_data
                ):  # 再次检查
                    logger.error(
                        "Failed to load skill files during compatibility check. Returning empty list."
                    )
                    return []
            except Exception as e:
                logger.error(
                    "Error loading skill files during compatibility check: %s. Returning empty list.",
                    e,
                )
                return []

        for skill_id_from_list in skills_ids_to_check:
            skill_config = self._skill_data["all_skill_configs"].get(skill_id_from_list)
            if not skill_config:
                logger.warning(
                    "Skill config for ID '%s' not found in 'all_skill_configs'. Skipping compatibility check for this skill.",
                    skill_id_from_list,
                )
                continue

            if self._determine_skill_compatibility(
                skill_id_from_list, skill_config, card_type
            ):
                compatible_skills.append(skill_id_from_list)
            else:
                logger.debug(
                    "Skill '%s' determined as NOT compatible with card_type '%s'.",
                    skill_id_from_list,
                    card_type,
                )

        logger.debug(
            f"For card_type '{card_type}', found {len(compatible_skills)} compatible skills out of {len(skills_ids_to_check)} checked: {compatible_skills}"
        )
        return compatible_skills

    async def generate_card_configs(self, cards_per_rarity: int = 3) -> bool:
        """生成卡牌RPG配置（每個稀有度各取N張卡牌）"""
        try:
            print("🃏 生成卡牌RPG配置...")

            # 從數據庫獲取卡牌數據
            cards = await self._get_cards_from_database(cards_per_rarity)

            if not cards:
                print("❌ 沒有找到卡牌數據")
                return False

            card_configs = {}

            for card in cards:
                card_config = self._generate_single_card_config_from_database(card)
                card_configs[str(card["card_id"])] = card_config

            # 保存配置文件
            config_path = os.path.join(self.output_dir, "cards.json")
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(card_configs, f, ensure_ascii=False, indent=2)

            print(f"✅ 生成了 {len(card_configs)} 個卡牌配置")
            print(f"   保存到: {config_path}")
            return True

        except Exception as e:
            print(f"❌ 生成卡牌配置失敗: {e}")
            logger.error("生成卡牌配置失敗: %s", e)
            return False

    def _generate_single_card_config_from_database(
        self, card: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        從數據庫卡牌數據生成單個卡牌的RPG配置（按照文檔設計）

        Args:
            card: 數據庫卡牌數據，包含CPR和card_type

        Returns:
            卡牌RPG配置字典
        """
        # 獲取CPR和預算模板
        cpr = card["cpr"]
        budget_template = self._get_stat_budget_template(cpr)

        # 生成基礎屬性
        base_stats = self._generate_base_stats_from_template(
            budget_template, card["card_type"]
        )

        # 生成成長屬性
        growth_stats = self._generate_growth_stats_from_template(
            budget_template, card["card_type"]
        )

        # 生成被动技能槽数量（根据CPR/稀有度）
        passive_slots = self._calculate_passive_skill_slots(cpr)

        # 获取预装技能数量
        skill_counts = self._get_preinstalled_skill_counts_by_rarity(card["rarity"])

        # 使用通用方法生成主动和被动技能
        initial_active_skills = self._assign_initial_skills(
            card,
            "active",
            3,
            skill_counts["active"],
            "active_skills",
            is_dict_output=False,
        )

        initial_passives = self._assign_initial_skills(
            card,
            "passive",
            passive_slots,
            skill_counts["passive"],
            "passive_skills",
            is_dict_output=True,
        )

        # 生成固有技能（如果有）
        innate_passive_skill_id = None
        if skill_counts["innate"] > 0:
            innate_passive_skill_id = self._get_guaranteed_skill_by_rarity(
                card["rarity"], "innate", card["card_type"]
            )

        return {
            "id": str(card["card_id"]),
            "name": card["name"],
            "description": card.get("description", f"{card['name']}的RPG配置"),
            "rarity": card["rarity_string"],
            "card_type": card["card_type"],
            "base_stats": base_stats,
            "growth_per_rpg_level": growth_stats,
            "star_level_effects_key": self._generate_star_effects_key(card),
            "primary_attack_skill_id": "BASIC_ATTACK",  # 固定使用基础攻击
            "innate_passive_skill_id": innate_passive_skill_id,
            "initial_equipped_active_skills": initial_active_skills,
            "initial_equipped_passives": initial_passives,
            "passive_skill_slots": passive_slots,
            "max_active_skill_slots": 3,  # 固定3个主动技能槽
            "cpr": cpr,  # 保留CPR用於調試
            "pool_type": card["pool_type"],  # 保留卡池類型用於調試
        }

    def _get_base_stat_names(self) -> List[str]:
        """獲取基礎屬性名稱列表"""
        return ["max_hp", "max_mp", "mp_regen_per_turn", "atk", "def", "spd"]

    def _get_combat_stats_defaults(self, is_growth: bool = False) -> Dict[str, float]:
        """獲取戰鬥屬性的默認值"""
        if is_growth:
            return {
                "crit_rate": 0.0,
                "crit_dmg_multiplier": 0.0,
                "accuracy": 0.0,
                "evasion": 0.0,
            }
        else:
            return {
                "crit_rate": 0.05,
                "crit_dmg_multiplier": 1.5,
                "accuracy": 0.95,
                "evasion": 0.05,
            }

    def _allocate_stats_by_weight(
        self,
        total_points: float,
        weights: Dict[str, float],
        min_value: float = 1.0,
        is_growth: bool = False,
    ) -> Dict[str, float]:
        """
        根據權重分配屬性點數（統一分配邏輯）

        Args:
            total_points: 總點數
            weights: 屬性權重字典
            min_value: 最小值
            is_growth: 是否為成長屬性

        Returns:
            分配後的屬性字典
        """
        stats = {}
        remaining_points = total_points
        stat_names = self._get_base_stat_names()

        for i, stat in enumerate(stat_names):
            if i == len(stat_names) - 1:
                # 最後一個屬性獲得剩餘點數
                value = max(min_value, float(remaining_points))
                stats[stat] = round(value, 2) if is_growth else float(int(value))
            else:
                # 根據權重分配點數
                weight = weights.get(stat, 0.1)
                if is_growth:
                    allocated = total_points * weight * random.uniform(0.8, 1.2)
                    allocated = max(
                        min_value,
                        min(
                            allocated,
                            remaining_points - (len(stat_names) - i - 1) * min_value,
                        ),
                    )
                    stats[stat] = round(allocated, 2)
                else:
                    allocated = int(total_points * weight * random.uniform(0.8, 1.2))
                    allocated = max(
                        int(min_value),
                        min(
                            allocated, int(remaining_points) - (len(stat_names) - i - 1)
                        ),
                    )
                    stats[stat] = float(allocated)
                remaining_points -= stats[stat]

        # 添加戰鬥屬性
        stats.update(self._get_combat_stats_defaults(is_growth))
        return stats


async def main():
    """主函數 - 專注於從數據庫生成卡牌RPG配置"""
    logger.info("🏗️ RPG卡牌配置生成腳本")
    logger.info("=" * 50)

    generator = RPGConfigGenerator()

    # 獲取每個稀有度的卡牌數量參數
    import sys

    cards_per_rarity = 3  # 默認每個稀有度3張卡牌
    if len(sys.argv) > 1:
        try:
            cards_per_rarity = int(sys.argv[1])
            logger.info("📊 將生成每個稀有度 %s 張卡牌的RPG配置", cards_per_rarity)
        except ValueError:
            logger.warning("⚠️ 參數無效，使用默認值3")

    logger.info("📊 從數據庫獲取每個稀有度 %s 張卡牌...", cards_per_rarity)

    try:
        # 生成卡牌配置
        result = await generator.generate_card_configs(cards_per_rarity)

        if result:
            logger.info("🎉 卡牌RPG配置生成完成！")
            logger.info("📁 配置文件保存在: %s/cards.json", generator.output_dir)
            logger.info("💡 提示：")
            logger.info("   - 技能配置已存在於 active_skills.json")
            logger.info("   - 星級效果配置已存在於 star_level_effects.json")
            logger.info("   - 怪物配置已存在於 monsters.json")
        else:
            logger.error("❌ 卡牌配置生成失敗")

    except Exception as e:
        print(f"❌ 生成過程中發生錯誤: {e}")
        logger.error("生成過程中發生錯誤: %s", e)

    finally:
        # 清理數據庫連接
        try:
            from database.postgresql.async_manager import close_connections

            await close_connections()
        except Exception:
            pass


if __name__ == "__main__":
    asyncio.run(main())
