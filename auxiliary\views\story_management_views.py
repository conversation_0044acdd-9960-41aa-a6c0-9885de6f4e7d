from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import discord

from auxiliary.exceptions import AuxiliaryError
from auxiliary.services import story_logic
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.response_embeds import SuccessEmbed, WarningEmbed

if TYPE_CHECKING:
    from auxiliary.cogs.story_cog import StoryCog



class StoryRenameModal(BaseModal, title="重新命名故事"):
    new_title = discord.ui.TextInput(
        label="新的故事標題",
        style=discord.TextStyle.short,
        placeholder="輸入新的故事標題...",
        required=True,
        max_length=100,
    )

    def __init__(self, story_action_view: "StoryActionView"):
        super().__init__(bot=story_action_view.story_cog.bot, title="重新命名故事")
        self.story_action_view = story_action_view
        current_title = story_action_view.story.get(
            "story_title"
        ) or story_action_view.story.get("theme_title", "未命名故事")
        self.new_title.default = current_title

    async def on_submit(self, interaction: discord.Interaction):
        user_id = self.story_action_view.user.id
        story_cog = self.story_action_view.story_cog

        if user_id in story_cog.active_story_users:
            raise AuxiliaryError("您有另一個故事操作正在進行中，請稍候再試。")

        story_cog.active_story_users.add(user_id)
        try:
            story_id = self.story_action_view.story["story_id"]
            new_title = self.new_title.value.strip()

            success = await story_logic.rename_story(user_id, story_id, new_title)

            if success:
                embed = SuccessEmbed(description=f"故事已重新命名為：「{new_title}」")
                await interaction.response.edit_message(embed=embed, view=None)

                self.story_action_view.story["story_title"] = new_title
                await self.story_action_view._update_parent_view()
            else:
                raise AuxiliaryError("重新命名失敗。")
        finally:
            story_cog.active_story_users.discard(user_id)


class StoryListView(BasePaginationView):
    def __init__(
        self,
        stories: List[Dict[str, Any]],
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
    ):
        self.stories = stories
        self.user = user
        self.story_cog = story_cog
        self.stories_per_page = 10
        total_pages = (
            len(stories) + self.stories_per_page - 1
        ) // self.stories_per_page or 1

        super().__init__(
            bot=story_cog.bot,
            user_id=user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=300,
        )
        self.message: Optional[discord.Message] = None
        self._update_components()

    def _update_components(self):
        """更新視圖元件，包括分頁按鈕和故事選擇下拉選單。"""
        self.clear_items()
        self.add_pagination_buttons(row=0)  # 添加基礎分頁按鈕
        self._add_story_select(row=1)  # 在下方添加故事選擇
        self._refresh_button_states()

    def _add_story_select(self, row: int):
        """創建並添加故事選擇下拉選單。"""
        start_idx = (self.current_page - 1) * self.stories_per_page
        end_idx = start_idx + self.stories_per_page
        current_stories = self.stories[start_idx:end_idx]

        if not current_stories:
            return

        story_options = []
        for story in current_stories:
            story_id = story.get("story_id")
            if not story_id:
                continue

            story_title = story.get("story_title") or story.get(
                "theme_title", "未命名故事"
            )
            theme_title = story.get("theme_title", "未知主題")
            turn_count = story.get("turn_count", 0)
            is_public = story.get("is_public", False)
            story_status = story.get("story_status", "未知狀態")

            status_emoji = "🟢" if story_status == "active" else "⏸️"
            public_emoji = "🔗" if is_public else "🔒"

            story_options.append(
                discord.SelectOption(
                    label=story_title[:45],
                    value=f"story_{story_id}",
                    description=f"主題: {theme_title[:30]} | 回合: {turn_count} | {public_emoji}",
                    emoji=status_emoji,
                )
            )

        if story_options:
            story_select = discord.ui.Select(
                placeholder="選擇故事進行操作...", options=story_options, row=row
            )
            story_select.callback = self.story_select_callback
            self.add_item(story_select)

    async def get_current_page_embed(self) -> discord.Embed:
        """創建並返回當前頁的 Embed。"""
        embed = discord.Embed(title="📚 你的故事列表", color=discord.Color.blue())

        if not self.stories:
            embed.description = (
                "你還沒有任何故事。使用 `/story start` 開始你的第一個冒險！"
            )
            return embed

        start_idx = (self.current_page - 1) * self.stories_per_page
        end_idx = start_idx + self.stories_per_page
        current_stories = self.stories[start_idx:end_idx]

        for story in current_stories:
            story_title = story.get("story_title") or story.get(
                "theme_title", "未命名故事"
            )
            theme_title = story.get("theme_title", "未知主題")
            story_status = story.get("story_status", "未知狀態")
            turn_count = story.get("turn_count", 0)
            is_public = story.get("is_public", False)
            created_at = story.get("created_at")

            status_emoji = "🟢" if story_status == "active" else "⏸️"
            public_emoji = "🔗" if is_public else "🔒"
            status_display = "進行中" if story_status == "active" else "暫停"

            details = [
                f"狀態：{status_display}",
                f"回合：{turn_count}",
                f"{public_emoji} {'公開' if is_public else '私人'}",
            ]
            if created_at:
                details.append(f"開始於 <t:{int(created_at.timestamp())}:R>")

            base_info = " | ".join(details)
            field_value = f"**主題：** {theme_title}\n{base_info}"

            if is_public:
                field_value += f"\nID：`{story['story_id']}`"

            embed.add_field(
                name=f"{status_emoji} {story_title}", value=field_value, inline=False
            )

        embed.set_footer(
            text=f"第 {self.current_page}/{self.total_pages} 頁 | 共 {len(self.stories)} 個故事"
        )
        return embed

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """當頁面改變時，更新 Embed 和視圖元件。"""
        self.current_page = page
        self._update_components()
        embed = await self.get_current_page_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    async def story_select_callback(self, interaction: discord.Interaction):
        """處理故事選擇下拉選單的選擇。"""
        if not interaction.data or "values" not in interaction.data:
            return

        story_id_str = interaction.data["values"][0].replace("story_", "")
        story = next(
            (s for s in self.stories if str(s.get("story_id")) == story_id_str), None
        )
        if not story:
            raise AuxiliaryError("找不到指定的故事。")

        story_title = story.get("story_title") or story.get("theme_title", "未命名故事")
        story_status = story.get("story_status", "未知狀態")
        is_public = story.get("is_public", False)

        action_view = StoryActionView(story, self.user, self.story_cog, self)
        embed = discord.Embed(
            title=f"📖 故事操作：{story_title}",
            description=f"**狀態：** {story_status}\n**分享：** {'公開' if is_public else '私人'}",
            color=discord.Color.blue(),
        )
        await interaction.response.send_message(
            embed=embed, view=action_view, ephemeral=True
        )


class StoryActionView(BaseView):
    """故事操作按鈕視圖"""

    def __init__(
        self,
        story: Dict[str, Any],
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
        parent_view: "StoryListView",
    ):
        super().__init__(bot=story_cog.bot, user_id=user.id, timeout=300)
        self.story = story
        self.user = user
        self.story_cog = story_cog
        self.parent_view = parent_view

        story_status = story.get("story_status", "未知狀態")
        is_public = story.get("is_public", False)

        if story_status == "paused":
            resume_button = discord.ui.Button(
                label="🔄 恢復故事", style=discord.ButtonStyle.success, row=0
            )
            resume_button.callback = self.resume_story
            self.add_item(resume_button)

        share_button = discord.ui.Button(
            label="🔗 分享" if not is_public else "🔒 取消分享",
            style=discord.ButtonStyle.secondary,
            row=0,
        )
        share_button.callback = self.toggle_share
        self.add_item(share_button)

        rename_button = discord.ui.Button(
            label="✏️ 重新命名", style=discord.ButtonStyle.secondary, row=0
        )
        rename_button.callback = self.rename_story
        self.add_item(rename_button)

        delete_button = discord.ui.Button(
            label="🗑️ 刪除", style=discord.ButtonStyle.danger, row=1
        )
        delete_button.callback = self.delete_story
        self.add_item(delete_button)

        cancel_button = discord.ui.Button(
            label="❌ 取消", style=discord.ButtonStyle.secondary, row=1
        )
        cancel_button.callback = self.cancel
        self.add_item(cancel_button)

    async def resume_story(self, interaction: discord.Interaction):
        if self.user.id in self.story_cog.active_story_users:
            raise AuxiliaryError("您有另一個故事操作正在進行中，請稍候再試。")
        self.story_cog.active_story_users.add(self.user.id)
        try:
            story_id = self.story["story_id"]
            story_title = self.story.get("story_title") or self.story.get(
                "theme_title", "未命名故事"
            )
            await interaction.response.defer()
            await story_logic.resume_story(self.user.id, story_id)
            await self._update_parent_view()
            embed = SuccessEmbed(
                description=f"已恢復故事：{story_title}\n使用 `/story start` 繼續遊玩！"
            )
            await interaction.edit_original_response(embed=embed, view=None)
        finally:
            self.story_cog.active_story_users.discard(self.user.id)

    async def toggle_share(self, interaction: discord.Interaction):
        if self.user.id in self.story_cog.active_story_users:
            raise AuxiliaryError("您有另一個故事操作正在進行中，請稍候再試。")
        self.story_cog.active_story_users.add(self.user.id)
        try:
            story_id = self.story["story_id"]
            story_title = self.story.get("story_title") or self.story.get(
                "theme_title", "未命名故事"
            )
            is_public = await story_logic.toggle_story_public(self.user.id, story_id)
            if is_public:
                embed = SuccessEmbed(
                    title="🔗 故事分享",
                    description=(
                        f"故事「{story_title}」已設為公開！\n\n"
                        f"**分享ID：** `{story_id}`\n\n"
                        f"其他人可以使用指令 `/story view story_id:{story_id}` 來查看你的故事。"
                    ),
                )
            else:
                embed = WarningEmbed(
                    title="🔒 取消分享",
                    description=f"故事「{story_title}」已設為私人，其他人無法再查看。",
                )
            await interaction.response.edit_message(embed=embed, view=None)
            await self._update_parent_view()
        finally:
            self.story_cog.active_story_users.discard(self.user.id)

    async def rename_story(self, interaction: discord.Interaction):
        modal = StoryRenameModal(self)
        await interaction.response.send_modal(modal)

    async def delete_story(self, interaction: discord.Interaction):
        story_id = self.story["story_id"]
        story_title = self.story.get("story_title") or self.story.get(
            "theme_title", "未命名故事"
        )
        confirm_embed = discord.Embed(
            title="⚠️ 確認刪除",
            description=f"你確定要刪除故事「{story_title}」嗎？\n此操作無法撤銷！",
            color=discord.Color.red(),
        )
        confirm_view = BaseView(bot=self.story_cog.bot, user_id=self.user.id)

        async def confirm_delete(interaction: discord.Interaction):
            if self.user.id in self.story_cog.active_story_users:
                raise AuxiliaryError("您有另一個故事操作正在進行中，請稍候再試。")
            self.story_cog.active_story_users.add(self.user.id)
            try:
                success = await story_logic.delete_story(self.user.id, story_id)
                if success:
                    embed = SuccessEmbed(description=f"已刪除故事：{story_title}")
                    await interaction.response.edit_message(embed=embed, view=None)
                    await self._update_parent_view()
                else:
                    raise AuxiliaryError("刪除故事失敗。")
            finally:
                self.story_cog.active_story_users.discard(self.user.id)

        async def cancel_delete(interaction: discord.Interaction):
            await interaction.response.edit_message(
                content="已取消刪除。", embed=None, view=None
            )

        confirm_button = discord.ui.Button(
            label="確定刪除", style=discord.ButtonStyle.danger
        )
        confirm_button.callback = confirm_delete
        confirm_view.add_item(confirm_button)
        cancel_button = discord.ui.Button(
            label="取消", style=discord.ButtonStyle.secondary
        )
        cancel_button.callback = cancel_delete
        confirm_view.add_item(cancel_button)
        await interaction.response.edit_message(embed=confirm_embed, view=confirm_view)

    async def cancel(self, interaction: discord.Interaction):
        await interaction.response.edit_message(
            content="已取消操作。", embed=None, view=None
        )

    async def _update_parent_view(self):
        """更新父視圖的故事列表並重新整理。"""
        updated_stories = await story_logic.get_user_stories(self.user.id)
        self.parent_view.stories = updated_stories
        self.parent_view.total_pages = (
            len(updated_stories) + self.parent_view.stories_per_page - 1
        ) // self.parent_view.stories_per_page or 1
        if self.parent_view.current_page > self.parent_view.total_pages:
            self.parent_view.current_page = self.parent_view.total_pages

        self.parent_view._update_components()

        if self.parent_view.message:
            try:
                embed = await self.parent_view.get_current_page_embed()
                await self.parent_view.message.edit(embed=embed, view=self.parent_view)
            except discord.NotFound:
                pass  # 訊息可能已被刪除
