卡片匯入系統整理完成報告
============================

整理日期: 2025-06-15
整理內容: 將 D:\DICKPK\scripts 底下的匯入卡片相關檔案和數據統一整理

## 整理前的檔案分佈
原本檔案散佈在 scripts 根目錄下：
- import_cards.py
- import_wixoss_cards.py  
- export_cards_by_pool.py
- pre_download_master_card_images.py
- hololive_card.json
- pokemon_cards_jp_updated.json
- union_arena_data.json
- wixoss_simplified.json

## 整理後的新結構
所有卡片相關檔案現在統一放在 scripts/card_import/ 目錄下：

scripts/card_import/
├── scripts/                           # 腳本檔案
│   ├── import_cards.py                # 通用卡片匯入腳本
│   ├── import_wixoss_cards.py        # WIXOSS 專用卡片匯入腳本
│   ├── export_cards_by_pool.py       # 按卡池導出卡片腳本
│   └── pre_download_master_card_images.py # 預下載卡片圖片腳本
├── data/                              # 數據檔案
│   ├── hololive_card.json            # Hololive 卡片數據
│   ├── pokemon_cards_jp_updated.json # 寶可夢卡片數據
│   ├── union_arena_data.json         # Union Arena 卡片數據
│   └── wixoss_simplified.json        # WIXOSS 卡片數據
├── import_cards.bat                   # 便利的批次執行檔案
├── README.md                          # 詳細說明文檔
├── USAGE.md                          # 使用指南
└── 整理完成.txt                      # 本報告檔案

## 主要改進

### 1. 檔案組織
- 腳本和數據分離，結構更清晰
- 相關檔案集中管理，便於維護
- 添加了完整的文檔說明

### 2. 路徑修正
- 修正了所有腳本中的 .env 檔案路徑
- 確保從新位置正確載入環境變數
- 更新了相對路徑引用

### 3. 使用便利性
- 提供了 import_cards.bat 批次檔案
- 支援選單式操作，無需記憶命令
- 包含詳細的使用說明和故障排除指南

### 4. 文檔完整性
- README.md: 完整的系統說明
- USAGE.md: 詳細的使用指南
- 包含環境需求和故障排除

## 使用方式

### 快速開始
1. 雙擊 scripts/card_import/import_cards.bat
2. 根據選單選擇要執行的操作

### 命令行使用
```bash
cd scripts/card_import

# 匯入 Hololive 卡片
python scripts/import_cards.py hololive data/hololive_card.json hololive

# 匯入 Pokemon TCG 卡片  
python scripts/import_cards.py ptcg data/pokemon_cards_jp_updated.json ptcg

# 匯入 Union Arena 卡片
python scripts/import_cards.py ua data/union_arena_data.json ua

# 匯入 WIXOSS 卡片
python scripts/import_wixoss_cards.py data/wixoss_simplified.json

# 導出所有卡片
python scripts/export_cards_by_pool.py

# 預下載卡片圖片
python scripts/pre_download_master_card_images.py
```

## 注意事項

1. **環境變數**: 確保 .env 檔案位於專案根目錄 (D:\DICKPK\.env)
2. **資料庫連接**: 檢查 PostgreSQL 服務是否運行
3. **Python 套件**: 確保安裝了必要的套件 (psycopg2, python-dotenv, asyncpg, aiohttp)
4. **備份**: 建議在大量匯入前備份資料庫

## 相容性

- 所有原有功能保持不變
- 腳本參數和使用方式完全相容
- 只是檔案位置發生變更

## 清理工作

原 scripts 目錄下的相關檔案已移動，但保留了：
- __pycache__ 目錄 (可手動清理)
- 其他非卡片相關的腳本檔案

整理工作已完成，系統可正常使用！
