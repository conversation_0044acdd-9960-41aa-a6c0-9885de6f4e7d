# 【搶劫 & 金庫系統】詳細實施計畫書 (V2.2 - 深度遊戲化整合版)

**核心目標：** 製造風險，懲罰囤積，刺激消費，促進流動 - 從「經濟系統」蛻變為「充滿變數和樂趣的博弈玩法」

**設計理念升級：**
- 🎯 **精準打擊超級富豪** - 財富加權算法確保火力集中在「超級炸彈」身上
- 🔒 **並發安全保障** - 使用數據庫事務和行級鎖防止競態條件
- 🎮 **多層攻防博弈** - 搶劫不再是單純成功率檢定，而是「攻擊道具」對抗「防禦道具」的策略對決
- 🎲 **結果隨機性與趣味性** - 分級隨機結果搭配生動文案，從數據報告變成故事講述
- � **道具驅動策略** - 玩家策略核心從「存錢/花錢」轉變為「買什麼道具來攻擊/防禦」
- 🎭 **沉浸式體驗** - 每次搶劫都是一個小故事，有緊張、有驚喜、有戲劇性

**總體架構圖 (Mermaid):**
```mermaid
flowchart TD
    subgraph Database Layer
        A[gacha_users Table]
        B[robbery_logs Table]
        C[config/gacha_settings.yaml]
        D[command_usage_stats Table]
    end

    subgraph Service Layer
        E[VaultService]
        F[RobberyService]
    end

    subgraph Command Layer (Discord Cogs)
        G[/vault commands]
        H[/rob commands]
    end

    A -- "新增 vault_balance, vault_level, rob_protection_until" --> E
    B -- "記錄所有搶劫行為" --> F
    C -- "提供系統參數" --> E & F
    D -- "提供活躍度判斷依據" --> F

    E --> G
    F --> H
```

---

## 第一階段：數據庫與配置準備 (Data Layer)

**目標：** 以最少的變更，建立穩固的底層數據結構。

### 1. 數據庫結構變更 (Database Schema)

#### 🔧 必要變更 (Critical Updates)
- **`gacha_users` 表升級**:
    ```sql
    -- 【重要】將 oil_balance 升級為 BIGINT 防止溢出
    ALTER TABLE public.gacha_users ALTER COLUMN oil_balance TYPE BIGINT;

    -- 新增金庫相關欄位
    ALTER TABLE public.gacha_users ADD COLUMN vault_balance BIGINT NOT NULL DEFAULT 0;
    ALTER TABLE public.gacha_users ADD COLUMN vault_level INTEGER NOT NULL DEFAULT 1;
    ALTER TABLE public.gacha_users ADD COLUMN rob_protection_until TIMESTAMP WITH TIME ZONE;

    -- 【V2.2 新增】雙軌准入機制支援
    ALTER TABLE public.gacha_users ADD COLUMN wealth_alert_sent BOOLEAN DEFAULT FALSE;

    -- 【V2.2 新增】支援複雜道具效果的 JSONB 欄位
    ALTER TABLE public.gacha_users ADD COLUMN active_effects JSONB NOT NULL DEFAULT '{}'::jsonb;
    /*
    active_effects 儲存範例:
    {
      "stealth_until": "2023-10-28T12:00:00Z",     -- 隱身效果到期時間
      "luck_modifier": 1.2,                        -- 來自威士忌的幸運加成
      "next_rob_is_caipirinha": true,              -- 下次搶劫觸發卡琵莉亞效果
      "vulnerable_until": "2023-10-28T10:00:00Z", -- 來自酒精的易受攻擊狀態
      "anonymous_rob_until": "2023-10-28T15:00:00Z" -- 搶匪面具匿名效果
    }
    */
    ```

- **新增 `robbery_logs` 表**:
    ```sql
    CREATE TABLE public.robbery_logs (
        log_id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
        attacker_id BIGINT NOT NULL REFERENCES public.gacha_users(user_id),
        target_id BIGINT NOT NULL REFERENCES public.gacha_users(user_id),
        was_successful BOOLEAN NOT NULL,
        amount_stolen BIGINT NOT NULL,
        amount_gained BIGINT NOT NULL,
        amount_burned BIGINT NOT NULL,
        failure_penalty BIGINT DEFAULT 0,  -- 失敗懲罰金額

        -- 【V2.2 新增】遊戲化細節記錄
        rob_tier VARCHAR(20),              -- 'TINY', 'SMALL', 'DECENT', 'MAX', 'CAIPIRINHA'
        failure_reason VARCHAR(50),        -- 'CAUGHT', 'PADLOCK_BLOCKED', 'LANDMINE_HIT'
        attacker_items_used JSONB,         -- 攻擊方消耗的道具
        target_items_triggered JSONB,      -- 目標觸發的防禦道具
        is_anonymous BOOLEAN DEFAULT FALSE, -- 是否為匿名搶劫

        attacker_balance_after BIGINT,
        target_balance_after BIGINT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    ```

- **新增 `user_inventory` 表** (道具庫存系統):
    ```sql
    CREATE TABLE public.user_inventory (
        user_id BIGINT NOT NULL REFERENCES public.gacha_users(user_id) ON DELETE CASCADE,
        item_id VARCHAR(50) NOT NULL, -- 'padlock', 'bolt_cutter', 'landmine', 'alcohol', etc.
        quantity INTEGER NOT NULL DEFAULT 1,
        is_active BOOLEAN DEFAULT FALSE, -- 某些道具需要激活狀態 (如掛鎖、地雷)
        expires_at TIMESTAMP WITH TIME ZONE, -- 道具到期時間 (可選)
        PRIMARY KEY (user_id, item_id)
    );
    ```

#### 📊 性能優化索引
```sql
-- 搶劫目標查詢優化
CREATE INDEX idx_gacha_users_rob_targets ON public.gacha_users(oil_balance, rob_protection_until)
WHERE oil_balance > 100000;

-- 搶劫日誌查詢優化
CREATE INDEX idx_robbery_logs_attacker_time ON public.robbery_logs(attacker_id, created_at);
CREATE INDEX idx_robbery_logs_target_time ON public.robbery_logs(target_id, created_at);

-- 活躍度查詢優化 (利用現有的 command_usage_stats)
CREATE INDEX idx_command_usage_recent ON public.command_usage_stats(user_id, used_at)
WHERE used_at > CURRENT_TIMESTAMP - INTERVAL '7 days';
```

### 2. 系統配置 (`config/gacha_settings.yaml`)

**【V2.3 數值平衡更新】基於經濟數據分析的精確調整**

根據 `gacha_analysis.md` 的經濟數據分析，我們對金庫系統進行了精確的數值平衡：

- **玩家基礎收入**: 每日 21,000 油幣 (5,000 每日 + 16,000 每時獎勵)
- **抽卡期望值**: 所有卡池 EV/Cost 在 44%-68% 之間，為穩定的貨幣回收機制
- **經濟模型**: 健康的通縮模型，油幣主要來源是系統發放，主要消耗是抽卡和小遊戲

```yaml
robbery_system:
  vault:
    base_deposit_fee: 0.03  # 基礎存款手續費 3%
    max_capacity_multiplier: 100000  # 金庫容量 = vault_level * multiplier

    # 【V2.4 革命性重構】反向設計 - 成本跟隨收益價值
    # 拋棄固定倍率模型，改用手工調校的成本表，確保投入產出比始終合理
    # 詳細成本表請參考下方 get_upgrade_cost_v2() 函數實現

    fee_reduction_per_level: 0.002  # 每級減少 0.2% 手續費

    # 【V2.3 革命性升級】動態安全門檻系統
    # 取代固定的 threshold_increase_per_vault_level，改用分段增長模型

  rob:
    enabled: true

    # 【V2.2 重構】雙軌准入機制 - 尊重玩家風格
    attacker_requirements:
      min_gacha_count: 1000      # 抽卡流：總抽卡次數 >= 1000
      min_game_count: 1000       # 遊戲流：小遊戲總遊玩次數 >= 1000
      # 滿足任一條件即可發起搶劫

    target_requirements:
      base_security_threshold: 100000      # 基礎安全門檻
      # 【V2.3 重要變更】移除固定的 threshold_increase_per_vault_level
      # 改用 Python 函數實現動態分段增長模型 (詳見下方實現)

    # 【V2.2 升級】多層次搶劫機制
    base_success_rate: 0.60  # 基礎成功率 60% (降低以平衡道具效果)
    rob_cost: 10000  # 發起搶劫的固定成本
    failure_penalty_rate: 0.05  # 失敗懲罰：現金的 5%
    max_steal_amount: 10000000  # 單次搶劫上限 1000萬
    protection_hours: 24  # 被搶後保護時間

    # 【V2.2 新增】動態掉落機制 (取代固定 burn_rate)
    loot_drop:
      no_drop_chance: 0.30      # 30% 機率完全沒掉落
      min_drop_rate: 0.05       # 最少掉落 5%
      max_drop_rate: 0.35       # 最多掉落 35%

    # 【V2.2 新增】分級搶劫結果
    rob_tiers:
      TINY:
        percentage: 0.10
        weight: 40
        message: "偷走了一小部分"
      SMALL:
        percentage: 0.20
        weight: 30
        message: "搶到了不少錢"
      DECENT:
        percentage: 0.40
        weight: 25
        message: "大獲全勝"
      MAX:
        percentage: 0.75
        weight: 5
        message: "席捲了一切"

    # 財富加權算法
    wealth_weight_formula: "log10"  # 使用 log10(cash) 作為權重

  # 【V2.2 重構】道具系統完整配置
  items:
    # 防禦道具
    padlock:
      type: "defense"
      price: 50000
      duration_hours: 24
      description: "🔒 安全掛鎖 - 阻止搶劫，需要斷線鉗才能破解"

    landmine:
      type: "defense_consumable"
      price: 80000
      description: "💣 地雷 - 一次性防禦，觸發時使攻擊者失敗"

    # 攻擊道具
    bolt_cutter:
      type: "attack_consumable"
      price: 30000
      description: "✂️ 斷線鉗 - 一次性消耗，可破解掛鎖"

    # 增益道具
    alcohol:
      type: "buff_debuff"
      price: 25000
      luck_modifier: 1.1
      vulnerable_hours: 2
      description: "🍺 酒精 - 增加搶劫運氣但自己也更容易被搶"

    whiskey:
      type: "buff"
      price: 60000
      luck_modifier: 1.15
      duration_hours: 1
      description: "🥃 威士忌 - 純增益，提升搶劫運氣無副作用"

    caipirinha:
      type: "high_risk"
      price: 100000
      description: "🍹 卡琵莉亞雞尾酒 - 50%機率大成功，50%機率慘敗"

    robbers_mask:
      type: "utility"
      price: 40000
      duration_hours: 6
      description: "🎭 搶匪面具 - 匿名搶劫，對方不知道是誰搶的"
```

---

## 【V2.3 專章】數值平衡深度分析

### 📊 經濟數據基礎分析

基於 `gacha_analysis.md` 的詳細數據，我們建立了以下經濟模型：

#### 玩家收入能力評估
```
每日基礎收入 = 5,000 (每日獎勵) + 16,000 (每時獎勵 × 8小時) = 21,000 油幣/天
每週基礎收入 = 21,000 × 7 = 147,000 油幣/週
每月基礎收入 = 21,000 × 30 = 630,000 油幣/月
```

#### 抽卡消費模式分析
```
混合池 EV/Cost = 66.74% (最受歡迎的卡池)
每日全投入抽卡 = 21,000 ÷ 50 = 420 次抽卡
期望回收 = 21,000 × 66.74% = 14,015 油幣
淨消耗 = 21,000 - 14,015 = 6,985 油幣/天
```

**結論**: 一個純 F2P 玩家每天淨消耗約 7,000 油幣，這是一個健康的通縮模型。

### ⚖️ 【V2.5 硬核模式】金庫升級風險分析

我們將完全圍繞**「升級費用必須且只能來自錢包餘額」**這一核心規則，來重新構建和分析金庫的1-30級結構。
這份表格將專注於新規則下，玩家在每個升級階段所面臨的**真實風險敞口**。

---

#### 【金庫系統 1-30 級結構分析表 (硬核模式)】

**核心規則：** 升級金庫所需的全額費用，必須存放在**錢包**中。金庫餘額在升級過程中不起任何作用。

| 等級 (LV) | 安全門檻 (油幣) | 金庫容量 (油幣) | 升級成本 (油幣) | **錢包必須持有的<br/>升級資金** | **暴露在風險中的<br/>升級資金**<br/>(錢包持有 - 安全門檻) | **風險資金占<br/>升級總款比例** | 階段評估與玩家心理 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **1** | 100,000 | 100,000 | 200,000 | 200,000 | **100,000** | 50.0% | **第一步的考驗**：即便最基礎的升級，也要求玩家承擔50%的風險。 |
| **2** | 120,000 | 118,000 | 244,000 | 244,000 | **124,000** | 50.8% | |
| **3** | 140,000 | 140,000 | 295,000 | 295,000 | **155,000** | 52.5% | |
| **4** | 160,000 | 165,000 | 354,000 | 354,000 | **194,000** | 54.8% | |
| **5** | 180,000 | 194,000 | 424,000 | 424,000 | **244,000** | 57.5% | |
| **6** | 200,000 | 229,000 | 508,000 | 508,000 | **308,000** | 60.6% | **血與淚的成長期**：風險比例超過60%，每一次儲蓄都是對心態的磨練。 |
| **7** | 220,000 | 271,000 | 608,000 | 608,000 | **388,000** | 63.8% | |
| **8** | 240,000 | 319,000 | 728,000 | 728,000 | **488,000** | 67.0% | |
| **9** | 260,000 | 377,000 | 873,000 | 873,000 | **613,000** | 70.2% | |
| **10** | 280,000 | 445,000 | 1,048,000 | 1,048,000 | **768,000** | 73.3% | **第一道天塹**：錢包需破百萬，其中近77萬是純粹的風險。 |
| **11** | 330,000 | 525,000 | 1,259,000 | 1,259,000 | **929,000** | 73.8% | **地獄難度的開端**：安全門檻的提升，遠跟不上成本的膨脹。 |
| **12** | 380,000 | 619,000 | 1,514,000 | 1,514,000 | **1,134,000** | 74.9% | |
| **13** | 430,000 | 731,000 | 1,822,000 | 1,822,000 | **1,392,000** | 76.4% | |
| **14** | 480,000 | 862,000 | 2,194,000 | 2,194,000 | **1,714,000** | 78.1% | |
| **15** | 530,000 | 1,018,000 | 2,644,000 | 2,644,000 | **2,114,000** | 79.9% | |
| **16** | 580,000 | 1,201,000 | 3,188,000 | 3,188,000 | **2,608,000** | 81.8% | |
| **17** | 630,000 | 1,417,000 | 3,848,000 | 3,848,000 | **3,218,000** | 83.6% | |
| **18** | 680,000 | 1,672,000 | 4,648,000 | 4,648,000 | **3,968,000** | 85.4% | |
| **19** | 730,000 | 1,973,000 | 5,620,000 | 5,620,000 | **4,890,000** | 87.0% | |
| **20** | 780,000 | 2,328,000 | 6,798,000 | 6,798,000 | **6,018,000** | 88.5% | **第二道天塹**：暴露的風險資金高達600萬，成為全服焦點。 |
| **21** | 900,000 | 2,747,000 | 8,228,000 | 8,228,000 | **7,328,000** | 89.1% | **神之試煉**：風險比例接近90%，每分每秒都是煎熬。 |
| **22** | 1,020,000 | 3,242,000 | 9,960,000 | 9,960,000 | **8,940,000** | 89.8% | |
| **23** | 1,140,000 | 3,825,000 | 12,058,000 | 12,058,000 | **10,918,000** | 90.5% | |
| **24** | 1,260,000 | 4,514,000 | 14,598,000 | 14,598,000 | **13,338,000** | 91.4% | |
| **25** | 1,380,000 | 5,326,000 | 17,670,000 | 17,670,000 | **16,290,000** | 92.2% | |
| **26** | 1,500,000 | 6,285,000 | 21,382,000 | 21,382,000 | **19,882,000** | 93.0% | |
| **27** | 1,620,000 | 7,416,000 | 25,872,000 | 25,872,000 | **24,252,000** | 93.7% | |
| **28** | 1,740,000 | 8,751,000 | 31,312,000 | 31,312,000 | **29,572,000** | 94.4% | |
| **29** | 1,860,000 | 10,326,000 | 41,304,000 | 41,304,000 | **39,444,000** | 95.5% | **封神之路**：向全服宣告你要攜帶4100萬現金，其中超過95%是可被掠奪的。 |
| **30** | **1,980,000** | **14,141,000** | - | - | - | - | **統治的開始**：登神成功。你的存在定義了新的頂級捕食者。 |

---

#### 表格數據解讀與分析 (硬核模式)

1.  **金庫容量的角色轉變：**
    在新規則下，金庫容量不再是升級過程中的「緩衝區」，它的作用變得極其純粹：**它只決定了玩家在「非升級期間」能安全囤積多少財富**。它是一個靜態的避風港，與動態的、充滿風險的升級過程完全脫鉤。

2.  **風險的壓倒性：**
    表格中的 **「風險資金占升級總款比例」** 這一列觸目驚心。從 LV6 開始，玩家超過 60% 的升級款都暴露在風險中；到 LV10 超過 73%；到 LV20 接近 90%；最終在 LV29 時達到**驚人的 95.5%**。
    這意味著，安全門檻提供的保護，在龐大的升級成本面前，幾乎可以忽略不計。玩家的心理感受會從「我的錢有一部分是安全的」，徹底轉變為**「我的錢幾乎全部是危險的」**。

3.  **重新定義「財富」：**
    這個系統重新定義了財富的意義。
    *   **金庫裡的錢** 是「資產」，代表了你過去的榮耀。
    *   **錢包裡的錢** 是「賭注」，代表了你對未來的野心。
    *   真正的強者，不是看他金庫有多少錢，而是看他**敢在錢包裡放多少錢，並能守住多久**。

4.  **系統的內在邏輯：**
    這個硬核模式下的系統，其內在邏輯鏈條是：
    *   **渴望安全 -> 必須升級 -> 必須在錢包積攢巨款 -> 創造巨大風險 -> 刺激搶劫與道具消費 -> 驅動社交抱團 -> 完成升級，獲得短暫安全 -> 為了更安全，開啟下一輪更高風險的循環。**

這是一個設計上極度自洽、玩法上極度殘酷、體驗上極度刺激的閉環。它完美地服務於您所追求的**高強度、高衝突的PVP環境**。

### 🎯 道具定價協調性

#### 道具價格與金庫成本的戰略關係
```
掛鎖價格 = 50,000 油幣
LV1→2 金庫成本 = 50,000 油幣

玩家選擇:
選項A: 花 50k 買掛鎖 → 獲得 24 小時臨時保護
選項B: 花 50k 升級金庫 → 獲得永久 +20k 安全門檻

戰略意義: 這種等價選擇創造了有趣的策略決策點
```

#### 其他道具價格驗證
```
斷線鉗 = 30,000 (約 1.4 天收入) - 攻擊道具，合理
地雷 = 80,000 (約 3.8 天收入) - 一次性防禦，偏高但合理
威士忌 = 60,000 (約 2.9 天收入) - 純增益，價格適中
卡琵莉亞 = 100,000 (約 4.8 天收入) - 高風險高回報，價格合理
```


## 第二階段：金庫系統實作 (The Vault)

**目標：** 推出革命性的金庫系統 - 從「倉庫」升級為「安全核心」，提供永久性的安全門檻提升。

### 🏰 【V2.2 革命性升級】金庫 = 玩家的安全堡壘

#### 1. 核心理念轉變
**舊思維**: 金庫是存錢的地方
**新思維**: 金庫是玩家的安全等級，是「家」、是「堡壘」

#### 2. 【V2.4 最終版】動態分段安全門檻系統

**設計理念**: 建立可控、平滑的收益曲線，作為反向設計成本的「錨點」和「金標準」。

```python
def get_security_threshold(vault_level: int) -> int:
    """【V2.4 最終版】動態分段安全門檻計算 - 可控的分段線性增長"""
    base_threshold = 100000  # 基礎門檻
    increase = 0

    # 分段式線性增長模型 - 數值可控，體驗平滑
    # LV 1-10: 每級 +20,000 (早期穩定成長)
    if vault_level > 1:
        increase += min(9, vault_level - 1) * 20000

    # LV 11-20: 每級 +50,000 (中期跳躍式提升)
    if vault_level > 10:
        increase += min(10, vault_level - 10) * 50000

    # LV 21-30: 每級 +120,000 (後期巨幅保障)
    if vault_level > 20:
        increase += min(10, vault_level - 20) * 120000

    return base_threshold + increase



def is_robbery_target(user_balance: int, vault_level: int) -> bool:
    """判斷玩家是否會成為搶劫目標"""
    security_threshold = get_security_threshold(vault_level)
    return user_balance > security_threshold
```

**收益曲線優勢**:
- **數值可控**: 最高保護近200萬，不會無限膨脹
- **體驗平滑**: 沒有巨大的數值斷層，每級都有意義
- **階段明確**: 三個清晰的成長階段，給玩家明確的目標感
- **跳躍驚喜**: LV11 和 LV21 的收益跳躍帶來巨大滿足感

#### 3. 完整的金庫功能系統
```python
def get_vault_capacity(vault_level: int) -> int:
    """計算金庫容量"""
    return vault_level * settings.vault.max_capacity_multiplier

def get_deposit_fee_rate(vault_level: int) -> float:
    """計算存款手續費率"""
    base_fee = settings.vault.base_deposit_fee
    reduction = (vault_level - 1) * settings.vault.fee_reduction_per_level
    return max(0.01, base_fee - reduction)  # 最低 1% 手續費

def get_upgrade_cost(current_level: int) -> int:
    """【V2.4 革命性重構】反向設計的成本表 - 成本跟隨收益價值"""

    # 【V2.5 更新】手工調校的成本表，確保投入產出比始終合理
    # 基於詳細的金庫等級設計，每個等級都有對應的升級標題和敘述
    cost_table = {
        2: 200000,      # 社區巡邏隊
        3: 244000,      # 持證保安
        4: 295000,      # 退役警犬
        5: 354000,      # 本地安保公司
        6: 424000,     # 前線退伍老兵
        7: 508000,     # 監控中心
        8: 608000,     # 防彈玻璃與加固牆體
        9: 728000,     # 快速反應小組 (QRT)
        10: 873000,    # 資深安保顧問
        11: 1048000,   # 網路安全部門
        12: 1259000,   # 紅外線與壓力感應器
        13: 1514000,   # 聲波防禦系統
        14: 1822000,   # 情報分析師團隊
        15: 2194000,   # 精英特種部隊 (Tier 2)
        16: 2644000,   # 無人機空中巡邏
        17: 3188000,   # 電磁脈衝 (EMP) 防護
        18: 3848000,   # 全球安全運營中心 (GSOC)
        19: 4648000,   # 私人軍事承包商 (PMC)
        20: 5620000,   # 「幽靈」協議
        21: 6798000,   # 衛星監控權限
        22: 8228000,   # 人工智能防禦矩陣
        23: 9960000,   # 外交豁免權
        24: 12058000,  # 地下深層掩體 (D.U.M.B.)
        25: 14598000,  # 「守護神」計劃
        26: 17670000,  # 軌道動能武器防禦系統
        27: 21382000,  # 近地軌道空間站
        28: 25872000,  # 因果律武器（傳聞）
        29: 31312000,  # 都市傳說
        30: 41304000   # 絕對安全 (MAX)
    }

    return cost_table.get(current_level + 1, 0)  # current_level 是當前等級，+1 是升級目標

# 【V2.4 成本收益匹配表】- 核心設計理念的體現
# 等級 | 安全門檻 | 單級提升 | 升級成本 | 成本/收益倍數 | 設計理念
# LV2  | 120,000  | +20,000  | 40,000   | 2x          | 新手友好，鼓勵參與
# LV10 | 280,000  | +20,000  | 250,000  | 12.5x       | 第一階段完成
# LV11 | 330,000  | +50,000  | 500,000  | 10x         | 收益跳躍，倍數回落！
# LV20 | 780,000  | +50,000  | 4,000,000| 80x         | 第二階段完成
# LV21 | 900,000  | +120,000 | 10,000,000| 83x        | 再次跳躍，倍數穩定！
# LV30 | 1,980,000| +120,000 | 100,000,000| 833x      | 終極目標

def get_total_investment_to_level(target_level: int) -> int:
    """計算升級到指定等級的總投資"""
    total = 0
    for level in range(2, target_level + 1):
        total += get_upgrade_cost(level - 1)  # level-1 是當前等級
    return total

# 【V2.4 總投資分析】
# 升到 LV10 總成本: 4,234,000 (約 423萬)
# 升到 LV20 總成本: 約 32,019,000 (約 3202萬)
# 升滿 LV30 總成本: 約 221,201,000 (約 2.2億)

def get_vault_upgrade_benefits(current_level: int) -> Dict:
    """獲取升級後的所有收益 - 用於激勵玩家"""
    next_level = current_level + 1

    return {
        "security_threshold": {
            "current": get_security_threshold(current_level),
            "next": get_security_threshold(next_level),
            "increase": get_security_threshold(next_level) - get_security_threshold(current_level)
        },
        "capacity": {
            "current": get_vault_capacity(current_level),
            "next": get_vault_capacity(next_level)
        },
        "fee_rate": {
            "current": get_deposit_fee_rate(current_level),
            "next": get_deposit_fee_rate(next_level)
        },
        "upgrade_cost": get_upgrade_cost(current_level)
    }
```

#### 4. 【V2.2 升級】金庫指令設計
- `/vault info` - 查看金庫狀態、安全門檻、容量、手續費率
- `/vault deposit <amount>` - 存錢到金庫 (扣除手續費)
- `/vault withdraw <amount>` - 從金庫提錢 (免費)
- `/vault upgrade` - 升級金庫等級 (提升安全門檻、容量、降低費用)

#### 5. 故事化的金庫指令實現
```python
@app_commands.command(name="info", description="查看金庫狀態與安全等級")
async def vault_info(self, interaction: discord.Interaction):
    user = await self.get_user(interaction.user.id)
    benefits = get_vault_upgrade_benefits(user.vault_level)

    embed = discord.Embed(title="🏰 你的安全堡壘", color=0x3498db)

    # 核心賣點：安全門檻
    embed.add_field(
        name="🛡️ 當前安全等級",
        value=f"**LV {user.vault_level}** 金庫\n"
              f"安全門檻: **{benefits['security_threshold']['current']:,}** 油幣\n"
              f"*隨身現金低於此數額時，你將不會成為搶劫目標*",
        inline=False
    )

    # 升級誘惑
    if user.vault_level < 50:  # 假設最高等級
        embed.add_field(
            name="⬆️ 升級收益預覽",
            value=f"升級至 **LV {user.vault_level + 1}**:\n"
                  f"🛡️ 安全門檻 +{benefits['security_threshold']['increase']:,}\n"
                  f"📦 容量 +{benefits['capacity']['next'] - benefits['capacity']['current']:,}\n"
                  f"💰 手續費 -{(benefits['fee_rate']['current'] - benefits['fee_rate']['next'])*100:.1f}%\n"
                  f"💸 升級成本: {benefits['upgrade_cost']:,} 油幣",
            inline=False
        )

    # 其他信息
    embed.add_field(name="💰 金庫餘額", value=f"{user.vault_balance:,} 油幣", inline=True)
    embed.add_field(name="📦 容量", value=f"{benefits['capacity']['current']:,} 油幣", inline=True)
    embed.add_field(name="💳 手續費", value=f"{benefits['fee_rate']['current']:.1%}", inline=True)

    await interaction.response.send_message(embed=embed)

@app_commands.command(name="upgrade", description="升級金庫 - 提升安全等級")
async def vault_upgrade(self, interaction: discord.Interaction):
    user = await self.get_user(interaction.user.id)
    upgrade_cost = get_upgrade_cost(user.vault_level)

    # 【V2.5 硬核模式】升級的代價 - 風險與機遇並存
    # 升級費用必須從錢包支付，考驗玩家積累財富並承擔風險的能力。

    if user.oil_balance < upgrade_cost:
        return await interaction.response.send_message(
            f"❌ 升級失敗！需要 {upgrade_cost:,} 油幣\n"
            f"你的錢包餘額: {user.oil_balance:,} 油幣",
            ephemeral=True
        )

    # 確認支付方式
    embed = discord.Embed(title="⚔️ 升級挑戰確認", color=0xffa500)
    embed.description = f"升級至 **LV {user.vault_level + 1}** 需要 **{upgrade_cost:,}** 油幣\n\n" \
                       f"**警告：** 升級費用將從你的 **錢包** 中扣除。\n" \
                       f"在積攢足夠資金的過程中，你將完全暴露在風險之下。"

    embed.add_field(name="💳 支付方式", value="💼 錢包直接支付", inline=True)
    embed.add_field(name="💰 支付金額", value=f"{upgrade_cost:,} 油幣", inline=True)
    embed.add_field(name="⚠️ 風險提示", value="⚠️ 100% 風險", inline=True)

    # 添加確認按鈕
    view = VaultUpgradeConfirmView(user.user_id, upgrade_cost)
    embed.set_footer(text="點擊下方按鈕確認接受挑戰")

    await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

class VaultUpgradeConfirmView(discord.ui.View):
    """金庫升級確認視圖"""

    def __init__(self, user_id: int, upgrade_cost: int):
        super().__init__(timeout=300)  # 5分鐘超時
        self.user_id = user_id
        self.upgrade_cost = upgrade_cost

    @discord.ui.button(label='✅ 確認升級', style=discord.ButtonStyle.green, emoji='🏰')
    async def confirm_upgrade(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("❌ 只有發起者可以確認升級", ephemeral=True)
            return

        try:
            # 執行原子性升級操作
            result = await self.execute_safe_upgrade(interaction.user.id)

            if result['success']:
                embed = discord.Embed(title="🏰 金庫升級成功！", color=0x00ff00)
                embed.description = f"**LV {result['old_level']} → LV {result['new_level']}**\n\n" \
                                   f"🛡️ 你雇傭了「**{result['guard_title']}**」，他們將誓死保衛你的財產！\n" \
                                   f"你的江湖地位得到了顯著提升。"

                embed.add_field(
                    name="🔒 安全門檻提升",
                    value=f"{result['old_threshold']:,} → **{result['new_threshold']:,}** 油幣\n"
                          f"*（提升了 {result['threshold_increase']:,} 的安全保障）*",
                    inline=False
                )

                embed.add_field(name="💰 支付詳情", value=result['payment_details'], inline=False)
                embed.set_footer(text="💡 只有身懷超過安全門檻的巨款，才會引起頂級大盜的注意")

                # 禁用所有按鈕
                for item in self.children:
                    item.disabled = True

                await interaction.response.edit_message(embed=embed, view=self)
            else:
                await interaction.response.send_message(f"❌ 升級失敗：{result['error']}", ephemeral=True)

        except Exception as e:
            logger.error(f"金庫升級執行錯誤: {e}")
            await interaction.response.send_message("❌ 升級過程中發生錯誤，請稍後再試", ephemeral=True)

    @discord.ui.button(label='❌ 取消', style=discord.ButtonStyle.grey, emoji='🚫')
    async def cancel_upgrade(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("❌ 只有發起者可以取消", ephemeral=True)
            return

        embed = discord.Embed(title="🚫 升級已取消", description="金庫升級操作已取消", color=0x666666)

        # 禁用所有按鈕
        for item in self.children:
            item.disabled = True

        await interaction.response.edit_message(embed=embed, view=self)

    async def execute_safe_upgrade(self, user_id: int) -> Dict:
        """執行金庫升級 - 原子性操作"""

        async with db_pool.acquire() as conn:
            async with conn.transaction():
                # 重新獲取最新用戶數據（防止併發問題）
                user = await conn.fetchrow(
                    "SELECT * FROM gacha_users WHERE user_id = $1 FOR UPDATE", user_id
                )

                if not user:
                    return {"success": False, "error": "用戶不存在"}

                # 驗證資金充足性 (只檢查錢包)
                if user['oil_balance'] < self.upgrade_cost:
                    return {"success": False, "error": "錢包餘額不足"}

                old_level = user['vault_level']
                new_level = old_level + 1
                old_threshold = get_security_threshold(old_level)
                new_threshold = get_security_threshold(new_level)

                # 計算新的餘額 (只從錢包扣除)
                new_oil_balance = user['oil_balance'] - self.upgrade_cost
                payment_details = f"💼 錢包支付: {self.upgrade_cost:,} 油幣"

                # 執行升級 (只更新 vault_level 和 oil_balance)
                await conn.execute("""
                    UPDATE gacha_users
                    SET vault_level = $1, oil_balance = $2
                    WHERE user_id = $3
                """, new_level, new_oil_balance, user_id)

                # 【V2.5 更新】生成守護者稱號 - 基於詳細的金庫等級設計
                # 直接使用升級標題作為守護者稱號
                level_titles = {
                    1: "基礎金庫", 2: "社區巡邏隊", 3: "持證保安", 4: "退役警犬", 5: "本地安保公司",
                    6: "前線退伍老兵", 7: "監控中心", 8: "防彈玻璃與加固牆體", 9: "快速反應小組 (QRT)", 10: "資深安保顧問",
                    11: "網路安全部門", 12: "紅外線與壓力感應器", 13: "聲波防禦系統", 14: "情報分析師團隊", 15: "精英特種部隊 (Tier 2)",
                    16: "無人機空中巡邏", 17: "電磁脈衝 (EMP) 防護", 18: "全球安全運營中心 (GSOC)", 19: "私人軍事承包商 (PMC)", 20: "「幽靈」協議",
                    21: "衛星監控權限", 22: "人工智能防禦矩陣", 23: "外交豁免權", 24: "地下深層掩體 (D.U.M.B.)", 25: "「守護神」計劃",
                    26: "軌道動能武器防禦系統", 27: "近地軌道空間站", 28: "因果律武器（傳聞）", 29: "都市傳說", 30: "絕對安全 (MAX)"
                }

                guard_title = level_titles.get(new_level, "未知守護者")



                return {
                    "success": True,
                    "old_level": old_level,
                    "new_level": new_level,
                    "old_threshold": old_threshold,
                    "new_threshold": new_threshold,
                    "threshold_increase": new_threshold - old_threshold,
                    "guard_title": guard_title,
                    "payment_details": payment_details
                }



### � 【V2.5 完整金庫等級詳細表】

以下是完整的金庫等級系統，包含每個等級的升級標題、敘述和安全門檻：

| 等級 | 升級標題 | 升級敘述 | 金庫容量 (Capacity) | 安全門檻 (油幣) | 升級成本 (油幣) |
|------|----------|----------|----------------------|-----------------|-----------------|
| 1 | 基礎金庫 | 只有一把脆弱的鎖，聊勝於無。 | 100,000 | 100,000 | - |
| 2 | 社區巡邏隊 | 你雇了幾個街坊鄰居晚上幫忙看門，至少能製造點響動。 | 118,000 | 120,000 | 200,000 |
| 3 | 持證保安 | 穿著制服的保安開始在門口站崗，看起來像那麼回事了。 | 140,000 | 140,000 | 244,000 |
| 4 | 退役警犬 | 一條經驗豐富的德國牧羊犬加入了你的隊伍，它的嗅覺比任何人都靈敏。 | 165,000 | 160,000 | 295,000 |
| 5 | 本地安保公司 | 你與一家本地安保公司簽訂了合同，他們提供 24 小時的輪班監控。 | 194,000 | 180,000 | 354,000 |
| 6 | 前線退伍老兵 | 團隊的核心換成了一群經驗豐富的退伍軍人，他們懂得如何應對真正的威脅。 | 229,000 | 200,000 | 424,000 |
| 7 | 監控中心 | 一個掛滿螢幕的監控中心落成，無數個攝像頭覆蓋了所有角落。 | 271,000 | 220,000 | 508,000 |
| 8 | 防彈玻璃與加固牆體 | 你的金庫進行了物理加固，普通的爆破已無法撼動。 | 319,000 | 240,000 | 608,000 |
| 9 | 快速反應小組 (QRT) | 一支裝備精良的快速反應小組在附近待命，能在數分鐘內抵達現場。 | 377,000 | 260,000 | 728,000 |
| 10 | 資深安保顧問 | 一位前特勤局的資深顧問為你設計了天衣無縫的防禦體系。 | 445,000 | 280,000 | 873,000 |
| 11 | 網路安全部門 | 你意識到威脅來自線上，一個白帽駭客團隊開始保護你的數字資產。 | 525,000 | 330,000 | 1,048,000 |
| 12 | 紅外線與壓力感應器 | 金庫走廊佈滿了肉眼不可見的紅外線網和壓力感應地板，任何入侵都會觸發警報。 | 619,000 | 380,000 | 1,259,000 |
| 13 | 聲波防禦系統 | 部署了非致命性聲波武器，能在不造成傷害的情況下，讓入侵者瞬間失去行動能力。 | 731,000 | 430,000 | 1,514,000 |
| 14 | 情報分析師團隊 | 一個情報團隊開始為你工作，預測潛在的威脅，將危險扼殺在搖籃中。 | 862,000 | 480,000 | 1,822,000 |
| 15 | 精英特種部隊 (Tier 2) | 你的核心護衛隊換成了前海豹或三角洲部隊級別的精英，他們是行動的藝術家。 | 1,018,000 | 530,000 | 2,194,000 |
| 16 | 無人機空中巡邏 | 裝配有熱成像儀的靜音無人機群開始 24/7 不間斷地在你的領地上空巡邏。 | 1,201,000 | 580,000 | 2,644,000 |
| 17 | 電磁脈衝 (EMP) 防護 | 整個設施被法拉第籠包裹，能抵禦企圖癱瘓你電子系統的電磁脈衝攻擊。 | 1,417,000 | 630,000 | 3,188,000 |
| 18 | 全球安全運營中心 (GSOC) | 你在海外建立了一個備用安全中心，實現了全球化的風險分散和指揮。 | 1,672,000 | 680,000 | 3,848,000 |
| 19 | 私人軍事承包商 (PMC) | 你不再滿足於單純的防禦，開始與一家頂級的私人軍事公司（如 Academi）合作。 | 1,973,000 | 730,000 | 4,648,000 |
| 20 | 「幽靈」協議 | 你啟動了傳說中的「幽靈」協議，能在緊急情況下，讓你的核心資產在物理和數字層面徹底消失。 | 2,328,000 | 780,000 | 5,620,000 |
| 21 | 衛星監控權限 | 你通過特殊渠道獲得了調用商業間諜衛星的權限，能監控全球任何角落。 | 2,747,000 | 900,000 | 6,798,000 |
| 22 | 人工智能防禦矩陣 | 一個超級 AI 接管了整個防禦系統，它的運算和反應速度遠超人類極限。 | 3,242,000 | 1,020,000 | 8,228,000 |
| 23 | 外交豁免權 | 你的影響力為你帶來了某個小國的外交豁免權，你的資產受到國際法的保護。 | 3,825,000 | 1,140,000 | 9,960,000 |
| 24 | 地下深層掩體 (D.U.M.B.) | 你的金庫主體被轉移到了地表數百米之下的深層軍事掩體中，能抵禦核打擊。 | 4,514,000 | 1,260,000 | 12,058,000 |
| 25 | 「守護神」計劃 | 你資助了一個由頂尖科學家組成的秘密團隊，他們的研究成果只為你一人服務。 | 5,326,000 | 1,380,000 | 14,598,000 |
| 26 | 軌道動能武器防禦系統 | 你部署了能攔截來自太空的軌道動能武器的防禦系統，以防萬一。 | 6,285,000 | 1,500,000 | 17,670,000 |
| 27 | 近地軌道空間站 | 你擁有了一個私人空間站，作為最終的資產備份和避難所。 | 7,416,000 | 1,620,000 | 21,382,000 |
| 28 | 因果律武器（傳聞） | 有傳言說，你的安保系統已經超越了物理層面，開始干涉概率和因果。 | 8,751,000 | 1,740,000 | 25,872,000 |
| 29 | 都市傳說 | 你的名字已經成為了一個傳說。沒人知道你的財富在哪，甚至不確定你是否真實存在。 | 10,326,000 | 1,860,000 | 31,312,000 |
| 30 | 絕對安全 (MAX) | 你就是規則本身。你的存在定義了「安全」，搶劫你已成為一個哲學概念，而非技術問題。 | 14,141,000 | 1,980,000 | 41,304,000 |



###🔧 技術實現要點

#### 1. 併發安全保障
```python
async with conn.transaction():
    # 使用 FOR UPDATE 鎖定用戶記錄
    user = await conn.fetchrow(
        "SELECT * FROM gacha_users WHERE user_id = $1 FOR UPDATE", user_id
    )
    # 執行升級操作...
```

#### 2. 數據一致性檢查
```python
# 重新驗證資金充足性（防止併發修改）
total_available = user['oil_balance'] + user['vault_balance']
if total_available < upgrade_cost:
    return {"success": False, "error": "資金不足"}
```

#### 3. 用戶友好的錯誤處理
```python
# 詳細的錯誤信息
if total_available < upgrade_cost:
    return await interaction.response.send_message(
        f"❌ 升級失敗！需要 {upgrade_cost:,} 油幣\n"
        f"你的總資產: {total_available:,} 油幣 (錢包: {oil_balance:,} + 金庫: {vault_balance:,})",
        ephemeral=True
    )
```

### 📊 實施優先級

**核心決策：** 此改動將遊戲的核心從「安全積累」轉變為「高風險投資」。

**實施複雜度：** 中等 - 主要是修改現有的 `/vault upgrade` 指令邏輯，並更新所有相關的UI和文案。

**遊戲體驗影響：** 極高 - 徹底改變玩家的升級策略，大幅增加遊戲的緊張感和PVP衝突。

**結論：** 這個改動使金庫系統成為一個殘酷但極具吸引力的硬核玩法，完全符合高衝突的設計目標。

---

## 第三階段：搶劫系統實作 (The Robbery)

**目標：** 上線核心的搶劫玩法，從單純的成功率檢定升級為多層次的策略博弈系統。

### 🎮 V2.2 搶劫執行流程圖

```mermaid
flowchart TD
    A[開始搶劫 /rob attack] --> B{DB 事務 & 行級鎖}
    B --> C{前置檢查: 成本/冷卻/目標有效性}
    C --> D{讀取雙方 active_effects & inventory}

    D --> E{特殊道具檢定: Caipirinha?}
    E -- 是 --> F{50% 機率檢定}
    E -- 否 --> G{防禦檢定 #1: 目標是否有掛鎖?}

    F -- 成功 --> H[MAX_GUARANTEED 大成功]
    F -- 失敗 --> I[CAIPIRINHA_FAILURE 慘敗]

    G -- 是 --> J{攻擊方是否有斷線鉗?}
    G -- 否 --> K{防禦檢定 #2: 目標是否有地雷?}
    J -- 是 --> K
    J -- 否 --> L[搶劫失敗: PADLOCK_BLOCKED]

    K -- 是 --> M[搶劫失敗: LANDMINE_HIT]
    K -- 否 --> N{核心成功率檢定 (被抓到?)}

    N -- 成功 --> O{計算戰利品等級}
    O --> O1[TINY portion 40%]
    O --> O2[SMALL portion 30%]
    O --> O3[DECENT chunk 25%]
    O --> O4[MAX haul 5%]

    subgraph 成功路徑
        H & O1 & O2 & O3 & O4 --> P{動態掉落檢定}
        P --> P1[30% 完美逃脫]
        P --> P2[70% 隨機掉落 5%-35%]
        P1 & P2 --> Q{更新雙方餘額 & 設定保護期}
        Q --> R[生成故事化成功通知]
    end

    subgraph 失敗路徑
        I & L & M --> S{計算失敗懲罰}
        N -- 失敗 --> S
        S --> T{更新攻擊方餘額}
        T --> U[生成故事化失敗通知]
    end
```

### 🎯 核心邏輯 (`RobberyService`)

#### 1. 【V2.2 重構】雙軌准入的智能目標篩選 (`generate_rob_list()`)
```python
async def generate_rob_list(self, attacker_id: int, limit: int = 10) -> List[Dict]:
    """生成搶劫目標列表 - 雙軌准入 + 財富加權 + 隱身過濾"""

    # 首先驗證攻擊方資格
    if not await self._validate_attacker_eligibility(attacker_id):
        return []

    # 【V2.2 革命性升級】使用動態安全門檻篩選目標
    # 【V2.2 革命性升級】使用與 Python 邏輯完全一致的 SQL 函數進行篩選
    # 這確保了無論是在應用層還是在數據庫層，安全門檻的計算邏輯都是單一且一致的。
    # 前提是在數據庫中已創建 get_security_threshold(level) SQL 函數。
    base_query = """
    SELECT u.user_id, u.oil_balance, u.vault_level,
           LOG10(GREATEST(u.oil_balance, 1)) as weight
    FROM gacha_users u
    WHERE u.oil_balance > get_security_threshold(u.vault_level) -- 調用 SQL 函數
      AND u.user_id != $1  -- 不能搶自己
      AND (u.rob_protection_until IS NULL OR u.rob_protection_until < NOW())  -- 保護期
      AND (u.active_effects->>'stealth_until' IS NULL
           OR (u.active_effects->>'stealth_until')::timestamp < NOW())  -- 隱身狀態
    ORDER BY RANDOM() * weight DESC  -- 財富加權隨機
    LIMIT $2
    """

    return await self.db.fetch(base_query,
        attacker_id,  # $1: 攻擊者ID
        limit         # $2: 限制數量
    )

async def _validate_attacker_eligibility(self, user_id: int) -> bool:
    """驗證攻擊方資格 - 雙軌准入機制"""

    # 查詢抽卡次數
    gacha_count = await self.db.fetchval(
        "SELECT total_gacha_count FROM gacha_users WHERE user_id = $1",
        user_id
    ) or 0

    # 查詢小遊戲總次數
    game_count = await self.db.fetchval(
        "SELECT COALESCE(SUM(total_games), 0) FROM user_game_stats WHERE user_id = $1",
        user_id
    ) or 0

    # 雙軌准入：滿足任一條件即可
    min_gacha = settings.attacker_requirements.min_gacha_count
    min_games = settings.attacker_requirements.min_game_count

    return gacha_count >= min_gacha or game_count >= min_games

async def get_attacker_progress(self, user_id: int) -> Dict:
    """獲取攻擊方資格進度 - 用於顯示給玩家"""

    gacha_count = await self.db.fetchval(
        "SELECT total_gacha_count FROM gacha_users WHERE user_id = $1",
        user_id
    ) or 0

    game_count = await self.db.fetchval(
        "SELECT COALESCE(SUM(total_games), 0) FROM user_game_stats WHERE user_id = $1",
        user_id
    ) or 0

    min_gacha = settings.attacker_requirements.min_gacha_count
    min_games = settings.attacker_requirements.min_game_count

    return {
        "eligible": gacha_count >= min_gacha or game_count >= min_games,
        "gacha_progress": {
            "current": gacha_count,
            "required": min_gacha,
            "percentage": min(100, (gacha_count / min_gacha) * 100)
        },
        "game_progress": {
            "current": game_count,
            "required": min_games,
            "percentage": min(100, (game_count / min_games) * 100)
        }
    }
```

#### 2. 【V2.2 重構】多層次博弈搶劫執行 (`execute_robbery_v2()`)
```python
async def execute_robbery_v2(self, attacker_id: int, target_id: int) -> Dict:
    """V2.2 多層次博弈搶劫系統 - 道具對抗 + 分級結果"""

    async with self.db_pool.acquire() as conn:
        async with conn.transaction():
            # 🔒 鎖定雙方帳戶和道具庫存
            attacker = await conn.fetchrow(
                "SELECT * FROM gacha_users WHERE user_id = $1 FOR UPDATE", attacker_id
            )
            target = await conn.fetchrow(
                "SELECT * FROM gacha_users WHERE user_id = $1 FOR UPDATE", target_id
            )
            attacker_items = await self._get_user_inventory(conn, attacker_id)
            target_items = await self._get_user_inventory(conn, target_id)

            # 前置檢查與成本扣除
            if attacker['oil_balance'] < settings.rob_cost:
                return {"success": False, "reason": "INSUFFICIENT_FUNDS"}

            new_attacker_balance = attacker['oil_balance'] - settings.rob_cost
            items_used = []
            items_triggered = []

            # 🍹 特殊道具檢定：Caipirinha (卡琵莉亞雞尾酒)
            if attacker.get('active_effects', {}).get('next_rob_is_caipirinha'):
                await self._clear_effect(conn, attacker_id, 'next_rob_is_caipirinha')
                items_used.append('caipirinha')

                if random.random() < 0.5:  # 50% 大成功
                    return await self._handle_rob_success(
                        conn, attacker, target, "CAIPIRINHA_MAX",
                        new_attacker_balance, items_used, items_triggered
                    )
                else:  # 50% 慘敗
                    return await self._handle_caipirinha_failure(
                        conn, attacker, target, new_attacker_balance
                    )

            # 🔒 防禦檢定 #1: Padlock vs Bolt Cutters
            if self._has_active_item(target_items, 'padlock'):
                items_triggered.append('padlock')
                if self._has_item(attacker_items, 'bolt_cutter'):
                    await self._consume_item(conn, attacker_id, 'bolt_cutter')
                    items_used.append('bolt_cutter')
                else:
                    return await self._handle_rob_failure(
                        conn, attacker, target, "PADLOCK_BLOCKED",
                        new_attacker_balance, items_used, items_triggered
                    )

            # 💣 防禦檢定 #2: Landmine
            if self._has_active_item(target_items, 'landmine'):
                await self._consume_item(conn, target_id, 'landmine')
                items_triggered.append('landmine')
                return await self._handle_rob_failure(
                    conn, attacker, target, "LANDMINE_HIT",
                    new_attacker_balance, items_used, items_triggered
                )

            # 🎲 核心成功率檢定 (被抓到?)
            success_rate = self._calculate_dynamic_success_rate(attacker, target)
            if random.random() > success_rate:
                return await self._handle_rob_failure(
                    conn, attacker, target, "CAUGHT",
                    new_attacker_balance, items_used, items_triggered
                )

            # 🎯 成功路徑：決定戰利品等級
            rob_tier = self._determine_rob_tier()
            return await self._handle_rob_success(
                conn, attacker, target, rob_tier,
                new_attacker_balance, items_used, items_triggered
            )

async def _handle_rob_success(self, conn, attacker, target, rob_tier,
                             new_attacker_balance, items_used, items_triggered):
    """處理搶劫成功 - 使用動態掉落機制"""

    # 計算基礎搶劫金額
    tier_config = settings.rob_tiers[rob_tier]
    if rob_tier == "CAIPIRINHA_MAX":
        steal_percentage = 0.75  # 卡琵莉亞大成功
    else:
        steal_percentage = tier_config['percentage']

    stolen_amount = min(
        int(target['oil_balance'] * steal_percentage),
        settings.max_steal_amount
    )

    # 【V2.2 核心】動態掉落計算
    drop_result = self._calculate_dynamic_drop(stolen_amount)

    attacker_gain = stolen_amount - drop_result['dropped_amount']
    new_attacker_balance += attacker_gain
    new_target_balance = target['oil_balance'] - stolen_amount

    # 設置目標保護期
    protection_until = datetime.now() + timedelta(hours=settings.protection_hours)

    # 更新數據庫
    await conn.execute("""
        UPDATE gacha_users
        SET oil_balance = $1, rob_protection_until = $2
        WHERE user_id = $3
    """, new_target_balance, protection_until, target['target_id'])

    await conn.execute(
        "UPDATE gacha_users SET oil_balance = $1 WHERE user_id = $2",
        new_attacker_balance, attacker['user_id']
    )

    # 記錄日誌
    await self._log_robbery_success(conn, {
        'attacker_id': attacker['user_id'],
        'target_id': target['user_id'],
        'rob_tier': rob_tier,
        'amount_stolen': stolen_amount,
        'amount_gained': attacker_gain,
        'amount_dropped': drop_result['dropped_amount'],
        'drop_message': drop_result['message'],
        'items_used': items_used,
        'items_triggered': items_triggered,
        'attacker_balance_after': new_attacker_balance,
        'target_balance_after': new_target_balance
    })

    return {
        "success": True,
        "rob_tier": rob_tier,
        "steal_amount": stolen_amount,
        "attacker_gain": attacker_gain,
        "dropped_amount": drop_result['dropped_amount'],
        "drop_message": drop_result['message'],
        "items_used": items_used,
        "items_triggered": items_triggered
    }
```

#### 3. 【V2.2 升級】動態成功率與輔助函式
```python
def _calculate_dynamic_success_rate(self, attacker: Dict, target: Dict) -> float:
    """V2.2 動態成功率計算 - 考慮道具效果"""
    base_rate = settings.base_success_rate  # 0.60

    # 攻擊方增益效果
    effects = attacker.get('active_effects', {})
    if 'luck_modifier' in effects:
        base_rate *= effects['luck_modifier']  # 威士忌/酒精加成

    # 目標方易受攻擊狀態
    target_effects = target.get('active_effects', {})
    if 'vulnerable_until' in target_effects:
        vulnerable_until = datetime.fromisoformat(target_effects['vulnerable_until'])
        if datetime.now() < vulnerable_until:
            base_rate *= 1.2  # 酒精副作用：更容易被搶

    return min(0.95, max(0.05, base_rate))

def _determine_rob_tier(self) -> str:
    """根據權重隨機決定搶劫等級"""
    tiers = settings.rob_tiers
    choices = list(tiers.keys())
    weights = [tiers[tier]['weight'] for tier in choices]
    return random.choices(choices, weights=weights)[0]

def _has_item(self, inventory: List[Dict], item_id: str) -> bool:
    """檢查玩家是否擁有指定道具"""
    return any(item['item_id'] == item_id and item['quantity'] > 0
               for item in inventory)

def _has_active_item(self, inventory: List[Dict], item_id: str) -> bool:
    """檢查玩家是否有激活狀態的道具 (如掛鎖、地雷)"""
    return any(item['item_id'] == item_id and item['is_active']
               for item in inventory)

async def _consume_item(self, conn, user_id: int, item_id: str):
    """消耗一個道具"""
    await conn.execute("""
        UPDATE user_inventory
        SET quantity = quantity - 1
        WHERE user_id = $1 AND item_id = $2 AND quantity > 0
    """, user_id, item_id)

    # 如果數量歸零，刪除記錄
    await conn.execute("""
        DELETE FROM user_inventory
        WHERE user_id = $1 AND item_id = $2 AND quantity <= 0
    """, user_id, item_id)

def _calculate_dynamic_drop(self, stolen_amount: int) -> Dict:
    """【V2.2 核心】動態掉落計算 - 隨機觸發機制"""

    # 30% 機率完全沒掉落 - 完美逃脫！
    if random.random() < settings.loot_drop.no_drop_chance:
        return {
            "dropped_amount": 0,
            "message": "你成功帶走了所有戰利品！"
        }

    # 70% 機率會掉落，掉落率在 5%-35% 之間隨機
    drop_rate = random.uniform(
        settings.loot_drop.min_drop_rate,
        settings.loot_drop.max_drop_rate
    )
    dropped_amount = int(stolen_amount * drop_rate)

    # 根據掉落程度生成不同的訊息
    if drop_rate < 0.10:
        message = f"逃跑時只掉了一點零錢 ({drop_rate:.1%})"
    elif drop_rate < 0.20:
        message = f"匆忙中掉落了一些戰利品 ({drop_rate:.1%})"
    elif drop_rate < 0.30:
        message = f"逃跑時大量戰利品散落一地 ({drop_rate:.1%})"
    else:
        message = f"慌亂逃跑，大部分戰利品都掉了 ({drop_rate:.1%})"

    return {
        "dropped_amount": dropped_amount,
        "message": message
    }
```

---

### 🛒 【V2.2 重構】整合現有 SHOP 系統

#### 1. 擴展現有商店分類 - 新增「搶劫道具」分頁
基於現有的 `ShopCategoryView` 和分頁系統，我們只需要：

**A. 更新 `shop_cog.py` 的分類選項：**
```python
# 在 ShopCategoryView 的 category_select 中新增選項
@select(placeholder='選擇一個商品分類...', options=[
    discord.SelectOption(label='兌換券', value='ticket_exchange', description='瀏覽可兌換的卡片兌換券'),
    discord.SelectOption(label='搶劫道具', value='robbery_items', description='購買搶劫攻防道具'),  # 新增
    discord.SelectOption(label='裝備', value='equipment_shop', description='瀏覽可購買的裝備 (未來功能)')
])
async def category_select(self, interaction: discord.Interaction, select_item: Select):
    # ... 現有邏輯 ...
    elif selected_category == 'robbery_items':
        try:
            # 獲取搶劫道具列表
            all_items: list[RobberyItemDefinition] = await robbery_service.get_robbery_shop_definitions()
            # 獲取油幣餘額 (搶劫道具用油幣購買，不是油票)
            oil_balance: int = await user_service.get_oil_balance(interaction.user.id, create_if_missing=True)

            if not all_items:
                await interaction.followup.send('搶劫道具商店目前沒有可購買的道具。', ephemeral=True)
                return

            items_per_page = 4
            view = RobberyShopItemListView(
                original_interaction=interaction,
                cog=self.cog,
                items=all_items,
                oil_balance=oil_balance,
                category_name='搶劫道具商店',
                items_per_page=items_per_page
            )
            embed = view.get_current_page_embed()
            await interaction.edit_original_response(embed=embed, view=view)
            view.message = await interaction.original_response()

        except Exception as e:
            logger.error("Error fetching robbery shop data for user %s: %s", interaction.user.id, e, exc_info=True)
            await interaction.followup.send('搶劫道具商店載入時發生錯誤，請稍後再試。', ephemeral=True)
```

**B. 創建 `RobberyItemDefinition` 模型：**
```python
# 在 models/shop_models.py 中新增
@dataclass
class RobberyItemDefinition:
    id: str
    name: str
    description: str
    price: int  # 油幣價格
    item_type: str  # 'defense', 'attack', 'buff', etc.
    icon: str
    duration_hours: Optional[int] = None
    effect_data: Optional[Dict] = None
```

**C. 創建 `RobberyShopItemListView`：**
```python
# 繼承現有的分頁邏輯，但適配搶劫道具
class RobberyShopItemListView(BasePaginationView):
    def __init__(self, original_interaction: discord.Interaction, cog: commands.Cog,
                 items: list[RobberyItemDefinition], oil_balance: int,
                 category_name: str='搶劫道具商店', items_per_page: int=4, timeout: int=180):
        # ... 類似現有的 ShopItemListView 但適配搶劫道具 ...

    @discord.ui.button(label='💰 購買道具', style=discord.ButtonStyle.green, emoji='💰')
    async def purchase_item_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """購買選中的搶劫道具"""
        if not self.selected_item_id:
            await interaction.response.send_message('請先從下拉選單中選擇一個道具。', ephemeral=True)
            return

        # 打開購買數量輸入模態
        purchase_modal = RobberyItemPurchaseModal(
            item_id=self.selected_item_id,
            original_message_to_edit=self.message
        )
        await interaction.response.send_modal(purchase_modal)
```

**D. 搶劫道具數據源：**
```python
# 在 services/robbery_service.py 中
async def get_robbery_shop_definitions() -> List[RobberyItemDefinition]:
    """獲取搶劫道具商店定義"""
    from config.app_config import get_gacha_core_settings
    settings = get_gacha_core_settings()

    items = []
    for item_id, config in settings.robbery_system.items.items():
        items.append(RobberyItemDefinition(
            id=item_id,
            name=config['description'].split(' - ')[0],  # 提取名稱部分
            description=config['description'],
            price=config['price'],
            item_type=config['type'],
            icon=config['description'][0],  # 提取 emoji
            duration_hours=config.get('duration_hours'),
            effect_data=config
        ))

    return items

class RobberyItemPurchaseModal(discord.ui.Modal):
    """搶劫道具購買模態"""

    def __init__(self, item_id: str, original_message_to_edit: discord.Message):
        super().__init__(title="購買搶劫道具")
        self.item_id = item_id
        self.original_message_to_edit = original_message_to_edit

        # 數量輸入
        self.quantity_input = discord.ui.TextInput(
            label="購買數量",
            placeholder="輸入要購買的數量 (預設: 1)",
            default="1",
            min_length=1,
            max_length=3
        )
        self.add_item(self.quantity_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            quantity = int(self.quantity_input.value)
            if quantity <= 0:
                await interaction.response.send_message("❌ 數量必須大於 0", ephemeral=True)
                return

            # 執行購買
            result = await robbery_service.purchase_robbery_item(
                interaction.user.id,
                self.item_id,
                quantity
            )

            if result['success']:
                embed = discord.Embed(title="🛒 購買成功", color=0x00ff00)
                embed.add_field(name="道具", value=result['item_name'], inline=False)
                embed.add_field(name="數量", value=quantity, inline=True)
                embed.add_field(name="花費", value=f"{result['total_cost']:,} 油幣", inline=True)
                embed.add_field(name="剩餘餘額", value=f"{result['remaining_balance']:,} 油幣", inline=True)

                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.response.send_message(f"❌ 購買失敗：{result['error']}", ephemeral=True)

        except ValueError:
            await interaction.response.send_message("❌ 請輸入有效的數量", ephemeral=True)
        except Exception as e:
            logger.error(f"購買搶劫道具時發生錯誤: {e}")
            await interaction.response.send_message("❌ 購買時發生錯誤，請稍後再試", ephemeral=True)

async def purchase_robbery_item(user_id: int, item_id: str, quantity: int) -> Dict:
    """購買搶劫道具的核心邏輯"""
    from config.app_config import get_gacha_core_settings
    settings = get_gacha_core_settings()

    # 檢查道具是否存在
    if item_id not in settings.robbery_system.items:
        return {"success": False, "error": "未知道具"}

    item_config = settings.robbery_system.items[item_id]
    total_cost = item_config['price'] * quantity

    async with db_pool.acquire() as conn:
        async with conn.transaction():
            # 檢查用戶餘額
            user_balance = await conn.fetchval(
                "SELECT oil_balance FROM gacha_users WHERE user_id = $1",
                user_id
            )

            if not user_balance or user_balance < total_cost:
                return {"success": False, "error": f"餘額不足！需要 {total_cost:,} 油幣"}

            # 扣除費用
            new_balance = user_balance - total_cost
            await conn.execute(
                "UPDATE gacha_users SET oil_balance = $1 WHERE user_id = $2",
                new_balance, user_id
            )

            # 添加道具到庫存
            await conn.execute("""
                INSERT INTO user_inventory (user_id, item_id, quantity)
                VALUES ($1, $2, $3)
                ON CONFLICT (user_id, item_id)
                DO UPDATE SET quantity = user_inventory.quantity + $3
            """, user_id, item_id, quantity)

            return {
                "success": True,
                "item_name": item_config['description'],
                "total_cost": total_cost,
                "remaining_balance": new_balance
            }
```

#### 2. 整合優勢
使用現有 SHOP 系統的優勢：

✅ **無縫整合** - 玩家熟悉的界面和操作流程
✅ **分頁支持** - 自動處理大量道具的分頁顯示
✅ **一致體驗** - 與現有兌換券商店保持一致的 UX
✅ **擴展性好** - 未來可輕鬆添加新的道具分類
✅ **錯誤處理** - 繼承現有系統的完善錯誤處理機制
        )
        await interaction.response.send_modal(purchase_modal)
```

#### 2. 道具使用系統 (`/use` 指令)
```python
@app_commands.command(name="use", description="使用道具")
@app_commands.describe(item="道具名稱")
async def use_item(self, interaction: discord.Interaction, item: str):

    if not await self.has_item(interaction.user.id, item):
        return await interaction.response.send_message("❌ 你沒有這個道具", ephemeral=True)

    item_config = settings.items[item]
    result = await self.activate_item(interaction.user.id, item, item_config)

    if result['success']:
        embed = discord.Embed(title="✨ 道具已激活", color=0x00ff00)
        embed.description = result['message']
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"❌ {result['error']}", ephemeral=True)

async def activate_item(self, user_id: int, item_id: str, config: Dict) -> Dict:
    """激活道具效果"""

    if config['type'] == 'defense':
        # 掛鎖：設置為激活狀態
        await self.set_item_active(user_id, item_id,
                                  hours=config['duration_hours'])
        return {"success": True, "message": f"🔒 掛鎖已激活 {config['duration_hours']} 小時"}

    elif config['type'] == 'defense_consumable':
        # 地雷：設置為激活狀態，等待觸發
        await self.set_item_active(user_id, item_id)
        return {"success": True, "message": "💣 地雷已部署，等待入侵者觸發"}

    elif config['type'] == 'buff':
        # 威士忌：添加幸運加成效果
        await self.add_user_effect(user_id, {
            'luck_modifier': config['luck_modifier']
        }, hours=config.get('duration_hours', 1))
        return {"success": True, "message": f"🥃 威士忌效果已激活，幸運+{(config['luck_modifier']-1)*100:.0f}%"}

    elif config['type'] == 'buff_debuff':
        # 酒精：幸運加成 + 易受攻擊副作用
        await self.add_user_effect(user_id, {
            'luck_modifier': config['luck_modifier'],
            'vulnerable_until': (datetime.now() + timedelta(hours=config['vulnerable_hours'])).isoformat()
        })
        return {"success": True, "message": f"🍺 酒精效果已激活，幸運+{(config['luck_modifier']-1)*100:.0f}% 但更容易被搶"}

    elif config['type'] == 'high_risk':
        # 卡琵莉亞：設置下次搶劫特殊標記
        await self.add_user_effect(user_id, {'next_rob_is_caipirinha': True})
        await self.consume_item(user_id, item_id)
        return {"success": True, "message": "🍹 卡琵莉亞已飲用，下次搶劫將是高風險高回報！"}

    elif config['type'] == 'utility':
        # 搶匪面具：匿名效果
        await self.add_user_effect(user_id, {
            'anonymous_rob_until': (datetime.now() + timedelta(hours=config['duration_hours'])).isoformat()
        })
        await self.consume_item(user_id, item_id)
        return {"success": True, "message": f"🎭 搶匪面具已戴上，{config['duration_hours']} 小時內搶劫將匿名進行"}
```

---

## 第四階段：Discord 指令介面

### 🎮 搶劫指令設計

#### `/rob list` - 【V2.2 升級】雙軌准入的目標列表
```python
@app_commands.command(name="list", description="查看可搶劫的目標列表")
async def rob_list(self, interaction: discord.Interaction):
    # 檢查攻擊方資格
    progress = await self.robbery_service.get_attacker_progress(interaction.user.id)

    if not progress['eligible']:
        # 顯示資格進度
        embed = discord.Embed(title="🚫 搶劫權限未解鎖", color=0xff6666)
        embed.description = "你需要滿足以下任一條件才能發起搶劫："

        # 抽卡進度
        gacha_prog = progress['gacha_progress']
        embed.add_field(
            name="🎴 抽卡流路線",
            value=f"進度: {gacha_prog['current']:,} / {gacha_prog['required']:,}\n"
                  f"完成度: {gacha_prog['percentage']:.1f}%",
            inline=True
        )

        # 遊戲進度
        game_prog = progress['game_progress']
        embed.add_field(
            name="🎮 遊戲流路線",
            value=f"進度: {game_prog['current']:,} / {game_prog['required']:,}\n"
                  f"完成度: {game_prog['percentage']:.1f}%",
            inline=True
        )

        embed.set_footer(text="💡 繼續抽卡或遊玩小遊戲來解鎖搶劫權限！")
        return await interaction.response.send_message(embed=embed, ephemeral=True)

    # 獲取目標列表
    targets = await self.robbery_service.generate_rob_list(interaction.user.id, limit=10)

    embed = discord.Embed(title="🎯 搶劫目標列表", color=0xff4444)

    if not targets:
        embed.description = "目前沒有合適的搶劫目標\n可能所有富豪都在保護期或隱身中"
        return await interaction.response.send_message(embed=embed)

    for i, target in enumerate(targets, 1):
        user = self.bot.get_user(target['user_id'])
        username = user.display_name if user else f"用戶 {target['user_id']}"

        embed.add_field(
            name=f"{i}. {username}",
            value=f"💰 現金: {target['oil_balance']:,} 油幣\n"
                  f"⚖️ 權重: {target['weight']:.2f}",
            inline=True
        )

    embed.set_footer(text="💡 使用 /rob attack <目標編號> 發起搶劫")
    await interaction.response.send_message(embed=embed)
```

#### `/rob attack <target>` - 【V2.2 升級】故事化搶劫體驗
```python
@app_commands.command(name="attack", description="搶劫指定目標")
@app_commands.describe(target="目標編號 (從搶劫列表獲得)")
async def rob_attack(self, interaction: discord.Interaction, target: int):
    # 獲取目標列表並驗證
    targets = await self.robbery_service.generate_rob_list()
    if target < 1 or target > len(targets):
        return await interaction.response.send_message("❌ 無效的目標編號", ephemeral=True)

    target_user = targets[target - 1]
    target_discord_user = self.bot.get_user(target_user['user_id'])
    target_name = target_discord_user.display_name if target_discord_user else f"用戶 {target_user['user_id']}"

    # 執行 V2.2 搶劫
    result = await self.robbery_service.execute_robbery_v2(
        interaction.user.id,
        target_user['user_id']
    )

    # 【V2.2 新增】故事化結果展示
    if result['success']:
        embed = await self._create_success_embed(result, target_name)
        # 發送給目標的被搶通知
        await self._send_victim_notification(target_user['user_id'], result, interaction.user)
    else:
        embed = await self._create_failure_embed(result, target_name)

    await interaction.response.send_message(embed=embed)

async def _create_success_embed(self, result: Dict, target_name: str) -> discord.Embed:
    """創建故事化的成功嵌入"""
    tier = result['rob_tier']
    tier_config = settings.rob_tiers[tier]

    # 根據搶劫等級選擇標題和描述
    if tier == 'TINY':
        title = "🎉 小有收穫"
        story = f"你像一陣風一樣溜進了 **{target_name}** 的地盤！"
    elif tier == 'SMALL':
        title = "🎉 搶劫成功！"
        story = f"你成功突破了 **{target_name}** 的防線！"
    elif tier == 'DECENT':
        title = "🎉 大獲全勝！"
        story = f"你像個專業盜賊一樣洗劫了 **{target_name}**！"
    elif tier == 'MAX':
        title = "🎉 世紀大劫案！"
        story = f"你簡直是個傳奇！你席捲了 **{target_name}** 的一切！"
    elif tier == 'CAIPIRINHA_MAX':
        title = "🍹 卡琵莉亞奇蹟！"
        story = f"卡琵莉亞的魔力爆發！你對 **{target_name}** 進行了史詩級搶劫！"

    embed = discord.Embed(title=title, description=story, color=0x00ff00)
    embed.add_field(name="💰 戰利品", value=f"{result['steal_amount']:,} 油幣", inline=True)
    embed.add_field(name="💵 實際獲得", value=f"{result['attacker_gain']:,} 油幣", inline=True)

    # 【V2.2 更新】動態掉落顯示
    if result['dropped_amount'] > 0:
        embed.add_field(name="💸 掉落", value=f"{result['dropped_amount']:,} 油幣", inline=True)
        embed.add_field(name="📝 掉落說明", value=result['drop_message'], inline=False)
    else:
        embed.add_field(name="✨ 完美逃脫", value="沒有掉落任何戰利品！", inline=True)

    # 顯示使用的道具
    if result.get('items_used'):
        items_text = "、".join(result['items_used'])
        embed.add_field(name="🛠️ 使用道具", value=items_text, inline=False)

    embed.set_footer(text=f"💡 {tier_config['message']} | 目標獲得 24 小時保護期")
    return embed

async def _create_failure_embed(self, result: Dict, target_name: str) -> discord.Embed:
    """創建故事化的失敗嵌入"""
    reason = result['reason']

    # 根據失敗原因生成不同的故事
    failure_stories = {
        'PADLOCK_BLOCKED': {
            'title': "🔒 被掛鎖阻擋",
            'story': f"你試圖潛入 **{target_name}** 的地盤，但被一個堅固的掛鎖擋住了去路！你需要一把斷線鉗！",
            'color': 0xffa500
        },
        'LANDMINE_HIT': {
            'title': "💥 踩到地雷！",
            'story': f"你剛踏進 **{target_name}** 的領地，腳下就傳來一聲巨響... 是地雷！你被炸得灰頭土臉！",
            'color': 0xff4444
        },
        'CAUGHT': {
            'title': "👮 被當場抓獲",
            'story': f"你潛入 **{target_name}** 的地盤時被發現了！你狼狽地逃跑，還留下了一些錢作為代價。",
            'color': 0xff6666
        },
        'CAIPIRINHA_FAILURE': {
            'title': "🍹 卡琵莉亞詛咒",
            'story': f"卡琵莉亞的副作用爆發！你不但沒搶到 **{target_name}** 的錢，反而...",
            'color': 0x8b0000
        }
    }

    story_config = failure_stories.get(reason, failure_stories['CAUGHT'])
    embed = discord.Embed(
        title=story_config['title'],
        description=story_config['story'],
        color=story_config['color']
    )

    embed.add_field(name="💸 損失", value=f"{result['penalty']:,} 油幣", inline=True)

    # 顯示觸發的防禦道具
    if result.get('items_triggered'):
        items_text = "、".join(result['items_triggered'])
        embed.add_field(name="🛡️ 觸發防禦", value=items_text, inline=True)

    return embed
```





#### `/inventory` - 道具庫存查看
```python
@app_commands.command(name="inventory", description="查看你的道具庫存")
async def view_inventory(self, interaction: discord.Interaction):
    inventory = await self.get_user_inventory(interaction.user.id)
    effects = await self.get_user_effects(interaction.user.id)

    embed = discord.Embed(title="🎒 你的道具庫存", color=0x3498db)

    if not inventory and not effects:
        embed.description = "你的庫存是空的，去 `/shop rob` 購買一些道具吧！"
        return await interaction.response.send_message(embed=embed)

    # 顯示道具庫存
    if inventory:
        items_text = []
        for item in inventory:
            config = settings.items[item['item_id']]
            status = " (已激活)" if item.get('is_active') else ""
            items_text.append(f"• {config['description']} x{item['quantity']}{status}")
        embed.add_field(name="🛠️ 道具", value="\n".join(items_text), inline=False)

    # 顯示激活效果
    if effects:
        effects_text = []
        for effect, value in effects.items():
            if effect == 'luck_modifier':
                effects_text.append(f"🍀 幸運加成: +{(value-1)*100:.0f}%")
            elif effect == 'vulnerable_until':
                effects_text.append(f"⚠️ 易受攻擊至: {value}")
            elif effect == 'next_rob_is_caipirinha':
                effects_text.append(f"🍹 下次搶劫: 卡琵莉亞效果")
            elif effect == 'anonymous_rob_until':
                effects_text.append(f"🎭 匿名搶劫至: {value}")

        if effects_text:
            embed.add_field(name="✨ 激活效果", value="\n".join(effects_text), inline=False)

    await interaction.response.send_message(embed=embed)
```

#### 💰 財富警報系統 - 新手保護與引導
```python
class WealthAlertService:
    """財富警報系統 - 當玩家首次成為搶劫目標時提供引導"""

    async def check_wealth_alert(self, user_id: int, new_balance: int):
        """檢查是否需要發送財富警報 - 基於動態安全門檻"""

        # 獲取用戶的金庫等級
        user_data = await self.db.fetchrow(
            "SELECT vault_level, wealth_alert_sent FROM gacha_users WHERE user_id = $1",
            user_id
        )

        if not user_data:
            return

        # 計算該用戶的安全門檻
        security_threshold = get_security_threshold(user_data['vault_level'])

        # 只對首次超過個人安全門檻的玩家發送警報
        if new_balance > security_threshold and not user_data['wealth_alert_sent']:
            await self._send_wealth_alert(user_id, new_balance, security_threshold)
            await self.db.execute(
                "UPDATE gacha_users SET wealth_alert_sent = TRUE WHERE user_id = $1",
                user_id
            )

    async def _send_wealth_alert(self, user_id: int, balance: int, security_threshold: int):
        """發送財富警報私信 - 基於動態安全門檻"""

        user = self.bot.get_user(user_id)
        if not user:
            return

        # 檢查玩家的攻擊資格
        progress = await self.robbery_service.get_attacker_progress(user_id)

        embed = discord.Embed(
            title="⚠️ 財富警報！",
            description=f"你的錢包餘額已達到 **{balance:,} 油幣**，\n"
                       f"超過了你的安全門檻 **{security_threshold:,} 油幣**！\n"
                       f"現在可能會成為老練劫匪的目標！",
            color=0xffa500
        )

        # 【V2.2 革命性】自保建議 - 強調金庫升級
        embed.add_field(
            name="🛡️ 如何提升安全？",
            value="🏰 **升級金庫**: `/vault upgrade` 永久提升安全門檻\n"
                  "🏦 **存入金庫**: `/vault deposit` 將閒置資金存起來\n"
                  "🔒 **購買道具**: `/shop rob` 看看保護道具\n"
                  "💸 **合理消費**: 購買卡包或遊玩小遊戲",
            inline=False
        )

        # 反擊路線指導
        if not progress['eligible']:
            embed.add_field(
                name="⚔️ 解鎖反擊權限",
                value="完成以下任一目標即可發起搶劫：\n"
                      f"🎴 抽卡 {progress['gacha_progress']['required']:,} 次 "
                      f"(目前 {progress['gacha_progress']['current']:,})\n"
                      f"🎮 遊玩小遊戲 {progress['game_progress']['required']:,} 次 "
                      f"(目前 {progress['game_progress']['current']:,})",
                inline=False
            )
        else:
            embed.add_field(
                name="⚔️ 你已解鎖搶劫權限",
                value="使用 `/rob list` 查看可搶劫的目標，以攻為守！",
                inline=False
            )

        embed.set_footer(text="💡 這條訊息只會發送一次，請妥善保管你的財富！")

        try:
            await user.send(embed=embed)
        except discord.Forbidden:
            # 如果無法發送私信，在公開頻道提醒
            channel = self.bot.get_channel(settings.alert_channel_id)  # 需要配置
            if channel:
                await channel.send(f"{user.mention}", embed=embed)

# 在餘額更新的地方調用
async def update_user_balance(self, user_id: int, new_balance: int):
    """更新用戶餘額並檢查財富警報"""

    # 更新餘額
    await self.db.execute(
        "UPDATE gacha_users SET oil_balance = $1 WHERE user_id = $2",
        new_balance, user_id
    )

    # 檢查財富警報
    await self.wealth_alert_service.check_wealth_alert(user_id, new_balance)
```
