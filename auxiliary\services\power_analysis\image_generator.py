# -*- coding: utf-8 -*-
"""
戰力分析圖像生成器
"""

import asyncio
import datetime
import io
import random
import re
import textwrap
import unicodedata
from typing import Any, Dict, List, Optional, Tuple, Union

from PIL import Image, ImageDraw, ImageFont

from utils.logger import logger

from .config import default_config, power_levels

# --- Font Management ---


def _load_fonts(config) -> Dict[str, Dict[str, ImageFont.ImageFont]]:
    """載入所有字體"""
    fonts = {"regular": {}, "bold": {}, "emoji": {}, "pixelcraft": {}}

    font_configs = [
        ("regular", config.chinese_font_path),
        ("bold", config.chinese_bold_font_path),
        ("emoji", config.emoji_font_path),
        ("pixelcraft", config.pixelcraft_font_path),
    ]

    loaded_fonts = []
    failed_fonts = []

    for font_type, font_path in font_configs:
        try:
            for size_name, size in config.font_sizes.items():
                fonts[font_type][size_name] = ImageFont.truetype(font_path, size)
            loaded_fonts.append(f"{font_type}({font_path})")
        except Exception as e:
            logger.warning("載入字體失敗 %s: %s", font_path, e)
            failed_fonts.append(f"{font_type}({font_path})")
            # 使用預設字體作為fallback
            default_font = ImageFont.load_default()
            for size_name in config.font_sizes:
                fonts[font_type][size_name] = default_font

    if loaded_fonts:
        logger.info("成功載入戰力分析字體: %s", ", ".join(loaded_fonts))
    if failed_fonts:
        logger.warning("載入失敗的字體: %s", ", ".join(failed_fonts))

    return fonts


_fonts = _load_fonts(default_config)


def get_font(
    size_name: str, bold: bool = False
) -> Union[ImageFont.ImageFont, ImageFont.FreeTypeFont]:
    """獲取中文字體"""
    font_type = "bold" if bold else "regular"
    return _fonts[font_type].get(size_name, ImageFont.load_default())


def get_emoji_font(
    size_name: str,
) -> Union[ImageFont.ImageFont, ImageFont.FreeTypeFont]:
    """獲取emoji字體"""
    return _fonts["emoji"].get(size_name, ImageFont.load_default())


def get_pixelcraft_font(
    size_name: str,
) -> Union[ImageFont.ImageFont, ImageFont.FreeTypeFont]:
    """獲取Pixelcraft字體（用於數字）"""
    return _fonts["pixelcraft"].get(size_name, ImageFont.load_default())


# --- Text Rendering ---


def _is_emoji(char: str) -> bool:
    """檢查字符是否為emoji"""
    if unicodedata.category(char) == "So":
        return True
    code_point = ord(char)
    emoji_ranges = [
        (0x1F600, 0x1F64F),
        (0x1F300, 0x1F5FF),
        (0x1F680, 0x1F6FF),
        (0x1F1E0, 0x1F1FF),
        (0x2600, 0x26FF),
        (0x2700, 0x27BF),
        (0xFE00, 0xFE0F),
        (0x1F900, 0x1F9FF),
        (0x1F018, 0x1F270),
    ]
    return any(start <= code_point <= end for start, end in emoji_ranges)


def _split_text_by_type(text: str) -> List[Tuple[str, str]]:
    """將文字按照字符類型分割"""
    if not text:
        return []
    segments = []
    current_segment = ""
    current_type = "emoji" if _is_emoji(text[0]) else "text"
    for char in text:
        char_type = "emoji" if _is_emoji(char) else "text"
        if current_type != char_type:
            if current_segment:
                segments.append((current_segment, current_type))
            current_segment = char
            current_type = char_type
        else:
            current_segment += char
    if current_segment:
        segments.append((current_segment, current_type))
    return segments


def _draw_dual_font_text(
    draw: ImageDraw.ImageDraw,
    position: Tuple[int, int],
    text: str,
    font_size: str,
    fill: Union[str, Tuple[int, int, int]],
    bold: bool = False,
) -> None:
    """使用雙字體渲染文字"""
    if not text:
        return
    x, y = position
    chinese_font = get_font(font_size, bold=bold)
    emoji_font = get_emoji_font(font_size)
    for segment_text, segment_type in _split_text_by_type(text):
        if not segment_text:
            continue
        font = emoji_font if segment_type == "emoji" else chinese_font
        try:
            draw.text((x, y), segment_text, fill=fill, font=font)
            bbox = draw.textbbox((0, 0), segment_text, font=font)
            x += bbox[2] - bbox[0]
        except Exception as e:
            logger.warning("渲染文字片段失敗: %s", e)


# --- Image Drawing ---


def _truncate_text_to_width(
    draw: ImageDraw.ImageDraw,
    text: str,
    font: Union[ImageFont.ImageFont, ImageFont.FreeTypeFont],
    max_width: int,
) -> str:
    """將文字截斷到指定寬度，超出時加上省略號"""
    try:
        text_width = draw.textbbox((0, 0), text, font=font)[2]
        if text_width <= max_width:
            return text
        ellipsis = "..."
        ellipsis_width = draw.textbbox((0, 0), ellipsis, font=font)[2]
        if ellipsis_width >= max_width:
            return ellipsis
        left, right, best_length = 0, len(text), 0
        while left <= right:
            mid = (left + right) // 2
            test_text = text[:mid] + ellipsis
            if draw.textbbox((0, 0), test_text, font=font)[2] <= max_width:
                best_length = mid
                left = mid + 1
            else:
                right = mid - 1
        return text[:best_length] + ellipsis if best_length > 0 else ellipsis
    except Exception as e:
        logger.warning("文字截斷時發生錯誤: %s", e)
        return text[:10] + "..." if len(text) > 10 else text


def _calculate_power_index(level: str) -> int:
    """根據等級計算戰力指數（加入隨機變化）"""
    power_level = power_levels.get(level.upper())
    if not power_level:
        return random.randint(400, 600)
    min_val, max_val = power_level.power_range
    if min_val == max_val:
        return max_val
    level_ranges = {
        "SS": (0.6, 0.9),
        "S": (0.4, 0.8),
        "A": (0.5, 0.9),
        "B": (0.3, 0.7),
        "C": (0.2, 0.8),
        "D": (0.1, 0.6),
    }
    range_min, range_max = level_ranges.get(level.upper(), (0.3, 0.7))
    multiplier = random.uniform(range_min, range_max)
    power_index = int(min_val + (max_val - min_val) * multiplier)
    return max(min_val, min(power_index, max_val))


def _draw_wrapped_text_sync(
    draw: ImageDraw.ImageDraw,
    text: str,
    area: Tuple[int, int, int, int],
    font: Union[ImageFont.ImageFont, ImageFont.FreeTypeFont],
    fill: str,
) -> None:
    """在指定區域內繪製自動換行的文字（同步版本）"""
    x, y, max_x, max_y = area
    max_width = max_x - x
    try:
        char_width = draw.textbbox((0, 0), "測", font=font)[2]
        chars_per_line = max(1, int(max_width // char_width))
        line_height = draw.textbbox((0, 0), "測試", font=font)[3] + 4
    except Exception:
        chars_per_line = max(1, int(max_width // 22))
        line_height = 26

    wrapped_lines = textwrap.wrap(text, width=chars_per_line)
    current_y = y
    for line in wrapped_lines:
        if current_y + line_height > max_y:
            break
        draw.text((x, current_y), line, fill=fill, font=font)
        current_y += line_height


async def _process_user_avatar(image_data: bytes, size: Tuple[int, int]) -> Image.Image:
    """處理用戶頭像，智能放大到佔滿整個空間"""

    def _sync_process():
        try:
            user_image = Image.open(io.BytesIO(image_data)).convert("RGBA")
            target_width, target_height = size
            original_width, original_height = user_image.size
            scale = max(target_width / original_width, target_height / original_height)
            new_width, new_height = (
                int(original_width * scale),
                int(original_height * scale),
            )
            user_image = user_image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            avatar = Image.new("RGBA", size, (0, 0, 0, 0))
            x, y = (target_width - new_width) // 2, (target_height - new_height) // 2

            crop_x, crop_y = max(0, -x), max(0, -y)
            crop_width, crop_height = (
                min(new_width, target_width),
                min(new_height, target_height),
            )
            cropped_image = user_image.crop(
                (crop_x, crop_y, crop_x + crop_width, crop_y + crop_height)
            )
            paste_x, paste_y = max(0, x), max(0, y)
            avatar.paste(cropped_image, (paste_x, paste_y), cropped_image)
            return avatar
        except Exception as e:
            logger.error("處理用戶頭像時發生錯誤: %s", e)
            return Image.new("RGBA", size, (128, 128, 128, 128))

    return await asyncio.to_thread(_sync_process)


async def generate_power_image(
    power_result: Dict[str, Any],
    user_image_data: bytes,
    user_name: Optional[str] = None,
) -> bytes:
    """生成戰力分析圖片"""
    config = default_config
    request_id = power_result.get("request_id", "unknown")
    logger.info("[%s] 開始生成戰力分析圖片，用戶: %s", request_id, user_name)

    def _sync_generate_image():
        template = Image.open(config.template_path).convert("RGBA")

        canvas = Image.new("RGBA", template.size, (0, 0, 0, 0))
        canvas.paste(user_avatar, config.avatar_position, user_avatar)
        canvas.paste(template, (0, 0), template)
        draw = ImageDraw.Draw(canvas)

        level = power_result.get("level", "C")

        # 繪製用戶名稱
        font_medium = get_font("medium")
        max_width = 346 - config.username_position[0]
        truncated_username = _truncate_text_to_width(
            draw,
            user_name or "戰士",
            font_medium,
            max_width,
        )
        draw.text(
            config.username_position, truncated_username, fill="white", font=font_medium
        )

        # 繪製戰力分析文字 (同步版本)
        analysis = power_result.get("analysis", "無法獲取戰力分析")
        _draw_wrapped_text_sync(
            draw,
            analysis,
            config.analysis_area,
            get_font("small"),
            "white",
        )

        # 繪製結論
        conclusion = power_result.get("conclusion", "戰力分析完成")
        cleaned_conclusion = re.sub(r"<0x[0-9A-Fa-f]{2}>", "", conclusion)
        _draw_dual_font_text(
            draw,
            config.conclusion_position,
            cleaned_conclusion,
            "medium",
            (255, 255, 255),
            bold=True,
        )

        # 繪製戰力指數
        power_index = _calculate_power_index(level)
        font_pixel = get_pixelcraft_font("medium")
        power_text = str(power_index)
        text_bbox = draw.textbbox((0, 0), power_text, font=font_pixel)
        right_aligned_x = config.power_index_position[0] - (text_bbox[2] - text_bbox[0])
        draw.text(
            (right_aligned_x, config.power_index_position[1]),
            power_text,
            fill="white",
            font=font_pixel,
        )

        # 繪製進度條
        bar_start_x, bar_y, bar_end_x, bar_bottom = config.progress_bar_area
        draw.rectangle(
            [(bar_start_x, bar_y), (bar_end_x, bar_bottom)],
            fill="#333333",
            outline="#555555",
        )
        if power_index > 0:
            fill_ratio = min(power_index / 10000, 1.0)
            fill_width = int((bar_end_x - bar_start_x) * fill_ratio)
            power_level = power_levels.get(level.upper())
            level_color = power_level.color if power_level else "#808080"
            draw.rectangle(
                [(bar_start_x, bar_y), (bar_start_x + fill_width, bar_bottom)],
                fill=level_color,
            )

        # 繪製時間戳和報告ID
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        report_id_short = request_id[:8].upper()
        info_text = f"{timestamp} | {report_id_short}"
        draw.text(
            config.timestamp_position, info_text, fill="white", font=get_font("tiny")
        )

        output = io.BytesIO()
        canvas.save(output, format="WEBP", quality=85)
        return output.getvalue()

    try:
        user_avatar = await _process_user_avatar(user_image_data, config.avatar_size)
        image_bytes = await asyncio.to_thread(_sync_generate_image)
        logger.info(
            "[%s] 戰力分析圖片生成完成，大小: %s bytes", request_id, len(image_bytes)
        )
        return image_bytes

    except Exception as e:
        logger.error(
            "[%s] 生成戰力分析圖片時發生錯誤: %s", request_id, e, exc_info=True
        )
        raise
