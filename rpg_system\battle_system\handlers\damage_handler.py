"""
傷害處理器 (DamageHandler)
處理複雜的傷害計算、修正和應用
"""

import random
from typing import Any, Dict, List, Optional

from utils.logger import logger

from ...config.loader import ConfigLoader
from ...formula_engine.evaluator import evaluate_formula
from ..models.battle import Battle
from ..models.combatant import Combatant

# 傷害計算常數
DEFENSE_CONSTANT = 100  # 防禦減免計算常數


async def calculate_and_apply_damage(
    caster: "Combatant",
    target: "Combatant",
    damage_effect: Dict[str, Any],
    base_damage_value: float,
    battle_context: "Battle",
    config_loader: "ConfigLoader",
    is_crit: Optional[bool] = None,
    skill_tags: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    計算並應用傷害

    Args:
        caster: 施法者
        target: 目標
        damage_effect: 傷害效果定義
        base_damage_value: 基礎傷害值
        battle_context: 戰鬥上下文
        config_loader: 配置加載器
        is_crit: 是否暴擊（如果為None則內部計算）
        skill_tags: 技能標籤列表

    Returns:
        包含傷害計算詳情的字典
    """
    try:
        # 1. 暴擊判斷
        if is_crit is None:
            is_crit = await _calculate_crit_chance(
                caster, damage_effect, battle_context
            )

        # 2. 命中判斷
        was_miss = await _calculate_miss_chance(caster, target, battle_context)

        # NEW: Update consecutive_hits based on hit/miss
        counts_for_combo = damage_effect.get("counts_for_combo", False)

        if was_miss:
            if counts_for_combo:
                caster.consecutive_hits = 0
            return {
                "final_damage": 0,
                "actual_damage_dealt": 0,
                "was_crit": False,
                "was_miss": True,
                "elemental_advantage": 1.0,
                "damage_breakdown": {
                    "base_damage": base_damage_value,
                    "crit_multiplier": 1.0,
                    "defense_reduction": 0.0,
                    "elemental_multiplier": 1.0,
                },
            }
        else:  # It was a hit
            if counts_for_combo:
                caster.consecutive_hits += 1

        # 3. 暴擊倍率計算
        crit_multiplier = await _calculate_crit_multiplier(caster, is_crit)

        # 4. 屬性克制計算
        elemental_multiplier = await _calculate_elemental_advantage(
            caster, target, damage_effect, skill_tags or []
        )

        # 5. 防禦減免計算
        defense_reduction = await _calculate_defense_reduction(target, damage_effect)

        # 6. 應用修正器
        modifier_multiplier = await _apply_damage_modifiers(
            caster, target, damage_effect, base_damage_value, battle_context
        )

        # 7. 最終傷害計算
        final_damage = (
            base_damage_value
            * crit_multiplier
            * elemental_multiplier
            * modifier_multiplier
            * (1 - defense_reduction)
        )

        # 確保傷害不為負數
        final_damage = max(0, final_damage)

        # 8. 應用傷害到目標
        actual_damage_dealt = await target.take_damage(
            final_damage,
            damage_effect.get("damage_type", "DAMAGE"),
            is_crit,
            battle_context,
        )

        return {
            "final_damage": final_damage,
            "actual_damage_dealt": actual_damage_dealt,
            "was_crit": is_crit,
            "was_miss": False,
            "elemental_advantage": elemental_multiplier,
            "damage_breakdown": {
                "base_damage": base_damage_value,
                "crit_multiplier": crit_multiplier,
                "defense_reduction": defense_reduction,
                "elemental_multiplier": elemental_multiplier,
                "modifier_multiplier": modifier_multiplier,
            },
        }

    except Exception as e:
        logger.error("傷害計算錯誤: %s", e)
        return {
            "final_damage": 0,
            "actual_damage_dealt": 0,
            "was_crit": False,
            "was_miss": True,
            "elemental_advantage": 1.0,
            "damage_breakdown": {},
        }


async def _calculate_crit_chance(
    caster: "Combatant",
    damage_effect: Dict[str, Any],
    battle_context: "Battle",
) -> bool:
    """
    計算暴擊機率

    Args:
        caster: 施法者
        damage_effect: 傷害效果定義
        battle_context: 戰鬥上下文

    Returns:
        是否暴擊
    """
    # 檢查效果是否允許暴擊
    can_crit = damage_effect.get("can_crit", True)
    if not can_crit:
        return False

    # 獲取暴擊率
    crit_rate = caster.current_stats.get("crit_rate", 0.05)

    # 使用戰鬥上下文的隨機數生成器
    if hasattr(battle_context, "_rng"):
        roll = battle_context._rng.random()
    else:
        roll = random.random()

    return roll < crit_rate


async def _calculate_miss_chance(
    caster: "Combatant", target: "Combatant", battle_context: "Battle"
) -> bool:
    """
    計算命中/閃避

    Args:
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文

    Returns:
        是否未命中
    """
    accuracy = caster.current_stats.get("accuracy", 0.95)
    evasion = target.current_stats.get("evasion", 0.05)

    hit_chance = max(0.05, min(0.95, accuracy - evasion))  # 限制在5%-95%之間

    # 使用戰鬥上下文的隨機數生成器
    if hasattr(battle_context, "_rng"):
        roll = battle_context._rng.random()
    else:
        roll = random.random()

    return roll > hit_chance


async def _calculate_crit_multiplier(caster: "Combatant", is_crit: bool) -> float:
    """
    計算暴擊倍率

    Args:
        caster: 施法者
        is_crit: 是否暴擊

    Returns:
        暴擊倍率
    """
    if not is_crit:
        return 1.0

    return caster.current_stats.get("crit_dmg_multiplier", 1.5)


async def _calculate_elemental_advantage(
    caster: "Combatant",
    target: "Combatant",
    damage_effect: Dict[str, Any],
    skill_tags: List[str],
) -> float:
    """
    計算屬性克制倍率

    Args:
        caster: 施法者
        target: 目標
        damage_effect: 傷害效果定義
        skill_tags: 技能標籤列表

    Returns:
        屬性克制倍率
    """
    # 簡化實現，後續可以根據需要擴展
    # 這裡可以根據施法者和目標的屬性、技能標籤等計算克制關係
    return 1.0


async def _calculate_defense_reduction(
    target: "Combatant", damage_effect: Dict[str, Any]
) -> float:
    """
    計算防禦減免

    Args:
        target: 目標
        damage_effect: 傷害效果定義

    Returns:
        防禦減免比例（0.0-1.0）
    """
    damage_type = damage_effect.get("damage_type", "DAMAGE")

    if damage_type == "DAMAGE":
        # 統一使用 def 屬性，如果不存在則嘗試使用舊的 pdef 或 mdef 中較高的值
        defense = target.current_stats.get("def", 0)
        if defense == 0:
            # 向後兼容：使用 pdef 和 mdef 中較高的值
            pdef = target.current_stats.get("pdef", 0)
            mdef = target.current_stats.get("mdef", 0)
            defense = max(pdef, mdef)
    elif damage_type == "TRUE_DAMAGE":
        return 0.0  # 真實傷害無視防禦
    else:
        defense = 0

    # 防禦減免公式: defense / (defense + DEFENSE_CONSTANT)
    # 這確保了防禦減免永遠不會達到100%
    if defense <= 0:
        return 0.0

    reduction = defense / (defense + DEFENSE_CONSTANT)
    return min(0.9, reduction)  # 最大減免90%


async def _apply_damage_modifiers(
    caster: "Combatant",
    target: "Combatant",
    damage_effect: Dict[str, Any],
    base_damage: float,
    battle_context: "Battle",
) -> float:
    """
    應用傷害修正器

    Args:
        caster: 施法者
        target: 目標
        damage_effect: 傷害效果定義
        base_damage: 基礎傷害
        battle_context: 戰鬥上下文

    Returns:
        修正器倍率
    """
    modifiers = damage_effect.get("modifiers", [])
    if not modifiers:
        return 1.0

    total_multiplier = 1.0

    for modifier in modifiers:
        modifier_type = modifier.get("modifier_type")

        if modifier_type == "SCALING_MODIFIER":
            multiplier = await _apply_scaling_modifier(
                modifier, caster, target, battle_context
            )
            total_multiplier *= multiplier
        elif modifier_type == "CONDITIONAL_BOOST":
            multiplier = await _apply_conditional_boost(
                modifier, caster, target, battle_context
            )
            total_multiplier *= multiplier
        elif modifier_type == "CUSTOM_EFFECT_TRIGGER":
            # CUSTOM_EFFECT_TRIGGER 不影響傷害倍率，但會觸發額外效果
            await _apply_custom_effect_trigger(modifier, caster, target, battle_context)
        else:
            logger.warning("未知的修正器類型: %s", modifier_type)

    return total_multiplier


async def _apply_scaling_modifier(
    modifier: Dict[str, Any],
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> float:
    """
    應用縮放修正器

    Args:
        modifier: 修正器定義
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文

    Returns:
        修正器倍率
    """
    scaling_source = modifier.get("scaling_source", "")
    scaling_factor = modifier.get("scaling_factor", 0.0)

    # 解析縮放來源
    if scaling_source.startswith("stat:"):
        stat_name = scaling_source[5:]  # 移除 'stat:' 前綴
        stat_value = caster.current_stats.get(stat_name, 0)
        return 1.0 + (stat_value * scaling_factor)
    elif scaling_source.startswith("target_stat:"):
        stat_name = scaling_source[12:]  # 移除 'target_stat:' 前綴
        stat_value = target.current_stats.get(stat_name, 0)
        return 1.0 + (stat_value * scaling_factor)
    elif scaling_source == "hit_count":
        hit_count = caster.consecutive_hits
        return 1.0 + (hit_count * scaling_factor)
    elif scaling_source == "missing_hp_percent":
        missing_hp_percent = 1.0 - (target.current_hp / max(target.max_hp, 1))
        return 1.0 + (missing_hp_percent * scaling_factor)
    elif scaling_source == "target_max_hp":
        return 1.0 + (target.max_hp * scaling_factor)
    elif scaling_source.startswith("custom:"):
        # 處理自定義縮放來源
        return await _handle_custom_scaling_source(
            scaling_source, caster, target, scaling_factor, modifier
        )
    else:
        logger.warning("未知的縮放來源: %s", scaling_source)
        return 1.0


async def _apply_conditional_boost(
    modifier: Dict[str, Any],
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> float:
    """
    應用條件加成修正器

    Args:
        modifier: 修正器定義
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文

    Returns:
        修正器倍率
    """
    condition_group = modifier.get("condition_group")
    if not condition_group:
        logger.warning("CONDITIONAL_BOOST修正器缺少condition_group")
        return 1.0

    condition_met = await _evaluate_condition_group(
        condition_group, caster, target, battle_context
    )

    if condition_met:
        boost_value = modifier.get("bonus_value", 0.0)
        return 1.0 + boost_value
    else:
        return 1.0


async def _evaluate_condition_group(
    condition_group: Dict[str, Any],
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> bool:
    """
    評估條件組

    Args:
        condition_group: 條件組定義
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文

    Returns:
        條件是否滿足
    """
    try:
        group_type = condition_group.get("type", "AND")
        conditions = condition_group.get("conditions", [])

        if not conditions:
            return True

        results = []
        for condition in conditions:
            result = await _evaluate_single_condition(
                condition, caster, target, battle_context
            )
            results.append(result)

        if group_type == "AND":
            return all(results)
        elif group_type == "OR":
            return any(results)
        else:
            logger.warning("未知的條件組類型: %s", group_type)
            return False

    except Exception as e:
        logger.error("評估條件組錯誤: %s", e)
        return False


async def _evaluate_single_condition(
    condition: Dict[str, Any],
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> bool:
    """
    評估單個條件

    Args:
        condition: 條件定義
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文

    Returns:
        條件是否滿足
    """
    try:
        source_combatant_str = condition.get("source_combatant", "target")
        check_type = condition.get("check")
        value = condition.get("value")

        # 確定檢查的戰鬥單位
        if source_combatant_str == "caster":
            source_combatant = caster
        elif source_combatant_str == "target":
            source_combatant = target
        else:
            logger.warning("未知的source_combatant: %s", source_combatant_str)
            return False

        # 執行具體的檢查
        if check_type == "hp_below_percent":
            current_hp_percent = source_combatant.current_hp / max(
                source_combatant.max_hp, 1
            )
            return current_hp_percent < (value or 0)
        elif check_type == "hp_above_percent":
            current_hp_percent = source_combatant.current_hp / max(
                source_combatant.max_hp, 1
            )
            return current_hp_percent > (value or 0)
        elif check_type == "has_status_effect":
            status_effect_id = condition.get("status_effect_id")
            return any(
                effect.status_effect_id == status_effect_id
                for effect in source_combatant.status_effects
            )
        elif check_type and check_type.startswith("custom:"):
            return await _evaluate_custom_condition(
                check_type,
                condition,
                source_combatant,
                caster,
                target,
                battle_context,
            )
        else:
            logger.warning("未知的檢查類型: %s", check_type)
            return False

    except Exception as e:
        logger.error("評估單個條件錯誤: %s", e)
        return False


async def _evaluate_custom_condition(
    check_type: str,
    condition: Dict[str, Any],
    source_combatant: "Combatant",
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> bool:
    """
    評估自定義條件

    Args:
        check_type: 檢查類型
        condition: 條件定義
        source_combatant: 源戰鬥單位
        battle_context: 戰鬥上下文

    Returns:
        條件是否滿足
    """
    try:
        if check_type == "custom:was_targeted_and_attacked_last_turn":
            return source_combatant.was_attacked_last_turn
        elif check_type == "custom:last_action_had_tag":
            tag_value = condition.get("value")
            return tag_value in source_combatant.last_action_tags
        elif check_type == "custom:mp_above_percent":
            value = condition.get("value", 0.8)
            mp_percent = source_combatant.current_mp / max(source_combatant.max_mp, 1)
            return mp_percent > value
        elif check_type == "custom:mp_below_percent":
            value = condition.get("value", 0.3)
            mp_percent = source_combatant.current_mp / max(source_combatant.max_mp, 1)
            return mp_percent < value
        elif check_type == "custom:stat_higher_than_target":
            # 比較施法者和目標的屬性
            stat_name = condition.get("stat_name")
            comparison_stat = condition.get("comparison_stat")
            if not stat_name or not comparison_stat:
                return False

            # 根據source_combatant確定比較的對象
            if source_combatant == caster:
                caster_stat = caster.current_stats.get(stat_name, 0)
                target_stat = target.current_stats.get(comparison_stat, 0)
                return caster_stat > target_stat
            else:  # source_combatant == target
                target_stat = target.current_stats.get(stat_name, 0)
                caster_stat = caster.current_stats.get(comparison_stat, 0)
                return target_stat > caster_stat

        elif check_type == "custom:total_stats_higher_than_target":
            # 比較總屬性值
            if source_combatant == caster:
                caster_total = sum(
                    [
                        caster.current_stats.get("patk", 0),
                        caster.current_stats.get("matk", 0),
                        caster.current_stats.get("pdef", 0),
                        caster.current_stats.get("mdef", 0),
                        caster.current_stats.get("spd", 0),
                    ]
                )
                target_total = sum(
                    [
                        target.current_stats.get("patk", 0),
                        target.current_stats.get("matk", 0),
                        target.current_stats.get("pdef", 0),
                        target.current_stats.get("mdef", 0),
                        target.current_stats.get("spd", 0),
                    ]
                )
                return caster_total > target_total
            return False

        elif check_type == "custom:stats_within_range":
            # 檢查屬性是否在指定範圍內（相對於對手）
            min_ratio = condition.get("min_ratio", 0.8)
            max_ratio = condition.get("max_ratio", 1.2)

            if source_combatant == caster:
                # 計算施法者相對於目標的屬性比例
                caster_total = sum(
                    [
                        caster.current_stats.get("patk", 0),
                        caster.current_stats.get("matk", 0),
                        caster.current_stats.get("pdef", 0),
                        caster.current_stats.get("mdef", 0),
                    ]
                )
                target_total = sum(
                    [
                        target.current_stats.get("patk", 0),
                        target.current_stats.get("matk", 0),
                        target.current_stats.get("pdef", 0),
                        target.current_stats.get("mdef", 0),
                    ]
                )

                if target_total > 0:
                    ratio = caster_total / target_total
                    return min_ratio <= ratio <= max_ratio
            return False
        elif check_type == "custom:lowest_defensive_stat":
            # 檢查是否是最低防禦屬性
            pdef = source_combatant.current_stats.get("pdef", 0)
            mdef = source_combatant.current_stats.get("mdef", 0)
            return pdef <= mdef  # 簡化判斷
        else:
            logger.warning("未實現的自定義條件: %s", check_type)
            return False

    except Exception as e:
        logger.error("評估自定義條件錯誤: %s", e)
        return False


async def _handle_custom_scaling_source(
    scaling_source: str,
    caster: "Combatant",
    target: "Combatant",
    scaling_factor: float,
    modifier: Dict[str, Any],
) -> float:
    """
    處理自定義縮放來源

    Args:
        scaling_source: 縮放來源
        caster: 施法者
        target: 目標
        scaling_factor: 縮放因子
        modifier: 修正器定義

    Returns:
        縮放倍率
    """
    try:
        if scaling_source == "custom:skill_hits_on_target_counter":
            # 技能對目標的命中次數
            if hasattr(caster, "skill_target_hit_counters"):
                counter = caster.skill_target_hit_counters.get(target.instance_id, {})
                # 這裡需要知道當前技能ID，暫時使用總計數
                total_hits = sum(counter.values())
                return 1.0 + (total_hits * scaling_factor)
            return 1.0

        elif scaling_source == "custom:hp_percent_diff_caster_vs_target":
            # 施法者與目標的HP百分比差異
            caster_hp_percent = caster.current_hp / max(caster.max_hp, 1)
            target_hp_percent = target.current_hp / max(target.max_hp, 1)
            hp_diff = abs(caster_hp_percent - target_hp_percent)
            return 1.0 + (hp_diff * scaling_factor)

        elif scaling_source.startswith("custom:stat_diff_caster_vs_target:"):
            # 施法者與目標的屬性差異
            stat_name = scaling_source.split(":")[-1]
            caster_stat = caster.current_stats.get(stat_name, 0)
            target_stat = target.current_stats.get(stat_name, 0)
            stat_diff = max(0, caster_stat - target_stat)
            max_bonus = modifier.get("max_bonus_value", 1.0)
            bonus = min(stat_diff * scaling_factor, max_bonus)
            return 1.0 + bonus

        elif scaling_source == "custom:hp_diff_caster_vs_target":
            # 施法者與目標的HP絕對值差異
            hp_diff = max(0, caster.current_hp - target.current_hp)
            max_bonus = modifier.get("max_bonus_value", 1.0)
            bonus = min(hp_diff * scaling_factor, max_bonus)
            return 1.0 + bonus

        elif scaling_source == "custom:missing_hp_percent_caster":
            # 施法者的缺失血量百分比
            missing_hp_percent = 1.0 - (caster.current_hp / max(caster.max_hp, 1))
            max_bonus = modifier.get("max_bonus_value", 1.0)
            bonus = min(missing_hp_percent * scaling_factor, max_bonus)
            return 1.0 + bonus

        elif scaling_source == "custom:resource_ratio_advantage":
            # 資源比例優勢（HP+MP vs 對方HP+MP）
            caster_resources = caster.current_hp + caster.current_mp
            target_resources = target.current_hp + target.current_mp
            if target_resources > 0:
                ratio_advantage = max(
                    0, (caster_resources - target_resources) / target_resources
                )
                max_bonus = modifier.get("max_bonus_value", 1.0)
                bonus = min(ratio_advantage * scaling_factor, max_bonus)
                return 1.0 + bonus
            return 1.0

        elif scaling_source == "custom:combined_offensive_stats":
            # 綜合攻擊屬性（物攻+魔攻）
            combined_stats = caster.current_stats.get(
                "patk", 0
            ) + caster.current_stats.get("matk", 0)
            max_bonus = modifier.get("max_bonus_value", 1.0)
            bonus = min(combined_stats * scaling_factor, max_bonus)
            return 1.0 + bonus

        else:
            logger.warning("未實現的自定義縮放來源: %s", scaling_source)
            return 1.0

    except Exception as e:
        logger.error("處理自定義縮放來源錯誤: %s", e)
        return 1.0


async def _apply_custom_effect_trigger(
    modifier: Dict[str, Any],
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
) -> None:
    """
    應用自定義效果觸發器

    Args:
        modifier: 修正器定義
        caster: 施法者
        target: 目標
        battle_context: 戰鬥上下文
    """
    try:
        triggered_effect = modifier.get("triggered_effect")
        if not triggered_effect:
            return

        # 確定效果目標
        effect_target = target
        target_override = triggered_effect.get("target_override")
        if target_override == "caster":
            effect_target = caster
        elif target_override == "target":
            effect_target = target

        # 確定受益者（對於轉移類效果）
        beneficiary = caster
        beneficiary_override = triggered_effect.get("beneficiary_override")
        if beneficiary_override == "caster":
            beneficiary = caster
        elif beneficiary_override == "target":
            beneficiary = target

        # 應用觸發的效果
        effect_type = triggered_effect.get("effect_type")

        if effect_type == "LOSE_MP":
            # MP消耗效果
            value_formula = triggered_effect.get("value_formula", "0")
            # 這裡需要準備上下文變量
            context_vars = _prepare_trigger_context(caster, target)

            mp_loss = await evaluate_formula(value_formula, context_vars)
            mp_loss = max(0, mp_loss)

            effect_target.current_mp = max(0, effect_target.current_mp - int(mp_loss))

        elif effect_type == "TRANSFER_MP":
            # MP轉移效果
            value_formula = triggered_effect.get("value_formula", "0")
            context_vars = _prepare_trigger_context(caster, target)

            transfer_amount = await evaluate_formula(value_formula, context_vars)
            transfer_amount = max(0, min(transfer_amount, effect_target.current_mp))

            effect_target.current_mp = max(
                0, effect_target.current_mp - int(transfer_amount)
            )
            beneficiary.current_mp = min(
                beneficiary.max_mp, beneficiary.current_mp + int(transfer_amount)
            )

        else:
            logger.warning("未實現的觸發效果類型: %s", effect_type)

    except Exception as e:
        logger.error("應用自定義效果觸發器錯誤: %s", e)


def _prepare_trigger_context(
    caster: "Combatant", target: "Combatant"
) -> Dict[str, Any]:
    """準備觸發效果的上下文變量"""
    return {
        "caster_stat_matk": caster.current_stats.get("matk", 0),
        "caster_stat_patk": caster.current_stats.get("patk", 0),
        "target_current_mp": target.current_mp,
        "target_max_mp": target.max_mp,
        "caster_current_mp": caster.current_mp,
        "caster_max_mp": caster.max_mp,
    }
