"""
最高星級記錄存儲庫模組 - 管理 gacha_card_highest_star 表的模組級函數
"""

from typing import List, Optional, Tuple

import asyncpg

from gacha.exceptions import EntityNotFoundError
from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)

# 表名常量
HIGHEST_STAR_TABLE = "gacha_card_highest_star"
COLLECTION_TABLE = "gacha_user_collections"


async def get_current_highest_star_holders(
    card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> List[Tuple[int, int]]:
    """
    批量獲取當前最高星級持有者

    Args:
        card_ids: 卡片ID列表
        connection: 可選的數據庫連接

    Returns:
        (card_id, user_id) 對的列表
    """
    if not card_ids:
        return []

    query = f"""
        SELECT card_id, user_id
        FROM {HIGHEST_STAR_TABLE}
        WHERE card_id = ANY($1::integer[])
    """

    results = await fetch_all(query, (card_ids,), connection=connection)
    return [(record["card_id"], record["user_id"]) for record in results]


async def check_has_star_holders(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """
    檢查是否有任何用戶擁有該卡片且有星級

    Args:
        card_id: 卡片ID
        connection: 可選的數據庫連接

    Returns:
        是否有星級持有者
    """
    query = f"""
        SELECT EXISTS(
            SELECT 1 FROM {COLLECTION_TABLE}
            WHERE card_id = $1 AND quantity > 0 AND star_level > 0
        ) as has_star_holders
    """

    return await fetch_value(query, (card_id,), connection=connection)


async def find_new_highest_star_holder(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Tuple[int, int]:
    """
    查找新的最高星級持有者

    Args:
        card_id: 卡片ID
        connection: 可選的數據庫連接

    Returns:
        (user_id, star_level)

    Raises:
        EntityNotFoundError: 如果找不到任何持有者
    """
    query = f"""
        SELECT user_id, star_level
        FROM {COLLECTION_TABLE}
        WHERE card_id = $1 AND quantity > 0 AND star_level > 0
        ORDER BY star_level DESC, user_id ASC
        LIMIT 1
    """

    result = await fetch_one(query, (card_id,), connection=connection)
    if result:
        return (result["user_id"], result["star_level"])
    raise EntityNotFoundError(f"找不到卡片 {card_id} 的任何星級持有者")


async def delete_highest_star_record(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """
    刪除最高星級記錄

    Args:
        card_id: 卡片ID
        connection: 可選的數據庫連接
    """
    query = f"""
        DELETE FROM {HIGHEST_STAR_TABLE}
        WHERE card_id = $1
    """

    await execute_query(query, (card_id,), connection=connection)


async def upsert_highest_star_record(
    card_id: int,
    user_id: int,
    star_level: int,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """
    更新或插入最高星級記錄

    Args:
        card_id: 卡片ID
        user_id: 用戶ID
        star_level: 星級
        connection: 可選的數據庫連接
    """
    query = f"""
        INSERT INTO {HIGHEST_STAR_TABLE} (card_id, user_id, star_level, achieved_at)
        VALUES ($1, $2, $3, NOW())
        ON CONFLICT (card_id)
        DO UPDATE SET
            user_id = EXCLUDED.user_id,
            star_level = EXCLUDED.star_level,
            achieved_at = EXCLUDED.achieved_at
    """

    await execute_query(query, (card_id, user_id, star_level), connection=connection)


async def recalculate_highest_star_holder(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """
    重新計算並更新最高星級持有者

    Args:
        card_id: 卡片ID
        connection: 可選的數據庫連接
    """
    # 檢查是否有星級持有者
    has_star_holders = await check_has_star_holders(card_id, connection)

    if not has_star_holders:
        # 沒有任何用戶有星級，刪除記錄
        await delete_highest_star_record(card_id, connection)
        return

    # 查找新的最高星級持有者
    # 如果在這裡拋出 EntityNotFoundError，讓它自然傳播出去
    user_id, star_level = await find_new_highest_star_holder(card_id, connection)
    await upsert_highest_star_record(card_id, user_id, star_level, connection)


async def filter_cards_needing_maintenance(
    card_user_pairs: List[Tuple[int, int]],
) -> List[Tuple[int, int]]:
    """
    批量檢查哪些卡片真正需要維護
    只有當用戶是當前最高星級持有者時才需要維護

    Args:
        card_user_pairs: (card_id, user_id) 對的列表

    Returns:
        需要維護的 (card_id, user_id) 對的列表
    """
    if not card_user_pairs:
        return []

    # 構建查詢參數
    card_ids = [pair[0] for pair in card_user_pairs]
    card_user_map = dict(card_user_pairs)

    # 批量獲取當前最高星級持有者
    current_holders = await get_current_highest_star_holders(card_ids)

    # 過濾出需要維護的卡片
    cards_needing_maintenance = []
    for card_id, current_holder_user_id in current_holders:
        losing_user_id = card_user_map.get(card_id)

        # 只有當失去卡片的用戶是當前最高星級持有者時才需要維護
        if losing_user_id == current_holder_user_id:
            cards_needing_maintenance.append((card_id, losing_user_id))

    return cards_needing_maintenance
