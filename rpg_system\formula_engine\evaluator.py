"""
安全的公式求值引擎
用於計算JSON配置中的公式字符串
使用 asteval 庫進行安全的表達式求值
"""

import math
import re
from typing import Any, Dict, Optional

from utils.logger import logger

try:
    from asteval import Interpreter
except ImportError:
    logger.error("asteval 庫未安裝，請運行: pip install asteval")
    raise


# 全局解釋器實例
_interpreter: Optional[Interpreter] = None
_number_pattern = re.compile(r"^\d+(\.\d+)?$")


def _get_interpreter() -> Interpreter:
    """獲取或創建全局 asteval 解釋器實例"""
    global _interpreter
    if _interpreter is None:
        _interpreter = Interpreter()
        _setup_allowed_functions(_interpreter)
    return _interpreter


def _setup_allowed_functions(interpreter: Interpreter):
    """設置允許的函數和常量"""
    interpreter.symtable.clear()
    interpreter.symtable["min"] = min
    interpreter.symtable["max"] = max
    interpreter.symtable["abs"] = abs
    interpreter.symtable["round"] = round
    interpreter.symtable["floor"] = math.floor
    interpreter.symtable["ceil"] = math.ceil
    interpreter.symtable["sqrt"] = math.sqrt
    interpreter.symtable["pow"] = pow
    interpreter.symtable["ln"] = math.log
    interpreter.symtable["log10"] = math.log10

    def iff(condition, value_if_true, value_if_false):
        return value_if_true if condition else value_if_false

    def clamp(value, min_val, max_val):
        return max(min_val, min(max_val, value))

    def log_func(x, base=math.e):
        return math.log(x, base)

    interpreter.symtable["iff"] = iff
    interpreter.symtable["if"] = iff
    interpreter.symtable["clamp"] = clamp
    interpreter.symtable["log"] = log_func
    interpreter.symtable["PI"] = math.pi
    interpreter.symtable["E"] = math.e


def _set_context_variables(interpreter: Interpreter, context: Dict[str, Any]):
    """設置上下文變量到解釋器"""
    vars_to_remove = [
        key
        for key, value in interpreter.symtable.items()
        if not callable(value) and key not in ["PI", "E"]
    ]
    for key in vars_to_remove:
        del interpreter.symtable[key]

    for key, value in context.items():
        safe_key = key.replace(".", "_") if "." in key else key
        interpreter.symtable[safe_key] = value


async def evaluate(formula: str, context: Dict[str, Any]) -> float:
    """
    求值公式

    Args:
        formula: 公式字符串
        context: 變量上下文

    Returns:
        計算結果
    """
    if not formula or not isinstance(formula, str):
        return 0.0

    try:
        formula = formula.strip()
        if _number_pattern.match(formula):
            return float(formula)

        interpreter = _get_interpreter()
        _set_context_variables(interpreter, context)

        result = interpreter.eval(formula, show_errors=False)

        if interpreter.error:
            error = interpreter.error[0]
            logger.warning("公式求值警告: %s, 錯誤: %s", formula, error.get_error())
            interpreter.error.clear()
            return 0.0

        if result is None:
            logger.warning("公式求值返回 None: %s", formula)
            return 0.0

        if isinstance(result, (int, float)):
            return float(result)
        else:
            logger.warning("公式返回非數值類型: %s -> %s", formula, type(result))
            return 0.0

    except Exception as e:
        logger.error("公式求值錯誤: %s, 錯誤: %s", formula, e)
        return 0.0


# For backward compatibility
async def evaluate_formula(formula: str, context: Dict[str, Any]) -> float:
    return await evaluate(formula, context)
