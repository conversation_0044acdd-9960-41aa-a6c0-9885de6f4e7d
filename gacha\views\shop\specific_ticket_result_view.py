from typing import List, Optional, <PERSON>ple

import discord

from gacha.models.models import Card
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from gacha.views.embeds.shop.specific_ticket_result_embed_builder import (
    build_specific_ticket_result_embed,
)
from gacha.views.shop.favorite_button import FavoriteButton


class SpecificTicketResultView(BasePaginationView):
    """
    顯示指定券兌換結果的 View，支持分頁和收藏。
    """

    def __init__(
        self,
        original_interaction: discord.Interaction,
        ticket_name: str,
        exchanged_cards: List[Card],
        *,
        timeout: Optional[float] = 180.0,
    ):
        self.original_interaction = original_interaction
        self.user_id = original_interaction.user.id
        self.ticket_name = ticket_name
        self.exchanged_cards = exchanged_cards
        self.items_per_page = 1
        self.total_items = len(self.exchanged_cards)
        total_pages = self.total_items if self.total_items > 0 else 1

        super().__init__(
            bot=original_interaction.client,  # type: ignore
            user_id=original_interaction.user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=timeout,
        )

    async def _add_favorite_button(self):
        """異步添加或更新收藏按鈕。"""
        for item in self.children:
            if isinstance(item, FavoriteButton):
                self.remove_item(item)

        card_index = self.current_page - 1
        if 0 <= card_index < self.total_items:
            card = self.exchanged_cards[card_index]
            from gacha.repositories.collection import user_collection_repository

            is_favorite = await user_collection_repository.get_card_favorite_status(
                self.user_id, card.card_id
            )
            self.add_item(
                FavoriteButton(
                    user_id=self.user_id,
                    card_id=card.card_id,
                    is_favorite=is_favorite or False,
                )
            )

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新視圖到指定頁面並編輯原始消息。"""
        await interaction.response.defer()
        self.current_page = page
        await self._add_favorite_button()
        embed = await self.get_current_page_embed()
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        await interaction.edit_original_response(embed=embed, view=self)

    async def get_current_page_embed(self) -> discord.Embed:
        """為當前頁面生成 discord.Embed。"""
        card_index = self.current_page - 1
        current_card_to_display = (
            self.exchanged_cards[card_index]
            if 0 <= card_index < self.total_items
            else None
        )

        return build_specific_ticket_result_embed(
            interaction=self.original_interaction,
            ticket_name=self.ticket_name,
            displayed_card=current_card_to_display,
            current_page=self.current_page,
            total_pages=self.total_pages,
        )

    async def prepare_initial_message_payload(
        self,
    ) -> Tuple[discord.Embed, "SpecificTicketResultView"]:
        """準備初始消息的 Embed 和 View 實例。"""
        await self._add_favorite_button()
        embed = await self.get_current_page_embed()
        return embed, self
