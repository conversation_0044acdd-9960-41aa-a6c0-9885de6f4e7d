-- 遷移檔案：簡化擴展story系統
-- 支援多故事並行和UUID分享

-- 1. 新增故事狀態（只需要兩種狀態）
ALTER TABLE story_history 
ADD COLUMN story_status VARCHAR(20) DEFAULT 'active' 
    CHECK (story_status IN ('active', 'paused'));

-- 2. 新增故事標題（用於顯示和管理）
ALTER TABLE story_history 
ADD COLUMN story_title VARCHAR(255);

-- 3. 新增公開分享支援
ALTER TABLE story_history 
ADD COLUMN is_public BOOLEAN DEFAULT FALSE;

-- 4. 新增索引優化查詢
CREATE INDEX IF NOT EXISTS idx_story_history_story_status ON story_history (story_status);
CREATE INDEX IF NOT EXISTS idx_story_history_user_status ON story_history (user_id, story_status);
CREATE INDEX IF NOT EXISTS idx_story_history_public ON story_history (is_public) WHERE is_public = TRUE;

-- 5. 更新現有資料：將所有is_active=FALSE的故事設為paused（保存歷史）
UPDATE story_history 
SET story_status = 'paused'
WHERE is_active = FALSE;

-- 6. 為現有故事設定story_title（從theme_title獲取）
UPDATE story_history 
SET story_title = COALESCE(theme_title, '未命名故事')
WHERE story_title IS NULL;

-- 7. 新增註解
COMMENT ON COLUMN story_history.story_status IS '故事狀態 (active: 進行中, paused: 暫停保存)';
COMMENT ON COLUMN story_history.story_title IS '故事標題';
COMMENT ON COLUMN story_history.is_public IS '是否公開（允許他人透過UUID查看）';