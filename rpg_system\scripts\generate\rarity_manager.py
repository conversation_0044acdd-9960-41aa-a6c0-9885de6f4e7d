"""
稀有度管理器

管理稀有度相關的計算和配置，包括倍率、等級、MP消耗、冷卻時間等。
"""

import json
import os
import random
from typing import Any, Dict, List


class RarityManager:
    """管理稀有度相關的計算和配置"""

    def __init__(self, balance_config: Dict[str, Any]):
        """初始化稀有度管理器

        Args:
            balance_config: 平衡配置字典，包含稀有度倍率、最大等級等信息
        """
        self.balance = balance_config
        self.generation_config = self._load_generation_config()

    def _load_generation_config(self) -> Dict[str, Any]:
        """加載生成配置文件"""
        # 獲取當前文件所在目錄
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, "generation_config.json")

        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)

    def _get_random_factor(self, rarity: int) -> float:
        """根據稀有度獲取隨機調整因子範圍

        Args:
            rarity: 稀有度等級

        Returns:
            隨機調整因子
        """
        if rarity <= 2:
            return random.uniform(0.7, 1.3)  # 較大範圍
        elif rarity <= 5:
            return random.uniform(0.8, 1.2)  # 中等範圍
        else:
            return random.uniform(0.9, 1.1)  # 較小範圍

    def get_multiplier(self, rarity: int, multiplier_type: str = "power") -> float:
        """獲取特定稀有度的倍率值，加入微小隨機性

        Args:
            rarity: 稀有度等級
            multiplier_type: 倍率類型（如 "power", "duration" 等）

        Returns:
            倍率值
        """
        base_mult = self.balance["rarity_multipliers"][rarity].get(multiplier_type, 1.0)
        # 為基礎倍率添加微小的隨機性
        return base_mult * random.uniform(0.95, 1.05)

    def get_max_level(self, rarity: int) -> int:
        """獲取特定稀有度的最大等級

        Args:
            rarity: 稀有度等級

        Returns:
            最大等級
        """
        return self.balance["max_levels"].get(rarity, 10)

    def get_mp_cost(self, rarity: int, category: str) -> int:
        """計算MP消耗，加入隨機性

        Args:
            rarity: 稀有度等級
            category: 技能類別

        Returns:
            MP消耗值
        """
        base_mp = self.balance["base_mp_costs"].get(category, 20)
        random_factor_base = self._get_random_factor(rarity)
        random_factor_rarity = random.uniform(0.8, 1.2)
        # 計算基礎隨機MP，並加入隨機化的稀有度加成
        calculated_mp = (base_mp * random_factor_base) + (
            rarity * 1.5 * random_factor_rarity
        )
        # 確保MP消耗不低於下限
        return max(5, int(calculated_mp))

    def get_cooldown(self, rarity: int, category: str) -> int:
        """計算冷卻時間，加入隨機性

        Args:
            rarity: 稀有度等級
            category: 技能類別

        Returns:
            冷卻時間
        """
        base_cd = self.balance["base_cooldowns"].get(category, 3)
        random_factor_base = self._get_random_factor(rarity)
        random_factor_rarity = random.uniform(0.8, 1.2)
        # 計算基礎隨機CD，並加入隨機化的稀有度加成
        calculated_cd = (base_cd * random_factor_base) + (
            rarity // 2 * random_factor_rarity
        )
        # 確保冷卻時間不低於下限，並四捨五入
        return max(1, int(round(calculated_cd)))

    def is_complex_feature_available(self, feature: str, rarity: int) -> bool:
        """檢查特定稀有度是否可使用複雜功能

        Args:
            feature: 功能名稱（如 "control", "special", "crit" 等）
            rarity: 稀有度等級

        Returns:
            是否可用該功能
        """
        feature_rarity_map = {
            "control": 4,
            "special": 3,  # 降低特殊技能要求至 R3
            "crit": 1,  # R1 開始就有機率爆擊
            "stack": 3,
            "percentage_damage": 6,
        }
        return rarity >= feature_rarity_map.get(feature, 99)

    def get_effect_templates(self, rarity: int) -> List[str]:
        """根據稀有度獲取可用的效果模板列表

        Args:
            rarity: 稀有度等級

        Returns:
            可用的效果模板列表
        """
        # 從配置文件中獲取效果模板分配
        templates_config = self.generation_config.get("effect_templates_by_rarity", {})
        rarity_str = str(rarity)

        if rarity_str not in templates_config:
            raise KeyError(f"配置文件中缺少稀有度 {rarity} 的效果模板配置")

        return templates_config[rarity_str].copy()

    def get_damage_multiplier(self, rarity: int) -> str:
        """獲取傷害倍率公式，加入隨機性

        Args:
            rarity: 稀有度等級

        Returns:
            傷害倍率公式字符串
        """
        rarity_power = self.get_multiplier(rarity, "power")
        random_factor = self._get_random_factor(rarity)
        level_scale_random = random.uniform(0.8, 1.2)

        # 隨機化基礎加成和等級加成因子
        base_add_factor = 1.0 * random_factor
        level_scale_factor = 0.15 * level_scale_random

        # 基礎值 = 隨機基礎 + 隨機稀有度加成
        randomized_base = base_add_factor + (
            rarity_power / 100.0 * random.uniform(0.9, 1.1)
        )
        randomized_scaling = level_scale_factor

        # 確保基礎值和縮放值不為負或過小
        randomized_base = max(0.1, randomized_base)
        randomized_scaling = max(0.01, randomized_scaling)

        return f"({randomized_base:.2f} + skill_level * {randomized_scaling:.2f})"

    def get_heal_value(self, rarity: int, heal_type: str) -> str:
        """獲取治療值公式，加入隨機性

        Args:
            rarity: 稀有度等級
            heal_type: 治療類型

        Returns:
            治療值公式字符串
        """
        rarity_power = self.get_multiplier(rarity, "power")
        random_factor = self._get_random_factor(rarity)
        level_scale_random = random.uniform(0.8, 1.2)

        if "PERCENT_CASTER_MATK" in heal_type.upper():
            base_heal = 0.8 * random_factor
            rarity_add = (rarity_power / 150.0) * random.uniform(0.9, 1.1)
            level_scale = 0.1 * level_scale_random
            final_base = max(0.1, base_heal + rarity_add)
            final_scale = max(0.01, level_scale)
            return f"({final_base:.2f} + skill_level * {final_scale:.2f})"
        elif "PERCENT_MAX_HP" in heal_type.upper():
            base_heal = 0.1 * random_factor
            rarity_add = (rarity_power / 150.0 * 0.2) * random.uniform(0.9, 1.1)
            level_scale = 0.02 * level_scale_random
            final_base = max(0.01, base_heal + rarity_add)
            final_scale = max(0.001, level_scale)
            return f"({final_base:.2f} + skill_level * {final_scale:.2f})"
        elif "FLAT" in heal_type.upper():
            base_heal = 50 * random_factor
            rarity_add = (rarity * 20) * random.uniform(0.9, 1.1)
            level_scale = 10 * level_scale_random
            final_base = max(10, base_heal + rarity_add)
            final_scale = max(1, level_scale)
            return f"({int(final_base)} + skill_level * {int(final_scale)})"

        # 默認回退治療類型
        base_heal = 1.0 * random_factor
        level_scale = 0.1 * level_scale_random
        final_base = max(0.1, base_heal)
        final_scale = max(0.01, level_scale)
        return f"({final_base:.2f} + skill_level * {final_scale:.2f})"

    def get_effect_duration(self, rarity: int) -> str:
        """獲取效果持續時間公式，加入隨機性

        Args:
            rarity: 稀有度等級

        Returns:
            效果持續時間公式字符串
        """
        random_factor = self._get_random_factor(rarity)
        # 基礎持續時間計算加入隨機性
        base_duration_calc = (2 + rarity // 2) * random_factor
        # 確保基礎持續時間至少為1
        base_duration = max(1, int(round(base_duration_calc)))
        # 等級加成部分使用固定公式確保一致性
        level_divisor = 3  # 固定使用3，確保描述和效果定義一致
        return f"({base_duration} + floor(skill_level / {level_divisor}))"

    def get_effect_chance(self, rarity: int) -> str:
        """獲取效果觸發機率公式，加入隨機性

        Args:
            rarity: 稀有度等級

        Returns:
            效果觸發機率公式字符串
        """
        random_factor = self._get_random_factor(rarity)
        # 基礎機率計算加入隨機性
        base_chance_calc = (0.6 + rarity * 0.04) * random_factor
        # 限制基礎機率在合理範圍內
        base_chance = max(0.1, min(0.95, base_chance_calc))
        # 等級加成也可以隨機化
        level_scale = 0.02 * random.uniform(0.7, 1.3)
        level_scale = max(0.005, level_scale)  # 確保不為0或負
        # 返回包含 min(1.0, ...) 的公式字符串
        return f"(min(1.0, {base_chance:.2f} + skill_level * {level_scale:.3f}))"

    def get_stack_count(self, rarity: int) -> int:
        """獲取效果堆疊數量，加入微小隨機性

        Args:
            rarity: 稀有度等級

        Returns:
            效果堆疊數量
        """
        base_stack = 1 + rarity // 3 if rarity >= 3 else 1
        # 高稀有度有小機率增加1層
        if rarity >= 5 and random.random() < 0.1:  # 10% chance for R5+
            base_stack += 1
        return base_stack
