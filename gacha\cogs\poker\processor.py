"""
德州撲克遊戲處理器 (Game Processor / Actor)
職責：每個遊戲實例的唯一管理者，處理命令隊列，更新遊戲狀態
"""

import asyncio
from typing import Optional

import discord

from utils.logger import logger

from . import logic as poker_logic
from .models import STAKE_CONFIGS, TimeoutEvent


class GameProcessor:
    """
    遊戲處理器 - 每個遊戲實例的 Actor

    這是一個獨立的 asyncio.Task，擁有一個命令隊列。
    它是唯一可以修改 PokerGameState 的組件，從根本上消除競態條件。
    """

    def __init__(self, game_state, cog):
        self.game_state = game_state
        self.cog = cog  # 用於調用渲染服務
        self.command_queue: asyncio.Queue = asyncio.Queue()
        self.processor_task: Optional[asyncio.Task] = None
        self.is_running = False
        self.ACTION_TIMEOUT = 60  # 60秒行動超時
        self.WARNING_TIME = 10  # 剩餘10秒時發送警告

    def start(self):
        """啟動遊戲處理器"""
        if self.is_running:
            logger.warning("遊戲處理器 %s 已經在運行", self.game_state.game_id)
            return

        self.is_running = True
        self.processor_task = asyncio.create_task(self._processor_loop())
        logger.info("遊戲處理器 %s 已啟動", self.game_state.game_id)

    async def stop(self):
        """停止遊戲處理器"""
        if not self.is_running:
            return

        self.is_running = False

        if self.processor_task and not self.processor_task.done():
            self.processor_task.cancel()
            try:
                await self.processor_task
            except asyncio.CancelledError:
                pass

        logger.info("遊戲處理器 %s 已停止", self.game_state.game_id)

    async def send_command(self, command):
        """發送命令到處理器隊列"""
        if not self.is_running:
            logger.warning("嘗試向已停止的處理器發送命令: %s", self.game_state.game_id)
            return

        await self.command_queue.put(command)

    async def _wait_for_command(self):
        """等待下一個命令，處理超時和警告"""
        try:
            timeout = self._get_action_timeout()
            if timeout is not None:
                return await self._wait_for_command_with_warning(timeout)
            else:
                logger.debug(
                    "遊戲 %s 無需超時，等待命令或退出", self.game_state.game_id
                )
                try:
                    return await asyncio.wait_for(self.command_queue.get(), timeout=5.0)
                except asyncio.TimeoutError:
                    return None  # 返回 None 表示需要重新檢查循環條件
        except asyncio.TimeoutError:
            from . import game_manager

            if (
                self.game_state.game_over
                or not game_manager.matchmaking_queue
                or self.game_state.game_id
                not in game_manager.matchmaking_queue.active_games
            ):
                logger.info(
                    "遊戲 %s 在超時檢查時發現已結束，處理器退出",
                    self.game_state.game_id,
                )
                return "exit"  # 特殊信號，表示退出循環
            logger.info("遊戲 %s 玩家行動超時", self.game_state.game_id)
            return TimeoutEvent()

    async def _processor_loop(self):
        """處理器主循環"""
        try:
            logger.info(
                "遊戲處理器 %s 的主循環已啟動。", self.game_state.game_id
            )  # <-- 添加日誌
            while self.is_running:
                from . import game_manager

                is_game_over = self.game_state.game_over
                is_in_active_games = (
                    game_manager.matchmaking_queue is not None
                    and self.game_state.game_id
                    in game_manager.matchmaking_queue.active_games
                )

                if is_game_over or not is_in_active_games:
                    # <-- 添加更詳細的日誌
                    logger.warning(
                        "遊戲處理器 %s 正在退出。原因: is_game_over=%s, is_in_active_games=%s",
                        self.game_state.game_id,
                        is_game_over,
                        is_in_active_games,
                    )
                    break

                command = await self._wait_for_command()

                if command is None:
                    continue
                if command == "exit":
                    break

                future = getattr(command, "future", None)
                try:
                    await self._process_command(command)
                    if future and not future.done():
                        future.set_result(True)
                except Exception as e:
                    # 將異常傳遞回 UI 層（如果可能），由 BaseView.on_error 統一處理日誌和回覆
                    if future and not future.done():
                        future.set_exception(e)
                    else:
                        # 如果命令沒有 future (例如系統事件 TimeoutEvent)，則在此處記錄日誌
                        logger.error(
                            "遊戲處理器 %s 發生未處理的錯誤 (無 future): %s",
                            self.game_state.game_id,
                            e,
                            exc_info=True,
                        )

        except asyncio.CancelledError:
            logger.info("遊戲處理器 %s 被取消", self.game_state.game_id)
        except Exception as e:
            logger.error(
                "遊戲處理器 %s 發生錯誤: %s", self.game_state.game_id, e, exc_info=True
            )
        finally:
            self.is_running = False
            logger.info(
                "遊戲處理器 %s 的主循環已終止並進入清理階段。", self.game_state.game_id
            )  # <-- 添加日誌
            try:
                from . import game_manager

                async with game_manager.processors_lock:
                    if self.game_state.game_id in game_manager.active_processors:
                        del game_manager.active_processors[self.game_state.game_id]
                        logger.info(
                            "處理器 %s 已從管理器中移除", self.game_state.game_id
                        )
            except Exception as cleanup_error:
                logger.error(
                    "清理處理器 %s 時發生錯誤: %s",
                    self.game_state.game_id,
                    cleanup_error,
                )

    def _get_action_timeout(self) -> Optional[float]:
        """獲取當前的行動超時時間"""
        # 🔧 修復：添加更嚴格的退出條件檢查
        # 如果遊戲結束或沒有需要行動的玩家，返回 None（無限等待）
        if (
            self.game_state.game_over
            or self.game_state.winner_id is not None
            or self.game_state.current_player_index is None
        ):
            logger.debug(
                "遊戲 %s 無需超時 - game_over=%s, winner_id=%s, current_player_index=%s",
                self.game_state.game_id,
                self.game_state.game_over,
                self.game_state.winner_id,
                self.game_state.current_player_index,
            )
            return None

        # 如果雙方都全押，不需要超時
        if self.game_state.player1.is_all_in and self.game_state.player2.is_all_in:
            logger.debug("遊戲 %s 無需超時 - 雙方都全押", self.game_state.game_id)
            return None

        # 🔧 修復：檢查遊戲是否從遊戲管理器中被移除
        from . import game_manager

        if (
            not game_manager.matchmaking_queue
            or self.game_state.game_id
            not in game_manager.matchmaking_queue.active_games
        ):
            logger.warning(
                "遊戲 %s 已從活躍遊戲中移除，處理器應該停止", self.game_state.game_id
            )
            return None

        logger.debug(
            "遊戲 %s 需要超時檢查 - 返回 %s 秒",
            self.game_state.game_id,
            self.ACTION_TIMEOUT,
        )
        return self.ACTION_TIMEOUT

    async def _wait_for_command_with_warning(self, timeout: float):
        """等待命令，在剩餘10秒時發送警告"""
        warning_time = timeout - self.WARNING_TIME

        if warning_time <= 0:
            # 如果超時時間不足10秒，直接等待
            return await asyncio.wait_for(self.command_queue.get(), timeout=timeout)

        try:
            # 先等待到警告時間點
            command = await asyncio.wait_for(
                self.command_queue.get(), timeout=warning_time
            )
            return command
        except asyncio.TimeoutError:
            # 到達警告時間點，發送警告消息
            await self._send_timeout_warning()

            # 繼續等待剩餘的10秒
            try:
                command = await asyncio.wait_for(
                    self.command_queue.get(), timeout=self.WARNING_TIME
                )
                return command
            except asyncio.TimeoutError:
                # 真正超時，重新拋出異常
                raise

    async def _send_timeout_warning(self):
        """發送超時警告消息"""
        try:
            current_player_index = self.game_state.current_player_index
            if current_player_index is None:
                return

            # 獲取當前玩家的消息對象
            if current_player_index == 0:
                message = self.game_state.player1_message
                player_name = self.game_state.player1.username
            else:
                message = self.game_state.player2_message
                player_name = self.game_state.player2.username

            if message:
                # 發送臨時警告消息
                warning_embed = discord.Embed(
                    title="⚠️ 行動時間警告",
                    description=f"**{player_name}，您還有10秒時間進行行動！**\n\n"
                    f"⏰ 如果不在10秒內行動，將自動棄牌\n"
                    f"🎯 請盡快選擇您的行動",
                    color=discord.Color.orange(),
                )

                # 發送臨時消息（5秒後自動刪除）
                await message.channel.send(embed=warning_embed, delete_after=5)
                logger.info("已向玩家 %s 發送超時警告", player_name)

        except Exception as e:
            logger.error("發送超時警告失敗: %s", e, exc_info=True)

    async def _process_command(self, command):
        """
        處理單個命令 - 最終修正版
        此函數現在只包含核心業務邏輯，異常將向上冒泡到 _processor_loop 進行處理。
        """
        old_game_over = self.game_state.game_over

        # 應用命令。成功時返回 None，失敗時拋出異常。
        # 異常將由 _processor_loop 捕捉並通過 future 傳遞回 UI 層。
        poker_logic.apply_command(self.game_state, command)

        # 核心修正：只要上面沒有異常，就必須執行後續所有步驟。
        logger.info(
            "遊戲 %s 狀態更新成功: stage=%s, winner=%s, game_over=%s",
            self.game_state.game_id,
            self.game_state.current_stage.value,
            self.game_state.winner_id,
            self.game_state.game_over,
        )

        # 1. 強制渲染UI
        await self._render_game_state()

        # 2. 處理後續流程
        hand_just_ended = (
            self.game_state.winner_id is not None and not self.game_state.game_over
        )
        if hand_just_ended:
            logger.info("遊戲 %s 一手牌結束，啟動7秒倒數計時", self.game_state.game_id)
            await self._handle_hand_end_countdown()
        elif self.game_state.game_over and not old_game_over:
            logger.info(
                "遊戲 %s 已結束，處理器將在下個循環退出", self.game_state.game_id
            )

    async def _render_game_state(self):
        """調用 Cog 的渲染服務來更新UI"""
        try:
            # 調用 Cog 提供的渲染方法
            if hasattr(self.cog, "render_game_views"):
                await self.cog.render_game_views(self.game_state)
            else:
                logger.warning("Cog 沒有提供 render_game_views 方法")

        except Exception as e:
            logger.error("渲染遊戲狀態時發生錯誤: %s", e, exc_info=True)

    async def _handle_hand_end_countdown(self):
        """處理一手牌結束的倒數計時（延遲7秒後檢查是否繼續）"""
        try:
            logger.info(
                "遊戲 %s 一手牌結束，開始7秒倒數計時...", self.game_state.game_id
            )

            # 延遲7秒讓玩家看結果和倒數計時
            await asyncio.sleep(7)

            # 重新檢查遊戲狀態（可能在等待期間被取消）
            if self.game_state.game_over:
                logger.info("遊戲 %s 已標記為結束，停止繼續", self.game_state.game_id)
                return

            if not self.is_running:
                logger.info("遊戲處理器 %s 已停止，停止繼續", self.game_state.game_id)
                return

            # 檢查雙方是否還能繼續遊戲
            can_continue = self._check_players_can_continue()
            logger.info(
                "遊戲 %s 籌碼檢查結果: %s", self.game_state.game_id, can_continue
            )

            if can_continue:
                # 雙方都可以繼續，開始新手牌
                logger.info(
                    "遊戲 %s 雙方籌碼充足，自動開始新手牌...", self.game_state.game_id
                )
                self.game_state.start_new_hand()
                # 重新渲染UI
                await self._render_game_state()
            else:
                # 有人籌碼不足，結束整個遊戲
                logger.info(
                    "遊戲 %s 有玩家籌碼不足，結束整個遊戲", self.game_state.game_id
                )
                self._finalize_game_due_to_insufficient_chips()
                # 重新渲染UI
                await self._render_game_state()

        except Exception as e:
            logger.error("處理手牌結束倒數計時失敗: %s", e, exc_info=True)

    def _check_players_can_continue(self) -> bool:
        """檢查雙方玩家是否還能繼續遊戲"""
        config = STAKE_CONFIGS[self.game_state.stake_tier]
        big_blind = config["big_blind"]

        player1_can_play = self.game_state.player1.chips >= big_blind
        player2_can_play = self.game_state.player2.chips >= big_blind

        return player1_can_play and player2_can_play

    def _finalize_game_due_to_insufficient_chips(self):
        """因籌碼不足而結束遊戲"""
        # 確定最終勝者
        if self.game_state.player1.chips > self.game_state.player2.chips:
            self.game_state.winner_id = self.game_state.player1.user_id
            self.game_state.game_end_reason = "對手籌碼不足"
        elif self.game_state.player2.chips > self.game_state.player1.chips:
            self.game_state.winner_id = self.game_state.player2.user_id
            self.game_state.game_end_reason = "對手籌碼不足"
        else:
            self.game_state.winner_id = None
            self.game_state.game_end_reason = "雙方籌碼不足，平局"

        self.game_state.game_over = True
