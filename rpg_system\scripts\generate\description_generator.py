"""
描述生成器

處理技能描述的生成，支援不同類型技能的描述模板處理。
"""

import os
import sys
from typing import Any, Dict, Optional

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

# 為了類型提示，需要導入 RarityManager
from typing import TYPE_CHECKING

from .error_handler import error_handler

if TYPE_CHECKING:
    from .rarity_manager import RarityManager


class DescriptionGenerator:
    """描述生成器，處理技能描述的生成"""

    def __init__(
        self,
        descriptions: Dict[str, Any],
        raw_templates: Dict[str, Any],
        rarity_manager: "RarityManager",
    ):
        """初始化描述生成器

        Args:
            descriptions: 描述模板字典
            raw_templates: 原始模板數據
            rarity_manager: 稀有度管理器實例
        """
        self.descriptions = descriptions
        self.raw_templates = raw_templates
        self.rarity_manager = rarity_manager

    @error_handler(
        default_return=lambda: "技能描述生成錯誤", log_message="生成描述時出錯"
    )
    def generate_description(
        self,
        name: str,
        template_key: str,
        effect_category: str,
        rarity: int,
        primary_effect_def: Optional[Dict[str, Any]] = None,
        trigger_desc: str = "",
    ) -> str:
        """生成技能描述

        Args:
            name: 技能名稱
            template_key: 模板鍵
            effect_category: 效果分類
            rarity: 稀有度等級
            primary_effect_def: 主要效果定義
            trigger_desc: 觸發描述

        Returns:
            生成的技能描述
        """
        base_power_multiplier = self.rarity_manager.get_multiplier(rarity, "power")
        duration = max(
            1, int(3 * self.rarity_manager.get_multiplier(rarity, "duration"))
        )
        prefix = f"{trigger_desc}：" if trigger_desc else ""

        # 直接在整個 config/skill_descriptions.json 中查找模板名稱，不依賴分類
        effect_template_name = (
            primary_effect_def.get("effect_template", "") if primary_effect_def else ""
        )
        description_template_data = None
        found_category = ""

        # 在所有分類中查找模板名稱
        for category, templates in self.descriptions.items():
            if effect_template_name in templates:
                description_template_data = templates[effect_template_name]
                found_category = category
                break
            elif template_key in templates:
                description_template_data = templates[template_key]
                found_category = category
                break

        # 如果找不到描述模板，拋出錯誤
        if not description_template_data:
            raise KeyError(
                f"在所有分類中都找不到效果模板 '{effect_template_name}' 或 '{template_key}' 的描述模板"
            )

        # 提取主描述模板
        if isinstance(description_template_data, dict):
            description_template = description_template_data.get("main_description", "")
        else:
            # 向後兼容舊格式（字符串）
            description_template = description_template_data

        # 使用找到的分類來處理後續邏輯
        actual_category = found_category if found_category else effect_category

        # 準備格式化參數（使用實際找到的分類）
        format_params = self._prepare_format_params(
            name,
            prefix,
            template_key,
            actual_category,
            base_power_multiplier,
            duration,
            rarity,
            primary_effect_def,
        )

        # 添加skill_level作为格式化参数
        format_params["skill_level"] = "skill_level"

        # 替换描述中的公式，使其显示为实际的计算公式（使用實際找到的分類）
        if primary_effect_def is not None:
            description_template = self._process_formula_replacements(
                description_template,
                actual_category,
                primary_effect_def,
                format_params,
                rarity,
            )

        return description_template.format(**format_params)

    def generate_secondary_description(self, effect_template_name: str) -> str:
        """生成次要效果的簡短描述

        Args:
            effect_template_name: 效果模板名稱

        Returns:
            次要效果的簡短描述
        """
        # 在所有分類中查找模板名稱
        for _, templates in self.descriptions.items():
            if effect_template_name in templates:
                description_template_data = templates[effect_template_name]
                if isinstance(description_template_data, dict):
                    return description_template_data.get(
                        "secondary_description", "附加效果"
                    )
                else:
                    # 向後兼容舊格式，返回通用描述
                    return "附加效果"

        # 如果找不到，返回通用描述
        return "附加效果"

    def _process_formula_replacements(
        self,
        description_template: str,
        effect_category: str,
        primary_effect_def: Dict[str, Any],
        format_params: Dict[str, Any],
        rarity: int,
    ) -> str:
        """處理描述模板中的公式替換 - 重構版，純粹的字符串替換

        Args:
            description_template: 描述模板
            effect_category: 效果分類
            primary_effect_def: 主要效果定義
            format_params: 格式化參數
            rarity: 稀有度等級

        Returns:
            處理後的描述模板
        """
        # 簡化邏輯：直接使用效果定義中的最終公式，不再進行任何修改
        if (
            effect_category == "damage"
            and primary_effect_def
            and "multiplier" in primary_effect_def
        ):
            # 直接使用效果定義中的最終 multiplier 公式
            multiplier_formula = primary_effect_def["multiplier"]
            description_template = description_template.replace(
                "{power}", multiplier_formula
            )
            # 所有 power 變體都使用相同的公式，保持一致性
            description_template = description_template.replace(
                "{power_boosted}", multiplier_formula
            )
            description_template = description_template.replace(
                "{power_doubled}", multiplier_formula
            )
            description_template = description_template.replace(
                "{power_half}", multiplier_formula
            )
            description_template = description_template.replace(
                "{power_third}", multiplier_formula
            )
            description_template = description_template.replace(
                "{power_fifth}", multiplier_formula
            )
        elif (
            effect_category == "heal"
            and primary_effect_def
            and "value" in primary_effect_def
        ):
            # 直接使用效果定義中的最終 value 公式
            heal_formula = primary_effect_def["value"]
            description_template = description_template.replace("{value}", heal_formula)
            description_template = description_template.replace("{power}", heal_formula)
            # 所有 power 變體都使用相同的公式
            description_template = description_template.replace(
                "{power_boosted}", heal_formula
            )
            description_template = description_template.replace(
                "{power_doubled}", heal_formula
            )
            description_template = description_template.replace(
                "{power_half}", heal_formula
            )
            description_template = description_template.replace(
                "{power_third}", heal_formula
            )
            description_template = description_template.replace(
                "{power_fifth}", heal_formula
            )
        elif effect_category == "damage" and not (
            primary_effect_def and "multiplier" in primary_effect_def
        ):
            # 如果沒有效果定義，拋出錯誤
            raise ValueError("傷害技能缺少 multiplier 定義，無法生成描述")
        elif effect_category == "heal" and not (
            primary_effect_def and "value" in primary_effect_def
        ):
            # 如果沒有效果定義，拋出錯誤
            raise ValueError("治療技能缺少 value 定義，無法生成描述")

        # 效果持續時間和觸發概率的處理
        if effect_category in ["buff", "debuff", "control", "special"]:
            description_template = self._handle_effect_duration_and_chance(
                description_template, rarity, primary_effect_def
            )

        return description_template

    def _handle_effect_duration_and_chance(
        self, description_template: str, rarity: int, primary_effect_def: Dict[str, Any]
    ) -> str:
        """處理效果持續時間和觸發概率 - 重構版，純粹的字符串替換

        Args:
            description_template: 描述模板
            rarity: 稀有度等級（保留參數以維持接口兼容性）
            primary_effect_def: 主要效果定義

        Returns:
            處理後的描述模板
        """
        # 直接使用 primary_effect_def 中的最終公式，不進行任何修改
        if "{duration}" in description_template:
            if primary_effect_def and "duration_turns" in primary_effect_def:
                # 直接使用效果定義中的最終持續時間公式
                duration_formula = primary_effect_def["duration_turns"]
                description_template = description_template.replace(
                    "{duration}", duration_formula
                )
            else:
                raise ValueError(
                    "描述模板需要 {duration} 參數，但效果定義中缺少 duration_turns"
                )

        # 直接使用 primary_effect_def 中的最終概率公式
        if "{chance}" in description_template:
            if primary_effect_def and "chance" in primary_effect_def:
                # 直接使用效果定義中的最終觸發概率公式
                chance_formula = primary_effect_def["chance"]
                description_template = description_template.replace(
                    "{chance}", chance_formula
                )
            else:
                raise ValueError("描述模板需要 {chance} 參數，但效果定義中缺少 chance")

        # 處理 {value_percent} 參數 - 簡化邏輯
        if "{value_percent}" in description_template:
            value_formula = "未知%"  # 默認值

            if primary_effect_def and "value_overrides" in primary_effect_def:
                value_overrides = primary_effect_def["value_overrides"]
                if value_overrides:
                    # 取第一個屬性的公式作為顯示值
                    first_stat = next(iter(value_overrides.keys()))
                    formula = value_overrides[first_stat]
                    # 如果是負值（debuff），去掉負號
                    if formula.startswith("-"):
                        formula = formula[1:]
                    # 直接添加百分比標記，不再處理括號（因為公式已經是最終形式）
                    value_formula = f"{formula} * 100%"

            description_template = description_template.replace(
                "{value_percent}", value_formula
            )

        return description_template

    def _prepare_format_params(
        self,
        name: str,
        prefix: str,
        template_key: str,
        effect_category: str,
        base_power_multiplier: float,
        duration: int,
        rarity: int = 1,
        primary_effect_def: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """準備格式化參數

        Args:
            name: 技能名稱
            prefix: 前綴
            template_key: 模板鍵
            effect_category: 效果分類
            base_power_multiplier: 基礎威力倍率
            duration: 持續時間
            rarity: 稀有度等級
            primary_effect_def: 主要效果定義

        Returns:
            格式化參數字典
        """
        power_text = ""
        value_text = ""  # 用于 heal.flat

        # 优先从 primary_effect_def 获取公式
        if primary_effect_def:
            if effect_category == "damage" and "multiplier" in primary_effect_def:
                power_text = primary_effect_def["multiplier"]
            elif effect_category == "heal" and "value" in primary_effect_def:
                value_text = primary_effect_def["value"]
                power_text = value_text  # 假设heal模板主要用 {value} 或 {power}

        # 如果沒有從 primary_effect_def 獲取到公式，拋出錯誤
        if not power_text and effect_category == "damage":
            raise ValueError("傷害技能缺少 multiplier 定義，無法生成描述")

        if not value_text and effect_category == "heal":
            raise ValueError("治療技能缺少 value 定義，無法生成描述")

        # 簡化邏輯：所有 power 變體都使用相同的公式，保持一致性
        power_boosted_text = power_text

        format_params = {
            "prefix": prefix,
            "name": name,
            "power": power_text,
            "power_boosted": power_boosted_text,
            "duration": str(duration),
            "power_doubled": f"{int(base_power_multiplier * 200)}%",
            "power_half": f"{int(base_power_multiplier * 50)}%",
            "power_third": f"{int(base_power_multiplier * 33)}%",
            "power_fifth": f"{int(base_power_multiplier * 20)}%",
            "damage_type": (
                "物理"
                if "physical" in template_key.lower()
                else ("魔法" if "magical" in template_key.lower() else "未知")
            ),
            "effect_name": "控制",
            "template_key": template_key,
            "effect_category": effect_category,
            "value": value_text if value_text else power_text,
            "value_percent": f"{int(base_power_multiplier * 50)}%",  # 後備值，實際值在 _handle_effect_duration_and_chance 中處理
            "rarity": rarity,
            "skill_level": "skill_level",  # 确保 skill_level 可用于 format
        }

        if effect_category == "control":
            control_map = {
                "stun": "眩暈",
                "silence": "沉默",
                "slow": "減速",
                "freeze": "冰凍",
            }
            format_params["effect_name"] = control_map.get(template_key, "控制")

        return format_params
