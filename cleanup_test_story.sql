-- 清理測試數據的SQL腳本（如果需要重置）
-- Story ID: 0beb6b09-a4a4-4b86-88a8-e9696da8e4e5

-- 選項1：只刪除新增的測試數據（保留原有的103個turn）
DELETE FROM story_turns 
WHERE story_id = '0beb6b09-a4a4-4b86-88a8-e9696da8e4e5' 
AND turn_number > 103;

-- 選項2：清理所有chunk summaries（如果要重新測試總結功能）
DELETE FROM story_chunk_summaries 
WHERE story_id = '0beb6b09-a4a4-4b86-88a8-e9696da8e4e5';

-- 檢查當前數據狀態
SELECT 
    COUNT(*) as total_turns,
    MAX(turn_number) as max_turn,
    MAX(turn_number)/2 as floors
FROM story_turns 
WHERE story_id = '0beb6b09-a4a4-4b86-88a8-e9696da8e4e5';