"""
配置模組 - 包含與API相關的設置和配置參數
"""

import os
import sys

# 確保可以導入根目錄的模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 使用統一的日誌系統
from utils.logger import logger

# 導入統一提示詞

logger.info("auxiliary.services.ai_core.config: 模組開始載入...")

# --- API 配置列表 ---
API_CONFIGS = []  # 將從配置文件中動態載入

try:
    logger.info(
        "auxiliary.services.ai_core.config: "
        "嘗試從 config.app_config 導入 get_settings..."
    )
    from config.app_config import get_settings

    logger.info("auxiliary.services.ai_core.config: 成功導入 get_settings.")

    settings = get_settings()  # 獲取設定實例

    if (
        hasattr(settings, "ai_assistant")
        and settings.ai_assistant
        and hasattr(settings.ai_assistant, "apis")
        and settings.ai_assistant.apis
    ):
        logger.info(
            "auxiliary.services.ai_core.config: 發現API配置列表，共 %d 個API",
            len(settings.ai_assistant.apis),
        )

        for api_config in settings.ai_assistant.apis:
            config_dict = {
                "priority": api_config.priority,
                "name": api_config.name,
                "endpoint": api_config.endpoint,
                "model": api_config.model,
                "api_key": api_config.api_key,
            }
            API_CONFIGS.append(config_dict)
            logger.info(
                (
                    "auxiliary.services.ai_core.config: 載入API配置 - "
                    "優先級: %d, 名稱: %s, 端點: %s, 模型: %s, Key已配置: %s"
                ),
                api_config.priority,
                api_config.name,
                api_config.endpoint,
                api_config.model,
                bool(api_config.api_key),
            )

        # 按優先級排序
        API_CONFIGS.sort(key=lambda x: x["priority"])
        logger.info("auxiliary.services.ai_core.config: API配置已按優先級排序")
    else:
        logger.warning(
            "auxiliary.services.ai_core.config: "
            "settings.ai_assistant.apis 未配置或為空。AI 功能將不可用。"
        )

except ImportError as e:
    logger.error(
        (
            "auxiliary.services.ai_core.config: 無法從 config.app_config 導入 "
            "settings: %s. AI 功能將不可用。"
        ),
        e,
        exc_info=True,
    )

except AttributeError as e:
    logger.error(
        (
            "auxiliary.services.ai_core.config: 從 settings 物件讀取屬性時發生 "
            "AttributeError: %s. 可能 settings 結構不完整或鍵名不匹配。"
        ),
        e,
        exc_info=True,
    )

# 圖像處理配置
MAX_IMAGE_SIZE = 4 * 1024 * 1024  # 4MB
MAX_IMAGE_DIMENSIONS = (2000, 2000)  # 最大寬x高

# Discord命令配置
COMMAND_NAME = "rate"  # Discord命令名稱
COMMAND_DESCRIPTION = "上傳並評分你的穿搭照片，獲取專業時尚建議"  # 命令描述


logger.info("auxiliary.services.ai_core.config: 模組載入完畢。")
