import asyncio
import logging

from playwright.async_api import Browser, <PERSON>, Playwright, async_playwright

logger = logging.getLogger(__name__)

# 1. 【新】將配置和狀態提升為模組級變數
#    這些配置可以從 app_config 讀取
_HEADLESS = True
_BROWSER_TYPE = "chromium"
_PAGE_POOL_SIZE = 10  # 頁面池大小，可根據併發量調整

_playwright: Playwright | None = None
_browser: Browser | None = None
_heartbeat_page: Page | None = None
_page_pool: asyncio.Queue[Page] | None = None  # 頁面池
_pool_context = None  # 專用於頁面池的上下文
_lock = asyncio.Lock()
_initialized = False


async def _is_browser_healthy() -> bool:
    """非侵入性地快速檢查瀏覽器是否仍在運行且可響應。"""
    global _browser
    if not _browser or not _browser.is_connected():
        logger.debug("瀏覽器實例不存在或未連接")
        return False
    try:
        _ = _browser.version
        contexts = _browser.contexts
        if not contexts:
            logger.warning("瀏覽器沒有可用的上下文")
            return False
        logger.debug("瀏覽器健康檢查通過")
        return True
    except Exception as e:
        logger.warning("瀏覽器健康檢查失敗，可能已斷開連接: %s", e)
        return False


async def _reinitialize_browser():
    """
    [強化] 安全地重新初始化瀏覽器實例。
    """
    global _lock
    async with _lock:
        if await _is_browser_healthy():
            logger.info("瀏覽器已被其他任務恢復，本次重新初始化已跳過。")
            return

        logger.warning("檢測到瀏覽器連接丟失或不健康，正在執行線程安全的重新初始化...")
        await close()  # 使用新的模組級 close
        await initialize()  # 使用新的模組級 initialize

        if not _initialized or not await _is_browser_healthy():
            logger.critical("瀏覽器重新初始化失敗，管理器處於故障狀態。")
            raise RuntimeError("無法重新初始化瀏覽器。")

        logger.info("瀏覽器重新初始化成功，管理器已恢復正常。")


async def initialize(headless=_HEADLESS, browser_type=_BROWSER_TYPE):
    """
    初始化 Playwright，啟動瀏覽器實例並預熱頁面池。此方法是冪等的。
    """
    global \
        _initialized, \
        _playwright, \
        _browser, \
        _heartbeat_page, \
        _page_pool, \
        _pool_context, \
        _lock

    async with _lock:
        if _initialized:
            logger.info("PlaywrightManager already initialized.")
            return

        logger.info(
            "Initializing PlaywrightManager (headless=%s, browser=%s)...",
            headless,
            browser_type,
        )
        try:
            _playwright = await async_playwright().start()
            browser_launcher = getattr(_playwright, browser_type)
            launch_options = {
                "headless": headless,
                "args": [
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-extensions",
                    "--disable-default-apps",
                    "--disable-sync",
                    "--disable-background-networking",
                    "--memory-pressure-off",
                    "--max_old_space_size=2048",
                    "--disable-web-security",
                    "--allow-running-insecure-content",
                    "--disable-font-subpixel-positioning",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-software-rasterizer",
                    "--disable-background-media-suspend",
                    "--disable-client-side-phishing-detection",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-hang-monitor",
                    "--disable-prompt-on-repost",
                    "--metrics-recording-only",
                    "--no-default-browser-check",
                    "--no-pings",
                    "--password-store=basic",
                    "--use-mock-keychain",
                ],
                "timeout": 20000,
            }
            logger.info("Launching %s with options: %s", browser_type, launch_options)
            _browser = await browser_launcher.launch(**launch_options)

            if _browser:
                _heartbeat_page = await _browser.new_page()
                logger.info("創建了一個心跳頁面以維持瀏覽器連接。")

                # 初始化並預熱頁面池
                logger.info(f"正在預熱頁面池，大小為 {_PAGE_POOL_SIZE}...")
                _page_pool = asyncio.Queue(maxsize=_PAGE_POOL_SIZE)

                # 建立專用於頁面池的上下文
                _pool_context = await _browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent=(
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                        "AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/120.0.0.0 Safari/537.36"
                    ),
                )

                # 預先創建頁面並放入池中
                for i in range(_PAGE_POOL_SIZE):
                    page = await _pool_context.new_page()
                    await _page_pool.put(page)
                    logger.info(f"頁面 {i + 1}/{_PAGE_POOL_SIZE} 已創建並加入池中。")

                _initialized = True
                logger.info(
                    "✅ %s browser launched successfully (version: %s). 頁面池預熱完成。",
                    browser_type.capitalize(),
                    _browser.version,
                )
        except Exception as e:
            logger.error("Failed to initialize Playwright: %s", e, exc_info=True)
            await close()
            raise


async def acquire_page(use_pool: bool = True) -> Page:
    """
    獲取一個瀏覽器頁面。優先從頁面池中獲取，如果禁用池或池為空則創建新頁面。

    Args:
        use_pool: 是否使用頁面池，默認為 True
    """
    global _browser, _page_pool

    if not await _is_browser_healthy():
        await _reinitialize_browser()

    if not _browser:
        logger.critical("在健康檢查和嘗試重啟後，瀏覽器實例依然不可用。")
        raise RuntimeError("瀏覽器實例不可用。")

    # 優先從頁面池獲取
    if use_pool and _page_pool is not None:
        logger.debug("正在從頁面池獲取頁面...")
        try:
            page = await asyncio.wait_for(_page_pool.get(), timeout=10.0)
            logger.debug("成功從池中獲取頁面。")
            return page
        except asyncio.TimeoutError:
            logger.warning("從頁面池獲取頁面超時，改為創建新頁面。")
        except Exception as e:
            logger.warning(f"從頁面池獲取頁面失敗: {e}，改為創建新頁面。")

    # 如果不使用池或池不可用，則創建新頁面
    logger.debug("正在創建新的瀏覽器頁面...")
    try:
        context = await _browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent=(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/120.0.0.0 Safari/537.36"
            ),
        )
        page = await context.new_page()
        return page
    except Exception as e:
        error_msg = str(e)
        logger.error(
            "創建新頁面失敗: %s",
            error_msg,
            exc_info=True,
        )
        connection_errors = [
            "Connection closed",
            "Browser has been closed",
            "new_context",
            "Protocol error",
        ]
        if any(err in error_msg for err in connection_errors):
            global _initialized
            logger.error("檢測到瀏覽器連接問題，標記為不健康狀態")
            _initialized = False
        raise


async def release_page(page: Page, return_to_pool: bool = True):
    """
    釋放頁面。如果是來自頁面池的頁面，清理後歸還到池中；否則關閉頁面。

    Args:
        page: 要釋放的頁面
        return_to_pool: 是否嘗試歸還到頁面池，默認為 True
    """
    global _browser, _page_pool, _pool_context

    if not page or page.is_closed():
        logger.warning("嘗試釋放一個空的或已經關閉的頁面。")
        return

    context = page.context

    # 如果是來自頁面池的頁面，嘗試歸還
    if return_to_pool and _page_pool is not None and context == _pool_context:
        try:
            # 清理頁面狀態
            await page.goto("about:blank", wait_until="domcontentloaded")

            # 歸還到池中
            await _page_pool.put(page)
            logger.debug("頁面已清理並歸還至池中。")
            return
        except Exception as e:
            logger.warning(f"歸還頁面到池中失敗: {e}。將銷毀此頁面。")
            # 歸還失敗，繼續執行下面的關閉邏輯

    # 關閉頁面和上下文
    try:
        await page.close()
        if (
            _browser
            and _browser.is_connected()
            and hasattr(_browser, "contexts")
            and _browser.contexts
        ):
            # 不關閉頁面池專用的上下文
            if (
                context
                and context is not _browser.contexts[0]
                and context != _pool_context
            ):
                await context.close()
    except Exception as e:
        logger.warning("關閉頁面或上下文時出錯（瀏覽器可能已崩潰）: %s", e)


def is_initialized() -> bool:
    """
    檢查 PlaywrightManager 是否已初始化

    Returns:
        bool: 如果已初始化返回 True，否則返回 False
    """
    global _initialized
    return _initialized


async def close():
    """
    優雅地關閉瀏覽器、清空頁面池並停止 Playwright。
    """
    global \
        _initialized, \
        _playwright, \
        _browser, \
        _heartbeat_page, \
        _page_pool, \
        _pool_context, \
        _lock

    async with _lock:
        if not _initialized:
            return

        logger.info("Closing PlaywrightManager...")

        # 清空頁面池
        if _page_pool is not None:
            logger.info("正在清空頁面池...")
            while not _page_pool.empty():
                try:
                    page = _page_pool.get_nowait()
                    if not page.is_closed():
                        await page.close()
                except Exception as e:
                    logger.warning(f"清理頁面池中的頁面時出錯: {e}")

        if _heartbeat_page and not _heartbeat_page.is_closed():
            try:
                await _heartbeat_page.close()
            except Exception as e:
                logger.error("關閉心跳頁面時出錯: %s", e, exc_info=True)

        if _browser:
            try:
                await _browser.close()
            except Exception as e:
                logger.error("Error closing browser: %s", e, exc_info=True)

        if _playwright:
            try:
                await _playwright.stop()
            except Exception as e:
                logger.error("Error stopping Playwright: %s", e, exc_info=True)

        _heartbeat_page = None
        _page_pool = None
        _pool_context = None
        _browser = None
        _playwright = None
        _initialized = False
        logger.info("PlaywrightManager closed successfully.")
