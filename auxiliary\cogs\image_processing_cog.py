"""
圖像處理相關Discord命令
"""

import asyncio
import os
from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands
from PIL import Image

from auxiliary.services.image_processing.gif_modifier.avatar_gif_overlay import (
    create_global_palette_and_convert,
    fetch_avatar,
    overlay_avatar_on_gif,
    save_gif,
)
from auxiliary.services.image_processing.gif_modifier.config import get_gif_config
from auxiliary.services.image_processing.gif_modifier.kick_trash_overlay import (
    generate_kick_trash_frames,
)
from auxiliary.services.image_processing.gif_modifier.petpet_overlay import (
    overlay_petpet_gif,
)
from auxiliary.services.image_processing.gif_modifier.play_overlay import (
    overlay_play_gif,
)
from auxiliary.services.image_processing.gif_modifier.punch_overlay import (
    overlay_punch_gif,
)
from gacha.views.utils import get_gif_type_choices
from utils.logger import logger

# GIF消息模板
GIF_MESSAGES = {
    "frog.gif": "{user_mention} 想吃迪奧了484",
    "uncle_abe.gif": "{user_mention} 吃阿北薯條啦",
    "punching.gif": "{user_mention} 正在使用渾身解數打拳!",
    "kick_trash.gif": "{user_mention} 被踢進了垃圾桶！🗑️",
    "誰想要約會.gif": "{user_mention} 想要約會...",
    "petpet.gif": "摸摸{user_mention} 🥰",
    "都不帶我玩.gif": "{target_user_mention} 又沒有帶 {user_mention} 玩了！",
}

# 默認消息模板
DEFAULT_GIF_MESSAGE = "{user_mention} 的{gif_type}GIF"

# GIF尺寸設定 - 針對不同GIF指定特殊輸出尺寸
GIF_SIZES = {
    "frog.gif": (150, 150),
    "uncle_abe.gif": (200, 122),
    "punching.gif": (200, 200),
    "誰想要約會.gif": (220, 242),  # 修改約会GIF尺寸
    "petpet.gif": (112, 112),  # 摸頭GIF尺寸
    "都不帶我玩.gif": (360, 360),  # 都不帶我玩GIF尺寸
}

# 默認GIF尺寸
DEFAULT_GIF_SIZE = (150, 150)


async def overlay_kick_trash_gif(
    gif_path_identifier: str,
    user: discord.User | discord.Member,
    output_path: Optional[str] = None,
):
    """
    處理"踢飛頭像進垃圾桶"GIF的疊加邏輯。
    gif_path_identifier 實際上是 "kick_trash.gif" 這個鍵。
    """
    config = get_gif_config(gif_path_identifier)

    avatar_img = await fetch_avatar(user)
    if avatar_img is None:
        raise ValueError("無法獲取用戶頭像")

    # 將 PIL 操作移到線程中
    assert avatar_img is not None  # 幫助類型檢查器

    def convert_avatar() -> Image.Image:
        assert avatar_img is not None
        return avatar_img.convert("RGBA")

    avatar_img = await asyncio.to_thread(convert_avatar)

    # 生成所有動畫影格 (移到線程中避免阻塞)
    processed_frames = await asyncio.to_thread(
        generate_kick_trash_frames, avatar_img, config
    )

    if not processed_frames:
        raise ValueError("未能為 kick_trash 生成任何影格")

    # 創建輸出路徑
    final_output_path = output_path
    if final_output_path is None:
        output_dir = os.path.join("auxiliary", "services", "image_processing", "temp")
        # 將目錄創建移至背景執行緒
        await asyncio.to_thread(os.makedirs, output_dir, exist_ok=True)
        final_output_path = os.path.join(
            output_dir,
            "kick_trash_%s_%s.gif" % (user.id, int(discord.utils.utcnow().timestamp())),
        )

    # 使用全局調色板轉換優化（與其他算法保持一致）(移到線程中避免阻塞)
    converted_frames = await asyncio.to_thread(
        create_global_palette_and_convert, processed_frames
    )

    # 直接保存 GIF 文件 (移到線程中避免阻塞)
    if not converted_frames:
        raise ValueError("converted_frames 為空，無法保存 GIF")

    # Ensure frame_duration is an integer
    frame_duration = config.get("frame_duration", 50)
    if not isinstance(frame_duration, int):
        frame_duration = 50

    await asyncio.to_thread(
        save_gif, converted_frames, final_output_path, frame_duration
    )

    logger.info("踢垃圾桶GIF處理完成，返回路徑: %s", final_output_path)
    return final_output_path


class ImageProcessingCog(commands.Cog, name="圖像處理"):
    def __init__(self, bot):
        self.bot = bot
        self._gif_base_path = os.path.join(
            "auxiliary", "services", "image_processing", "gifs"
        )

    @app_commands.command(name="gif", description="將用戶頭像疊加到GIF上")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        gif_type="要使用的GIF類型", user="要處理的用戶頭像 (默認為自己)"
    )
    @app_commands.choices(gif_type=get_gif_type_choices())
    async def gif_overlay(
        self,
        interaction: discord.Interaction,
        gif_type: str,
        user: Optional[discord.User] = None,
    ):
        """
        將用戶頭像疊加到指定的GIF上

        參數:
            interaction: Discord交互對象
            gif_type: 要使用的GIF類型
            user: 要處理的用戶，默認為命令使用者
        """
        # 延遲響應，因為處理可能需要時間
        await interaction.response.defer(thinking=True)

        # 確認要處理的用戶
        target_user = user if user else interaction.user

        try:
            # 處理特殊GIF類型
            if gif_type == "kick_trash.gif":
                result_path = await overlay_kick_trash_gif(gif_type, target_user)
            elif gif_type == "petpet.gif":
                result_path = await overlay_petpet_gif(gif_type, target_user)
            elif gif_type == "都不帶我玩.gif":
                # 都不帶我玩需要兩個用戶：指令使用者和被tag的用戶
                result_path = await overlay_play_gif(
                    gif_type, interaction.user, target_user
                )
            else:
                # 處理標準GIF類型
                gif_path = os.path.join(self._gif_base_path, gif_type)
                if not os.path.exists(gif_path):
                    available_gifs = [choice.name for choice in get_gif_type_choices()]
                    await interaction.followup.send(
                        "GIF文件不存在。可用類型: %s" % ", ".join(available_gifs),
                        ephemeral=True,
                    )
                    return

                output_size = GIF_SIZES.get(gif_type, DEFAULT_GIF_SIZE)

                # 選擇處理函數
                if gif_type == "punching.gif":
                    result_path = await overlay_punch_gif(
                        gif_path, target_user, fixed_size=output_size
                    )
                else:
                    result_path = await overlay_avatar_on_gif(
                        gif_path, target_user, fixed_size=output_size
                    )

            # 獲取對應的訊息模板
            message_template = GIF_MESSAGES.get(gif_type, DEFAULT_GIF_MESSAGE)

            # 替換模板中的變量
            if gif_type == "都不帶我玩.gif":
                # 都不帶我玩的特殊消息格式
                message = message_template.format(
                    user_mention=interaction.user.mention,
                    target_user_mention=target_user.mention,
                )
            else:
                message = message_template.format(
                    user_mention=target_user.mention, gif_type=gif_type.split(".")[0]
                )

            # 發送結果
            await interaction.followup.send(message, file=discord.File(result_path))

            # 可選：處理完成後刪除臨時文件（異步）
            await asyncio.to_thread(os.remove, result_path)

        except Exception as e:
            await interaction.followup.send(
                "處理GIF時發生錯誤: %s" % str(e), ephemeral=True
            )


async def setup(bot):
    """註冊圖像處理相關命令"""
    await bot.add_cog(ImageProcessingCog(bot))
