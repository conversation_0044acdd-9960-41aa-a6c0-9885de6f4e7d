"""
Pioneer System Pydantic 資料模型
定義開拓者系統的數據結構和配置驗證模型
"""

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, validator

# ========================================
# 核心數據模型
# ========================================


@dataclass
class PioneerProfile:
    """開拓者個人資料模型"""

    user_id: int
    energy: int = 100
    max_energy: int = 100
    last_energy_update: Optional[datetime] = None
    pending_oil_earnings: int = 0  # 待收取的油幣收益
    total_oil_earnings: int = 0  # 累計油幣收益記錄
    current_era: int = 1
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class PioneerSkill:
    """技能模型"""

    id: int
    user_id: int
    skill_id: str
    level: int = 1
    xp: int = 0


@dataclass
class WarehouseItem:
    """倉庫物品模型"""

    id: int
    user_id: int
    item_id: str
    quantity: int


@dataclass
class QuestProgress:
    """任務進度模型"""

    id: int
    user_id: int
    quest_id: str
    progress: int = 0
    target: int = 0
    completed_at: Optional[datetime] = None


@dataclass
class Facility:
    """設施模型"""

    id: int
    user_id: int
    facility_type: str
    facility_name: Optional[str] = None
    level: int = 1
    upgrades: Optional[Dict[str, Any]] = None
    efficiency_bonus: Decimal = Decimal("0.00")
    last_production_time: Optional[datetime] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class FacilityState:
    """設施槽位狀態模型"""

    facility_id: int
    slot_type: str
    slot_index: int = 0
    item_id: Optional[str] = None
    quantity: int = 0
    max_capacity: int = 100
    slot_config: Optional[Dict[str, Any]] = None
    updated_at: Optional[datetime] = None


@dataclass
class ResearchLevel:
    """研究等級模型"""

    id: int
    user_id: int
    project_id: str
    level: int = 0
    total_invested_oil: int = 0
    last_upgraded_at: Optional[datetime] = None


# ========================================
# 配置驗證模型
# ========================================


class EnergySystemConfig(BaseModel):
    max_energy: int = Field(..., gt=0)
    energy_regen_minutes: int = Field(..., gt=0)
    energy_regen_amount: int = Field(..., gt=0)


class SkillSystemConfig(BaseModel):
    base_xp_per_level: int = Field(..., gt=0)
    max_skill_level: int = Field(..., gt=0)
    skill_bonus_per_level: float = Field(..., ge=0)


class FacilitySystemConfig(BaseModel):
    max_facilities_per_user: int = Field(..., gt=0)
    facility_slot_base_capacity: int = Field(..., gt=0)
    production_time_variance: float = Field(..., ge=0, le=1)


class WarehouseSystemConfig(BaseModel):
    base_capacity: int = Field(..., ge=0)
    facility_base_capacity: int = Field(..., ge=0)
    level_multiplier: float = Field(..., gt=0)
    upgrade_multiplier: float = Field(..., gt=0)


class ResearchSystemConfig(BaseModel):
    max_research_level: Optional[int] = Field(
        None, description="最大研究等級 (None = 無限制)"
    )
    research_effect_cap: float = Field(..., gt=0)
    research_cost_base: int = Field(..., gt=0)


class BalanceParametersConfig(BaseModel):
    inflation_control: bool
    price_adjustment_factor: float = Field(..., ge=0)
    supply_demand_sensitivity: float = Field(..., ge=0)


class EconomyConfig(BaseModel):
    """經濟配置驗證模型"""

    base_production: Dict[str, float] = Field(..., description="基礎產出配置")
    offline_limits: Dict[str, Union[int, float]] = Field(
        ..., description="離線限制配置"
    )
    energy_system: EnergySystemConfig = Field(..., description="能量系統配置")
    skill_system: SkillSystemConfig = Field(..., description="技能系統配置")
    facility_system: FacilitySystemConfig = Field(..., description="設施系統配置")
    warehouse_system: WarehouseSystemConfig = Field(..., description="倉庫系統配置")
    research_system: ResearchSystemConfig = Field(..., description="研究系統配置")
    balance_parameters: BalanceParametersConfig = Field(
        ..., description="經濟平衡參數配置"
    )


class ItemConfig(BaseModel):
    """物品配置驗證模型"""

    name: str = Field(..., description="物品名稱")
    description: str = Field(..., description="物品描述")
    category: str = Field(..., description="物品分類")
    tier: int = Field(..., ge=1, description="物品等級")
    base_sell_price: int = Field(..., ge=0, description="基礎售價")
    stack_size: int = Field(..., ge=1, description="堆疊上限")
    effects: Optional[List[Dict[str, Any]]] = Field(default=[], description="物品效果")


class RecipeConfig(BaseModel):
    """配方配置驗證模型"""

    name: str = Field(..., description="配方名稱")
    category: str = Field(..., description="配方分類")
    skill_required: Optional[str] = Field(None, description="所需技能")
    min_skill_level: int = Field(1, ge=1, description="最低技能等級")
    craft_time: int = Field(..., ge=1, description="製作時間(秒)")
    inputs: List[Dict[str, Union[str, int]]] = Field(..., description="輸入材料")
    outputs: List[Dict[str, Union[str, int]]] = Field(..., description="輸出物品")
    facility_required: Optional[str] = Field(None, description="所需設施")


class FacilityUpgrade(BaseModel):
    """設施升級配置模型"""

    cost_formula: Optional[str] = Field(None, description="成本公式")
    cost_oil: Optional[int] = Field(None, description="固定油幣成本")
    effect: Optional[str] = Field(None, description="升級效果")
    description: Optional[str] = Field(None, description="升級描述")


class FacilityConfig(BaseModel):
    """設施配置驗證模型"""

    name: str = Field(..., description="設施名稱")
    is_buildable: Optional[bool] = Field(False, description="是否可建造")
    cost_oil: Optional[int] = Field(None, ge=0, description="建造成本(油幣)")
    required_items: Optional[List[Dict[str, Any]]] = Field(
        default=[], description="建造所需物品"
    )
    process_type: str = Field(..., description="處理類型")
    process_time: int = Field(..., ge=1, description="處理時間(秒)")
    inputs: Optional[List[Dict[str, Any]]] = Field(default=[], description="輸入需求")
    outputs: List[Dict[str, Any]] = Field(..., description="輸出產品")
    upgrades: Dict[str, FacilityUpgrade] = Field(..., description="升級配置")

    @validator("process_type")
    def validate_process_type(cls, v):  # noqa: N805
        allowed_types = ["generate", "transform", "sell", "research", "storage"]
        if v not in allowed_types:
            raise ValueError(
                f"Invalid process_type: {v}. Must be one of {allowed_types}"
            )
        return v


class ActionConfig(BaseModel):
    """動作配置驗證模型"""

    name: str = Field(..., description="動作名稱")
    type: str = Field(..., description="動作類型")
    energy_cost: int = Field(
        0, ge=0, description="能量消耗（默認為0，只有指定的動作才消耗能量）"
    )
    requirements: Optional[List[Dict[str, Any]]] = Field(
        default=[], description="執行條件"
    )
    inputs: Optional[List[Dict[str, Any]]] = Field(default=[], description="輸入消耗")
    outputs: List[Dict[str, Any]] = Field(..., description="輸出獎勵")

    @validator("type")
    def validate_action_type(cls, v):  # noqa: N805
        allowed_types = [
            "gather",
            "craft",
            "build_facility",
            "upgrade_facility",
            "research",
            "sell",
            "collect_earnings",
            "check_earnings",
            "facility_sell",
            "era_advance",
            "stock_shop_item",
        ]
        if v not in allowed_types:
            raise ValueError(
                f"Invalid action type: {v}. Must be one of {allowed_types}"
            )
        return v


class EraConfig(BaseModel):
    """時代配置驗證模型"""

    era_id: int = Field(..., ge=1, description="時代ID")
    name: str = Field(..., description="時代名稱")
    description: str = Field(..., description="時代描述")
    requirements: List[Dict[str, Any]] = Field(..., description="晉升條件")
    costs: List[Dict[str, Any]] = Field(..., description="晉升成本")
    unlocks: List[Dict[str, Any]] = Field(..., description="解鎖內容")


class ResearchProjectConfig(BaseModel):
    """研究項目配置驗證模型"""

    name: str = Field(..., description="項目名稱")
    description: str = Field(..., description="項目描述")
    effect_per_level: float = Field(..., gt=0, description="每級效果")
    cost_formula: str = Field(..., description="成本公式")
    max_level: Optional[int] = Field(None, description="最大等級")
    currency: Literal["oil"] = Field(..., description="消耗貨幣類型")


class SkillConfig(BaseModel):
    """技能配置驗證模型"""

    name: str = Field(..., description="技能名稱")
    description: str = Field(..., description="技能描述")


class TaskConfig(BaseModel):
    """任務配置驗證模型"""

    name: str = Field(..., description="任務名稱")
    type: str = Field(..., description="任務類型")
    description: str = Field(..., description="任務描述")
    reset_time: Optional[str] = Field(None, description="重置時間")
    unlock_era: Optional[int] = Field(None, description="解鎖時代")
    objectives: List[Dict[str, Any]] = Field(..., description="任務目標")
    rewards: List[Dict[str, Any]] = Field(..., description="任務獎勵")

    @validator("type")
    def validate_task_type(cls, v):  # noqa: N805
        allowed_types = ["daily", "weekly", "achievement", "era_quest"]
        if v not in allowed_types:
            raise ValueError(f"Invalid task type: {v}. Must be one of {allowed_types}")
        return v


# ========================================
# 動作結果模型
# ========================================


class ActionResult(BaseModel):
    """動作執行結果模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="結果訊息")
    rewards: Optional[List[Dict[str, Any]]] = Field(default=[], description="獲得獎勵")
    costs: Optional[List[Dict[str, Any]]] = Field(default=[], description="消耗資源")
    xp_gained: Optional[Dict[str, int]] = Field(default={}, description="獲得經驗")
    error_code: Optional[str] = Field(None, description="錯誤代碼")

    @classmethod
    def success_result(
        cls,
        message: str,
        rewards: Optional[List[Dict[str, Any]]] = None,
        **kwargs,
    ):
        """創建成功結果"""
        return cls(success=True, message=message, rewards=rewards or [], **kwargs)

    @classmethod
    def failed_result(cls, message: str, error_code: Optional[str] = None):
        """創建失敗結果"""
        return cls(success=False, message=message, error_code=error_code)
