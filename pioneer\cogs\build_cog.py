from typing import List

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.exceptions import PioneerError
from pioneer.modules import action_module
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


async def get_facility_choices(
    interaction: discord.Interaction, current: str
) -> List[app_commands.Choice[str]]:
    """
    從 GameDataLoader 獲取可建造設施的選項列表，包含成本信息
    """
    assert game_data is not None
    choices = []
    try:
        all_facilities = game_data.get_all_facilities()
        all_items = game_data.get_all_items()

        for facility_id, facility_config in all_facilities.items():
            # 過濾掉不可建造的設施
            if not facility_config.is_buildable:
                continue

            # 如果有搜索條件，進行過濾
            if (
                current
                and current.lower() not in facility_config.name.lower()
                and current.lower() not in facility_id.lower()
            ):
                continue

            # 構建成本信息
            cost_parts = []
            if facility_config.cost_oil and facility_config.cost_oil > 0:
                cost_parts.append(f"{facility_config.cost_oil:,} 油幣")

            if facility_config.required_items:
                item_costs = []
                for req_item in facility_config.required_items:
                    item_config = all_items.get(req_item["item_id"])
                    item_name = item_config.name if item_config else req_item["item_id"]
                    item_costs.append(f"{item_name}x{req_item['quantity']}")
                if item_costs:
                    cost_parts.append(", ".join(item_costs))

            # 構建顯示名稱
            cost_text = " | ".join(cost_parts) if cost_parts else "免費"
            display_name = f"{facility_config.name} ({cost_text})"

            # Discord 選項名稱限制為 100 字符
            if len(display_name) > 100:
                display_name = display_name[:97] + "..."

            choices.append(app_commands.Choice(name=display_name, value=facility_id))

        return choices[:25]

    except Exception as e:
        logger.error("從 game_data 載入設施配置失敗: %s", e)
        return []


class BuildCog(commands.Cog):
    """開拓者建造指令"""

    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="build", description="建造一個新設施")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(facility="要建造的設施（顯示所需資源）")
    @app_commands.autocomplete(facility=get_facility_choices)
    async def build(self, interaction: discord.Interaction, facility: str):
        """執行建造設施動作"""
        assert game_data is not None
        await interaction.response.defer(thinking=False)
        assert game_data is not None
        user_id = interaction.user.id
        await repositories.create_pioneer_profile(user_id)
        params = {"facility_type": facility}
        result = await action_module.execute_action(user_id, "build_facility", params)

        if result.success:
            embed = SuccessEmbed(title="🏗️ 建造成功", description=result.message)
            # 可以在這裡添加更多關於新建造設施的信息
            facility_config = game_data.get_facility_config(facility)
            if facility_config:
                embed.add_field(
                    name="設施名稱", value=facility_config.name, inline=False
                )
            await interaction.followup.send(embed=embed)
        else:
            raise PioneerError(result.message)


async def setup(bot):
    """設置 Cog"""
    await bot.add_cog(BuildCog(bot))
