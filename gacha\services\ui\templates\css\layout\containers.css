/* containers.css - 布局容器相關樣式 */

/* 主容器 */
.profile-container {
  width: 100%;
  height: 100%;
  position: relative;
  /* 更現代的背景處理 - 移除漸變，只保留默認圖片 */
  background-image: url('../../background.png'); /* 已修正拼寫 */
  background-size: cover;
  background-position: center;
  overflow: hidden;
  
  /* 添加多層次黑色邊框 - 更精美的設計 */
  border: 12px solid transparent;
  border-image: linear-gradient(
    to bottom right,
    rgba(30, 30, 30, 0.95),
    rgba(10, 10, 10, 0.9),
    rgba(5, 5, 5, 0.95),
    rgba(20, 20, 20, 0.9)
  ) 1;
  
  /* 陰影效果 */
  box-shadow: 
    /* 內陰影，增加深度 */
    inset 0 0 15px rgba(0, 0, 0, 0.7),
    /* 外陰影，提升層次感 */
    0 0 20px rgba(0, 0, 0, 0.5),
    /* 微妙的光暈效果 */
    0 0 2px rgba(255, 255, 255, 0.05);
  
  box-sizing: border-box; /* 確保邊框計入總尺寸 */
}

/* 新增：為 profile-container 添加 ::before 偽元素作為漸變疊層和紋理 */
.profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    /* 頂層: 噪點紋理 */
    url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADUSURBVGhD7diBCYMwFEXR7uAIjuAIjuAIjuAIjuAIHcERHKEjdARH6A/kgwQilH/7Cy+HQIgQL4ESIYQQgghhBBCyOdk67xszQut5a1/rZXrVDeWrtrNWPM1V5WE6mdrv8/V3urWlTplbj1p3Z0vd5ahONyPJFRzYcoPRMKUHoiU6eMHEsXnRRwv4nkZ04tZXsz2cpaXc72c6+V8L+d7OT+S8yMZ35fxfRnfl/F9Gd+X8X0Z35fxfRnfl/F9Gd+X8X0Z35fxvZwfyfmRjO/L+L6MD0IIIYRkS0o33TvEHe8zNdwAAAAASUVORK5CYII='),
    /* 中間層: 光效 */
    radial-gradient(circle at top right, rgba(99, 102, 241, 0.05), transparent 35%),
    radial-gradient(circle at bottom left, rgba(16, 185, 129, 0.05), transparent 35%),
    /* 底層: 深色漸變 */
    linear-gradient(rgba(0, 0, 0, 0.7), rgba(10, 10, 10, 0.85));
  
  /* 之前的紋理層有 opacity: 0.3 和 background-blend-mode: overlay。
     如果需要在這裡複製類似效果，可能需要調整各層的透明度或嘗試 blend mode。
     此處暫不設置全局 opacity 和 blend-mode，依賴各背景層自身透明度。 */
  /* background-blend-mode: overlay; */
  /* opacity: 0.3; */

  z-index: 1; /* 確保在背景圖片之上，但在內容之下 */
  pointer-events: none; /* 允許點擊穿透 */
}

/* 邊框特效 - 四個角落添加装饰 */
.profile-container::after {
  content: '';
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 30;
  
  /* 增強邊框效果 - 更細緻的線條和角落裝飾 */
  border: 1px solid rgba(255, 255, 255, 0.07);
  
  /* 角落特效 - 精緻的L形裝飾 */
  background: 
    /* 左上角裝飾 */
    linear-gradient(to right, rgba(255, 255, 255, 0.12), transparent 70%) top left / 100px 2px no-repeat,
    linear-gradient(to bottom, rgba(255, 255, 255, 0.12), transparent 70%) top left / 2px 100px no-repeat,
    /* 右上角裝飾 */
    linear-gradient(to left, rgba(255, 255, 255, 0.12), transparent 70%) top right / 100px 2px no-repeat,
    linear-gradient(to bottom, rgba(255, 255, 255, 0.12), transparent 70%) top right / 2px 100px no-repeat,
    /* 左下角裝飾 */
    linear-gradient(to right, rgba(255, 255, 255, 0.12), transparent 70%) bottom left / 100px 2px no-repeat,
    linear-gradient(to top, rgba(255, 255, 255, 0.12), transparent 70%) bottom left / 2px 100px no-repeat,
    /* 右下角裝飾 */
    linear-gradient(to left, rgba(255, 255, 255, 0.12), transparent 70%) bottom right / 100px 2px no-repeat,
    linear-gradient(to top, rgba(255, 255, 255, 0.12), transparent 70%) bottom right / 2px 100px no-repeat,
    
    /* 角落微光 */
    radial-gradient(circle at top left, rgba(255, 255, 255, 0.05), transparent 50px) top left / 100px 100px no-repeat,
    radial-gradient(circle at top right, rgba(255, 255, 255, 0.05), transparent 50px) top right / 100px 100px no-repeat,
    radial-gradient(circle at bottom left, rgba(255, 255, 255, 0.05), transparent 50px) bottom left / 100px 100px no-repeat,
    radial-gradient(circle at bottom right, rgba(255, 255, 255, 0.05), transparent 50px) bottom right / 100px 100px no-repeat;
}

/* 主要內容區域 - 總體垂直佈局 */
.main-content-area {
  padding: calc(var(--spacing-xl) * 0.6);
  position: relative; /* 確保有定位上下文 */
  z-index: 2; /* 確保在 ::before 偽元素的漸變疊層之上 */
  /* display, flex-direction, height, position, z-index, max-width, margin, overflow-y REMOVED as controlled by Tailwind or HTML structure */
}

/* 上部內容行 (用戶資訊 + 主卡片) */
.upper-content-row {
  /* display, width, flex-shrink REMOVED as controlled by Tailwind */
  gap: var(--spacing-lg); /* Consider using gap-* in HTML e.g. gap-6 if --spacing-lg is 24px */
}

/* 左側用戶資訊欄 */
.user-info-column {
  /* width REMOVED as controlled by Tailwind 'w-full' in HTML */
  display: flex; 
  flex-direction: column; 
}

/* 右側主卡片欄 */
.main-card-column {
  /* width, display, justify-content REMOVED as controlled by Tailwind in HTML */
  flex-direction: column; 
  align-items: center;   
  padding-right: var(--spacing-lg);
  padding-left: calc(var(--spacing-md) * 1.5);
}

/* 通用面板樣式 */
.info-panel {
  background: rgba(22, 22, 26, 0.75); /* 更微妙的半透明背景 */
  backdrop-filter: blur(12px) saturate(180%); 
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: var(--shadow-md);
  color: var(--color-text-light);
}

/* 副卡片面板的容器 - sub-cards-area 的父級，用於控制其佈局，這裡不再直接使用 .sub-cards-panel */
/* .sub-cards-panel 已被移除，副卡片直接在 .sub-cards-area 中處理 */

/*
// 媒體查詢 - 響應式設計 
@media (max-width: 1400px) {
  .main-content-area {
    padding: var(--spacing-lg);
  }
  .upper-content-row {
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-lg);
  }
  .user-info-column, .main-card-column {
    width: 100%;
    align-items: center;
  }
  .main-card-column {
    padding-top: var(--spacing-md);
  }
  .sub-cards-area {
    margin-top: 0;
    flex-wrap: wrap; // 允許卡片換行 
    justify-content: center;
  }
}
*/

/* 布局輔助類 */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
} 