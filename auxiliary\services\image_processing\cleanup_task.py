"""
圖像處理臨時文件清理任務
定期清理臨時文件，防止磁盤空間不足和文件鎖定問題
"""

import logging

from discord.ext import tasks

from auxiliary.services.image_processing.temp_file_manager import temp_file_manager

logger = logging.getLogger(__name__)


class ImageProcessingCleanupTask:
    """圖像處理清理任務"""

    def __init__(self):
        self.cleanup_task = None
        self.is_running = False

    def start(self):
        """啟動清理任務"""
        if not self.is_running:
            self.cleanup_task = self._periodic_cleanup.start()
            self.is_running = True
            logger.info("圖像處理清理任務已啟動")

    def stop(self):
        """停止清理任務"""
        if self.is_running and self.cleanup_task:
            self.cleanup_task.cancel()
            self.is_running = False
            logger.info("圖像處理清理任務已停止")

    @tasks.loop(hours=6)  # 每6小時執行一次
    async def _periodic_cleanup(self):
        """定期清理任務"""
        try:
            logger.info("開始執行圖像處理臨時文件清理...")

            # 清理超過24小時的舊文件
            old_files_cleaned = await temp_file_manager.cleanup_old_files(
                max_age_hours=24
            )

            # 檢查並強制清理可能被鎖定的文件
            locked_files_cleaned = await temp_file_manager.force_cleanup_locked_files()

            # 獲取臨時目錄大小
            temp_dir_size = await temp_file_manager.get_temp_dir_size()
            temp_dir_size_mb = temp_dir_size / (1024 * 1024)

            logger.info(
                "圖像處理清理完成 - 清理舊文件: %d, 清理鎖定文件: %d, "
                "臨時目錄大小: %.2f MB",
                old_files_cleaned,
                locked_files_cleaned,
                temp_dir_size_mb,
            )

            # 如果臨時目錄過大（超過100MB），發出警告
            if temp_dir_size_mb > 100:
                logger.warning(
                    "臨時目錄大小過大: %.2f MB，建議檢查是否有文件清理問題",
                    temp_dir_size_mb,
                )

        except Exception as e:
            logger.error("圖像處理清理任務執行失敗: %s", e, exc_info=True)

    @_periodic_cleanup.before_loop
    async def _before_cleanup(self):
        """清理任務開始前的準備"""
        logger.debug("圖像處理清理任務準備就緒")

    @_periodic_cleanup.after_loop
    async def _after_cleanup(self):
        """清理任務結束後的處理"""
        if self._periodic_cleanup.is_being_cancelled():
            logger.debug("圖像處理清理任務被取消")
        else:
            logger.debug("圖像處理清理任務結束")


# 全局實例
cleanup_task = ImageProcessingCleanupTask()


# 便利函數
def start_cleanup_task():
    """啟動清理任務"""
    cleanup_task.start()


def stop_cleanup_task():
    """停止清理任務"""
    cleanup_task.stop()


async def manual_cleanup():
    """手動執行清理"""
    try:
        logger.info("手動執行圖像處理臨時文件清理...")

        old_files_cleaned = await temp_file_manager.cleanup_old_files(
            max_age_hours=1
        )  # 清理1小時以上的文件
        locked_files_cleaned = await temp_file_manager.force_cleanup_locked_files()
        temp_dir_size = await temp_file_manager.get_temp_dir_size()
        temp_dir_size_mb = temp_dir_size / (1024 * 1024)

        result = {
            "old_files_cleaned": old_files_cleaned,
            "locked_files_cleaned": locked_files_cleaned,
            "temp_dir_size_mb": temp_dir_size_mb,
        }

        logger.info(
            "手動清理完成 - 清理舊文件: %d, 清理鎖定文件: %d, 臨時目錄大小: %.2f MB",
            old_files_cleaned,
            locked_files_cleaned,
            temp_dir_size_mb,
        )

        return result

    except Exception as e:
        logger.error("手動清理失敗: %s", e, exc_info=True)
        raise
