from typing import TYPE_CHECKING, List, Optional

import discord

from gacha.views.collection.collection_view.base_pagination import (
    BasePaginationView,
)
from gacha.views.embeds.shop.random_ticket_result_embed_builder import (
    build_random_ticket_result_embed,
)
from gacha.views.shop.favorite_button import FavoriteButton

if TYPE_CHECKING:
    from gacha.models.models import Card


class RandomTicketResultView(BasePaginationView):
    """
    顯示隨機券兌換結果的 View, 使用 BasePaginationView 實現分頁。
    """

    def __init__(
        self,
        original_interaction: discord.Interaction,
        ticket_name: str,
        quantity_used: int,
        drawn_cards: List["Card"],
        ticket_definition_pool_type: Optional[str] = None,
        timeout: Optional[float] = 180.0,
    ):
        self.original_interaction = original_interaction
        self.user_id = original_interaction.user.id
        self.ticket_name = ticket_name
        self.quantity_used = quantity_used
        self.drawn_cards = drawn_cards
        self.ticket_definition_pool_type = ticket_definition_pool_type
        self.items_per_page = 1
        self.total_items = len(self.drawn_cards)
        total_pages = self.total_items if self.total_items > 0 else 1
        super().__init__(
            bot=original_interaction.client,  # type: ignore
            user_id=original_interaction.user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=timeout,
        )
        # 注意：這裡不能 await，所以我們在 prepare_initial_message_payload 中異步添加按鈕

    async def _add_favorite_button(self):
        """異步添加或更新收藏按鈕。"""
        # 移除舊按鈕
        for item in self.children:
            if isinstance(item, FavoriteButton):
                self.remove_item(item)

        card_index = self.current_page - 1
        if 0 <= card_index < self.total_items:
            card = self.drawn_cards[card_index]
            from gacha.repositories.collection import user_collection_repository

            is_favorite = await user_collection_repository.get_card_favorite_status(
                self.user_id, card.card_id
            )
            self.add_item(
                FavoriteButton(
                    user_id=self.user_id,
                    card_id=card.card_id,
                    is_favorite=is_favorite or False,
                )
            )

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新視圖到指定頁面並編輯原始消息。"""
        self.current_page = page
        await self._add_favorite_button()  # 更新按鈕
        embed = await self.get_current_page_embed()
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        await interaction.response.edit_message(embed=embed, view=self)

    async def get_current_page_embed(self) -> discord.Embed:
        """為當前頁面生成 discord.Embed。"""
        card_index = self.current_page - 1
        current_card_to_display = (
            self.drawn_cards[card_index] if 0 <= card_index < self.total_items else None
        )
        return build_random_ticket_result_embed(
            interaction=self.original_interaction,
            ticket_name=self.ticket_name,
            quantity_used=self.quantity_used,
            displayed_card=current_card_to_display,
            current_page=self.current_page,
            total_pages=self.total_pages,
            total_cards_drawn=self.total_items,
            ticket_definition_pool_type=self.ticket_definition_pool_type,
        )

    async def send_initial_message(self, interaction: discord.Interaction):
        """發送初始消息。這個方法會被外部調用來啟動視圖。"""
        embed = self.get_current_page_embed()
        return (embed, self)

    async def prepare_initial_message_payload(
        self, interaction: Optional[discord.Interaction] = None
    ) -> tuple[discord.Embed, "RandomTicketResultView"]:
        """準備初始消息的 Embed 和 View 實例。"""
        await self._add_favorite_button()  # 添加初始按鈕
        embed = await self.get_current_page_embed()
        return (embed, self)
