import asyncio
import os
import sys
from typing import Any, Dict, Tuple

# 改進路徑處理，確保能夠找到項目根目錄
current_script_path = os.path.abspath(__file__)
scripts_dir = os.path.dirname(current_script_path)
project_root = os.path.dirname(scripts_dir)  # 獲取項目根目錄

# 添加項目根目錄到Python模塊搜索路徑
sys.path.insert(0, project_root)

import asyncpg  # noqa: E402
from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)
from utils.logger import logger  # noqa: E402

# Load environment variables from .env file
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)


async def fetch_collection_stats(
    conn: asyncpg.Connection, since_timestamp: Any = None
) -> Dict[int, Dict[str, Any]]:
    """
    Fetches total_owned_quantity, unique_owner_count, and favorite_count
    for each card_id from gacha_user_collections.

    Args:
        conn: Database connection
        since_timestamp: If provided, only fetch stats for cards that changed
            since this timestamp
    """
    if since_timestamp:
        # 增量更新：只查詢有變化的卡片
        query = """
        SELECT
            guc.card_id,
            SUM(guc.quantity) AS total_owned_quantity,
            COUNT(DISTINCT guc.user_id) AS unique_owner_count,
            SUM(CASE WHEN guc.is_favorite = TRUE THEN 1 ELSE 0 END) AS favorite_count
        FROM
            gacha_user_collections guc
        WHERE guc.card_id IN (
            SELECT DISTINCT card_id
            FROM gacha_user_collections
            WHERE last_acquired > $1
        )
        GROUP BY
            guc.card_id;
        """
        rows = await conn.fetch(query, since_timestamp)
        logger.info("增量更新：找到 %s 張卡片需要更新統計", len(rows))
    else:
        # 全量更新：查詢所有卡片
        query = """
        SELECT
            card_id,
            SUM(quantity) AS total_owned_quantity,
            COUNT(DISTINCT user_id) AS unique_owner_count,
            SUM(CASE WHEN is_favorite = TRUE THEN 1 ELSE 0 END) AS favorite_count
        FROM
            gacha_user_collections
        GROUP BY
            card_id;
        """
        rows = await conn.fetch(query)
        logger.info("全量更新：處理 %s 張卡片的統計", len(rows))

    stats: Dict[int, Dict[str, Any]] = {}
    for row in rows:
        stats[row["card_id"]] = {
            "total_owned_quantity": row["total_owned_quantity"],
            "unique_owner_count": row["unique_owner_count"],
            "favorite_count": row["favorite_count"],
        }
    return stats


async def fetch_wishlist_stats(
    conn: asyncpg.Connection, since_timestamp: Any = None
) -> Dict[int, int]:
    """
    Fetches wishlist_count for each card_id from gacha_user_wishes.

    Args:
        conn: Database connection
        since_timestamp: If provided, only fetch stats for cards that had
            wishlist changes since this timestamp
    """
    if since_timestamp:
        # 增量更新：只查詢有變化的卡片的完整願望清單統計
        query = """
        SELECT
            guw.card_id,
            COUNT(guw.user_id) AS wishlist_count
        FROM
            gacha_user_wishes guw
        WHERE guw.card_id IN (
            SELECT DISTINCT card_id
            FROM gacha_user_wishes
            WHERE created_at AT TIME ZONE 'Asia/Taipei' > $1
        )
        GROUP BY
            guw.card_id;
        """
        rows = await conn.fetch(query, since_timestamp)
        logger.info("增量更新：找到 %s 張卡片的願望清單需要更新", len(rows))
    else:
        # 全量更新：查詢所有卡片
        query = """
        SELECT
            card_id,
            COUNT(user_id) AS wishlist_count
        FROM
            gacha_user_wishes
        GROUP BY
            card_id;
        """
        rows = await conn.fetch(query)
        logger.info("全量更新：處理 %s 張卡片的願望清單統計", len(rows))

    stats: Dict[int, int] = {row["card_id"]: row["wishlist_count"] for row in rows}
    return stats


async def get_last_market_stats_update(conn: asyncpg.Connection):
    """
    獲取最後一次市場統計更新的時間戳

    Returns:
        datetime object or None
    """
    query = "SELECT MAX(last_sd_calculated_at) FROM gacha_card_market_stats"
    result = await conn.fetchval(query)
    return result  # 直接返回 datetime 對象，不轉換為字符串


async def should_use_incremental_update(conn: asyncpg.Connection) -> Tuple[str, Any]:
    """
    判斷是否應該使用增量更新

    Returns:
        Tuple[str, Optional[datetime]]: (更新策略, 上次更新時間戳)
        更新策略可能的值：
        - "skip": 跳過更新（沒有變化）
        - "incremental": 增量更新
        - "full": 全量更新
    """
    # 檢查是否存在市場統計數據
    stats_count = await conn.fetchval("SELECT COUNT(*) FROM gacha_card_market_stats")

    if stats_count == 0:
        logger.info("市場統計表為空，將執行全量初始化")
        return "full", None

    # 獲取最後更新時間
    last_update = await get_last_market_stats_update(conn)
    if not last_update:
        logger.info("無法獲取最後更新時間，將執行全量更新")
        return "full", None

    # 檢查是否有新的變化
    # 使用 SQL 來處理時區轉換，避免 Python 端的時區問題
    collection_changes = await conn.fetchval(
        "SELECT COUNT(*) FROM gacha_user_collections WHERE last_acquired > $1",
        last_update,
    )

    # 對於 gacha_user_wishes，在 SQL 中將 created_at 轉換為帶時區的時間戳進行比較
    wishlist_changes = await conn.fetchval(
        """SELECT COUNT(*) FROM gacha_user_wishes
           WHERE created_at AT TIME ZONE 'Asia/Taipei' > $1""",
        last_update,
    )

    total_changes = (collection_changes or 0) + (wishlist_changes or 0)

    if total_changes == 0:
        logger.info("沒有發現新的變化，跳過市場統計更新")
        return "skip", None

    # 如果變化量小於總數據的 10%，使用增量更新
    if stats_count and total_changes < stats_count * 0.1:
        logger.info(
            "發現 %s 個變化，將使用增量更新（上次更新時間：%s）",
            total_changes,
            last_update,
        )
        return "incremental", last_update
    else:
        logger.info("發現 %s 個變化（超過 10%%），將使用全量更新", total_changes)
        return "full", None


async def initialize_market_stats(
    conn: asyncpg.Connection, force_full_update: bool = False
):
    """
    初始化或更新 gacha_card_market_stats 表

    Args:
        conn: 數據庫連接
        force_full_update: 是否強制執行全量更新
    """

    # 決定更新策略
    if force_full_update:
        logger.info("強制執行全量更新")
        update_strategy = "full"
        since_timestamp = None
    else:
        update_strategy, since_timestamp = await should_use_incremental_update(conn)

    # 如果策略是跳過，直接返回
    if update_strategy == "skip":
        logger.info("跳過市場統計更新")
        return

    # 獲取統計數據
    if update_strategy == "incremental":
        collection_stats = await fetch_collection_stats(conn, since_timestamp)
        wishlist_stats = await fetch_wishlist_stats(conn, since_timestamp)
    else:  # update_strategy == "full"
        collection_stats = await fetch_collection_stats(conn)
        wishlist_stats = await fetch_wishlist_stats(conn)

    all_card_ids = set(collection_stats.keys()) | set(wishlist_stats.keys())

    if not all_card_ids:
        logger.info("沒有需要更新的卡片")
        return

    # Prepare data for batch insertion/update
    records_to_upsert = []
    for card_id in all_card_ids:
        coll_stat = collection_stats.get(card_id, {})
        wish_count = wishlist_stats.get(card_id, 0)

        records_to_upsert.append(
            {
                "card_id": card_id,
                "total_owned_quantity": coll_stat.get("total_owned_quantity", 0),
                "unique_owner_count": coll_stat.get("unique_owner_count", 0),
                "favorite_count": coll_stat.get("favorite_count", 0),
                "wishlist_count": wish_count,
                # supply_demand_modifier and previous_sd_modifier will use
                # default 1.0000
            }
        )

    # Upsert data into gacha_card_market_stats
    # This query will insert if card_id does not exist, or update if it does.
    # For initialization, it's mostly inserts, but ON CONFLICT handles re-runs.
    upsert_query = """
    INSERT INTO gacha_card_market_stats (
        card_id, total_owned_quantity, unique_owner_count,
        favorite_count, wishlist_count,
        supply_demand_modifier, previous_sd_modifier, last_sd_calculated_at
    ) VALUES (
        $1, $2, $3, $4, $5, 1.0000, 1.0000, CURRENT_TIMESTAMP
    )
    ON CONFLICT (card_id) DO UPDATE SET
        total_owned_quantity = EXCLUDED.total_owned_quantity,
        unique_owner_count = EXCLUDED.unique_owner_count,
        favorite_count = EXCLUDED.favorite_count,
        wishlist_count = EXCLUDED.wishlist_count,
        last_sd_calculated_at = CURRENT_TIMESTAMP;
    """

    # Execute in a transaction
    update_type = "增量" if update_strategy == "incremental" else "全量"
    logger.info("開始執行%s更新，處理 %s 張卡片", update_type, len(records_to_upsert))

    async with conn.transaction():
        updated_count = 0
        for record in records_to_upsert:
            try:
                await conn.execute(
                    upsert_query,
                    record["card_id"],
                    record["total_owned_quantity"],
                    record["unique_owner_count"],
                    record["favorite_count"],
                    record["wishlist_count"],
                )
                updated_count += 1
            except Exception as e:
                logger.error("更新卡片 %s 的統計時出錯: %s", record["card_id"], e)
                # 繼續處理其他卡片，不中斷整個流程

    logger.info("%s更新完成，成功更新 %s 張卡片的市場統計", update_type, updated_count)


async def main(conn: asyncpg.Connection, force_full_update: bool = False):
    """
    主函數，執行市場統計初始化或更新

    Args:
        conn: 數據庫連接
        force_full_update: 是否強制執行全量更新
    """
    try:
        await initialize_market_stats(conn, force_full_update=force_full_update)
        logger.info("市場統計初始化/更新成功完成")
    except Exception as e:
        logger.error("市場統計初始化/更新過程中發生錯誤: %s", e, exc_info=True)
        raise  # 重新拋出異常，讓調用者知道初始化失敗


async def run_standalone():
    """作為獨立腳本運行時的入口點"""
    import argparse

    parser = argparse.ArgumentParser(description="初始化或更新市場統計數據。")
    parser.add_argument(
        "--full",
        action="store_true",
        help="強制執行全量更新，即使系統認為可以進行增量更新。",
    )
    args = parser.parse_args()

    await setup_connections()
    try:
        pool = get_pool()
        if not pool:
            logger.error("數據庫連接池初始化失敗")
            return
        async with pool.acquire() as conn:
            await main(conn, force_full_update=args.full)
    finally:
        await close_connections()


if __name__ == "__main__":
    asyncio.run(run_standalone())
