from typing import TYPE_CHECKING

import discord

from utils.error_handler import handle_interaction_error

if TYPE_CHECKING:
    from utils.error_handler import BotType


class BaseModal(discord.ui.Modal):
    """
    所有 Modal 的基底類別，提供統一的錯誤處理機制。
    """

    def __init__(
        self, *, bot: "BotType", title: str, timeout: float | None = None
    ) -> None:
        super().__init__(title=title, timeout=timeout)
        self.bot = bot

    async def on_error(
        self, interaction: discord.Interaction, error: Exception
    ) -> None:
        """
        通用錯誤處理器。
        與 BaseView.on_error 行為保持一致，將所有錯誤轉發給全域處理器。
        """
        # 將所有錯誤（包括 BusinessError）直接轉發給集中的錯誤處理器
        await handle_interaction_error(interaction, error, self.bot)
