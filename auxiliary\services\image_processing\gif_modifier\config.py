"""
GIF配置模塊
為不同的GIF提供配置參數，定義頭像位置、大小和動畫效果
"""

import os

# GIF配置字典 - 針對不同GIF的特殊配置
GIF_CONFIGS = {
    "frog.gif": {
        "avatar_position": "left_bottom",  # 左下方
        "avatar_size": (70, 70),  # 頭像尺寸
        "offset_x": 5,  # X軸位置偏移 (從左邊緣開始)
        "offset_y": -10,  # Y軸位置偏移 (相對於底部)
        "animation": {  # 動畫效果
            "x_amplitude": 5,  # X軸抖動幅度
            "y_amplitude": 3,  # Y軸抖動幅度
            "phase_shift": 0.33,  # 相位差 (0-1)
            "frequency": 1.0,  # 震動頻率
        },
    },
    "uncle_abe.gif": {
        "avatar_position": "right_bottom",  # 右下方
        "avatar_size": (120, 120),  # 更大的頭像尺寸
        "offset_x": 140,  # X軸位置偏移 (從右邊緣開始)
        "offset_y": -20,  # Y軸位置偏移 (相對於底部)
        "animation": {  # 動畫效果
            "x_amplitude": 12,  # X軸抖動幅度 (提高了幅度)
            "y_amplitude": 10,  # Y軸抖動幅度 (提高了幅度)
            "phase_shift": 0.25,  # 相位差 (0-1) (調整為更快的節奏)
            "frequency": 2.0,  # 新增頻率參數，用於控制震動速度
        },
    },
    "誰想要約會.gif": {
        "avatar_position": "left_bottom",  # 左下方
        "avatar_size": (140, 140),  # 更新頭像尺寸
        "offset_x": 0,  # X軸位置偏移 (從左邊緣開始)
        "offset_y": -25,  # Y軸位置偏移 (相對於底部)
        "animation": {  # 動畫效果
            "x_amplitude": 0,  # X軸抖動幅度
            "y_amplitude": 0,  # Y軸抖動幅度
            "phase_shift": 0,  # 相位差 (0-1)
            "frequency": 0,  # 震動頻率
        },
    },
    "punching.gif": {
        "avatar_position": "center",  # 中間位置
        "avatar_size": (210, 210),  # 頭像尺寸
        "offset_x": 0,  # X軸位置偏移
        "offset_y": 40,  # Y軸位置偏移 (相對於中心)
        "animation": {  # 動畫效果
            "x_amplitude": 0,  # X軸抖動幅度
            "y_amplitude": 0,  # Y軸抖動幅度
            "phase_shift": 0,  # 相位差 (0-1)
            "frequency": 0,  # 震動頻率
        },
    },
    "kick_trash.gif": {
        "gif_source_type": "frames_sequence",
        "source_path_pattern": (
            "auxiliary/services/image_processing/gifs/kick/"
            "frame_{:02d}_delay-0.05s.gif"
        ),
        "total_source_frames": 30,
        "avatar_size": (64, 64),
        "output_gif_size": (211, 254),  # 最終輸出GIF的尺寸 - 已修改為原始踢擊動畫尺寸
        "frame_duration": 50,  # 每一影格的持續時間 (毫秒) - 已修改
        # --- 階段一：踢擊動畫設定 ---
        "kick_phase": {
            "kick_trigger_frame": 17,  # 索引從0開始，所以第18幀是索引17
            "avatar_initial_offset_kick": (
                110,
                230,
            ),  # 頭像在腳邊的 (x,y) 偏移 (相對於GIF左上角，需微調) - 已修改
            "avatar_deformation_scale": (1, 1),  # 被踢中時的形變 - 增強衝擊感
            "kick_fly_frames": 5,  # 踢飛動作在原場景持續的影格數
            "kick_fly_velocity": (
                40,
                -40,
            ),  # 頭像被踢飛的初始每幀 (dx, dy) 速度 - 大幅提高
            "kick_fly_rotation_speed": 45,  # 被踢飛時的每幀旋轉角度 (可選) - 大幅提高
        },
        # --- 階段二：飛入垃圾桶設定 ---
        "trash_phase": {
            "trash_scene_frames": 20,  # 飛入垃圾桶動畫的影格數 - 拉長時間
            "trash_bin_opening_rect": (
                100,
                80,
                200,
                150,
            ),  # (x1,y1,x2,y2) 垃圾桶開口座標
            "avatar_final_pos_in_trash_offset": (
                -25,
                45,
            ),  # 頭像落點相對於開口中心的偏移 - Y掉更深
            "avatar_entry_style": "arc_top_down",
            "arc_start_y_offset_above_bin_top": 60,
            "arc_peak_y_offset_from_linear": 40,
            "avatar_scale_in_trash": 0.55,  # 垃圾桶階段頭像縮放比例
        },
    },
    "petpet.gif": {
        "gif_source_type": "frames_sequence",
        "source_path_pattern": (
            "auxiliary/services/image_processing/gifs/petpet/row-1-column-{}.png"
        ),
        "total_source_frames": 5,
        "avatar_size": (100, 100),  # 寵物原始尺寸
        "output_gif_size": (112, 112),  # 最終輸出GIF的尺寸
        "frame_duration": 60,  # 每一影格的持續時間 (毫秒)
        # 摸頭動畫設定
        "petpet_phase": {
            "squish": 1.25,  # 基礎擠壓程度
            "scale": 0.875,  # 基礎整體縮放
            "sprite_base_position": (15, 25),  # 寵物基礎位置
            # 每一幀的形變偏移 [x_offset, y_offset, width_change, height_change]
            "frame_offsets": [
                [0, 0, 0, 0],  # 影格 0: 初始/結束狀態
                [-4, 12, 4, -12],  # 影格 1: 開始向下壓
                [-12, 18, 12, -18],  # 影格 2: 壓到最低點，最扁
                [-8, 12, 4, -12],  # 影格 3: 開始回彈
                [-4, 0, 0, 0],  # 影格 4: 幾乎回到靜止
            ],
        },
    },
    "都不帶我玩.gif": {
        "gif_source_type": "frames_sequence",
        "source_path_pattern": (
            "auxiliary/services/image_processing/gifs/play/Layer {}.png"
        ),
        "frame_numbers": [
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            10,
            11,
            12,
            13,
            14,
            15,
        ],  # 實際存在的幀編號
        "avatar_size": (160, 160),  # 頭像尺寸
        "output_gif_size": (360, 360),  # 最終輸出GIF的尺寸（匹配原始圖片）
        "frame_duration": 50,  # 每一影格的持續時間 (毫秒)
        # 都不帶我玩動畫設定
        "play_phase": {
            "avatar_position": "center_top",  # 頭像位置：中間正上方
            "avatar_offset": (0, 0),  # 頭像偏移量 (x, y)
            "text_area": {
                "position": (30, 280),  # 文字區域位置 (x, y) - 調整到360x360尺寸
                "max_width": 300,  # 文字區域最大寬度
                "font_size": 20,  # 字體大小 - 稍微縮小
                "font_path": "fonts/NotoSansTC-Regular.ttf",  # 字體路徑
                "text_color": (0, 0, 0),  # 文字顏色 (黑色)
                "line_spacing": 6,  # 行間距
            },
        },
    },
}

# 默認配置
DEFAULT_GIF_CONFIG = {
    "avatar_position": "center_bottom",  # 底部中央
    "avatar_size": (80, 80),  # 頭像尺寸
    "offset_x": 0,  # X軸位置偏移
    "offset_y": -10,  # Y軸位置偏移 (相對於底部)
    "animation": {  # 動畫效果
        "x_amplitude": 0,  # X軸抖動幅度
        "y_amplitude": 0,  # Y軸抖動幅度
        "phase_shift": 0,  # 相位差 (0-1)
        "frequency": 1.0,  # 震動頻率
    },
}


def get_gif_config(gif_path):
    """根據GIF路徑獲取配置"""
    gif_name = os.path.basename(gif_path)
    return GIF_CONFIGS.get(gif_name, DEFAULT_GIF_CONFIG)


def register_gif_config(gif_name, config):
    """註冊新的GIF配置"""
    GIF_CONFIGS[gif_name] = config
    return True


def list_available_gifs():
    """列出所有已配置的GIF名稱"""
    return list(GIF_CONFIGS.keys())
