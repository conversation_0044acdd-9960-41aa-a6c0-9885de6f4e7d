"""
Gacha系統十連抽Embed構建器
"""

from typing import Any, Dict, List, Optional, Union

import discord

from gacha.constants import RarityLevel
from gacha.models.models import Card, CardWithStatus
from gacha.services.ui import card_formatting_service
from gacha.views import utils as view_utils
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from utils.logger import logger


class MultiDrawEmbedBuilder(BaseEmbedBuilder):
    """構建十連抽結果嵌入消息的類"""

    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        cards_results: List[Union[Dict[str, Any], CardWithStatus]],
        balance: int,
        nickname: Optional[str] = None,
    ):
        """初始化十連抽Embed構建器

        參數:
            user: Discord用戶對象
            cards_results: 十連抽結果列表 (已按稀有度排序) - 可以是CardWithStatus對象列表或舊格式的字典列表
            balance: 用戶剩餘油幣
            nickname: 用戶暱稱，如果有的話優先使用
        """
        multi_draw_data = {
            "user": user,
            "cards_results": cards_results,
            "balance": balance,
            "nickname": nickname,
        }
        super().__init__(data=multi_draw_data)
        self.user = user
        self.cards_results = cards_results
        self.balance = balance
        self.nickname = nickname
        self.processed_cards: List[CardWithStatus] = []
        for res in self.cards_results:
            if isinstance(res, dict):
                try:
                    self.processed_cards.append(CardWithStatus.from_result_dict(res))
                except (ValueError, KeyError) as e:
                    logger.error(
                        "Failed to convert dict to CardWithStatus: %s. Dict: %s",
                        e,
                        res,
                    )
            elif isinstance(res, CardWithStatus):
                self.processed_cards.append(res)

    def build_embed(self) -> discord.Embed:
        """創建十連抽總覽的Embed

        返回:
            格式化後的Discord Embed對象
        """
        final_embed_color_obj: discord.Color = self.DEFAULT_EMBED_COLOR
        if self.processed_cards:
            first_result = self.processed_cards[0]
            card_for_color = first_result.card
            pool_type_for_color = first_result.pool_type
            representative_card_rarity_int = card_for_color.rarity
            if representative_card_rarity_int is not None:
                rarity_enum_for_color = None
                try:
                    rarity_enum_for_color = RarityLevel(representative_card_rarity_int)
                except ValueError:
                    logger.warning(
                        "[MultiDrawEmbedBuilder] 無法將代表性稀有度 %s 解析為 RarityLevel。",
                        representative_card_rarity_int,
                    )
                if rarity_enum_for_color:
                    final_embed_color_obj = self._get_rarity_color(
                        rarity_level=rarity_enum_for_color,
                        pool_type=pool_type_for_color,
                    )
        else:
            logger.warning(
                "[MultiDrawEmbedBuilder] processed_cards is empty, cannot determine color."
            )

        embed = self._create_base_embed(
            title="", description="", color=final_embed_color_obj
        )
        self._set_draw_author(
            embed, user=self.user, nickname=self.nickname, is_multi_draw=True
        )

        # 將抽卡結果放在 description 中，參考 card_view.py 的列表模式
        description_content = self._build_draw_results_description()
        embed.description = description_content

        embed.set_footer(text="使用翻頁按鈕查看卡片詳情")
        return embed

    def _build_draw_results_description(self) -> str:
        """構建抽卡結果的 description 內容，保持原本的格式但放到 description 中"""
        if not self.processed_cards:
            return "沒有抽卡結果"

        # 按卡池分組
        pool_results: Dict[str, List[CardWithStatus]] = {}
        for result in self.processed_cards:
            pool_type = result.pool_type or "unknown"
            pool_results.setdefault(pool_type, []).append(result)

        description_lines = []

        # 為每個卡池構建內容，保持原本的格式
        for pool_type, results_list in pool_results.items():
            if not results_list:
                continue

            from config.app_config import get_pool_type_names

            pool_name = get_pool_type_names().get(pool_type, pool_type.capitalize())

            # 添加卡池標題（保持原本的格式）
            description_lines.append(f"**【{pool_name}】**")

            # 使用原本的格式化方法
            formatted_lines = self._generate_card_list_content(
                results_list, shorten_series=False
            )
            description_lines.extend(formatted_lines)
            description_lines.append("")  # 空行分隔不同卡池

        # 組合所有內容
        full_content = "\n".join(description_lines).strip()

        # 檢查長度限制（Discord description 限制 4096 字符）
        if len(full_content) > 4096:
            # 如果超過限制，嘗試縮短系列名
            description_lines = []

            for pool_type, results_list in pool_results.items():
                if not results_list:
                    continue

                from config.app_config import get_pool_type_names

                pool_name = get_pool_type_names().get(pool_type, pool_type.capitalize())
                description_lines.append(f"**【{pool_name}】**")

                # 使用縮短的系列名
                formatted_lines = self._generate_card_list_content(
                    results_list, shorten_series=True
                )
                description_lines.extend(formatted_lines)
                description_lines.append("")

            full_content = "\n".join(description_lines).strip()

            # 如果還是超過限制，使用原本的策略處理
            if len(full_content) > 4096:
                # 使用原本的複雜策略來處理長度限制
                description_lines = []
                for pool_type, results_list in pool_results.items():
                    if not results_list:
                        continue

                    from config.app_config import get_pool_type_names

                    pool_name = get_pool_type_names().get(
                        pool_type, pool_type.capitalize()
                    )
                    description_lines.append(f"**【{pool_name}】**")

                    # 使用原本的 _build_fields_for_pool_results 邏輯但輸出為文字
                    pool_content = self._build_pool_content_for_description(
                        results_list, pool_name
                    )
                    description_lines.append(pool_content)
                    description_lines.append("")

                full_content = "\n".join(description_lines).strip()

                # 最後防線：截斷
                if len(full_content) > 4096:
                    full_content = full_content[:4000] + "\n...(內容過長，已截斷)"

        return full_content

    def _build_pool_content_for_description(
        self, results: List[CardWithStatus], pool_name: str
    ) -> str:
        """為單個卡池構建內容，使用原本的邏輯但輸出為文字而非 field"""
        discord_description_section_limit = 800  # 為每個卡池預留的空間

        # 嘗試完整格式
        formatted_lines_full = self._generate_card_list_content(
            results, shorten_series=False
        )
        content_full = "\n".join(formatted_lines_full)
        if len(content_full) <= discord_description_section_limit:
            return content_full

        # 嘗試縮短系列名
        if len(results) <= 8:
            formatted_lines_shortened = self._generate_card_list_content(
                results, shorten_series=True
            )
            content_shortened = "\n".join(formatted_lines_shortened)
            if len(content_shortened) <= discord_description_section_limit:
                return content_shortened

        # 使用原本的高稀有度 + 統計策略
        high_rarity_results: List[CardWithStatus] = []
        rarity_counts: Dict[RarityLevel, int] = {}

        for res_item in results:
            rarity_level = res_item.card.rarity
            rarity_counts[rarity_level] = rarity_counts.get(rarity_level, 0) + 1
            if rarity_level.value >= RarityLevel.SSR.value:
                high_rarity_results.append(res_item)

        content_parts = []
        if high_rarity_results:
            content_parts.extend(
                self._generate_card_list_content(
                    high_rarity_results, shorten_series=True
                )
            )

        # 為低稀有度添加統計
        for r_lvl_enum in sorted(rarity_counts.keys(), key=lambda r: r.value):
            if (
                r_lvl_enum.value < RarityLevel.SSR.value
                and rarity_counts[r_lvl_enum] > 0
            ):
                content_parts.append(
                    self._format_rarity_count_line(
                        r_lvl_enum, rarity_counts[r_lvl_enum]
                    )
                )

        return "\n".join(content_parts)

    def _generate_card_list_content(
        self,
        results: List[CardWithStatus],
        shorten_series: bool = False,
    ) -> List[str]:
        """生成卡片列表的純文字內容行列表。

        參數:
            results: 卡片結果列表。
            shorten_series: 是否縮短系列名稱。

        返回:
            List[str]: 每張卡片格式化後的字串列表。
        """
        return [
            self._format_card_info(result, shorten_series=shorten_series)
            for result in results
        ]

    def _format_rarity_count_line(
        self, rarity_level: Optional[RarityLevel], count: int
    ) -> str:
        """格式化稀有度統計行。"""
        emoji = view_utils.get_rarity_display_code(rarity_level)
        friendly_code = ""
        if rarity_level:
            if rarity_level == RarityLevel.SSR:
                friendly_code = "SSR"
            else:
                friendly_name_full = view_utils.get_user_friendly_rarity_name(
                    rarity_level
                )
                parts = friendly_name_full.split(" ")
                if (
                    len(parts) > 0
                    and parts[-1].startswith("(")
                    and parts[-1].endswith(")")
                ):
                    friendly_code = parts[-1].strip("()")
                else:
                    friendly_code = str(rarity_level.value)
        else:
            friendly_code = "未知"
        return f"{emoji} **{friendly_code}**: x{count}"

    def _build_fields_for_pool_results(
        self, results: List[CardWithStatus], pool_name: str
    ) -> List[tuple[str, str]]:
        """為單個卡池的結果列表生成 embed 欄位。"""
        fields_to_add = []
        discord_field_limit = 1000
        formatted_lines_full = self._generate_card_list_content(
            results, shorten_series=False
        )
        field_content_full = "\n".join(formatted_lines_full)
        if len(field_content_full) <= discord_field_limit:
            fields_to_add.append((f"【{pool_name}】", field_content_full))
            return fields_to_add
        if len(results) <= 8:
            formatted_lines_shortened = self._generate_card_list_content(
                results, shorten_series=True
            )
            field_content_shortened = "\n".join(formatted_lines_shortened)
            if len(field_content_shortened) <= discord_field_limit:
                fields_to_add.append((f"【{pool_name}】", field_content_shortened))
                return fields_to_add
            else:
                mid_point = len(formatted_lines_shortened) // 2
                content1_lines = formatted_lines_shortened[:mid_point]
                content2_lines = formatted_lines_shortened[mid_point:]
                content1 = "\n".join(content1_lines)
                content2 = "\n".join(content2_lines)
                fields_to_add.append((f"【{pool_name}】", content1))
                fields_to_add.append((f"【{pool_name}】(續)", content2))
                return fields_to_add
        high_rarity_results: List[CardWithStatus] = []
        low_rarity_results: List[CardWithStatus] = []
        rarity_counts: Dict[RarityLevel, int] = {}
        for res_item in results:
            rarity_level = res_item.card.rarity
            rarity_counts[rarity_level] = rarity_counts.get(rarity_level, 0) + 1
            if rarity_level.value >= RarityLevel.SSR.value:
                high_rarity_results.append(res_item)
            else:
                low_rarity_results.append(res_item)
        content_parts = []
        if high_rarity_results:
            content_parts.extend(
                self._generate_card_list_content(
                    high_rarity_results, shorten_series=True
                )
            )
        if low_rarity_results:
            low_rarity_lines_detailed = self._generate_card_list_content(
                low_rarity_results, shorten_series=True
            )
            temp_combined_content = "\n".join(content_parts + low_rarity_lines_detailed)
            if len(temp_combined_content) <= discord_field_limit:
                content_parts.extend(low_rarity_lines_detailed)
            else:
                for r_lvl_enum in sorted(rarity_counts.keys(), key=lambda r: r.value):
                    if (
                        r_lvl_enum.value < RarityLevel.SSR.value
                        and rarity_counts[r_lvl_enum] > 0
                    ):
                        content_parts.append(
                            self._format_rarity_count_line(
                                r_lvl_enum, rarity_counts[r_lvl_enum]
                            )
                        )
        field_content_grouped = "\n".join(content_parts)
        if len(field_content_grouped) <= discord_field_limit:
            fields_to_add.append((f"【{pool_name}】", field_content_grouped))
            return fields_to_add
        t5_plus_results: List[CardWithStatus] = []
        t4_results_for_counting: List[CardWithStatus] = []
        other_rarity_counts_s5: Dict[RarityLevel, int] = {}
        for res_item_final in results:
            card_final = res_item_final.card
            rarity_level_final = card_final.rarity
            if rarity_level_final.value >= RarityLevel.ULTRA_RARE.value:
                t5_plus_results.append(res_item_final)
            elif rarity_level_final == RarityLevel.SSR:
                t4_results_for_counting.append(res_item_final)
            else:
                other_rarity_counts_s5[rarity_level_final] = (
                    other_rarity_counts_s5.get(rarity_level_final, 0) + 1
                )
        final_parts_for_strategy5 = []
        if t5_plus_results:
            final_parts_for_strategy5.extend(
                self._generate_card_list_content(t5_plus_results, shorten_series=True)
            )
        if t4_results_for_counting:
            r4_enum = RarityLevel.SSR
            final_parts_for_strategy5.append(
                self._format_rarity_count_line(r4_enum, len(t4_results_for_counting))
            )
        for r_lvl_enum_final, count in sorted(
            other_rarity_counts_s5.items(), key=lambda item: item[0].value
        ):
            final_parts_for_strategy5.append(
                self._format_rarity_count_line(r_lvl_enum_final, count)
            )
        final_content_ultimate = "\n".join(final_parts_for_strategy5)
        fields_to_add.append((f"【{pool_name}】", final_content_ultimate))
        return fields_to_add

    def _format_card_info(
        self,
        result: CardWithStatus,
        shorten_series: bool = False,
    ) -> str:
        """格式化單張卡片信息 (私有方法)
        現在委託給 CardFormattingService.format_multi_draw_card_summary_line。

        參數:
            result: CardWithStatus對象
            shorten_series: 是否縮短系列名稱

        返回:
            str: 格式化後的卡片信息
        """
        card = result.card
        status = result.status
        is_new_card = status.is_new_card
        is_wish = status.is_wish
        is_favorite = status.is_favorite
        card_rarity_enum: Optional[RarityLevel] = None
        try:
            if isinstance(card.rarity, int):
                card_rarity_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                card_rarity_enum = card.rarity
        except ValueError:
            logger.warning(
                "_format_card_info: Could not parse rarity %s for card %s to RarityLevel.",
                card.rarity,
                card.name,
            )
        return card_formatting_service.format_multi_draw_card_summary_line(
            card_name=card.name,
            card_series=card.series,
            is_favorite=is_favorite,
            is_new_card=is_new_card,
            is_wish=is_wish,
            rarity_enum=card_rarity_enum,
            shorten_series=shorten_series,
        )

    def _build_and_enrich_multi_draw_page_embed(
        self,
        embed_to_enrich: discord.Embed,
        card: Card,
        is_new_card: bool,
        is_wish: bool,
        is_favorite: bool,
        star_level: int,
        pool_type: str,
        owner_count: int,
        current_index: int,
        total_cards: int,
        original_summary_field_count: int,
    ) -> discord.Embed:
        """
        統一處理多抽卡片頁面 Embed 的構建與內容填充。
        在總覽 embed 基礎上添加當前卡片的詳細信息。
        會根據當前顯示的卡片更新 Embed 顏色和內容。

        參數:
            embed_to_enrich: 作為基礎的 Embed 物件（總覽部分）。
            card: 當前要顯示的卡片物件。
            is_new_card: 是否為新卡。
            is_wish: 是否為許願卡。
            is_favorite: 是否為最愛卡。
            star_level: 卡片星級。
            pool_type: 當前卡片的卡池類型。
            owner_count: 卡片擁有者數量。
            current_index: 當前卡片在多抽結果中的索引。
            total_cards: 多抽結果的總卡片數量。
            original_summary_field_count: 原始 embed_to_enrich 中代表總覽部分的欄位數量。
        """
        current_card_rarity_enum: Optional[RarityLevel] = None
        try:
            if isinstance(card.rarity, int):
                current_card_rarity_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                current_card_rarity_enum = card.rarity
        except ValueError:
            logger.warning(
                "MultiDraw (_build_and_enrich): 無法將卡片稀有度 %s (卡片: %s) 解析為 RarityLevel。",
                card.rarity,
                card.name,
            )
        current_card_color = self._get_rarity_color(
            rarity_level=current_card_rarity_enum, pool_type=pool_type
        )
        embed_to_enrich.color = current_card_color
        num_fields_to_remove = (
            len(embed_to_enrich.fields) - original_summary_field_count
        )
        for _ in range(num_fields_to_remove):
            if len(embed_to_enrich.fields) > original_summary_field_count:
                embed_to_enrich.remove_field(len(embed_to_enrich.fields) - 1)
        display_name_for_title = self.nickname or self.user.display_name
        custom_field_name = (
            card_formatting_service.format_multi_draw_page_card_event_title(
                display_name=display_name_for_title,
                is_wish=is_wish,
                pool_type=pool_type,
                is_new_card=is_new_card,
            )
        )
        self._add_card_primary_details_field_or_description_with_price(
            embed=embed_to_enrich,
            card=card,
            is_favorite=is_favorite,
            is_new_card=is_new_card,
            is_wish=is_wish,
            star_level=star_level,
            owner_count=owner_count,
            as_description=False,
            field_name=custom_field_name,
            inline=False,
            pool_type=pool_type,
            balance=self.balance,
        )
        self._set_card_visuals(
            embed=embed_to_enrich,
            card_image_url=card.image_url,
            card_rarity_enum=current_card_rarity_enum,
            pool_type=pool_type,
        )
        self._set_draw_specific_footer(
            embed=embed_to_enrich,
            card_for_id=card,
            is_wish=is_wish,
            owner_count=owner_count,
            multi_draw_page_info=f"第 {current_index + 1}/{total_cards} 張",
            include_bot_signature=True,
        )
        return embed_to_enrich

    def build_combined_embed(
        self,
        card: Card,
        is_new_card: bool,
        is_wish: bool,
        is_favorite: bool,
        star_level: int,
        pool_type: str,
        owner_count: int,
        current_index: int,
        total_cards: int,
    ) -> discord.Embed:
        """
        構建包含總覽和當前卡片詳細信息的完整 embed
        不使用緩存，每次都重新構建
        """
        # 先構建總覽部分
        summary_embed = self.build_embed()
        # 現在總覽部分使用 description，所以 field 數量應該是 0
        summary_field_count = 0

        # 然後添加當前卡片的詳細信息
        return self._build_and_enrich_multi_draw_page_embed(
            embed_to_enrich=summary_embed,
            card=card,
            is_new_card=is_new_card,
            is_wish=is_wish,
            is_favorite=is_favorite,
            star_level=star_level,
            pool_type=pool_type,
            owner_count=owner_count,
            current_index=current_index,
            total_cards=total_cards,
            original_summary_field_count=summary_field_count,
        )
