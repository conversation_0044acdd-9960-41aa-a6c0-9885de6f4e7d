"""
全局技能Repository
負責管理玩家的全局技能熟練度數據
使用BaseRepository的正確API
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from gacha.exceptions import DatabaseOperationError
from gacha.repositories import _base_repo
from rpg_system.exceptions import GlobalSkillNotFoundError

logger = logging.getLogger(__name__)


@dataclass
class GlobalSkillData:
    """全局技能數據模型"""

    user_id: int
    skill_id: str
    skill_type: str  # 'ACTIVE' or 'PASSIVE'
    skill_level: int
    skill_xp: int
    created_at: str
    updated_at: str

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "user_id": self.user_id,
            "skill_id": self.skill_id,
            "skill_type": self.skill_type,
            "skill_level": self.skill_level,
            "skill_xp": self.skill_xp,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }


async def get_user_learned_skills(
    user_id: int, skill_type: Optional[str] = None
) -> List[GlobalSkillData]:
    """
    獲取用戶已學習的技能列表

    Args:
        user_id: 用戶ID
        skill_type: 技能類型過濾（可選）

    Returns:
        技能數據列表

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        query = """
            SELECT user_id, skill_id, skill_type, skill_level, skill_xp,
                   created_at, updated_at
            FROM gacha_user_learned_global_skills
            WHERE user_id = $1
        """
        params: List[Any] = [user_id]

        if skill_type:
            query += " AND skill_type = $2"
            params.append(skill_type)

        query += " ORDER BY skill_type, skill_id"

        rows = await _base_repo.fetch_all(query, params)

        return [
            GlobalSkillData(
                user_id=row["user_id"],
                skill_id=row["skill_id"],
                skill_type=row["skill_type"],
                skill_level=row["skill_level"],
                skill_xp=row["skill_xp"],
                created_at=str(row["created_at"]),
                updated_at=str(row["updated_at"]),
            )
            for row in rows
        ]

    except Exception as e:
        logger.error(
            "獲取用戶技能列表失敗: user_id=%s, error=%s", user_id, e, exc_info=True
        )
        raise DatabaseOperationError(f"獲取技能列表失敗: {str(e)}") from e


async def get_skill_data(
    user_id: int, skill_id: str, skill_type: str
) -> GlobalSkillData:
    """
    獲取特定技能的數據

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        skill_type: 技能類型

    Returns:
        技能數據

    Raises:
        GlobalSkillNotFoundError: 技能未找到
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        query = """
            SELECT user_id, skill_id, skill_type, skill_level, skill_xp,
                   created_at, updated_at
            FROM gacha_user_learned_global_skills
            WHERE user_id = $1 AND skill_id = $2 AND skill_type = $3
        """

        row = await _base_repo.fetch_one(query, [user_id, skill_id, skill_type])

        if not row:
            raise GlobalSkillNotFoundError(skill_id=skill_id)

        return GlobalSkillData(
            user_id=row["user_id"],
            skill_id=row["skill_id"],
            skill_type=row["skill_type"],
            skill_level=row["skill_level"],
            skill_xp=row["skill_xp"],
            created_at=str(row["created_at"]),
            updated_at=str(row["updated_at"]),
        )
    except GlobalSkillNotFoundError:
        raise
    except Exception as e:
        logger.error(
            "獲取技能數據失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"獲取技能數據失敗: {str(e)}") from e


async def learn_skill(
    user_id: int,
    skill_id: str,
    skill_type: str,
    initial_level: int = 1,
    initial_xp: int = 0,
) -> GlobalSkillData:
    """
    學習新技能

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        skill_type: 技能類型
        initial_level: 初始等級
        initial_xp: 初始經驗

    Returns:
        創建的技能數據

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        query = """
            INSERT INTO gacha_user_learned_global_skills
            (user_id, skill_id, skill_type, skill_level, skill_xp, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            ON CONFLICT (user_id, skill_id, skill_type) DO UPDATE SET
                skill_level = EXCLUDED.skill_level,
                skill_xp = EXCLUDED.skill_xp,
                updated_at = NOW()
        """

        await _base_repo.execute_query(
            query, [user_id, skill_id, skill_type, initial_level, initial_xp]
        )

        # 返回創建的技能數據
        skill_data = await get_skill_data(user_id, skill_id, skill_type)

        logger.info("用戶學習技能成功: user_id=%s, skill_id=%s", user_id, skill_id)
        return skill_data

    except Exception as e:
        logger.error(
            "學習技能失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"學習技能失敗: {str(e)}") from e


async def add_skill_xp(
    user_id: int, skill_id: str, skill_type: str, xp_amount: int
) -> GlobalSkillData:
    """
    增加技能經驗

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        skill_type: 技能類型
        xp_amount: 經驗值

    Returns:
        更新後的技能數據

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 檢查技能是否存在(get_skill_data會拋出異常)
        await get_skill_data(user_id, skill_id, skill_type)

        # 更新經驗值
        query = """
            UPDATE gacha_user_learned_global_skills
            SET skill_xp = skill_xp + $1, updated_at = NOW()
            WHERE user_id = $2 AND skill_id = $3 AND skill_type = $4
        """

        await _base_repo.execute_query(
            query, [xp_amount, user_id, skill_id, skill_type]
        )

        # 返回更新後的數據
        updated_skill_data = await get_skill_data(user_id, skill_id, skill_type)

        logger.info(
            "技能經驗增加成功: user_id=%s, skill_id=%s, xp_amount=%s",
            user_id,
            skill_id,
            xp_amount,
        )
        return updated_skill_data

    except Exception as e:
        logger.error(
            "增加技能經驗失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"增加技能經驗失敗: {str(e)}") from e


async def level_up_skill(
    user_id: int,
    skill_id: str,
    skill_type: str,
    new_level: int,
    remaining_xp: int = 0,
) -> GlobalSkillData:
    """
    技能升級

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        skill_type: 技能類型
        new_level: 新等級
        remaining_xp: 剩餘經驗

    Returns:
        更新後的技能數據

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        query = """
            UPDATE gacha_user_learned_global_skills
            SET skill_level = $1, skill_xp = $2, updated_at = NOW()
            WHERE user_id = $3 AND skill_id = $4 AND skill_type = $5
        """

        await _base_repo.execute_query(
            query, [new_level, remaining_xp, user_id, skill_id, skill_type]
        )

        # 返回更新後的數據
        updated_skill_data = await get_skill_data(user_id, skill_id, skill_type)

        logger.info(
            "技能升級成功: user_id=%s, skill_id=%s, new_level=%s",
            user_id,
            skill_id,
            new_level,
        )
        return updated_skill_data

    except Exception as e:
        logger.error(
            "技能升級失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"技能升級失敗: {str(e)}") from e


async def get_skill_count_by_type(user_id: int) -> Dict[str, int]:
    """
    獲取用戶各類型技能的數量統計

    Args:
        user_id: 用戶ID

    Returns:
        技能類型數量字典

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        query = """
            SELECT skill_type, COUNT(*) as count
            FROM gacha_user_learned_global_skills
            WHERE user_id = $1
            GROUP BY skill_type
        """

        rows = await _base_repo.fetch_all(query, [user_id])

        result = {"ACTIVE": 0, "PASSIVE": 0}
        for row in rows:
            skill_type = row["skill_type"]
            count = row["count"]
            result[skill_type] = count

        return result

    except Exception as e:
        logger.error(
            "獲取技能統計失敗: user_id=%s, error=%s", user_id, e, exc_info=True
        )
        raise DatabaseOperationError(f"獲取技能統計失敗: {str(e)}") from e
