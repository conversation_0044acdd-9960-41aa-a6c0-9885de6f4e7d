# gacha/cogs/decorators.py
from datetime import datetime, timedelta, timezone

from discord import Interaction, app_commands


def account_age_check():
    """
    一個 app_commands check，驗證用戶帳號年齡。
    如果失敗，它會返回 False，讓 discord.py 拋出標準的 CheckFailure。
    """

    async def predicate(interaction: Interaction) -> bool:
        # 4 個月約等於 120 天
        four_months_ago = datetime.now(timezone.utc) - timedelta(days=120)

        # 只返回布林值，不再 raise
        return interaction.user.created_at <= four_months_ago

    return app_commands.check(predicate)
