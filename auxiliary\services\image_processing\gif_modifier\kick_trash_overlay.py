"""
踢飛頭像進垃圾桶 GIF 疊加模塊
"""

import os

from PIL import Image


def get_resampling_filter(filter_name: str):
    """獲取 Pillow 重採樣濾鏡，兼容新舊版本"""
    try:
        # Pillow 10.0.0+ 使用 Image.Resampling
        return getattr(Image.Resampling, filter_name)
    except AttributeError:
        # 舊版本 Pillow 直接從 Image 獲取
        return getattr(Image, filter_name, 1)  # 1 是 LANCZOS 的默認值


def _calculate_paste_position_after_rotation(new_size, original_center_paste_pos):
    """
    計算旋轉後的圖像應該粘貼在原背景的哪個位置，
    以使其看起來像是圍繞原始未旋轉圖像的中心點進行了旋轉。
    """
    new_width, new_height = new_size
    new_center_offset_x = new_width / 2
    new_center_offset_y = new_height / 2
    paste_x = original_center_paste_pos[0] - new_center_offset_x
    paste_y = original_center_paste_pos[1] - new_center_offset_y
    return int(paste_x), int(paste_y)


def _preload_kick_frames(config, output_gif_size):
    """預先載入踢擊動畫的影格"""
    total_source_frames = config.get("total_source_frames", 30)
    source_path_pattern = config.get("source_path_pattern", "")
    preloaded_kick_frames = []
    for frame_index in range(total_source_frames):
        frame_path = source_path_pattern.format(frame_index)
        if os.path.exists(frame_path):
            with Image.open(frame_path) as kick_frame:
                if kick_frame.mode != "RGBA":
                    kick_frame = kick_frame.convert("RGBA")
                if kick_frame.size != output_gif_size:
                    kick_frame = kick_frame.resize(
                        output_gif_size, get_resampling_filter("LANCZOS")
                    )
                preloaded_kick_frames.append(kick_frame.copy())
        else:
            if preloaded_kick_frames:
                preloaded_kick_frames.append(preloaded_kick_frames[-1].copy())
            else:
                preloaded_kick_frames.append(
                    Image.new("RGBA", output_gif_size, (0, 0, 0, 0))
                )
    return preloaded_kick_frames


def _generate_kick_phase_frames(
    config, avatar_pil, preloaded_kick_frames, base_avatar_size
):
    """生成踢擊階段的影格"""
    kick_phase_config = config.get("kick_phase", {})
    kick_trigger_frame = kick_phase_config.get("kick_trigger_frame", 17)
    kick_fly_frames = kick_phase_config.get("kick_fly_frames", 5)
    last_kick_frame = kick_trigger_frame + kick_fly_frames - 1

    initial_offset = kick_phase_config.get("avatar_initial_offset_kick", (50, 180))
    center_pos = list(initial_offset)
    angle = 0.0
    is_kicked = False

    velocity = list(kick_phase_config.get("kick_fly_velocity", (15, -25)))
    rotation_speed = kick_phase_config.get("kick_fly_rotation_speed", 15)
    deformation = kick_phase_config.get("avatar_deformation_scale", (1.0, 0.8))

    kick_frames = []

    for i, bg in enumerate(preloaded_kick_frames):
        if is_kicked and i > last_kick_frame:
            break

        bg_copy = bg.copy()
        avatar_frame = avatar_pil.copy()

        if i < kick_trigger_frame:
            paste_pos = (
                int(center_pos[0] - base_avatar_size[0] / 2),
                int(center_pos[1] - base_avatar_size[1] / 2),
            )
            bg_copy.paste(avatar_frame, paste_pos, avatar_frame)
        else:
            is_kicked = True
            deformed_size = (
                int(base_avatar_size[0] * deformation[0]),
                int(base_avatar_size[1] * deformation[1]),
            )
            avatar_frame = avatar_frame.resize(
                deformed_size, get_resampling_filter("LANCZOS")
            )

            if i > kick_trigger_frame:
                center_pos[0] += velocity[0]
                center_pos[1] += velocity[1]
                angle += rotation_speed

            if angle != 0:
                rotated = avatar_frame.rotate(
                    angle, resample=get_resampling_filter("BICUBIC"), expand=True
                )
                paste_pos = _calculate_paste_position_after_rotation(
                    rotated.size, center_pos
                )
                bg_copy.paste(rotated, paste_pos, rotated)
            else:
                paste_pos = (
                    int(center_pos[0] - deformed_size[0] / 2),
                    int(center_pos[1] - deformed_size[1] / 2),
                )
                bg_copy.paste(avatar_frame, paste_pos, avatar_frame)

        kick_frames.append(bg_copy)

    return kick_frames


def _generate_trash_phase_frames(config, avatar_pil, base_avatar_size, output_gif_size):
    """生成飛入垃圾桶階段的影格"""
    trash_config = config.get("trash_phase", {})
    num_frames = trash_config.get("trash_scene_frames", 15)

    back_path = "auxiliary/services/image_processing/gifs/trash_can_back.png"
    front_path = "auxiliary/services/image_processing/gifs/trash_can_front_lip.png"

    if not (os.path.exists(back_path) and os.path.exists(front_path)):
        return []

    with Image.open(back_path) as back_img, Image.open(front_path) as front_img:
        trash_back = back_img.convert("RGBA").resize(
            output_gif_size, get_resampling_filter("LANCZOS")
        )
        trash_front = front_img.convert("RGBA").resize(
            output_gif_size, get_resampling_filter("LANCZOS")
        )

    opening_rect = trash_config.get("trash_bin_opening_rect", (100, 80, 200, 150))
    final_offset = trash_config.get("avatar_final_pos_in_trash_offset", (0, 20))
    scale = trash_config.get("avatar_scale_in_trash", 1.0)

    trash_avatar_size = (
        int(base_avatar_size[0] * scale),
        int(base_avatar_size[1] * scale),
    )
    trash_avatar = avatar_pil.resize(
        trash_avatar_size, get_resampling_filter("LANCZOS")
    )

    start_x = -base_avatar_size[0]
    start_y_offset = trash_config.get("arc_start_y_offset_above_bin_top", 0)
    start_y = opening_rect[1] - start_y_offset

    center_x = (opening_rect[0] + opening_rect[2]) / 2
    center_y = (opening_rect[1] + opening_rect[3]) / 2
    target_x = center_x + final_offset[0]
    target_y = center_y + final_offset[1]

    trash_frames = []
    fall_frames = min(10, num_frames) if num_frames > 0 else 0

    for i in range(num_frames):
        bg_copy = trash_back.copy()

        if i < fall_frames and fall_frames > 0:
            progress = i / (fall_frames - 1) if fall_frames > 1 else 1.0
            curr_x = start_x + (target_x - start_x) * progress
            linear_y = start_y + (target_y - start_y) * progress

            peak_offset = trash_config.get("arc_peak_y_offset_from_linear", 0)
            parabolic_offset = 4 * peak_offset * progress * (1 - progress)
            curr_y = linear_y - parabolic_offset
        else:
            curr_x = target_x
            curr_y = target_y

        paste_x = int(curr_x - trash_avatar_size[0] / 2)
        paste_y = int(curr_y - trash_avatar_size[1] / 2)

        bg_copy.paste(trash_avatar, (paste_x, paste_y), trash_avatar)
        bg_copy.paste(trash_front, (0, 0), trash_front)
        trash_frames.append(bg_copy)

    return trash_frames


def generate_kick_trash_frames(user_avatar_img, config):
    """
    生成"踢飛頭像進垃圾桶"動畫的所有影格。
    """
    output_size = config.get("output_gif_size", (300, 250))
    avatar_size = config.get("avatar_size", (64, 64))
    avatar_pil = user_avatar_img.resize(avatar_size, get_resampling_filter("LANCZOS"))

    preloaded_frames = _preload_kick_frames(config, output_size)

    kick_frames = _generate_kick_phase_frames(
        config, avatar_pil, preloaded_frames, avatar_size
    )

    trash_frames = _generate_trash_phase_frames(
        config, avatar_pil, avatar_size, output_size
    )

    all_final_frames = kick_frames + trash_frames

    if not all_final_frames:
        raise ValueError("未生成任何動畫影格")

    return all_final_frames
