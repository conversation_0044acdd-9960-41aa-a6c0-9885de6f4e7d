"""
模板處理器

處理各種類型的模板選擇和配置，支援不同技能類型的模板處理。
"""

import os
import random
import sys
from typing import TYPE_CHECKING, Any, Dict, Tuple

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

from .error_handler import error_handler

if TYPE_CHECKING:
    from rpg_system.scripts.generate.rarity_manager import RarityManager


class TemplateProcessor:
    """模板處理基類，處理各種類型的模板選擇和配置"""

    def __init__(
        self,
        templates: Dict[str, Dict[str, Any]],
        raw_templates: Dict[str, Any],
        rarity_manager: "RarityManager",
    ):
        """初始化模板處理器

        Args:
            templates: 分類後的模板字典
            raw_templates: 原始模板數據
            rarity_manager: 稀有度管理器實例
        """
        self.templates = templates
        self.raw_templates = raw_templates
        self.rarity_manager = rarity_manager

    @error_handler(
        default_return=lambda: ("unknown", "BASIC_DAMAGE"), log_message="選擇模板時出錯"
    )
    def select_template(self, category: str, rarity: int) -> Tuple[str, str]:
        """根據分類和稀有度選擇合適的模板

        Args:
            category: 效果分類（保留參數以維持接口兼容性，但不再使用）
            rarity: 稀有度等級

        Returns:
            (模板鍵, 模板名稱) 的元組
        """
        # 直接從稀有度配置中獲取可用模板並隨機選擇
        available_templates = self.rarity_manager.get_effect_templates(rarity)

        if not available_templates:
            raise ValueError(f"沒有找到適合稀有度 {rarity} 的模板")

        # 直接隨機選擇模板，不再進行分類過濾
        template_name = random.choice(available_templates)
        # 生成模板鍵
        template_key = template_name.lower().replace("_", "")
        return template_key, template_name

    @error_handler(default_return=lambda: ({}), log_message="創建效果定義時出錯")
    def create_effect_definition(
        self,
        template_name: str,
        category: str,
        rarity: int,
        variant_multiplier: float = 1.0,
    ) -> Dict[str, Any]:
        """創建效果定義 - 重構版，一次性生成最終公式

        Args:
            template_name: 模板名稱
            category: 效果分類
            rarity: 稀有度等級
            variant_multiplier: 變體倍率（直接應用到基礎值，不再後續處理）

        Returns:
            效果定義字典（包含最終公式，無需後續修改）
        """
        effect_def: Dict[str, Any] = {"effect_template": template_name}

        if category == "damage":
            # 直接生成考慮變體倍率的最終傷害公式
            effect_def["multiplier"] = self._generate_damage_formula(
                rarity, variant_multiplier
            )
            if self.rarity_manager.is_complex_feature_available("crit", rarity):
                effect_def["can_crit"] = True
        elif category == "heal":
            # 直接生成考慮變體倍率的最終治療公式
            effect_def["value"] = self._generate_heal_formula(
                rarity, template_name, variant_multiplier
            )
        elif category in ["buff", "debuff", "control", "shield"]:
            # 直接生成考慮變體倍率的最終持續時間和概率公式
            effect_def["duration_turns"] = self._generate_duration_formula(
                rarity, variant_multiplier
            )
            effect_def["chance"] = self.rarity_manager.get_effect_chance(rarity)
            if self.rarity_manager.is_complex_feature_available("stack", rarity):
                effect_def["stack_count"] = self.rarity_manager.get_stack_count(rarity)

            # 為不同類型的模板生成最終的 value_overrides
            template_data = self.raw_templates.get(template_name, {})
            if template_data.get("effect_type") == "STAT_MODIFICATION":
                effect_def["stat_value_overrides"] = (
                    self._generate_stat_value_overrides(
                        template_data, rarity, variant_multiplier
                    )
                )
            elif template_data.get("effect_type") == "APPLY_STATUS_EFFECT":
                if "value_overrides" in template_data:
                    effect_def["value_overrides"] = (
                        self._generate_apply_value_overrides(
                            template_data, rarity, variant_multiplier
                        )
                    )
        elif category == "special":
            # 為特殊效果添加一些默認參數，考慮變體倍率
            if "SHIELD" in template_name.upper() or "BARRIER" in template_name.upper():
                base_value = (50 + rarity * 25) * variant_multiplier
                level_scaling = 15 * variant_multiplier
                effect_def["value"] = (
                    f"({base_value:.1f} + skill_level * {level_scaling:.1f})"
                )
                effect_def["duration_turns"] = self._generate_duration_formula(
                    rarity, variant_multiplier
                )
            elif "MP" in template_name.upper():
                base_value = (10 + rarity * 5) * variant_multiplier
                level_scaling = 2 * variant_multiplier
                effect_def["value"] = (
                    f"({base_value:.1f} + skill_level * {level_scaling:.1f})"
                )
            else:
                effect_def["duration_turns"] = self._generate_duration_formula(
                    rarity, variant_multiplier
                )
                effect_def["chance"] = self.rarity_manager.get_effect_chance(rarity)

        return effect_def

    # ==================== 統一公式生成方法 ====================

    def _generate_damage_formula(
        self, rarity: int, variant_multiplier: float = 1.0
    ) -> str:
        """生成最終傷害公式，一次性考慮稀有度和變體倍率

        Args:
            rarity: 稀有度等級
            variant_multiplier: 變體倍率

        Returns:
            最終傷害公式字符串
        """
        # 獲取基礎值並直接應用變體倍率
        rarity_power = self.rarity_manager.get_multiplier(rarity, "power")
        random_factor = self.rarity_manager._get_random_factor(rarity)
        level_scale_random = random.uniform(0.8, 1.2)

        # 計算最終基礎值和等級縮放（直接包含變體倍率）
        base_add_factor = 1.0 * random_factor * variant_multiplier
        level_scale_factor = 0.15 * level_scale_random * variant_multiplier

        # 基礎值 = 隨機基礎 + 隨機稀有度加成
        randomized_base = base_add_factor + (
            rarity_power / 100.0 * random.uniform(0.9, 1.1) * variant_multiplier
        )
        randomized_scaling = level_scale_factor

        # 確保基礎值和縮放值不為負或過小
        randomized_base = max(0.1, randomized_base)
        randomized_scaling = max(0.01, randomized_scaling)

        return f"({randomized_base:.2f} + skill_level * {randomized_scaling:.2f})"

    def _generate_heal_formula(
        self, rarity: int, heal_type: str, variant_multiplier: float = 1.0
    ) -> str:
        """生成最終治療公式，一次性考慮稀有度和變體倍率

        Args:
            rarity: 稀有度等級
            heal_type: 治療類型
            variant_multiplier: 變體倍率

        Returns:
            最終治療公式字符串
        """
        rarity_power = self.rarity_manager.get_multiplier(rarity, "power")
        random_factor = self.rarity_manager._get_random_factor(rarity)
        level_scale_random = random.uniform(0.8, 1.2)

        if "PERCENT_CASTER_MATK" in heal_type.upper():
            base_heal = 0.8 * random_factor * variant_multiplier
            rarity_add = (
                (rarity_power / 150.0) * random.uniform(0.9, 1.1) * variant_multiplier
            )
            level_scale = 0.1 * level_scale_random * variant_multiplier
            final_base = max(0.1, base_heal + rarity_add)
            final_scale = max(0.01, level_scale)
            return f"({final_base:.2f} + skill_level * {final_scale:.2f})"
        elif "PERCENT_MAX_HP" in heal_type.upper():
            base_heal = 0.1 * random_factor * variant_multiplier
            rarity_add = (
                (rarity_power / 150.0 * 0.2)
                * random.uniform(0.9, 1.1)
                * variant_multiplier
            )
            level_scale = 0.02 * level_scale_random * variant_multiplier
            final_base = max(0.01, base_heal + rarity_add)
            final_scale = max(0.001, level_scale)
            return f"({final_base:.2f} + skill_level * {final_scale:.2f})"
        elif "FLAT" in heal_type.upper():
            base_heal = 50 * random_factor * variant_multiplier
            rarity_add = (rarity * 20) * random.uniform(0.9, 1.1) * variant_multiplier
            level_scale = 10 * level_scale_random * variant_multiplier
            final_base = max(10, base_heal + rarity_add)
            final_scale = max(1, level_scale)
            return f"({int(final_base)} + skill_level * {int(final_scale)})"

        # 默認回退治療類型
        base_heal = 1.0 * random_factor * variant_multiplier
        level_scale = 0.1 * level_scale_random * variant_multiplier
        final_base = max(0.1, base_heal)
        final_scale = max(0.01, level_scale)
        return f"({final_base:.2f} + skill_level * {final_scale:.2f})"

    def _generate_duration_formula(
        self, rarity: int, variant_multiplier: float = 1.0
    ) -> str:
        """生成最終持續時間公式，一次性考慮稀有度和變體倍率

        Args:
            rarity: 稀有度等級
            variant_multiplier: 變體倍率

        Returns:
            最終持續時間公式字符串
        """
        random_factor = self.rarity_manager._get_random_factor(rarity)
        # 基礎持續時間計算加入隨機性和變體倍率
        base_duration_calc = (2 + rarity // 2) * random_factor * variant_multiplier
        # 確保基礎持續時間至少為1
        base_duration = max(1, int(round(base_duration_calc)))
        # 等級加成部分使用固定公式確保一致性
        level_divisor = 3  # 固定使用3，確保描述和效果定義一致
        return f"({base_duration} + floor(skill_level / {level_divisor}))"

    def _generate_stat_value_overrides(
        self,
        template_data: Dict[str, Any],
        rarity: int,
        variant_multiplier: float = 1.0,
    ) -> Dict[str, str]:
        """為 STAT_MODIFICATION 模板生成動態數值覆蓋 - 重構版，一次性考慮變體倍率

        Args:
            template_data: 模板數據
            rarity: 稀有度等級
            variant_multiplier: 變體倍率

        Returns:
            屬性數值覆蓋字典（最終公式，無需後續修改）
        """
        overrides = {}
        modifications = template_data.get("modifications", [])

        for mod in modifications:
            stat_name = mod.get("stat_name")
            if not stat_name:
                continue

            # 根據屬性類型、稀有度和變體倍率生成最終公式
            base_value = (
                self._get_base_value_for_stat(stat_name, rarity) * variant_multiplier
            )
            level_scaling = (
                self._get_level_scaling_for_stat(stat_name, rarity) * variant_multiplier
            )

            # 生成最終公式字符串
            formula = f"({base_value:.1f} + skill_level * {level_scaling:.2f}) / 100"
            overrides[stat_name] = formula

        return overrides

    def _generate_apply_value_overrides(
        self,
        template_data: Dict[str, Any],
        rarity: int,
        variant_multiplier: float = 1.0,
    ) -> Dict[str, str]:
        """為 APPLY_STATUS_EFFECT 模板生成動態數值覆蓋 - 重構版，一次性考慮變體倍率

        Args:
            template_data: 模板數據
            rarity: 稀有度等級
            variant_multiplier: 變體倍率

        Returns:
            數值覆蓋字典（最終公式，無需後續修改）
        """
        overrides = {}
        base_overrides = template_data.get("value_overrides", {})

        for stat_name, base_formula in base_overrides.items():
            # 根據稀有度和變體倍率調整基礎數值和成長率
            base_value = (
                self._get_base_value_for_stat(stat_name, rarity) * variant_multiplier
            )
            level_scaling = (
                self._get_level_scaling_for_stat(stat_name, rarity) * variant_multiplier
            )

            # 生成最終動態公式，考慮原始公式的符號
            if base_formula.startswith("-"):
                # 負面效果（debuff）
                formula = (
                    f"-({base_value:.1f} + skill_level * {level_scaling:.2f}) / 100"
                )
            else:
                # 正面效果（buff）
                formula = (
                    f"({base_value:.1f} + skill_level * {level_scaling:.2f}) / 100"
                )

            overrides[stat_name] = formula

        return overrides

    def _get_base_value_for_stat(self, stat_name: str, rarity: int) -> int:
        """獲取屬性的基礎數值

        Args:
            stat_name: 屬性名稱
            rarity: 稀有度等級

        Returns:
            基礎數值（百分比形式）
        """
        # 基礎數值映射
        base_values = {
            "patk": 8 + rarity * 2,
            "matk": 8 + rarity * 2,
            "pdef": 10 + rarity * 2,
            "mdef": 10 + rarity * 2,
            "max_hp": 15 + rarity * 3,
            "max_mp": 12 + rarity * 2,
            "spd": 6 + rarity * 2,
            "crit_rate": 5 + rarity * 1,
            "crit_dmg_multiplier": 10 + rarity * 3,
            "accuracy": 8 + rarity * 2,
            "evasion": 8 + rarity * 2,
            "mp_regen_per_turn": 20 + rarity * 5,
        }

        return base_values.get(stat_name, 10 + rarity * 2)

    def _get_level_scaling_for_stat(self, stat_name: str, rarity: int) -> float:
        """獲取屬性的等級縮放係數

        Args:
            stat_name: 屬性名稱
            rarity: 稀有度等級

        Returns:
            等級縮放係數
        """
        # 等級縮放係數映射
        scaling_values = {
            "patk": 1.5 + rarity * 0.3,
            "matk": 1.5 + rarity * 0.3,
            "pdef": 1.8 + rarity * 0.3,
            "mdef": 1.8 + rarity * 0.3,
            "max_hp": 2.5 + rarity * 0.5,
            "max_mp": 2.0 + rarity * 0.4,
            "spd": 1.2 + rarity * 0.2,
            "crit_rate": 0.8 + rarity * 0.2,
            "crit_dmg_multiplier": 2.0 + rarity * 0.4,
            "accuracy": 1.5 + rarity * 0.3,
            "evasion": 1.5 + rarity * 0.3,
            "mp_regen_per_turn": 3.0 + rarity * 0.6,
        }

        return scaling_values.get(stat_name, 1.5 + rarity * 0.3)
