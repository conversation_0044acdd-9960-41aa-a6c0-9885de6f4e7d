/* cards.css - 卡片相關樣式 */

/* 卡片基本樣式 - .card-image 已由 Tailwind classes 在 HTML 中實現 */

/* 主卡片容器 */
.main-card-container {
  width: var(--main-card-width);
  height: var(--main-card-height);
  position: relative;
  overflow: visible;
  border-radius: var(--radius-lg);
  margin-top: calc(var(--spacing-md) * -1.5); /* 微調垂直位置 */
}

/* 副卡片容器 */
.sub-card-container {
  width: var(--sub-card-width);
  height: var(--sub-card-height);
  position: relative;
  overflow: visible;
  margin: var(--spacing-sm);
  transform: rotate(-2deg); /* 保持向左輕微傾斜效果 */
  /* 移除了陰影效果 */
}

/* 副卡片圖片移除圓角效果 */
.sub-card-container img {
  border-radius: 0; /* 移除圓角 */
}

/* 副卡片名稱樣式 - 美化版本 */
.sub-card-name {
  position: absolute;
  bottom: -30px; /* 调整位置，稍微下移一点 */
  left: 50%;
  transform: translateX(-50%) rotate(-2deg); /* 與卡片傾斜一致 */
  width: 85%; /* 进一步缩小宽度避免重叠 */
  max-width: calc(var(--sub-card-width) - 10px); /* 确保不会超过卡片宽度 */
  text-align: center;
  font-size: var(--font-xs); /* 增大字體尺寸，從xxs改為xs */
  font-weight: 600; /* 增加字體粗細 */
  color: var(--color-text-light);
  background: linear-gradient(180deg, rgba(15, 15, 20, 0.85), rgba(20, 20, 25, 0.95)); /* 漸變背景 */
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  padding: 6px 10px; /* 增加內邊距 */
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.12); /* 略微明顯的邊框 */
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5); /* 陰影效果 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  z-index: 5;
  letter-spacing: 0.5px; /* 字間距 */
}

/* 移除副卡片底部裝飾線 */
.sub-card-container::after {
  display: none;
}

/* 副卡片區域 - .sub-cards-area 已由 Tailwind classes 在 HTML 中實現 */

/* 卡片星級 - 純星星顯示，無背景 */
.card-stars {
  color: var(--color-achievements);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8), 0 0 3px currentColor;
  padding: 4px 0px;
  background-color: transparent; /* 移除背景色 */
  font-size: var(--font-xs);
  display: inline-block;
  position: absolute;
  bottom: 12px;
  left: 12px;
  border: none; /* 移除邊框 */
  z-index: 10;
  letter-spacing: 1px;
  opacity: 1; /* 完全不透明 */
  box-shadow: none; /* 移除陰影 */
  min-width: calc(var(--sub-card-width) * 0.65);
  text-align: left;
}

/* 星星樣式 */
.star-normal {
  color: #FFD700; /* 普通星星為淡金色 */
  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 1));
  margin: 0 0px;
  text-shadow: 0 0 6px rgba(255, 215, 0, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-special {
  color: #FFA500; /* 特殊星星為橙色 */
  filter: drop-shadow(0 0 6px rgba(255, 165, 0, 1));
  margin: 0 0px;
  text-shadow: 0 0 7px rgba(255, 165, 0, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-rare {
  color: #FF4500; /* 稀有星星為紅橙色 */
  filter: drop-shadow(0 0 6px rgba(255, 69, 0, 1));
  margin: 0 0px;
  text-shadow: 0 0 7px rgba(255, 69, 0, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-epic {
  color: #FF00FF; /* 史詩星星為品紅色 */
  filter: drop-shadow(0 0 6px rgba(255, 0, 255, 1));
  margin: 0 0px;
  text-shadow: 0 0 7px rgba(255, 0, 255, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-legendary {
  color: #9400D3; /* 傳說星星為紫色 */
  filter: drop-shadow(0 0 6px rgba(148, 0, 211, 1));
  margin: 0 0px;
  text-shadow: 0 0 7px rgba(148, 0, 211, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-mythic {
  color: #00BFFF; /* 神話星星為藍色 */
  filter: drop-shadow(0 0 6px rgba(0, 191, 255, 1));
  margin: 0 0px;
  text-shadow: 0 0 7px rgba(0, 191, 255, 0.9), 0 0 10px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}
.star-ultimate {
  color: #32CD32; /* 終極星星為綠色 */
  filter: drop-shadow(0 0 7px rgba(50, 205, 50, 1));
  margin: 0 1px;
  text-shadow: 0 0 8px rgba(50, 205, 50, 1), 0 0 12px rgba(0, 0, 0, 0.8);
  font-size: 1.1em;
}

/* 主卡片信息面板 */
.main-card-info-panel { /* Applied alongside .info-panel, so only define overrides or specifics */
  /* border-radius, border, and color are inherited from .info-panel */
  max-width: calc(var(--main-card-width) - 10px); /* Specific to main-card-info-panel */
  background: rgba(15, 15, 20, 0.6); /* Override .info-panel */
  backdrop-filter: blur(10px) saturate(150%); /* Override .info-panel */
  -webkit-backdrop-filter: blur(10px) saturate(150%); /* Override .info-panel */
  padding: var(--spacing-md); /* Specific to main-card-info-panel */
  margin-top: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.main-card-info-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  opacity: 0.6;
}

/* #main-card-stars 已由 Tailwind classes 在 HTML 中實現 */

#main-card-name {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  /* font-weight, margin, font-size, color, letter-spacing are now primarily
     expected to be handled by Tailwind utility classes on the HTML element
     or by the styles of the .card-name-container. */
}

#main-card-series {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px; /* Specific letter-spacing not covered by current Tailwind classes on the element */
  opacity: 0.9;         /* Specific opacity not covered by current Tailwind classes on the element */
  /* color, font-size, font-style are now primarily expected to be handled
     by Tailwind utility classes on the HTML element. */
}

/* Styles moved from profile_template.html's internal <style> block */
/* 主卡片信息样式 */
.card-info-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
    position: relative;
}

.star-rating-container {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    position: relative;
    max-width: fit-content;
}

.star-rating-container::before,
.star-rating-container::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30px;
    height: 1px;
}

.star-rating-container::before {
    left: -35px;
    background: linear-gradient(90deg, transparent, rgba(50, 205, 50, 0.7));
}

.star-rating-container::after {
    right: -35px;
    background: linear-gradient(90deg, rgba(50, 205, 50, 0.7), transparent);
}

.card-name-container {
    position: relative;
    margin-top: 3px;
    padding: 0 15px;
}

.card-name-container::before,
.card-name-container::after {
    content: '✦';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
}

.card-name-container::before {
    left: -5px;
}

.card-name-container::after {
    right: -5px;
}

.card-series-container {
    position: relative;
    margin-top: 2px;
    display: inline-block;
}

.card-series-container::before,
.card-series-container::after {
    content: none; /* 移除Vocaloid旁边的装饰线 */
} 