"""
Pioneer System 採集處理器
處理採集類動作（伐木、採礦等）
"""

from typing import Any, Dict

from pioneer.exceptions import (
    PioneerActionError,
    PioneerNotFoundError,
    PioneerValidationError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

from .base_processor import BaseProcessor


class GatherProcessor(BaseProcessor):
    """採集動作處理器"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """執行採集動作

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 動作參數（可能包含採集類型等）

        Returns:
            ActionResult: 執行結果
        """
        try:
            if not action_config.requirements:
                raise PioneerValidationError(
                    f"動作 '{action_config.name}' 的設定不完整：缺少 'requirements'。"
                )

            skill_id = next(
                (
                    req.get("skill")
                    for req in action_config.requirements
                    if req.get("type") == "skill_level" and "skill" in req
                ),
                None,
            )

            if not skill_id:
                raise PioneerValidationError(
                    f"在動作 '{action_config.name}' 的設定中找不到對應的技能要求。"
                )

            skill = await self.repository.get_user_skill(user_id, skill_id)
            if not skill:
                raise PioneerNotFoundError(
                    f"找不到玩家 {user_id} 的技能資料：{skill_id}。"
                )

            drop_results = await self._get_drop_table_results(
                user_id, skill_id, action_config.outputs
            )

            if not drop_results:
                return ActionResult.success_result(message="本次採集沒有任何收穫。")

            total_xp = 0
            rewards = []
            for result in drop_results:
                item_id = result["item_id"]
                quantity = result["quantity"]
                xp = result["xp"]
                await self.repository.add_warehouse_item(user_id, item_id, quantity)
                rewards.append(
                    {"type": "item", "item_id": item_id, "quantity": quantity}
                )
                total_xp += xp

            xp_gained = {}
            message = "採集成功！"
            if total_xp > 0:
                final_xp = await self._calculate_xp_gain(total_xp, user_id, skill_id)
                level_before = skill.level
                await self.repository.add_skill_xp(user_id, skill_id, final_xp)
                skill_after = await self.repository.get_user_skill(user_id, skill_id)
                if not skill_after:
                    raise PioneerNotFoundError(
                        f"在更新經驗後，找不到玩家 {user_id} 的技能資料：{skill_id}。"
                    )
                level_after = skill_after.level

                skill_name = self.game_data.get_skill_name(skill_id)
                if not skill_name:
                    logger.warning("找不到技能ID '%s' 的名稱。", skill_id)
                    skill_name = skill_id

                xp_gained = {skill_id: final_xp}
                if level_after > level_before:
                    message = (
                        f"採集成功！您的「{skill_name}」技能已提升至 {level_after} 級！"
                    )
                else:
                    message = f"採集成功！獲得 {final_xp} 點「{skill_name}」經驗。"

            from pioneer.services.task_updater import task_updater

            await task_updater.check_and_update_tasks(
                user_id, "gather", action=action_config.name, quantity=1
            )

            return ActionResult.success_result(
                message=message, rewards=rewards, xp_gained=xp_gained
            )
        except (PioneerValidationError, PioneerNotFoundError) as e:
            raise e
        except Exception as e:
            logger.error("採集處理器執行失敗: %s", e, exc_info=True)
            raise PioneerActionError(
                f"執行採集動作 '{action_config.name}' 時發生未預期錯誤。"
            ) from e
