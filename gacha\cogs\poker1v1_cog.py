"""
德州撲克1v1機器人 COG - V2.3 (優化版)
處理德州撲克1v1遊戲的Discord命令和交互
包含全面的競態條件防護和錯誤處理
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import List, Optional

import discord
from discord import app_commands
from discord.ext import commands, tasks

import gacha.services.economy_service as economy_service
from gacha.cogs.poker.logic import (
    can_player_act,
)

# 導入撲克模組
from gacha.cogs.poker.models import (
    HIDDEN_CARD_EMOJI,
    STAKE_CONFIGS,
    GameStage,
    MatchmakingEntry,
    PokerCard,
    PokerDeck,
    PokerGameState,
    PokerPlayer,
    StakeTier,
)
from gacha.cogs.poker.processor import GameProcessor
from gacha.cogs.poker.views import MatchmakingView, PokerGameView, StakeTierSelectView
from gacha.exceptions import (
    CommandBlacklistError,
    GameError,
    InsufficientBalanceError,
    InvalidBetAmountError,
    InvalidOperationError,
    TradeValidationError,
)
from gacha.services import game_stats_service
from gacha.services.activity_service import is_user_in_trading_blacklist
from utils.logger import logger

from .decorators import account_age_check
from .poker import game_manager

# 場次UI配置
TIER_UI_CONFIG = {
    StakeTier.NEWBIE: {
        "name": "🟢 新手場",
        "emoji": "🟢",
        "color": discord.Color.green(),
    },
    StakeTier.REGULAR: {
        "name": "🟡 普通場",
        "emoji": "🟡",
        "color": discord.Color.blue(),
    },
    StakeTier.PRO: {"name": "🔴 高手場", "emoji": "🔴", "color": discord.Color.red()},
}


class Poker1v1Cog(commands.Cog):
    """德州撲克1v1 COG - 優化版"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # Game state is now managed by the game_manager module.
        # The cleanup_expired_matchmaking_task is a @tasks.loop
        # and is started automatically by the extension loader.

    async def _is_player_in_operation(self, user_id: int) -> bool:
        """檢查玩家是否正在操作中"""
        async with game_manager.operation_lock:
            return user_id in game_manager.players_in_operation

    async def _mark_player_in_operation(self, user_id: int):
        """標記玩家為操作中"""
        async with game_manager.operation_lock:
            game_manager.players_in_operation.add(user_id)

    async def _unmark_player_in_operation(self, user_id: int):
        """取消玩家操作中標記"""
        async with game_manager.operation_lock:
            game_manager.players_in_operation.discard(user_id)

    # ==================== 處理器管理方法 ====================

    async def _get_game_processor(self, game_id: str) -> Optional[GameProcessor]:
        """獲取遊戲處理器"""
        async with game_manager.processors_lock:
            return game_manager.active_processors.get(game_id)

    async def _create_game_processor(self, game_state) -> "GameProcessor":
        """Creates and starts a game processor."""
        processor = GameProcessor(game_state, self)

        async with game_manager.processors_lock:
            game_manager.active_processors[game_state.game_id] = processor

        processor.start()
        logger.info("遊戲處理器 %s 已創建並啟動", game_state.game_id)
        return processor

    async def _cleanup_game_processor(self, game_id: str):
        """清理遊戲處理器"""
        async with game_manager.processors_lock:
            processor = game_manager.active_processors.pop(game_id, None)

        if processor:
            await processor.stop()
            logger.info("遊戲處理器 %s 已清理", game_id)

    async def render_game_views(self, game_state: PokerGameState):
        """渲染服務：更新遊戲UI（供處理器調用）"""
        # 檢查是否有待記錄的手牌統計
        if (
            hasattr(game_state, "_hand_stats_pending")
            and game_state._hand_stats_pending
        ):
            await self._record_hand_stats(game_state)

        await self._render_game_ui_only(game_state)

    async def _render_game_ui_only(self, game_state: PokerGameState):
        """純UI渲染邏輯（不包含狀態變化處理）"""
        # 檢查遊戲是否仍然有效
        if (
            not game_manager.matchmaking_queue
            or game_state.game_id not in game_manager.matchmaking_queue.active_games
        ):
            logger.warning("嘗試更新已清理的遊戲: %s", game_state.game_id)
            return

        # 並行更新雙方玩家的消息
        update_tasks = []

        if game_state.player1_message:
            update_tasks.append(
                self._safe_update_player_message(game_state, 0, "玩家1")
            )

        if game_state.player2_message:
            update_tasks.append(
                self._safe_update_player_message(game_state, 1, "玩家2")
            )

        # 等待所有更新完成
        if update_tasks:
            results = await asyncio.gather(*update_tasks, return_exceptions=True)
            # 檢查是否有更新失敗
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error("更新玩家 %s 消息失敗: %s", i + 1, result)
                elif result is False:
                    # 🔧 修復：明確記錄UI更新失敗的情況
                    logger.error("玩家 %s UI更新失敗，可能導致按鈕狀態不同步", i + 1)

        # 如果遊戲結束，觸發業務結算
        if game_state.game_over:
            logger.info("遊戲 %s 已結束，觸發業務結算", game_state.game_id)
            asyncio.create_task(self._handle_game_completion(game_state))

    @tasks.loop(minutes=10)  # 改為10分鐘，避免與View超時衝突
    async def cleanup_expired_matchmaking_task(self):
        """定期清理過期的匹配條目和殘留處理器"""
        # 清理超過5分鐘的條目（給View超時留出足夠時間）
        if game_manager.matchmaking_queue:
            await game_manager.matchmaking_queue.cleanup_expired_entries_atomic(
                timeout_seconds=300
            )
            logger.debug("清理過期匹配條目完成")

        # 🔧 修復：清理殘留的處理器
        await self._cleanup_orphaned_processors()

    async def _cleanup_orphaned_processors(self):
        """清理孤立的處理器（沒有對應活躍遊戲的處理器）"""
        async with game_manager.processors_lock:
            active_processors = dict(game_manager.active_processors)
            active_games = (
                set(game_manager.matchmaking_queue.active_games.keys())
                if game_manager.matchmaking_queue
                else set()
            )

            orphaned_processors = []
            for game_id, processor in active_processors.items():
                # 檢查處理器是否對應活躍遊戲
                if game_id not in active_games:
                    orphaned_processors.append(game_id)
                # 檢查處理器是否已經停止運行
                elif hasattr(processor, "is_running") and not processor.is_running:
                    orphaned_processors.append(game_id)
                # 檢查遊戲是否已經結束
                elif (
                    hasattr(processor, "game_state") and processor.game_state.game_over
                ):
                    orphaned_processors.append(game_id)

            if orphaned_processors:
                logger.info(
                    "發現 %s 個孤立處理器需要清理: %s",
                    len(orphaned_processors),
                    orphaned_processors,
                )

                for game_id in orphaned_processors:
                    processor = game_manager.active_processors.pop(game_id, None)
                    if processor and hasattr(processor, "stop"):
                        await processor.stop()
                    logger.info("已清理孤立處理器: %s", game_id)

    async def cog_load(self):
        """Initializes the matchmaking queue if it doesn't exist."""
        # The matchmaking queue is now a global state in game_manager.
        # We ensure it's initialized when the cog loads.
        if not game_manager.matchmaking_queue:
            game_manager.initialize_matchmaking_queue()
        logger.info("Poker1v1Cog loaded. Matchmaking queue initialized.")

    async def cog_unload(self):
        """Cancels tasks and cleans up persistent views upon cog unload."""
        # 🔧 修復：停止定時任務
        self.cleanup_expired_matchmaking_task.cancel()

        # 🔧 修復：清理所有活躍的遊戲處理器，防止殘留處理器繼續運行
        if (
            game_manager.matchmaking_queue
            and game_manager.matchmaking_queue.active_games
        ):
            games_to_cleanup = list(game_manager.matchmaking_queue.active_games.keys())
            logger.info(
                "Poker1v1Cog 卸載：發現 %s 個活躍遊戲需要清理",
                len(games_to_cleanup),
            )

            for game_id in games_to_cleanup:
                await self._force_cleanup_game(game_id)

            logger.info("Poker1v1Cog 卸載：已清理所有活躍遊戲")

        # 🔧 修復：清理所有活躍的處理器
        async with game_manager.processors_lock:
            processors_to_cleanup = list(game_manager.active_processors.keys())
            if processors_to_cleanup:
                logger.info(
                    "Poker1v1Cog 卸載：發現 %s 個活躍處理器需要清理",
                    len(processors_to_cleanup),
                )

                for game_id in processors_to_cleanup:
                    processor = game_manager.active_processors.pop(game_id, None)
                    if processor:
                        await processor.stop()
                        logger.info("已停止處理器: %s", game_id)

                logger.info("Poker1v1Cog 卸載：已清理所有活躍處理器")

        # 清理持久化視圖
        if hasattr(self.bot, "persistent_views") and hasattr(
            self.bot.persistent_views, "clear"
        ):
            # Type check to ensure we can call clear()
            persistent_views = getattr(self.bot, "persistent_views", None)
            if persistent_views and hasattr(persistent_views, "clear"):
                persistent_views.clear()
        logger.info(
            "Poker1v1Cog unloaded. Cleanup task cancelled, all games and processors cleaned up, and persistent views cleared."
        )

        # 關鍵修復：手動重置 game_manager 狀態，確保熱重載時能重新初始化
        game_manager.matchmaking_queue = None
        logger.info("Poker game_manager state has been reset for hot reload.")

    async def _force_cleanup_game(self, game_id: str):
        """強制清理遊戲（COG卸載時使用）"""
        # 清理處理器
        await self._cleanup_game_processor(game_id)

        if (
            game_manager.matchmaking_queue
            and game_id in game_manager.matchmaking_queue.active_games
        ):
            game_state = game_manager.matchmaking_queue.active_games[game_id]
            # 退還剩餘籌碼
            if hasattr(game_state, "player1") and game_state.player1.chips > 0:
                await economy_service.award_oil(
                    user_id=game_state.player1.user_id,
                    amount=game_state.player1.chips,
                    transaction_type="game:poker1v1_refund",
                    reason="Poker 1v1 force cleanup refund",
                )
            if hasattr(game_state, "player2") and game_state.player2.chips > 0:
                await economy_service.award_oil(
                    user_id=game_state.player2.user_id,
                    amount=game_state.player2.chips,
                    transaction_type="game:poker1v1_refund",
                    reason="Poker 1v1 force cleanup refund",
                )
            if game_manager.matchmaking_queue:
                del game_manager.matchmaking_queue.active_games[game_id]
            logger.info("強制清理遊戲: %s", game_id)

    @app_commands.command(name="poker1v1", description="開始1v1德州撲克遊戲")
    @account_age_check()
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(tier="場次等級 (可選)", buyin="帶入金額 (可選)")
    @app_commands.choices(
        tier=[
            app_commands.Choice(name="新手場 (10/20) 帶入1K-5K", value="newbie"),
            app_commands.Choice(name="普通場 (100/200) 帶入10K-50K", value="regular"),
            app_commands.Choice(name="高手場 (1000/2000) 帶入100K-500K", value="pro"),
        ]
    )
    async def poker1v1_command(
        self,
        interaction: discord.Interaction,
        tier: Optional[str] = None,
        buyin: Optional[int] = None,
    ):
        """處理德州撲克1v1命令"""
        await interaction.response.defer(thinking=True)

        # 檢查發起者是否在交易黑名單中
        if await is_user_in_trading_blacklist(interaction.user.id):
            raise CommandBlacklistError("您已被限制使用 poker1v1 指令。")

        # 檢查是否正在操作中（防止重複命令）
        if await self._is_player_in_operation(interaction.user.id):
            raise InvalidOperationError("您已經在操作中，請等待當前操作完成！")

        # 原子性檢查玩家狀態
        if await self._is_player_in_game(interaction.user.id):
            raise InvalidOperationError("您已經在遊戲中！")

        if await self._is_player_in_queue(interaction.user.id):
            raise InvalidOperationError("您已經在匹配隊列中！")

        if tier and buyin:
            # --- 核心變化：快速匹配模式，直接調用統一入口 ---
            try:
                stake_tier = StakeTier(tier)
            except ValueError as e:
                raise InvalidBetAmountError("無效的場次等級！") from e

            # 驗證帶入金額範圍
            config = STAKE_CONFIGS[stake_tier]
            min_buyin = int(config["min_buyin"])
            max_buyin = int(config["max_buyin"])
            if buyin is None or not (min_buyin <= buyin <= max_buyin):
                raise InvalidBetAmountError(
                    f"帶入金額必須在 {config['min_buyin']:,} ~ {config['max_buyin']:,} 之間！"
                )

            # 參數驗證通過後才標記為操作中
            await self._mark_player_in_operation(interaction.user.id)
            await self._join_matchmaking(
                interaction, stake_tier, buyin, use_followup=True
            )
        else:
            # 選單模式 - 不提前標記操作中，讓用戶可以自由查看場次
            await self._show_stake_selection(interaction)

    def _create_tier_field(self, tier, stats: dict) -> dict:
        """統一創建場次信息字段"""
        config = STAKE_CONFIGS[tier]
        tier_stats = stats.get(tier.value, {"waiting": 0, "playing": 0})

        # 動態創建 UI 配置，避免使用頂部導入的舊實例
        tier_ui_configs = {
            StakeTier.NEWBIE: {
                "name": "🟢 新手場",
                "emoji": "🟢",
                "color": discord.Color.green(),
            },
            StakeTier.REGULAR: {
                "name": "🟡 普通場",
                "emoji": "🟡",
                "color": discord.Color.blue(),
            },
            StakeTier.PRO: {
                "name": "🔴 高手場",
                "emoji": "🔴",
                "color": discord.Color.red(),
            },
        }
        ui_config = tier_ui_configs[tier]

        return {
            "name": ui_config["name"],
            "value": f"💰 **帶入**\n"
            f"<:Reply:1357534074830590143> {config['min_buyin']:,} ~ {config['max_buyin']:,}\n"
            f"👥 **等待：** {tier_stats['waiting']:,}\n"
            f"🎮 **對局：** {tier_stats['playing']:,}",
            "inline": True,
        }

    async def _show_stake_selection(self, interaction: discord.Interaction):
        """顯示場次選擇界面"""
        embed = discord.Embed(
            title="🎰 德州撲克 1v1 對戰",
            description="🎯 **歡迎來到德州撲克競技場！**\n\n"
            "💰 選擇您的場次，與其他玩家一決高下\n"
            "🏆 展現您的撲克技巧，贏取豐厚獎勵",
            color=discord.Color.gold(),
        )

        # 添加場次信息
        stats = {}
        if game_manager.matchmaking_queue:
            stats = game_manager.matchmaking_queue.get_queue_stats()
        # 🔧 修復：使用重新導入的模組來獲取新的枚舉實例
        for tier in StakeTier:
            field = self._create_tier_field(tier, stats)
            embed.add_field(**field)

        embed.add_field(
            name="📋 遊戲規則",
            value="• 德州撲克標準規則\n• 1v1 對戰模式\n• 籌碼不足時自動結束\n• 抽水率：5%（翻牌後）",
            inline=False,
        )

        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1381275927589163121/1385320626293575832/casino-chip.png?ex=6855a3df&is=6854525f&hm=cf13c5400d1351ec7b7dfe137b5cbe41e232f95eb889b2b7e8f46af7f368c360&"
        )
        embed.set_footer(
            text="🎲 選擇場次後需要輸入帶入金額 | 建議在機器人加入的伺服器玩，不然很容易遇到權限錯誤"
        )

        # 🔧 修復：使用重新導入的模組來獲取類
        view = StakeTierSelectView(self, interaction.user.id)
        await interaction.followup.send(embed=embed, view=view)

    async def _join_matchmaking(
        self,
        interaction: discord.Interaction,
        stake_tier: StakeTier,
        buyin_amount: int,
        use_followup: bool = False,
        original_message=None,
    ):
        """統一的匹配隊列加入方法 (重構後)"""
        # 核心業務邏輯驗證
        if await self._is_player_in_game(interaction.user.id):
            raise InvalidOperationError("您已經在遊戲中！")
        if await self._is_player_in_queue(interaction.user.id):
            raise InvalidOperationError("您已經在匹配隊列中！")

        try:
            # 1. 餘額檢查 (業務邏輯)
            if not await self._check_balance(interaction, buyin_amount, use_followup):
                # 檢查失敗時，需要取消操作標記
                await self._unmark_player_in_operation(interaction.user.id)
                return

            # 2. 創建匹配條目 (業務邏輯)
            # 🔧 修復：使用重新導入的模組來獲取類
            entry = MatchmakingEntry(
                user_id=interaction.user.id,
                username=interaction.user.display_name,
                stake_tier=stake_tier,
                buyin_amount=buyin_amount,
                timestamp=time.time(),
                interaction=interaction,
            )

            # 3. 原子性匹配操作 (核心業務)
            opponent = None
            if game_manager.matchmaking_queue:
                opponent = await game_manager.matchmaking_queue.add_to_queue_atomic(
                    entry
                )

            # 4. 取消操作標記（因為已經加入隊列或開始遊戲）
            await self._unmark_player_in_operation(interaction.user.id)

            # 5. 根據結果處理UI和後續流程 (業務流程控制)
            if opponent:
                # 匹配成功
                await self._show_waiting_screen(
                    interaction,
                    entry,
                    use_followup=use_followup,
                    original_message=original_message,
                )
                await self._start_game_safe(entry, opponent)
            else:
                # 進入等待隊列
                await self._show_waiting_screen(
                    interaction,
                    entry,
                    use_followup=use_followup,
                    original_message=original_message,
                )

        except Exception as e:
            # 發生錯誤時也要取消操作標記
            await self._unmark_player_in_operation(interaction.user.id)
            logger.error("加入匹配時發生嚴重錯誤: %s", e, exc_info=True)
            # 向上拋出或處理錯誤回覆
            raise e

    async def _check_balance(
        self,
        interaction: discord.Interaction,
        buyin_amount: int,
        use_followup: bool = False,
    ) -> bool:
        """統一的餘額檢查方法"""
        balance_info = await economy_service.get_balance(interaction.user.id)
        if balance_info["balance"] < buyin_amount:
            raise InsufficientBalanceError(
                required=buyin_amount, current=balance_info["balance"]
            )
        return True

    async def _show_waiting_screen(
        self,
        interaction: discord.Interaction,
        entry,
        use_followup: bool = False,
        original_message=None,
    ):
        """統一的等待匹配畫面顯示方法 (重構後)"""
        # 🔧 修復：使用重新導入的模組來獲取配置
        config = STAKE_CONFIGS[entry.stake_tier]

        # 動態創建 UI 配置，避免使用頂部導入的舊實例
        tier_ui_configs = {
            StakeTier.NEWBIE: {
                "name": "🟢 新手場",
                "emoji": "🟢",
                "color": discord.Color.green(),
            },
            StakeTier.REGULAR: {
                "name": "🟡 普通場",
                "emoji": "🟡",
                "color": discord.Color.blue(),
            },
            StakeTier.PRO: {
                "name": "🔴 高手場",
                "emoji": "🔴",
                "color": discord.Color.red(),
            },
        }
        ui_config = tier_ui_configs[entry.stake_tier]

        embed = discord.Embed(
            title="🔍 正在匹配對手...",
            description=(
                "⏳ **正在為您尋找 %s %s 的對手**\n\n"
                "🎯 請稍候，我們正在為您匹配實力相當的對手\n"
                "💫 匹配成功後將立即開始對戰"
            )
            % (ui_config["emoji"], config["name"]),
            color=getattr(ui_config["color"], "value", 0)
            if hasattr(ui_config["color"], "value")
            else (ui_config["color"] if isinstance(ui_config["color"], int) else 0),
        )

        embed.add_field(
            name="🎮 對戰信息",
            value=("🏷️ **場次：** %s %s\n💰 **盲注：** %s/%s\n💵 **帶入：** %s")
            % (
                ui_config["emoji"],
                config["name"],
                f"{config['small_blind']:,}",
                f"{config['big_blind']:,}",
                f"{entry.buyin_amount:,}",
            ),
            inline=True,
        )

        embed.add_field(
            name="⏰ 匹配狀態",
            value="🔄 **狀態：** 搜尋中\n⏱️ **超時：** 3分鐘\n🎲 **模式：** 1v1 對戰",
            inline=True,
        )

        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1381275927589163121/1385320626293575832/casino-chip.png?ex=6855a3df&is=6854525f&hm=cf13c5400d1351ec7b7dfe137b5cbe41e232f95eb889b2b7e8f46af7f368c360&"
        )
        embed.set_footer(text="🎰 匹配將在3分鐘後超時 | 可隨時取消匹配")

        # 🔧 修復：使用重新導入的模組來獲取類
        view = MatchmakingView(entry.stake_tier, self, entry.user_id)

        try:
            # 如果有原始消息（來自場次選擇界面），直接編輯它變成等待界面
            if original_message:
                await original_message.edit(embed=embed, view=view)
                entry.waiting_message = original_message
                logger.info("已編輯原始消息 %s 為等待界面", original_message.id)
            else:
                # 如果沒有原始消息（來自快速匹配命令），則發送新消息
                # 交互已在命令或模態框中被 defer，所以使用 followup
                entry.waiting_message = await interaction.followup.send(
                    embed=embed, view=view
                )
                logger.info("已發送新的等待界面消息")

            # 為 view 設置 message 引用，以便後續操作（如超時）
            if hasattr(view, "message"):
                view.message = entry.waiting_message
            entry.matchmaking_view = view

        except discord.errors.Forbidden as e:
            logger.error(
                "顯示等待畫面失敗 (Forbidden): interaction_id=%s, interaction_expired=%s, original_message_id=%s, error=%s",
                interaction.id,
                interaction.is_expired(),
                original_message.id if original_message else "N/A",
                e,
                exc_info=True,
            )
            # 備用方案：由於權限問題，總是嘗試發送新消息
            try:
                if not getattr(entry, "waiting_message", None):  # 確保不會重複發送
                    entry.waiting_message = await interaction.followup.send(
                        embed=embed, view=view, ephemeral=False
                    )
                    if hasattr(view, "message"):
                        view.message = entry.waiting_message
                    entry.matchmaking_view = view
                    logger.info("Forbidden後，已成功發送新的等待畫面")
            except Exception as backup_e:
                logger.error("備用等待畫面也失敗: %s", backup_e, exc_info=True)
                # 不再向上拋出，避免整個流程中斷
        except Exception as e:
            logger.error("顯示等待畫面發生未知錯誤: %s", e, exc_info=True)
            # 備用方案
            try:
                if not getattr(entry, "waiting_message", None):
                    entry.waiting_message = await interaction.followup.send(
                        embed=embed, view=view, ephemeral=False
                    )
                    view.message = entry.waiting_message
                    entry.matchmaking_view = view
            except Exception as backup_e:
                logger.error("備用等待畫面也失敗: %s", backup_e, exc_info=True)
                raise

    async def _start_game_safe(
        self, player1_entry: MatchmakingEntry, player2_entry: MatchmakingEntry
    ):
        """安全地開始遊戲（新架構：無需鎖保護）"""
        game_id = "poker1v1_%s_%s_%s" % (
            player1_entry.user_id,
            player2_entry.user_id,
            int(time.time()),
        )

        try:
            # 檢查雙方帳號年齡
            four_months_ago = datetime.now(timezone.utc) - timedelta(days=120)

            player1_user = self.bot.get_user(
                player1_entry.user_id
            ) or await self.bot.fetch_user(player1_entry.user_id)
            if player1_user.created_at > four_months_ago:
                raise TradeValidationError(
                    f"玩家 {player1_user.mention} 不符合遊戲資格。"
                )

            player2_user = self.bot.get_user(
                player2_entry.user_id
            ) or await self.bot.fetch_user(player2_entry.user_id)
            if player2_user.created_at > four_months_ago:
                raise TradeValidationError(
                    f"玩家 {player2_user.mention} 不符合遊戲資格。"
                )

            # 再次檢查雙方餘額（防止在匹配期間餘額變化）
            player1_balance = await economy_service.get_balance(player1_entry.user_id)
            player2_balance = await economy_service.get_balance(player2_entry.user_id)

            if player1_balance["balance"] < player1_entry.buyin_amount:
                raise InsufficientBalanceError(
                    message=f"玩家 {player1_entry.username} 餘額不足",
                    required=player1_entry.buyin_amount,
                    current=player1_balance["balance"],
                )

            if player2_balance["balance"] < player2_entry.buyin_amount:
                raise InsufficientBalanceError(
                    message=f"玩家 {player2_entry.username} 餘額不足",
                    required=player2_entry.buyin_amount,
                    current=player2_balance["balance"],
                )

            # 計算有效籌碼
            effective_stack = min(
                player1_entry.buyin_amount, player2_entry.buyin_amount
            )

            # 🔧 修復：只扣除有效籌碼，避免玩家多付錢
            try:
                await economy_service.award_oil(
                    user_id=player1_entry.user_id,
                    amount=-effective_stack,
                    transaction_type="game:poker1v1_buy_in",
                    reason=f"Poker 1v1 buy-in for table {game_id}",
                )

                try:
                    await economy_service.award_oil(
                        user_id=player2_entry.user_id,
                        amount=-effective_stack,
                        transaction_type="game:poker1v1_buy_in",
                        reason=f"Poker 1v1 buy-in for table {game_id}",
                    )
                except Exception as e:
                    # 第二個扣款失敗，回滾第一個
                    await economy_service.award_oil(
                        user_id=player1_entry.user_id,
                        amount=effective_stack,
                        transaction_type="game:poker1v1_buy_in_refund",
                        reason=f"Poker 1v1 buy-in failed for opponent on table {game_id}",
                    )
                    raise e

            except Exception as e:
                logger.error("扣除帶入金額失敗: %s", e, exc_info=True)
                raise GameError("扣除帶入金額時發生錯誤") from e

            # 創建遊戲狀態
            # 🔧 修復：使用重新導入的模組來獲取類
            player1 = PokerPlayer(
                user_id=player1_entry.user_id,
                username=player1_entry.username,
                chips=effective_stack,
            )

            player2 = PokerPlayer(
                user_id=player2_entry.user_id,
                username=player2_entry.username,
                chips=effective_stack,
            )

            game_state = PokerGameState(
                game_id=game_id,
                player1=player1,
                player2=player2,
                deck=PokerDeck(),
                stake_tier=player1_entry.stake_tier,
                effective_stack=effective_stack,
                player1_original_buyin=player1_entry.buyin_amount,
                player2_original_buyin=player2_entry.buyin_amount,
            )

            # 開始第一手牌
            game_state.start_new_hand()

            # 保存到內存
            if game_manager.matchmaking_queue:
                game_manager.matchmaking_queue.active_games[game_id] = game_state

            # 創建並啟動遊戲處理器
            await self._create_game_processor(game_state)

            # 創建並發送遊戲界面給雙方
            await self._create_game_messages(game_state, player1_entry, player2_entry)

            logger.info("遊戲 %s 成功開始，處理器已啟動", game_id)

        except (
            TradeValidationError,
            InvalidOperationError,
            InsufficientBalanceError,
        ) as e:
            logger.warning("因業務規則，遊戲 %s 無法開始: %s", game_id, e)
            await self._notify_game_start_failure(player1_entry, str(e))
            await self._notify_game_start_failure(player2_entry, str(e))
            # 向上拋出，讓調用者知道遊戲創建失敗
            raise GameError(f"開始遊戲失敗: {e}") from e

        except Exception as e:
            logger.error("開始遊戲失敗: %s", e, exc_info=True)
            error_message = "開始遊戲時發生未知錯誤，請聯繫管理員。"
            await self._notify_game_start_failure(player1_entry, error_message)
            await self._notify_game_start_failure(player2_entry, error_message)
            raise GameError(f"開始遊戲失敗: {e}") from e

    async def _notify_game_start_failure(
        self, player_entry: MatchmakingEntry, reason: str
    ):
        """通知玩家遊戲開始失敗"""
        if player_entry.waiting_message:
            embed = discord.Embed(
                title="✅ 遊戲開始失敗", description=reason, color=discord.Color.red()
            )
            embed.set_footer(text="您的匹配已被取消。")
            try:
                # 停止舊的 View
                if (
                    hasattr(player_entry, "matchmaking_view")
                    and player_entry.matchmaking_view
                ):
                    if hasattr(player_entry.matchmaking_view, "stop"):
                        player_entry.matchmaking_view.stop()
                await player_entry.waiting_message.edit(embed=embed, view=None)
            except discord.HTTPException:
                logger.warning("編輯玩家 %s 的等待消息失敗", player_entry.user_id)

    async def _create_game_messages(
        self,
        game_state: PokerGameState,
        player1_entry: MatchmakingEntry,
        player2_entry: MatchmakingEntry,
    ):
        """為雙方創建遊戲消息"""

        # 🔧 修復：停止舊的 MatchmakingView 以防止超時
        await self._stop_old_matchmaking_views(player1_entry, player2_entry)

        # 為玩家1創建遊戲消息
        embed1 = self._create_game_embed(game_state, 0)
        # 🔧 修復：使用重新導入的模組來獲取類
        view1 = PokerGameView(game_state, 0, self)

        # 為玩家2創建遊戲消息
        embed2 = self._create_game_embed(game_state, 1)
        view2 = PokerGameView(game_state, 1, self)

        # 🔧 修復：並行編輯消息，避免時序問題
        edit_tasks = []

        if player1_entry.waiting_message:
            edit_tasks.append(
                self._safe_edit_message(
                    player1_entry.waiting_message,
                    embed1,
                    view1,
                    "玩家1 %s" % player1_entry.user_id,
                    game_state,
                    0,
                )
            )
        else:
            logger.warning("玩家1 %s 沒有等待消息", player1_entry.user_id)

        if player2_entry.waiting_message:
            edit_tasks.append(
                self._safe_edit_message(
                    player2_entry.waiting_message,
                    embed2,
                    view2,
                    "玩家2 %s" % player2_entry.user_id,
                    game_state,
                    1,
                )
            )
        else:
            logger.warning("玩家2 %s 沒有等待消息", player2_entry.user_id)

        # 並行執行編輯操作
        if edit_tasks:
            results = await asyncio.gather(*edit_tasks, return_exceptions=True)

            # 檢查結果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error("玩家%s 消息編輯異常: %s", i + 1, result)
                elif not result:
                    logger.warning("玩家%s 消息編輯失敗", i + 1)

        # 處理器已經自動管理超時，不需要手動啟動計時器
        logger.info("遊戲 %s UI創建完成，處理器已接管遊戲流程", game_state.game_id)

    async def _handle_http_exception(
        self, e: discord.HTTPException, player_name: str
    ) -> bool:
        """處理 _safe_edit_message 中的 HTTP 異常，返回是否應該重試"""
        if e.status == 429:
            retry_after = getattr(e, "retry_after", 1.0)
            logger.warning("%s 被限速，等待 %s 秒後重試", player_name, retry_after)
            await asyncio.sleep(retry_after)
            return True  # Should retry

        error_map = {
            401: "Invalid Webhook Token",
            404: "Message Not Found",
            403: "Forbidden",
        }
        if e.status in error_map:
            logger.error(
                "%s 編輯失敗 (HTTP %s - %s): %s",
                player_name,
                e.status,
                error_map[e.status],
                e,
            )
            return False  # Should not retry

        logger.error("%s 編輯失敗 (HTTP %s): %s", player_name, e.status, e)
        return False  # Should not retry for other HTTP errors by default

    async def _safe_edit_message(
        self,
        message,
        embed,
        view,
        player_name: str,
        game_state: Optional[PokerGameState] = None,
        player_index: Optional[int] = None,
        max_retries: int = 3,
    ):
        """通用的安全消息編輯方法，帶重試機制"""
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    await asyncio.sleep(0.3 * attempt)

                await message.edit(embed=embed, view=view)

                if game_state and player_index is not None:
                    if player_index == 0:
                        game_state.player1_message = message
                    else:
                        game_state.player2_message = message
                if view:
                    view.message = message

                logger.info("%s 消息編輯成功", player_name)
                return True

            except discord.NotFound:
                logger.warning("%s 的消息已被刪除", player_name)
                return False

            except discord.HTTPException as e:
                should_retry = await self._handle_http_exception(e, player_name)
                if not should_retry:
                    return False

            except Exception as e:
                logger.error(
                    "%s 編輯失敗 (嘗試 %s/%s): %s",
                    player_name,
                    attempt + 1,
                    max_retries,
                    e,
                )

        logger.error(
            "%s 消息編輯完全失敗，所有 %s 次重試都失敗", player_name, max_retries
        )
        return False

    async def _stop_old_matchmaking_views(
        self, player1_entry: MatchmakingEntry, player2_entry: MatchmakingEntry
    ):
        """停止舊的 MatchmakingView 以防止超時"""
        try:
            # 嘗試從 entry 中獲取並停止舊的 View
            for entry in [player1_entry, player2_entry]:
                if hasattr(entry, "matchmaking_view") and entry.matchmaking_view:
                    try:
                        old_view = entry.matchmaking_view
                        if hasattr(old_view, "stop"):
                            old_view.stop()
                            logger.info(
                                "已停止玩家 %s 的舊 MatchmakingView", entry.user_id
                            )
                        # 清理引用
                        entry.matchmaking_view = None
                    except Exception as e:
                        logger.warning(
                            "停止玩家 %s 的舊 View 失敗: %s", entry.user_id, e
                        )
        except Exception as e:
            logger.error("停止舊 MatchmakingView 時發生錯誤: %s", e, exc_info=True)

    def _create_game_embed(self, game_state, player_index: int) -> discord.Embed:
        """創建遊戲Embed"""
        player = game_state.player1 if player_index == 0 else game_state.player2
        opponent = game_state.player2 if player_index == 0 else game_state.player1
        # 🔧 修復：使用重新導入的模組來獲取配置
        config = STAKE_CONFIGS[game_state.stake_tier]

        # 檢查是否一手牌剛結束（需要顯示倒數）
        hand_just_ended = game_state.winner_id is not None and not game_state.game_over

        # 設置標題和顏色
        title, color = self._get_embed_title_and_color(
            game_state, player_index, hand_just_ended
        )
        embed = discord.Embed(title=title, color=color)

        # 構建描述
        if game_state.game_over:
            description = self._build_game_over_description(
                game_state, player, opponent
            )
        elif hand_just_ended:
            description = self._build_hand_end_description(game_state, player, opponent)
        else:
            description = self._build_game_progress_description(
                game_state, player, opponent, player_index
            )

        embed.description = description

        # 頁腳信息
        footer_text = "%s | 盲注: %s/%s | 手牌: %s" % (
            config["name"],
            config["small_blind"],
            config["big_blind"],
            game_state.hand_number,
        )
        if game_state.game_over:
            footer_text += " | %s" % game_state.win_reason
        elif hand_just_ended:
            footer_text += " | %s" % game_state.win_reason

        embed.set_footer(text=footer_text)
        return embed

    def _format_cards_display(self, cards: List[PokerCard]) -> str:
        """統一格式化卡牌顯示"""
        return " ".join(str(card) for card in cards) if cards else ""

    def _format_hand_section(
        self, cards: List[PokerCard], title: str, show_condition: bool = True
    ) -> str:
        """統一格式化手牌區段"""
        if not show_condition or not cards:
            return ""

        cards_str = self._format_cards_display(cards)
        return f"**{title}：**\n# {cards_str}\n\n"

    def _get_embed_title_and_color(
        self, game_state: PokerGameState, player_index: int, hand_just_ended: bool
    ):
        """獲取embed標題和顏色"""
        player = game_state.player1 if player_index == 0 else game_state.player2

        if game_state.game_over:
            if game_state.winner_id == player.user_id:
                return "🎉 您獲勝了！", discord.Color.green()
            elif game_state.winner_id is None:
                return "🤝 平局", discord.Color.blue()
            else:
                return "😔 您輸了", discord.Color.red()
        elif hand_just_ended:
            if game_state.winner_id == player.user_id:
                return "🎉 您贏得這手牌！", discord.Color.green()
            elif game_state.winner_id is None:
                return "🤝 這手牌平局", discord.Color.blue()
            else:
                return "😔 您輸了這手牌", discord.Color.red()
        else:
            # 🔧 修復：直接使用函數
            if can_player_act(game_state, player_index):
                return (
                    "🃏 輪到您行動 - %s" % game_state.current_stage.value.title(),
                    discord.Color.gold(),
                )
            elif game_state.current_player_index == player_index:
                # 輪到你但你不能行動（比如已全押）
                return (
                    "⏸️ 您已全押 - %s" % game_state.current_stage.value.title(),
                    discord.Color.orange(),
                )
            else:
                return (
                    "🃏 等待對手行動 - %s" % game_state.current_stage.value.title(),
                    discord.Color.blue(),
                )

    def _build_game_over_description(
        self, game_state: PokerGameState, player, opponent
    ):
        """構建遊戲結束時的描述"""
        description = "**對局結束！**\n\n"

        # 顯示獲勝原因和底池信息（不重複標題中的結果）
        if game_state.winner_id == player.user_id:
            if hasattr(game_state, "final_pot") and game_state.final_pot > 0:
                description += "💰 **贏得底池：%s**\n" % f"{game_state.final_pot:,}"
            reason = game_state.game_end_reason or game_state.win_reason
            description += "📋 **獲勝原因：** %s\n" % reason
        elif game_state.winner_id is None:
            reason = game_state.game_end_reason or game_state.win_reason
            description += "📋 **結果：** %s\n" % reason
        else:
            reason = game_state.game_end_reason or game_state.win_reason
            description += "📋 **失敗原因：** %s\n" % reason

        # ===== 🔴 Bug 修復點 START 🔴 =====
        # 不再直接讀取 player.chips，而是根據遊戲結果計算最終籌碼

        total_chips_at_start = (
            game_state.player1_original_buyin + game_state.player2_original_buyin
        )

        your_final_chips = 0
        opponent_final_chips = 0

        if game_state.winner_id == player.user_id:
            # 你贏了
            your_final_chips = total_chips_at_start - game_state.rake_amount
            opponent_final_chips = 0
        elif game_state.winner_id is None:
            # 平局
            your_final_chips = (total_chips_at_start - game_state.rake_amount) // 2
            opponent_final_chips = (total_chips_at_start - game_state.rake_amount) // 2
        else:
            # 你輸了
            your_final_chips = 0
            opponent_final_chips = total_chips_at_start - game_state.rake_amount

        description += "\n**最終籌碼：**\n"
        description += "您：%s\n" % f"{your_final_chips:,}"
        description += "%s：%s\n\n" % (opponent.username, f"{opponent_final_chips:,}")

        # ===== 🔴 Bug 修復點 END 🔴 =====

        # 顯示最終手牌（如果有攤牌）
        description += self._format_hand_section(game_state.community_cards, "公共牌")
        description += self._format_hand_section(player.hole_cards, "您的手牌")

        show_opponent_cards = game_state.current_stage == GameStage.SHOWDOWN
        description += self._format_hand_section(
            opponent.hole_cards, "對手手牌", show_opponent_cards
        )

        return description

    def _build_hand_end_description(self, game_state: PokerGameState, player, opponent):
        """構建一手牌結束時的描述（顯示倒數計時）"""
        description = "**這手牌結束！**\n\n"

        # 顯示結果
        if game_state.winner_id == player.user_id:
            description += "🎉 **您贏得這手牌！**\n"
            if hasattr(game_state, "final_pot") and game_state.final_pot > 0:
                description += "贏得底池：%s\n" % f"{game_state.final_pot:,}"
        elif game_state.winner_id is None:
            description += "🤝 **這手牌平局**\n"
        else:
            description += "😔 **您輸了這手牌**\n"

        description += "\n**當前籌碼：**\n"
        description += "您：%s\n" % f"{player.chips:,}"
        description += "%s：%s\n\n" % (opponent.username, f"{opponent.chips:,}")

        # 顯示公共牌和手牌
        description += self._format_hand_section(game_state.community_cards, "公共牌")
        description += self._format_hand_section(player.hole_cards, "您的手牌")

        # 🔧 修復：在攤牌階段時顯示對手手牌（更準確的判斷條件）
        show_opponent_cards = game_state.current_stage == GameStage.SHOWDOWN
        description += self._format_hand_section(
            opponent.hole_cards, "對手手牌", show_opponent_cards
        )

        # 添加倒數計時
        description += "⏱️ **7秒後自動開始下一手牌...**"

        return description

    def _build_game_progress_description(
        self, game_state: PokerGameState, player, opponent, player_index: int
    ):
        """構建遊戲進行中的描述"""
        description = "**公共牌 (Community Cards):**\n"

        # 公共牌
        if game_state.community_cards:
            community_cards_str = " ".join(
                str(card) for card in game_state.community_cards
            )
            description += "# %s\n" % community_cards_str
        else:
            description += "[尚未發牌]\n"

        description += "\n**底池 (Pot):** %s\n\n" % f"{game_state.pot:,}"

        # 對手信息
        opponent_status = self._get_player_status(opponent)
        description += "**對手: %s**\n" % opponent.username
        description += "籌碼: %s | 狀態: %s\n" % (
            f"{opponent.chips:,}",
            opponent_status,
        )
        # 🔧 修復：使用重新導入的模組來獲取常量
        description += "# %s%s\n\n" % (
            HIDDEN_CARD_EMOJI,
            HIDDEN_CARD_EMOJI,
        )

        # 自己的信息
        player_status = self._get_player_status(player)
        arrow = " ➡️" if game_state.current_player_index == player_index else ""
        description += "**您: %s**%s\n" % (player.username, arrow)
        description += "籌碼: %s | 狀態: %s\n" % (f"{player.chips:,}", player_status)

        # 手牌
        if player.hole_cards:
            cards_str = self._format_cards_display(player.hole_cards)
            description += "# %s\n" % cards_str
        else:
            # 🔧 修復：使用重新導入的模組來獲取常量
            description += "# %s%s\n" % (
                HIDDEN_CARD_EMOJI,
                HIDDEN_CARD_EMOJI,
            )

        # 添加行動提示
        # 🔧 修復：直接使用函數
        if can_player_act(game_state, player_index):
            description += "\n⏰ **輪到您行動！您有60秒思考時間**"
        elif game_state.current_player_index == player_index:
            # 輪到你但你不能行動 - 檢查具體原因
            current_player = (
                game_state.player1 if player_index == 0 else game_state.player2
            )
            if current_player.is_all_in:
                description += "\n⏸️ **您已全押，等待對手行動中...**"
            else:
                # 其他原因導致無法行動（比如遊戲已結束等）
                description += "\n⏸️ **等待遊戲繼續...**"
        else:
            description += "\n⏳ **等待對手行動中...**"

        return description

    def _get_player_status(self, player):
        """獲取玩家狀態描述"""
        if player.has_folded:
            return "已棄牌"
        elif player.is_all_in:
            return "全押"
        elif player.last_action:
            return player.last_action.value
        else:
            return "等待中"

    async def _safe_update_player_message(
        self, game_state: PokerGameState, player_index: int, player_name: str
    ):
        """安全地更新玩家消息"""
        message = (
            game_state.player1_message
            if player_index == 0
            else game_state.player2_message
        )
        if not message:
            return

        embed = self._create_game_embed(game_state, player_index)
        view = PokerGameView(game_state, player_index, self)

        # 使用通用的編輯方法，減少重試次數（更新頻繁）
        await self._safe_edit_message(message, embed, view, player_name, max_retries=2)

    async def _handle_game_completion(self, game_state: PokerGameState):
        """處理遊戲完成後的業務邏輯（退還籌碼、記錄統計、清理）"""
        logger.info("開始處理遊戲 %s 的結算", game_state.game_id)

        try:
            # 原子性檢查和設置結算狀態
            if getattr(game_state, "_settlement_in_progress", False):
                logger.info("遊戲 %s 結算正在進行中，跳過", game_state.game_id)
                return

            if getattr(game_state, "_settlement_completed", False):
                logger.info("遊戲 %s 已經結算過，跳過", game_state.game_id)
                return

            # 標記結算正在進行
            game_state._settlement_in_progress = True
            logger.info("遊戲 %s 開始結算", game_state.game_id)

            try:
                # 退還剩餘籌碼到用戶餘額
                settlement_tasks = []

                if game_state.player1.chips > 0:
                    settlement_tasks.append(
                        economy_service.award_oil(
                            user_id=game_state.player1.user_id,
                            amount=game_state.player1.chips,
                            transaction_type="game:poker1v1_settlement",
                            reason=f"Poker 1v1 settlement for table {game_state.game_id}",
                        )
                    )
                    logger.info("準備退還玩家1 %s 籌碼", game_state.player1.chips)

                if game_state.player2.chips > 0:
                    settlement_tasks.append(
                        economy_service.award_oil(
                            user_id=game_state.player2.user_id,
                            amount=game_state.player2.chips,
                            transaction_type="game:poker1v1_settlement",
                            reason=f"Poker 1v1 settlement for table {game_state.game_id}",
                        )
                    )
                    logger.info("準備退還玩家2 %s 籌碼", game_state.player2.chips)

                # 並行執行退還操作
                if settlement_tasks:
                    await asyncio.gather(*settlement_tasks)
                    logger.info("遊戲 %s 籌碼退還完成", game_state.game_id)

                # 統計記錄已在每手牌結束時完成，這裡不需要再記錄
                logger.info("遊戲 %s 統計記錄已在手牌結束時完成", game_state.game_id)

                # 標記結算完成
                game_state._settlement_completed = True

            except Exception as settlement_error:
                logger.error(
                    "遊戲 %s 結算過程中發生錯誤: %s",
                    game_state.game_id,
                    settlement_error,
                    exc_info=True,
                )
                # 即使結算失敗，也要清理遊戲狀態
                game_state._settlement_completed = True
                raise settlement_error

            finally:
                # 清理遊戲（無論結算是否成功）
                if (
                    game_manager.matchmaking_queue
                    and game_state.game_id
                    in game_manager.matchmaking_queue.active_games
                ):
                    del game_manager.matchmaking_queue.active_games[game_state.game_id]
                    logger.info("遊戲 %s 從active_games中清理", game_state.game_id)
                else:
                    logger.warning("遊戲 %s 不在active_games中", game_state.game_id)

                # 清理遊戲處理器
                await self._cleanup_game_processor(game_state.game_id)

            logger.info("遊戲 %s 結算完成", game_state.game_id)

        except Exception as e:
            logger.error("遊戲結算失敗: %s", e, exc_info=True)
            # 確保遊戲被清理
            if (
                game_manager.matchmaking_queue
                and game_state.game_id in game_manager.matchmaking_queue.active_games
            ):
                del game_manager.matchmaking_queue.active_games[game_state.game_id]

    async def _is_player_in_game(self, user_id: int) -> bool:
        """檢查玩家是否已在進行中的遊戲中（不包括已結束的遊戲）"""
        if not game_manager.matchmaking_queue:
            return False
        for game_state in game_manager.matchmaking_queue.active_games.values():
            # 只檢查未結束的遊戲
            if not game_state.game_over and (
                game_state.player1.user_id == user_id
                or game_state.player2.user_id == user_id
            ):
                return True
        return False

    async def _is_player_in_queue(self, user_id: int) -> bool:
        """檢查玩家是否已在隊列中"""
        if not game_manager.matchmaking_queue:
            return False
        for tier_queue in game_manager.matchmaking_queue.queues.values():
            for entry in tier_queue:
                if entry.user_id == user_id:
                    return True
        return False

    async def _record_hand_stats(self, game_state: PokerGameState):
        """記錄單手牌統計"""
        try:
            # 為雙方記錄這手牌的統計
            for player_index, player in enumerate(
                [game_state.player1, game_state.player2]
            ):
                opponent = (
                    game_state.player2 if player_index == 0 else game_state.player1
                )

                # 判斷這手牌的結果
                if game_state.winner_id == player.user_id:
                    result = "win"
                elif game_state.winner_id is None:
                    result = "push"  # 統一使用 'push' 而不是 'draw'
                else:
                    result = "lose"

                # 計算這手牌的盈虧（基於底池分配）
                hand_profit = 0
                if game_state.winner_id == player.user_id:
                    hand_profit = game_state.final_pot - player.total_bet_this_hand
                elif game_state.winner_id is None:
                    hand_profit = (
                        game_state.final_pot // 2
                    ) - player.total_bet_this_hand
                else:
                    hand_profit = -player.total_bet_this_hand

                # 🔧 修復：計算抽水統計分攤
                rake_for_stats = 0
                if game_state.rake_amount > 0:
                    if game_state.winner_id == player.user_id:
                        # 獲勝者承擔全部抽水統計
                        rake_for_stats = game_state.rake_amount
                    elif game_state.winner_id is None:
                        # 平局時，將抽水統計分攤給兩個玩家
                        rake_for_stats = game_state.rake_amount // 2

                # 構建單手牌數據
                hand_data = {
                    # 標準統一參數
                    "bet": player.total_bet_this_hand,
                    "payout": (
                        player.total_bet_this_hand + hand_profit
                        if hand_profit > 0
                        else 0
                    ),
                    "profit": hand_profit,
                    "result": result,
                    # 德州撲克核心參數
                    "opponent_id": opponent.user_id,
                    "stake_tier": game_state.stake_tier.value,
                    "hands_played": 1,  # 單手牌統計
                    "rake_amount": rake_for_stats,
                    "win_reason": game_state.win_reason,
                    "game_duration": 0,  # 單手牌不計算總遊戲時間
                    # 手牌信息
                    "player_hand": [
                        (card.suit, card.value) for card in player.hole_cards
                    ],
                    "community_cards": [
                        (card.suit, card.value) for card in game_state.community_cards
                    ],
                    # 布林統計標記
                    "is_fold": result == "lose" and game_state.win_reason == "對手棄牌",
                    "is_all_in": player.is_all_in,
                }

                await game_stats_service.record_game_result(
                    player.user_id, "poker1v1", hand_data
                )

            # 標記統計已記錄
            game_state._hand_stats_pending = False
            logger.info(
                "遊戲 %s 手牌 %s 統計記錄完成",
                game_state.game_id,
                game_state.hand_number,
            )

        except Exception as e:
            logger.error("記錄手牌統計失敗: %s", e, exc_info=True)

    async def _record_game_stats(self, game_state: PokerGameState):
        """記錄遊戲統計（已廢棄，改用每手牌記錄）"""
        # 這個方法現在只用於記錄整個遊戲的結束，不再記錄統計數據
        # 統計數據改為在每手牌結束時記錄
        logger.info(
            "遊戲 %s 結束，總共 %s 手牌", game_state.game_id, game_state.hand_number
        )
        pass


async def setup(bot: commands.Bot):
    """設置COG"""
    await bot.add_cog(Poker1v1Cog(bot))
