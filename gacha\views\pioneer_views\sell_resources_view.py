import discord
from discord.ext.commands import Bo<PERSON>
from discord.ui import Modal, Select, TextInput

from gacha.exceptions import BusinessError
from pioneer.core.game_data_loader import game_data
from pioneer.modules import action_module
from pioneer.repositories import warehouse_repo
from utils.base_view import BaseView
from utils.response_embeds import SuccessEmbed


def get_item_emoji(item_id: str) -> str:
    """根據物品ID獲取其表情符號，如果未定義則返回默認表情符號。"""
    assert game_data is not None
    item_config = game_data.get_item_config(item_id)
    return getattr(item_config, "emoji", "📦") if item_config else "📦"


class ItemSelectDropdown(Select):
    def __init__(self, items, placeholder="選擇要出售的物品..."):
        options = [
            discord.SelectOption(
                label=f"{item.name} (庫存: {item.quantity})",
                value=str(item.item_id),
                emoji=get_item_emoji(item.item_id),
            )
            for item in items
            if item.quantity > 0
        ]
        if not options:
            options.append(
                discord.SelectOption(
                    label="你的倉庫是空的！", value="empty", emoji="📭"
                )
            )

        super().__init__(
            placeholder=placeholder,
            options=options,
            min_values=1,
            max_values=1,
            disabled=not options or options[0].value == "empty",
        )

    async def callback(self, interaction: discord.Interaction):
        assert self.view is not None
        if self.values[0] == "empty":
            await interaction.response.defer()
            return

        self.view.selected_item_id = self.values[0]
        await interaction.response.defer()
        await self.view.update_message(interaction)


class SellModal(Modal, title="輸入出售數量"):
    quantity_input = TextInput(
        label="數量", placeholder="請輸入要出售的數量", required=True
    )

    def __init__(self, item_name: str, max_quantity: int, view: "SellResourcesView"):
        super().__init__()
        self.item_name = item_name
        self.max_quantity = max_quantity
        self.view = view
        self.quantity_input.placeholder = f"最多可出售 {max_quantity} 個"

    async def on_submit(self, interaction: discord.Interaction):
        quantity_str = self.quantity_input.value

        # 讓 BusinessError 統一處理
        from gacha.exceptions import BusinessError

        try:
            quantity = int(quantity_str)
            if not (0 < quantity <= self.max_quantity):
                raise BusinessError(
                    f"數量無效。請輸入 1 到 {self.max_quantity} 之間的數字。"
                )
        except ValueError as e:
            raise BusinessError("請輸入有效的數字。") from e

        await interaction.response.defer(ephemeral=True)

        params = {"item_id": self.view.selected_item_id, "quantity": quantity}

        # 讓 action_module 的錯誤自然冒泡
        result = await action_module.execute_action(
            user_id=interaction.user.id, action_id="sell_resource", params=params
        )

        if result.success:
            embed = SuccessEmbed(description=result.message)
            await interaction.followup.send(embed=embed, ephemeral=True)
            await self.view.update_message(
                interaction, sold_item_id=self.view.selected_item_id
            )
        else:
            # 如果 action_module 執行失敗，它應該拋出 BusinessError
            raise BusinessError(f"交易失敗：{result.message}")


class SellResourcesView(BaseView):
    def __init__(self, bot: Bot, user_id: int, timeout=180):
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.selected_item_id = None
        self.items = []
        self.message = None

    async def create_embed(self):
        assert game_data is not None
        embed = discord.Embed(
            title="📦 資源出售",
            description="選擇你想要出售的物品。\n出售價格為物品基礎售價。",
            color=discord.Color.green(),
        )
        if self.selected_item_id:
            item_config = game_data.get_item_config(self.selected_item_id)
            warehouse_item = await warehouse_repo.get_warehouse_item(
                self.user_id, self.selected_item_id
            )
            if item_config and warehouse_item:
                embed.add_field(
                    name="選中物品",
                    value=f"{get_item_emoji(self.selected_item_id)} {item_config.name}",
                    inline=True,
                )
                embed.add_field(
                    name="單價",
                    value=f"{item_config.base_sell_price:,} 油幣",
                    inline=True,
                )
                embed.add_field(
                    name="庫存", value=f"{warehouse_item.quantity}", inline=True
                )
                embed.set_footer(text=f"物品ID: {self.selected_item_id}")
        else:
            embed.set_footer(text="請從下方選擇一個物品以查看詳情。")

        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif?ex=682f9ce9&is=682e4b69&hm=4c0eb6b418eaed804fe07b7cba125dd1daeee0212508d71f598be3c19294b647&"
        )
        return embed

    async def update_components(self, sold_item_id=None):
        self.clear_items()

        # 如果有物品被賣出，重新從數據庫獲取物品列表
        # 否則，如果 self.items 已經存在，就使用它
        if sold_item_id or not self.items:
            self.items = await warehouse_repo.get_user_warehouse(self.user_id)

        self.add_item(ItemSelectDropdown(self.items))

        if self.selected_item_id:
            self.add_item(self.create_sell_button())

    def create_sell_button(self):
        return discord.ui.Button(
            label="出售選中物品",
            style=discord.ButtonStyle.success,
            custom_id="sell_item_button",
            disabled=not self.selected_item_id,
        )

    async def on_timeout(self) -> None:
        if self.message:
            for item in self.children:
                if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                    item.disabled = True
            await self.message.edit(view=self)

    async def update_message(self, interaction: discord.Interaction, sold_item_id=None):
        await self.update_components(sold_item_id=sold_item_id)
        embed = await self.create_embed()

        # 確保在編輯之前有回應
        if not interaction.response.is_done():
            await interaction.response.defer()

        await interaction.edit_original_response(embed=embed, view=self)
        if not self.message:
            self.message = await interaction.original_response()

    @discord.ui.button(
        label="出售選中物品",
        style=discord.ButtonStyle.success,
        custom_id="sell_item_button",
        disabled=True,
    )
    async def sell_item_button_callback(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        assert game_data is not None
        if not self.selected_item_id:
            raise BusinessError("請先選擇一個物品。")

        item_config = game_data.get_item_config(self.selected_item_id)
        warehouse_item = await warehouse_repo.get_warehouse_item(
            self.user_id, self.selected_item_id
        )

        if not item_config or not warehouse_item or warehouse_item.quantity <= 0:
            raise BusinessError("物品資訊有誤或庫存為零。")

        modal = SellModal(
            item_name=item_config.name,
            max_quantity=warehouse_item.quantity,
            view=self,
        )
        await interaction.response.send_modal(modal)
