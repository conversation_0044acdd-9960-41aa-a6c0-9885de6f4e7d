from __future__ import annotations

from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Dict, List, Literal, Optional

import asyncpg

import gacha.repositories.collection.user_collection_repository as collection_repo

# 依賴的服務和倉庫
import gacha.services.user_service as user_service
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import SellValidationError, StoredPriceError, UserNotFoundError
from gacha.models.filters import CollectionFilters
from gacha.repositories.collection.user_collection_repository import (
    CardSellOperation,
    SellOperationDetailType,
)
from gacha.services import (
    highest_star_maintenance_service,
)
from utils.logger import logger


class InvalidPriceError(Exception):
    """當卡片價格無效或無法獲取時拋出此異常。"""

    pass


SellOperationType = Literal["ONE", "LEAVE_ONE", "ALL"]


async def get_balance(user_id: int, nickname: Optional[str] = None) -> Dict[str, int]:
    """
    獲取用戶油幣餘額和相關資訊（自動創建用戶）

    Args:
        user_id: 用戶ID
        nickname: 用戶暱稱（僅在創建時使用）

    Returns:
        Dict[str, int]: 包含餘額和抽卡次數的字典

    Raises:
        UserNotFoundError: 如果用戶不存在且無法創建
    """
    return await user_service.get_balance(user_id, nickname)


async def prepare_sell_preview(
    user_id: int,
    filters: CollectionFilters,
    operation_type: SellOperationType,
    force_sell: bool = False,
) -> Dict[str, Any]:
    """
    準備卡片售賣的預覽，計算將被售賣的卡片和預期收益，但不執行任何資料庫寫入。

    參數:
        user_id (int): 用戶ID。
        filters (CollectionFilters): 卡片篩選條件。
        operation_type (SellOperationType): 售賣操作類型 ('ONE', 'LEAVE_ONE', 'ALL')。
        force_sell (bool): 是否強制售賣最愛卡片。

    返回:
        包含詳細售賣計畫的字典，與 _prepare_sell_operations 的返回結構相同。

    拋出:
        UserNotFoundError: 如果用戶不存在
        StoredPriceError: 如果卡片預存價格無效
        ValueError: 如果沒有符合條件的卡片可售賣
    """
    # 驗證用戶存在
    user_obj = await user_service.get_user(user_id)
    if not user_obj:
        raise UserNotFoundError(user_id=user_id)

    # 獲取候選卡片 (這一步是安全的，只讀取數據)
    candidates_with_stored_prices = await _gather_sell_candidates_and_stored_prices(
        user_id, filters
    )
    if not candidates_with_stored_prices:
        raise SellValidationError("沒有找到符合篩選條件的卡片可供售賣。")

    # 準備售賣計劃 (這一步是純計算，是安全的)
    sell_plan = _prepare_sell_operations(
        candidates_with_stored_prices, operation_type, force_sell, filters
    )

    # 檢查是否有任何東西可以賣
    if not sell_plan.get("cards_to_update", []) and not sell_plan.get(
        "cards_to_delete", []
    ):
        raise SellValidationError(
            "沒有符合售賣條件的卡片（可能由於數量、最愛設置或操作類型限制）。"
        )

    # 直接返回計算好的計畫，不執行交易
    return sell_plan


async def sell_cards_universal(
    user_id: int,
    filters: CollectionFilters,
    operation_type: SellOperationType,
    force_sell: bool = False,
) -> Dict[str, Any]:
    """
    通用的卡片售賣接口 (基於預存價格)。

    參數:
        user_id (int): 用戶ID。
        filters (CollectionFilters): 卡片篩選條件。
        operation_type (SellOperationType): 售賣操作類型 ('ONE', 'LEAVE_ONE', 'ALL')。
        force_sell (bool): 是否強制售賣最愛卡片。

    返回:
        包含詳細售賣結果的字典。

    拋出:
        UserNotFoundError: 如果用戶不存在
        StoredPriceError: 如果卡片預存價格無效
        ValueError: 如果沒有符合條件的卡片可售賣
        RuntimeError: 如果數據庫操作失敗
    """
    # 驗證用戶存在
    user_obj = await user_service.get_user(user_id)
    if not user_obj:
        raise UserNotFoundError(user_id=user_id)

    # 獲取候選卡片
    candidates_with_stored_prices = await _gather_sell_candidates_and_stored_prices(
        user_id, filters
    )
    if not candidates_with_stored_prices:
        raise SellValidationError("沒有找到符合篩選條件的卡片可供售賣。")

    # 準備售賣計劃
    sell_plan = _prepare_sell_operations(
        candidates_with_stored_prices, operation_type, force_sell, filters
    )

    if not sell_plan.get("cards_to_update", []) and not sell_plan.get(
        "cards_to_delete", []
    ):
        raise SellValidationError(
            "沒有符合售賣條件的卡片（可能由於數量、最愛設置或操作類型限制）。"
        )

    # 執行售賣事務
    sell_tx_result = await _execute_sell_in_transaction(
        user_id=user_id,
        sell_operations_for_repo=sell_plan["sell_operations_for_repo"],
        precise_total_revenue=sell_plan["total_revenue"],
        sold_cards_details_for_response=sell_plan["sold_cards_details_for_response"],
        candidates_with_stored_prices=candidates_with_stored_prices,
    )

    formatted_response = _format_sell_response(
        message="",
        total_cards_sold=sell_plan["total_quantity_sold"],
        total_revenue=int(sell_tx_result["total_revenue"]),
        new_balance=sell_tx_result["new_balance"],
        sold_cards_summary=sell_plan["sold_cards_details_for_response"],
    )
    # 把摘要加進去
    formatted_response["operation_summary"] = sell_plan.get("operation_summary")
    return formatted_response


async def _gather_sell_candidates_and_stored_prices(
    user_id: int, filters: CollectionFilters
) -> List[Dict[str, Any]]:
    """
    (事務外) 獲取符合篩選條件的用戶卡片快照，這些快照需包含預存的市場價格。
    如果任何卡片的預存價格無效 (例如 NULL 或 <=0)，則拋出 StoredPriceError。
    """
    card_snapshots = (
        await collection_repo.get_sellable_card_snapshots_with_stored_price(
            user_id, filters
        )
    )
    if not card_snapshots:
        return []

    valid_candidates = []
    for snapshot in card_snapshots:
        card_id = snapshot.get("card_id")
        if card_id is None:
            continue

        stored_price = snapshot.get("current_market_sell_price")
        if (
            not stored_price
            or not isinstance(stored_price, Decimal)
            or stored_price <= Decimal(0)
        ):
            card_name = snapshot.get("name", "N/A")
            err_msg = f"卡片 {card_id} (名稱: {card_name}) 的預存市場價格無效 ({stored_price})。售賣操作已中止。"
            raise StoredPriceError(err_msg)

        valid_candidates.append(
            {
                "card_id": card_id,
                "quantity": snapshot["quantity"],
                "is_favorite": snapshot["is_favorite"],
                "star_level": snapshot.get("star_level", 0),
                "name": snapshot.get("name", "N/A"),
                "rarity": snapshot.get("rarity"),
                "pool_type": snapshot.get("pool_type"),
                "actual_sell_price": stored_price,
            }
        )

    return valid_candidates


def _prepare_sell_operations(
    candidates_with_stored_prices: List[Dict[str, Any]],
    operation_type: SellOperationType,
    force_sell: bool,
    filters: CollectionFilters,
) -> Dict[str, Any]:
    """
    (事務外) 根據候選卡片、其預存價格和操作類型，計算實際要售賣的卡片和數量。
    返回一個包含 CardSellOperation 列表以及其他摘要信息的字典。
    """
    sell_operations_for_repo: List[CardSellOperation] = []
    sold_cards_details_for_response: List[Dict[str, Any]] = []
    total_revenue = Decimal(0)
    total_quantity_sold = 0
    cards_to_update: List[int] = []
    cards_to_delete: List[int] = []

    for candidate in candidates_with_stored_prices:
        # 跳過最愛卡片（除非強制售賣）
        if not force_sell and candidate["is_favorite"]:
            continue

        card_id = candidate["card_id"]
        current_quantity = candidate["quantity"]
        sell_price = candidate["actual_sell_price"]

        # 計算售賣數量和操作類型
        quantity_to_sell, repo_operation_type = _calculate_sell_quantity(
            current_quantity, operation_type
        )

        if quantity_to_sell == 0 and current_quantity > 0:
            continue  # 跳過不需要售賣的卡片

        # *** 核心修改：使用無條件捨去（直接取整） ***
        effective_price_per_unit = int(sell_price)  # 直接用 int() 實現無條件捨去

        # 計算收益（使用捨去後的價格）
        card_revenue = (
            effective_price_per_unit * quantity_to_sell if quantity_to_sell > 0 else 0
        )
        total_revenue += Decimal(card_revenue)
        total_quantity_sold += quantity_to_sell

        # 創建操作記錄
        sell_operations_for_repo.append(
            {
                "card_id": card_id,
                "operation_type": repo_operation_type,
                "current_quantity_owned": current_quantity,
                "quantity_to_sell": (
                    quantity_to_sell
                    if repo_operation_type == "sell_specific_quantity_of_card"
                    else None
                ),
            }
        )

        # 創建響應詳情
        sold_cards_details_for_response.append(
            {
                "card_id": card_id,
                "name": candidate.get("name", "N/A"),
                "rarity": candidate.get("rarity"),
                "pool_type": candidate.get("pool_type"),
                "series": candidate.get("series"),
                "quantity_sold": quantity_to_sell,
                "price_per_unit": effective_price_per_unit,
                "total_value_sold_for_card": card_revenue,
                "is_favorite": candidate["is_favorite"],
            }
        )

        # 記錄卡片狀態變化
        remaining_quantity = current_quantity - quantity_to_sell
        if remaining_quantity > 0:
            cards_to_update.append(card_id)
        else:
            cards_to_delete.append(card_id)

    # 判斷是否是針對單張卡的精確操作
    is_single_card_operation = (
        operation_type == "ONE" and len(sold_cards_details_for_response) == 1
    )

    # 生成操作摘要元數據
    operation_summary = {
        "operation_type_display": {
            "ALL": "賣出所有",
            "LEAVE_ONE": "對每種卡片賣到剩1張",
            "ONE": "對每種卡片賣出1張",
        }.get(operation_type, "未知操作"),
        "include_favorites_display": (
            "（包含最愛 ❤️）" if force_sell else "（不包含最愛）"
        ),
        "filters_display": _generate_filter_display_from_results(
            sold_cards_details_for_response, is_single_card_operation, filters
        ),
        "operation_type": operation_type,  # 保留原始操作類型用於判斷
        "is_single_card_operation": is_single_card_operation,
    }

    return {
        "sell_operations_for_repo": sell_operations_for_repo,
        "total_revenue": total_revenue,
        "total_quantity_sold": total_quantity_sold,
        "sold_cards_details_for_response": sold_cards_details_for_response,
        "cards_to_update": cards_to_update,
        "cards_to_delete": cards_to_delete,
        "operation_summary": operation_summary,
    }


def _calculate_sell_quantity(
    current_quantity: int, operation_type: SellOperationType
) -> tuple[int, SellOperationDetailType]:
    """計算售賣數量和操作類型"""
    if operation_type == "ONE":
        if current_quantity >= 1:
            return 1, "sell_specific_quantity_of_card"
        elif current_quantity == 0:
            return 0, "sell_all_for_card"
    elif operation_type == "LEAVE_ONE":
        if current_quantity > 1:
            return current_quantity - 1, "sell_leaving_one_for_card"
        elif current_quantity == 0:
            return 0, "sell_all_for_card"
    elif operation_type == "ALL":
        return current_quantity, "sell_all_for_card"

    return 0, "sell_all_for_card"  # 默認情況


def _generate_filter_display_from_results(
    results: List[Dict[str, Any]],
    is_single_card_operation: bool,
    filters: CollectionFilters,
) -> str:
    """從用戶輸入的篩選條件生成準確的描述文字。"""
    if not results:
        return "卡片"

    # 如果是只賣一張的精確操作，直接顯示卡片名稱或ID
    if is_single_card_operation:
        if filters.card_id:
            return f"ID為 `{filters.card_id}` 的卡片"
        elif filters.card_name:
            return f"名稱包含「{filters.card_name}」的卡片"
        else:
            return f"卡片「{results[0].get('name', '未知')}」"

    filter_parts = []

    # 現在，我們只在用戶真正指定了篩選條件時，才加入描述
    if filters.series:
        filter_parts.append(f"系列為「{filters.series}」")

    if filters.pool_type:
        from config.app_config import get_pool_type_names

        pool_name = get_pool_type_names().get(filters.pool_type, filters.pool_type)
        filter_parts.append(f"來自「{pool_name}」卡池")

    if filters.rarity_in:
        # 假設 rarity_in 裡只有一個元素，因為 UI 上是單選
        rarity_value = filters.rarity_in[0]
        try:
            from gacha.constants import RarityLevel

            rarity_level = RarityLevel(rarity_value)
            from config.app_config import get_rarity_display_codes

            display_code = get_rarity_display_codes().get(
                rarity_level.value, f"R:{rarity_value}"
            )
            filter_parts.append(f"稀有度為 {display_code}")
        except (ValueError, TypeError):
            filter_parts.append(f"稀有度為 {rarity_value}")

    if not filter_parts:
        # 如果用戶沒有輸入任何批量篩選條件，返回空字符串
        # 讓上層邏輯處理組合，避免重複的「所有」
        return ""

    # 組合描述
    return " ".join(filter_parts)


async def _execute_sell_in_transaction(
    user_id: int,
    sell_operations_for_repo: List[CardSellOperation],
    precise_total_revenue: Decimal,
    sold_cards_details_for_response: List[Dict[str, Any]],
    candidates_with_stored_prices: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """
    (事務內) 執行實際的數據庫售賣操作，調用 UserCollectionRepository 中的通用批量方法。
    此方法接收精確的總收益 (Decimal)，並在其內部進行四捨五入以更新用戶餘額。
    返回包含四捨五入總收益 (Decimal) 和新餘額 (int) 的字典。
    """
    card_ids_to_invalidate: List[int] = []
    repo_result_data_for_stream: Optional[Dict[str, Any]] = None

    pool = get_pool()
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 獲取當前餘額
            user = await user_service.get_user_for_update(user_id, connection=conn)
            current_balance = user.oil_balance
            if current_balance is None:
                raise RuntimeError(
                    f"執行售賣事務失敗：找不到用戶 {user_id} 或無法獲取其鎖定餘額"
                )

            # 處理售賣操作
            if sell_operations_for_repo:
                repo_result_data = await collection_repo.process_sell_operations_batch(
                    user_id=user_id,
                    operations=sell_operations_for_repo,
                    connection=conn,
                )
                repo_result_data_for_stream = repo_result_data
                card_ids_to_invalidate = repo_result_data.get(
                    "card_ids_for_cache_invalidation", []
                )

            # 計算新餘額
            rounded_revenue = precise_total_revenue.quantize(
                Decimal("1"), rounding=ROUND_HALF_UP
            )
            new_balance = current_balance + int(rounded_revenue)

            # 更新用戶餘額並記錄歷史
            # 我們將在這裡直接調用 user_service.award_balance，
            # 因為它現在封裝了更新餘額和記錄歷史的邏輯。
            # 由於我們已經在一個事務中，所以傳入 connection。
            await user_service.award_balance(
                user_id=user_id,
                amount=int(rounded_revenue),
                transaction_type="sell_card",
                reason=f"Sold {len(sold_cards_details_for_response)} types of cards",
                connection=conn,
            )

            # 【新增】在同一事務中更新市場統計
            if repo_result_data_for_stream:
                from gacha.services.direct_market_stats_updater import (
                    update_market_stats_for_sell_in_transaction,
                )

                deleted_cards = repo_result_data_for_stream.get("deleted_cards", [])
                await update_market_stats_for_sell_in_transaction(
                    conn=conn,
                    sold_cards_details=sold_cards_details_for_response,
                    deleted_cards=deleted_cards,
                )

                logger.info(
                    "[SELL_TRANSACTION] 賣卡事務完成 - 用戶: %s, 賣出: %s, 刪除: %s, 統計更新: 完成",
                    user_id,
                    len(sold_cards_details_for_response),
                    len(deleted_cards),
                )

    # 事務成功後的後續處理
    redis_client = get_redis_client()
    if card_ids_to_invalidate and redis_client:
        # 根據規範，移除 try-except，讓快取錯誤自然冒泡
        await collection_repo._invalidate_owner_count_cache_batch(
            redis_client, card_ids_to_invalidate
        )

    # 安排最高星級維護任務（後台處理）- 只在卡片被完全移除且用戶有星級時觸發
    if highest_star_maintenance_service and repo_result_data_for_stream:
        # 根據規範，移除 try-except，讓後台任務排程的錯誤也能被追蹤
        deleted_cards = repo_result_data_for_stream.get("deleted_cards", [])
        if deleted_cards:
            # 使用字典推導創建卡片ID到星級的映射
            card_star_levels = {
                candidate["card_id"]: candidate.get("star_level", 0)
                for candidate in candidates_with_stored_prices
            }

            # 使用列表推導只處理有星級的卡片
            card_user_pairs_with_stars = [
                (card_id, user_id)
                for deleted_card in deleted_cards
                if (card_id := deleted_card.get("card_id"))
                and card_star_levels.get(card_id, 0) > 0
            ]

            if card_user_pairs_with_stars:
                await highest_star_maintenance_service.schedule_card_sold_maintenance_batch(
                    card_user_pairs_with_stars
                )

    return {"total_revenue": rounded_revenue, "new_balance": new_balance}


def _format_sell_response(
    message: str,
    total_cards_sold: int = 0,
    total_revenue: int = 0,
    new_balance: Optional[int] = None,
    sold_cards_summary: Optional[List[Dict]] = None,
) -> Dict[str, Any]:
    """
    格式化售賣操作的響應。

    參數:
        message: 響應消息
        total_cards_sold: 售賣的卡片總數
        total_revenue: 總收益
        new_balance: 新餘額
        sold_cards_summary: 售賣卡片摘要

    返回:
        格式化的響應字典
    """
    sold_cards_summary = sold_cards_summary or []
    return {
        "message": message,
        "total_cards_sold": total_cards_sold,
        "total_revenue": total_revenue,
        "new_balance": new_balance,
        "sold_cards_summary": sold_cards_summary,
        "sold_cards_for_examples": sold_cards_summary,
    }


async def award_oil(
    user_id: int,
    amount: int,
    transaction_type: str,
    reason: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """
    獎勵用戶油幣（用於遊戲獲勝等情況）

    Args:
        user_id: 用戶ID
        amount: 獎勵金額（可為負數，表示扣除）
        transaction_type: 交易類型
        reason: 交易原因描述（可選，用於日誌記錄）
        connection: 可選的資料庫連接

    Returns:
        int: 更新後的餘額

    Raises:
        UserNotFoundError: 如果用戶不存在
        ValueError: 如果扣除金額大於用戶餘額
        DatabaseOperationError: 如果資料庫操作失敗
    """
    # 記錄交易原因到日誌（如果提供）
    if reason:
        pass

    return await user_service.award_balance(
        user_id, amount, transaction_type, reason, connection=connection
    )
