/* user-info.css - 用戶資訊相關樣式 */

/* 用戶資訊面板 */
.user-info-panel { /* Applied alongside .info-panel, so only define overrides or specifics */
  /* border-radius and color are inherited from .info-panel */
  background: rgba(20, 20, 22, 0.15); /* Override .info-panel */
  backdrop-filter: blur(6px) saturate(120%); /* Override .info-panel */
  -webkit-backdrop-filter: blur(6px) saturate(120%); /* Override .info-panel */
  border: 1px solid rgba(255, 255, 255, 0.08); /* Override .info-panel */
  padding: var(--spacing-sm) var(--spacing-lg); /* Reduced padding */
  margin-bottom: 0;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.user-info-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  opacity: 0.6;
}

/* 用戶頭像 */
.user-avatar-container {
  width: var(--avatar-size); /* 改为固定值，不再使用 calc(var(--avatar-size) * 0.9) */
  height: var(--avatar-size); /* 确保宽高相等 */
  border-radius: 50%; /* 确保是完美的圆形，不再使用变量 */
  background-color: var(--color-bg-darker);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-3xl);
  font-weight: bold;
  color: var(--color-text-light);
  border: 2px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  min-width: var(--avatar-size); /* 确保不会被压缩 */
  min-height: var(--avatar-size); /* 确保不会被压缩 */
  flex-shrink: 0; /* 防止在flex布局中被压缩 */
}

.user-avatar-container::after {
  content: '';
  position: absolute;
  top: -10%; 
  left: -10%;
  width: 120%; 
  height: 120%;
  background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
  pointer-events: none;
  opacity: 0.7;
}

#username-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-primary-rgb), 0.2);
  font-size: var(--avatar-font-size);
  color: var(--color-text-light);
  text-shadow: 0 2px 10px rgba(0,0,0,0.5);
  letter-spacing: 1px;
}

/* 用戶名稱 (username-container class was here, removed as margin is handled by Tailwind in HTML) */

/* 用戶統計數據 */
.user-stats {
  margin-top: var(--spacing-xs); /* Reduced margin-top */
  font-size: var(--font-lg);
}

.user-stats .stat-label {
  color: rgba(255, 255, 255, 0.85);
  margin-right: var(--spacing-sm);
  font-weight: 600;
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.user-stats .stat-label i {
  margin-right: 8px;
  background: rgba(255, 255, 255, 0.05);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  color: var(--color-text-light);
}

.user-stats .stat-value {
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  padding: 2px 12px;
  border-radius: var(--radius-md);
  background-color: rgba(0, 0, 0, 0.25);
  font-size: 1.4rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 特定統計項目顏色 */
#oil-balance {
  color: rgba(255, 255, 255, 0.9);
}

#oil-ticket-balance {
  color: rgba(255, 255, 255, 0.9);
}

#completion-rate {
  color: rgba(255, 255, 255, 0.9);
}

#total-draws {
  color: rgba(255, 255, 255, 0.9);
}

#like-count {
  color: rgba(255, 255, 255, 0.9);
}

#total-owned {
  color: rgba(255, 255, 255, 0.9);
}

/* 旧的 #rarity-counts 样式移除或注释掉 */
/*
#rarity-counts {
  font-size: 1.1em; 
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.3;
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-xs) * 0.7) var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.25);
  border-radius: var(--radius-md);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
*/

/* 新的稀有度标签样式 */
#rarity-counts {
  display: flex; /* Use flexbox for alignment */
  flex-wrap: wrap; /* Allow tags to wrap to the next line */
  gap: calc(var(--spacing-xs) * 0.8); /* Gap between tags, slightly smaller */
  justify-content: flex-start; /* Align tags to the start */
  align-items: center; /* Center items vertically */
  margin-top: calc(var(--spacing-xs) * 0.5); /* Add a little top margin to the container itself */
}

.rarity-tag {
  display: inline-flex; 
  align-items: center;
  padding: calc(var(--spacing-xs) * 0.6) calc(var(--spacing-sm) * 0.8);
  border-radius: var(--radius-pill); /* Pill shape */
  font-size: 1rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 1px rgba(0,0,0,0.25);
  line-height: 1.2;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.rarity-tag .count {
  font-weight: 800;
  margin-left: calc(var(--spacing-xs) * 0.6);
}

.rarity-c {
  background-color: #6c757d; 
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-r {
  background-color: #0d6efd; /* Updated Bootstrap primary blue */
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-sr {
  background-color: #6f42c1; 
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-ssr {
  background-color: #fd7e14; 
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-ur {
  background-color: #dc3545; 
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-lr {
  background-color: #0dcaf0; /* Updated Bootstrap info cyan */
  border: 1px solid rgba(0,0,0,0.1);
}

.rarity-ex {
  background-color: #ffc107; 
  border: 1px solid rgba(0,0,0,0.1);
  color: #000; /* Darker text for better contrast on yellow */
}

/*徽章圖標 - REMOVED as unused in profile_template.html */
/*
.badge-icon {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  border: 1px solid var(--color-border);
  cursor: default;
  position: relative;
  overflow: hidden;
}
*/

#username {
  font-weight: 700;
  font-size: 3rem;
  letter-spacing: 0.5px;
  color: white;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
  margin-bottom: 0;
  line-height: 1;
}

/* 为用户ID和用户状态增加样式 */
.user-info-panel p, 
#user-status { /* 选择用户状态和面板内的 p 标签 */
  font-size: 1.2rem; /* 增大字体 */
  font-weight: 500; /* 增加粗细 */
}