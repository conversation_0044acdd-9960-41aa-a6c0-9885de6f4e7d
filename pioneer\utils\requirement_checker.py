"""
Pioneer System - Requirement Checker Utility
提供一個通用的需求檢查功能。
"""

from typing import TYPE_CHECKING, Any, Dict, List

from pioneer.exceptions import ConfigNotFoundError, RequirementsNotMetError

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader


async def check_requirements(
    user_id: int,
    requirements: List[Dict[str, Any]],
    game_data: "GameDataLoader",
    repos: Any,
    conn=None,
) -> None:
    """
    通用需求檢查函數。
    如果需求不滿足，將拋出 RequirementsNotMetError。
    如果設定檔缺失，將拋出 ConfigNotFoundError。

    Args:
        user_id (int): 用戶ID。
        requirements (List[Dict[str, Any]]): 需求列表。
        game_data (GameDataLoader): 遊戲數據載入器。
        repos (module): 包含所有 repository 函數的模組。
        conn: 可選的資料庫連接。

    Raises:
        RequirementsNotMetError: 當一項或多項需求未得到滿足時。
        ConfigNotFoundError: 當找不到必要的遊戲設定時。
    """
    if not requirements:
        return

    missing_messages = []
    from gacha.services import user_service

    for req in requirements:
        req_type = req.get("type")

        if req_type == "skill_level":
            skill_id = req.get("skill")
            if isinstance(skill_id, str):
                min_level = req.get("min_level", 1)
                skill = await repos.get_user_skill(user_id, skill_id, conn)
                if not skill or skill.level < min_level:
                    skill_name = game_data.get_skill_name(skill_id)
                    missing_messages.append(
                        f"• 技能 **{skill_name}** 等級需要達到 **{min_level}** 級"
                    )

        elif req_type == "era":
            min_era = req.get("min_era", 1)
            profile = await repos.get_pioneer_profile(user_id, conn)
            if profile and profile.current_era < min_era:
                era_config = game_data.get_era_config(min_era)
                if not era_config:
                    raise ConfigNotFoundError("era", str(min_era))
                missing_messages.append(f"• 需要達到 **{era_config.name}**")

        elif req_type == "currency":
            if req.get("currency") == "oil":
                amount = req.get("amount", 0)
                user = await user_service.get_user(user_id, connection=conn)
                if not user or user.oil_balance < amount:
                    missing_messages.append(f"• 需要擁有 **{amount:,}** 油幣")

        elif req_type == "item":
            item_id = req.get("item_id")
            if isinstance(item_id, str):
                quantity = req.get("quantity", 1)
                warehouse_item = await repos.get_warehouse_item(user_id, item_id, conn)
                if not warehouse_item or warehouse_item.quantity < quantity:
                    item_name = game_data.get_item_name(item_id)
                    if not item_name:
                        raise ConfigNotFoundError("item", item_id)
                    available = warehouse_item.quantity if warehouse_item else 0
                    missing_messages.append(
                        f"• 需要擁有 **{quantity}** 個 **{item_name}** (現有: {available})"
                    )

        elif req_type == "facility_count":
            facility_type = req.get("facility_type")
            if isinstance(facility_type, str):
                min_count = req.get("min_count", 1)
                facilities = await repos.get_user_facilities_by_type(
                    user_id, facility_type, conn
                )
                if len(facilities) < min_count:
                    facility_config = game_data.get_facility_config(facility_type)
                    if not facility_config:
                        raise ConfigNotFoundError("facility", facility_type)
                    missing_messages.append(
                        f"• 需要擁有至少 **{min_count}** 個 **{facility_config.name}**"
                    )

        elif req_type == "research_level":
            project_id = req.get("project_id")
            if isinstance(project_id, str):
                min_level = req.get("min_level", 1)
                research = await repos.get_research_level(user_id, project_id, conn)
                if not research or research.level < min_level:
                    project_config = game_data.get_research_project_config(project_id)
                    if not project_config:
                        raise ConfigNotFoundError("research_project", project_id)
                    missing_messages.append(
                        f"• 研究項目 **{project_config.name}** 等級需要達到 **{min_level}** 級"
                    )

    if missing_messages:
        raise RequirementsNotMetError(missing_messages)
