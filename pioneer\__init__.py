"""
Pioneer System - 開拓者指令系統
一個數據驅動的經濟模擬遊戲系統，採用統一規則引擎架構
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 導出主要組件
from .exceptions import (
    PioneerActionCooldownError,
    PioneerCardAlreadyAssignedError,
    PioneerError,
    PioneerFacilityNotAvailableError,
    PioneerInsufficientFundsError,
    PioneerInsufficientItemsError,
    PioneerInvalidSelectionError,
    PioneerNotFoundError,
    PioneerResearchMaxLevelError,
    PioneerValidationError,
)

__all__ = [
    "PioneerError",
    "PioneerValidationError",
    "PioneerNotFoundError",
    "PioneerInsufficientFundsError",
    "PioneerInsufficientItemsError",
    "PioneerFacilityNotAvailableError",
    "PioneerResearchMaxLevelError",
    "PioneerActionCooldownError",
    "PioneerCardAlreadyAssignedError",
    "PioneerInvalidSelectionError",
]
