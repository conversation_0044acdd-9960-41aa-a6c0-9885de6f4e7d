# gacha/services/activity_service.py
"""
This module provides functions to analyze user activity for calculating reward multipliers
and captcha chances. It is designed to be stateless and modular.
"""

from typing import Optional

from asyncpg import Connection

from database.postgresql.async_manager import get_pool

# Settings are now loaded within the functions to ensure they are read after app initialization.


async def get_captcha_chance(
    user_id: int, connection: Optional[Connection] = None
) -> float:
    """
    檢查是否需要觸發驗證碼（僅基於精準定時檢測）。
    """
    should_release = False
    conn = connection
    if conn is None:
        pool = get_pool()
        conn = await pool.acquire()
        should_release = True

    if conn is None:
        return 0.0  # Failsafe

    try:
        # 只檢查精確定時的自動化行為
        return await _check_precise_timing_pattern(user_id, conn)

    finally:
        if should_release and conn:
            await conn.close()


async def _check_precise_timing_pattern(user_id: int, connection: Connection) -> float:
    """
    檢查用戶是否有精確定時的自動化行為模式

    檢測邏輯：
    - 查看最近的指定指令使用記錄
    - 如果連續多次指令都在精確時間間隔內，觸發驗證碼

    Args:
        user_id: 用戶ID
        connection: 數據庫連接

    Returns:
        float: 驗證碼觸發機率 (0.0-1.0)，如果檢測到精確定時模式則返回 1.0
    """
    try:
        # 載入精確定時檢測設定
        from config.app_config import get_gacha_core_settings

        activity_settings = get_gacha_core_settings().activity_monitoring
        timing_settings = activity_settings.precise_timing_detection

        # 如果功能被禁用，直接返回
        if not timing_settings.enabled:
            return 0.0

        # 計算需要查詢的記錄數量（間隔數 + 1）
        required_records = timing_settings.consecutive_required + 1
        query_limit = required_records + 1  # 多查一條以防萬一

        # 查詢最近的指定指令記錄
        query = """
            SELECT used_at
            FROM command_usage_stats
            WHERE user_id = $1 AND command_name = $2 AND success = true
            ORDER BY used_at DESC
            LIMIT $3;
        """
        records = await connection.fetch(
            query, user_id, timing_settings.command_name, query_limit
        )

        if len(records) < required_records:
            return 0.0

        # 將時間戳轉換為秒數並計算間隔
        timestamps = [record["used_at"].timestamp() for record in records]
        timestamps.reverse()  # 按時間順序排列（最早的在前）

        intervals = []
        for i in range(1, len(timestamps)):
            interval = timestamps[i] - timestamps[i - 1]
            intervals.append(interval)

        # 檢查是否有連續的精確間隔
        target_interval = timing_settings.target_interval
        tolerance = timing_settings.tolerance

        # 檢查所有可能的連續子序列
        max_consecutive = 0
        current_consecutive = 0

        for interval in intervals:
            if abs(interval - target_interval) <= tolerance:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        # 如果達到連續精確間隔的要求，觸發驗證碼
        if max_consecutive >= timing_settings.consecutive_required:
            from utils.logger import logger

            # 找出連續精確間隔的位置，用於日誌記錄
            precise_sequence = []
            current_seq = []

            for _, interval in enumerate(intervals):
                if abs(interval - target_interval) <= tolerance:
                    current_seq.append(f"{interval:.1f}s")
                else:
                    if len(current_seq) >= timing_settings.consecutive_required:
                        precise_sequence = current_seq
                    current_seq = []

            # 檢查最後一個序列
            if len(current_seq) >= timing_settings.consecutive_required:
                precise_sequence = current_seq

            logger.warning(
                f"檢測到用戶 {user_id} 有精確定時模式 ({timing_settings.command_name}): "
                f"連續精確間隔 {precise_sequence}, "
                f"目標間隔: {target_interval}±{tolerance}s"
            )
            return 1.0

        return 0.0

    except Exception as e:
        from utils.logger import logger

        logger.error(f"檢查精確定時模式時發生錯誤: {e}")
        return 0.0


async def is_user_in_trading_blacklist(user_id: int) -> bool:
    """
    檢查用戶是否在交易黑名單中（僅檢查 trading_blacklist）。
    用於檢查用戶是否可以使用交易相關指令或參與交易。

    Args:
        user_id: 用戶ID

    Returns:
        bool: 如果用戶在交易黑名單中返回 True，否則返回 False
    """
    try:
        from config.app_config import get_gacha_core_settings

        activity_settings = get_gacha_core_settings().activity_monitoring
        trading_blacklist_set = set(activity_settings.trading_blacklist)

        return user_id in trading_blacklist_set
    except Exception as e:
        from utils.logger import logger

        logger.error(f"檢查交易黑名單時發生錯誤: {e}")
        return False  # 發生錯誤時默認不在黑名單中


async def is_user_in_any_blacklist(user_id: int) -> bool:
    """
    檢查用戶是否在任一黑名單中（trading_blacklist 或 global_blacklist）。
    用於檢查目標用戶是否可以參與交易。

    Args:
        user_id: 用戶ID

    Returns:
        bool: 如果用戶在任一黑名單中返回 True，否則返回 False
    """
    try:
        from config.app_config import get_gacha_core_settings

        activity_settings = get_gacha_core_settings().activity_monitoring

        # 檢查交易黑名單
        trading_blacklist_set = set(activity_settings.trading_blacklist)
        if user_id in trading_blacklist_set:
            return True

        # 檢查全域黑名單
        global_blacklist_set = set(activity_settings.global_blacklist)
        if user_id in global_blacklist_set:
            return True

        return False
    except Exception as e:
        from utils.logger import logger

        logger.error(f"檢查黑名單時發生錯誤: {e}")
        return False
