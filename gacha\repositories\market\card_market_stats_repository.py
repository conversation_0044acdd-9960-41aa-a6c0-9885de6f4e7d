"""
卡片市場統計存儲庫模組 - 處理卡片市場統計數據的模組級函數
"""

from decimal import Decimal
from typing import List, Optional, Tuple

import asyncpg

from utils.logger import logger


async def get_max_stat_value(
    conn: asyncpg.Connection,
    column_name: str,
    table_name: str = "gacha_card_market_stats",
) -> Optional[Decimal]:
    """獲取指定表和列的最大值"""
    query = f"SELECT MAX({column_name}) FROM {table_name}"
    db_max_value = await conn.fetchval(query)
    if db_max_value is not None:
        return Decimal(str(db_max_value))
    return None


async def get_all_card_stats_for_supply_demand(
    conn: asyncpg.Connection,
) -> List[asyncpg.Record]:
    """獲取所有卡牌的市場統計數據，用於計算供需調節因子"""
    query = """
        SELECT
            card_id,
            total_owned_quantity,
            unique_owner_count,
            wishlist_count,
            favorite_count,
            supply_demand_modifier AS old_modifier
        FROM gacha_card_market_stats
    """
    records = await conn.fetch(query)
    return records


async def batch_update_supply_demand_modifiers(
    conn: asyncpg.Connection, updates: List[Tuple[Decimal, Decimal, int]]
) -> None:
    """批量更新卡牌的供需調節因子（帶死鎖防護）"""
    if not updates:
        return

    # 【解決死鎖的關鍵】按 card_id (第三個元素) 排序以確保所有事務以相同順序鎖定行
    sorted_updates = sorted(updates, key=lambda x: x[2])

    update_query = """
            UPDATE gacha_card_market_stats
            SET supply_demand_modifier = $1,
                previous_sd_modifier = $2,
                last_sd_calculated_at = CURRENT_TIMESTAMP
            WHERE card_id = $3
        """
    try:
        await conn.executemany(update_query, sorted_updates)
        logger.info("已成功為 %s 張卡片批量更新供需調節因子。", len(sorted_updates))
    except Exception as e:
        logger.error("批量更新供需調節因子時出錯: %s", e, exc_info=True)
        raise
