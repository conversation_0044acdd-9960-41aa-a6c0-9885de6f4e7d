# auxiliary/cogs/qa_cog.py
import logging
from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from utils.base_modal import BaseModal

# 導入新的 service 層模組
from ..exceptions import AuxiliaryError
from ..services.ai_core import message_handler
from ..services.qa_system.qa_logic import QASystem

logger = logging.getLogger(__name__)


# 在類外部定義上下文菜單命令 - AI問答
@app_commands.context_menu(name="AI問答")
@app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
@app_commands.allowed_installs(guilds=True, users=True)
@app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
async def ask_question_context(
    interaction: discord.Interaction, message: discord.Message
):
    """消息上下文菜單 - AI問答功能"""
    # 類型斷言，確保 client 是 Bot 類型
    from discord.ext import commands

    if isinstance(interaction.client, (commands.Bot, commands.AutoShardedBot)):
        cog = interaction.client.get_cog("QACog")
    else:
        cog = None
    if not cog:
        raise AuxiliaryError("AI 問答服務暫時不可用。")

    image_attachment = None
    image_url = None
    if message.attachments:
        for attachment in message.attachments:
            if attachment.content_type and attachment.content_type.startswith("image/"):
                image_attachment = attachment
                break

    if not image_attachment and message.embeds:
        for embed in message.embeds:
            if embed.image and embed.image.url:
                image_url = embed.image.url
                break

    question = message.content.strip() if message.content else None

    if not image_attachment and not image_url and not question:
        raise AuxiliaryError("所選消息不包含文字內容或圖片。")

    await cog._ask_question_command(  # type: ignore
        interaction, question=question, image=image_attachment, url=image_url
    )


class QAModal(BaseModal, title="向AI提問"):
    def __init__(
        self,
        cog: "QACog",
        original_interaction: discord.Interaction,
        attached_image: Optional[discord.Attachment] = None,
        image_url: Optional[str] = None,
    ):
        super().__init__(bot=cog.bot, title="向AI提問", timeout=300)
        self.cog = cog
        self.original_interaction = original_interaction
        self.attached_image = attached_image
        self.image_url = image_url

        self.question_input = discord.ui.TextInput(
            label="您的問題",
            style=discord.TextStyle.paragraph,
            placeholder="請在此輸入您的問題...",
            required=True,
            max_length=1500,
        )
        self.add_item(self.question_input)

    async def on_submit(self, interaction: discord.Interaction):
        question = self.question_input.value
        await self.cog._ask_question_command(
            interaction,
            question=question,
            image=self.attached_image,
            url=self.image_url,
        )


class QACog(commands.Cog, name="QACog"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.qa_system = QASystem()
        self.bot.tree.add_command(ask_question_context)
        logger.info("QACog 初始化並註冊上下文菜單完成")

    async def cog_unload(self):
        """COG 卸載時清理資源"""
        self.bot.tree.remove_command(
            ask_question_context.name, type=ask_question_context.type
        )
        logger.info("✅ AI問答 Context Menu 已移除")

    async def _ask_question_command(
        self,
        interaction: discord.Interaction,
        question: Optional[str] = None,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
        use_modal: Optional[bool] = False,
    ):
        """處理問答的內部命令邏輯"""
        if use_modal:
            qa_modal = QAModal(
                cog=self,
                original_interaction=interaction,
                attached_image=image,
                image_url=url,
            )
            await interaction.response.send_modal(qa_modal)
            return

        if not question and not image and not url:
            raise AuxiliaryError("請提供問題或圖片。或使用 `use_modal:True`。")

        processing_message_text = "💬 正在處理您的問題..."
        if (image or url) and question:
            processing_message_text = f"🖼️🔍 正在分析圖片並思考問題: {question[:30]}..."
        elif image or url:
            processing_message_text = "🖼️ 正在分析圖片..."
        elif question:
            processing_message_text = f"💬 正在思考問題: {question[:30]}..."

        await message_handler.handle_interaction(
            interaction=interaction,
            processing_message=processing_message_text,
            processor_func=self.qa_system.process_request_from_handler,
            image=image,
            url=url,
            user_prompt=question,
        )

    @app_commands.command(name="ask", description="向AI提問（可附帶圖片）")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        question="您的問題",
        image="附上一張圖片以進行分析",
        use_modal="使用彈出式視窗來輸入較長的問題",
    )
    async def ask_slash_command(
        self,
        interaction: discord.Interaction,
        question: Optional[str] = None,
        image: Optional[discord.Attachment] = None,
        use_modal: Optional[bool] = False,
    ):
        """斜線命令的入口點"""
        await self._ask_question_command(
            interaction, question=question, image=image, use_modal=use_modal
        )


async def setup(bot: commands.Bot):
    await bot.add_cog(QACog(bot))
