"""
Gacha系統收藏視圖包
提供卡冊和系列收集的視圖組件
"""

# from .collection_data_manager import CollectionDataManager  # 模組不存在，暫時註釋

# from gacha.views.utils import get_completion_indicator  # Currently unused
from .base_pagination import BasePaginationJumpModal, BasePaginationView
from .button_factory import UnifiedButtonFactory
from .card_view import CollectionView
from .enhance_confirm_view import EnhanceConfirmView
from .series_view import SeriesListView
from .sort_position_modal import SortPositionModal

__all__ = [
    "BasePaginationView",
    "BasePaginationJumpModal",
    "CollectionView",
    "SeriesListView",
    "EnhanceConfirmView",
    "SortPositionModal",
    # "CollectionDataManager",  # 模組不存在，暫時註釋
    "UnifiedButtonFactory",
]
