# 数据库模块设计文档

## 数据库连接管理

本项目使用模組級的連接管理統一管理所有数据库连接，所有系统（GACHA、RPG等）都使用 asyncpg 连接池。

### 模組級連接管理

使用 `database.postgresql.async_manager` 模組提供統一的連接管理：

```python
from database.postgresql.async_manager import setup_connections, close_connections, pool, redis_client

# 初始化連接
await setup_connections()

# 使用全局連接池
async with pool.acquire() as conn:
    result = await conn.fetchrow("SELECT * FROM users WHERE id = $1", user_id)

# 關閉連接
await close_connections()
```

### UnitOfWork

`UnitOfWork`类同样实现了基于系统类型的单例模式，确保每个系统有独立的工作单元实例：

```python
class UnitOfWork:
    """统一工作单元模式实现，提供一致的事务管理机制"""
    
    _instances = {}  # 系统类型 -> 实例的映射
    _instance_lock = threading.Lock()
    
    def __new__(cls, db_manager=None):
        """确保每个系统类型有一个单独的实例"""
        system_type = getattr(db_manager, 'system_type', 'DEFAULT') if db_manager else 'DEFAULT'
        
        with cls._instance_lock:
            if system_type not in cls._instances:
                instance = super(UnitOfWork, cls).__new__(cls)
                instance._initialized = False
                cls._instances[system_type] = instance
                logger.info(f"创建系统类型 [{system_type}] 的UnitOfWork实例")
            
            return cls._instances[system_type]
```

## 統一連接池的優勢

使用模組級連接管理有以下優勢：

1. **資源統一管理**：所有系統共享同一個連接池，避免連接數超限。

2. **簡化配置**：只需要配置一個連接池，減少配置複雜度。

3. **更好的性能**：asyncpg 提供更好的異步性能。

4. **統一的錯誤處理**：所有數據庫操作使用相同的錯誤處理機制。

5. **簡化架構**：移除了類的複雜性，直接使用模組級變量。

## 使用示例

```python
# 初始化連接池（通常在應用啟動時）
from database.postgresql.async_manager import setup_connections, pool
await setup_connections()

# 在 Repository 中使用
class UserRepository(BaseRepository):
    def __init__(self):
        super().__init__()  # 自動使用全局 pool

    async def get_user(self, user_id: int):
        async with self.pool.acquire() as conn:
            return await conn.fetchrow(
                "SELECT * FROM users WHERE id = $1", user_id
            )

# 直接使用
user_repo = UserRepository()
```

這種設計確保了所有系統使用統一的數據庫連接管理，提高了資源利用效率和系統穩定性，同時簡化了代碼結構。