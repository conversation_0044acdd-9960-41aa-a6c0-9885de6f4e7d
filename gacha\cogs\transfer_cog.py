# gacha/cogs/transfer_cog.py
from __future__ import annotations

from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import TYPE_CHECKING, Union

import asyncpg
import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from config.app_config import get_config
from database.postgresql.async_manager import get_pool
from gacha.exceptions import CommandBlacklistError, TradeValidationError
from gacha.services import user_service, validation_service
from gacha.services.activity_service import (
    is_user_in_any_blacklist,
    is_user_in_trading_blacklist,
)
from gacha.views.ui_components.confirmation import ConfirmationView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

from .decorators import account_age_check


class TransferCog(commands.Cog, name="轉錢功能"):
    """處理玩家間轉錢指令，內置所有邏輯"""

    def __init__(self, bot: BotType):
        self.bot = bot
        # 獲取轉錢手續費率（與交易手續費一致）
        self.transfer_fee_percentage = Decimal(
            str(get_config("gacha_core_settings.trade_fee_percentage", 0.05))
        )
        logger.info("TransferCog initialized.")

    async def cog_load(self):
        logger.info("TransferCog has been loaded.")

    async def _ensure_user_exists(self, user_id: int, nickname: str) -> None:
        """確保用戶存在，如果不存在則創建"""
        # 使用 validation_service 來處理用戶創建
        await validation_service.ensure_user_exists(
            user_id, nickname=nickname, create_if_missing=True
        )

        # 使用 user_service 來更新暱稱
        await user_service.ensure_nickname_updated(user_id, nickname)

    async def _transfer_money(
        self, sender_id: int, receiver_id: int, amount: int
    ) -> dict:
        """執行轉錢操作的核心邏輯，並記錄交易歷史"""
        pool = get_pool()
        try:
            if pool is None:
                raise RuntimeError("Database pool is not initialized")
            async with pool.acquire() as conn:
                async with conn.transaction():
                    # 計算手續費
                    decimal_amount = Decimal(str(amount))
                    fee_paid_decimal = decimal_amount * self.transfer_fee_percentage
                    fee_paid = int(fee_paid_decimal.quantize(Decimal("1")))
                    net_amount_to_receiver = amount - fee_paid

                    # 檢查發送者餘額並扣款（扣除全額）
                    sender_new_balance = await user_service.award_balance(
                        user_id=sender_id,
                        amount=-amount,
                        transaction_type="transfer_send",
                        reason=f"Transfer {amount:,} oil to user {receiver_id}",
                        connection=conn,
                    )

                    # 給接收者加錢（扣除手續費後的金額）
                    receiver_new_balance = await user_service.award_balance(
                        user_id=receiver_id,
                        amount=net_amount_to_receiver,
                        transaction_type="transfer_receive",
                        reason=f"Receive {net_amount_to_receiver:,} oil from user {sender_id} (fee: {fee_paid:,})",
                        connection=conn,
                    )

                    # 記錄交易歷史
                    await conn.execute(
                        """
                        INSERT INTO user_transaction_history (
                            sender_id, receiver_id, transaction_type,
                            gross_amount, fee_amount, net_amount,
                            sender_balance_after, receiver_balance_after
                        ) VALUES ($1, $2, 'OIL_TRANSFER', $3, $4, $5, $6, $7)
                        """,
                        sender_id,
                        receiver_id,
                        amount,
                        fee_paid,
                        net_amount_to_receiver,
                        sender_new_balance,
                        receiver_new_balance,
                    )
                    logger.info(
                        "Transaction logged: %s -> %s, Amount: %s, Fee: %s",
                        sender_id,
                        receiver_id,
                        amount,
                        fee_paid,
                    )

                    logger.info(
                        "Money transfer completed: %s oil from user %s to user %s (fee: %s)",
                        amount,
                        sender_id,
                        receiver_id,
                        fee_paid,
                    )

                    return {
                        "sender_new_balance": sender_new_balance,
                        "receiver_new_balance": receiver_new_balance,
                        "amount": amount,
                        "net_amount": net_amount_to_receiver,
                        "fee": fee_paid,
                    }
        except asyncpg.CheckViolationError as e:
            # 捕獲資料庫層級的檢查約束失敗（例如，餘額不足）
            logger.warning("轉錢失敗，觸發資料庫約束: %s", e)
            # 將其轉換為對用戶友好的業務邏輯錯誤
            raise TradeValidationError("餘額不足！請檢查您的油錢。") from e

    async def _execute_transfer(
        self,
        interaction: discord.Interaction,
        user: discord.User,
        amount: int,
        sender_nickname: str,
        receiver_nickname: str,
    ):
        """執行實際的轉錢操作並發送成功訊息"""
        # 執行轉錢操作
        transfer_result = await self._transfer_money(
            interaction.user.id, user.id, amount
        )
        from config.app_config import get_oil_emoji

        oil_emoji = get_oil_emoji()

        # 創建成功的嵌入式訊息
        embed = SuccessEmbed(
            title="💸 轉錢成功",
            description=f"{interaction.user.mention} 成功轉錢給 {user.mention}",
            timestamp=datetime.now(),
        )

        embed.add_field(name="轉錢金額", value=f"`{amount}` {oil_emoji}", inline=True)
        embed.add_field(
            name="手續費 (3%)",
            value=f"`{transfer_result['fee']}` {oil_emoji}",
            inline=True,
        )
        embed.add_field(
            name="實際到帳",
            value=f"`{transfer_result['net_amount']}` {oil_emoji}",
            inline=True,
        )
        embed.add_field(
            name=f"{sender_nickname} 餘額",
            value=f"`{transfer_result['sender_new_balance']}` {oil_emoji}",
            inline=True,
        )
        embed.add_field(
            name=f"{receiver_nickname} 餘額",
            value=f"`{transfer_result['receiver_new_balance']}` {oil_emoji}",
            inline=True,
        )
        embed.add_field(name="\u200b", value="\u200b", inline=True)  # 空白欄位對齊

        embed.set_thumbnail(url="https://cdn.dev.conquest.bot/thumbnails/transfer.png")
        embed.set_author(
            name=f"{sender_nickname} → {receiver_nickname}",
            icon_url=(
                interaction.user.display_avatar.url
                if interaction.user.display_avatar
                else None
            ),
        )
        embed.set_footer(text="轉錢操作已完成（已扣除手續費並記錄）")

        await interaction.response.edit_message(embed=embed, view=None)

    @app_commands.command(
        name="transfer", description="轉錢給其他玩家 (需支付3%手續費)"
    )
    @account_age_check()
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        user="要轉錢的目標玩家",
        amount="轉錢金額（必須為正整數） - 注意：需支付3%手續費",
    )
    async def transfer(
        self, interaction: discord.Interaction, user: discord.User, amount: int
    ):
        """處理轉錢命令"""
        await interaction.response.defer(ephemeral=False)

        # 檢查發起者是否在交易黑名單中
        if await is_user_in_trading_blacklist(interaction.user.id):
            raise CommandBlacklistError("您已被限制使用 transfer 指令。")

        # 檢查接收者是否在任一黑名單中
        if await is_user_in_any_blacklist(user.id):
            raise CommandBlacklistError("目標用戶已被限制轉帳功能，無法向其轉帳。")

        # 基本參數驗證
        if amount <= 0:
            raise TradeValidationError("轉錢金額必須大於 0")
        if interaction.user.id == user.id:
            raise TradeValidationError("您不能轉錢給自己")

        # 檢查目標帳號年齡
        four_months_ago = datetime.now(timezone.utc) - timedelta(days=120)
        if user.created_at > four_months_ago:
            raise TradeValidationError(
                f"目標用戶 {user.mention} 不符合接收轉帳的資格。"
            )

        # 獲取用戶資訊
        sender_nickname = interaction.user.display_name
        receiver_nickname = user.display_name

        # 確保雙方用戶都存在
        await self._ensure_user_exists(interaction.user.id, sender_nickname)
        await self._ensure_user_exists(user.id, receiver_nickname)

        # 獲取發送者當前餘額並檢查
        current_balance = (await user_service.get_balance(interaction.user.id))[
            "balance"
        ]

        if current_balance < amount:
            raise TradeValidationError(
                f"餘額不足：需要 {amount:,}，目前 {current_balance:,}"
            )

        # 計算手續費和實際到帳金額
        decimal_amount = Decimal(str(amount))
        fee = decimal_amount * self.transfer_fee_percentage
        fee = fee.quantize(Decimal("1"))
        net_amount = decimal_amount - fee

        from config.app_config import get_oil_emoji

        oil_emoji = get_oil_emoji()

        # 創建確認回調函數
        async def confirm_transfer_callback(
            confirm_interaction: discord.Interaction, is_confirmed: bool
        ):
            if is_confirmed:
                await self._execute_transfer(
                    confirm_interaction,
                    user,
                    amount,
                    sender_nickname,
                    receiver_nickname,
                )
            else:
                cancel_embed = discord.Embed(
                    title="❌ 轉錢已取消",
                    description="您已取消轉錢操作，沒有任何金額被轉出。",
                    color=discord.Color.blue(),
                )
                await confirm_interaction.response.edit_message(
                    embed=cancel_embed, view=None
                )

        # 創建確認訊息
        field_value = (
            f"**轉錢金額：** `{amount:,}` {oil_emoji}\n"
            f"**手續費 (3%)：** `{int(fee):,}` {oil_emoji}\n"
            f"**實際到帳：** `{int(net_amount):,}` {oil_emoji}\n"
            f"**您的當前餘額：** `{current_balance:,}` {oil_emoji}\n"
            f"**轉錢後餘額：** `{current_balance - amount:,}` {oil_emoji}"
        )

        # 創建確認嵌入
        embed = discord.Embed(
            title="💸 確認轉錢",
            description=f"您確定要轉錢給 {user.mention} 嗎？",
            color=discord.Color.orange(),
        )
        embed.add_field(name="轉錢詳情", value=field_value, inline=False)
        embed.set_footer(text="請仔細確認轉錢資訊後再操作。")

        # 創建確認視圖
        view = ConfirmationView(
            bot=self.bot,
            user_id=interaction.user.id,
            on_confirm=lambda confirm_interaction: confirm_transfer_callback(
                confirm_interaction, True
            ),
            on_cancel=lambda cancel_interaction: confirm_transfer_callback(
                cancel_interaction, False
            ),
            confirm_label="確認轉錢",
            cancel_label="取消",
            confirm_style=discord.ButtonStyle.success,
            cancel_style=discord.ButtonStyle.secondary,
        )

        await interaction.followup.send(embed=embed, view=view, ephemeral=False)


async def setup(bot: BotType):
    """載入 TransferCog"""
    await bot.add_cog(TransferCog(bot))
    logger.info("TransferCog has been added to the bot.")
