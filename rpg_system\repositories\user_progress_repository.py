"""
RPG用戶進度存儲庫
負責管理用戶在RPG PVE模式下的進度數據
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

import asyncpg

from gacha.exceptions import DatabaseOperationError, EntityNotFoundError
from gacha.repositories import _base_repo
from rpg_system.exceptions import UserProgressNotFoundError
from utils.logger import logger

TABLE_NAME = "rpg_user_progress"


async def get_user_progress(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """
    獲取用戶進度信息

    Args:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    Returns:
        用戶進度數據字典

    Raises:
        UserProgressNotFoundError: 用戶進度未找到
    """
    query = f"""
        SELECT
            user_id,
            current_floor_unlocked,
            current_floor_wins,
            max_floor_cleared,
            current_team_formation,
            created_at,
            updated_at
        FROM {TABLE_NAME}
        WHERE user_id = $1
    """

    try:
        row = await _base_repo.fetch_one(query, (user_id,), connection=connection)
        if not row:
            raise UserProgressNotFoundError(user_id)
        result = dict(row)
        # asyncpg 自動處理 JSONB 解碼，無需手動解析
        return result
    except UserProgressNotFoundError:
        raise
    except Exception as e:
        logger.error("獲取用戶進度失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶進度失敗: {str(e)}") from e


async def create_user_progress(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """
    創建新用戶的進度記錄

    Args:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    Returns:
        創建的用戶進度數據
    """
    query = f"""
        INSERT INTO {TABLE_NAME} (
            user_id,
            current_floor_unlocked,
            current_floor_wins,
            max_floor_cleared,
            current_team_formation
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING
            user_id,
            current_floor_unlocked,
            current_floor_wins,
            max_floor_cleared,
            current_team_formation,
            created_at,
            updated_at
    """

    try:
        row = await _base_repo.fetch_one(
            query,
            (
                user_id,
                1,
                0,
                0,
                None,
            ),  # 默認值：樓層1解鎖，0勝利，0最高通關，無隊伍配置
            connection=connection,
        )
        if row is None:
            raise RuntimeError(f"Failed to create user progress for user_id: {user_id}")
        result: Dict[str, Any] = dict(row)
        result["current_team_formation"] = None  # JSON字段初始為None
        logger.info("創建用戶進度成功: user_id=%s", user_id)
        return result
    except Exception as e:
        logger.error("創建用戶進度失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise


async def get_or_create_user_progress(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """
    獲取或創建用戶進度

    Args:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    Returns:
        用戶進度數據
    """
    try:
        return await get_user_progress(user_id, connection)
    except UserProgressNotFoundError:
        return await create_user_progress(user_id, connection)


async def update_floor_progress(
    user_id: int,
    floor_unlocked: Optional[int] = None,
    floor_wins: Optional[int] = None,
    max_floor_cleared: Optional[int] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """
    更新用戶樓層進度（純異常模式）

    Args:
        user_id: 用戶ID
        floor_unlocked: 新的解鎖樓層（可選）
        floor_wins: 新的勝利次數（可選）
        max_floor_cleared: 新的最高通關樓層（可選）
        connection: 可選的數據庫連接

    Raises:
        ValueError: 沒有提供任何更新字段
        EntityNotFoundError: 用戶記錄不存在
        DatabaseOperationError: 數據庫操作失敗
    """
    # 構建動態更新查詢
    update_fields = []
    params = []
    param_index = 1

    if floor_unlocked is not None:
        update_fields.append(f"current_floor_unlocked = ${param_index}")
        params.append(floor_unlocked)
        param_index += 1

    if floor_wins is not None:
        update_fields.append(f"current_floor_wins = ${param_index}")
        params.append(floor_wins)
        param_index += 1

    if max_floor_cleared is not None:
        update_fields.append(f"max_floor_cleared = ${param_index}")
        params.append(max_floor_cleared)
        param_index += 1

    if not update_fields:
        logger.warning("更新樓層進度時沒有提供任何字段: user_id=%s", user_id)
        raise ValueError("沒有提供任何更新字段")

    # 添加updated_at字段
    update_fields.append(f"updated_at = ${param_index}")
    params.append(datetime.utcnow())
    param_index += 1

    # 添加user_id參數
    params.append(user_id)

    query = f"""
        UPDATE {TABLE_NAME}
        SET {", ".join(update_fields)}
        WHERE user_id = ${param_index}
    """

    try:
        result = await _base_repo.execute_query(query, params, connection=connection)
        if result != 1:
            logger.warning("更新樓層進度失敗，用戶可能不存在: user_id=%s", user_id)
            raise EntityNotFoundError(f"找不到用戶記錄: {user_id}")
        logger.info("更新樓層進度成功: user_id=%s", user_id)
    except (ValueError, EntityNotFoundError):
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error("更新樓層進度失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(
            f"更新樓層進度失敗: {str(e)}", original_exception=e
        ) from e


async def get_team_formation(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[int]:
    """
    獲取用戶隊伍配置

    Args:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    Returns:
        隊伍配置（卡牌收藏ID列表），如果沒有配置則為空列表
    """
    try:
        progress = await get_user_progress(user_id, connection)
        formation_data = progress.get("current_team_formation")

        if not formation_data:
            return []

        # 支援兩種格式：簡單列表 [123, 456] 或複雜對象格式
        if isinstance(formation_data, list):
            return formation_data
        elif isinstance(formation_data, dict) and "slots" in formation_data:
            # 複雜格式：提取card_collection_id
            return [
                slot.get("card_collection_id")
                for slot in formation_data.get("slots", [])
                if slot.get("card_collection_id")
            ]
        else:
            logger.warning(
                "未知的隊伍配置格式: user_id=%s, data=%s",
                user_id,
                formation_data,
            )
            return []
    except UserProgressNotFoundError:
        return []  # 如果用戶進度不存在，返回空列表
    except Exception as e:
        logger.error("獲取隊伍配置失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise


async def set_team_formation(
    user_id: int,
    card_collection_ids: List[int],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """
    設置用戶隊伍配置（純異常模式）

    Args:
        user_id: 用戶ID
        card_collection_ids: 卡牌收藏ID列表
        connection: 可選的數據庫連接

    Raises:
        EntityNotFoundError: 用戶記錄不存在
        DatabaseOperationError: 數據庫操作失敗
    """
    # 使用簡單列表格式存儲，讓 asyncpg 自動處理 JSONB 編碼

    query = f"""
        UPDATE {TABLE_NAME}
        SET current_team_formation = $1, updated_at = $2
        WHERE user_id = $3
    """

    try:
        result = await _base_repo.execute_query(
            query,
            (card_collection_ids, datetime.utcnow(), user_id),
            connection=connection,
        )
        if result != 1:
            logger.warning("設置隊伍配置失敗，用戶可能不存在: user_id=%s", user_id)
            raise EntityNotFoundError(f"找不到用戶記錄: {user_id}")
        logger.info(
            "設置隊伍配置成功: user_id=%s, formation=%s",
            user_id,
            card_collection_ids,
        )
    except EntityNotFoundError:
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error("設置隊伍配置失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(
            f"設置隊伍配置失敗: {str(e)}", original_exception=e
        ) from e


async def clear_team_formation(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """
    清空用戶隊伍配置（純異常模式）

    Args:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    Raises:
        EntityNotFoundError: 用戶記錄不存在
        DatabaseOperationError: 數據庫操作失敗
    """
    await set_team_formation(user_id, [], connection)
