# gacha/repositories/card/card_encyclopedia_repository.py
"""卡片圖鑑存儲庫，提供卡片圖鑑相關查詢功能"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg

from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    DatabaseOperationError,
    EntityNotFoundError,
)
from gacha.repositories._base_repo import execute_query, fetch_one, fetch_value
from utils.logger import logger

# --- 表名常量 ---
MASTER_CARD_TABLE = "gacha_master_cards"
HIGHEST_STAR_TABLE = "gacha_card_highest_star"
CARD_DESCRIPTIONS_TABLE = "gacha_card_descriptions"
USER_TABLE = "gacha_users"


async def update_highest_star(
    card_id: int,
    user_id: int,
    star_level: int,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新或插入卡片最高星級記錄。
    如果提供了 connection，則在該連接上執行，否則創建新事務。

    Raises:
        DatabaseOperationError: 當數據庫操作失敗時
    """

    async def _update_logic(conn: asyncpg.Connection) -> None:
        try:
            # 在事務中檢查當前記錄並鎖定
            check_query = f"SELECT user_id, star_level FROM {HIGHEST_STAR_TABLE} WHERE card_id = $1 FOR UPDATE"
            current_record = await conn.fetchrow(check_query, card_id)
            now = datetime.now()

            if current_record:
                current_star = current_record.get("star_level", 0)
                if star_level > current_star:
                    update_query = f"""
                        UPDATE {HIGHEST_STAR_TABLE}
                        SET user_id = $1, star_level = $2, achieved_at = $3
                        WHERE card_id = $4
                    """
                    await conn.execute(update_query, user_id, star_level, now, card_id)
            else:
                # 記錄不存在，創建新記錄
                insert_query = f"""
                    INSERT INTO {HIGHEST_STAR_TABLE} (card_id, user_id, star_level, achieved_at)
                    VALUES ($1, $2, $3, $4)
                """
                await conn.execute(insert_query, card_id, user_id, star_level, now)

        except asyncpg.PostgresError as e:
            logger.error(
                f"Database error in update_highest_star for card_id {card_id}, user_id {user_id}: {str(e)}",
                exc_info=True,
            )
            raise DatabaseOperationError(
                f"更新卡片 {card_id} 最高星級失敗: {str(e)}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error in update_highest_star for card_id {card_id}, user_id {user_id}: {str(e)}",
                exc_info=True,
            )
            raise DatabaseOperationError(
                f"更新卡片 {card_id} 最高星級時發生未知錯誤: {str(e)}"
            ) from e

    if connection:
        await _update_logic(connection)
    else:
        pool = get_pool()
        async with pool.acquire() as conn:
            async with conn.transaction():
                await _update_logic(conn)


async def update_card_description(
    user_id: int,
    card_id: int,
    description: str,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """更新或插入卡片自訂描述 (使用 UPSERT 邏輯)

    Raises:
        DatabaseOperationError: If the database operation fails.
    """
    now = datetime.now()
    upsert_query = f"""
        INSERT INTO {CARD_DESCRIPTIONS_TABLE} (card_id, user_id, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (card_id, user_id) DO UPDATE SET
            description = EXCLUDED.description,
            updated_at = EXCLUDED.updated_at;
    """
    try:
        await execute_query(
            upsert_query,
            [card_id, user_id, description, now, now],
            connection=connection,
        )
    except DatabaseOperationError as e:
        logger.error(
            f"Database error in update_card_description for card_id {card_id}, user_id {user_id}: {str(e)}",
            exc_info=True,
        )
        raise DatabaseOperationError(f"更新卡片 {card_id} 描述失敗: {str(e)}") from e
    except Exception as e:
        logger.error(
            f"Unexpected error in update_card_description for card_id {card_id}, user_id {user_id}: {str(e)}",
            exc_info=True,
        )
        raise DatabaseOperationError(
            f"更新卡片 {card_id} 描述時發生未知錯誤: {str(e)}"
        ) from e


async def get_encyclopedia_extra_data(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """獲取圖鑑額外數據 - 只查詢額外字段（最高星級、用戶暱稱、自定義描述）"""
    query = f"""
        SELECT
            hs.user_id as highest_star_user_id,
            hs.star_level as highest_star_level,
            hs.achieved_at,
            gu.nickname,
            cd.description as custom_description,
            cd.user_id as description_user_id
        FROM {HIGHEST_STAR_TABLE} hs
        LEFT JOIN {USER_TABLE} gu ON hs.user_id = gu.user_id
        LEFT JOIN {CARD_DESCRIPTIONS_TABLE} cd ON hs.card_id = cd.card_id AND cd.user_id = hs.user_id
        WHERE hs.card_id = $1
    """
    try:
        result = await fetch_one(query, [card_id], connection=connection)
        return dict(result) if result else None
    except DatabaseOperationError as e:
        logger.error(
            f"Database error in get_encyclopedia_extra_data for card_id {card_id}: {str(e)}",
            exc_info=True,
        )
        raise DatabaseOperationError(
            f"獲取卡片 {card_id} 圖鑑額外數據失敗: {str(e)}"
        ) from e
    except Exception as e:
        logger.error(
            f"Unexpected error in get_encyclopedia_extra_data for card_id {card_id}: {str(e)}",
            exc_info=True,
        )
        raise DatabaseOperationError(
            f"獲取卡片 {card_id} 圖鑑額外數據時發生未知錯誤: {str(e)}"
        ) from e


def _build_filter_conditions(
    pool_type: Optional[str],
    rarity: Optional[Union[int, List[int]]],
    series_name: Optional[str],
    card_name: Optional[str],
    card_id: Optional[int],
) -> Tuple[List[str], List[Any], int]:
    """構建篩選條件"""
    filter_conditions = []
    filter_params, param_idx = [], 1

    if pool_type:
        filter_conditions.append(f"mc.pool_type = ${param_idx}")
        filter_params.append(pool_type)
        param_idx += 1
    if rarity is not None:
        if isinstance(rarity, list):
            if rarity:
                filter_conditions.append(f"mc.rarity = ANY(${param_idx}::integer[])")
                filter_params.append(rarity)
                param_idx += 1
        else:
            filter_conditions.append(f"mc.rarity = ${param_idx}")
            filter_params.append(rarity)
            param_idx += 1
    if series_name:
        filter_conditions.append(f"mc.series = ${param_idx}")
        filter_params.append(series_name)
        param_idx += 1
    if card_name and card_id is None:
        filter_conditions.append(f"mc.name ILIKE ${param_idx}")
        filter_params.append(f"%{card_name}%")
        param_idx += 1

    return filter_conditions, filter_params, param_idx


def _build_join_clauses(sort_by: str) -> str:
    """構建 JOIN 子句"""
    join_clauses = []
    if sort_by == "star":
        join_clauses.append(
            f"LEFT JOIN {HIGHEST_STAR_TABLE} hs ON mc.card_id = hs.card_id"
        )
    if sort_by in ["owner_count", "wishlist_count"]:
        join_clauses.append(
            "LEFT JOIN gacha_card_market_stats ms ON mc.card_id = ms.card_id"
        )
    return " ".join(join_clauses)


def _build_order_by_clause(sort_by: str, sort_order: str) -> str:
    """構建 ORDER BY 子句"""
    order = "DESC" if sort_order.lower() == "desc" else "ASC"
    nulls = "NULLS LAST" if order == "DESC" else "NULLS FIRST"
    tie_breaker = "ASC"  # card_id 永遠升序

    # 使用圖鑑專用的排序邏輯
    from gacha.config.sorting_config import SortingConfig

    sort_field_map = SortingConfig.get_db_field_map(use_encyclopedia=True)

    if sort_by == "star":
        return f"ORDER BY hs.star_level {order} {nulls}, mc.rarity {order}, mc.card_id {tie_breaker}"
    elif sort_by in ["owner_count", "wishlist_count"]:
        sort_column = sort_field_map.get(sort_by, "mc.rarity")
        return f"ORDER BY COALESCE({sort_column}, 0) {order}, mc.rarity {order}, mc.card_id {tie_breaker}"
    else:
        sort_column = sort_field_map.get(sort_by, "mc.rarity")
        if sort_by in ["price"]:
            return f"ORDER BY COALESCE({sort_column}, 0) {order}, mc.rarity {order}, mc.card_id {tie_breaker}"
        else:
            return f"ORDER BY {sort_column} {order}, mc.card_id {tie_breaker}"


def _build_ownership_filter(
    user_id: Optional[int], ownership_filter: str, param_start_index: int
) -> Tuple[str, str, List[Any]]:
    """構建擁有狀態篩選的 JOIN 和 WHERE 條件

    Args:
        user_id: 用戶ID
        ownership_filter: 擁有狀態篩選 ("all", "owned", "not_owned")
        param_start_index: 參數起始索引

    Returns:
        Tuple[join_clause, where_clause, params]
    """
    if ownership_filter == "all" or user_id is None:
        return "", "", []

    join_clause = "LEFT JOIN gacha_user_collections uc ON mc.card_id = uc.card_id AND uc.user_id = ${}".format(
        param_start_index + 1
    )

    if ownership_filter == "owned":
        where_clause = "uc.card_id IS NOT NULL AND uc.quantity > 0"
    elif ownership_filter == "not_owned":
        where_clause = "uc.card_id IS NULL OR uc.quantity = 0"
    else:
        # 默認情況，不篩選
        return "", "", []

    return join_clause, where_clause, [user_id]


async def get_paginated_card_id(
    page: int = 1,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    pool_type: Optional[str] = None,
    rarity: Optional[Union[int, List[int]]] = None,
    series_name: Optional[str] = None,
    card_id: Optional[int] = None,
    card_name: Optional[str] = None,
    user_id: Optional[int] = None,
    ownership_filter: str = "all",
    # 移除 seek 相關參數
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """獲取分頁的圖鑑卡片ID (優化的 OFFSET 模式)"""
    if page < 1:
        page = 1

    # 構建篩選條件和 JOIN
    filter_conditions, filter_params, _ = _build_filter_conditions(
        pool_type, rarity, series_name, card_name, card_id
    )
    where_clause_for_filters = (
        " AND ".join(filter_conditions) if filter_conditions else ""
    )
    join_clause = _build_join_clauses(sort_by)
    order_by_clause = _build_order_by_clause(sort_by, sort_order)

    try:
        return await _execute_pagination_query(
            filter_params,
            where_clause_for_filters,
            join_clause,
            order_by_clause,
            page,
            card_id,
            user_id,
            ownership_filter,
            connection,
        )
    except EntityNotFoundError:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in get_paginated_card_id: {str(e)}", exc_info=True
        )
        raise DatabaseOperationError(f"獲取分頁卡片ID時發生未知錯誤: {str(e)}") from e


async def _execute_pagination_query(
    filter_params: List[Any],
    where_clause_for_filters: str,
    join_clause: str,
    order_by_clause: str,
    page: int,
    card_id: Optional[int],
    user_id: Optional[int],
    ownership_filter: str,
    connection: Optional[asyncpg.Connection],
) -> Dict[str, Any]:
    """執行分頁查詢"""
    # 構建擁有狀態篩選的 JOIN 和 WHERE 條件
    ownership_join, ownership_where, ownership_params = _build_ownership_filter(
        user_id, ownership_filter, len(filter_params)
    )

    # 合併 JOIN 子句
    full_join_clause = f"{join_clause} {ownership_join}".strip()

    # 合併 WHERE 條件
    all_where_conditions = []
    if where_clause_for_filters:
        all_where_conditions.append(where_clause_for_filters)
    if ownership_where:
        all_where_conditions.append(ownership_where)

    full_where_clause = (
        " AND ".join(all_where_conditions) if all_where_conditions else ""
    )
    all_params = filter_params + ownership_params

    # 先計算總數
    count_query = f"SELECT COUNT(DISTINCT mc.card_id) FROM {MASTER_CARD_TABLE} mc {full_join_clause}"
    if full_where_clause:
        count_query += f" WHERE {full_where_clause}"

    total_cards = await fetch_value(count_query, all_params, connection=connection) or 0
    if total_cards == 0:
        raise EntityNotFoundError("在指定的篩選條件下找不到任何卡片。")

    # 因為每頁一張卡，所以 total_pages = total_cards
    total_pages = total_cards

    # 處理 card_id 跳轉或正常翻頁
    if card_id is not None:
        current_page, card_id_for_page = await _handle_card_id_jump(
            all_params,
            full_where_clause,
            full_join_clause,
            order_by_clause,
            card_id,
            connection,
        )
    else:
        current_page, card_id_for_page = await _handle_normal_pagination(
            all_params,
            full_where_clause,
            full_join_clause,
            order_by_clause,
            page,
            total_pages,
            connection,
        )

    return {
        "card_id": card_id_for_page,
        "current_page": current_page,
        "total_pages": total_pages,
        "total_cards": total_cards,
    }


async def _handle_card_id_jump(
    filter_params: List[Any],
    where_clause_for_filters: str,
    join_clause: str,
    order_by_clause: str,
    card_id: int,
    connection: Optional[asyncpg.Connection],
) -> Tuple[int, int]:
    """處理 card_id 跳轉"""
    rank_query_params = filter_params + [card_id]
    rank_query = f"""
        WITH RankedCards AS (
            SELECT mc.card_id, ROW_NUMBER() OVER ({order_by_clause}) as rn
            FROM {MASTER_CARD_TABLE} mc {join_clause}"""
    if where_clause_for_filters:
        rank_query += f" WHERE {where_clause_for_filters}"
    rank_query += f"""
        )
        SELECT rn FROM RankedCards WHERE card_id = ${len(rank_query_params)}::bigint;
    """
    current_page = await fetch_value(
        rank_query, rank_query_params, connection=connection
    )
    if current_page is None:
        raise EntityNotFoundError(
            f"找不到 ID 為 {card_id} 的卡片，或該卡片不符合目前的篩選條件。"
        )
    return current_page, card_id


async def _handle_normal_pagination(
    filter_params: List[Any],
    where_clause_for_filters: str,
    join_clause: str,
    order_by_clause: str,
    page: int,
    total_pages: int,
    connection: Optional[asyncpg.Connection],
) -> Tuple[int, int]:
    """處理正常翻頁"""
    current_page = max(1, min(page, total_pages))
    offset = current_page - 1
    pagination_query_params = filter_params + [offset]
    pagination_query = f"SELECT mc.card_id FROM {MASTER_CARD_TABLE} mc {join_clause}"
    if where_clause_for_filters:
        pagination_query += f" WHERE {where_clause_for_filters}"
    pagination_query += (
        f" {order_by_clause} LIMIT 1 OFFSET ${len(pagination_query_params)};"
    )
    card_id_for_page = await fetch_value(
        pagination_query, pagination_query_params, connection=connection
    )
    if card_id_for_page is None:
        raise EntityNotFoundError(
            f"在頁碼 {current_page} (總頁數: {total_pages}) 找不到卡片。"
        )
    return current_page, card_id_for_page
