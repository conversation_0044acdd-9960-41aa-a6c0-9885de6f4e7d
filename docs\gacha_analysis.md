# Gacha 系統分析報告

本文檔整理了目前 Gacha 系統的卡池機率、成本、期望值 (EV) 以及貨幣（油幣）的獲取管道。

# 卡池數據總覽

## 主卡池（Main Pool）
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R7    | 0.01%  | 55,000    | 5.50    |
| R6    | 0.05%  | 12,000    | 6.00    |
| R5    | 0.70%  | 1,500     | 10.50   |
| R4    | 1.00%  | 600       | 6.00    |
| R3    | 4.50%  | 20        | 0.90    |
| R2    | 25.00% | 6         | 1.50    |
| R1    | 68.74% | 4         | 2.75    |
| **總計** | 100% | | **33.15** |

- **抽卡成本**：（混合池用）50 油
- **總EV**：33.15 油
- **EV/成本**：66.3%

## 典藏女仆卡池（Mazoku Maid Pool）
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R5    | 0.05%  | 50,000    | 25.00   |
| R4    | 0.15%  | 17,000    | 25.50   |
| R3    | 2.00%  | 500       | 10.00   |
| R2    | 15.00% | 80        | 12.00   |
| R1    | 82.80% | 4         | 3.31    |
| **總計** | 100% | | **75.81** |

- **抽卡成本**：160 油
- **總EV**：75.81 油
- **EV/成本**：47.38%

## 情人節卡池（Valentine's Pool）
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R6    | 0.05%  | 45,000    | 22.50   |
| R5    | 0.15%  | 15,000    | 22.50   |
| R4    | 3.00%  | 1,000     | 30.00   |
| R3    | 10.00% | 70        | 7.00    |
| R2    | 25.00% | 12        | 3.00    |
| R1    | 61.80% | 3         | 1.85    |
| **總計** | 100% | | **86.85** |

- **抽卡成本**：150 油
- **總EV**：86.85 油
- **EV/成本**：57.90%

## 泳裝卡池（Summer Pool）
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R6    | 0.05%  | 40,000    | 20.00   |
| R5    | 0.15%  | 12,000    | 18.00   |
| R4    | 3.00%  | 800       | 24.00   |
| R3    | 10.00% | 50        | 5.00    |
| R2    | 25.00% | 12        | 3.00    |
| R1    | 61.80% | 3         | 1.85    |
| **總計** | 100% | | **71.85** |

- **抽卡成本**：130 油
- **總EV**：71.85 油
- **EV/成本**：55.27%

## Hololive 卡池 (Hololive Pool)
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R7    | 0.01%  | 60,000    | 6.00    |
| R6    | 0.05%  | 22,000    | 11.00   |
| R5    | 0.50%  | 7,000     | 35.00   |
| R4    | 2.00%  | 800       | 16.00   |
| R3    | 8.00%  | 80        | 6.40    |
| R2    | 25.00% | 12        | 3.00    |
| R1    | 64.44% | 4         | 2.58    |
| **總計** | 100% | | **79.98** |

- **抽卡成本**：150 油
- **總EV**：79.98 油
- **EV/成本**：53.32%

## UNION ARENA卡池 (UNION ARENA Pool)
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R7    | 0.01%  | 50,000    | 5.00    |
| R6    | 0.05%  | 20,000    | 10.00   |
| R5    | 0.50%  | 7,000     | 35.00   |
| R4    | 2.00%  | 750       | 15.00   |
| R3    | 8.00%  | 70        | 5.60    |
| R2    | 25.00% | 10        | 2.50    |
| R1    | 64.44% | 3         | 1.93    |
| **總計** | 100.00% | | **75.03** |

- **抽卡成本**：120 油
- **總EV**：75.03 油
- **EV/成本**：62.53%

## Pokemon TCG卡池 (Pokemon TCG Pool)
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R7    | 0.01%  | 35,000    | 3.50    |
| R6    | 0.05%  | 15,000    | 7.50    |
| R5    | 0.60%  | 4,000     | 24.00   |
| R4    | 2.50%  | 500       | 12.50   |
| R3    | 8.00%  | 50        | 4.00    |
| R2    | 25.00% | 8         | 2.00    |
| R1    | 63.84% | 3         | 1.92    |
| **總計** | 100.00% | | **55.42** |

- **抽卡成本**：90 油
- **總EV**：55.42 油
- **EV/成本**：61.58%

## WIXOSS卡池 (WIXOSS Pool)
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R7    | 0.01%  | 65,000    | 6.50    |
| R6    | 0.05%  | 25,000    | 12.50   |
| R5    | 0.50%  | 8,000     | 40.00   |
| R4    | 2.00%  | 1,200     | 24.00   |
| R3    | 8.00%  | 80        | 6.40    |
| R2    | 25.00% | 15        | 3.75    |
| R1    | 64.44% | 4         | 2.58    |
| **總計** | 100.00% | | **95.73** |

- **抽卡成本**：140 油
- **總EV**：95.73 油
- **EV/成本**：68.38%

## Ongeki 卡池 (Ongeki Pool)
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R5    | 0.05%  | 50,000    | 25.00   |
| R4    | 0.15%  | 18,000    | 27.00   |
| R3    | 2.00%  | 800       | 16.00   |
| R2    | 15.00% | 50        | 7.50    |
| R1    | 82.80% | 4         | 3.31    |
| **總計** | 100% | | **78.81** |

- **抽卡成本**：140 油
- **總EV**：78.81 油
- **EV/成本**：56.29%

## 典藏卡池（Special Pool）
| 稀有度 | 機率   | 售價（油） | EV貢獻  |
|:------|:-------|:----------|:--------|
| R5    | 0.05%  | 42,000    | 21.00   |
| R4    | 0.15%  | 13,000    | 19.50   |
| R3    | 2.00%  | 400       | 8.00    |
| R2    | 15.00% | 50        | 7.50    |
| R1    | 82.80% | 7         | 5.80    |
| **總計** | 100% | | **61.80** |

- **抽卡成本**：140 油
- **總EV**：61.80 油
- **EV/成本**：44.14%

## 混合池（All Pool）
- **抽卡成本**：50 油
- **組成**：主卡池 99.00% + 典藏卡池 1.00%
- **總EV**：33.37 油
- **EV/成本**：66.74%

## 卡池總覽對比
| 卡池類型   | 抽卡成本（油） | 頂級卡（售價） | 次級卡（售價） | EV/成本   |
|:----------|:-------------|:-------------|:-------------|:----------|
| **WIXOSS** | 140         | R7: 65,000   | R6: 25,000   | **68.38%** |
| **混合池** | 50          | (來自構成池)   | (來自構成池)  | **66.74%** |
| **主卡池** | (混合池用)    | R7: 55,000   | R6: 12,000   | **66.30%** |
| **UNION ARENA** | 120     | R7: 50,000   | R6: 20,000   | **62.53%** |
| **Pokemon TCG** | 90      | R7: 35,000   | R6: 15,000   | **61.58%** |
| **情人節** | 150         | R6: 45,000   | R5: 15,000   | **57.90%** |
| **Ongeki** | 140         | R5: 50,000   | R4: 18,000   | **56.29%** |
| **泳裝**  | 130         | R6: 40,000   | R5: 12,000   | **55.27%** |
| **Hololive** | 150     | R7: 60,000   | R6: 22,000   | **53.32%** |
| **典藏女仆** | 160        | R5: 50,000   | R4: 17,000   | **47.38%** |
| **典藏**  | 140         | R5: 42,000   | R4: 13,000   | **44.14%** |

## 貨幣（油幣）獲取管道

以下是目前已知的油幣獲取方式：

1.  **每日獎勵:**
    *   每天可領取 **5000** 油幣。
    *   可通過配置文件 `config/gacha_settings.yaml` 中的 `economy_daily_reward` 調整。
    *   來源: `gacha/services/core/economy_service.py` (`claim_daily_reward`)

2.  **每小時獎勵:**
    *   每小時可領取 **2000** 油幣。
    *   有 1 小時的冷卻時間。
    *   可通過配置文件 `config/gacha_settings.yaml` 中的 `economy_hourly_reward` 調整。
    *   來源: `gacha/services/core/economy_service.py` (`claim_hourly_reward`)

3.  **賣出卡片:**
    *   將抽到的卡片賣出以換取油幣。
    *   售價根據卡片的稀有度和所屬卡池類型決定（詳見上方表格）。
    *   售價配置位於 `config/gacha_settings.yaml` 中的 `pool_rarity_prices` 區段。
    *   來源: `gacha/services/core/economy_service.py` (`sell_card`, etc.)

4.  **遊戲獎勵:**
    *   可能透過參與遊戲或其他活動獲得油幣獎勵。
    *   具體數量和條件取決於活動設計。
    *   來源: `gacha/services/core/economy_service.py` (`award_oil`)

5.  **小遊戲獲利:**
    *   透過參與各種小遊戲，可能獲得或損失油幣。
    *   每個小遊戲的賠率和獲利期望值各不相同（詳見下方表格）。
    *   來源: 各遊戲服務類 (`gacha/services/games/` 目錄下的服務)

### 小遊戲詳細分析

#### 21點 (Blackjack)

*   **基本賠率:**
    *   普通獲勝: 1:1 (贏得與下注相同的金額)
    *   Blackjack: 1.5:1 (下注的 2.5 倍總回報，即 1.5 倍獎勵)
    *   平局: 返還下注 (不贏不輸)
    *   輸: 損失全部下注

*   **遊戲機制:**
    *   玩家與莊家比較手牌點數，目標是接近但不超過 21 點。
    *   初始獲得兩張牌，可選擇繼續要牌或停牌。
    *   點數超過 21 點即爆牌，自動輸掉。
    *   莊家必須要牌直到達到 17 點或以上。

*   **期望值分析:**
    *   理論上使用完美策略的 EV 約為 -0.5%，略低於下注金額。
    *   實際 EV 取決於玩家策略，通常會低於理論值。

#### 骰子大小 (Dice Game)

*   **基本賠率:**
    *   押大小 (大/小): 1:1 (贏得與下注相同的金額)
    *   押圍骰 (三個相同點數): 1:30 (贏得下注金額的 30 倍)

*   **遊戲機制:**
    *   遊戲擲出**三顆骰子**並計算總和。
    *   **小**: 總點數 4-10
    *   **大**: 總點數 11-17
    *   **圍骰**: 三顆骰子點數相同 (例如 1-1-1, 2-2-2)
    *   **莊家優勢**: 如果開出**圍骰**，所有押注「大」或「小」的玩家均視為**莊家贏**。

*   **期望值分析:**
    *   總組合數: 6 × 6 × 6 = 216 種。
    *   **押大小**:
        *   獲勝組合 (大或小): 各 105 種。獲勝機率 = 105 / 216 ≈ 48.61%。
        *   失敗組合: 111 種 (對家的 105 種 + 圍骰的 6 種)。
        *   期望回報 (EV) = (105 / 216) × 2 (本金+獎金) ≈ 0.9722。
        *   **莊家優勢約為 2.78%**。
    *   **押圍骰**:
        *   獲勝組合: 6 種 (111, 222, ..., 666)。獲勝機率 = 6 / 216 ≈ 2.78%。
        *   期望回報 (EV) = (6 / 216) × 31 (本金+獎金) ≈ 0.8611。
        *   **莊家優勢約為 13.89%**。

#### 尋寶礦區 (Mines)

*   **基本機制:**
    *   遊戲棋盤為 4x5 方格，共 20 格。
    *   玩家選擇開始時的地雷數量 (3, 7 或 12 顆)，地雷數量決定風險和賠率。
    *   玩家逐一選擇安全方格，每選中一個，獎金乘數增加。
    *   選中地雷則遊戲結束，損失全部下注。
    *   玩家可在任何時候提現，獲得當前乘數的獎勵。

*   **莊家優勢:**
    *   遊戲設有 7% 的莊家優勢 (House Edge)。
    *   實際支付乘數 = 理論乘數 × 0.93。

*   **乘數計算:**
    *   理論乘數 = 總選擇方式數 / 安全選擇方式數
    *   總選擇方式數 = C(20, k) (從 20 格中選 k 格的組合數)
    *   安全選擇方式數 = C(安全格數, k) (從安全格中選 k 格的組合數)
    *   安全格數 = 20 - 地雷數

*   **風險與收益分析:**
    *   地雷越少，單次安全選擇的獎勵乘數越低，但整體風險更小。
    *   地雷越多，單次安全選擇的獎勵乘數越高，但風險顯著增加。
    *   長期來看，由於 7% 的莊家優勢，期望值為下注金額的 -7%。

*   **提現策略:**
    *   保守策略: 在低乘數時提現，確保小額但穩定的收益。
    *   激進策略: 追求更高乘數，但風險大幅增加。

#### 小遊戲收益比較表

| 遊戲名稱 | 莊家優勢 | 長期期望值 (EV) | 風險等級 | 策略影響 | 推薦策略 |
| :------- | :------- | :-------------- | :------- | :------- | :------- |
| 21點 | ~0.5% | 下注的 99.5% | 中 | 高 | 使用基本策略表，避免不必要冒險 |
| 骰子大小 | ~2.78% | 下注的 97.22% | 低 | 低 | 押大小的策略最優，但長期仍為負收益 |
| 尋寶礦區 | 7% | 下注的 93% | 高 | 高 | 選擇較少地雷，在 1.5-2.0x 乘數時提現 |

**注意:** 所有小遊戲長期來看都不會為玩家提供正收益，但娛樂性和短期運氣可能帶來一定收益。

## 煉金系統分析

煉金系統是一個高風險的油幣轉油票機制，適合資金充裕但不想抽卡的玩家快速獲取油票。

### 煉金基本機制

| 項目 | 數值/說明 |
| :--- | :--- |
| **最低投入** | 100,000 油幣 |
| **冷卻時間** | 10 秒 |
| **轉換目標** | 油幣 → 油票 |
| **結果性質** | 完全隨機，無技巧成分 |
| **指令** | `/alchemy <金額>` |

### 煉金獎勵計算公式

煉金系統使用複雜的數學模型來計算獎勵：

1. **期望獎勵**: `expected_reward = amount × 0.0075`
2. **波動率因子**: `volatility_factor = 0.5 + (log10(amount / 100000) × 0.15)`
3. **隨機因子**: `roll = random(0, 1)`

### 煉金結果分布

| 機率區間 | 機率 | 結果描述 | 獎勵計算公式 |
| :--- | :--- | :--- | :--- |
| **血本無歸** | 20.0% | 極低回報 | `EV × (1 - vol × 1.5~1.8)` |
| **保底小虧** | 69.85% | 略低於成本 | `EV × (1 - vol × 0.2~0.5)` |
| **接近期望** | 5.0% | 接近期望值 | `EV × (0.98~1.02)` |
| **暴擊大賺** | 5.0% | 顯著高於成本 | `EV × (1 + vol × 2.0~3.0)` |
| **傳奇大獎** | 0.15% | 極高回報 | `EV × (1 + vol × 10.0)` |

其中：
- `EV` = 期望獎勵
- `vol` = 波動率因子

### 不同投入金額的期望分析

| 投入金額 | 期望油票 | 波動率因子 | 血本無歸範圍 | 傳奇大獎 |
| :--- | :--- | :--- | :--- | :--- |
| 100,000 | 750 | 0.50 | 0~375 | 4,500 |
| 500,000 | 3,750 | 0.605 | 0~1,406 | 26,438 |
| 1,000,000 | 7,500 | 0.65 | 0~2,625 | 56,250 |
| 5,000,000 | 37,500 | 0.755 | 0~9,563 | 320,625 |
| 10,000,000 | 75,000 | 0.80 | 0~15,000 | 675,000 |

### 煉金系統特點

#### 優勢
- **快速轉換**: 無需抽卡過程，直接獲得油票
- **高額潛在回報**: 0.15%機率獲得極高回報
- **適合大戶**: 投入越多，波動率越大，潛在收益越高

#### 風險
- **高風險性**: 20%機率血本無歸
- **負期望值**: 長期來看不是正收益投資
- **門檻較高**: 最低10萬油幣投入

### 與抽卡換票的功能定位比較

| 獲取方式 | 成本 | 獲得 | 功能定位 | 適用場景 |
| :--- | :--- | :--- | :--- | :--- |
| **抽卡換票** | 100油 | 1油票 + 1張卡片 | 核心機制 | 正常遊戲流程 |
| **煉金術** | 100,000油 | ~750油票 | 快捷轉換 | 資金充裕且不想抽卡 |

**重要說明**:
- **抽卡是「創造」**: 向遊戲注入新卡片，維持遊戲生態
- **煉金是「轉換」**: 純粹的資產轉換，不產生新內容

**設計哲學**: 煉金術的負期望回報是**刻意設計**，目的是：
1. 確保抽卡仍是主要的遊戲方式
2. 防止玩家完全放棄抽卡轉向煉金
3. 維護遊戲的收集樂趣和卡片生態平衡

煉金術是為「錢多時間少」的玩家提供的**高價快捷方式**，而非最優策略。

### 數據記錄

所有煉金交易都會記錄在 `alchemy_logs` 表中，包含：
- 用戶ID和投入金額
- 獲得的油票數量
- 波動率因子和隨機數值
- 交易時間戳

這些數據用於系統監控和玩家行為分析。

---
報告完畢。

## 升星系統成本分析

升星系統允許玩家通過消耗資源提升卡片的星級，從而增加卡片的價值和稀有性。

### 升星基本成本

以下是升星系統的基本成本架構：

| 項目 | 數值 | 說明 |
| :--- | :--- | :--- |
| 基礎油幣成本 | 50 油 | 每次升星的基本油幣消耗 |
| 每星級增加成本 | 50 油/星 | 每增加一星，油幣成本增加50 |
| 重複卡消耗 | 1 張 | 每次升星固定消耗1張重複卡 |

### 升星成本計算公式

1. **基本油幣成本**: base_oil_cost = 基礎成本(50) + (當前星級 × 每星級增加成本(50))
2. **25星以上額外倍率**: 25星以上卡片成本乘以2.5倍
3. **稀有度修正係數**: 根據卡池類型和稀有度不同，成本增加倍率不同

### 稀有度消耗修正係數

#### 主卡池(main)
| 稀有度 | 消耗倍率 | 舊稱 |
| :--- | :--- | :--- |
| 1 | 1.0倍 | T1 |
| 2 | 1.5倍 | T2 |
| 3 | 2.0倍 | T3 |
| 4 | 3.0倍 | T4 |
| 5 | 4.0倍 | T5 |
| 6 | 6.0倍 | T6 |
| 7 | 10.0倍 | TS |

#### 典藏卡池(special)
| 稀有度 | 消耗倍率 | 舊稱 |
| :--- | :--- | :--- |
| 1 | 1.5倍 | C-T1 |
| 2 | 2.0倍 | C-T2 |
| 3 | 3.0倍 | C-T3 |
| 4 | 5.0倍 | C-T4 |
| 5 | 8.0倍 | C-T5 |

#### 泳裝卡池(summer)
| 稀有度 | 消耗倍率 | 舊稱 |
| :--- | :--- | :--- |
| 1 | 1.0倍 | T1 |
| 2 | 1.5倍 | T2 |
| 3 | 2.0倍 | T3 |
| 4 | 3.0倍 | T4 |
| 5 | 4.0倍 | T5 |
| 6 | 6.0倍 | T6 |

#### 其他卡池
其他卡池（情人節、典藏女僕、Hololive、UNION ARENA、Pokemon TCG）的升星成本修正係數請參考配置文件 `config/gacha_settings.yaml` 或使用主卡池的倍率作為參考。

### 升星成功率

| 星級區間 | 成功率(%) |
| :--- | :--- |
| 0-5星 | 90.0% |
| 6-10星 | 80.0% |
| 11-15星 | 70.0% |
| 16-20星 | 60.0% |
| 21-24星 | 40.0% |
| 25-27星 | 25.0% |
| 28-30星 | 15.0% |
| 31-35星 | 8.0% |

**注意:** 升星若失敗，消耗的資源仍會被扣除。

## 許願系統成本分析

許願系統允許玩家提高特定卡片的抽取機率。系統有兩個主要組成部分：許願槽位和許願力度。

### 許願槽位擴充成本

每個玩家初始擁有1個許願槽位，最多可以擴充至10個槽位。

| 當前槽位數 | 擴充下一槽位成本 (油) |
| :--- | :--- |
| 1 | 10,000 |
| 2 | 25,000 |
| 3 | 50,000 |
| 4 | 100,000 |
| 5 | 250,000 |
| 6 | 500,000 |
| 7 | 1,000,000 |
| 8 | 2,000,000 |
| 9 | 5,000,000 |

### 許願力度提升成本

許願力度決定許願卡片的權重提升倍數。初始力度為1級，最高可提升至10級。

| 當前力度等級 | 提升下一級成本 (油) |
| :--- | :--- |
| 1 | 20,000 |
| 2 | 40,000 |
| 3 | 80,000 |
| 4 | 160,000 |
| 5 | 300,000 |
| 6 | 500,000 |
| 7 | 800,000 |
| 8 | 1,200,000 |
| 9 | 2,000,000 |

### 許願權重倍數

許願卡片在抽取時會獲得權重提升，提升倍數取決於許願力度等級。

| 力度等級 | 權重倍數 |
| :--- | :--- |
| 1 | 3.0倍 |
| 2 | 4.0倍 |
| 3 | 5.0倍 |
| 4 | 6.0倍 |
| 5 | 8.0倍 |
| 6 | 10.0倍 |
| 7 | 12.0倍 |
| 8 | 15.0倍 |
| 9 | 18.0倍 |
| 10 | 20.0倍 |

**注意:** 許願僅提高權重，並不保證一定能抽到許願卡片。權重倍數越高，抽中機率相對越大。

## 各卡池稀有度分布統計

以下為現有遊戲中各卡池內不同稀有度的卡片數量統計：

| 卡池 | 稀有度 | 卡牌數量 |
|------|--------|----------|
| hololive | 1 | 241 |
| hololive | 2 | 183 |
| hololive | 3 | 145 |
| hololive | 4 | 129 |
| hololive | 5 | 49 |
| hololive | 6 | 78 |
| hololive | 7 | 16 |
| main | 1 | 11216 |
| main | 2 | 7609 |
| main | 3 | 5912 |
| main | 4 | 4518 |
| main | 5 | 1898 |
| main | 6 | 504 |
| main | 7 | 104 |
| ptcg | 1 | 3486 |
| ptcg | 2 | 2514 |
| ptcg | 3 | 1774 |
| ptcg | 4 | 675 |
| ptcg | 5 | 1122 |
| ptcg | 6 | 246 |
| ptcg | 7 | 103 |
| special | 1 | 4060 |
| special | 2 | 2733 |
| special | 3 | 490 |
| special | 4 | 51 |
| special | 5 | 23 |
| special_maid | 1 | 307 |
| special_maid | 2 | 123 |
| special_maid | 3 | 51 |
| special_maid | 4 | 3 |
| special_maid | 5 | 3 |
| summer | 1 | 318 |
| summer | 2 | 294 |
| summer | 3 | 258 |
| summer | 4 | 224 |
| summer | 5 | 173 |
| summer | 6 | 53 |
| ua | 1 | 3574 |
| ua | 2 | 898 |
| ua | 3 | 634 |
| ua | 4 | 610 |
| ua | 5 | 973 |
| ua | 6 | 347 |
| ua | 7 | 57 |
| vd | 1 | 201 |
| vd | 2 | 176 |
| vd | 3 | 132 |
| vd | 4 | 115 |
| vd | 5 | 64 |
| vd | 6 | 25 |
| wixoss | 1 | 856 |
| wixoss | 2 | 1484 |
| wixoss | 3 | 717 |
| wixoss | 4 | 571 |
| wixoss | 5 | 384 |
| wixoss | 6 | 197 |
| wixoss | 7 | 39 |
| ongeki | 1 | 17 |
| ongeki | 2 | 525 |
| ongeki | 3 | 916 |
| ongeki | 4 | 93 |
| ongeki | 5 | 355 |

---

# Gacha 商店票券定價模型 V4 (2025/06/27) - 平方根增長溢價模型

## 1. 核心設計哲學

本文件闡述了商店中「隨機券」與「指定券」的定價體系。此體系旨在取代舊有的固定倍率保底系統，建立一個更公平、更能反映真實獲取成本的經濟模型。

核心原則如下：

*   **分離定價**：「隨機券」和「指定券」的價值被分開評估。
*   **隨機券 (Random Ticket)**：其價格嚴格基於獲取一張**隨機**該稀有度卡片的**數學期望成本**。它是一個純粹的機率產物，作為價格計算的基準。
*   **指定券 (Specific Ticket)**：其價值不僅包含隨機獲取的成本，更重要的是包含了**「從大量選項中精準選擇」**的權利。這種選擇權的價值會隨著選項池（即該稀有度的卡片總數 `N`）的增大而**非線性增長**。

## 2. 定價公式

### 2.1. 隨機券價格公式

隨機券的價格直接反映了獲得一張對應稀有度卡片的平均成本（以「油票」計價）。

```
隨機券價格 (油票) = (單抽成本 × 期望抽數) / 100
```
其中：
*   `期望抽數 = 1 / 抽出機率`
*   除以 `100` 是因為 `100 油 = 1 油票`。

**此價格是後續計算指定券價格的基礎。**

### 2.2. 指定券溢價係數公式 (平方根增長模型)

指定券的價格是在隨機券價格的基礎上，乘以一個「溢價係數」。這個係數反映了「指定權」的價值，並根據卡池中該稀有度的卡片總數 `N` 動態變化。

```
溢價係數 = 3.0 + (sqrt(N) - 1) * 1.5
```
其中：
*   `N` 是該卡池中，對應稀有度的卡片總數量。
*   `sqrt(N)` 即 `N` 的平方根。
*   **3.0** 是基礎溢價倍數，確保即使在 `N=1` 的極端情況下，指定券依然比隨機券有價值。
*   **1.5** 是增長因子，用於調節溢價隨 `N` 增長的速率。

### 2.3. 最終指定券價格公式

```
指定券價格 (油票) = 隨機券價格 × 溢價係數
```

## 3. 價格表示例 (截至 2025/06/27)

下表展示了根據此模型計算出的最新商店價格。**此表格應作為 `gacha_settings.yaml` 中價格的權威來源。**

| 卡池 (商店ID) | 稀有度(R) | 卡片數(N) | 隨機券價格 | **新溢價係數** | **【新指定券價格 (油票)】** |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **main_R7** | 7 | 104 | 5,000 | **16.8x** | **84,000** |
| **main_R6** | 6 | 504 | 1,000 | **35.1x** | **35,100** |
| **special_R5** | 5 | 23 | 2,800 | **8.7x** | **24,360** |
| **special_R4** | 4 | 51 | 934 | **12.2x** | **11,395** |
| **summer_R6** | 6 | 53 | 2,600 | **12.4x** | **32,240** |
| **summer_R5** | 5 | 173 | 867 | **21.2x** | **18,380** |
| **vd_R6** | 6 | 25 | 3,000 | **9.0x** | **27,000** |
| **vd_R5** | 5 | 64 | 1,000 | **13.5x** | **13,500** |
| **special_maid_R5** | 5 | 3 | 3,200 | **4.1x** | **13,120** |
| **special_maid_R4** | 4 | 3 | 1,067 | **4.1x** | **4,375** |
| **hololive_R7** | 7 | 16 | 15,000 | **7.5x** | **112,500** |
| **hololive_R6** | 6 | 78 | 3,000 | **14.8x** | **44,400** |
| **ua_R7** | 7 | 57 | 12,000 | **12.8x** | **153,600** |
| **ua_R6** | 6 | 347 | 2,400 | **30.0x** | **72,000** |
| **ptcg_R7** | 7 | 103 | 9,000 | **16.7x** | **150,300** |
| **ptcg_R6** | 6 | 246 | 1,800 | **25.0x** | **45,000** |
| **wixoss_R7** | 7 | 39 | 14,000 | **10.9x** | **152,600** |
| **wixoss_R6** | 6 | 197 | 2,800 | **22.5x** | **63,000** |
| **ongeki_R5** | 5 | 355 | 2,800 | **29.8x** | **83,500** |
| **ongeki_R4** | 4 | 93 | 933 | **16.0x** | **14,900** |

*(所有價格已根據最新卡片數量重新計算，並四捨五入到合理數值)*

## 4. 模型優勢與影響分析

1.  **解決定價倒掛**：徹底解決了舊模型中，部分小型卡池指定券價格被嚴重高估的問題（如 `special_maid`）。
2.  **反映真實成本**：精準地反映了在大型卡池（如 `main_R6`, `ua_R6`）中指定一張次級稀有卡的巨大難度，使其價格回歸合理水平。
3.  **價格體系邏輯自洽**：卡池間的價格差異現在完全由其卡片數量 `N` 決定，邏輯清晰，易於理解和維護。例如，`ua_R6` (N=347) 的價格遠高於 `vd_R6` (N=25)，這在舊模型中是無法體現的。
4.  **可擴展性與可維護性**：當未來卡池內容更新（卡片數量 `N` 變化）時，我們只需使用此模型重新計算並更新 `gacha_settings.yaml` 中的硬編碼值即可。整個流程清晰、可控，避免了複雜的實時動態計算。

## 5. 結論

「平方根增長溢價模型」為遊戲的票券商店提供了一個健壯、公平且可持續的定價框架。它不僅解決了歷史遺留的定價問題，也為未來的內容更新提供了一套標準化的維護流程。