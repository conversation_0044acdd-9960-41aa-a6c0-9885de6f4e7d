{"REGEN_WEAK": {"name": "微弱回復", "description": "每回合開始時回復少量生命值", "icon_key": "regen", "is_buff": true, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 3, "tick_at_turn_start": true, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [{"effect_type": "HEAL", "heal_type": "FLAT", "value": 15.0}], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE"]}, "INVINCIBLE_SHORT": {"name": "短暫無敵", "description": "短時間內免疫所有傷害", "icon_key": "invincible", "is_buff": true, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 1, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["UNDISPELLABLE", "NON_STACKABLE_IGNORE"]}, "ATK_BOOST": {"name": "攻擊力提升", "description": "物理攻擊力和魔法攻擊力提升10%", "icon_key": "atk_boost", "is_buff": true, "max_stacks": 5, "duration_type": "TURNS", "default_duration": 5, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.10"}, {"stat_name": "matk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.10"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "DEF_BOOST": {"name": "防禦力提升", "description": "物理防禦力和魔法防禦力提升15%", "icon_key": "def_boost", "is_buff": true, "max_stacks": 5, "duration_type": "TURNS", "default_duration": 5, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.15"}, {"stat_name": "mdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.15"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "HP_BOOST": {"name": "生命力提升", "description": "最大生命值提升20%", "icon_key": "hp_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 5, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "max_hp", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "SPD_BOOST": {"name": "速度提升", "description": "速度提升25%", "icon_key": "spd_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.25"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "CRIT_RATE_BOOST": {"name": "爆擊率提升", "description": "爆擊率提升15%", "icon_key": "crit_rate_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.15"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "CRIT_DMG_BOOST": {"name": "爆擊傷害提升", "description": "爆擊傷害倍數提升30%", "icon_key": "crit_dmg_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_dmg_multiplier", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.30"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ACCURACY_BOOST": {"name": "命中率提升", "description": "命中率提升20%", "icon_key": "accuracy_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "accuracy", "modification_type": "FLAT_ADD", "value_formula": "0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "EVASION_BOOST": {"name": "閃避率提升", "description": "閃避率提升20%", "icon_key": "evasion_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "evasion", "modification_type": "FLAT_ADD", "value_formula": "0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "MP_BOOST": {"name": "法力值提升", "description": "最大法力值提升25%", "icon_key": "mp_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 5, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "max_mp", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.25"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "MP_REGEN_BOOST": {"name": "法力回復提升", "description": "每回合法力回復提升50%", "icon_key": "mp_regen_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 5, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "mp_regen_per_turn", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.50"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "REFLECT_STATUS": {"name": "傷害反射", "description": "反射部分受到的傷害給攻擊者", "icon_key": "reflect", "is_buff": true, "max_stacks": 1, "duration_type": "TURNS", "default_duration": 2, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "NON_STACKABLE_REFRESH_DURATION"]}, "CRIT_BOOST": {"name": "暴擊強化", "description": "暴擊率和暴擊傷害提升", "icon_key": "crit_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "crit_rate", "modification_type": "FLAT_ADD", "value_formula": "0.10"}, {"stat_name": "crit_dmg_multiplier", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.20"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}, "ALL_STATS_BOOST": {"name": "全能力強化", "description": "所有基礎屬性提升", "icon_key": "all_stats_boost", "is_buff": true, "max_stacks": 3, "duration_type": "TURNS", "default_duration": 4, "tick_at_turn_start": false, "tick_at_turn_end": false, "effect_definitions_on_apply": [{"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.08"}, {"stat_name": "matk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.08"}, {"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.08"}, {"stat_name": "mdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.08"}, {"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.08"}]}], "effect_definitions_per_tick": [], "effect_definitions_on_expire": [], "effect_definitions_triggered": [], "special_flags": ["DISPELLABLE", "STACKABLE_INTENSITY"]}}