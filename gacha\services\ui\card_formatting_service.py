# -*- coding: utf-8 -*-
"""
服務模組，用於提供卡片相關的UI元素格式化功能。
"""

from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from config.app_config import get_config
from gacha.constants import RarityLevel
from gacha.views import utils as view_utils
from gacha.views.utils import format_oil  # 假設存在，用於格式化油幣

if TYPE_CHECKING:
    pass  # 避免循環導入

# Discord Emoji 常量
STATUS_EMOJI = "<:status:1357722672317993231>"
REPLY_CONT_EMOJI = "<:ReplyCont:1383146319425699931>"
REPLY_EMOJI = "<:Reply:1357534074830590143>"


def _get_pool_prefix(pool_type: Optional[str]) -> str:
    """獲取卡池前綴。"""
    if not pool_type:
        return ""
    prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
    return prefixes.get(pool_type, "") if isinstance(prefixes, dict) else ""


def format_draw_title(
    display_name: str,
    is_wish: bool,
    pool_name_or_prefix: str,
    is_new_card: bool,
) -> str:
    """格式化抽卡結果的Embed標題。"""
    wish_prefix = "✨" if is_wish else ""
    status_text = "新卡!" if is_new_card else "重複!"
    return f"{STATUS_EMOJI} {display_name} 抽到了 {wish_prefix}{pool_name_or_prefix} {status_text}"


def format_card_name_and_star_line(
    card_name: str,
    is_wish: bool,
    pool_emoji_prefix: str = "",
    star_level: int = 0,
) -> str:
    """格式化卡片名稱、狀態 Emoji 和星級。"""
    wish_prefix = "✨ " if is_wish else ""
    star_display = (
        f" {view_utils.get_star_emoji_string(star_level)}" if star_level > 0 else ""
    )
    return f"{REPLY_CONT_EMOJI} **{pool_emoji_prefix}{wish_prefix}{card_name}**{star_display}"


def format_card_series_line(card_series: str) -> str:
    """格式化卡片系列行。"""
    return f"{REPLY_CONT_EMOJI} *{card_series}*"


def format_rarity_pool_line(rarity_enum: Optional[RarityLevel], pool_type: str) -> str:
    """格式化稀有度和卡池資訊行。"""
    rarity_display = (
        view_utils.get_user_friendly_rarity_name(rarity_enum)
        if rarity_enum
        else "未知稀有度"
    )
    prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
    pool_display_text = (
        prefixes.get(pool_type, pool_type) if isinstance(prefixes, dict) else pool_type
    )  # 使用前綴或原始類型
    return f"稀有度: {rarity_display} | {pool_display_text}"


def format_owner_count_display_line(owner_count: int) -> str:
    """格式化擁有者數量顯示行。"""
    return f"擁有者: {owner_count:,}" if owner_count > 0 else ""


def format_price_and_balance_line(
    price_label: str, price_display: str, balance_display: str
) -> str:
    """格式化價格和餘額顯示行。"""
    combined_value = combine_price_and_balance_field_value(
        price_label, price_display, balance_display
    )
    return f"{REPLY_EMOJI} {combined_value}"


def _get_base_price(rarity: Optional[RarityLevel], pool_type: str) -> int:
    """獲取基礎價格。"""
    rarity_value = rarity.value if rarity else None
    prices_config = get_config(f"gacha_core_settings.pool_rarity_prices.{pool_type}")
    if prices_config and rarity_value:
        return getattr(prices_config, str(rarity_value), 0)
    return 0


def format_market_price_display(
    current_market_sell_price: Optional[Decimal],
    card_rarity_for_fallback: Optional[RarityLevel],
    pool_type_for_fallback: str,
    trend_symbol: str = "",
) -> tuple[str, str]:
    """格式化市場價格顯示。返回 (價格標籤, 格式化後的價格字串)。"""
    from config.app_config import get_oil_emoji

    oil_emoji = get_oil_emoji()

    # 嘗試使用市場價格
    if current_market_sell_price is not None:
        try:
            price = int(Decimal(str(current_market_sell_price)))
            return "市場價", f"`{price:,}` {oil_emoji} {trend_symbol}".strip()
        except (ValueError, TypeError):
            # 轉換失敗，使用基礎價格
            base_price = _get_base_price(
                card_rarity_for_fallback, pool_type_for_fallback
            )
            return "基礎賣價 (異常)", f"`{base_price:,}` {oil_emoji}"

    # 沒有市場價格，使用基礎價格
    base_price = _get_base_price(card_rarity_for_fallback, pool_type_for_fallback)
    return "基礎賣價 (未更新)", f"`{base_price:,}` {oil_emoji}"


def format_balance_display(balance: int) -> str:
    """格式化餘額顯示。"""
    return format_oil(balance, label="餘額")


def combine_price_and_balance_field_value(
    price_label: str, price_display: str, balance_display: str
) -> str:
    """組合價格和餘額信息。"""
    return f"{price_label}: {price_display} | {balance_display}"


def format_draw_footer_text_main_content(
    is_wish: bool, owner_count: Optional[int] = None, sell_command_text: str = ""
) -> str:
    """格式化抽卡 Embed 頁腳的主要文字內容。"""
    parts = []
    if owner_count is not None and owner_count > 0:
        parts.append(f"擁有者: {owner_count:,}")
    if is_wish:
        parts.append("✨許願成功✨")
    return " • ".join(parts)


def format_multi_draw_page_card_event_title(
    display_name: str, is_wish: bool, pool_type: Optional[str], is_new_card: bool
) -> str:
    """格式化十連抽翻頁檢視時，單張卡片的事件描述標題。"""
    pool_prefix = _get_pool_prefix(pool_type)
    wish_prefix = "✨" if is_wish else ""
    combined_prefix = (
        f"{wish_prefix}{pool_prefix}" if wish_prefix or pool_prefix else ""
    )

    return format_draw_title(
        display_name,
        is_wish=is_wish,
        pool_name_or_prefix=combined_prefix,
        is_new_card=is_new_card,
    )


def _get_status_emoji(is_favorite: bool, is_new_card: bool) -> str:
    """獲取狀態 emoji。"""
    if is_favorite:
        return view_utils.get_ui_emoji("heart")
    return (
        view_utils.get_ui_emoji("new_card")
        if is_new_card
        else view_utils.get_ui_emoji("old_card")
    )


def _shorten_series_name(series: str, max_len: int = 12, suffix: str = "...") -> str:
    """縮短系列名稱。"""
    if len(series) <= max_len:
        return series
    actual_max = max(0, max_len - len(suffix))
    return series[:actual_max] + suffix


def format_multi_draw_card_summary_line(
    card_name: str,
    card_series: str,
    is_favorite: bool,
    is_new_card: bool,
    is_wish: bool,
    rarity_enum: Optional[RarityLevel],
    shorten_series: bool = False,
    max_series_len: int = 12,
    series_suffix: str = "...",
) -> str:
    """格式化十連抽總覽中單張卡片的摘要行。"""
    status_emoji = _get_status_emoji(is_favorite, is_new_card)
    rarity_emoji = (
        view_utils.get_rarity_display_code(rarity_enum) if rarity_enum else "<?> "
    )
    wish_prefix = "✨ " if is_wish else ""

    series_name = (
        _shorten_series_name(card_series, max_series_len, series_suffix)
        if shorten_series
        else card_series
    )

    return (
        f"{status_emoji} {rarity_emoji}{wish_prefix}**{card_name}** *({series_name})*"
    )
