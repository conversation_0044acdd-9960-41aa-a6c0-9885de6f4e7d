"""
Gacha系統抽卡服務

負責處理抽卡流程的整體協調，包括：
- 接收外部抽卡請求（單抽、多抽）
- 計算抽卡成本
- 調用 DrawEngineService 來決定抽取的卡片
- 處理用戶數據更新（油幣扣除、卡片添加至收藏）
- 管理數據庫事務
- 將抽卡事件發布到消息隊列供其他服務消費
- 格式化並返回最終的抽卡結果

專注於業務流程和數據管理，而不是抽卡決策邏輯（由 DrawEngineService 處理）。
"""

import asyncio  # 確保導入
from typing import Any, Dict, List, Optional, Tuple

from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import (
    DatabaseOperationError,
    InsufficientBalanceError,
    NoCardsDeterminedError,
)
from gacha.models.models import CardStatus, CardWithStatus
from gacha.repositories.collection import user_collection_repository
from gacha.services import (
    draw_engine_service,
    validation_service,
    wish_service,
)
from utils.logger import logger


async def draw_multiple(
    user_id: int,
    pool_types: List[str],
    count: int = 10,
    nickname: Optional[str] = None,
    user_selected_pool: str = "all",
    draw_type: str = "multi",
) -> Dict[str, Any]:
    """執行多抽"""
    return await draw_cards(
        user_id, pool_types, count, nickname, user_selected_pool, draw_type
    )


def calculate_draw_cost(pool_types: List[str], count: int = 1) -> int:
    """
    計算抽卡成本
    """
    from config.app_config import get_gacha_core_settings

    pool_costs = get_gacha_core_settings().pool_costs
    if not pool_types:
        return pool_costs.get("main", 50) * count
    if "all" in pool_types or len(pool_types) > 1:
        return pool_costs.get("all", 50) * count
    pool_type = pool_types[0]
    cost = pool_costs.get(pool_type)
    if cost is None:
        logger.warning(
            "卡池類型 '%s' 在 pool_costs 中未找到，默認使用 'main' 卡池成本", pool_type
        )
        cost = pool_costs.get("main", 50)
    return cost * count


async def draw_cards(
    user_id: int,
    pool_types: List[str],
    count: int = 1,
    nickname: Optional[str] = None,
    user_selected_pool: str = "all",
    draw_type: str = "single",
) -> Dict[str, Any]:
    """
    執行抽卡，這是整個抽卡流程的主要方法 (終極優化版)
    """
    # 1. 驗證和成本計算
    valid_pools = validation_service.validate_pool_types(pool_types)
    total_cost = calculate_draw_cost(valid_pools, count)

    # 1.5. 【新】應用層樂觀檢查 (快速失敗)
    # 導入 user_service
    from . import user_service

    # 確保用戶存在並獲取其資訊
    # 注意：這裡的 nickname 可能為 None，但 get_user 會處理
    user = await user_service.get_user(
        user_id, create_if_missing=True, nickname=nickname
    )
    if user.oil_balance < total_cost:
        raise InsufficientBalanceError(required=total_cost, current=user.oil_balance)

    # 2. 決定卡片 (使用已優化的 draw_engine)
    (
        cards_results_raw,
        drawn_card_ids,
    ) = await draw_engine_service.determine_cards_to_draw(user_id, valid_pools, count)
    if not drawn_card_ids:
        raise NoCardsDeterminedError("抽卡失敗，未能獲取任何卡片")

    # 3. 【方案3】並行獲取許願和最愛狀態
    wish_future = wish_service.get_wishes_status_batch(user_id, drawn_card_ids)
    favorite_future = user_collection_repository.get_favorite_status_batch(
        user_id, drawn_card_ids
    )
    wish_status_map, favorite_status_map = await asyncio.gather(
        wish_future, favorite_future
    )

    # 4. 【方案4】執行核心事務（包含統計更新）
    (
        new_balance,
        bulk_add_results,
        owner_changes_from_db,
    ) = await _execute_draw_transaction(user_id, nickname, total_cost, drawn_card_ids)

    # 5. 寫入抽卡歷史 (非事務性，或在獨立事務中)
    # 這一步可以非同步執行，不阻塞主流程返回
    asyncio.create_task(
        _write_draw_history(
            user_id,
            cards_results_raw,
            bulk_add_results,
            wish_status_map,
            user_selected_pool,
            draw_type,
        )
    )

    # 6. 市場統計更新已在主事務中完成，無需額外處理

    # 7. 豐富抽卡結果並返回
    enriched_cards, owner_counts = await _enrich_drawn_cards_info(
        cards_results_raw, bulk_add_results, wish_status_map, favorite_status_map
    )

    return {
        "cards_with_status": enriched_cards,
        "owner_counts": owner_counts,
        "new_balance": new_balance,
        "is_multi_draw": count > 1,
        "user_selected_pool": user_selected_pool,
        "draw_type": draw_type,
    }


async def _execute_draw_transaction(
    user_id: int, nickname: Optional[str], total_cost: int, drawn_card_ids: List[int]
) -> Tuple[int, Dict[int, Any], List[Dict[str, Any]]]:
    """
    【重構】在應用程式層執行抽卡核心事務，以整合餘額歷史記錄
    """
    # 導入 economy_service
    from decimal import Decimal

    from . import economy_service, user_service

    pool = get_pool()
    async with pool.acquire() as conn:
        async with conn.transaction():
            try:
                # 1. 扣除油幣 (這會自動檢查餘額並記錄歷史)
                new_balance = await economy_service.award_oil(
                    user_id=user_id,
                    amount=-total_cost,
                    transaction_type="draw",
                    reason=f"Draw {len(drawn_card_ids)} cards",
                    connection=conn,
                )

                # 2. 增加總抽卡次數
                await user_service.increment_draws(
                    user_id=user_id,
                    draw_count=len(drawn_card_ids),
                    connection=conn,
                )

                # 3. 增加油票
                tickets_to_add = Decimal(total_cost) / Decimal("100.0")
                if tickets_to_add > 0:
                    await user_service.increment_oil_ticket_balance(
                        user_id=user_id,
                        amount_to_add=tickets_to_add,
                        connection=conn,
                    )

                # 4. 將卡片添加到收藏
                redis_client = get_redis_client()
                if not redis_client:
                    raise DatabaseOperationError("Redis client is not available.")

                bulk_add_results = (
                    await user_collection_repository.bulk_add_cards_and_check_new(
                        redis_client=redis_client,
                        user_id=user_id,
                        card_ids=drawn_card_ids,
                        connection=conn,
                    )
                )

                # 5. 根據 is_new 標記模擬 owner_changes
                owner_changes = [
                    {"card_id": card_id, "change": 1}
                    for card_id, result in bulk_add_results.items()
                    if result.get("is_new")
                ]

                # 6. 【新增】在同一事務中更新市場統計
                from gacha.services.direct_market_stats_updater import (
                    update_market_stats_in_transaction,
                )

                await update_market_stats_in_transaction(
                    conn=conn,
                    drawn_card_ids=drawn_card_ids,
                    owner_changes=owner_changes,
                )

                return new_balance, bulk_add_results, owner_changes

            except InsufficientBalanceError as e:
                # award_oil 會拋出這個異常，我們直接向上傳遞
                raise e
            except Exception as e:
                logger.error(
                    "抽卡事務在應用程式層執行失敗: user=%s, error=%s",
                    user_id,
                    e,
                    exc_info=True,
                )
                raise DatabaseOperationError(f"資料庫事務處理失敗: {e}") from e


async def _write_draw_history(
    user_id: int,
    cards_results_raw: List[Dict[str, Any]],
    bulk_add_results: Dict[int, Any],
    wish_status_map: Dict[int, bool],
    user_selected_pool: str,
    draw_type: str,
):
    """
    【新】異步寫入抽卡歷史記錄
    """
    try:
        history_records = []
        pool = get_pool()
        # 使用一個獨立的會話ID
        async with pool.acquire() as conn:
            session_id_row = await conn.fetchrow("SELECT gen_random_uuid()")
            if not session_id_row:
                raise DatabaseOperationError("無法生成抽卡會話ID")
            draw_session_id = session_id_row[0]

        for card_result in cards_results_raw:
            card = card_result["card"]
            card_id = card.card_id
            pool_type = card.pool_type
            # bulk_add_results 來自DB，是 authoritative source
            op_res = bulk_add_results.get(card_id, {})
            is_new = op_res.get("is_new", False)
            # wish_status_map 來自 Python 端
            is_wish = wish_status_map.get(card_id, False) or card_result.get(
                "is_wish", False
            )
            history_records.append(
                (
                    draw_session_id,
                    user_id,
                    card_id,
                    pool_type,
                    is_new,
                    is_wish,
                    user_selected_pool,
                    draw_type,
                )
            )

        if history_records:
            async with pool.acquire() as conn:
                await conn.executemany(
                    """
                    INSERT INTO gacha_draw_history (draw_session_id, user_id, card_id, pool_type, is_new, is_wish, user_selected_pool, draw_type)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """,
                    history_records,
                )
    except Exception as e:
        logger.error(
            "異步寫入抽卡歷史失敗: user=%s, error=%s", user_id, e, exc_info=True
        )


# 【第一步】建立一個新的、純同步的函式來執行 for 迴圈
def _enrich_drawn_cards_info_sync(
    cards_results: List[Dict[str, Any]],
    bulk_add_results: Dict[int, Dict[str, Any]],
    wish_status_map: Dict[int, bool],
    favorite_status_map: Dict[int, bool],
) -> List[CardWithStatus]:
    """純同步的資料豐富化處理，專門運行在背景執行緒"""
    processed_cards = []
    for card_result in cards_results:
        card = card_result["card"]
        card_id = card.card_id
        is_wish = wish_status_map.get(card_id, False) or card_result.get("is_wish", False)
        is_favorite = favorite_status_map.get(card_id, False)
        op_res = bulk_add_results.get(card_id, {})
        status = CardStatus(
            is_new_card=op_res.get("is_new", False),
            star_level=op_res.get("star_level", 0),
            is_wish=is_wish,
            is_favorite=is_favorite,
        )
        card_with_status = CardWithStatus.create(
            card=card, status=status, pool_type=card_result["pool_type"]
        )
        processed_cards.append(card_with_status)
    return processed_cards


# 【第二步】修改原有的 async 函式，讓它變成一個調度器
async def _enrich_drawn_cards_info(
    cards_results: List[Dict[str, Any]],
    bulk_add_results: Dict[int, Dict[str, Any]],
    wish_status_map: Dict[int, bool],
    favorite_status_map: Dict[int, bool],
) -> Tuple[List[CardWithStatus], Dict[int, int]]:
    """
    豐富抽取的卡片信息 (最終非阻塞優化版)
    """
    all_drawn_card_ids = [cr["card"].card_id for cr in cards_results]

    # 【關鍵】將 CPU 密集型的 for 迴圈外包到背景執行緒
    processed_cards_future = asyncio.to_thread(
        _enrich_drawn_cards_info_sync,
        cards_results,
        bulk_add_results,
        wish_status_map,
        favorite_status_map
    )

    # 同時，準備 I/O 密集的 Redis 查詢
    async def get_empty_dict():
        return {}
        
    if all_drawn_card_ids:
        redis_client = get_redis_client()
        if redis_client is None:
            logger.warning("Redis client is not initialized, skipping owner count fetch.")
            owner_counts_future = get_empty_dict()
        else:
            owner_counts_future = user_collection_repository.get_card_owner_counts_batch(
                redis_client, all_drawn_card_ids
            )
    else:
        owner_counts_future = get_empty_dict()

    # 使用 asyncio.gather 來並行等待兩個操作的結果
    # to_thread 會在背景執行緒運行，Redis 查詢會註冊到事件迴圈
    # gather 會等到兩者都完成後才返回
    processed_cards, owner_counts = await asyncio.gather(
        processed_cards_future,
        owner_counts_future
    )

    return processed_cards, owner_counts
