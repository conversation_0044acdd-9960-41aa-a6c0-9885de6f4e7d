# 專案名稱：終極百家樂 Bot (Ultimate Baccarat Bot)

## 第一部分：核心架構與設計理念

1.  **指令系統**: 只使用斜線指令 (`/`)。
2.  **互動介面**: 完全依賴按鈕 (Buttons) 和彈出視窗 (Modals)。
3.  **訊息呈現**: 所有遊戲相關訊息都使用精緻的嵌入式卡片 (Embeds)。
4.  **後端邏輯**:
    *   **事件驅動**: Bot 只對互動事件做出反應。
    *   **完全解耦**: 遊戲邏輯與 Discord 頻道無關。
    *   **ID 追蹤**: 每個遊戲實例都由一個唯一 ID (`game_id`) 標識，該 ID 嵌入在互動元件的 `custom_id` 中。
5.  **多實例處理**: 天生支持。每次 `/baccarat` 指令都會創建一個全新的、獨立的遊戲實例，互不干擾。

---

## 第二部分：指令與參數定義 (`/baccarat`)

*   **指令名稱**: `/baccarat`
*   **描述**: 發起一場新的百家樂牌局。
*   **參數 (Options)**:
    1.  `amount`
        *   **類型**: `Number` (數字)
        *   **是否必須**: 是 (Required)
        *   **描述**: 您想下的第一筆賭注金額。
    2.  `on`
        *   **類型**: `String` (字串)
        *   **是否必須**: 是 (Required)
        *   **選項 (Choices)**: `閒家`, `莊家`, `和局`
        *   **描述**: 您想押注的對象。

---

## 第三部分：遊戲流程與 Bot 實現細節

#### 階段 1：發起牌局 (Interaction: Slash Command)

1.  **玩家 A** 輸入 `/baccarat amount:100 on:莊家`。
2.  **Bot 收到 `interactionCreate` 事件，執行以下動作:**
    a.  **創建遊戲實例**: 在後台創建一個 `Game` 物件/資料結構。
        ```python
        # 偽代碼示例 (Python)
        import time
        
        game_id = f"bacc_{interaction.user.id}_{int(time.time() * 1000)}"
        new_game = {
          "game_id": game_id,
          "host_id": interaction.user.id,
          "state": "betting",
          "players": [
            {"user_id": interaction.user.id, "on": "莊家", "amount": 100}
          ],
          "timers": {},
          "message_id": None  # 稍後填入
        }
        # 將 new_game 存儲在一個全局的字典中
        active_games[game_id] = new_game
        ```
    b.  **設計互動元件**: 創建帶有唯一 ID 的按鈕。
        ```python
        # 按鈕的 custom_id 設計 (Python)
        player_button_id = f"bacc:join:player:{game_id}"
        banker_button_id = f"bacc:join:banker:{game_id}"
        tie_button_id = f"bacc:join:tie:{game_id}"
        start_button_id = f"bacc:start:{game_id}"
        cancel_button_id = f"bacc:cancel:{game_id}"
        ```
    c.  **發送遊戲面板**: 回應指令，發送一個包含 Embed 和上述按鈕的訊息。
    d.  **儲存訊息 ID**: 獲取剛發送訊息的 ID，並更新到後台的 `Game` 物件中，用於後續的編輯操作。
    e.  **啟動計時器**: 為這個 `gameId` 啟動專屬的「動態倒數」和「全局超時」計時器。

#### 階段 2：玩家加入 (Interaction: Button Click)

1.  **玩家 B** 點擊遊戲面板上的 `[押閒家]` 按鈕。
2.  **Bot 收到 `interactionCreate` 事件，執行以下動作:**
    a.  **解析 ID**: 從事件的 `custom_id` (`bacc:join:player:...`) 中解析出 `gameId` 和意圖 (`join`, `player`)。
    b.  **定位遊戲實例**: 使用 `gameId` 從 `activeGames` 中找到對應的 `Game` 物件。如果找不到，說明遊戲已結束，可以回覆一條臨時的錯誤訊息。
    c.  **彈出視窗 (Modal)**: 創建並彈出一個 Modal，讓玩家 B 輸入下注金額。這個 Modal 的 `custom_id` 同樣需要包含 `gameId`，以便提交時能找到正確的遊戲。

#### 階段 3：提交下注 (Interaction: Modal Submit)

1.  **玩家 B** 在 Modal 中輸入金額並提交。
2.  **Bot 收到 `interactionCreate` 事件，執行以下動作:**
    a.  **解析 ID**: 從 Modal 的 `custom_id` 中解析出 `gameId`。
    b.  **獲取遊戲鎖**: 鎖定該 `gameId` 對應的 `asyncio.Lock`。
    c.  **定位並驗證遊戲狀態**:
        *   使用 `gameId` 從 `active_games` 中查找遊戲實例。
        *   **檢查 1 (遊戲是否存在)**: 如果找不到遊戲，說明遊戲已結束並被清理。向玩家回覆一條僅他可見的錯誤訊息（如「牌局已結束」），然後釋放鎖並終止操作。
        *   **檢查 2 (狀態是否為下注中)**: 如果遊戲存在，但其 `state` 不是 `'betting'`，說明下注時間已截止。同樣回覆一條僅他可見的錯誤訊息，然後釋放鎖並終止操作。
    d.  **更新數據**: 只有在通過上述所有檢查後，才將玩家 B 的下注資訊添加到 `Game` 物件的 `players` 列表中。
    e.  **重置計時器**: 重置該 `Game` 物件的「動態倒數」計時器。
    f.  **發送臨時確認**: 使用 `interaction.followup.send(ephemeral=True)` 向該玩家發送一條僅他可見的臨時訊息，確認其下注成功（例如：「✅ 您已成功下注 100 油幣到閒家！」）。
    g.  **觸發批量更新**: 觸發或重置一個短延時的計時器，以便在稍後將多個玩家的變動一次性更新到主遊戲面板上。
    h.  **釋放鎖**: 完成所有操作後，釋放遊戲鎖。

#### 階段 4：遊戲開始 (三種觸發方式)

1.  **手動觸發 (Button Click)**: 發起人點擊 `[▶️ 開始遊戲]` 按鈕。Bot 收到事件，解析 `gameId`，找到遊戲，立即將 `state` 改為 `playing` 並取消所有計時器。
2.  **動態倒數觸發**: 某個 `Game` 物件的「動態倒數」計時器歸零，自動觸發遊戲開始。
3.  **全局超時觸發**: 「全局超時」計時器歸零，強制觸發遊戲開始。

**遊戲開始時的動作:**
a.  **封盤**: 編輯對應 `messageId` 的訊息，將所有按鈕設為禁用 (disabled)。
b.  **更新 Embed**: 更改 Embed 標題為「下注已鎖定，開始發牌！」。
c.  **執行核心邏輯**: 在後台運行百家樂的發牌、補牌、比點算法。

#### 階段 5：結算與結束

1.  **計算完成後，Bot 執行最後的動作:**
    a.  **最終編輯**: 再次編輯那則訊息，更新 Embed，清晰地展示出：
        *   最終的勝負結果 (e.g., "莊家獲勝！")
        *   詳細的發牌過程。
        *   閒家和莊家的最終牌面與點數。
        *   一個條列式的玩家結算列表，標明每個參與者的輸贏金額。
    b.  **清理後台**: 將該 `Game` 物件從 `activeGames` 中移除，釋放記憶體。遊戲生命週期正式結束。

#### 階段 6：再來一局 (Interaction: Button Click on Replay View)

1.  **遊戲結束後，面板上會出現「再來一局」按鈕。**
2.  **玩家點擊該按鈕，觸發回調函式。**
3.  **在回調函式中，必須立即執行以下操作：**
    a.  **停止舊視圖**: 在執行任何新遊戲邏輯之前，必須先呼叫該按鈕所在視圖 (View) 的 `self.stop()` 方法。這會將舊的結果面板上的所有按鈕禁用，防止重複點擊。
    b.  **開啟新遊戲**: 接著，執行開啟一場全新牌局的邏輯，這將會發送一條帶有全新、可互動視圖的新訊息。

---

## 第四部分：視覺呈現 (Embed 設計)

*   **下注階段**:
    *   **顏色**: 藍色或金色。
    *   **標題**: `🎲 百家樂牌局進行中！`
    *   **欄位**: 「下注情況」，動態列出已下注的玩家。
*   **結算階段**:
    *   **顏色**: 根據勝負方改變，莊家贏用紅色，閒家贏用綠色，和局用灰色。
    *   **標題**: `🎉 閒家獲勝！(9點 vs 5點) 🎉`
    *   **欄位**: 分別設立「發牌過程」、「最終牌面」、「玩家結算」等欄位，條理分明。

---

## 第五部分：實作細節與參考

根據討論，我們確定以下實作細節：

1.  **並發控制 (Concurrency Control)**:
    *   為了解決多玩家同時操作可能引發的競態條件問題，系統將為每一個活躍的遊戲實例 (`gameId`) 配備一個 `asyncio.Lock`。
    *   任何需要修改遊戲狀態的操作（如玩家下注），都必須先獲取該遊戲的鎖，操作完成後再釋放。這確保了資料的一致性和準確性。

2.  **後端儲存 (方案 A)**:
    *   遊戲狀態將儲存在 Bot 記憶體中的一個字典裡 (`active_games`)。
    *   這意味著如果 Bot 重啟，正在進行中的遊戲將會遺失。
    *   初期版本**不整合** `economy_service`，遊戲結束時只顯示輸贏金額，不實際增減玩家餘額。

3.  **遊戲統計整合**:
    *   在每局遊戲結束時，將會呼叫 `gacha.services.game_stats_service.record_game_result()` 來記錄統計數據。
    *   由於百家樂是多人遊戲，系統將會**為每一位參與的玩家單獨呼叫**一次記錄函式。
    *   傳遞給服務的 `game_data` 字典將包含以下內容：
        *   **標準欄位**: `bet`, `payout`, `profit`, `result` (win/lose/push)。
        *   **遊戲特定欄位 (`game_specific_stats`)**:
            *   `bet_on`: 玩家下注的對象 ('閒家', '莊家', '和局')。
            *   `final_player_hand`: 閒家最終手牌。
            *   `final_banker_hand`: 莊家最終手牌。
            *   `final_winner`: 該局的獲勝方。
    
4.  **遊戲規則、賠率與平衡**:
    *   **賠率規則 (內建莊家優勢)**:
        *   押閒家贏: 賠率 1:1。
        *   押莊家贏: 賠率 1:0.95 (收取 5% 佣金)。
        *   押和局贏: 賠率 1:8。
        *   開出和局時，押閒家和莊家的賭注將被退還 (Push)。
    *   **補牌規則**:
        *   將嚴格按照國際通用的「Punto Banco」規則進行閒家和莊家的補牌判斷，確保遊戲的公平性和真實性。詳細的補牌邏輯將被封裝在 `BaccaratGame` 類別中。
    *   **結算邏輯**:
        *   遊戲結束時，系統會遍歷所有玩家，根據其下注對象和最終賽果，按照上述賠率精確計算每個人的輸贏。
    
5.  **補充考量與優化**:
    *   **API 速率限制與即時反饋**: 為避免觸發 Discord API 速率限制，將對主遊戲面板的訊息更新採用「節流」機制（批量更新）。同時，為了提供即時反饋，當玩家成功下注後，會立即收到一條僅自己可見的臨時訊息（Ephemeral Message）來確認操作成功。
    *   **超時處理**: 明確定義全局超時的行為。若超時發生時，玩家少於兩人，則遊戲自動取消；若玩家多於一人，則遊戲自動開始。
    *   **規則透明度**: 在初始遊戲面板的 Embed 中，將明確標示出核心賠率，特別是莊家贏的 5% 佣金，以管理玩家預期。
    *   **`custom_id` 長度**: `game_id` 的設計將確保其與 `custom_id` 的前綴組合後，總長度在 Discord 的 100 字元限制之內。
    
6.  **程式碼結構與視覺參考**:
    *   整個功能的程式碼結構將大量參考現有的 `gacha/cogs/blackjack_cog.py`。
    *   **遊戲邏輯**：將創建一個 `BaccaratGame` 類別來封裝所有核心規則（如發牌、補牌、計算點數、判斷勝負）。
    *   **互動控制**：`BaccaratCog` 將處理所有 Discord 的斜線指令和元件互動。
    *   **視覺呈現**：
        *   遊戲的 Embed 將模仿 `blackjack_cog.py` 的風格。
        *   使用 Markdown 的 `#` 語法來**放大**顯示牌面點數。
        *   使用現有的卡牌 Emoji 來顯示撲克牌。
        *   遊戲資訊（如玩家列表、各方手牌）將被放在 Embed 的 `description` 欄位中，以獲得最大的靈活性和空間。