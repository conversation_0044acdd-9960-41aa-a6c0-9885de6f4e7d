"""
Gacha系統篩選條件模型
定義用於卡片收藏系統的篩選條件結構
"""

from dataclasses import dataclass
from typing import List, Optional

from gacha.exceptions import InvalidCardIdError

# PostgreSQL integer 類型的範圍限制
MIN_VALID_CARD_ID = 1
MAX_VALID_CARD_ID = 2147483647  # PostgreSQL integer 最大值


# 最愛狀態常數 - 使用常數而非枚舉以支持熱重載
class FavoriteStatusConstants:
    """最愛狀態常數類"""

    ALL = "all"
    FAVORITE_ONLY = "favorite_only"
    NON_FAVORITE_ONLY = "non_favorite_only"

    @classmethod
    def get_valid_values(cls):
        """獲取所有有效的最愛狀態值"""
        return [cls.ALL, cls.FAVORITE_ONLY, cls.NON_FAVORITE_ONLY]

    @classmethod
    def is_valid(cls, value: str) -> bool:
        """檢查是否為有效的最愛狀態值"""
        return value in cls.get_valid_values()


def validate_card_id(card_id: Optional[int]) -> Optional[int]:
    """驗證 card_id 是否在有效範圍內

    Args:
        card_id: 要驗證的卡片ID

    Returns:
        驗證通過的 card_id 或 None

    Raises:
        InvalidCardIdError: 當 card_id 超出有效範圍時
    """
    if card_id is None:
        return None

    if not isinstance(card_id, int):
        raise InvalidCardIdError(
            message=f"卡片ID必須是整數，收到: {type(card_id).__name__}"
        )

    if card_id < MIN_VALID_CARD_ID or card_id > MAX_VALID_CARD_ID:
        raise InvalidCardIdError(
            card_id=card_id,
            message=f"卡片ID {card_id} 超出有效範圍（{MIN_VALID_CARD_ID} - {MAX_VALID_CARD_ID}）",
        )

    return card_id


@dataclass
class CollectionFilters:
    """卡片收藏篩選條件數據類

    封裝所有可能的卡片收藏篩選條件，提高可擴展性和可維護性
    """

    # 排序選項（使用統一的默認配置）
    sort_by: str = "rarity"
    sort_order: str = "desc"

    # 卡池類型篩選（例如：'main', 'special'）
    pool_type: Optional[str] = None

    # 卡池類型複選篩選（例如：['main', 'special']）
    pool_type_in: Optional[List[str]] = None

    # 最愛狀態篩選: 'all', 'favorite_only', 'non_favorite_only'
    favorite_status: str = FavoriteStatusConstants.ALL

    # 稀有度篩選列表（例如：[1, 2]）- 使用數字稀有度
    rarity_in: Optional[List[int]] = None

    # 系列篩選
    series: Optional[str] = None

    # 數量篩選（大於指定值）
    quantity_greater_than: Optional[int] = None

    # 卡片ID精確篩選
    card_id: Optional[int] = None

    # 卡片名稱模糊篩選
    card_name: Optional[str] = None

    # 是否只包含重複的卡片（數量大於1）
    # 注意：在賣卡命令中，推薦使用 operation=leave_one 代替此屬性
    only_duplicates: bool = False

    # 是否保留每種卡片的一張
    leave_one: bool = False

    # 稀有度排除列表（例如：[7] 排除 TS）- 使用數字稀有度
    rarity_not_in: Optional[List[int]] = None

    # 系列排除
    series_not_in: Optional[List[str]] = None

    # 用於擴展的字典，可以存儲未來可能添加的篩選條件
    extra_filters: Optional[dict] = None

    def __post_init__(self):
        """初始化後處理，驗證 card_id 並確保列表類型的屬性為空列表而非None"""
        # 驗證 card_id
        if self.card_id is not None:
            self.card_id = validate_card_id(self.card_id)

        # 驗證 favorite_status
        if not FavoriteStatusConstants.is_valid(self.favorite_status):
            self.favorite_status = FavoriteStatusConstants.ALL

        # 確保列表類型的屬性為空列表而非None
        if self.rarity_in is None:
            self.rarity_in = []

        if self.rarity_not_in is None:
            self.rarity_not_in = []

        if self.series_not_in is None:
            self.series_not_in = []

        if self.pool_type_in is None:
            self.pool_type_in = []

        if self.extra_filters is None:
            self.extra_filters = {}

    def set_card_id(self, card_id: Optional[int]) -> None:
        """安全設置 card_id，自動進行驗證

        Args:
            card_id: 要設置的卡片ID

        Raises:
            ValueError: 當 card_id 超出有效範圍時
        """
        self.card_id = validate_card_id(card_id)

    def has_any_filter(self) -> bool:
        """檢查是否有除了最愛篩選之外的任何篩選條件被設置"""
        # 檢查主要篩選屬性
        if (
            self.pool_type is not None
            or (self.pool_type_in and len(self.pool_type_in) > 0)
            or (self.rarity_in and len(self.rarity_in) > 0)
            or self.series is not None
            or self.quantity_greater_than is not None
            or self.card_id is not None
            or self.card_name is not None
            or self.only_duplicates is True  # 假設 only_duplicates=False 是無篩選
            or (self.rarity_not_in and len(self.rarity_not_in) > 0)
            or (self.series_not_in and len(self.series_not_in) > 0)
            or self.favorite_status != "all"
        ):
            return True

        # 檢查 extra_filters 是否有最愛篩選以外的鍵
        if self.extra_filters:
            for key, value in self.extra_filters.items():
                if key not in [
                    "exclude_favorites",
                    "include_favorites",
                    "include_favorites_only",
                ]:
                    # 假設任何其他鍵且其值不為 None 表示篩選器處於活動狀態
                    if value is not None:
                        return True
        return False
