"""
Captcha Service
處理與驗證碼相關的所有邏輯，以供不同的 cogs 使用。
"""

from typing import Awaitable, Callable, Optional, Union

import discord
from discord import File
from discord.ext.commands import AutoShardedBot, Bot

from database.postgresql.async_manager import get_redis_client
from gacha.exceptions import CaptchaPendingError, IncorrectCaptchaError
from gacha.utils.captcha_utils import generate_captcha
from gacha.views.captcha_view import CaptchaView

BotType = Union[Bot, AutoShardedBot]


class CaptchaService:
    def __init__(
        self,
        bot: BotType,
        cog_name: str,
        user_id: int,
        on_correct: Callable[[discord.Interaction], Awaitable[bool]],
        on_final_fail: Callable[[discord.Interaction], Awaitable[bool]],
        on_timeout: Callable[[], Awaitable[None]],
        on_refresh: Optional[
            Callable[
                [discord.Interaction, "CaptchaView"],
                Awaitable[Optional[discord.File]],
            ]
        ] = None,
        max_fails: int = 3,
    ):
        self.bot = bot
        self.user_id = user_id
        self.on_correct = on_correct
        self.on_final_fail = on_final_fail
        self.on_timeout = on_timeout
        self.on_refresh = on_refresh
        self.max_fails = max_fails

        self.CAPTCHA_KEY_PREFIX = f"gacha:captcha:{cog_name}:"
        self.CAPTCHA_FAIL_KEY_PREFIX = f"gacha:captcha:fail:{cog_name}:"
        self.CAPTCHA_PENDING_KEY_PREFIX = f"gacha:captcha:pending:{cog_name}:"

    async def is_captcha_pending(self) -> bool:
        """檢查使用者是否有待處理的驗證碼"""
        redis_client = get_redis_client()
        if not redis_client:
            return False
        pending_key = f"{self.CAPTCHA_PENDING_KEY_PREFIX}{self.user_id}"
        return await redis_client.exists(pending_key)

    async def set_captcha_pending(self, pending: bool):
        """設定或清除使用者的驗證碼待處理狀態"""
        redis_client = get_redis_client()
        if not redis_client:
            return
        pending_key = f"{self.CAPTCHA_PENDING_KEY_PREFIX}{self.user_id}"
        if pending:
            await redis_client.setex(pending_key, 300, "1")  # 5分鐘後過期
        else:
            await redis_client.delete(pending_key)

    async def handle_captcha(self, interaction: discord.Interaction):
        """處理驗證碼流程"""
        if await self.is_captcha_pending():
            raise CaptchaPendingError()

        await self.set_captcha_pending(True)

        redis_client = get_redis_client()
        if not redis_client:
            raise RuntimeError("Redis client is not available for captcha.")

        captcha_text, image_io = generate_captcha()
        captcha_key = f"{self.CAPTCHA_KEY_PREFIX}{self.user_id}"
        await redis_client.setex(captcha_key, 120, captcha_text)

        embed = discord.Embed(
            title="🤖 請進行安全驗證",
            description="為了確認您是人類，請點擊下方按鈕並輸入圖片中的文字。",
            color=discord.Color.orange(),
        )
        embed.set_image(url="attachment://captcha.png")

        view = CaptchaView(
            bot=self.bot,
            user_id=self.user_id,
            correct_answer=captcha_text,
            on_correct=self._on_correct_wrapper,
            on_incorrect=self._on_incorrect_wrapper,
            on_timeout=self._on_timeout_wrapper,
            on_refresh=self._on_refresh_wrapper if self.on_refresh else None,
        )

        message = await interaction.followup.send(
            embed=embed,
            file=File(fp=image_io, filename="captcha.png"),
            view=view,
            ephemeral=False,
        )
        view.message = message

    async def _on_correct_wrapper(self, interaction: discord.Interaction) -> bool:
        await self.set_captcha_pending(False)
        return await self.on_correct(interaction)

    async def _on_incorrect_wrapper(self, interaction: discord.Interaction) -> bool:
        redis_client = get_redis_client()
        if not redis_client:
            # 如果沒有 redis，直接觸發 on_final_fail，並假設這是最大失敗次數
            await self.set_captcha_pending(False)
            return await self.on_final_fail(interaction)

        fail_key = f"{self.CAPTCHA_FAIL_KEY_PREFIX}{self.user_id}"
        current_fails = await redis_client.incr(fail_key)
        await redis_client.expire(fail_key, 300)

        if current_fails >= self.max_fails:
            await self.set_captcha_pending(False)
            return await self.on_final_fail(interaction)
        else:
            remaining_attempts = self.max_fails - current_fails
            raise IncorrectCaptchaError(remaining_attempts=remaining_attempts)

    async def _on_timeout_wrapper(self):
        await self.set_captcha_pending(False)
        await self.on_timeout()

    async def _on_refresh_wrapper(
        self, interaction: discord.Interaction, view: "CaptchaView"
    ) -> Optional[discord.File]:
        redis_client = get_redis_client()
        if not redis_client:
            await interaction.response.send_message(
                "無法刷新驗證碼，請稍後再試。", ephemeral=True
            )
            return None

        new_captcha_text, new_image_io = generate_captcha()
        new_captcha_key = f"{self.CAPTCHA_KEY_PREFIX}{self.user_id}"
        await redis_client.setex(new_captcha_key, 120, new_captcha_text)

        view.correct_answer = new_captcha_text

        if self.on_refresh:
            await self.on_refresh(interaction, view)

        return File(fp=new_image_io, filename="captcha.png")
