import json
import logging
import os
from typing import Optional

import asyncpg
import redis.asyncio as redis
from redis.asyncio import Redis

from config.app_config import get_settings
from database.redis.config import is_redis_enabled
from utils.singleton import SingletonType

logger = logging.getLogger(__name__)


class _DatabaseManager(metaclass=SingletonType):
    """
    一個內部單例類，用於管理整個應用程式的數據庫和 Redis 連接。
    不應直接從外部導入。
    """

    def __init__(self):
        """初始化 DatabaseManager 的實例變量。"""
        self.pool: Optional[asyncpg.Pool] = None
        self.redis_client: Optional[Redis] = None
        logger.info("DatabaseManager Singleton instance created.")

    async def _init_db_connection(self, conn):
        """初始化每個新的數據庫連接"""
        try:
            await conn.set_type_codec(
                "json", encoder=json.dumps, decoder=json.loads, schema="pg_catalog"
            )
            await conn.set_type_codec(
                "jsonb", encoder=json.dumps, decoder=json.loads, schema="pg_catalog"
            )
        except Exception as e:
            logger.warning("設置 JSON/JSONB 編解碼器失敗: %s", e)

    async def setup_connections(self):
        """初始化並建立所有資料庫和緩存連接。"""
        if self.pool and not self.pool.is_closing():
            logger.info("連接已存在，跳過重複初始化")
            return

        # --- PostgreSQL ---
        try:
            settings = get_settings()
            if not settings.DATABASE_URL:
                raise ValueError("DATABASE_URL is not configured in settings.")

            self.pool = await asyncpg.create_pool(
                dsn=settings.DATABASE_URL,
                min_size=settings.database.pool_min_size,
                max_size=settings.database.pool_max_size,
                init=self._init_db_connection,
            )
            logger.info(
                "✅ PostgreSQL 連接池初始化成功 (Host: %s, DB: %s)",
                settings.PG_HOST,
                settings.GACHA_DB_NAME,
            )
        except Exception as e:
            logger.error("❌ 初始化 PostgreSQL 連接池失敗: %s", e, exc_info=True)
            raise RuntimeError(f"無法初始化資料庫連接池: {e}") from e

        # --- Redis ---
        try:
            if is_redis_enabled():
                redis_url = os.environ.get("REDIS_URL")
                if not redis_url:
                    redis_host = os.environ.get("REDIS_HOST", "localhost")
                    redis_port = int(os.environ.get("REDIS_PORT", "6379"))
                    redis_db = int(os.environ.get("REDIS_DB", "0"))
                    redis_password = os.environ.get("REDIS_PASSWORD", "")
                    if redis_password:
                        redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
                    else:
                        redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"

                self.redis_client = redis.from_url(
                    redis_url, decode_responses=False, health_check_interval=30.0
                )
                await self.redis_client.ping()
                logger.info("✅ Redis 客戶端初始化成功")
            else:
                self.redis_client = None
                logger.info("⚠️ Redis 已在配置中被禁用")
        except Exception as e:
            logger.error("❌ 初始化 Redis 失敗: %s", e, exc_info=True)
            self.redis_client = None

    async def close_connections(self):
        """安全關閉所有連接。"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("PostgreSQL 連接池已關閉")
        if self.redis_client:
            await self.redis_client.aclose()
            self.redis_client = None
            logger.info("Redis 連接已關閉")


# 創建內部單例的實例
_db_manager = _DatabaseManager()

# --- 公共接口函數 ---


async def setup_connections():
    """初始化並建立所有資料庫和緩存連接。"""
    await _db_manager.setup_connections()


async def close_connections():
    """安全關閉所有連接。"""
    await _db_manager.close_connections()


def get_pool() -> asyncpg.Pool:
    """獲取已初始化的 asyncpg 連接池"""
    if _db_manager.pool is None:
        raise RuntimeError("Asyncpg 連接池未初始化。請先調用 setup_connections()")
    return _db_manager.pool


def get_redis_client() -> Optional[Redis]:
    """獲取 Redis 客戶端"""
    return _db_manager.redis_client
