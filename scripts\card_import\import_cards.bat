@echo off
chcp 65001 >nul
cd /d "%~dp0"
echo ========================================
echo           卡片匯入工具
echo ========================================
echo 當前工作目錄: %CD%
echo.

:menu
echo 請選擇要執行的操作：
echo.
echo 1. 匯入 Hololive 卡片
echo 2. 匯入 Pokemon TCG 卡片
echo 3. 匯入 Union Arena 卡片
echo 4. 匯入 WIXOSS 卡片
echo 5. 導出所有卡片（按卡池分類）
echo 6. 預下載卡片圖片
echo 7. 退出
echo.

set /p choice="請輸入選項 (1-7): "

if "%choice%"=="1" goto hololive
if "%choice%"=="2" goto pokemon
if "%choice%"=="3" goto union_arena
if "%choice%"=="4" goto wixoss
if "%choice%"=="5" goto export
if "%choice%"=="6" goto download_images
if "%choice%"=="7" goto exit
echo 無效選項，請重新選擇。
echo.
goto menu

:hololive
echo.
echo 正在匯入 Hololive 卡片...
python scripts\import_cards.py hololive data\hololive_card.json hololive
echo.
pause
goto menu

:pokemon
echo.
echo 正在匯入 Pokemon TCG 卡片...
python scripts\import_cards.py ptcg data\pokemon_cards_jp_updated.json ptcg
echo.
pause
goto menu

:union_arena
echo.
echo 正在匯入 Union Arena 卡片...
python scripts\import_cards.py ua data\union_arena_data.json ua
echo.
pause
goto menu

:wixoss
echo.
echo 正在匯入 WIXOSS 卡片...
python scripts\import_wixoss_cards.py data\wixoss_simplified.json
echo.
pause
goto menu

:export
echo.
echo 正在導出所有卡片...
python scripts\export_cards_by_pool.py
echo.
pause
goto menu

:download_images
echo.
echo 正在預下載卡片圖片...
python scripts\pre_download_master_card_images.py
echo.
pause
goto menu

:exit
echo 再見！
pause
