gacha_core_settings:
  # gacha_settings.yaml
  # Gacha 核心玩法配置

  # 活躍度監控
  activity_monitoring:


    # 精確定時檢測設定
    precise_timing_detection:
      enabled: true                    # 是否啟用精確定時檢測
      target_interval: 3600            # 目標間隔時間（秒），預設為1小時
      tolerance: 20                    # 容忍度（秒），±20秒內視為精確定時
      consecutive_required: 2          # 需要連續多少個精確間隔才觸發（2個間隔=3次記錄）
      command_name: "hourly"           # 要檢測的指令名稱
    # 全域黑名單，名單中的使用者無法使用任何指令
    global_blacklist:
      # 從 fraud_analysis/blacklist.json 新增的小號
      - 1398296602808160339
      - 195182001639129088
      - 240838768259497984
      - 362570975616827392
      - 371185926396706816
      - 377451890985664533
      - 432168507577335828
      - 462469134903541760
      - 504950909349003264
      - 512271003293057036
      - 536093098409721856
      - 543843844907728899
      - 563957538647965719
      - 571865614365491200
      - 586333316022796328
      - 600182110820499457
      - 620587719617413170
      - 624834028092129310
      - 661788402353569822
      - 668961608898445357
      - 692339741496180736
      - 715468297377677362
      - 731161167929802792
      - 731694946465218661
      - 732262674896781334
      - 769115846936363019
      - 776079810555674644
      - 777139354916225024
      - 781529981112156170
      - 814162510029520927
      - 846213659667464272
      - 849579356229337118
      - 852051132750364693
      - 853257619199098900
      - 854071479079665674
      - 854073598124752906
      - 861441595659649044
      - 861442605526155285
      - 864544839341309954
      - 870286998496219257
      - 871601422041509898
      - 893703629763141672
      - 898970655029088276
      - 904921536274780200
      - 905009115196162079
      - 905085357647880253
      - 905457689063202856
      - 905475263809347625
      - 910890129567154256
      - 911899134615756850
      - 917391407524741163
      - 930957540836126761
      - 931799232606781451
      - 931820769707692033
      - 932199075728740383
      - 941181776611659837
      - 957282424142856202
      - 962239467664605195
      - 964477267847827547
      - 967035484910026782
      - 968082643180736523
      - 971414226998534217
      - 976409604948885524
      - 982514593198911529
      - 1000658628572885022
      - 1048275084940423188
      - 1057585921169559622
      - 1057902175340212235
      - 1060222691187097620
      - 1063062499747975249
      - 1066365829257637948
      - 1080151605527195728
      - 1080447576098209812
      - 1080699513590779974
      - 1084089812975374477
      - 1088023965412380702
      - 1104664235499327548
      - 1105504151376511058
      - 1105511120854323291
      - 1109847285912838195
      - 1114962366732435508
      - 1120625389358829629
      - 1125091398677438554
      - 1127123842201042946
      - 1127872097331466322
      - 1130402622998859816
      - 1130429870552404048
      - 1130432766933225523
      - 1130486762414800937
      - 1130490521693978685
      - 1131652646349062304
      - 1137810906462572725
      - 1142256275489575033
      - 1146346311516438600
      - 1157280942721880126
      - 1160444083932770325
      - 1161592760332197949
      - 1163903673118760982
      - 1166970218850025504
      - 1170262550194163715
      - 1173224863369023562
      - 1182474410653921450
      - 1191600548844163194
      - 1194647039041081394
      - 1212711187310190612
      - 1217113606815223871
      - 1217324215351509124
      - 1233048671424217092
      - 1235229512799617057
      - 1238128076500504679
      - 1238129499061883042
      - 1243906347607789579
      - 1243906360710660129
      - 1243906788911353911
      - 1243906827033382998
      - 1243907399149031508
      - 1243907709791633412
      - 1243907806323675219
      - 1389664883016073296
      - 1243907864070983722
      - 1243907867099136090
      - 1243907957721141364
      - 1248991446669983895
      - 1249330519561207839
      - 1252576616580911106
      - 1263978429192208424
      - 1272539196598517870
      - 1284905985047003251
      - 1285931935574786110
      - 1292784461947211797
      - 1294333651231703195
      - 1298555393550843937
      - 1304448256310251540
      - 1320959595228958790
      - 1323274144024104980
      - 1324754040936202292
      - 1324938272497078407
      - 1330504188601569311
      - 1333644464853483620
      - 1336286727228096588
      - 1339025740209066118
      - 1341797344387137657
      - 1348093075171446835
      - 1363861815146315882
      - 1364960362096627784
      - 1375074734726709252
      - 1376940122339020833
      - 1377227987753242655
      - 1381242842017566841
      - 1381247048292110336
      - 1382233098451943565
      - 1382641547769675888
      - 1383426591450992650
      - 1383427323923140779
      - 1383427798835793953
      - 1383427832998264943
      - 1383427848022266000
      - 1383711825547366430
      - 1383712348996636782
      - 1383714694598103080
      - 1383715441100328992
      - 1383716358436425799
      - 1383716375838593170
      - 1384499090544529500
      - 1384529283632074975
      - 1384543695952416920
      - 1384750451982864496
      - 1384783562133209190
      - 1384785621230293002
      - 1384786927714304092
      - 1384787938357018726
      - 1384818915644936236
      - 1384820821817823417
      - 1384883189054574625
      - 1385512832325386300
      - 1385601271762911455
      - 1385602682529775648
      - 1385814487545811025
      - 1385816278266155120
      - 1385951334464749618
      - 1385952220138180608
      - 1386002795131441162
      - 1386030597310513174
      - 1386031500352553013
      - 1386032237140639795
      - 1386034922527002745
      - 1386036256630378630
      - 1386037089845641379
      - 1386037471464128576
      - 1386043513623548016
      - 1386206193541976067
      - 1386207391111970897
      - 1386348856140107817
      - 1386524459392958515
      - 1386540594255364096
      - 1386560399561986078
      - 1386598185144680590
      - 1386601402855329792
      - 1386862080098959470
      - 1386863027407683727
      - 1386865900707119136
      - 1387016733776285758
      - 1387115071653544039
      - 1387278043873280114
      - 1387740335068811306
      - 1387797497891524639
      - 1387801641461350420
      - 1387806956017942672
      - 1387808427064955012
      - 1387815562922688693
      - 1387827419339165868
      - 1388009095604338758
      - 1388011554540355606
      - 1388013019711410288
      - 1388019937670467614
      - 1392050900339654800
      - 1388038143164551320
      - 1388040783709601873
      - 1388044574722887821
      - 1388047539890032721
      - 1388057024905871422
      - 1388132017723543653
      - 1388388569743229069
      - 1388580528336404550
      - 1388583962246385795
      - 1388724846103629916
      - 1388840667370881034
      - 1389534480410349609
      - 1389536008994947162
      - 1389537562208632856
      - 1389538513510203493
      - 1389539360248168641
      - 1389544572904018100
      - 1389573587807109192
      - 1389581715676332033
      - 1389588599456010311
      - 1389591741652533288
      - 1389624675256111176
      - 1389636412080390274
      - 1389644648401866822
      - 1389646011647135774
      - 1389660425917829292
      - 1389662303032447026
      - 1389663359712100604
      - 1389942346346659910
      - 1390574635027796047
      - 1391221319290458313
      - 1391296646654070896
      - 1391296647291736135
      - 1389641998071234803
      - 1391300603686158376
      - 1394320055273263205
      - 1391300798578823259
      - 1391460030796271707
      - 1391756091691499574
    # 交易功能黑名單，名單中的使用者無法使用交易相關指令（transfer、trade、poker1v1）
    trading_blacklist: # 示例用户ID，可以根据需要添加更多
      - 1369932190611148810
      - 1391134019130884247
      - 1389616506303942798
      - 1388343237059805297
      - 1388349135232569465
      - 1388350195422724217
      - 1388351362776957070
      - 1388847043061088418
      - 1388837330336354345
      - 1388838252181458964
      - 1388840318375301141
      - 1391799505115938967
      - 1388938890240589895
      - 1389799344781135883

  # 稀有度排序值 (數值越大，稀有度越高)
  rarity_sort_values:
    1: 1  # COMMON
    2: 2  # RARE
    3: 3  # SUPER_RARE
    4: 4  # SSR
    5: 5  # ULTRA_RARE
    6: 6  # LEGENDARY_RARE
    7: 7  # EXCLUSIVE

  # 星級相關
  max_star_level: 35
  stars_per_tier: 5
# 交易手續費設定
  trade_fee_percentage: 0.03  # 3% 手續費，無最低限制

  # 按卡池和稀有度的統一售價
  pool_rarity_prices:
    main:
      1: 4
      2: 6
      3: 20
      4: 600
      5: 1500
      6: 12000
      7: 55000
    special:
      1: 7
      2: 50
      3: 400
      4: 13000
      5: 42000
    summer:
      1: 3
      2: 12
      3: 50
      4: 800
      5: 12000
      6: 40000
    vd:
      1: 3
      2: 12
      3: 70
      4: 1000
      5: 15000
      6: 45000
    special_maid:
      1: 4
      2: 80
      3: 500
      4: 17000
      5: 50000
    hololive:
      1: 4
      2: 12
      3: 80
      4: 800
      5: 7000
      6: 22000
      7: 60000
    ua: # Correctly placed under pool_rarity_prices
      1: 3
      2: 10
      3: 70
      4: 750
      5: 7000
      6: 20000
      7: 50000
    ptcg: # Pokemon Trading Card Game卡池
      1: 3
      2: 8
      3: 50
      4: 500
      5: 4000
      6: 15000
      7: 35000
    wixoss: # WIXOSS卡池
      1: 4
      2: 15
      3: 80
      4: 1200
      5: 8000
      6: 25000
      7: 65000
    ongeki: # Ongeki卡池
      1: 4
      2: 50
      3: 800
      4: 18000
      5: 50000
    sve: # Shadowverse Evolve卡池 - 根據平方根增長溢價模型
      1: 4      # R1 - 64.44% 機率
      2: 15     # R2 - 25.00% 機率
      3: 80     # R3 - 8.00% 機率
      4: 1500   # R4 - 2.00% 機率
      5: 9000   # R5 - 0.50% 機率
      6: 28000  # R6 - 0.05% 機率
      7: 70000  # R7 - 0.01% 機率

  # 卡池類型名稱
  pool_type_names:
    main: '主卡池'
    special: '典藏卡池'
    summer: '泳裝卡池'
    vd: '情人節卡池'
    special_maid: '典藏女僕卡池'
    hololive: 'Hololive卡池' #
    ua: 'UNION ARENA卡池'
    ptcg: 'Pokemon TCG卡池'
    wixoss: 'WIXOSS卡池'
    ongeki: '音擊卡池'
    sve: 'Shadowverse Evolve卡池'

  # 卡池類型前綴
  pool_type_prefixes:
    main: '普通'
    special: '典藏'
    summer: '泳裝'
    vd: '情人節'
    special_maid: '典藏女僕'
    hololive: 'Hololive'
    ua: 'UNION ARENA'
    ptcg: 'Pokemon TCG'
    wixoss: 'WIXOSS'
    ongeki: '音擊'
    sve: 'Shadowverse Evolve'

  # 卡池抽卡成本
  pool_costs:
    main: 50
    special: 140
    summer: 130
    vd: 150
    special_maid: 160
    hololive: 150
    ua: 120
    ptcg: 90
    wixoss: 140
    ongeki: 140
    sve: 170  # Shadowverse Evolve 卡池成本
    all: 50 # 混合池成本

  # 各卡池稀有度及概率配置
  all_pool_rarity_configs:
    main:
      - [1, 68.74]
      - [2, 25.00]
      - [3, 4.50]
      - [4, 1.00]
      - [5, 0.70]
      - [6, 0.05]
      - [7, 0.01]
    special:
      - [1, 82.80]
      - [2, 15.00]
      - [3, 2.00]
      - [4, 0.15]
      - [5, 0.05]
    summer:
      - [1, 61.80]
      - [2, 25.00]
      - [3, 10.00]
      - [4, 3.00]
      - [5, 0.15]
      - [6, 0.05]
    vd:
      - [1, 61.80]
      - [2, 25.00]
      - [3, 10.00]
      - [4, 3.00]
      - [5, 0.15]
      - [6, 0.05]
    special_maid:
      - [1, 82.80]
      - [2, 15.00]
      - [3, 2.00]
      - [4, 0.15]
      - [5, 0.05]
    hololive:
      - [1, 64.44]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.00]
      - [5, 0.50]
      - [6, 0.05]
      - [7, 0.01]
    ua: 
      - [1, 64.44]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.00]
      - [5, 0.50]
      - [6, 0.05]
      - [7, 0.01]
    ptcg:
      - [1, 63.84]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.50]
      - [5, 0.60]
      - [6, 0.05]
      - [7, 0.01]
    wixoss:
      - [1, 64.44]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.00]
      - [5, 0.50]
      - [6, 0.05]
      - [7, 0.01]
    ongeki:
      - [1, 82.80]
      - [2, 15.00]
      - [3, 2.00]
      - [4, 0.15]
      - [5, 0.05]
    sve: # Shadowverse Evolve 卡池機率配置
      - [1, 64.44]  # R1 - 64.44% 機率
      - [2, 25.00]  # R2 - 25.00% 機率
      - [3, 8.00]   # R3 - 8.00% 機率
      - [4, 2.00]   # R4 - 2.00% 機率
      - [5, 0.50]   # R5 - 0.50% 機率
      - [6, 0.05]   # R6 - 0.05% 機率
      - [7, 0.01]   # R7 - 0.01% 機率

  # 混合抽卡卡池概率配置
  mixed_pool_draw_config:
    - ["main", 99.00]
    - ["special", 1.00]
    - ["summer", 0.00]
    - ["vd", 0.00]
    - ["special_maid", 0.00]
    - ["hololive", 0.00]
    - ["ua", 0.00]
    - ["ptcg", 0.00]
    - ["wixoss", 0.00]

  # 卡池組合配置 (用於命令界面)
  pool_configurations:
    main:
      name: "主卡池"
      description: "所有常駐的基礎卡片。"
      pools: ["main"]
    all:
      name: "混合池 (50油)"
      description: "混合所有可用卡片的標準卡池。"
      pools: ["main", "special"]
    special:
      name: "典藏卡池 (140油)"
      description: "包含稀有和限定卡片的特殊卡池。"
      pools: ["special"]
    summer:
      name: "泳裝卡池 (130油)"
      description: "夏季主題限定卡池。"
      pools: ["summer"]
    vd:
      name: "情人節卡池 (150油)"
      description: "情人節主題限定卡池。"
      pools: ["vd"]
    special_maid:
      name: "典藏女僕卡池 (160油)"
      description: "特殊女僕主題限定卡池。"
      pools: ["special_maid"]
    hololive:
      name: "Hololive 卡池 (150油)"
      description: "Hololive 聯動主題限定卡池。"
      pools: ["hololive"]
    ua: 
      name: "UNION ARENA 卡池 (120油)"
      description: "UNION ARENA 主題限定卡池。"
      pools: ["ua"]
    ptcg:
      name: "Pokemon TCG 卡池 (90油)"
      description: "Pokemon Trading Card Game 主題限定卡池。"
      pools: ["ptcg"]
    wixoss:
      name: "WIXOSS 卡池 (140油)"
      description: "WIXOSS 主題限定卡池。"
      pools: ["wixoss"]
    ongeki:
      name: "音擊 卡池 (140油)"
      description: "音擊 主題限定卡池。"
      pools: ["ongeki"]
    sve:
      name: "Shadowverse Evolve 卡池 (170油)"
      description: "Shadowverse Evolve 主題限定卡池。"
      pools: ["sve"]


  # 各卡池可用稀有度列表
  pool_rarity_mapping:
    main: [1, 2, 3, 4, 5, 6, 7]
    special: [1, 2, 3, 4, 5]
    summer: [1, 2, 3, 4, 5, 6]
    vd: [1, 2, 3, 4, 5, 6]
    special_maid: [1, 2, 3, 4, 5]
    hololive: [1, 2, 3, 4, 5, 6, 7]
    ua: [1, 2, 3, 4, 5, 6, 7]
    ptcg: [1, 2, 3, 4, 5, 6, 7]
    wixoss: [1, 2, 3, 4, 5, 6, 7]
    ongeki: [1, 2, 3, 4, 5]
    sve: [1, 2, 3, 4, 5, 6, 7]

  # 票券商店物品定義
  ticket_shop_items:
    # 主卡池 (main) 票券
    main_R7_specific:
      display_name: "主卡池 EX 指定券"
      description: "從主卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 84000 # 舊: 75000
      pool_type: 'main'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 10
    main_R7_random:
      display_name: "主卡池 EX 隨機券"
      description: "從主卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 5000
      pool_type: 'main'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 11
    main_R6_specific:
      display_name: "主卡池 LR 指定券"
      description: "從主卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 35100 # 舊: 15000
      pool_type: 'main'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 20
    main_R6_random:
      display_name: "主卡池 LR 隨機券"
      description: "從主卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 1000
      pool_type: 'main'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 21
    
    # 典藏卡池 (special) 票券
    special_R5_specific:
      display_name: "典藏卡池 UR 指定券"
      description: "從典藏卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 24360 # 上次: 22400
      pool_type: 'special'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 30
    special_R5_random:
      display_name: "典藏卡池 UR 隨機券"
      description: "從典藏卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 2800
      pool_type: 'special'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 31
    special_R4_specific:
      display_name: "典藏卡池 SSR 指定券"
      description: "從典藏卡池中選擇一張SSR稀有度的卡片。"
      cost_oil_tickets: 11395 # 上次: 10180
      pool_type: 'special'
      rarity: 4
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 40
    special_R4_random:
      display_name: "典藏卡池 SSR 隨機券"
      description: "從典藏卡池中隨機獲得一張SSR稀有度的卡片。"
      cost_oil_tickets: 934
      pool_type: 'special'
      rarity: 4
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 41
    
    # 泳裝卡池 (summer) 票券
    summer_R6_specific:
      display_name: "泳裝卡池 LR 指定券"
      description: "從泳裝卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 32240 # 舊: 39000
      pool_type: 'summer'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 50
    summer_R6_random:
      display_name: "泳裝卡池 LR 隨機券"
      description: "從泳裝卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 2600
      pool_type: 'summer'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 51
    summer_R5_specific:
      display_name: "泳裝卡池 UR 指定券"
      description: "從泳裝卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 18380 # 舊: 13005
      pool_type: 'summer'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 60
    summer_R5_random:
      display_name: "泳裝卡池 UR 隨機券"
      description: "從泳裝卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 867
      pool_type: 'summer'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 61
    
    # 情人節卡池 (vd) 票券
    vd_R6_specific:
      display_name: "情人節卡池 LR 指定券"
      description: "從情人節卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 27000 # 舊: 45000
      pool_type: 'vd'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 70
    vd_R6_random:
      display_name: "情人節卡池 LR 隨機券"
      description: "從情人節卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 3000
      pool_type: 'vd'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 71
    vd_R5_specific:
      display_name: "情人節卡池 UR 指定券"
      description: "從情人節卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 13500 # 舊: 15000
      pool_type: 'vd'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 80
    vd_R5_random:
      display_name: "情人節卡池 UR 隨機券"
      description: "從情人節卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 1000
      pool_type: 'vd'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 81
    
    # 典藏女僕卡池 (special_maid) 票券
    special_maid_R5_specific:
      display_name: "典藏女僕卡池 UR 指定券"
      description: "從典藏女僕卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 13120 # 舊: 48000
      pool_type: 'special_maid'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 90
    special_maid_R5_random:
      display_name: "典藏女僕卡池 UR 隨機券"
      description: "從典藏女僕卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 3200
      pool_type: 'special_maid'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 91
    special_maid_R4_specific:
      display_name: "典藏女僕卡池 SSR 指定券"
      description: "從典藏女僕卡池中選擇一張SSR稀有度的卡片。"
      cost_oil_tickets: 4375  # 舊: 16005
      pool_type: 'special_maid'
      rarity: 4
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 100
    special_maid_R4_random:
      display_name: "典藏女僕卡池 SSR 隨機券"
      description: "從典藏女僕卡池中隨機獲得一張SSR稀有度的卡片。"
      cost_oil_tickets: 1067
      pool_type: 'special_maid'
      rarity: 4
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 101
    
    # Hololive卡池 (hololive) 票券
    hololive_R7_specific:
      display_name: "Hololive卡池 EX 指定券"
      description: "從Hololive卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 112500 # 上次: 100500
      pool_type: 'hololive'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 110
    hololive_R7_random:
      display_name: "Hololive卡池 EX 隨機券"
      description: "從Hololive卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 15000
      pool_type: 'hololive'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 111
    hololive_R6_specific:
      display_name: "Hololive卡池 LR 指定券"
      description: "從Hololive卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 44400 # 上次: 39000
      pool_type: 'hololive'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 120
    hololive_R6_random:
      display_name: "Hololive卡池 LR 隨機券"
      description: "從Hololive卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 3000
      pool_type: 'hololive'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 121
    
    # UNION ARENA卡池 (ua) 票券
    ua_R7_specific:
      display_name: "UNION ARENA卡池 EX 指定券"
      description: "從UNION ARENA卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 153600 # 舊: 180000
      pool_type: 'ua'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 130
    ua_R7_random:
      display_name: "UNION ARENA卡池 EX 隨機券"
      description: "從UNION ARENA卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 12000
      pool_type: 'ua'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 131
    ua_R6_specific:
      display_name: "UNION ARENA卡池 LR 指定券"
      description: "從UNION ARENA卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 72000  # 舊: 36000
      pool_type: 'ua'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 140
    ua_R6_random:
      display_name: "UNION ARENA卡池 LR 隨機券"
      description: "從UNION ARENA卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 2400
      pool_type: 'ua'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 141
    
    # Pokemon TCG卡池 (ptcg) 票券
    ptcg_R7_specific:
      display_name: "Pokemon TCG卡池 EX 指定券"
      description: "從Pokemon TCG卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 150300 # 舊: 135000
      pool_type: 'ptcg'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 150
    ptcg_R7_random:
      display_name: "Pokemon TCG卡池 EX 隨機券"
      description: "從Pokemon TCG卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 9000
      pool_type: 'ptcg'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 151
    ptcg_R6_specific:
      display_name: "Pokemon TCG卡池 LR 指定券"
      description: "從Pokemon TCG卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 45000  # 舊: 27000
      pool_type: 'ptcg'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 160
    ptcg_R6_random:
      display_name: "Pokemon TCG卡池 LR 隨機券"
      description: "從Pokemon TCG卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 1800
      pool_type: 'ptcg'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 161

    # WIXOSS卡池 (wixoss) 票券
    wixoss_R7_specific:
      display_name: "WIXOSS卡池 EX 指定券"
      description: "從WIXOSS卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 152600 # 舊: 210000
      pool_type: 'wixoss'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 170
    wixoss_R7_random:
      display_name: "WIXOSS卡池 EX 隨機券"
      description: "從WIXOSS卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 14000
      pool_type: 'wixoss'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 171
    wixoss_R6_specific:
      display_name: "WIXOSS卡池 LR 指定券"
      description: "從WIXOSS卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 63000  # 舊: 42000
      pool_type: 'wixoss'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 180
    wixoss_R6_random:
      display_name: "WIXOSS卡池 LR 隨機券"
      description: "從WIXOSS卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 2800
      pool_type: 'wixoss'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 181

    # Ongeki卡池 (ongeki) 票券
    ongeki_R5_specific:
      display_name: "音擊卡池 UR 指定券"
      description: "從Ongeki卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 83500 # V4模型計算: 83,440，取整為 83,500
      pool_type: 'ongeki'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 190
    ongeki_R5_random:
      display_name: "音擊卡池 UR 隨機券"
      description: "從Ongeki卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 2800
      pool_type: 'ongeki'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 191
    ongeki_R4_specific:
      display_name: "音擊卡池 SSR 指定券"
      description: "從Ongeki卡池中選擇一張SSR稀有度的卡片。"
      cost_oil_tickets: 14900 # V4模型計算: 14,928，取整為 14,900
      pool_type: 'ongeki'
      rarity: 4
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 200
    ongeki_R4_random:
      display_name: "音擊卡池 SSR 隨機券"
      description: "從Ongeki卡池中隨機獲得一張SSR稀有度的卡片。"
      cost_oil_tickets: 933
      pool_type: 'ongeki'
      rarity: 4
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 201

    # Shadowverse Evolve卡池 (sve) 票券
    sve_R7_specific:
      display_name: "Shadowverse Evolve卡池 EX 指定券"
      description: "從Shadowverse Evolve卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 314000  # 根據平方根增長溢價模型計算
      pool_type: 'sve'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 210
    sve_R7_random:
      display_name: "Shadowverse Evolve卡池 EX 隨機券"
      description: "從Shadowverse Evolve卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 17000  # 隨機券價格
      pool_type: 'sve'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 211
    sve_R6_specific:
      display_name: "Shadowverse Evolve卡池 LR 指定券"
      description: "從Shadowverse Evolve卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 87000  # 根據平方根增長溢價模型計算
      pool_type: 'sve'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 220
    sve_R6_random:
      display_name: "Shadowverse Evolve卡池 LR 隨機券"
      description: "從Shadowverse Evolve卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 3400  # 隨機券價格
      pool_type: 'sve'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 221

  # 排行榜每頁顯示數量
  leaderboard_items_per_page: 5

  # 排行榜顯示配置
  leaderboard_config:
    rarity:
      title: "稀有度數量排行榜"
      label: "稀有度數量"
      description: "按照稀有度收集數量排名的玩家"
      color: 16766720 # 0xFFD700 (Gold)
      category: "collection"
      default_for_category: true
      pool_filterable: true
    completion:
      title: "圖鑑完成率排行榜"
      label: "圖鑑完成率"
      description: "按照全圖鑑完成率排名的玩家"
      color: 255 # 0x0000FF (Blue)
      category: "collection"
      pool_filterable: true
    draws:
      title: "抽卡次數排行榜"
      label: "抽卡次數"
      description: "按照抽卡次數排名的玩家"
      color: 8388736 # 0x800080 (Purple)
      category: "collection"
      pool_filterable: true
    oil:
      title: "油幣餘額排行榜"
      label: "油幣餘額"
      description: "按照油幣餘額排名的玩家"
      color: 32768 # 0x008000 (Green)
      category: "general"
      default_for_category: true
    collection_unique:
      title: "稀有度收集排行榜"
      label: "稀有度收集"
      description: "按照收集的不同卡片數量排名的玩家"
      color: 32896 # 0x008080 (Teal)
      category: "collection"
      pool_filterable: true
    luck_index:
      title: "🍀 運氣指數排行榜"
      label: "運氣指數"
      description: "綜合玩家出貨率與期望率的指標"
      color: 16705372
      category: "collection"
      pool_filterable: true
    avg_draws:
      title: "🎯 平均出貨抽數排行榜"
      label: "平均出貨抽數"
      description: "平均花費多少抽才出一張頂級卡"
      color: 3447003
      category: "collection"
      pool_filterable: true
    longest_drought:
      title: "🏜️ 最長乾旱排行榜"
      label: "最長乾旱"
      description: "連續未出頂級卡的最大抽數"
      color: 15105570
      category: "collection"
      pool_filterable: true
    portfolio_value:
      title: "總資產價值排行榜"
      label: "總資產價值"
      description: "按照玩家股票總市值排名的玩家"
      color: 3447003 # 0x3498DB (Blue)
      category: "stock_market"
      default_for_category: true
    stock_holding:
      title: "特定股票持有排行榜"
      label: "特定股票持有"
      description: "按照特定股票持有數量排名的玩家"
      color: 3066993 # 0x2ECC71 (Green)
      category: "stock_market"
    trade_volume:
      title: "股票交易總額排行榜"
      label: "股票交易總額"
      description: "按照股票市場交易總金額排名的玩家"
      color: 15844367 # 0xF1C40F (Yellow)
      category: "stock_market"
    trade_count:
      title: "股票交易次數排行榜"
      label: "股票交易次數"
      description: "按照股票市場交易次數排名的玩家"
      color: 15158332 # 0xE74C3C (Red)
      category: "stock_market"
    stock_profit_loss:
      title: "股票總盈虧排行榜"
      label: "股票總盈虧"
      description: "按照股票已實現盈虧排名的玩家（僅計算已完成的交易盈虧）"
      color: 65280 # 0x00FF00 (Lime Green)
      category: "stock_market"
    profile_likes:
      title: "個人檔案讚數排行榜"
      label: "個人檔案讚數"
      description: "按照個人檔案獲得讚數排名的玩家"
      color: 16711935 # 0xFF00FF (Magenta)
      category: "other"
      default_for_category: true
    blackjack:
      title: "21點排行榜"
      label: "21點"
      description: "按照21點遊戲統計數據排名的玩家"
      color: 9442302 # 0x8FBC8F (Dark Sea Green)
      category: "games"
      default_for_category: true
    dice:
      title: "骰子大小排行榜"
      label: "骰子大小"
      description: "按照骰子大小遊戲統計數據排名的玩家"
      color: 15844367 # 0xF1C40F (Yellow)
      category: "games"
    mines:
      title: "尋寶礦區排行榜"
      label: "尋寶礦區"
      description: "按照尋寶礦區遊戲統計數據排名的玩家"
      color: 16711935 # 0xFF00FF (Magenta)
      category: "games"
    tower:
      title: "爬塔遊戲排行榜"
      label: "爬塔遊戲"
      description: "按照爬塔遊戲統計排名的玩家"
      color: 16753920 # 0xFF8C00 (Dark Orange)
      category: "games"
    slot:
      title: "拉霸機排行榜"
      label: "拉霸機"
      description: "按照拉霸機遊戲統計數據排名的玩家"
      color: 16776960 # 0xFFD700 (Gold)
      category: "games"
    poker1v1:
      title: "德州撲克 1v1 排行榜"
      label: "德州撲克 1v1"
      description: "按照德州撲克1v1遊戲統計數據排名的玩家"
      color: 9127187 # 0x8B4513 (SaddleBrown)
      category: "games"
    spin_wheel:
      title: "轉盤遊戲排行榜"
      label: "轉盤遊戲"
      description: "按照轉盤遊戲統計數據排名的玩家"
      color: 5793266 # 0x5865F2 (Discord Blurple)
      category: "games"
    baccarat:
      title: "百家樂排行榜"
      label: "百家樂"
      description: "按照百家樂遊戲統計數據排名的玩家"
      color: 13116726 # 0xC82536 (Red)
      category: "games"

  # --- Wish Service Configs ---
  # 這些值應與 gacha/app_config.py 中 GachaCoreSettings 內的默認值匹配
  # 如果在此處指定，則會覆蓋 Pydantic 模型中的默認值。
  wish_max_slots: 10 # Maximum number of wish slots a user can have.
  wish_max_power_level: 10 # Maximum wish power level a user can reach.
  default_wish_slots: 1 # Default number of wish slots for new users.
  default_wish_power_level: 1 # Default wish power level for new users.
  default_wish_multiplier: 3.0 # Default wish chance multiplier for power level 1.
  wish_slot_costs: # Costs to upgrade to the next wish slot (key is current_slots, value is cost for next).
    1: 10000
    2: 25000
    3: 50000
    4: 100000
    5: 250000
    6: 500000
    7: 1000000
    8: 2000000
    9: 5000000
  wish_power_costs: # Costs to upgrade to the next wish power level (key is current_level, value is cost for next).
    1: 20000
    2: 40000
    3: 80000
    4: 160000
    5: 300000
    6: 500000
    7: 800000
    8: 1200000
    9: 2000000
  wish_power_multipliers: # Wish chance multipliers for each power level.
    1: 3.0
    2: 4.0
    3: 5.0
    4: 6.0
    5: 8.0
    6: 10.0
    7: 12.0
    8: 15.0
    9: 18.0
    10: 20.0

  # --- Economy Service Configs ---
  # 這些值應與 gacha/app_config.py 中 GachaCoreSettings 內的默認值匹配
  economy_daily_reward: 5000 # Amount of oil for daily reward.
  economy_hourly_reward: 2000 # Amount of oil for hourly reward.
  economy_hourly_claim_key_prefix: "gacha:hourly_claim:" # Redis key prefix for hourly claims.

# 個人檔案服務相關設定
profile:
  image_local_base_path: "downloaded_gacha_master_cards" # 個人檔案卡片圖片本地基礎路徑
  # like_cooldown_seconds: 3600 # 按讚冷卻時間 (秒) - 已移至 ProfileService 內部管理
  # profile_cache_ttl: 604800 # 檔案圖片快取TTL (秒) - 已移至 ProfileService 內部管理
  # max_status_length: 150 # 個性簽名最大長度 - 已移至 ProfileService 或 ProfileCog 內部管理

# --- UI Settings (config/ui_settings.yaml) ---
# ui_settings: ... (This section should be in config/ui_settings.yaml, not here)
# Removed ui_settings from gacha_settings.yaml as it's loaded from a separate file.

# --- Other top-level settings (config/config.yaml) ---
# application: ...
# database: ...
# gacha_stock_integration: ...
# market_stats_updater: ...
# price_update_service: ...
# ai_assistant: ...
# Removed these from gacha_settings.yaml as they belong in config/config.yaml

gacha_notification_settings:
  webhook_url: "https://discord.com/api/webhooks/1377699345050374225/MTsvSpcSo5NgcCv5pq_hbzJR6FCK1qgFy5YMDU-xJqXCW82L3zi1JLXDOcU0uzW0k9eC" # Discord Webhook URL，用於發送通知
  # notification_channel_id: 1375058548874149898 # Discord 通知頻道ID (如果 webhook_url 未設定，則使用此項)
  # 設定哪些卡池的哪些稀有度卡片被抽出時需要通知
  # 格式: pool_type: [rarity1, rarity2, ...]
  # 如果某個 pool_type 未被列出，或列表為空，則該卡池的任何稀有度都不會觸發通知
  # 若要通知所有稀有度，可以將列表設置為包含該卡池所有可能的稀有度
  notify_rarities:
    main: [6, 7] # 主卡池通知 UR, LR, EX
    special: [4, 5]   # 典藏卡池通知 SSR, UR
    summer: [5, 6]    # 泳裝卡池通知 UR, LR
    vd: [5, 6]        # 情人節卡池通知 UR, LR
    special_maid: [4, 5] # 典藏女僕卡池通知 SSR, UR
    hololive: [6, 7]  # Hololive 卡池通知 LR, EX
    ua: [6, 7]        # UNION ARENA 卡池通知 LR, EX
    ptcg: [6, 7]      # Pokemon TCG 卡池通知 LR, EX
    wixoss: [6, 7]    # WIXOSS 卡池通知 LR, EX
    ongeki: [4, 5]    # Ongeki 卡池通知 SSR, UR
    sve: [6, 7]       # Shadowverse Evolve 卡池通知 LR, EX

