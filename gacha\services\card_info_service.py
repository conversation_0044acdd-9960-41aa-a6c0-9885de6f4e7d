from dataclasses import dataclass, field
from decimal import Decimal
from typing import Any, List, Optional

from database.postgresql.async_manager import get_pool
from gacha.constants import RarityLevel
from gacha.exceptions import (
    BusinessError,
    CardNotFoundError,
    MarketSystemError,
)
from gacha.models.market_models import StockLifecycleStatus
from gacha.models.models import Card
from gacha.repositories.card import master_card_repository
from utils.logger import logger

# ==================== 資料類別 ====================


@dataclass
class CardMarketStatsData:
    total_owned_quantity: Optional[int] = None
    unique_owner_count: Optional[int] = None
    wishlist_count: Optional[int] = None
    favorite_count: Optional[int] = None


@dataclass
class LinkedStockData:
    asset_symbol: str
    asset_name: str
    current_price: Decimal
    lifecycle_status: StockLifecycleStatus

    @classmethod
    def from_record(cls, record) -> "LinkedStockData":
        return cls(
            asset_symbol=record["asset_symbol"],
            asset_name=record["asset_name"],
            current_price=Decimal(str(record["current_price"])),
            lifecycle_status=StockLifecycleStatus(record["lifecycle_status"]),
        )


@dataclass
class CardInfoData:
    card_id: int
    name: str
    series: Optional[str] = None
    rarity: Optional[int] = None
    pool_type: Optional[str] = None
    current_market_sell_price: Optional[Decimal] = None
    image_url: Optional[str] = None
    description: Optional[str] = None
    market_stats: Optional[CardMarketStatsData] = None
    linked_stocks: List[LinkedStockData] = field(default_factory=list)
    raw_card_data: Optional[Any] = None


# ==================== Helper Functions ====================


async def _find_card_by_query(card_query: str) -> Card:
    """
    根據查詢（ID、名稱或 original_id）查找卡片。
    如果找不到，則拋出 CardNotFoundError。
    """
    card = None
    if card_query.isdigit():
        try:
            card_id = int(card_query)
            card = await master_card_repository.get_card(card_id)
        except ValueError:
            # 如果轉換失敗，當作一般名稱查詢處理
            pass
        except CardNotFoundError as e:
            # 明確地捕捉並重新拋出，確保錯誤類型統一
            raise CardNotFoundError(
                f"找不到卡片 ID: {card_query}", card_id=int(card_query)
            ) from e

    if not card:
        pool = get_pool()
        if pool is None:
            raise MarketSystemError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            raw_data = await conn.fetchrow(
                "SELECT * FROM gacha_master_cards WHERE name = $1 OR original_id = $1 LIMIT 1",
                card_query,
            )
            if raw_data:
                card = Card.from_db_record(raw_data)

    if not card:
        raise CardNotFoundError(f"找不到卡片: {card_query}")
    return card


def _extract_card_properties(card: Card) -> dict:
    """從卡片對象中提取屬性。"""
    rarity_val = None
    if card.rarity is not None:
        if isinstance(card.rarity, RarityLevel):
            rarity_val = card.rarity.value
        elif isinstance(card.rarity, int):
            rarity_val = card.rarity
        else:
            logger.warning(
                "卡片ID %s 的稀有度類型為 %s，預期為 RarityLevel 枚舉。",
                card.card_id,
                type(card.rarity),
            )

    return {
        "card_id": card.card_id,
        "name": card.name,
        "series": getattr(card, "series", None),
        "rarity": rarity_val,
        "pool_type": getattr(card, "pool_type", None),
        "current_market_sell_price": (
            Decimal(str(card.current_market_sell_price))
            if card.current_market_sell_price is not None
            else None
        ),
        "image_url": getattr(card, "image_url", None),
        "description": getattr(card, "description", None),
        "raw_card_data": card,
    }


# ==================== Service 函數 ====================


async def get_card_info_data(card_query: str) -> CardInfoData:
    """獲取卡片信息，包括市場統計和關聯股票。"""
    logger.info("CardInfoService: 正在為查詢 '%s' 獲取卡片信息", card_query)

    try:
        card_data_obj = await _find_card_by_query(card_query)

        card_id = card_data_obj.card_id
        market_stats_dto = await get_card_market_stats(card_id)
        linked_stocks_dto = await get_linked_stocks_for_card(card_id, card_data_obj)

        card_properties = _extract_card_properties(card_data_obj)

        return CardInfoData(
            **card_properties,
            market_stats=market_stats_dto,
            linked_stocks=linked_stocks_dto,
        )

    except BusinessError:
        raise  # 重新拋出所有業務錯誤，讓上層處理
    except Exception as e:
        logger.error(
            "為查詢 %s 獲取卡片信息時發生意外錯誤: %s",
            card_query,
            e,
            exc_info=True,
        )
        raise MarketSystemError(f"獲取卡片信息時發生意外錯誤: {str(e)}") from e


async def get_card_market_stats(card_id: int) -> Optional[CardMarketStatsData]:
    """獲取給定卡片ID的市場統計信息。"""
    logger.info("CardInfoService: 正在為 card_id %s 獲取市場統計信息", card_id)
    pool = get_pool()
    if pool is None:
        logger.error("資料庫連線池未初始化，無法獲取市場統計數據。")
        return CardMarketStatsData()
    async with pool.acquire() as conn:
        try:
            stats_row = await conn.fetchrow(
                "SELECT total_owned_quantity, unique_owner_count, wishlist_count, favorite_count FROM gacha_card_market_stats WHERE card_id = $1",
                card_id,
            )
            if stats_row:
                return CardMarketStatsData(**dict(stats_row))
            return CardMarketStatsData()
        except Exception as e:
            logger.error(
                "為 card_id %s 獲取卡片市場統計信息時出錯: %s",
                card_id,
                e,
                exc_info=True,
            )
            return CardMarketStatsData()


async def get_linked_stocks_for_card(
    card_id: int, card_model_instance: "Card"
) -> List[LinkedStockData]:
    """根據卡片屬性使用正確的 linked_criteria 匹配獲取與卡片關聯的股票。"""
    pool = get_pool()
    conditions, query_params = [], []

    pool_type = getattr(card_model_instance, "pool_type", None)
    if pool_type:
        conditions.append(
            f"(linked_criteria_type = 'POOL_TYPE' AND linked_criteria_value = ${len(query_params) + 1})"
        )
        query_params.append(pool_type)

    rarity = getattr(card_model_instance, "rarity", None)
    if rarity and pool_type:
        rarity_value = str(rarity.value) if hasattr(rarity, "value") else str(rarity)
        conditions.append(
            f"(linked_criteria_type = 'RARITY_IN_POOL' AND linked_criteria_value = ${len(query_params) + 1} AND linked_pool_context = ${len(query_params) + 2})"
        )
        query_params.extend([rarity_value, pool_type])

    conditions.append(
        "(linked_criteria_type = 'GLOBAL' AND linked_criteria_value = '__all__')"
    )

    where_clause = " OR ".join(conditions)
    query = f"SELECT asset_symbol, asset_name, current_price, lifecycle_status FROM virtual_assets WHERE {where_clause} ORDER BY current_price DESC LIMIT 5"

    logger.debug(
        "正在為 card_id %s 搜索關聯股票，查詢: %s，參數: %s",
        card_id,
        query,
        query_params,
    )

    linked_stocks = []
    pool = get_pool()
    if pool is None:
        logger.error("資料庫連線池未初始化，無法獲取關聯股票。")
        return []
    async with pool.acquire() as conn:
        try:
            records = await conn.fetch(query, *query_params)
            logger.debug("為 card_id %s 找到 %d 個關聯股票", len(records), card_id)
            for row in records:
                linked_stocks.append(LinkedStockData.from_record(row))
        except Exception as e:
            logger.error(
                "為 card_id %s 獲取關聯股票時出錯: %s", card_id, e, exc_info=True
            )

    return linked_stocks
