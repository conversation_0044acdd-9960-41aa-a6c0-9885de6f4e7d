import math
from decimal import Decimal
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot


from gacha.exceptions import BusinessError
from gacha.models.history_models import TradeHistory, TransactionType
from gacha.models.market_models import StockLifecycleStatus
from gacha.services import stock_trading_service, user_service
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# Portfolio Cog - 投資組合功能的單檔案實現


class BuyStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="購買數量",
        placeholder="請輸入要購買的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: BotType,
        asset_symbol: str,
        current_price: Decimal,
        parent_view: Any,
    ):
        super().__init__(bot=bot, title="買入股票")
        self.asset_symbol = asset_symbol
        self.current_price = current_price
        self.parent_view = parent_view
        # 添加只讀的股票代碼和價格欄位
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.asset_symbol,
                style=discord.TextStyle.short,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("購買數量必須是正整數。")

        # 直接調用，讓 service 層處理所有業務邏輯（包括餘額檢查和手續費計算）
        message = await stock_trading_service.buy_stock(
            user_id=interaction.user.id,
            asset_symbol=self.asset_symbol,
            quantity=quantity,
        )

        # 優化：直接顯示服務層返回的完整消息，避免重複計算
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.parent_view:
            await self.parent_view.handle_trade_completion()


class SellStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="賣出數量",
        placeholder="請輸入要賣出的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: BotType,
        asset_symbol: str,
        current_price: Decimal,
        user_stock_quantity: int,
        parent_view: Any,
    ):
        super().__init__(bot=bot, title="賣出股票")
        self.asset_symbol = asset_symbol
        self.current_price = current_price
        self.user_stock_quantity = user_stock_quantity
        self.parent_view = parent_view
        # 添加只讀的股票代碼和價格欄位
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.asset_symbol,
                style=discord.TextStyle.short,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("賣出數量必須是正整數。")

        # 直接調用，讓 service 層處理所有業務邏輯
        message = await stock_trading_service.sell_stock(
            user_id=interaction.user.id,
            asset_symbol=self.asset_symbol,
            quantity=quantity,
        )

        # 優化：直接顯示服務層返回的完整消息，避免重複計算
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.parent_view:
            await self.parent_view.handle_trade_completion()


class ShortStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="做空數量",
        placeholder="請輸入要做空的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: BotType,
        asset_symbol: str,
        current_price: Decimal,
        parent_view: Any,
    ):
        super().__init__(bot=bot, title="做空股票")
        self.asset_symbol = asset_symbol
        self.current_price = current_price
        self.parent_view = parent_view
        # 添加只讀的股票代碼和價格欄位
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.asset_symbol,
                style=discord.TextStyle.short,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("做空數量必須是正整數。")

        # 調用做空功能
        message = await stock_trading_service.short_stock(
            user_id=interaction.user.id,
            asset_symbol=self.asset_symbol,
            quantity=quantity,
        )

        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.parent_view:
            await self.parent_view.handle_trade_completion()


class CoverShortModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="回補數量",
        placeholder="請輸入要回補的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: BotType,
        asset_symbol: str,
        current_price: Decimal,
        user_short_quantity: int,
        parent_view: Any,
    ):
        super().__init__(bot=bot, title="回補做空")
        self.asset_symbol = asset_symbol
        self.current_price = current_price
        self.user_short_quantity = user_short_quantity
        self.parent_view = parent_view
        # 添加只讀的股票代碼和價格欄位
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.asset_symbol,
                style=discord.TextStyle.short,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("回補數量必須是正整數。")

        # 調用回補功能
        message = await stock_trading_service.cover_short(
            user_id=interaction.user.id,
            asset_symbol=self.asset_symbol,
            quantity=quantity,
        )

        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.parent_view:
            await self.parent_view.handle_trade_completion()


class StockPortfolioSelect(discord.ui.Select):
    def __init__(self, holdings: List[Dict[str, Any]], view_instance: Any):
        self.parent_view = view_instance
        options = []
        if not holdings:
            options.append(
                discord.SelectOption(
                    label="您沒有持倉", value="_no_holdings", default=True
                )
            )
        else:
            for stock in holdings:
                status_suffix = ""
                stock_status_str = stock.get("lifecycle_status")
                if stock_status_str == StockLifecycleStatus.ST.value:
                    status_suffix = " (ST)"
                elif stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = " (已退市)"
                label = (
                    f"{stock['asset_symbol']} ({stock['asset_name']}{status_suffix})"
                )
                description = f"持有: {stock['quantity']}股, 現價: {Decimal(str(stock['current_market_price'])):.2f}"
                options.append(
                    discord.SelectOption(
                        label=label,
                        value=str(stock["asset_id"]),
                        description=description[:100],
                    )
                )
        super().__init__(
            placeholder="選擇要操作的股票...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="portfolio_cog_stock_select",
            disabled=not holdings,
        )

    async def callback(self, interaction: discord.Interaction):
        if self.values[0] == "_no_holdings":
            await interaction.response.defer()
            return
        await interaction.response.defer()
        selected_asset_id = int(self.values[0])
        selected_stock = next(
            (
                s
                for s in self.parent_view.all_holdings
                if s["asset_id"] == selected_asset_id
            ),
            None,
        )
        self.parent_view.selected_stock = selected_stock
        await self.parent_view._refresh_view_and_embed()


class ShortPositionSelect(discord.ui.Select):
    """做空倉位選擇菜單"""

    def __init__(self, short_positions: List[Dict[str, Any]], view_instance: Any):
        self.parent_view = view_instance
        options = []
        if not short_positions:
            options.append(
                discord.SelectOption(
                    label="您沒有做空倉位", value="_no_short_positions", default=True
                )
            )
        else:
            for position in short_positions:
                status_suffix = ""
                stock_status_str = position.get("lifecycle_status")
                if stock_status_str == StockLifecycleStatus.ST.value:
                    status_suffix = " (ST)"
                elif stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = " (已退市)"
                label = f"{position['asset_symbol']} ({position['asset_name']}{status_suffix})"
                description = f"做空: {position['short_quantity']}股, 現價: {Decimal(str(position['current_market_price'])):.2f}"
                options.append(
                    discord.SelectOption(
                        label=label,
                        value=str(position["asset_id"]),
                        description=description[:100],
                    )
                )
        super().__init__(
            placeholder="選擇要操作的做空倉位...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="portfolio_cog_short_select",
            disabled=not short_positions,
        )

    async def callback(self, interaction: discord.Interaction):
        if self.values[0] == "_no_short_positions":
            await interaction.response.defer()
            return
        await interaction.response.defer()
        selected_asset_id = int(self.values[0])
        selected_position = next(
            (
                s
                for s in self.parent_view.all_short_positions
                if s["asset_id"] == selected_asset_id
            ),
            None,
        )
        self.parent_view.selected_short_position = selected_position
        await self.parent_view._refresh_view_and_embed()


class TradeHistoryView(BaseView):
    """交易歷史記錄的視圖，包含分頁功能"""

    ITEMS_PER_PAGE = 5

    def __init__(
        self, bot: BotType, user_id: int, original_interaction: discord.Interaction
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=180)
        self.original_interaction = original_interaction
        self.current_page = 1
        self.total_pages = 1
        self.trades: List[TradeHistory] = []
        self.total_realized_pnl: Optional[Decimal] = None  # 全部交易的總盈虧

    @staticmethod
    def _calculate_trade_pnl(trade: TradeHistory) -> Optional[Decimal]:
        """計算單筆交易的盈虧，返回None表示無法計算"""
        if not (
            trade.context
            and "avg_cost" in trade.context
            and trade.context["avg_cost"] is not None
        ):
            return None

        avg_cost = Decimal(str(trade.context["avg_cost"]))

        if trade.transaction_type == TransactionType.SELL:
            return (trade.price_per_unit - avg_cost) * trade.quantity
        elif trade.transaction_type in [
            TransactionType.COVER,
            TransactionType.FORCED_COVER,
        ]:
            return (avg_cost - trade.price_per_unit) * trade.quantity
        elif trade.transaction_type == TransactionType.DELISTED_SETTLEMENT:
            # 下市結算：回購價格 - 平均成本
            return (trade.price_per_unit - avg_cost) * trade.quantity

        return None

    @staticmethod
    def _format_pnl_display(pnl: Decimal) -> tuple[str, str]:
        """格式化盈虧顯示，返回(指示器, 文字)"""
        if pnl > 0:
            return "🟢", f"獲利 {pnl:+.2f}"
        elif pnl < 0:
            return "🔴", f"虧損 {abs(pnl):.2f}"
        else:
            return "⚪", "損益平衡 0.00"

    @staticmethod
    def _get_transaction_display_info(trade: TradeHistory) -> tuple[str, str, str]:
        """獲取交易顯示信息，返回(指示器, 動作文字, 金額描述)"""
        if trade.transaction_type in [TransactionType.BUY, TransactionType.SHORT]:
            # 花費類交易
            cost = trade.total_amount + trade.fee
            action_text = (
                "買入" if trade.transaction_type == TransactionType.BUY else "做空"
            )
            indicator = "🔹" if trade.transaction_type == TransactionType.BUY else "🔸"
            amount_desc = f"花費: `{cost:.2f}` (含手續費 `{trade.fee:.2f}`)"
        else:
            # 收益類交易 (SELL, COVER, FORCED_COVER, DELISTED_SETTLEMENT)
            revenue = trade.total_amount - trade.fee
            if trade.transaction_type == TransactionType.SELL:
                action_text = "賣出"
                indicator = "✅"
            elif trade.transaction_type == TransactionType.COVER:
                action_text = "回補"
                indicator = "🔄"
            elif trade.transaction_type == TransactionType.FORCED_COVER:
                action_text = "強制回補"
                indicator = "🔄"
            elif trade.transaction_type == TransactionType.DELISTED_SETTLEMENT:
                action_text = "下市結算"
                indicator = "🏛️"
            else:
                action_text = "未知交易"
                indicator = "❓"

            if trade.transaction_type == TransactionType.DELISTED_SETTLEMENT:
                # 下市結算沒有手續費
                amount_desc = f"獲得: `{trade.total_amount:.2f}` (下市回購)"
            else:
                amount_desc = f"獲得: `{revenue:.2f}` (已扣手續費 `{trade.fee:.2f}`)"

        return indicator, action_text, amount_desc

    async def start(self):
        """異步初始化並顯示第一頁"""
        await self.update_history()
        await self._calculate_total_realized_pnl()  # 計算總盈虧
        embed = self._build_history_embed()
        self.update_buttons()
        await self.original_interaction.followup.send(embed=embed, view=self)

    async def update_history(self):
        """從服務層獲取交易歷史數據"""
        history_data = await stock_trading_service.get_user_trade_history_paginated(
            self.user_id, self.current_page, self.ITEMS_PER_PAGE
        )
        self.trades = history_data["trades"]
        self.total_pages = history_data["total_pages"]

    async def _calculate_total_realized_pnl(self):
        """計算用戶所有交易的總已實現盈虧"""
        try:
            # 獲取所有交易記錄（不分頁）
            all_history_data = (
                await stock_trading_service.get_user_trade_history_paginated(
                    self.user_id,
                    page=1,
                    per_page=10000,  # 使用大數字獲取所有記錄
                )
            )
            all_trades = all_history_data["trades"]

            total_pnl = Decimal("0")

            for trade in all_trades:
                trade_pnl = self._calculate_trade_pnl(trade)
                if trade_pnl is not None:
                    total_pnl += trade_pnl

            self.total_realized_pnl = total_pnl

        except Exception as e:
            logger.error("計算總盈虧失敗: %s", e)
            self.total_realized_pnl = None

    def update_buttons(self):
        """更新分頁按鈕的狀態"""
        self.clear_items()

        first_page_btn = discord.ui.Button(
            label="⏪",
            style=discord.ButtonStyle.secondary,
            custom_id="th_first",
            disabled=self.current_page == 1,
        )
        first_page_btn.callback = self.go_to_first_page
        self.add_item(first_page_btn)

        prev_page_btn = discord.ui.Button(
            label="⬅️",
            style=discord.ButtonStyle.secondary,
            custom_id="th_prev",
            disabled=self.current_page == 1,
        )
        prev_page_btn.callback = self.go_to_previous_page
        self.add_item(prev_page_btn)

        page_indicator = discord.ui.Button(
            label=f"{self.current_page}/{self.total_pages}",
            style=discord.ButtonStyle.grey,
            disabled=True,
        )
        self.add_item(page_indicator)

        next_page_btn = discord.ui.Button(
            label="➡️",
            style=discord.ButtonStyle.secondary,
            custom_id="th_next",
            disabled=self.current_page >= self.total_pages,
        )
        next_page_btn.callback = self.go_to_next_page
        self.add_item(next_page_btn)

        last_page_btn = discord.ui.Button(
            label="⏩",
            style=discord.ButtonStyle.secondary,
            custom_id="th_last",
            disabled=self.current_page >= self.total_pages,
        )
        last_page_btn.callback = self.go_to_last_page
        self.add_item(last_page_btn)

    def _build_history_embed(self) -> discord.Embed:
        """建立交易歷史的 Embed"""
        embed = discord.Embed(
            title=f"📜 {self.original_interaction.user.display_name} 的交易歷史",
            color=discord.Color.gold(),
        )
        embed.set_footer(text=f"第 {self.current_page}/{self.total_pages} 頁")

        if not self.trades:
            embed.description = "沒有找到任何交易記錄。"
            return embed

        for trade in self.trades:
            # 計算盈虧顯示
            trade_pnl = self._calculate_trade_pnl(trade)
            if trade_pnl is not None:
                pnl_indicator, _ = self._format_pnl_display(trade_pnl)
                pnl_text = f" / **盈虧: {pnl_indicator} `{trade_pnl:+.2f}`**"
            else:
                pnl_text = " / **盈虧: N/A**"

            # 獲取交易顯示信息
            indicator, action_text, field_value = self._get_transaction_display_info(
                trade
            )

            field_name = f"{indicator} {action_text} {trade.quantity} 股 **{trade.asset_symbol}** @ `{trade.price_per_unit:.2f}`"
            field_value += f"\n<t:{int(trade.timestamp.timestamp())}:R>{pnl_text}"
            embed.add_field(name=field_name, value=field_value, inline=False)

        # 添加全部交易總盈虧統計
        if self.total_realized_pnl is not None and self.total_realized_pnl != 0:
            pnl_indicator, pnl_text = self._format_pnl_display(self.total_realized_pnl)

            embed.add_field(
                name="\u200b",  # 空白分隔
                value=f"-# 總盈虧: {pnl_indicator} `{pnl_text}`",
                inline=False,
            )

        return embed

    async def _update_view(self, interaction: discord.Interaction):
        """通用視圖更新方法"""
        await self.update_history()
        embed = self._build_history_embed()
        self.update_buttons()
        await interaction.response.edit_message(embed=embed, view=self)

    async def go_to_first_page(self, interaction: discord.Interaction):
        self.current_page = 1
        await self._update_view(interaction)

    async def go_to_previous_page(self, interaction: discord.Interaction):
        self.current_page -= 1
        await self._update_view(interaction)

    async def go_to_next_page(self, interaction: discord.Interaction):
        self.current_page += 1
        await self._update_view(interaction)

    async def go_to_last_page(self, interaction: discord.Interaction):
        self.current_page = self.total_pages
        await self._update_view(interaction)


class PortfolioPaginatedView(BaseView):
    ITEMS_PER_PAGE = 10

    def __init__(
        self,
        bot: BotType,
        original_interaction: discord.Interaction,
        user_id: int,
        all_holdings: List[Dict[str, Any]],
        all_short_positions: List[Dict[str, Any]],
        oil_balance: Decimal,
        total_portfolio_value: Decimal,
        total_portfolio_cost_basis: Decimal,
        overall_pnl: Decimal,
        total_short_pnl: Decimal,
        total_asset_value: Decimal = Decimal("0"),
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.original_interaction = original_interaction
        self.all_holdings = all_holdings
        self.all_short_positions = all_short_positions
        self.oil_balance = oil_balance
        self.total_portfolio_value = total_portfolio_value
        self.total_portfolio_cost_basis = total_portfolio_cost_basis
        self.overall_pnl = overall_pnl
        self.total_short_pnl = total_short_pnl
        self.total_asset_value = total_asset_value
        self.current_page = 1
        self.view_mode = "holdings"  # 'holdings' 或 'shorts'
        self.total_pages = self._calculate_total_pages()
        self.selected_stock: Optional[Dict[str, Any]] = None
        self.selected_short_position: Optional[Dict[str, Any]] = None
        self._build_view_components()

    async def show_history_callback(self, interaction: discord.Interaction):
        """顯示交易歷史記錄的回調"""
        # 發送一個 "思考中" 的回應，讓用戶知道機器人正在處理
        await interaction.response.defer(thinking=True)
        history_view = TradeHistoryView(
            bot=self.bot, user_id=self.user_id, original_interaction=interaction
        )
        await history_view.start()

    def _calculate_total_pages(self) -> int:
        """計算當前視圖模式的總頁數"""
        if self.view_mode == "holdings":
            return (
                math.ceil(len(self.all_holdings) / self.ITEMS_PER_PAGE)
                if self.all_holdings
                else 1
            )
        else:  # shorts
            return (
                math.ceil(len(self.all_short_positions) / self.ITEMS_PER_PAGE)
                if self.all_short_positions
                else 1
            )

    def _build_view_components(self):
        self.clear_items()

        # 分頁按鈕（第一行）- 默認顯示
        first_page_btn = discord.ui.Button(
            label="⏪",
            style=discord.ButtonStyle.secondary,
            custom_id="pf_first",
            disabled=self.current_page == 1 or self.total_pages <= 1,
            row=0,
        )
        first_page_btn.callback = self.go_to_first_page
        self.add_item(first_page_btn)
        prev_page_btn = discord.ui.Button(
            label="⬅️",
            style=discord.ButtonStyle.secondary,
            custom_id="pf_prev",
            disabled=self.current_page == 1 or self.total_pages <= 1,
            row=0,
        )
        prev_page_btn.callback = self.go_to_previous_page
        self.add_item(prev_page_btn)
        page_indicator_btn = discord.ui.Button(
            label=f"{self.current_page}/{self.total_pages}",
            style=discord.ButtonStyle.grey,
            disabled=True,
            custom_id="pf_page_indicator",
            row=0,
        )
        self.add_item(page_indicator_btn)
        next_page_btn = discord.ui.Button(
            label="➡️",
            style=discord.ButtonStyle.secondary,
            custom_id="pf_next",
            disabled=self.current_page >= self.total_pages or self.total_pages <= 1,
            row=0,
        )
        next_page_btn.callback = self.go_to_next_page
        self.add_item(next_page_btn)
        last_page_btn = discord.ui.Button(
            label="⏩",
            style=discord.ButtonStyle.secondary,
            custom_id="pf_last",
            disabled=self.current_page >= self.total_pages or self.total_pages <= 1,
            row=0,
        )
        last_page_btn.callback = self.go_to_last_page
        self.add_item(last_page_btn)

        # 選擇菜單（第二行）
        if self.view_mode == "holdings":
            self.select_menu = StockPortfolioSelect(self.all_holdings, self)
            self.select_menu.row = 1
            self.add_item(self.select_menu)
        else:  # shorts
            self.short_select_menu = ShortPositionSelect(self.all_short_positions, self)
            self.short_select_menu.row = 1
            self.add_item(self.short_select_menu)

        # 視圖切換按鈕（第三行）
        holdings_btn = discord.ui.Button(
            label="💹 持有股票",
            style=(
                discord.ButtonStyle.success
                if self.view_mode == "holdings"
                else discord.ButtonStyle.secondary
            ),
            custom_id="pf_view_holdings",
            disabled=self.view_mode == "holdings",
            row=2,
        )
        holdings_btn.callback = self.switch_to_holdings_view
        self.add_item(holdings_btn)

        shorts_btn = discord.ui.Button(
            label="📉 做空倉位",
            style=(
                discord.ButtonStyle.success
                if self.view_mode == "shorts"
                else discord.ButtonStyle.secondary
            ),
            custom_id="pf_view_shorts",
            disabled=self.view_mode == "shorts",
            row=2,
        )
        shorts_btn.callback = self.switch_to_shorts_view
        self.add_item(shorts_btn)

        # 操作按鈕（第三行，與視圖切換按鈕同行）
        if self.view_mode == "holdings" and self.selected_stock:
            asset_id = self.selected_stock["asset_id"]
            asset_symbol = self.selected_stock["asset_symbol"]
            self.buy_button = discord.ui.Button(
                label=f"買入更多 {asset_symbol}",
                style=discord.ButtonStyle.success,
                custom_id=f"pf_action_buy_{asset_id}",
                row=2,
            )
            self.buy_button.callback = self.buy_more_callback
            self.add_item(self.buy_button)
            self.short_button = discord.ui.Button(
                label=f"做空 {asset_symbol}",
                style=discord.ButtonStyle.success,
                custom_id=f"pf_action_short_{asset_id}",
                row=2,
            )
            self.short_button.callback = self.short_stock_callback
            self.add_item(self.short_button)
            self.sell_button = discord.ui.Button(
                label=f"賣出部分 {asset_symbol}",
                style=discord.ButtonStyle.danger,
                custom_id=f"pf_action_sell_{asset_id}",
                row=2,
            )
            self.sell_button.callback = self.sell_partial_callback
            self.add_item(self.sell_button)
        elif self.view_mode == "shorts" and self.selected_short_position:
            asset_id = self.selected_short_position["asset_id"]
            asset_symbol = self.selected_short_position["asset_symbol"]
            self.short_button = discord.ui.Button(
                label=f"做空 {asset_symbol}",
                style=discord.ButtonStyle.success,
                custom_id=f"pf_action_short_{asset_id}",
                row=2,
            )
            self.short_button.callback = self.short_stock_callback
            self.add_item(self.short_button)
            self.cover_button = discord.ui.Button(
                label=f"回補 {asset_symbol}",
                style=discord.ButtonStyle.danger,
                custom_id=f"pf_action_cover_{asset_id}",
                row=2,
            )
            self.cover_button.callback = self.cover_short_callback
            self.add_item(self.cover_button)

        # 第四行：功能按鈕
        history_btn = discord.ui.Button(
            label="📜 交易歷史",
            style=discord.ButtonStyle.primary,
            custom_id="pf_view_history",
            row=3,
        )
        history_btn.callback = self.show_history_callback
        self.add_item(history_btn)

    async def _refresh_view_and_embed(self):
        balance_data = await user_service.get_balance(self.user_id)
        self.oil_balance = (
            Decimal(str(balance_data.get("balance", 0)))
            if isinstance(balance_data, dict)
            else Decimal("0")
        )
        portfolio_data_full = await stock_trading_service.get_user_portfolio(
            self.user_id
        )
        self.all_holdings = portfolio_data_full.get("holdings", [])
        self.all_short_positions = portfolio_data_full.get("short_positions", [])
        self.total_portfolio_value = Decimal(
            str(
                portfolio_data_full.get(
                    "total_portfolio_value_at_current_market_price", "0"
                )
            )
        )
        self.total_portfolio_cost_basis = Decimal(
            str(portfolio_data_full.get("total_portfolio_cost_basis", "0"))
        )
        self.total_short_pnl = Decimal(
            str(portfolio_data_full.get("total_short_pnl", "0"))
        )
        self.overall_pnl = Decimal(
            str(portfolio_data_full.get("overall_unrealized_pnl", "0"))
        )
        self.total_asset_value = Decimal(
            str(portfolio_data_full.get("total_asset_value", "0"))
        )
        self.total_pages = self._calculate_total_pages()
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        if self.current_page < 1:
            self.current_page = 1

        # 更新選中的項目
        if self.selected_stock:
            selected_asset_id = self.selected_stock["asset_id"]
            self.selected_stock = next(
                (s for s in self.all_holdings if s["asset_id"] == selected_asset_id),
                None,
            )
        if self.selected_short_position:
            selected_asset_id = self.selected_short_position["asset_id"]
            self.selected_short_position = next(
                (
                    s
                    for s in self.all_short_positions
                    if s["asset_id"] == selected_asset_id
                ),
                None,
            )

        # 根據當前視圖模式獲取數據
        start_index = (self.current_page - 1) * self.ITEMS_PER_PAGE
        end_index = start_index + self.ITEMS_PER_PAGE

        if self.view_mode == "holdings":
            current_page_data = self.all_holdings[start_index:end_index]
            embed = PortfolioCog._build_portfolio_embed_static(
                user_display_name=self.original_interaction.user.display_name,
                oil_balance=self.oil_balance,
                current_page_holdings=current_page_data,
                total_portfolio_value=self.total_portfolio_value,
                total_portfolio_cost_basis=self.total_portfolio_cost_basis,
                overall_pnl=self.overall_pnl,
                total_short_pnl=self.total_short_pnl,
                total_asset_value=self.total_asset_value,
                current_page=self.current_page,
                total_pages=self.total_pages,
                total_holdings_count=len(self.all_holdings),
            )
        else:  # shorts
            current_page_data = self.all_short_positions[start_index:end_index]
            embed = PortfolioCog._build_short_positions_embed_static(
                user_display_name=self.original_interaction.user.display_name,
                oil_balance=self.oil_balance,
                current_page_short_positions=current_page_data,
                total_portfolio_value=self.total_portfolio_value,
                total_portfolio_cost_basis=self.total_portfolio_cost_basis,
                total_short_pnl=self.total_short_pnl,
                overall_pnl=self.overall_pnl,
                total_asset_value=self.total_asset_value,
                current_page=self.current_page,
                total_pages=self.total_pages,
                total_short_positions_count=len(self.all_short_positions),
            )

        self._build_view_components()
        await self.original_interaction.edit_original_response(embed=embed, view=self)

    async def buy_more_callback(self, interaction: discord.Interaction):
        if not self.selected_stock:
            raise BusinessError("請先選擇股票。")

        current_price = Decimal(str(self.selected_stock.get("current_market_price", 0)))
        modal = BuyStockModal(
            bot=self.bot,
            asset_symbol=self.selected_stock["asset_symbol"],
            current_price=current_price,
            parent_view=self,
        )
        await interaction.response.send_modal(modal)

    async def sell_partial_callback(self, interaction: discord.Interaction):
        if not self.selected_stock:
            raise BusinessError("請先選擇股票。")

        user_quantity = self.selected_stock.get("quantity", 0)
        current_price = Decimal(str(self.selected_stock.get("current_market_price", 0)))
        modal = SellStockModal(
            bot=self.bot,
            asset_symbol=self.selected_stock["asset_symbol"],
            current_price=current_price,
            user_stock_quantity=user_quantity,
            parent_view=self,
        )
        await interaction.response.send_modal(modal)

    async def switch_to_holdings_view(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.view_mode = "holdings"
        self.current_page = 1
        self.selected_stock = None
        self.selected_short_position = None
        await self._refresh_view_and_embed()

    async def switch_to_shorts_view(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.view_mode = "shorts"
        self.current_page = 1
        self.selected_stock = None
        self.selected_short_position = None
        await self._refresh_view_and_embed()

    async def short_stock_callback(self, interaction: discord.Interaction):
        # 支持從持有股票視圖或做空倉位視圖進行做空操作
        asset_symbol = None
        current_price = Decimal("0")

        if self.view_mode == "holdings" and self.selected_stock:
            asset_symbol = self.selected_stock["asset_symbol"]
            current_price = Decimal(
                str(self.selected_stock.get("current_market_price", 0))
            )
        elif self.view_mode == "shorts" and self.selected_short_position:
            asset_symbol = self.selected_short_position["asset_symbol"]
            current_price = Decimal(
                str(self.selected_short_position.get("current_market_price", 0))
            )

        if not asset_symbol:
            raise BusinessError("請先選擇股票或做空倉位。")

        modal = ShortStockModal(
            bot=self.bot,
            asset_symbol=asset_symbol,
            current_price=current_price,
            parent_view=self,
        )
        await interaction.response.send_modal(modal)

    async def cover_short_callback(self, interaction: discord.Interaction):
        if not self.selected_short_position:
            raise BusinessError("請先選擇做空倉位。")

        user_short_quantity = self.selected_short_position.get("short_quantity", 0)
        current_price = Decimal(
            str(self.selected_short_position.get("current_market_price", 0))
        )
        modal = CoverShortModal(
            bot=self.bot,
            asset_symbol=self.selected_short_position["asset_symbol"],
            current_price=current_price,
            user_short_quantity=user_short_quantity,
            parent_view=self,
        )
        await interaction.response.send_modal(modal)

    async def handle_trade_completion(self):
        await self._refresh_view_and_embed()

    async def go_to_first_page(self, interaction: discord.Interaction):
        await interaction.response.defer()
        if self.current_page != 1:
            self.current_page = 1
            self.total_pages = self._calculate_total_pages()
            await self._refresh_view_and_embed()

    async def go_to_previous_page(self, interaction: discord.Interaction):
        await interaction.response.defer()
        if self.current_page > 1:
            self.current_page -= 1
            self.total_pages = self._calculate_total_pages()
            await self._refresh_view_and_embed()

    async def go_to_next_page(self, interaction: discord.Interaction):
        await interaction.response.defer()
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.total_pages = self._calculate_total_pages()
            await self._refresh_view_and_embed()

    async def go_to_last_page(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.total_pages = self._calculate_total_pages()
        if self.current_page != self.total_pages:
            self.current_page = self.total_pages
            await self._refresh_view_and_embed()


class PortfolioCog(commands.Cog, name="投資組合"):
    def __init__(self, bot: BotType):
        self.bot = bot
        logger.info("PortfolioCog initialized.")

    @staticmethod
    def _build_portfolio_embed_static(
        user_display_name: str,
        oil_balance: Decimal,
        current_page_holdings: List[Dict[str, Any]],
        total_portfolio_value: Decimal,
        total_portfolio_cost_basis: Decimal,
        overall_pnl: Decimal,
        total_short_pnl: Decimal = Decimal("0"),
        total_asset_value: Decimal = Decimal("0"),
        title_suffix: str = "",
        current_page: int = 1,
        total_pages: int = 1,
        total_holdings_count: int = 0,
    ) -> discord.Embed:
        title = f"**{user_display_name}** 的投資組合"
        if total_pages > 1:
            title += f" (第 {current_page}/{total_pages} 頁)"
        title += title_suffix
        embed = discord.Embed(title=title, color=discord.Color.purple())
        if total_holdings_count == 0:
            embed.add_field(
                name="股票持倉",
                value="您目前未持有任何股票。\n\n💡 **想要開始投資？**\n使用 `/stock` 指令查看股票市場並開始買入股票！",
                inline=False,
            )
        elif not current_page_holdings:
            embed.add_field(
                name="股票持倉", value="此頁沒有更多持倉股票。", inline=False
            )
        else:
            for stock_item in current_page_holdings:
                symbol = stock_item["asset_symbol"]
                name = stock_item["asset_name"]
                qty = stock_item["quantity"]
                avg_buy = Decimal(str(stock_item["average_buy_price"]))
                curr_price = Decimal(str(stock_item["current_market_price"]))
                curr_value = Decimal(str(stock_item["current_value"]))
                pnl_val = Decimal(str(stock_item["unrealized_pnl"]))
                status_suffix = ""
                stock_status_str = stock_item.get("lifecycle_status")
                if stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = " (已退市)"
                pnl_str = f"{pnl_val:+.2f}"
                pnl_indicator = "🟢" if pnl_val > 0 else "🔴" if pnl_val < 0 else "⚪"
                emoji_to_use = (
                    "<a:Error:1371096622053724292>"
                    if stock_status_str == StockLifecycleStatus.ST.value
                    else "<a:my2:1370641774950875146>"
                )
                field_name_stock = (
                    f"{emoji_to_use} **{symbol}** ({name}{status_suffix})"
                )
                field_value_stock = f"<:ReplyCont:1383146319425699931> 持有: `{qty}` 股\n<:ReplyCont:1383146319425699931> 均買價: `{avg_buy:.2f}`\n<:ReplyCont:1383146319425699931> 現價: `{curr_price:.2f}`\n<:ReplyCont:1383146319425699931> 市值: `{curr_value:.2f}`\n<:Reply:1357534074830590143> 盈虧: {pnl_indicator} `{pnl_str}`"
                embed.add_field(
                    name=field_name_stock, value=field_value_stock, inline=True
                )
        if total_holdings_count > 0 and current_page_holdings:
            embed.add_field(name="\u200b", value="\u200b", inline=False)

        # --- 全局資產總覽 (統一版本) ---
        stock_pnl = total_portfolio_value - total_portfolio_cost_basis
        stock_pnl_indicator = "🟢" if stock_pnl > 0 else "🔴" if stock_pnl < 0 else "⚪"
        short_pnl_indicator = (
            "🟢" if total_short_pnl > 0 else "🔴" if total_short_pnl < 0 else "⚪"
        )

        portfolio_summary_value = (
            f"股票盈虧: {stock_pnl_indicator} `{stock_pnl:+.2f}`\n"
            f"做空盈虧: {short_pnl_indicator} `{total_short_pnl:+.2f}`"
        )
        embed.add_field(name="組合盈虧", value=portfolio_summary_value, inline=True)

        asset_summary_value = (
            f"💰 可用油幣: `{int(oil_balance)}`\n📊 總資產: `{int(total_asset_value)}`"
        )
        embed.add_field(name="資產概況", value=asset_summary_value, inline=True)

        pnl_color_indicator = (
            "🟢" if overall_pnl > 0 else "🔴" if overall_pnl < 0 else "⚪"
        )
        embed.add_field(
            name="總未實現盈虧",
            value=f"{pnl_color_indicator} `{overall_pnl:+.2f}`",
            inline=True,
        )
        footer_text = "使用下方按鈕切換視圖或查看交易歷史。"
        if title_suffix:
            footer_text += " 數據已刷新。"
        embed.set_footer(text=footer_text)
        return embed

    @staticmethod
    def _build_short_positions_embed_static(
        user_display_name: str,
        oil_balance: Decimal,
        current_page_short_positions: List[Dict[str, Any]],
        total_portfolio_value: Decimal,
        total_portfolio_cost_basis: Decimal,
        total_short_pnl: Decimal,
        overall_pnl: Decimal,
        total_asset_value: Decimal = Decimal("0"),
        title_suffix: str = "",
        current_page: int = 1,
        total_pages: int = 1,
        total_short_positions_count: int = 0,
    ) -> discord.Embed:
        title = f"**{user_display_name}** 的做空倉位"
        if total_pages > 1:
            title += f" (第 {current_page}/{total_pages} 頁)"
        title += title_suffix
        embed = discord.Embed(title=title, color=discord.Color.red())

        if total_short_positions_count == 0:
            embed.add_field(
                name="做空倉位",
                value="您目前沒有做空倉位。\n\n💡 **想要做空股票？**\n在持有股票頁面選擇股票後點擊做空按鈕！\n\n⚠️ **風險提醒**\nST狀態股票將被強制回補，請注意風險控制。",
                inline=False,
            )
        elif not current_page_short_positions:
            embed.add_field(
                name="做空倉位", value="此頁沒有更多做空倉位。", inline=False
            )
        else:
            for position in current_page_short_positions:
                symbol = position["asset_symbol"]
                name = position["asset_name"]
                short_qty = position["short_quantity"]
                short_avg_price = Decimal(str(position["short_average_price"]))
                curr_price = Decimal(str(position["current_market_price"]))
                original_bet = Decimal(str(position["original_bet"]))
                short_pnl = Decimal(str(position["short_pnl"]))

                status_suffix = ""
                stock_status_str = position.get("lifecycle_status")
                if stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = " (已退市)"

                pnl_str = f"{short_pnl:+.2f}"
                pnl_indicator = (
                    "🟢" if short_pnl > 0 else "🔴" if short_pnl < 0 else "⚪"
                )
                emoji_to_use = (
                    "<a:Error:1371096622053724292>"
                    if stock_status_str == StockLifecycleStatus.ST.value
                    else "📉"
                )

                field_name_position = (
                    f"{emoji_to_use} **{symbol}** ({name}{status_suffix})"
                )
                field_value_position = f"<:ReplyCont:1383146319425699931> 做空: `{short_qty}` 股\n<:ReplyCont:1383146319425699931> 均做空價: `{short_avg_price:.2f}`\n<:ReplyCont:1383146319425699931> 現價: `{curr_price:.2f}`\n<:ReplyCont:1383146319425699931> 押注金額: `{original_bet:.2f}`\n<:Reply:1357534074830590143> 盈虧: {pnl_indicator} `{pnl_str}`"

                if stock_status_str == StockLifecycleStatus.ST.value:
                    field_value_position += "\n⚠️ **ST狀態：將被強制回補**"

                embed.add_field(
                    name=field_name_position, value=field_value_position, inline=True
                )

        if total_short_positions_count > 0 and current_page_short_positions:
            embed.add_field(name="\u200b", value="\u200b", inline=False)

        # --- 全局資產總覽 (統一版本) ---
        stock_pnl = total_portfolio_value - total_portfolio_cost_basis
        stock_pnl_indicator = "🟢" if stock_pnl > 0 else "🔴" if stock_pnl < 0 else "⚪"
        short_pnl_indicator = (
            "🟢" if total_short_pnl > 0 else "🔴" if total_short_pnl < 0 else "⚪"
        )

        portfolio_summary_value = (
            f"股票盈虧: {stock_pnl_indicator} `{stock_pnl:+.2f}`\n"
            f"做空盈虧: {short_pnl_indicator} `{total_short_pnl:+.2f}`"
        )
        embed.add_field(name="組合盈虧", value=portfolio_summary_value, inline=True)

        asset_summary_value = (
            f"💰 可用油幣: `{int(oil_balance)}`\n📊 總資產: `{int(total_asset_value)}`"
        )
        embed.add_field(name="資產概況", value=asset_summary_value, inline=True)

        pnl_color_indicator = (
            "🟢" if overall_pnl > 0 else "🔴" if overall_pnl < 0 else "⚪"
        )
        embed.add_field(
            name="總未實現盈虧",
            value=f"{pnl_color_indicator} `{overall_pnl:+.2f}`",
            inline=True,
        )

        footer_text = "使用下方按鈕切換視圖或查看交易歷史。"
        if title_suffix:
            footer_text += " 數據已刷新。"
        embed.set_footer(text=footer_text)
        return embed

    @app_commands.command(name="portfolio", description="查看您的油幣和股票投資組合。")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def portfolio(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=False)

        user_id = interaction.user.id

        balance_data = await user_service.get_balance(user_id)
        oil_balance = (
            Decimal(str(balance_data.get("balance", 0)))
            if isinstance(balance_data, dict)
            else Decimal("0")
        )

        portfolio_data = await stock_trading_service.get_user_portfolio(user_id)
        all_holdings = portfolio_data.get("holdings", [])
        all_short_positions = portfolio_data.get("short_positions", [])
        total_portfolio_value = Decimal(
            str(
                portfolio_data.get("total_portfolio_value_at_current_market_price", "0")
            )
        )
        total_portfolio_cost_basis = Decimal(
            str(portfolio_data.get("total_portfolio_cost_basis", "0"))
        )
        total_short_pnl = Decimal(str(portfolio_data.get("total_short_pnl", "0")))
        overall_pnl = Decimal(str(portfolio_data.get("overall_unrealized_pnl", "0")))
        total_asset_value = Decimal(str(portfolio_data.get("total_asset_value", "0")))

        view = PortfolioPaginatedView(
            bot=self.bot,
            original_interaction=interaction,
            user_id=user_id,
            all_holdings=all_holdings,
            all_short_positions=all_short_positions,
            oil_balance=oil_balance,
            total_portfolio_value=total_portfolio_value,
            total_portfolio_cost_basis=total_portfolio_cost_basis,
            overall_pnl=overall_pnl,
            total_short_pnl=total_short_pnl,
            total_asset_value=total_asset_value,
        )

        start_index = (view.current_page - 1) * view.ITEMS_PER_PAGE
        end_index = start_index + view.ITEMS_PER_PAGE
        current_page_holdings_data = all_holdings[start_index:end_index]

        embed = self._build_portfolio_embed_static(
            user_display_name=interaction.user.display_name,
            oil_balance=oil_balance,
            current_page_holdings=current_page_holdings_data,
            total_portfolio_value=total_portfolio_value,
            total_portfolio_cost_basis=total_portfolio_cost_basis,
            overall_pnl=overall_pnl,
            total_short_pnl=total_short_pnl,
            total_asset_value=total_asset_value,
            current_page=view.current_page,
            total_pages=view.total_pages,
            total_holdings_count=len(all_holdings),
        )

        await interaction.followup.send(embed=embed, view=view)


async def setup(bot: commands.Bot):
    await bot.add_cog(PortfolioCog(bot))
    logger.info("PortfolioCog added successfully.")
