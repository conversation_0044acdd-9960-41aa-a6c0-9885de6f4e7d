from typing import Any, List, Optional, Tuple

import asyncpg

from database.postgresql.async_manager import get_pool
from utils.logger import logger

DB_OPERATION_INTERNAL_BATCH_SIZE = 1000


class MarketStatsUpdateError(Exception):
    """Base exception for market stats update errors"""

    pass


class InvalidColumnNameError(MarketStatsUpdateError):
    """Exception raised when an invalid column name is provided"""

    def __init__(self, column_name: str):
        self.column_name = column_name
        super().__init__(f"Invalid column name: {column_name}")


class BatchUpdateError(MarketStatsUpdateError):
    """Exception raised when a batch update operation fails"""

    def __init__(
        self, operation_description: str, batch_number: int, original_error: Exception
    ):
        self.operation_description = operation_description
        self.batch_number = batch_number
        self.original_error = original_error
        super().__init__(
            f"Batch update for {operation_description} failed (batch {batch_number}): {original_error}"
        )


class InvalidUpdateFormatError(MarketStatsUpdateError):
    """Exception raised when an invalid update format is provided"""

    def __init__(self, update_item: Any):
        self.update_item = update_item
        super().__init__(
            f"Invalid update format: {update_item}. Expected tuple (card_id, change)."
        )


async def _bulk_update_stats_column(
    conn: asyncpg.Connection,
    updates: List[Tuple[int, int]],
    column_name: str,
    operation_description: str,
):
    """
    通用的批量更新 gacha_card_market_stats 表中指定列的輔助函數。
    使用 INSERT ... ON CONFLICT DO UPDATE ... SET column = GREATEST(0, column + EXCLUDED.column)。

    【死鎖防護】對 card_id 進行排序以確保所有事務以相同順序鎖定行。

    :param conn: 資料庫連接物件。
    :param updates: 元組列表：[(card_id, change), ...]
    :param column_name: 要更新的列名 (例如 "total_owned_quantity", "favorite_count")。
    :param operation_description: 用於日誌記錄的操作描述 (例如 "total_owned_quantity")。
    """
    if not isinstance(updates, list):
        logger.error(
            "DBUpdater: _bulk_update_stats_column 接收到的 'updates' 参数类型不正确，期望是 list，实际是 %s",
            type(updates),
        )
        raise TypeError(f"Expected 'updates' to be a list, got {type(updates)}")

    if not updates:
        return
    if not column_name.isidentifier():
        logger.error("DBUpdater: 無效的列名 '%s' 用於批量更新。", column_name)
        raise InvalidColumnNameError(column_name)

    # 【解決重複卡片ID問題】合併相同卡片ID的變化
    merged_updates = {}
    for card_id, change in updates:
        merged_updates[card_id] = merged_updates.get(card_id, 0) + change

    # 過濾掉變化為0的更新
    filtered_updates = [
        (card_id, change) for card_id, change in merged_updates.items() if change != 0
    ]

    if not filtered_updates:
        logger.debug("DBUpdater: 所有更新在合併後變化為0，跳過批量更新")
        return

    # 【解決死鎖的關鍵】按 card_id 排序以確保所有事務以相同順序鎖定行
    sorted_updates = sorted(filtered_updates, key=lambda x: x[0])

    # 單一批量操作取代多次迴圈，減少 I/O 阻塞
    sql = f"""
    INSERT INTO gacha_card_market_stats (card_id, {column_name})
    SELECT unnest_card_ids, unnest_deltas
    FROM unnest($1::integer[], $2::integer[]) AS t(unnest_card_ids, unnest_deltas)
    ON CONFLICT (card_id) DO UPDATE
    SET {column_name} = GREATEST(0, gacha_card_market_stats.{column_name} + EXCLUDED.{column_name});
    """

    # Extract card_ids and deltas from tuple updates
    card_ids = [update[0] for update in sorted_updates]
    deltas = [update[1] for update in sorted_updates]

    try:
        await conn.execute(sql, card_ids, deltas)
    except Exception as e:
        logger.error(
            "DBUpdater: 批量更新 %s 失敗: %s",
            operation_description,
            e,
            exc_info=True,
        )
        raise BatchUpdateError(operation_description, 1, e) from e


async def bulk_update_total_owned(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    批量更新卡片的總擁有數量 (total_owned_quantity)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "total_owned_quantity", "total_owned_quantity"
        )
    else:
        async with get_pool().acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "total_owned_quantity", "total_owned_quantity"
            )


async def bulk_update_favorite_counts_direct(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    直接批量更新卡片的最愛數量 (favorite_count)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "favorite_count", "favorite_count"
        )
    else:
        async with get_pool().acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "favorite_count", "favorite_count"
            )


async def _internal_bulk_update_unique_owners(
    conn: asyncpg.Connection, owner_changes: List[Tuple[int, int]]
):
    """
    批量更新卡片的獨立擁有者數量 (unique_owner_count) 的內部邏輯。

    【死鎖防護】對 card_id 進行排序以確保所有事務以相同順序鎖定行。
    """
    if not owner_changes:
        return
    increment_card_ids: List[int] = []
    decrement_card_ids: List[int] = []

    for update in owner_changes:
        try:
            if not isinstance(update, (list, tuple)) or len(update) != 2:
                raise InvalidUpdateFormatError(update)

            card_id, delta = update

            if delta == 1:
                increment_card_ids.append(card_id)
            elif delta == -1:
                decrement_card_ids.append(card_id)
            else:
                logger.warning("未知的 change 值: %s for card_id: %s", delta, card_id)
        except (ValueError, TypeError) as e:
            logger.error("無效的更新格式: %s, 錯誤: %s", update, e)
            raise InvalidUpdateFormatError(update) from e

    # 【解決死鎖的關鍵】對 card_id 進行排序
    increment_card_ids.sort()
    decrement_card_ids.sort()

    if increment_card_ids:
        sql_increment = """
        INSERT INTO gacha_card_market_stats (card_id, unique_owner_count)
        SELECT card_id_val, 1
        FROM unnest($1::integer[]) AS t(card_id_val)
        ON CONFLICT (card_id) DO UPDATE
        SET unique_owner_count = gacha_card_market_stats.unique_owner_count + 1;
        """
        try:
            await conn.execute(sql_increment, increment_card_ids)
        except Exception as e:
            logger.error(
                "DBUpdater: 批量增加 unique_owner_count 失敗: %s",
                e,
                exc_info=True,
            )
            raise BatchUpdateError("unique_owner_count_increment", 1, e) from e

    if decrement_card_ids:
        sql_decrement = """
        UPDATE gacha_card_market_stats
        SET unique_owner_count = GREATEST(0, unique_owner_count - 1)
        WHERE card_id = ANY($1::integer[]);
        """
        try:
            await conn.execute(sql_decrement, decrement_card_ids)
        except Exception as e:
            logger.error(
                "DBUpdater: 批量減少 unique_owner_count 失敗: %s",
                e,
                exc_info=True,
            )
            raise BatchUpdateError("unique_owner_count_decrement", 1, e) from e


async def bulk_update_unique_owners(
    owner_changes: List[Tuple[int, int]],
    connection: Optional[asyncpg.Connection] = None,
):
    """
    批量更新卡片的獨立擁有者數量 (unique_owner_count)。

    :param owner_changes: 元組列表：[(card_id, change), ...]
                          change 為 1 表示增加，-1 表示減少。
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _internal_bulk_update_unique_owners(connection, owner_changes)
    else:
        async with get_pool().acquire() as conn:
            await _internal_bulk_update_unique_owners(conn, owner_changes)


async def bulk_update_wishlist_counts_direct(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    直接批量更新卡片的許願數量 (wishlist_count)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "wishlist_count", "wishlist_count"
        )
    else:
        async with get_pool().acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "wishlist_count", "wishlist_count"
            )


async def _internal_bulk_update_combined_stats(
    conn: asyncpg.Connection, updates: List[Tuple[int, int, int, int]]
):
    """
    內部輔助函數，用於在單一查詢中批量更新多個統計列。
    【已修復】使用自定義複合類型 market_stats_update_type 來避免 'anonymous record' 錯誤，
    並移除了多餘的列定義列表。

    :param updates: 一個元組列表，每個元組的結構必須符合在DB中定義的
                     `market_stats_update_type` 類型:
                     (card_id, total_owned_quantity_delta, favorite_count_delta, wishlist_count_delta)
    """
    if not updates:
        return

    # 【最終修正】
    # 當使用已命名的複合類型時，`AS d(...)` 部分是多餘的，必須移除。
    # PostgreSQL 會自動從 `market_stats_update_type` 的定義中獲取列名。
    sql = """
        INSERT INTO gacha_card_market_stats (card_id, total_owned_quantity, favorite_count, wishlist_count)
        SELECT
            d.card_id,
            d.total_owned_quantity_delta,
            d.favorite_count_delta,
            d.wishlist_count_delta
        FROM
            -- 這裡就是唯一的改動：移除了 `AS d(...)`
            unnest($1::market_stats_update_type[]) AS d
        ON CONFLICT (card_id) DO UPDATE SET
            total_owned_quantity = GREATEST(0, gacha_card_market_stats.total_owned_quantity + EXCLUDED.total_owned_quantity),
            favorite_count = GREATEST(0, gacha_card_market_stats.favorite_count + EXCLUDED.favorite_count),
            wishlist_count = GREATEST(0, gacha_card_market_stats.wishlist_count + EXCLUDED.wishlist_count);
    """

    try:
        await conn.execute(sql, updates)
        logger.info("DBUpdater: 成功批量合併更新了 %s 條市場統計記錄。", len(updates))
    except Exception as e:
        logger.error("DBUpdater: 批量合併更新統計數據失敗: %s", e, exc_info=True)
        raise BatchUpdateError("combined_stats", 1, e) from e


# 替換 `bulk_update_combined_stats` 函數
# 注意函數簽名變化：updates 參數類型從 Dict 變為 List[Tuple]
async def bulk_update_combined_stats(
    updates: List[Tuple[int, int, int, int]],
    connection: Optional[asyncpg.Connection] = None,
):
    """
    公開接口，用於批量更新多個簡單的統計列。

    :param updates: 一個元組列表，其結構必須符合在DB中定義的 `market_stats_update_type`。
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _internal_bulk_update_combined_stats(connection, updates)
    else:
        async with get_pool().acquire() as conn:
            await _internal_bulk_update_combined_stats(conn, updates)


# ===== 新增：事務內統計更新函數 =====


async def update_market_stats_in_transaction(
    conn: asyncpg.Connection,
    drawn_card_ids: List[int],
    owner_changes: List[dict],
    favorite_changes: Optional[List[Tuple[int, int]]] = None,
    wishlist_changes: Optional[List[Tuple[int, int]]] = None,
) -> None:
    """
    在現有事務中同步更新市場統計數據，確保與主業務邏輯的原子性。

    這個函數專門用於替代異步隊列方式，直接在主事務中執行統計更新，
    避免主事務成功但統計更新失敗導致的數據不一致問題。

    :param conn: 已開啟事務的數據庫連接
    :param drawn_card_ids: 抽取的卡片ID列表（用於計算total_owned_quantity增量）
    :param owner_changes: 擁有者變化列表，格式：[{"card_id": int, "change": int}, ...]
    :param favorite_changes: 收藏變化列表，格式：[(card_id, change), ...]，可選
    :param wishlist_changes: 許願變化列表，格式：[(card_id, change), ...]，可選
    """
    try:
        logger.info(
            "[MARKET_STATS_TRANSACTION] 開始在事務中更新統計 - 卡片數: %s, 擁有者變化: %s",
            len(drawn_card_ids),
            len(owner_changes) if owner_changes else 0,
        )

        # 1. 處理 total_owned_quantity 更新
        if drawn_card_ids:
            # 計算每張卡片的數量變化
            card_counts = {}
            for card_id in drawn_card_ids:
                card_counts[card_id] = card_counts.get(card_id, 0) + 1

            total_owned_updates = list(card_counts.items())

            logger.debug(
                "[MARKET_STATS_TRANSACTION] 更新總擁有量: %s", total_owned_updates
            )
            await bulk_update_total_owned(total_owned_updates, connection=conn)

        # 2. 處理 unique_owner_count 更新
        if owner_changes:
            unique_owner_updates = [
                (change["card_id"], change["change"]) for change in owner_changes
            ]

            logger.debug(
                "[MARKET_STATS_TRANSACTION] 更新獨立擁有者數: %s", unique_owner_updates
            )
            await bulk_update_unique_owners(unique_owner_updates, connection=conn)

        # 3. 處理 favorite_count 更新（如果提供）
        if favorite_changes:
            logger.debug("[MARKET_STATS_TRANSACTION] 更新收藏數: %s", favorite_changes)
            await bulk_update_favorite_counts_direct(favorite_changes, connection=conn)

        # 4. 處理 wishlist_count 更新（如果提供）
        if wishlist_changes:
            logger.debug("[MARKET_STATS_TRANSACTION] 更新許願數: %s", wishlist_changes)
            await bulk_update_wishlist_counts_direct(wishlist_changes, connection=conn)

        logger.info(
            "[MARKET_STATS_TRANSACTION] 統計更新完成 - 總擁有量: %s, 獨立擁有者: %s, 收藏: %s, 許願: %s",
            len(drawn_card_ids) if drawn_card_ids else 0,
            len(owner_changes) if owner_changes else 0,
            len(favorite_changes) if favorite_changes else 0,
            len(wishlist_changes) if wishlist_changes else 0,
        )

    except Exception as e:
        logger.error(
            "[MARKET_STATS_TRANSACTION] 事務內統計更新失敗: %s",
            e,
            exc_info=True,
        )
        # 不需要手動回滾，因為這是在調用方的事務中執行的
        # 調用方的事務失敗會自動回滾所有更改
        raise MarketStatsUpdateError(f"事務內統計更新失敗: {e}") from e


async def update_market_stats_for_sell_in_transaction(
    conn: asyncpg.Connection,
    sold_cards_details: List[dict],
    deleted_cards: List[dict],
) -> None:
    """
    在賣卡事務中同步更新市場統計數據。

    :param conn: 已開啟事務的數據庫連接
    :param sold_cards_details: 賣出卡片詳情，格式：[{"card_id": int, "quantity_sold": int}, ...]
    :param deleted_cards: 被刪除的卡片詳情，格式：[{"card_id": int, "was_favorite": bool}, ...]
    """
    try:
        # 1. 處理 total_owned_quantity 減少
        if sold_cards_details:
            total_owned_updates = [
                (item["card_id"], -item["quantity_sold"])
                for item in sold_cards_details
                if item.get("card_id") and item.get("quantity_sold")
            ]

            if total_owned_updates:
                await bulk_update_total_owned(total_owned_updates, connection=conn)

        # 2. 處理 unique_owner_count 減少（當卡片完全被刪除時）
        if deleted_cards:
            unique_owner_updates = [
                (deleted_card["card_id"], -1)
                for deleted_card in deleted_cards
                if deleted_card.get("card_id")
            ]

            if unique_owner_updates:
                await bulk_update_unique_owners(unique_owner_updates, connection=conn)

        # 3. 處理 favorite_count 減少（當收藏的卡片被完全刪除時）
        favorite_count_updates = [
            (deleted_card["card_id"], -1)
            for deleted_card in deleted_cards
            if deleted_card.get("card_id") and deleted_card.get("was_favorite", False)
        ]

        if favorite_count_updates:
            await bulk_update_favorite_counts_direct(
                favorite_count_updates, connection=conn
            )

    except Exception as e:
        logger.error(
            "[MARKET_STATS_SELL_TRANSACTION] 賣卡事務內統計更新失敗: %s",
            e,
            exc_info=True,
        )
        raise MarketStatsUpdateError(f"賣卡事務內統計更新失敗: {e}") from e


async def update_market_stats_for_ticket_exchange_in_transaction(
    conn: asyncpg.Connection,
    granted_card_ids: List[int],
    new_owner_card_ids: List[int],
) -> None:
    """
    在票券兌換事務中同步更新市場統計數據。

    :param conn: 已開啟事務的數據庫連接
    :param granted_card_ids: 授予的卡片ID列表
    :param new_owner_card_ids: 新擁有者的卡片ID列表（首次獲得的卡片）
    """
    try:
        logger.info(
            "[MARKET_STATS_TICKET_TRANSACTION] 開始在票券兌換事務中更新統計 - 授予: %s, 新擁有者: %s",
            len(granted_card_ids),
            len(new_owner_card_ids),
        )

        # 1. 處理 total_owned_quantity 增加
        if granted_card_ids:
            card_counts = {}
            for card_id in granted_card_ids:
                card_counts[card_id] = card_counts.get(card_id, 0) + 1

            total_owned_updates = list(card_counts.items())

            logger.debug(
                "[MARKET_STATS_TICKET_TRANSACTION] 增加總擁有量: %s",
                total_owned_updates,
            )
            await bulk_update_total_owned(total_owned_updates, connection=conn)

        # 2. 處理 unique_owner_count 增加（新擁有者）
        if new_owner_card_ids:
            unique_owner_updates = [(card_id, 1) for card_id in new_owner_card_ids]

            logger.debug(
                "[MARKET_STATS_TICKET_TRANSACTION] 增加獨立擁有者數: %s",
                unique_owner_updates,
            )
            await bulk_update_unique_owners(unique_owner_updates, connection=conn)

        logger.info(
            "[MARKET_STATS_TICKET_TRANSACTION] 票券兌換統計更新完成 - 總擁有量: %s, 獨立擁有者: %s",
            len(granted_card_ids),
            len(new_owner_card_ids),
        )

    except Exception as e:
        logger.error(
            "[MARKET_STATS_TICKET_TRANSACTION] 票券兌換事務內統計更新失敗: %s",
            e,
            exc_info=True,
        )
        raise MarketStatsUpdateError(f"票券兌換事務內統計更新失敗: {e}") from e
