-- migrations/create_balance_history_table.sql

-- 創建餘額歷史記錄表
CREATE TABLE balance_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    change_amount DECIMAL(20, 2) NOT NULL,
    balance_before DECIMAL(20, 2) NOT NULL,
    balance_after DECIMAL(20, 2) NOT NULL,
    transaction_type VARCHAR(50), -- 例如：'daily_reward', 'trade', 'game_win', 'sell_card'
    reason TEXT, -- 更詳細的原因，例如賣了什麼卡
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 為常用查詢欄位創建索引
CREATE INDEX idx_balance_history_user_id ON balance_history(user_id);
CREATE INDEX idx_balance_history_created_at ON balance_history(created_at);
CREATE INDEX idx_balance_history_transaction_type ON balance_history(transaction_type);

COMMENT ON TABLE balance_history IS '記錄用戶油幣餘額的所有變動歷史';
COMMENT ON COLUMN balance_history.user_id IS '用戶的 Discord ID';
COMMENT ON COLUMN balance_history.change_amount IS '本次變動的金額，正數為增加，負數為減少';
COMMENT ON COLUMN balance_history.balance_before IS '變動前的餘額';
COMMENT ON COLUMN balance_history.balance_after IS '變動後的餘額';
COMMENT ON COLUMN balance_history.transaction_type IS '交易類型，用於分類和篩選';
COMMENT ON COLUMN balance_history.reason IS '交易的具體原因或相關的元數據';
COMMENT ON COLUMN balance_history.created_at IS '記錄創建時間';