-- 生成100樓層（200個turn）測試數據的SQL腳本
-- Story ID: 0beb6b09-a4a4-4b86-88a8-e9696da8e4e5

-- 首先清除可能存在的舊數據
DELETE FROM story_turns 
WHERE story_id = '0beb6b09-a4a4-4b86-88a8-e9696da8e4e5' 
AND turn_number > 103; -- 保留現有的103個turn

-- 生成測試數據：從第104個turn開始到第200個turn
DO $$
DECLARE
    story_uuid UUID := '0beb6b09-a4a4-4b86-88a8-e9696da8e4e5';
    current_turn INTEGER;
    floor_num INTEGER;
    user_content TEXT;
    ai_content TEXT;
    ai_options TEXT[];
    status_content TEXT;
BEGIN
    -- 生成從104到200的turn（對應52到100樓層）
    FOR current_turn IN 104..200 LOOP
        floor_num := (current_turn + 1) / 2;
        
        IF current_turn % 2 = 0 THEN
            -- 偶數turn：USER turn
            user_content := format('選擇：在第%s樓層做出重要決定 - 選項%s', 
                                 floor_num, (current_turn % 4) + 1);
            
            INSERT INTO story_turns (
                story_id, turn_number, role, content, created_at
            ) VALUES (
                story_uuid, current_turn, 'user', user_content,
                CURRENT_TIMESTAMP
            );
            
        ELSE
            -- 奇數turn：ASSISTANT turn
            ai_content := format(
                '在第%s樓層的冒險中，英雄面臨了新的挑戰。經過一番思考和行動，情況發生了重要變化。' ||
                '這一層的經歷讓角色獲得了寶貴的經驗，同時也為接下來的冒險埋下了伏筆。' ||
                '周圍的環境充滿了神秘色彩，每一個選擇都可能影響後續的發展。', 
                floor_num
            );
            
            ai_options := ARRAY[
                format('勇敢地向第%s樓層前進', floor_num + 1),
                format('仔細搜索第%s樓層的隱藏區域', floor_num),
                format('與第%s樓層的NPC對話', floor_num),
                format('休息並整理第%s樓層的收穫', floor_num)
            ];
            
            status_content := format(
                '樓層：%s | 生命值：%s/100 | 魔力：%s/50 | 經驗值：%s', 
                floor_num, 
                85 + (current_turn % 15),  -- 85-99的生命值
                30 + (current_turn % 20),  -- 30-49的魔力值
                current_turn * 10          -- 經驗值隨turn增長
            );
            
            INSERT INTO story_turns (
                story_id, turn_number, role, content, options, status_block,
                summary, created_at
            ) VALUES (
                story_uuid, current_turn, 'assistant', ai_content, ai_options, status_content,
                format('第%s樓層：完成了重要的冒險活動', floor_num),
                CURRENT_TIMESTAMP
            );
        END IF;
        
        -- 每10個turn輸出進度
        IF current_turn % 10 = 0 THEN
            RAISE NOTICE '已生成到第%個turn（第%樓層）', current_turn, floor_num;
        END IF;
    END LOOP;
    
    RAISE NOTICE '成功生成100樓層測試數據！總共200個turn。';
END $$;