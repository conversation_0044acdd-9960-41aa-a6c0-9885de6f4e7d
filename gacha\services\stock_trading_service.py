import asyncio
import io
import logging
import math
import os
from datetime import datetime, timedelta, timezone
from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
import matplotlib
import matplotlib.axes as axes
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import matplotlib.ticker as mticker
import pytz
from matplotlib.figure import Figure
from matplotlib.font_manager import FontProperties, fontManager

from config.app_config import get_gacha_stock_integration_config
from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    InsufficientBalanceError,
    InsufficientStockQuantityError,
    InvalidQuantityError,
    MarketStockNotFoundError,
    StockNotFoundError,
    StockTradingError,
)
from gacha.models.history_models import TradeHistory
from gacha.models.market_models import StockLifecycleStatus
from gacha.repositories.market import stock_asset_repository
from gacha.services import economy_service, user_service
from utils.logger import logger

matplotlib.use("Agg")

# 完全禁用 matplotlib 的字體調試日誌
logging.getLogger("matplotlib.font_manager").setLevel(logging.CRITICAL)
logging.getLogger("matplotlib").setLevel(logging.WARNING)

# 全局字體配置緩存
_font_configured = False
_font_name = None

# 模組級別的配置和客戶端
_stock_config = get_gacha_stock_integration_config().stock_market
transaction_fee_rate = Decimal(str(_stock_config.transaction_fee_rate))
minimum_transaction_fee = Decimal(str(_stock_config.minimum_transaction_fee))
per_share_fee_amount = Decimal(str(_stock_config.per_share_fee_amount))

# 專業股票圖表配色方案
CHART_FIG_FACE_COLOR = "#1E1E1E"  # 更深的背景色
CHART_AX_FACE_COLOR = "#2D2D2D"  # 圖表區域背景
CHART_TEXT_COLOR = "#FFFFFF"  # 文字顏色
CHART_LINE_COLOR = "#00D4FF"  # 主線條顏色（亮藍色）
CHART_GRID_COLOR = "#404040"  # 網格線顏色
CHART_POSITIVE_COLOR = "#00FF88"  # 上漲顏色（綠色）
CHART_NEGATIVE_COLOR = "#FF4444"  # 下跌顏色（紅色）

# 台灣時區
TW_TIMEZONE = pytz.timezone("Asia/Taipei")


def _get_font_path(font_filename: str) -> str:
    """獲取字體文件的正確路徑，支持Docker和非Docker環境"""
    # 嘗試多個可能的路徑
    possible_paths = [
        os.path.join("fonts", font_filename),  # 相對路徑（Docker環境）
        os.path.join(os.getcwd(), "fonts", font_filename),  # 當前工作目錄
        os.path.join("/app", "fonts", font_filename),  # Docker容器內路徑
        os.path.join(r"D:\DICKPK", "fonts", font_filename),  # Windows開發環境路徑
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果都找不到，返回相對路徑作為fallback
    return os.path.join("fonts", font_filename)


def setup_chinese_fonts():
    """設定中文字體支援 - 優化版本，支持Docker環境"""
    global _font_configured, _font_name

    if _font_configured:
        return _font_name

    try:
        # 嘗試使用專案中的中文字體檔案（使用動態路徑解析）
        fonts_to_try = [
            _get_font_path("NotoSansTC-Regular.ttf"),
            _get_font_path("NotoSansTC-Bold.ttf"),
            _get_font_path("NotoSerifCJKtc-Medium.otf"),
        ]

        font_loaded = False
        loaded_fonts = []
        failed_fonts = []

        for font_path in fonts_to_try:
            if os.path.exists(font_path):
                try:
                    # 檢查字體是否已經載入
                    font_prop = FontProperties(fname=font_path)
                    font_name = font_prop.get_name()

                    # 只在需要時添加字體
                    if font_name not in [f.name for f in fontManager.ttflist]:
                        fontManager.addfont(font_path)

                    # 設定字體優先順序：專案字體 > 系統中文字體 > Docker內建字體
                    plt.rcParams["font.family"] = [
                        font_name,
                        "Noto Sans CJK TC",
                        "Microsoft JhengHei",
                        "DejaVu Sans",
                    ]
                    plt.rcParams["axes.unicode_minus"] = False

                    loaded_fonts.append(f"{font_name}({font_path})")
                    _font_name = font_name
                    font_loaded = True
                    break
                except Exception as e:
                    failed_fonts.append(f"{font_path}: {e}")
                    continue
            else:
                failed_fonts.append(f"{font_path}: 文件不存在")

        # 如果專案字體都載入失敗，嘗試使用Docker內建或系統中文字體
        if not font_loaded:
            # Docker環境通常有這些字體
            fallback_fonts = [
                "Noto Sans CJK TC",
                "Noto Sans CJK SC",
                "Microsoft JhengHei",
                "Microsoft YaHei",
            ]
            plt.rcParams["font.family"] = fallback_fonts + ["DejaVu Sans"]
            plt.rcParams["axes.unicode_minus"] = False
            _font_name = fallback_fonts[0]
            logger.warning("專案字體載入失敗，使用fallback字體: %s", fallback_fonts)
            if failed_fonts:
                logger.warning("字體載入失敗詳情: %s", failed_fonts)

        if loaded_fonts:
            logger.info("股票圖表中文字體載入成功: %s", loaded_fonts)

        _font_configured = True
        return _font_name

    except Exception as e:
        logger.error("字體設定失敗: %s", e)
        # 最後的後備方案
        plt.rcParams["font.family"] = [
            "Noto Sans CJK TC",
            "Microsoft JhengHei",
            "DejaVu Sans",
        ]
        plt.rcParams["axes.unicode_minus"] = False
        _font_configured = True
        _font_name = "Noto Sans CJK TC"
        return _font_name


# 初始化字體設定
setup_chinese_fonts()


def _calculate_transaction_details(
    quantity: int, price: Decimal, transaction_type: str
) -> Tuple[Decimal, Decimal, Decimal]:
    """
    Calculates total amount, fee, and net amount for a transaction.

    Returns:
        Tuple[Decimal, Decimal, Decimal]: (total_amount_before_fee, final_fee, net_amount_with_fee)

    Raises:
        InvalidGameActionError: If the transaction type is unknown.
    """
    from gacha.exceptions import InvalidGameActionError

    total_amount_before_fee = price * quantity
    fee_percentage = (total_amount_before_fee * transaction_fee_rate).quantize(
        Decimal("0.0001"), rounding=ROUND_HALF_UP
    )
    fee_per_share_total = (Decimal(str(quantity)) * per_share_fee_amount).quantize(
        Decimal("0.0001"), rounding=ROUND_HALF_UP
    )
    calculated_fee_before_min = max(fee_percentage, fee_per_share_total)
    final_fee = max(
        calculated_fee_before_min,
        minimum_transaction_fee.quantize(Decimal("0.0001"), rounding=ROUND_HALF_UP),
    )

    if transaction_type == "BUY" or transaction_type == "SHORT":
        # 買入和做空都需要支付成本+手續費
        net_amount_with_fee = total_amount_before_fee + final_fee
    elif transaction_type == "SELL" or transaction_type == "COVER":
        # 賣出和回補都是收益-手續費
        net_amount_with_fee = total_amount_before_fee - final_fee
    elif transaction_type == "DELISTED_SETTLEMENT":
        # 下市結算沒有手續費
        net_amount_with_fee = total_amount_before_fee
    else:
        raise InvalidGameActionError(
            action=f"未知的交易類型: {transaction_type}", game_type="stock"
        )

    return (total_amount_before_fee, final_fee, net_amount_with_fee)


async def _execute_buy_transaction_optimized(
    conn: asyncpg.Connection, user_id: int, asset_symbol: str, quantity: int
) -> Dict[str, Any]:
    """
    Service層：優化版購買交易 - 委託Repository層處理數據庫操作
    """
    # 使用Repository層的批量查詢方法
    data = await stock_asset_repository.get_stock_and_user_data_for_buy(
        asset_symbol, user_id, conn
    )
    if not data:
        raise MarketStockNotFoundError(
            f"股票代碼 {asset_symbol} 不存在或價格異常", stock_symbol=asset_symbol
        )

    asset_id, current_price, locked_symbol, lifecycle_status = (
        data["asset_id"],
        Decimal(data["current_price"]),
        data["asset_symbol"],
        data["lifecycle_status"],
    )

    if lifecycle_status == StockLifecycleStatus.DELISTED.value:
        raise StockTradingError(f"股票 {locked_symbol} 已退市，無法進行買入操作。")
    current_balance = Decimal(data["oil_balance"])

    # Service層負責業務邏輯計算
    _, fee, total_cost_with_fee = _calculate_transaction_details(
        quantity, current_price, "BUY"
    )

    if current_balance < total_cost_with_fee:
        raise InsufficientBalanceError(
            message=f"餘額不足：需要 {math.ceil(total_cost_with_fee)} 油幣，目前餘額 {int(current_balance)} 油幣",
            required=math.ceil(total_cost_with_fee),
            current=int(current_balance),
        )

    # 使用 economy_service 處理餘額變更
    await economy_service.award_oil(
        user_id=user_id,
        amount=-math.ceil(total_cost_with_fee),
        transaction_type="stock:buy",
        reason=f"Buy {quantity} of {locked_symbol} at {current_price}",
        connection=conn,
    )

    # 委託Repository層執行批量數據庫操作
    await stock_asset_repository.execute_buy_transaction_batch(
        user_id, asset_id, quantity, current_price, fee, conn
    )

    return {"symbol": locked_symbol, "total_cost": total_cost_with_fee, "fee": fee}


async def _execute_sell_transaction_optimized(
    conn: asyncpg.Connection, user_id: int, asset_symbol: str, quantity: int
) -> Dict[str, Any]:
    """
    Service層：優化版出售交易 - 委託Repository層處理數據庫操作
    """
    # 使用Repository層的批量查詢方法
    data = await stock_asset_repository.get_stock_and_user_data_for_sell(
        asset_symbol, user_id, conn
    )
    if not data:
        raise MarketStockNotFoundError(
            f"股票代碼 {asset_symbol} 不存在或價格異常", stock_symbol=asset_symbol
        )

    asset_id, current_price, locked_symbol, lifecycle_status = (
        data["asset_id"],
        Decimal(data["current_price"]),
        data["asset_symbol"],
        data["lifecycle_status"],
    )

    if lifecycle_status == StockLifecycleStatus.DELISTED.value:
        raise StockTradingError(f"股票 {locked_symbol} 已退市，無法進行賣出操作。")
    stock_quantity, portfolio_id = (
        data["stock_quantity"],
        data["portfolio_id"],
    )

    # Service層負責業務邏輯驗證
    if stock_quantity < quantity:
        raise InsufficientStockQuantityError(
            required=quantity,
            current=stock_quantity,
        )

    # Service層負責業務邏輯計算
    total_proceeds_before_fee, fee, total_proceeds_after_fee = (
        _calculate_transaction_details(quantity, current_price, "SELL")
    )

    # 使用 economy_service 處理餘額變更
    await economy_service.award_oil(
        user_id=user_id,
        amount=math.floor(total_proceeds_after_fee),
        transaction_type="stock:sell",
        reason=f"Sell {quantity} of {locked_symbol} at {current_price}",
        connection=conn,
    )

    # 準備上下文
    context = {"avg_cost": float(data.get("average_buy_price", 0))}

    # 委託Repository層執行批量數據庫操作
    await stock_asset_repository.execute_sell_transaction_batch(
        user_id,
        asset_id,
        quantity,
        current_price,
        total_proceeds_before_fee,
        fee,
        stock_quantity,
        portfolio_id,
        context,
        conn,
    )

    return {"symbol": locked_symbol, "proceeds": total_proceeds_after_fee, "fee": fee}


async def get_asset_id_by_symbol(asset_symbol: str) -> Optional[int]:
    """
    根據股票代碼獲取資產ID。

    Args:
        asset_symbol: 股票代碼

    Returns:
        Optional[int]: 資產ID，如果不存在則返回 None
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        stock_asset = await stock_asset_repository.get_asset_by_symbol(
            asset_symbol, conn
        )
        return stock_asset.asset_id if stock_asset else None


async def get_stock_details(asset_symbol: str) -> Optional[Dict[str, Any]]:
    """
    獲取股票詳細信息。

    Args:
        asset_symbol: 股票代碼

    Returns:
        Optional[Dict]: 股票詳細信息，如果不存在則返回 None
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        return await stock_asset_repository.get_asset_details_by_symbol_or_id(
            asset_symbol, conn
        )


async def get_all_stocks_paginated(page: int = 1, per_page: int = 10) -> Dict[str, Any]:
    """
    分頁獲取股票列表。

    Args:
        page: 頁碼
        per_page: 每頁數量

    Returns:
        Dict: 包含股票列表和分頁信息的字典
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        result = await stock_asset_repository.get_stocks_paginated(page, per_page, conn)

        # 處理趨勢數據
        stocks_processed = []
        for stock_data in result["stocks"]:
            current_price_from_va = Decimal(str(stock_data["current_price"]))
            previous_price_for_trend = stock_data.get("previous_price")

            if previous_price_for_trend is not None:
                previous_price_for_trend = Decimal(str(previous_price_for_trend))
                if previous_price_for_trend == current_price_from_va:
                    stock_data["previous_price"] = None
                else:
                    stock_data["previous_price"] = previous_price_for_trend
            else:
                stock_data["previous_price"] = None

            stocks_processed.append(stock_data)

        return {
            "stocks": stocks_processed,
            "total_stocks": result["total_stocks"],
            "page": page,
            "per_page": per_page,
            "total_pages": result["total_pages"],
        }


async def buy_stock(user_id: int, asset_symbol: str, quantity: int) -> str:
    """
    優化版股票購買 - 使用批量操作減少數據庫往返次數

    Returns:
        str: A success message with transaction details.

    Raises:
        InvalidQuantityError: If the quantity is invalid.
        MarketStockNotFoundError: If the stock is not found.
        InsufficientBalanceError: If the user has insufficient balance.
        Exception: For unexpected database errors.
    """
    if quantity <= 0:
        raise InvalidQuantityError(quantity=quantity)

    from gacha.utils.deadlock_retry import standard_retry

    asset_symbol_upper = asset_symbol.upper()

    async def _buy_transaction():
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            async with conn.transaction():
                # 根據規範，不再捕捉通用 Exception，讓錯誤自然冒泡
                result = await _execute_buy_transaction_optimized(
                    conn, user_id, asset_symbol_upper, quantity
                )
                stock_cost = result["total_cost"] - result["fee"]
                return f"成功購買 {quantity} 股 {result['symbol']}，總共花費 {result['total_cost']:.0f} 油幣 (其中股票成本 {stock_cost:.0f}，手續費 {result['fee']:.0f})。"

    return await standard_retry(_buy_transaction)


async def sell_stock(user_id: int, asset_symbol: str, quantity: int) -> str:
    """優化版股票出售 - 使用批量操作減少數據庫往返次數

    Returns:
        str: A success message with transaction details.

    Raises:
        InvalidQuantityError: If the quantity is invalid.
        MarketStockNotFoundError: If the stock is not found.
        InsufficientStockQuantityError: If the user has insufficient stock quantity.
        Exception: For unexpected database errors.
    """
    if quantity <= 0:
        raise InvalidQuantityError(quantity=quantity)

    from gacha.utils.deadlock_retry import standard_retry

    asset_symbol_upper = asset_symbol.upper()

    async def _sell_transaction():
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            async with conn.transaction():
                # 根據規範，不再捕捉通用 Exception，讓錯誤自然冒泡
                result = await _execute_sell_transaction_optimized(
                    conn, user_id, asset_symbol_upper, quantity
                )
                gross_proceeds = result["proceeds"] + result["fee"]
                return f"成功出售 {quantity} 股 {result['symbol']}，總共獲得 {result['proceeds']:.0f} 油幣 (出售總額 {gross_proceeds:.0f}，已扣除手續費 {result['fee']:.0f})。"

    return await standard_retry(_sell_transaction)


async def get_user_stock_quantity(user_id: int, asset_id: int) -> int:
    """Fetches the quantity of a specific stock a user owns."""
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        quantity = await conn.fetchval(
            "SELECT quantity FROM player_portfolios WHERE user_id = $1 AND asset_id = $2",
            user_id,
            asset_id,
        )
        return quantity if quantity is not None else 0


async def get_user_portfolio_assets(user_id: int) -> List[int]:
    """Fetches a list of asset_ids the user currently holds (持有股票，不包括做空倉位)."""
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        rows = await conn.fetch(
            "SELECT asset_id FROM player_portfolios WHERE user_id = $1 AND quantity > 0",
            user_id,
        )
        return [row["asset_id"] for row in rows]


async def get_user_short_position_assets(user_id: int) -> List[int]:
    """Fetches a list of asset_ids the user currently has short positions in (做空倉位)."""
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        rows = await conn.fetch(
            "SELECT asset_id FROM player_portfolios WHERE user_id = $1 AND short_quantity > 0",
            user_id,
        )
        return [row["asset_id"] for row in rows]


async def get_user_portfolio(user_id: int) -> Dict[str, Any]:
    """
    獲取用戶投資組合，包括持有股票和做空倉位

    Returns:
        Dict包含:
        - holdings: 持有股票列表
        - short_positions: 做空倉位列表
        - 各種統計數據
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        # 查詢持有股票和做空倉位
        query = """
        SELECT pp.asset_id, va.asset_symbol, va.asset_name,
               pp.quantity, pp.average_buy_price,
               pp.short_quantity, pp.short_average_price,
               va.current_price AS current_market_price, va.lifecycle_status
        FROM player_portfolios pp
        JOIN virtual_assets va ON pp.asset_id = va.asset_id
        WHERE pp.user_id = $1 AND (pp.quantity > 0 OR pp.short_quantity > 0)
        ORDER BY va.asset_symbol
        """
        portfolio_rows = await conn.fetch(query, user_id)

        holdings = []  # 持有股票
        short_positions = []  # 做空倉位
        total_portfolio_value_at_current = Decimal(0)
        total_portfolio_cost_basis = Decimal(0)
        total_short_pnl = Decimal(0)

        for row in portfolio_rows:
            current_market_price = Decimal(row["current_market_price"])
            asset_data = {
                "asset_id": row["asset_id"],
                "asset_symbol": row["asset_symbol"],
                "asset_name": row["asset_name"],
                "current_market_price": float(current_market_price),
                "lifecycle_status": row["lifecycle_status"],
            }

            # 處理持有股票
            if row["quantity"] > 0:
                quantity = Decimal(row["quantity"])
                avg_buy_price = Decimal(row["average_buy_price"])
                cost_basis = quantity * avg_buy_price
                current_value = quantity * current_market_price
                unrealized_pnl = current_value - cost_basis

                total_portfolio_value_at_current += current_value
                total_portfolio_cost_basis += cost_basis

                holding_data = asset_data.copy()
                holding_data.update(
                    {
                        "quantity": int(quantity),
                        "average_buy_price": float(avg_buy_price),
                        "cost_basis": float(cost_basis),
                        "current_value": float(current_value),
                        "unrealized_pnl": float(unrealized_pnl),
                    }
                )
                holdings.append(holding_data)

            # 處理做空倉位
            if row["short_quantity"] > 0:
                short_quantity = Decimal(row["short_quantity"])
                short_avg_price = Decimal(row["short_average_price"])
                original_bet = short_quantity * short_avg_price
                profit_per_share = short_avg_price - current_market_price
                short_pnl = profit_per_share * short_quantity

                total_short_pnl += short_pnl

                short_data = asset_data.copy()
                short_data.update(
                    {
                        "short_quantity": int(short_quantity),
                        "short_average_price": float(short_avg_price),
                        "original_bet": float(original_bet),
                        "short_pnl": float(short_pnl),
                    }
                )
                short_positions.append(short_data)

        # 計算總體統計
        overall_unrealized_pnl = (
            total_portfolio_value_at_current
            - total_portfolio_cost_basis
            + total_short_pnl
        )
        user_gacha_info = await user_service.get_user(user_id, create_if_missing=True)
        user_oil_balance = user_gacha_info.oil_balance if user_gacha_info else 0

        return {
            "user_oil_balance": user_oil_balance,
            "holdings": holdings,
            "short_positions": short_positions,
            "total_portfolio_value_at_current_market_price": float(
                total_portfolio_value_at_current
            ),
            "total_portfolio_cost_basis": float(total_portfolio_cost_basis),
            "total_short_pnl": float(total_short_pnl),
            "overall_unrealized_pnl": float(overall_unrealized_pnl),
            "total_asset_value": float(
                user_oil_balance + total_portfolio_value_at_current + total_short_pnl
            ),
        }


async def get_aggregated_buy_sell_volume_7d(asset_id: int) -> Dict[str, int]:
    """
    Fetches the aggregated buy and sell volume for a given asset_id over the last 7 days.
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        return await _get_aggregated_buy_sell_volume_7d_with_conn(conn, asset_id)


async def _get_aggregated_buy_sell_volume_7d_with_conn(
    conn: asyncpg.Connection, asset_id: int
) -> Dict[str, int]:
    """
    Internal method with connection reuse for aggregated buy/sell volume.
    """
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    query = """
        SELECT
            COALESCE(SUM(CASE WHEN transaction_type = 'BUY' THEN quantity ELSE 0 END), 0) as total_buy_volume,
            COALESCE(SUM(CASE WHEN transaction_type = 'SELL' THEN quantity ELSE 0 END), 0) as total_sell_volume
        FROM market_transactions
        WHERE asset_id = $1 AND timestamp >= $2;
    """
    try:
        row = await conn.fetchrow(query, asset_id, seven_days_ago)
        if row:
            return {
                "total_buy_volume": int(row["total_buy_volume"]),
                "total_sell_volume": int(row["total_sell_volume"]),
            }
        return {"total_buy_volume": 0, "total_sell_volume": 0}
    except Exception as e:
        logger.error(
            "Error fetching aggregated buy/sell volume for asset %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return {"total_buy_volume": 0, "total_sell_volume": 0}


async def get_price_history_for_chart_7d(asset_id: int) -> List[Dict[str, Any]]:
    """
    Fetches and downsamples price history for a given asset_id over the last 7 days,
    aiming for approximately hourly data points.
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        return await _get_price_history_for_chart_7d_with_conn(conn, asset_id)


async def _get_price_history_for_chart_7d_with_conn(
    conn: asyncpg.Connection, asset_id: int
) -> List[Dict[str, Any]]:
    """
    Internal method with connection reuse for price history.
    """
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    query = """
        SELECT
            date_trunc('hour', timestamp) as hour_bucket,
            AVG(price) as avg_price
        FROM asset_price_history
        WHERE asset_id = $1 AND timestamp >= $2
        GROUP BY hour_bucket
        ORDER BY hour_bucket ASC;
    """
    try:
        rows = await conn.fetch(query, asset_id, seven_days_ago)
        price_history = []
        for row in rows:
            price_history.append(
                {"timestamp": row["hour_bucket"], "price": Decimal(row["avg_price"])}
            )
        return price_history
    except Exception as e:
        logger.error(
            "Error fetching price history for chart for asset %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return []


async def get_recent_transactions(
    asset_id: int, limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Fetches the most recent transactions for a given asset_id, including user display names.
    Uses database nickname field directly for better performance.
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        return await _get_recent_transactions_with_conn(conn, asset_id, limit)


async def _get_recent_transactions_with_conn(
    conn: asyncpg.Connection, asset_id: int, limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Internal method with connection reuse for recent transactions.
    Optimized to avoid slow JOIN by separating transaction and user queries.
    """
    try:
        # 1. 快速獲取交易記錄（避免JOIN用戶表）
        transaction_query = """
            SELECT user_id, transaction_type, quantity, price_per_unit, timestamp
            FROM market_transactions
            WHERE asset_id = $1
            ORDER BY timestamp DESC
            LIMIT $2
        """

        transaction_rows = await conn.fetch(transaction_query, asset_id, limit)
        if not transaction_rows:
            return []

        # 2. 批量獲取用戶暱稱
        user_ids = [row["user_id"] for row in transaction_rows]
        user_query = """
            SELECT user_id, COALESCE(nickname, CAST(user_id AS VARCHAR)) as user_nickname
            FROM gacha_users
            WHERE user_id = ANY($1)
        """

        user_rows = await conn.fetch(user_query, user_ids)
        user_map = {row["user_id"]: row["user_nickname"] for row in user_rows}

        # 3. 組合結果
        recent_transactions = []
        for row in transaction_rows:
            recent_transactions.append(
                {
                    "user_nickname": user_map.get(row["user_id"], str(row["user_id"])),
                    "transaction_type": row["transaction_type"],
                    "quantity": row["quantity"],
                    "price_per_unit": Decimal(row["price_per_unit"]),
                    "timestamp": row["timestamp"],
                }
            )

        return recent_transactions
    except Exception as e:
        logger.error(
            "Error fetching recent transactions for asset %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return []


def _apply_chart_styles(ax: axes.Axes, asset_symbol: str) -> None:
    """設定圖表樣式 - 統一版本"""
    # 設定圖表顏色
    ax.set_facecolor(CHART_AX_FACE_COLOR)

    # 設定標題和標籤
    days_limit = _stock_config.price_history_days
    ax.set_title(
        f"{asset_symbol} 價格趨勢 (最近 {days_limit} 天)",
        fontsize=16,
        fontweight="bold",
        color=CHART_TEXT_COLOR,
        pad=20,
    )
    ax.set_xlabel("日期時間 (UTC+8)", fontsize=12, color=CHART_TEXT_COLOR, labelpad=10)
    ax.set_ylabel("價格", fontsize=12, color=CHART_TEXT_COLOR, labelpad=10)

    # 設定網格
    ax.grid(True, color=CHART_GRID_COLOR, linestyle="-", linewidth=0.5, alpha=0.7)
    ax.set_axisbelow(True)

    # 設定邊框
    for spine in ax.spines.values():
        spine.set_color(CHART_GRID_COLOR)
        spine.set_linewidth(1)

    # 設定刻度標籤
    ax.tick_params(colors=CHART_TEXT_COLOR, labelsize=10)


def _draw_simple_price_chart(
    ax: axes.Axes, timestamps: List, prices: List[float]
) -> None:
    """繪製簡單的價格曲線圖"""
    if len(prices) < 2:
        return

    # 使用固定的藍色線條和點
    line_color = CHART_LINE_COLOR  # 固定使用藍色 #00D4FF

    # 只繪製簡單的價格線條，每個數據點都有圓點
    ax.plot(
        timestamps,
        prices,
        color=line_color,
        linewidth=2,
        alpha=0.9,
        marker="o",
        markersize=5,
        markerfacecolor=line_color,
        markeredgewidth=0,
    )


def _add_price_info(ax: axes.Axes, prices: List[float]) -> None:
    """在圖表上添加簡單的價格統計信息"""
    if not prices:
        return

    min_price = min(prices)
    max_price = max(prices)
    current_price = prices[-1]

    # 簡單的價格信息框
    info_text = (
        f"當前: {current_price:.2f}\n最高: {max_price:.2f}\n最低: {min_price:.2f}"
    )

    ax.text(
        0.98,
        0.98,
        info_text,
        transform=ax.transAxes,
        fontsize=9,
        color=CHART_TEXT_COLOR,
        verticalalignment="top",
        horizontalalignment="right",
        bbox={
            "boxstyle": "round,pad=0.3",
            "facecolor": CHART_AX_FACE_COLOR,
            "edgecolor": CHART_GRID_COLOR,
            "alpha": 0.9,
            "linewidth": 0.5,
        },
    )


def _do_generate_price_trend_chart(
    asset_symbol: str, price_history_data: List[Dict[str, Any]]
) -> Optional[bytes]:
    """
    生成簡潔的價格趨勢曲線圖 - 線程安全版本
    """
    if not price_history_data:
        logger.warning(
            "No price history data provided for chart generation of %s.", asset_symbol
        )
        return None

    # 確保字體已配置
    setup_chinese_fonts()

    # 預處理數據
    timestamps_utc = [item["timestamp"] for item in price_history_data]
    prices = [float(item["price"]) for item in price_history_data]

    if not timestamps_utc or not prices:
        logger.warning(
            "Empty timestamps or prices for chart generation of %s.", asset_symbol
        )
        return None

    # 轉換時間戳為台灣時間
    timestamps_tw = [
        (
            ts.replace(tzinfo=timezone.utc)
            if ts.tzinfo is None
            else ts.astimezone(timezone.utc)
        ).astimezone(TW_TIMEZONE)
        for ts in timestamps_utc
    ]

    # 創建獨立的 Figure 和 Axes 物件
    fig = Figure(
        figsize=(_stock_config.chart_figsize_width, _stock_config.chart_figsize_height),
        facecolor=CHART_FIG_FACE_COLOR,
    )
    ax = fig.add_subplot(1, 1, 1)

    # 應用樣式
    _apply_chart_styles(ax, asset_symbol)

    # 繪製簡單圖表
    _draw_simple_price_chart(ax, timestamps_tw, prices)

    # 設定格式化器
    date_formatter = mdates.DateFormatter(_stock_config.chart_date_format)
    ax.xaxis.set_major_formatter(date_formatter)
    ax.xaxis.set_major_locator(
        mticker.MaxNLocator(nbins=_stock_config.chart_max_xticks, prune="both")
    )

    # 直接在 ax 上設定刻度標籤屬性
    for label in ax.get_xticklabels():
        label.set_rotation(45)
        label.set_horizontalalignment("right")

    ax.yaxis.set_major_formatter(mticker.FormatStrFormatter("%.2f"))

    # 添加價格信息
    _add_price_info(ax, prices)

    fig.tight_layout(pad=1.0)

    # 保存圖表
    buf = io.BytesIO()
    try:
        # 直接使用 fig 物件的 savefig 方法
        fig.savefig(
            buf,
            format="png",
            dpi=_stock_config.chart_dpi,
            bbox_inches="tight",
            facecolor=CHART_FIG_FACE_COLOR,
            edgecolor="none",
        )
        buf.seek(0)
        return buf.getvalue()
    except Exception as e_save:
        logger.error(
            "Error saving Matplotlib chart for %s to buffer: %s",
            asset_symbol,
            e_save,
            exc_info=True,
        )
        return None
    finally:
        # 不需要再呼叫 plt.close() 或 plt.clf()
        # fig 物件會在函式結束後自動被垃圾回收
        buf.close()


async def generate_price_trend_chart_7d(
    asset_id: int, asset_symbol: str, price_history_data: List[Dict[str, Any]]
) -> Optional[bytes]:
    """
    Asynchronously generates a 7-day price trend chart image using Matplotlib with asyncio.to_thread.
    """
    if not price_history_data:
        price_history_data = await get_price_history_for_chart_7d(asset_id)
        if not price_history_data:
            logger.warning(
                "Still no price history data after fetching for asset %s to generate chart.",
                asset_id,
            )
            return None

    try:
        # 使用 asyncio.to_thread 來處理 matplotlib 阻塞操作
        image_bytes = await asyncio.to_thread(
            _do_generate_price_trend_chart, asset_symbol, price_history_data
        )
        return image_bytes
    except Exception as e:
        logger.error(
            "Error in asyncio.to_thread for chart generation for asset %s: %s",
            asset_symbol,
            e,
            exc_info=True,
        )
        return None


async def _get_user_specific_stock_details_with_conn(
    conn: asyncpg.Connection, user_id: int, asset_id: int
) -> Tuple[Optional[Decimal], Optional[int], Optional[int]]:
    """Internal method with connection reuse for user-specific stock details."""
    user_oil_balance: Optional[Decimal] = None
    user_stock_quantity: Optional[int] = None
    user_short_quantity: Optional[int] = None
    try:
        # 批量查詢用戶油幣餘額、股票持有量和做空數量
        user_data_query = """
            SELECT
                gu.oil_balance,
                COALESCE(pp.quantity, 0) as stock_quantity,
                COALESCE(pp.short_quantity, 0) as short_quantity
            FROM gacha_users gu
            LEFT JOIN player_portfolios pp ON gu.user_id = pp.user_id AND pp.asset_id = $2
            WHERE gu.user_id = $1
        """
        row = await conn.fetchrow(user_data_query, user_id, asset_id)
        if row:
            user_oil_balance = Decimal(row["oil_balance"])
            user_stock_quantity = int(row["stock_quantity"])
            user_short_quantity = int(row["short_quantity"])
    except Exception as e_user_data:
        logger.error(
            "Error fetching user-specific data for user %s, asset %s: %s",
            user_id,
            asset_id,
            e_user_data,
            exc_info=True,
        )
    return (user_oil_balance, user_stock_quantity, user_short_quantity)


async def get_stock_details_for_view(
    asset_symbol_or_id: Union[str, int], user_id: Optional[int] = None
) -> Optional[Dict[str, Any]]:
    """
    Fetches all necessary details for the StockDetailView with optimized connection reuse.
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        # 1. 獲取基本股票數據
        stock_data_dict = (
            await stock_asset_repository.get_asset_details_by_symbol_or_id(
                asset_symbol_or_id, conn
            )
        )

        if not stock_data_dict:
            logger.warning(
                "Could not find stock data for %s in get_stock_details_for_view.",
                asset_symbol_or_id,
            )
            raise StockNotFoundError(asset_symbol_or_id=asset_symbol_or_id)
        asset_id = stock_data_dict["asset_id"]
        asset_symbol = stock_data_dict["asset_symbol"]

        # 2. 批量獲取所有相關數據（復用同一個連接）
        aggregated_volume_7d = await _get_aggregated_buy_sell_volume_7d_with_conn(
            conn, asset_id
        )
        recent_transactions_5 = await _get_recent_transactions_with_conn(
            conn, asset_id, limit=5
        )
        price_history_for_chart = await _get_price_history_for_chart_7d_with_conn(
            conn, asset_id
        )

        # 3. 獲取用戶特定數據（如果需要）
        user_oil_balance: Optional[Decimal] = None
        user_stock_quantity: Optional[int] = None
        user_short_quantity: Optional[int] = None
        if user_id is not None:
            (
                user_oil_balance,
                user_stock_quantity,
                user_short_quantity,
            ) = await _get_user_specific_stock_details_with_conn(
                conn, user_id, asset_id
            )

    # 4. 生成圖表（在連接外執行，避免長時間佔用連接）
    chart_image_bytes: Optional[bytes] = None
    if price_history_for_chart:
        chart_image_bytes = await generate_price_trend_chart_7d(
            asset_id, asset_symbol, price_history_for_chart
        )
    else:
        logger.warning(
            "No price history data to generate chart for asset %s in get_stock_details_for_view.",
            asset_id,
        )

    return {
        "stock_data": stock_data_dict,
        "aggregated_volume_7d": aggregated_volume_7d,
        "recent_transactions_5": recent_transactions_5,
        "chart_image_bytes": chart_image_bytes,
        "user_oil_balance": user_oil_balance,
        "user_stock_quantity": user_stock_quantity,
        "user_short_quantity": user_short_quantity,
    }


async def get_related_news_for_stock(
    asset_id: int, limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Fetches recent news headlines related to a specific stock asset_id.
    """
    query = "\n            SELECT headline, published_at, news_type, character_archetype, character_name\n            FROM market_news\n            WHERE affected_asset_id = $1\n            ORDER BY published_at DESC\n            LIMIT $2;\n        "
    try:
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            news_records = await conn.fetch(query, asset_id, limit)
        return [dict(record) for record in news_records]
    except Exception as e:
        logger.error(
            "Error fetching related news for asset %s: %s", asset_id, e, exc_info=True
        )
        return []


async def search_asset_symbols_with_details(search_term: str) -> List[Dict[str, Any]]:
    """
    搜索資產代碼並返回詳細資訊（用於更詳細的自動完成）。
    只返回活躍和ST狀態的股票，過濾掉已退市的股票。

    Args:
        search_term: 搜索詞

    Returns:
        List[Dict[str, Any]]: 包含資產代碼、名稱、價格、狀態等詳細資訊的列表
    """
    try:
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            return await stock_asset_repository.search_asset_symbols_with_details(
                search_term, 25, conn
            )
    except Exception as e:
        logger.error(
            "Error during search_asset_symbols_with_details: %s", e, exc_info=True
        )
        return []


# ==================== 做空功能相關方法 ====================


async def _execute_short_transaction_optimized(
    conn: asyncpg.Connection, user_id: int, asset_symbol: str, quantity: int
) -> Dict[str, Any]:
    """
    Service層：優化版做空交易 - 委託Repository層處理數據庫操作
    """
    # 使用Repository層的批量查詢方法
    data = await stock_asset_repository.get_stock_and_user_data_for_short(
        asset_symbol, user_id, conn
    )
    if not data:
        raise MarketStockNotFoundError(
            f"股票代碼 {asset_symbol} 不存在或價格異常", stock_symbol=asset_symbol
        )

    asset_id, current_price, locked_symbol = (
        data["asset_id"],
        Decimal(data["current_price"]),
        data["asset_symbol"],
    )
    current_balance, lifecycle_status = (
        Decimal(data["oil_balance"]),
        data["lifecycle_status"],
    )

    # 檢查股票是否允許做空（不能是ST或DELISTED狀態）
    if lifecycle_status == StockLifecycleStatus.ST.value:
        raise StockTradingError(
            f"高風險(ST)股票 {locked_symbol} 不允許開立新的做空倉位。"
        )
    if lifecycle_status == StockLifecycleStatus.DELISTED.value:
        raise StockTradingError(f"股票 {locked_symbol} 已退市，無法進行做空操作。")

    # Service層負責業務邏輯計算
    bet_amount = quantity * current_price  # 押注金額
    _, fee, total_cost_with_fee = _calculate_transaction_details(
        quantity, current_price, "SHORT"
    )

    if current_balance < total_cost_with_fee:
        raise InsufficientBalanceError(
            message=f"餘額不足：需要 {math.ceil(total_cost_with_fee)} 油幣，目前餘額 {int(current_balance)} 油幣",
            required=math.ceil(total_cost_with_fee),
            current=int(current_balance),
        )

    # 使用 economy_service 處理餘額變更
    await economy_service.award_oil(
        user_id=user_id,
        amount=-math.ceil(total_cost_with_fee),
        transaction_type="stock:short",
        reason=f"Short {quantity} of {locked_symbol} at {current_price}",
        connection=conn,
    )

    # 委託Repository層執行批量數據庫操作
    await stock_asset_repository.execute_short_transaction_batch(
        user_id, asset_id, quantity, current_price, fee, conn
    )

    return {"symbol": locked_symbol, "bet_amount": bet_amount, "fee": fee}


async def _execute_cover_transaction_optimized(
    conn: asyncpg.Connection, user_id: int, asset_symbol: str, quantity: int
) -> Dict[str, Any]:
    """
    Service層：優化版回補交易 - 委託Repository層處理數據庫操作
    """
    # 使用Repository層的批量查詢方法
    data = await stock_asset_repository.get_stock_and_user_data_for_cover(
        asset_symbol, user_id, conn
    )
    if not data:
        raise MarketStockNotFoundError(
            f"股票代碼 {asset_symbol} 不存在或價格異常", stock_symbol=asset_symbol
        )

    asset_id, current_price, locked_symbol, lifecycle_status = (
        data["asset_id"],
        Decimal(data["current_price"]),
        data["asset_symbol"],
        data["lifecycle_status"],
    )

    if lifecycle_status == StockLifecycleStatus.DELISTED.value:
        raise StockTradingError(f"股票 {locked_symbol} 已退市，無法進行回補操作。")
    short_quantity, short_avg_price, portfolio_id = (
        data["short_quantity"],
        Decimal(str(data["short_average_price"])),
        data["portfolio_id"],
    )

    # Service層負責業務邏輯驗證
    if short_quantity < quantity:
        raise InsufficientStockQuantityError(
            required=quantity,
            current=short_quantity,
            message=f"做空持倉不足！您試圖回補 {quantity:,} 股，但目前只做空 {short_quantity:,} 股。",
        )

    # Service層負責業務邏輯計算
    original_bet = quantity * short_avg_price  # 原押注金額
    profit_per_share = short_avg_price - current_price  # 做空：價格下跌為正收益
    total_profit = profit_per_share * quantity
    settlement_amount = original_bet + total_profit  # 原押注 + 盈虧

    # 計算手續費（基於回補時的市場總值，與賣出邏輯統一）
    market_value_at_cover = current_price * quantity
    fee_percentage = (market_value_at_cover * transaction_fee_rate).quantize(
        Decimal("0.0001"), rounding=ROUND_HALF_UP
    )
    fee_per_share_total = (Decimal(str(quantity)) * per_share_fee_amount).quantize(
        Decimal("0.0001"), rounding=ROUND_HALF_UP
    )
    calculated_fee_before_min = max(fee_percentage, fee_per_share_total)
    final_fee = max(
        calculated_fee_before_min,
        minimum_transaction_fee.quantize(Decimal("0.0001"), rounding=ROUND_HALF_UP),
    )

    net_return = settlement_amount - final_fee

    # 使用 economy_service 處理餘額變更
    await economy_service.award_oil(
        user_id=user_id,
        amount=math.floor(net_return),
        transaction_type="stock:cover",
        reason=f"Cover {quantity} of {locked_symbol} at {current_price}",
        connection=conn,
    )

    # 準備上下文
    context = {"avg_cost": float(short_avg_price)}

    # 委託Repository層執行批量數據庫操作
    await stock_asset_repository.execute_cover_transaction_batch(
        user_id,
        asset_id,
        quantity,
        current_price,
        settlement_amount,
        final_fee,
        short_quantity,
        portfolio_id,
        context,
        conn,
    )

    return {
        "symbol": locked_symbol,
        "settlement": net_return,
        "fee": final_fee,
        "profit_loss": total_profit,
    }


async def short_stock(user_id: int, asset_symbol: str, quantity: int) -> str:
    """
    做空股票 - 直接扣錢模式

    Args:
        user_id: 用戶ID
        asset_symbol: 股票代碼
        quantity: 做空數量

    Returns:
        str: 成功消息

    Raises:
        InvalidQuantityError: If the quantity is invalid.
        MarketStockNotFoundError: If the stock is not found.
        InsufficientBalanceError: If the user has insufficient balance.
        StockTradingError: If shorting is not allowed.
        Exception: For unexpected database errors.
    """
    if quantity <= 0:
        raise InvalidQuantityError(quantity=quantity)

    from gacha.utils.deadlock_retry import standard_retry

    asset_symbol_upper = asset_symbol.upper()

    async def _short_transaction():
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            async with conn.transaction():
                result = await _execute_short_transaction_optimized(
                    conn, user_id, asset_symbol_upper, quantity
                )
                total_cost = result["bet_amount"] + result["fee"]
                return f"成功做空 {quantity} 股 {result['symbol']}，總共花費 {total_cost:.0f} 油幣 (其中押注金額 {result['bet_amount']:.0f}，手續費 {result['fee']:.0f})。"

    return await standard_retry(_short_transaction)


async def get_user_trade_history_paginated(
    user_id: int, page: int = 1, per_page: int = 10
) -> Dict[str, Any]:
    """
    獲取用戶交易歷史（分頁）。

    Args:
        user_id: 用戶ID
        page: 頁碼
        per_page: 每頁數量

    Returns:
        Dict: 包含 TradeHistory 模型列表和分頁信息的字典
    """
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        result = await stock_asset_repository.get_trade_history_by_user_paginated(
            user_id, page, per_page, conn
        )

        # 將原始數據映射到 Pydantic 模型
        trades = [TradeHistory(**trade_data) for trade_data in result["trades"]]

        return {
            "trades": trades,
            "total_trades": result["total_trades"],
            "current_page": result["current_page"],
            "per_page": result["per_page"],
            "total_pages": result["total_pages"],
        }


async def cover_short(user_id: int, asset_symbol: str, quantity: int) -> str:
    """
    回補做空倉位

    Args:
        user_id: 用戶ID
        asset_symbol: 股票代碼
        quantity: 回補數量

    Returns:
        str: 成功消息

    Raises:
        InvalidQuantityError: If the quantity is invalid.
        MarketStockNotFoundError: If the stock is not found.
        InsufficientStockQuantityError: If the user has insufficient short quantity.
        Exception: For unexpected database errors.
    """
    if quantity <= 0:
        raise InvalidQuantityError(quantity=quantity)

    from gacha.utils.deadlock_retry import standard_retry

    asset_symbol_upper = asset_symbol.upper()

    async def _cover_transaction():
        pool = get_pool()
        assert pool is not None, "Database connection pool is not initialized"
        async with pool.acquire() as conn:
            async with conn.transaction():
                result = await _execute_cover_transaction_optimized(
                    conn, user_id, asset_symbol_upper, quantity
                )
                profit_loss_text = (
                    f"獲利 {result['profit_loss']:.0f}"
                    if result["profit_loss"] > 0
                    else f"虧損 {abs(result['profit_loss']):.0f}"
                )
                gross_settlement = result["settlement"] + result["fee"]
                return f"成功回補 {quantity} 股 {result['symbol']}，總共獲得 {result['settlement']:.0f} 油幣 ({profit_loss_text}，返還總額 {gross_settlement:.0f}，已扣除手續費 {result['fee']:.0f})。"

    return await standard_retry(_cover_transaction)
