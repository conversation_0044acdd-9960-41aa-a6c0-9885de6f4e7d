"""
被動技能生成腳本

使用重構後的模組化組件生成被動技能配置。
"""

import argparse
import os
import sys

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from rpg_system.scripts.generate import PassiveSkillGenerator
from utils.logger import initialize_logging, logger


def main():
    """腳本入口點"""
    try:
        # 初始化日誌
        initialize_logging()

        # 添加命令行參數處理
        parser = argparse.ArgumentParser(description="生成 RPG 被動技能配置文件。")
        parser.add_argument(
            "num_per_rarity_arg",
            type=int,
            nargs="?",
            default=None,
            help="指定每個稀有度生成的技能數量（會覆蓋環境變數 SKILLS_PER_RARITY）",
        )
        args = parser.parse_args()

        print("🛡️ RPG被動技能自動生成腳本 v1.0 - 模組化架構版")
        print("=" * 60)
        print("✨ 使用新的模組化架構")
        print("🔧 支援被動技能觸發條件和效果生成")
        print("📊 智能稀有度平衡系統")
        print("🎨 創意名稱生成引擎")
        print("=" * 60)

        skill_generator = PassiveSkillGenerator()
        logger.info("被動技能生成器初始化完成")

        # 決定 skills_per_rarity 的值
        if args.num_per_rarity_arg is not None:
            skills_per_rarity = args.num_per_rarity_arg
            print(f"ℹ️ 使用命令行參數指定的數量：{skills_per_rarity}")
        else:
            skills_per_rarity = int(os.environ.get("SKILLS_PER_RARITY", "5"))
            print(f"ℹ️ 使用環境變數或默認數量：{skills_per_rarity}")

        print(f"📊 開始生成技能，每個稀有度 {skills_per_rarity} 個技能...")
        all_skills = skill_generator.generate_all_skills(skills_per_rarity)

        skill_generator.save_skills_to_files(all_skills)

        return 0
    except Exception as e:
        logger.error("腳本執行出錯: %s", e, exc_info=True)
        print(f"❌ 錯誤: {e}")
        return 1


if __name__ == "__main__":
    main()
