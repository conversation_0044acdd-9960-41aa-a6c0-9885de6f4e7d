"""
交易 Cog - 單檔案實現，使用 Python 原生異常
整合了交易服務、視圖和 Embed 構建器的功能
"""

import logging
import re
import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, Optional, Tuple, Union, cast

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button

from config.app_config import get_config
from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    CardNotFoundError,
    CommandBlacklistError,
    InsufficientBalanceError,
    InvalidOperationError,
    NotParticipantInTradeError,
    TradeNotFoundError,
    TradeValidationError,
)
from gacha.models.trade_models import CardTradeHistoryModel, TradeStatus, TradeType
from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import user_collection_repository
from gacha.repositories.trading import card_trade_history_repository
from gacha.services import collection_service, user_service
from gacha.services.activity_service import (
    is_user_in_any_blacklist,
    is_user_in_trading_blacklist,
)
from utils.base_view import BaseView
from utils.response_embeds import SuccessEmbed

from .decorators import account_age_check

logger = logging.getLogger(__name__)


class TradingCog(commands.Cog):
    """交易 Cog - 使用 Python 原生異常的單檔案實現"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot

        # 交易配置
        self.trade_fee_percentage = Decimal(
            str(get_config("gacha_core_settings.trade_fee_percentage", 0.05))
        )

        # 待處理交易存儲
        self.pending_trades: Dict[str, Dict[str, Any]] = {}

        logger.info("TradingCog initialized successfully")

    @app_commands.command(
        name="trade", description="與另一位使用者發起卡片交易 (賣方需支付3%手續費)"
    )
    @account_age_check()
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        user="您想與之交易的使用者",
        offer_card_id="您提供的卡片 ID (master_card_id)",
        offer_quantity="您提供的卡片數量 (預設 1)",
        price="您向對方索要的油幣價格 (可選，不填則為贈送) - 注意：賣方需支付3%手續費",
    )
    async def trade(
        self,
        interaction: discord.Interaction,
        user: discord.User,
        offer_card_id: str,
        offer_quantity: Optional[int] = 1,
        price: Optional[float] = None,
    ):
        """處理 /trade 命令"""
        await interaction.response.defer()

        # 檢查發起者是否在交易黑名單中
        if await is_user_in_trading_blacklist(interaction.user.id):
            raise CommandBlacklistError("您已被限制使用 trade 指令。")

        # 檢查目標用戶是否在任一黑名單中
        if await is_user_in_any_blacklist(user.id):
            raise CommandBlacklistError("目標用戶已被限制交易功能，無法與其進行交易。")

        # 基本參數驗證 (拋出更具體的業務異常)
        if offer_quantity is not None and offer_quantity <= 0:
            raise TradeValidationError("提供的卡片數量必須大於 0")
        if price is not None and price <= 0:
            raise TradeValidationError("索要的油幣價格必須大於 0")
        if interaction.user.id == user.id:
            raise TradeValidationError("您不能與自己交易")

        # 檢查目標帳號年齡
        four_months_ago = datetime.now(timezone.utc) - timedelta(days=120)
        if user.created_at > four_months_ago:
            raise TradeValidationError(f"目標用戶 {user.mention} 不符合交易資格。")

        # 創建交易
        trade_details = await self._initiate_trade(
            initiator_id=interaction.user.id,
            receiver_id=user.id,
            offered_card_id=offer_card_id,
            offer_quantity=offer_quantity or 1,
            price=int(price) if price is not None else None,
        )

        # 構建交易請求 Embed 和視圖
        embed = await self._build_trade_request_embed(
            trade_details, interaction.user, user
        )
        view = TradeRequestView(
            bot=self.bot,
            cog=self,
            trade_details=trade_details,
            user_id=user.id,  # 傳入目標用戶的 ID
        )

        # 發送交易請求
        content_message = f"{interaction.user.mention} 向 {user.mention} 發起了交易請求！\n請目標用戶點擊下方按鈕進行操作。"
        await interaction.followup.send(content=content_message, embed=embed, view=view)

        logger.info(
            "Trade request %s sent by %s to %s",
            trade_details.get("trade_id"),
            interaction.user.id,
            user.id,
        )

    @trade.autocomplete("offer_card_id")
    async def offer_card_id_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> list[app_commands.Choice[str]]:
        """自動完成卡片 ID"""
        choices = []
        from gacha.models.filters import CollectionFilters

        # 根據輸入內容決定搜索方式
        # 預設過濾掉最愛的卡片
        base_extra_filters = {"exclude_favorites": True}

        if not current:
            # 空輸入時，獲取所有卡片
            filters = CollectionFilters(extra_filters=base_extra_filters)
            logger.debug(
                "TradingCog: offer_card_id_autocomplete - current is empty, fetching all non-favorite cards for user %s",
                interaction.user.id,
            )
        else:
            try:
                # 嘗試解析為卡片ID
                card_id_input = int(current)
                filters = CollectionFilters(extra_filters=base_extra_filters)
                try:
                    filters.set_card_id(card_id_input)  # 使用安全的設置方法
                    logger.debug(
                        "TradingCog: offer_card_id_autocomplete - current is '%s' (parsed as ID), fetching with card_id filter for user %s",
                        current,
                        interaction.user.id,
                    )
                except ValueError as card_id_error:
                    # card_id 超出範圍，當作名稱搜索
                    logger.debug(
                        "TradingCog: offer_card_id_autocomplete - current is '%s' (invalid card_id: %s), treating as name for user %s",
                        current,
                        card_id_error,
                        interaction.user.id,
                    )
                    filters = CollectionFilters(
                        card_name=current, extra_filters=base_extra_filters
                    )
            except ValueError:
                # 不是數字，當作名稱搜索
                filters = CollectionFilters(
                    card_name=current, extra_filters=base_extra_filters
                )
                logger.debug(
                    "TradingCog: offer_card_id_autocomplete - current is '%s' (parsed as name), fetching with card_name filter for user %s",
                    current,
                    interaction.user.id,
                )

        # 使用為自動完成優化的輕量級方法
        from gacha.repositories.collection import user_collection_repository

        user_cards_data = await user_collection_repository.get_cards_for_autocomplete(
            user_id=interaction.user.id, filters=filters
        )

        for card_data in user_cards_data:
            # 構建顯示名稱
            display_name = f"{card_data['name']} (ID: {card_data['card_id']})"
            if "quantity" in card_data and card_data["quantity"] is not None:
                display_name += f" (擁有: {card_data['quantity']})"

            # 限制顯示長度
            if len(display_name) > 100:
                display_name = display_name[:97] + "..."

            choices.append(
                app_commands.Choice(name=display_name, value=str(card_data["card_id"]))
            )

        logger.debug(
            "Offer_card_id autocomplete for '%s' for user %s resulted in %s choices.",
            current,
            interaction.user.id,
            len(choices),
        )
        return choices

    async def _initiate_trade(
        self,
        initiator_id: int,
        receiver_id: int,
        offered_card_id: Union[int, str],
        offer_quantity: int,
        price: Optional[int] = None,
    ) -> Dict[str, Any]:
        """創建交易請求"""

        # 解析卡片 ID
        try:
            # 優先嘗試直接轉換為整數
            card_id = int(offered_card_id)
        except (ValueError, TypeError):
            # 如果直接轉換失敗，則嘗試從自動完成的字串中提取 ID
            # 這解決了用戶在選擇後再次點擊輸入框，導致傳送顯示名稱而非 ID 值的問題
            match = re.search(r"\(ID: (\d+)\)", str(offered_card_id))
            if match:
                card_id = int(match.group(1))
            else:
                # 如果兩種方法都失敗，則拋出錯誤
                raise TradeValidationError(
                    f"無效的卡片 ID: {offered_card_id}"
                ) from None

        # 檢查交易價格是否有效（只需要大於 0）
        if price is not None and price <= 0:
            raise TradeValidationError("交易價格必須大於 0")

        # 驗證卡片存在
        offered_card = await master_card_repository.get_card(card_id)
        if not offered_card:
            raise CardNotFoundError(f"卡片 ID {card_id} 不存在")

        # 檢查發起者是否有足夠的卡片
        initiator_card_quantity = await collection_service.get_user_card_quantity(
            user_id=initiator_id, master_card_id=card_id
        )
        if initiator_card_quantity < offer_quantity:
            raise TradeValidationError(
                f"您的卡片「{offered_card.name}」數量不足：需要 {offer_quantity}，目前 {initiator_card_quantity}"
            )

        # 檢查卡片是否為最愛
        is_favorite = await user_collection_repository.get_card_favorite_status(
            user_id=initiator_id, card_id=card_id
        )
        if is_favorite:
            raise TradeValidationError(
                f"您不能交易已標示為最愛的卡片「{offered_card.name}」。\n請先到您的卡冊中取消最愛後再試。"
            )

        # 創建交易詳情
        trade_id = str(uuid.uuid4())
        trade_type_value = (
            TradeType.GIFT_CARD.value if price is None else TradeType.CARD_FOR_OIL.value
        )

        trade_details = {
            "trade_id": trade_id,
            "initiator_id": initiator_id,
            "receiver_id": receiver_id,
            "offered_card_id": card_id,
            "offered_card_name": offered_card.name,
            "offered_quantity": offer_quantity,
            "price": price,
            "trade_type": trade_type_value,
            "status": TradeStatus.PENDING.value,
        }

        # 存儲待處理交易
        self.pending_trades[trade_id] = trade_details
        logger.info(
            "Trade %s initiated between %s and %s", trade_id, initiator_id, receiver_id
        )

        return trade_details

    async def _build_trade_request_embed(
        self,
        trade_details: Dict,
        initiator: Union[discord.User, discord.Member],
        target_user: discord.User,
    ) -> discord.Embed:
        """構建交易請求 Embed"""
        offer_card_id = trade_details.get("offered_card_id")
        offer_card_name = trade_details.get(
            "offered_card_name", f"卡片ID: {offer_card_id}"
        )
        offer_quantity = trade_details.get("offered_quantity", 1)
        price = trade_details.get("price")

        # 獲取卡片資訊（包含卡池資訊）
        pool_info = ""
        offered_card = None
        if offer_card_id:
            offered_card = await master_card_repository.get_card(int(offer_card_id))
            if offered_card and offered_card.pool_type:
                from config.app_config import get_pool_type_names

                pool_type_names = get_pool_type_names()
                pool_name = pool_type_names.get(
                    offered_card.pool_type, offered_card.pool_type
                )
                pool_info = f" [{pool_name}]"

        description = f"{initiator.mention} 向您發起了一個交易請求！\n\n"
        description += "**他們提供：**\n"
        if pool_info:
            description += f"- **{offer_card_name}** {pool_info}\n"
            description += f"  └ ID: `{offer_card_id}` | 數量: {offer_quantity}\n\n"
        else:
            description += f"- **{offer_card_name}** (ID: `{offer_card_id}`) x {offer_quantity}\n\n"

        if price is not None:
            # 計算手續費和實際收到的金額
            fee_amount = int(Decimal(str(price)) * self.trade_fee_percentage)
            net_amount = price - fee_amount

            description += "**他們索要油幣：**\n"
            description += f"- {price} 油幣\n"
            description += (
                f"- ⚠️ **手續費提醒**：賣方將支付 {fee_amount} 油幣手續費 (3%)\n"
            )
            description += f"- 💰 賣方實際收到：{net_amount} 油幣\n"
        else:
            description += "**他們想要：**\n"
            description += "- (無特定要求，可能是贈送)\n"

        embed = discord.Embed(
            title="📬 新的交易請求",
            description=description,
            color=discord.Color.blue(),
            timestamp=discord.utils.utcnow(),
        )
        embed.set_author(
            name=f"來自 {initiator.display_name} 的交易",
            icon_url=initiator.display_avatar.url if initiator.display_avatar else None,
        )
        embed.set_footer(
            text="請在30分鐘內回應",
            icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1382681804896473138/biometric.png?ex=684c0a47&is=684ab8c7&hm=d3a573e284478774da4304b702237c52f8d47c53026411934d0281904770db33&",
        )

        # 添加交易縮圖
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1382680621385519144/card.png?ex=684c092d&is=684ab7ad&hm=090949a44f8d4e21c5d87881a9e43049545348eac4d7a58603749a41fb839d86&"
        )

        # 添加卡片圖片（使用已獲取的卡片資訊）
        if offered_card and offered_card.image_url:
            embed.set_image(url=offered_card.image_url)
            logger.debug(
                "Added card image to trade request embed: %s",
                offered_card.image_url,
            )

        return embed

    async def _accept_trade(
        self, trade_details: Dict[str, Any], accepting_user_id: int
    ) -> None:
        """接受交易的核心邏輯"""
        trade_id = trade_details.get("trade_id")
        initiator_id = trade_details.get("initiator_id")
        offered_card_id = trade_details.get("offered_card_id")
        offered_quantity = trade_details.get("offered_quantity")
        price = trade_details.get("price")
        trade_type_str = trade_details.get("trade_type")

        if accepting_user_id != trade_details.get("receiver_id"):
            raise TradeValidationError("您不是此交易的指定接收方，無法接受")

        try:
            trade_type = TradeType(trade_type_str)
        except ValueError as e:
            raise TradeValidationError(f"無效的交易類型: {trade_type_str}") from e

        pool = get_pool()
        if pool is None:
            raise RuntimeError("Database pool is not initialized")
        async with pool.acquire() as conn:
            async with conn.transaction():
                # 檢查發起者是否仍有足夠的卡片
                if (
                    initiator_id is None
                    or offered_card_id is None
                    or offered_quantity is None
                ):
                    raise TradeValidationError("交易資訊不完整")
                current_initiator_cards = (
                    await collection_service.get_user_card_quantity(
                        user_id=initiator_id,
                        master_card_id=offered_card_id,
                        connection=conn,
                    )
                )
                if current_initiator_cards < offered_quantity:
                    card_name = trade_details.get(
                        "offered_card_name", str(offered_card_id)
                    )
                    raise TradeValidationError(
                        f"發起者的卡片「{card_name}」數量不足：需要 {offered_quantity}，目前 {current_initiator_cards}"
                    )

                # 檢查接受者餘額（如果是油幣交易）
                if trade_type == TradeType.CARD_FOR_OIL:
                    if not isinstance(price, int) or price <= 0:
                        raise TradeValidationError("油幣交易價格資訊不正確")

                    receiver_balance = await user_service.get_user_balance(
                        user_id=accepting_user_id
                    )
                    if receiver_balance < price:
                        raise InsufficientBalanceError(
                            required=price, current=receiver_balance
                        )

                # 執行卡片轉移
                if (
                    initiator_id is None
                    or offered_card_id is None
                    or offered_quantity is None
                ):
                    raise TradeValidationError("交易資訊不完整")
                await collection_service.transfer_card_quantity(
                    sender_id=initiator_id,
                    receiver_id=accepting_user_id,
                    master_card_id=offered_card_id,
                    quantity=offered_quantity,
                    connection=conn,
                )
                logger.info(
                    "Transferred %s of card %s from %s to %s",
                    offered_quantity,
                    offered_card_id,
                    initiator_id,
                    accepting_user_id,
                )

                # 處理油幣轉移和手續費
                if trade_type == TradeType.CARD_FOR_OIL:
                    decimal_price = Decimal(str(price))
                    fee_paid = decimal_price * self.trade_fee_percentage
                    fee_paid = fee_paid.quantize(Decimal("1"))
                    net_amount_to_initiator = decimal_price - fee_paid
                    final_initiator_gets_oil = int(net_amount_to_initiator)

                    # 從接受者扣除油幣
                    if price is not None:
                        from gacha.services import economy_service

                        await economy_service.award_oil(
                            user_id=accepting_user_id,
                            amount=-price,
                            transaction_type="trade:buy",
                            reason=f"Trade {trade_id} with {initiator_id}",
                            connection=conn,
                        )

                    # 給發起者加油幣（扣除手續費後）
                    if initiator_id is not None:
                        from gacha.services import economy_service

                        await economy_service.award_oil(
                            user_id=initiator_id,
                            amount=final_initiator_gets_oil,
                            transaction_type="trade:sell",
                            reason=f"Trade {trade_id} with {accepting_user_id}",
                            connection=conn,
                        )

                    logger.info(
                        "Trade %s completed: %s oil from %s to %s (fee: %s)",
                        trade_id,
                        price,
                        accepting_user_id,
                        initiator_id,
                        fee_paid,
                    )
                else:
                    fee_paid = Decimal("0")

                # 記錄交易歷史
                if (
                    initiator_id is None
                    or offered_card_id is None
                    or offered_quantity is None
                ):
                    raise TradeValidationError("交易資訊不完整")
                history_entry = CardTradeHistoryModel(
                    initiator_user_id=initiator_id,
                    receiver_user_id=accepting_user_id,
                    offered_master_card_id=offered_card_id,
                    offered_quantity=offered_quantity,
                    requested_master_card_id=None,
                    requested_quantity=None,
                    price_amount=(
                        price if trade_type == TradeType.CARD_FOR_OIL else None
                    ),
                    fee_charged=(
                        int(fee_paid) if trade_type == TradeType.CARD_FOR_OIL else None
                    ),
                    trade_type=trade_type,
                    completed_at=datetime.now(),
                )
                await card_trade_history_repository.add_trade_history(
                    history_entry, connection=conn
                )

        # 移除待處理交易
        if trade_id:
            self._remove_pending_trade(trade_id)
        else:
            logger.warning("Attempted to remove a pending trade with no trade_id.")
        logger.info(
            "Trade %s completed successfully between %s and %s",
            trade_id,
            initiator_id,
            accepting_user_id,
        )

    def _get_embed_title_and_color(self, status: str) -> Tuple[str, discord.Color]:
        """根據交易狀態獲取標題和顏色"""
        title = f"🔁 交易更新 - {status}"
        color = discord.Color.default()

        if status in ["已接受", "已成功", "完成"]:
            color = discord.Color.green()
            title = f"✅ 交易 {status}!"
        elif status in ["已拒絕", "已取消"]:
            color = discord.Color.red()
            title = f"🚫 交易 {status}"
        elif status == "已過期":
            color = discord.Color.orange()
            title = f"⏳ 交易 {status}"
        elif status == "失敗":
            color = discord.Color.dark_red()
            title = f"❌ 交易 {status}"

        return title, color

    async def _build_trade_description(
        self,
        trade_id: str,
        initiator: Optional[discord.User],
        initiator_id: int,
        target_user: Optional[discord.User],
        target_user_id: int,
        offer_card_name: str,
        offer_card_id: int,
        offer_quantity: int,
        price: Optional[int],
        status: str,
        reason: Optional[str] = None,
        acting_user: Optional[discord.User] = None,
    ) -> Tuple[str, Optional[Any]]:
        """構建交易描述文本，返回描述和卡片資訊"""
        # 獲取卡池資訊
        pool_info = ""
        offered_card = await master_card_repository.get_card(offer_card_id)
        if offered_card and offered_card.pool_type:
            from config.app_config import get_pool_type_names

            pool_type_names = get_pool_type_names()
            pool_name = pool_type_names.get(
                offered_card.pool_type, offered_card.pool_type
            )
            pool_info = f" [{pool_name}]"

        description = f"交易 ID: `{trade_id}`\n"
        description += f"發起者: {(initiator.mention if initiator else f'用戶ID {initiator_id}')}\n"
        description += f"目標用戶: {(target_user.mention if target_user else f'用戶ID {target_user_id}')}\n\n"
        description += "**發起方提供：**\n"
        if pool_info:
            description += f"- **{offer_card_name}** {pool_info}\n"
            description += f"  └ ID: `{offer_card_id}` | 數量: {offer_quantity}\n"
        else:
            description += (
                f"- **{offer_card_name}** (ID: `{offer_card_id}`) x {offer_quantity}\n"
            )

        if price is not None:
            description += "**發起方索要油幣：**\n"
            description += f"- {price} 油幣\n"

            # 如果交易已完成，顯示手續費詳情
            if status in ["已接受", "已成功", "完成"]:
                fee_amount = int(Decimal(str(price)) * self.trade_fee_percentage)
                net_amount = price - fee_amount
                description += f"- 💸 手續費已扣除：{fee_amount} 油幣 (3%)\n"
                description += f"- 💰 賣方實際收到：{net_amount} 油幣\n"

        if reason:
            description += f"\n原因：{reason}"
        if acting_user:
            description += f"\n操作者：{acting_user.mention}"

        return description, offered_card

    async def _build_trade_status_embed(
        self,
        trade_details: Dict,
        status: str,
        reason: Optional[str] = None,
        acting_user: Optional[discord.User] = None,
    ) -> discord.Embed:
        """構建交易狀態 Embed"""
        trade_id = trade_details.get("trade_id", "N/A")
        initiator_id = trade_details.get("initiator_id")
        target_user_id = trade_details.get("receiver_id")
        offer_card_id = trade_details.get("offered_card_id")
        offer_card_name = trade_details.get(
            "offered_card_name", f"卡片ID: {offer_card_id}"
        )
        offer_quantity = trade_details.get("offered_quantity", 1)
        price = trade_details.get("price")

        # 獲取用戶對象
        initiator = None
        if initiator_id:
            initiator = self.bot.get_user(initiator_id) or await self.bot.fetch_user(
                initiator_id
            )

        target_user = None
        if target_user_id:
            target_user = self.bot.get_user(
                target_user_id
            ) or await self.bot.fetch_user(target_user_id)

        # 獲取標題和顏色
        title, color = self._get_embed_title_and_color(status)

        # 構建描述
        if not all(
            [
                trade_id,
                initiator_id,
                target_user_id,
                offer_card_name,
                offer_card_id,
                offer_quantity,
            ]
        ):
            raise TradeValidationError("交易資訊不完整")

        # 確保傳遞給 _build_trade_description 的 ID 不是 None
        if initiator_id is None or target_user_id is None or offer_card_id is None:
            raise TradeValidationError("交易資訊不完整，缺少必要的 ID")

        description, offered_card = await self._build_trade_description(
            trade_id=trade_id,
            initiator=initiator,
            initiator_id=initiator_id,
            target_user=target_user,
            target_user_id=target_user_id,
            offer_card_name=offer_card_name,
            offer_card_id=offer_card_id,
            offer_quantity=offer_quantity,
            price=price,
            status=status,
            reason=reason,
            acting_user=acting_user,
        )

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=discord.utils.utcnow(),
        )

        if acting_user and acting_user.display_avatar:
            embed.set_author(
                name=f"由 {acting_user.display_name} 操作",
                icon_url=acting_user.display_avatar.url,
            )

        embed.set_footer(
            text="交易系統",
            icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1382681804896473138/biometric.png?ex=684c0a47&is=684ab8c7&hm=d3a573e284478774da4304b702237c52f8d47c53026411934d0281904770db33&",
        )

        # 添加交易縮圖
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1382680621385519144/card.png?ex=684c092d&is=684ab7ad&hm=090949a44f8d4e21c5d87881a9e43049545348eac4d7a58603749a41fb839d86&"
        )

        # 添加卡片圖片（使用已獲取的卡片資訊）
        if offered_card and offered_card.image_url:
            embed.set_image(url=offered_card.image_url)
            logger.debug(
                "Added card image to trade status embed: %s",
                offered_card.image_url,
            )

        return embed

    def _remove_pending_trade(self, trade_id: str) -> None:
        """移除待處理交易"""
        if trade_id in self.pending_trades:
            del self.pending_trades[trade_id]
            logger.debug("Removed pending trade %s", trade_id)

    async def get_trade_details(self, trade_id: str) -> Dict[str, Any]:
        """獲取交易詳情"""
        trade = self.pending_trades.get(trade_id)
        if not trade:
            raise TradeNotFoundError(f"交易 {trade_id} 不存在或已過期")
        return trade

    async def _cancel_trade(
        self,
        trade_details: Dict[str, Any],
        canceling_user_id: int,
        reason: str = "交易被取消",
    ) -> str:
        """取消交易"""
        trade_id = trade_details.get("trade_id")
        if not trade_id:
            raise TradeValidationError("交易資訊不完整 (缺少 trade_id)")

        initiator_id = trade_details.get("initiator_id")
        receiver_id = trade_details.get("receiver_id")

        if initiator_id is None or receiver_id is None:
            raise TradeValidationError("交易資訊不完整")

        if canceling_user_id != initiator_id and canceling_user_id != receiver_id:
            raise TradeValidationError("您不是此交易的參與者，無法取消")

        logger.info(
            "Trade %s between initiator %s and receiver %s was cancelled by user %s. Reason: %s",
            trade_id,
            initiator_id,
            receiver_id,
            canceling_user_id,
            reason,
        )

        self._remove_pending_trade(trade_id)
        return reason


class TradeRequestView(BaseView):
    """交易請求視圖 - 使用裝飾器風格"""

    def __init__(
        self, bot: commands.Bot, cog: TradingCog, trade_details: Dict, user_id: int
    ):
        # __init__ 現在只負責儲存狀態
        super().__init__(bot=bot, user_id=user_id, timeout=1800)
        self.cog = cog
        self.trade_details = trade_details
        self.trade_id = trade_details.get("trade_id")
        self.target_user_id = trade_details.get("receiver_id")
        self.initiator_id = trade_details.get("initiator_id")

    # 覆寫 interaction_check 以允許發起者也能操作
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id not in (self.target_user_id, self.initiator_id):
            raise NotParticipantInTradeError("您不是此交易的參與者！😠")
        return True

    @discord.ui.button(
        label="接受交易", style=discord.ButtonStyle.green, custom_id="trade_accept"
    )
    async def accept_button(self, interaction: discord.Interaction, button: Button):
        """處理接受交易按鈕的點擊"""
        # 讓 BaseView.on_error 處理所有錯誤
        await interaction.response.defer(ephemeral=True)

        # 檢查接受者是否在交易黑名單中

        if await is_user_in_trading_blacklist(interaction.user.id):
            raise CommandBlacklistError("您已被限制使用交易功能。")

        if interaction.user.id != self.target_user_id:
            raise TradeValidationError("只有目標用戶才能接受交易。")

        if not self.trade_id:
            raise TradeValidationError("交易 ID 不存在")
        current_trade_details = await self.cog.get_trade_details(self.trade_id)
        await self.cog._accept_trade(current_trade_details, interaction.user.id)

        # --- 成功流程 ---
        card_id = current_trade_details.get("offered_card_id")
        if card_id is None:
            raise TradeValidationError("卡片 ID 不存在")
        initial_is_favorite = await user_collection_repository.get_card_favorite_status(
            user_id=interaction.user.id,
            card_id=card_id,
        )
        embed = await self.cog._build_trade_status_embed(
            current_trade_details,
            "已接受",
            acting_user=cast(discord.User, interaction.user),
        )
        if initial_is_favorite is None:
            raise TradeValidationError("無法獲取卡片的最愛狀態")
        complete_view = TradeCompleteView(
            bot=self.cog.bot,
            cog=self.cog,
            trade_details=current_trade_details,
            initial_is_favorite=initial_is_favorite,
            user_id=interaction.user.id,
        )
        await interaction.edit_original_response(embed=embed, view=complete_view)
        success_embed = SuccessEmbed(description="交易已接受！ 🎉")
        await interaction.followup.send(embed=success_embed, ephemeral=True)

    @discord.ui.button(
        label="拒絕交易", style=discord.ButtonStyle.red, custom_id="trade_reject"
    )
    async def reject_button(self, interaction: discord.Interaction, button: Button):
        """處理拒絕/取消交易按鈕的點擊"""
        # 讓 BaseView.on_error 處理所有錯誤
        await interaction.response.defer(ephemeral=True)

        if not self.trade_id:
            raise TradeValidationError("交易 ID 不存在")
        current_trade_details = await self.cog.get_trade_details(self.trade_id)

        is_initiator = interaction.user.id == self.initiator_id
        reason = "發起者取消了交易" if is_initiator else "用戶拒絕交易"
        status = "已取消" if is_initiator else "已拒絕"

        await self.cog._cancel_trade(
            current_trade_details, interaction.user.id, reason=reason
        )

        embed = await self.cog._build_trade_status_embed(
            current_trade_details,
            status,
            acting_user=cast(discord.User, interaction.user),
        )
        await interaction.edit_original_response(embed=embed, view=None)

        response_message = "交易已取消。" if is_initiator else "交易已拒絕。 🚫"
        embed = SuccessEmbed(description=response_message)
        await interaction.followup.send(embed=embed, ephemeral=True)


class FavoriteButton(discord.ui.Button):
    """一個動態的收藏按鈕，封裝了自己的狀態和回呼邏輯。"""

    def __init__(
        self, cog: "TradingCog", trade_details: Dict[str, Any], is_favorite: bool
    ):
        self.cog = cog
        self.trade_details = trade_details
        self.receiver_id = trade_details.get("receiver_id")
        self.card_id = trade_details.get("offered_card_id")

        # 根據初始狀態設置按鈕外觀
        label = "已收藏" if is_favorite else "加入最愛"
        style = (
            discord.ButtonStyle.success
            if is_favorite
            else discord.ButtonStyle.secondary
        )
        emoji = (
            "<a:pu:1365482490478989353>"
            if is_favorite
            else "<a:sw:1365447243863429273>"
        )

        super().__init__(
            label=label,
            style=style,
            emoji=emoji,
            custom_id=f"trade_fav_{self.trade_details.get('trade_id')}",
        )

    async def callback(self, interaction: discord.Interaction):
        """處理收藏按鈕的點擊事件。"""
        # 雖然 View 級別有檢查，但按鈕級別的檢查更佳
        if interaction.user.id != self.receiver_id:
            raise InvalidOperationError("只有卡片接收者才能將其加入最愛。")

        await interaction.response.defer(ephemeral=True)

        # 讓 BaseView.on_error 處理所有錯誤
        from gacha.services import favorite_service

        # 切換收藏狀態
        if self.receiver_id is None or self.card_id is None:
            raise TradeValidationError("交易資訊不完整")
        new_is_favorite = await favorite_service.toggle_favorite_card(
            self.receiver_id, self.card_id, operator_id=self.receiver_id
        )

        # 更新按鈕自身的外觀
        self.label = "已收藏" if new_is_favorite else "加入最愛"
        self.style = (
            discord.ButtonStyle.success
            if new_is_favorite
            else discord.ButtonStyle.secondary
        )
        self.emoji = (
            "<a:pu:1365482490478989353>"
            if new_is_favorite
            else "<a:sw:1365447243863429273>"
        )

        # 更新原始訊息，self.view 會包含更新後的按鈕
        await interaction.edit_original_response(view=self.view)

        action_text = "成功加入收藏！" if new_is_favorite else "已從收藏中移除。"
        embed = SuccessEmbed(description=action_text)
        await interaction.followup.send(embed=embed, ephemeral=True)


class TradeCompleteView(BaseView):
    """
    交易成功後顯示的視圖，使用自訂的 Button 子類別來處理動態按鈕。
    """

    def __init__(
        self,
        bot: commands.Bot,
        cog: "TradingCog",
        trade_details: Dict[str, Any],
        initial_is_favorite: bool,
        user_id: int,
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=1800)
        self.receiver_id = trade_details.get("receiver_id")

        # 添加自訂的、狀態感知的按鈕
        self.add_item(FavoriteButton(cog, trade_details, initial_is_favorite))

    # interaction_check 已由 BaseView 提供，無需再寫


async def setup(bot: commands.Bot):
    await bot.add_cog(TradingCog(bot))
    logger.info("TradingCog has been loaded.")
