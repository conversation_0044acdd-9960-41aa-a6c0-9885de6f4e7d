"""
Gacha系統基礎分頁視圖
提供用於卡片和系列列表的通用分頁功能
"""

from typing import Optional

import discord
from discord.ui import Button, TextInput

from gacha.exceptions import BusinessError
from utils.base_modal import BaseModal
from utils.base_view import BaseView, BotType
from utils.logger import logger


class BasePaginationJumpModal(BaseModal):
    """基礎分頁跳轉模態框"""

    page_number = TextInput(
        label="頁碼",
        placeholder="輸入要跳轉的頁碼",
        required=True,
        min_length=1,
        max_length=5,
    )

    def __init__(
        self, bot: BotType, view: "BasePaginationView", title="跳轉到指定頁碼"
    ):
        super().__init__(bot=bot, title=title)
        self.pagination_view = view

    async def on_submit(self, interaction: discord.Interaction):
        """提交處理"""
        from gacha.exceptions import BusinessError

        # 驗證輸入是否為有效數字
        page_input = self.page_number.value.strip()
        if not page_input.isdigit():
            raise BusinessError("請輸入有效的頁碼數字！")

        page = int(page_input)
        # 確保頁碼在有效範圍內
        page = max(1, min(page, self.pagination_view.total_pages))
        self.pagination_view.current_page = page
        self.pagination_view._refresh_button_states()
        await self.pagination_view._update_page(page, interaction)


class BasePaginationView(BaseView):
    """基礎分頁視圖，提供通用的分頁功能"""

    def __init__(
        self,
        *,
        bot: BotType,
        user_id: int,
        current_page: int,
        total_pages: int,
        timeout: Optional[float] = 120,
    ):
        """初始化基礎分頁視圖

        參數:
            bot: Discord Bot 實例
            user_id: 用戶 ID
            current_page: 當前頁碼
            total_pages: 總頁數
            timeout: 超時時間(秒)
        """
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.current_page = current_page
        self.total_pages = max(1, total_pages)
        self.message: Optional[discord.Message] = None
        # 移除複雜的 InteractionManager
        self.btn_first: Optional[Button] = None
        self.btn_prev: Optional[Button] = None
        self.btn_jump: Optional[Button] = None
        self.btn_next: Optional[Button] = None
        self.btn_last: Optional[Button] = None
        self.add_pagination_buttons()
        self._refresh_button_states()

    def add_pagination_buttons(self, row: int = 0):
        """創建並添加分頁按鈕到視圖中。"""
        self.btn_first = Button(
            emoji="⏮️", style=discord.ButtonStyle.gray, custom_id="first_page", row=row
        )
        self.btn_first.callback = self.first_page_callback
        self.add_item(self.btn_first)
        self.btn_prev = Button(
            emoji="◀️", style=discord.ButtonStyle.gray, custom_id="prev_page", row=row
        )
        self.btn_prev.callback = self.prev_page_callback
        self.add_item(self.btn_prev)
        self.btn_jump = Button(
            label=f"{self.current_page}/{self.total_pages}",
            style=discord.ButtonStyle.gray,
            custom_id="jump",
            row=row,
        )
        self.btn_jump.callback = self.jump_callback
        self.add_item(self.btn_jump)
        self.btn_next = Button(
            emoji="▶️", style=discord.ButtonStyle.gray, custom_id="next_page", row=row
        )
        self.btn_next.callback = self.next_page_callback
        self.add_item(self.btn_next)
        self.btn_last = Button(
            emoji="⏭️", style=discord.ButtonStyle.gray, custom_id="last_page", row=row
        )
        self.btn_last.callback = self.last_page_callback
        self.add_item(self.btn_last)

    def _refresh_button_states(self):
        """根據當前頁面和總頁數更新分頁按鈕的標籤和禁用狀態。"""
        if not all(
            [self.btn_first, self.btn_prev, self.btn_jump, self.btn_next, self.btn_last]
        ):
            logger.warning(
                "Attempted to refresh button states before buttons were fully initialized."
            )
            return
        is_first_page = self.current_page == 1
        is_last_page = self.current_page == self.total_pages
        is_single_page = self.total_pages <= 1
        if self.btn_first:
            self.btn_first.disabled = is_first_page or is_single_page
        if self.btn_prev:
            self.btn_prev.disabled = is_first_page or is_single_page
        if self.btn_jump:
            self.btn_jump.label = f"{self.current_page}/{self.total_pages}"
            self.btn_jump.disabled = is_single_page
        if self.btn_next:
            self.btn_next.disabled = is_last_page or is_single_page
        if self.btn_last:
            self.btn_last.disabled = is_last_page or is_single_page

    async def _handle_page_change(
        self, new_page: int, interaction: discord.Interaction
    ):
        """通用處理頁面變更的邏輯 - 移除 defer 優化性能"""
        # 統一移除 defer，直接執行切頁操作
        # if not await self.interaction_manager.try_defer(interaction):
        #     logger.warning('BasePaginationView: Failed to defer interaction %s for page change to %s. Aborting page update.', interaction.id, new_page)
        #     return
        self.current_page = new_page
        self._refresh_button_states()
        await self._update_page(self.current_page, interaction)

    async def first_page_callback(self, interaction: discord.Interaction):
        if self.current_page != 1:
            await self._handle_page_change(1, interaction)
        else:
            # 移除不必要的 defer - 已經在第一頁
            pass

    async def prev_page_callback(self, interaction: discord.Interaction):
        if self.current_page > 1:
            await self._handle_page_change(max(1, self.current_page - 1), interaction)
        else:
            # 移除不必要的 defer - 已經在第一頁
            pass

    async def jump_callback(self, interaction: discord.Interaction):
        if self.total_pages <= 1:
            raise BusinessError("目前只有一頁，無需跳轉。")
        modal = self._create_jump_modal()
        await interaction.response.send_modal(modal)

    async def next_page_callback(self, interaction: discord.Interaction):
        if self.current_page < self.total_pages:
            await self._handle_page_change(
                min(self.total_pages, self.current_page + 1), interaction
            )
        else:
            # 移除不必要的 defer - 已經在最後一頁
            pass

    async def last_page_callback(self, interaction: discord.Interaction):
        if self.current_page != self.total_pages:
            await self._handle_page_change(self.total_pages, interaction)
        else:
            # 移除不必要的 defer - 已經在最後一頁
            pass

    def _create_jump_modal(self) -> BasePaginationJumpModal:
        return BasePaginationJumpModal(self.bot, self)

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed，子類必須實現此方法"""
        raise NotImplementedError("子類必須實現get_current_page_embed方法")

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面，子類必須實現此方法"""
        raise NotImplementedError("子類必須實現_update_page方法")
