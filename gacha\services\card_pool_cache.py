from __future__ import annotations

import random
from collections import defaultdict
from typing import Dict, List

from gacha.constants import <PERSON><PERSON>Level
from gacha.repositories._base_repo import fetch_all
from utils.logger import logger

# --- 模組級別的全局主快取 ---

# 索引化快取結構：Dict[str, Dict[RarityLevel, List[int]]]
_indexed_master_cache: Dict[str, Dict[RarityLevel, List[int]]] = {}


async def get_indexed_master_cache() -> Dict[str, Dict[RarityLevel, List[int]]]:
    """獲取預先按 pool_type 和 rarity 索引好的卡片ID緩存。"""
    if not _indexed_master_cache:
        await _populate_and_index_master_cache()
    return _indexed_master_cache


async def _populate_and_index_master_cache():
    """從資料庫獲取所有卡片，並直接構建成一個嵌套的、索引好的字典。"""
    global _indexed_master_cache
    logger.info("[CACHE] 開始構建索引化卡片主資料快取...")

    query = "SELECT card_id, pool_type, rarity FROM gacha_master_cards"
    try:
        all_cards_from_db = await fetch_all(query)
        all_cards_dicts = [dict(record) for record in all_cards_from_db]

        temp_cache = defaultdict(lambda: defaultdict(list))

        for card_record in all_cards_dicts:
            try:
                pool_type = card_record.get("pool_type")
                card_id = card_record.get("card_id")
                rarity_val = card_record.get("rarity")

                if not all((pool_type, card_id, rarity_val is not None)):
                    continue

                rarity_enum = RarityLevel(rarity_val)
                temp_cache[pool_type][rarity_enum].append(card_id)

            except (KeyError, ValueError, TypeError) as e:
                logger.warning(
                    f"[CACHE] 處理卡片記錄時跳過一筆無效數據: {card_record}, 錯誤: {e}"
                )
                continue

        # 對每個池子裡的每個稀有度的卡片列表進行隨機化
        for pool in temp_cache.values():
            for rarity_list in pool.values():
                random.shuffle(rarity_list)

        _indexed_master_cache = dict(temp_cache)
        total_cards = len(all_cards_from_db)
        logger.info(
            f"✅ [CACHE] 索引化卡片主資料快取構建完成，共處理 {total_cards} 張卡片。"
        )

    except Exception as e:
        logger.error("❌ [CACHE] 構建索引化卡片主資料快取失敗: %s", e, exc_info=True)
        _indexed_master_cache = {}


def clear_card_pool_cache() -> None:
    """
    手動清除索引化卡片快取。
    """
    global _indexed_master_cache
    _indexed_master_cache = {}
    logger.info("[CACHE] 索引化卡片快取已被手動清除。下次請求時將會從資料庫重新載入。")
