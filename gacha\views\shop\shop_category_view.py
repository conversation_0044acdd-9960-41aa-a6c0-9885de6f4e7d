from typing import Union

import discord
from discord.ext.commands import <PERSON><PERSON>
from discord.ui import Select

from gacha.exceptions import BusinessError
from gacha.services import base_ticket_service, user_service

# 導入新的出售資源視圖
from gacha.views.pioneer_views.sell_resources_view import SellResourcesView
from gacha.views.shop.shop_item_list_view import ShopItemListView
from utils.base_view import BaseView


class ShopCategoryView(BaseView):
    def __init__(
        self, bot: Bot, user: Union[discord.User, discord.Member], timeout=180
    ):
        super().__init__(bot=bot, user_id=user.id, timeout=timeout)
        self.bot = bot
        self.user = user

    @discord.ui.select(
        placeholder="選擇一項商業服務...",
        options=[
            discord.SelectOption(
                label="油票兌換",
                value="ticket_exchange",
                description="使用油票購買卡片兌換券。",
                emoji="🎫",
            ),
            discord.SelectOption(
                label="出售資源",
                value="sell_resources",
                description="將倉庫物品賣給系統換取油幣。",
                emoji="📦",
            ),
            # 未來擴展
            discord.SelectOption(
                label="裝備商店 (未來開放)",
                value="equipment_shop",
                description="購買強力裝備。",
                emoji="⚔️",
            ),
        ],
    )
    async def category_select(self, interaction: discord.Interaction, select: Select):
        """
        處理商業服務選擇。
        """
        selected_value = select.values[0]

        if selected_value == "ticket_exchange":
            await interaction.response.defer(ephemeral=True)
            # 根據規範，移除 try-except，讓錯誤自然冒泡
            all_items = await base_ticket_service.get_ticket_shop_definitions()
            balance = await user_service.get_oil_ticket_balance(
                self.user.id, create_if_missing=True
            )

            view = ShopItemListView(
                original_interaction=interaction,
                items=all_items,
                oil_ticket_balance=balance,
                category_name="油票兌換券商店",
            )
            embed = await view.get_current_page_embed()
            await interaction.edit_original_response(
                content=None, embed=embed, view=view
            )
            view.message = await interaction.original_response()

        elif selected_value == "sell_resources":
            await interaction.response.defer(ephemeral=True)
            # 根據規範，移除 try-except，讓錯誤自然冒泡
            sell_view = SellResourcesView(bot=self.bot, user_id=self.user.id)
            await sell_view.update_components()
            embed = await sell_view.create_embed()
            await interaction.edit_original_response(
                content=None, embed=embed, view=sell_view
            )

        elif selected_value == "equipment_shop":
            raise BusinessError("此功能尚未開放，敬請期待！")
