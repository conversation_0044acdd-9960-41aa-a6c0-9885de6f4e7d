"""
Gacha系統卡片排序模態框
提供自定義卡片排序的輸入界面
"""

from typing import TYPE_CHECKING, Any, Callable, Coroutine, Dict, Optional

import discord

from utils.base_modal import BaseModal

if TYPE_CHECKING:
    from utils.error_handler import BotType


class SortPositionModal(BaseModal):
    """移動卡片至指定位置的模態框"""

    def __init__(
        self,
        bot: "BotType",
        user_id: int,
        card_id: int,
        callback_func: Callable[[int, int, str], Coroutine[Any, Any, Dict[str, Any]]],
        update_view_func: Callable[
            [Dict[str, Any], discord.Interaction, Optional[Exception]], Any
        ],
        max_position: int = 9999,
    ):
        """初始化位置輸入模態框

        參數:
            bot: 機器人實例
            user_id: 用戶ID
            card_id: 卡片ID
            callback_func: 回調函數，接收用戶ID、卡片ID和目標位置(str)，返回操作結果
            update_view_func: 更新視圖的函數，接收結果、交互對象和可能的異常
            max_position: 最大允許的位置值
        """
        super().__init__(bot=bot, title="移動卡片至指定位置")
        self.user_id = user_id
        self.card_id = card_id
        self.callback_func = callback_func
        self.update_view_func = update_view_func
        self.max_position = max_position
        self.position_input = discord.ui.TextInput(
            label="請輸入目標位置",
            placeholder=f"輸入 1 到 {max_position} 之間的數字",
            required=True,
            min_length=1,
            max_length=5,
            style=discord.TextStyle.short,
        )
        self.add_item(self.position_input)

    async def on_submit(self, interaction: discord.Interaction):
        """提交表單時的回調"""
        # 根據規範，UI層不應有 try-except，讓錯誤自然冒泡
        await interaction.response.defer()

        # 直接調用回調函數，傳遞原始字符串，讓服務層進行驗證
        result = await self.callback_func(
            self.user_id, self.card_id, self.position_input.value
        )
        # 成功時調用視圖更新
        await self.update_view_func(result, interaction, None)

    # 根據規範，移除 on_error。錯誤將由 BaseView.on_error 或全局處理器捕獲
