"""
錯誤處理模組

提供通用的錯誤處理裝飾器，用於技能生成過程中的異常處理。
"""

import functools
import os
import sys

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

from gacha.exceptions import BusinessError
from utils.logger import logger


def error_handler(default_return=None, log_message="函數執行出錯"):
    """錯誤處理裝飾器

    Args:
        default_return: 錯誤時的默認返回值，可以是值或可調用對象
        log_message: 錯誤時的日誌消息前綴

    Returns:
        裝飾器函數

    Example:
        @error_handler(default_return=[], log_message="獲取技能列表時出錯")
        def get_skills():
            # 可能出錯的代碼
            pass
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BusinessError:
                # 如果是業務邏輯錯誤，直接重新拋出，讓上層處理
                raise
            except Exception as e:
                # 獲取函數名
                func_name = func.__name__
                # 記錄錯誤
                logger.error("%s: %s - %s", log_message, func_name, e, exc_info=True)
                # 返回默認值
                if callable(default_return):
                    return default_return()
                return default_return

        return wrapper

    return decorator
