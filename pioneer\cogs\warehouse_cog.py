import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.views.pioneer_views import PioneerWarehouseView


class WarehouseCog(commands.Cog):
    """開拓者倉庫指令"""

    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="warehouse", description="查看開拓者倉庫")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def warehouse(self, interaction: discord.Interaction):
        """查看開拓者倉庫"""
        await interaction.response.defer(thinking=False)
        user_id = interaction.user.id

        # 確保用戶資料存在
        await repositories.create_pioneer_profile(user_id)

        # 獲取倉庫物品
        warehouse_items = await repositories.get_user_warehouse(user_id)

        # 創建倉庫視圖
        from pioneer.core.game_data_loader import get_game_data

        game_data = get_game_data()

        view = PioneerWarehouseView(
            bot=self.bot,
            user_id=user_id,
            warehouse_items=warehouse_items,
            game_data=game_data,
        )
        embed = await view.create_warehouse_embed()

        await interaction.followup.send(embed=embed, view=view)


async def setup(bot):
    """設置 Cog"""
    await bot.add_cog(WarehouseCog(bot))
