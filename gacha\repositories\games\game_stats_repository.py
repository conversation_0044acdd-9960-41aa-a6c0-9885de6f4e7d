"""
遊戲統計存儲庫
處理遊戲統計數據的數據庫操作
"""

import logging
from typing import Any, Dict, List, Optional

import asyncpg

from database.postgresql.async_manager import get_pool
from gacha.core.game_registry import GameRegistry
from gacha.exceptions import DatabaseOperationError
from gacha.repositories._base_repo import fetch_all, fetch_one

logger = logging.getLogger(__name__)

GAME_HISTORY_TABLE = "game_history"
USER_GAME_STATS_TABLE = "user_game_stats"


async def record_game_result(
    user_id: int,
    game_type: str,
    game_data: Dict[str, Any],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """
    記錄遊戲結果並更新統計數據

    Args:
        user_id: 用戶ID
        game_type: 遊戲類型 ('blackjack', 'dice', 'mines', 'tower', 'slot', 'poker1v1', 'spin_wheel')
        game_data: 遊戲數據字典，包含 bet, payout, profit, result 等
        connection: 可選的數據庫連接
    """
    try:
        if connection:
            await _record_with_connection(connection, user_id, game_type, game_data)
        else:
            pool = get_pool()
            if pool is None:
                raise DatabaseOperationError("資料庫連線池未初始化")
            async with pool.acquire() as conn:
                async with conn.transaction():
                    await _record_with_connection(conn, user_id, game_type, game_data)

    except Exception as e:
        logger.error(
            f"記錄遊戲結果失敗: user_id={user_id}, game_type={game_type}, error={e}"
        )
        raise DatabaseOperationError(f"記錄遊戲結果失敗: {e}") from e


async def _record_with_connection(
    conn: asyncpg.Connection, user_id: int, game_type: str, game_data: Dict[str, Any]
) -> None:
    """使用指定連接記錄遊戲結果"""

    # 1. 記錄詳細遊戲歷史
    await conn.execute(
        f"""
        INSERT INTO {GAME_HISTORY_TABLE} (
            user_id, game_type, bet_amount, payout_amount,
            profit_loss, game_result, game_data
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    """,
        user_id,
        game_type,
        game_data["bet"],
        game_data["payout"],
        game_data["profit"],
        game_data["result"],
        game_data,
    )

    # 2. 更新統計表
    await _update_user_stats(conn, user_id, game_type, game_data)


async def _update_user_stats(
    conn: asyncpg.Connection, user_id: int, game_type: str, game_data: Dict[str, Any]
) -> None:
    """更新用戶遊戲統計"""

    # 計算基本統計更新
    is_win = game_data["profit"] > 0
    is_loss = game_data["profit"] < 0
    is_push = game_data["profit"] == 0

    # 獲取當前統計
    current_stats = await conn.fetchrow(
        f"""
        SELECT game_specific_stats FROM {USER_GAME_STATS_TABLE}
        WHERE user_id = $1 AND game_type = $2
    """,
        user_id,
        game_type,
    )

    # 更新遊戲特定統計
    specific_stats = current_stats["game_specific_stats"] if current_stats else {}
    if not isinstance(specific_stats, dict):
        specific_stats = {}

    updated_specific = _update_specific_stats(game_type, specific_stats, game_data)

    # 更新或插入統計記錄
    await conn.execute(
        f"""
        INSERT INTO {USER_GAME_STATS_TABLE} (
            user_id, game_type, total_games, total_wins, total_losses, total_pushes,
            total_bet_amount, total_payout_amount, total_profit_loss,
            max_win, max_loss, game_specific_stats, last_played_at
        ) VALUES ($1, $2, 1, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, game_type) DO UPDATE SET
            total_games = {USER_GAME_STATS_TABLE}.total_games + 1,
            total_wins = {USER_GAME_STATS_TABLE}.total_wins + $3,
            total_losses = {USER_GAME_STATS_TABLE}.total_losses + $4,
            total_pushes = {USER_GAME_STATS_TABLE}.total_pushes + $5,
            total_bet_amount = {USER_GAME_STATS_TABLE}.total_bet_amount + $6,
            total_payout_amount = {USER_GAME_STATS_TABLE}.total_payout_amount + $7,
            total_profit_loss = {USER_GAME_STATS_TABLE}.total_profit_loss + $8,
            max_win = GREATEST({USER_GAME_STATS_TABLE}.max_win, $9),
            max_loss = LEAST({USER_GAME_STATS_TABLE}.max_loss, $10),
            game_specific_stats = $11,
            last_played_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
    """,
        user_id,
        game_type,
        1 if is_win else 0,
        1 if is_loss else 0,
        1 if is_push else 0,
        game_data["bet"],
        game_data["payout"],
        game_data["profit"],
        game_data["profit"] if is_win else 0,
        game_data["profit"] if is_loss else 0,
        updated_specific,
    )


def _update_specific_stats(
    game_type: str, current_stats: Dict[str, Any], game_data: Dict[str, Any]
) -> Dict[str, Any]:
    """更新遊戲特定統計數據"""

    # 使用遊戲註冊系統
    game_config = GameRegistry.get_game(game_type)
    if game_config and game_config.stats_updater:
        # 更新連勝/連敗（通用邏輯）- 統一使用標準result判斷
        result = game_data.get("result", "lose")
        is_win = result == "win"
        is_loss = result == "lose"
        _update_streak_stats(current_stats, is_win, is_loss)

        # 調用遊戲特定的統計更新函數
        return game_config.stats_updater(current_stats, game_data)
    else:
        return current_stats


def _update_streak_stats(
    stats: Dict[str, Any], is_win: bool, is_loss: bool, is_tie: bool = False
) -> None:
    """更新連勝/連敗統計（所有遊戲通用）"""
    # 確保必要的字段存在，如果不存在則設置默認值（新用戶初始化）
    stats.setdefault("current_streak", 0)
    stats.setdefault("current_streak_type", "none")
    stats.setdefault("consecutive_wins", 0)
    stats.setdefault("consecutive_losses", 0)

    if is_win:
        if stats["current_streak_type"] == "win":
            stats["current_streak"] += 1
        else:
            stats["current_streak"] = 1
            stats["current_streak_type"] = "win"
        stats["consecutive_wins"] = max(
            stats["consecutive_wins"], stats["current_streak"]
        )
    elif is_loss:
        if stats["current_streak_type"] == "loss":
            stats["current_streak"] += 1
        else:
            stats["current_streak"] = 1
            stats["current_streak_type"] = "loss"
        stats["consecutive_losses"] = max(
            stats["consecutive_losses"], stats["current_streak"]
        )
    elif is_tie:
        # 平局不改變連勝/連敗狀態，保持當前連勝類型和數量
        pass
    else:
        # 未知結果，重置連勝
        stats["current_streak"] = 0
        stats["current_streak_type"] = "none"


async def get_user_stats_for_single_game(
    user_id: int, game_type: str, connection: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """獲取指定用戶和單一遊戲的統計數據"""
    query = (
        f"SELECT * FROM {USER_GAME_STATS_TABLE} WHERE user_id = $1 AND game_type = $2"
    )
    try:
        record = await fetch_one(query, (user_id, game_type), connection=connection)
        return dict(record) if record else None
    except Exception as e:
        logger.error(
            f"獲取單一遊戲統計失敗: user_id={user_id}, game_type={game_type}, error={e}"
        )
        raise DatabaseOperationError(f"獲取單一遊戲統計失敗: {e}") from e


async def get_user_stats_for_all_games(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取指定用戶所有遊戲的統計數據"""
    query = (
        f"SELECT * FROM {USER_GAME_STATS_TABLE} WHERE user_id = $1 ORDER BY game_type"
    )
    try:
        records = await fetch_all(query, (user_id,), connection=connection)
        return [dict(record) for record in records]
    except Exception as e:
        logger.error("獲取所有遊戲統計失敗: user_id=%s, error=%s", user_id, e)
        raise DatabaseOperationError(f"獲取所有遊戲統計失敗: {e}") from e


async def get_user_game_rankings(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取指定用戶在所有遊戲和總體上的排名數據"""
    query = "SELECT * FROM gacha_user_game_rankings_mv WHERE user_id = $1"
    try:
        records = await fetch_all(query, (user_id,), connection=connection)
        return [dict(record) for record in records]
    except Exception as e:
        logger.error("獲取用戶遊戲排名失敗: user_id=%s, error=%s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶遊戲排名失敗: {e}") from e
