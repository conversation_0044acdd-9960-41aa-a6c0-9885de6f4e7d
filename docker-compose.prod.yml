services:
  # Bot 正式環境配置（連接到主機現有服務）
  bot-prod:
    build:
      context: .
      target: runtime  # 生產環境使用完整建置
    container_name: dickpk_bot_prod
    env_file: []  # 禁止讀取 .env 文件，避免被本機設定覆蓋
    volumes:
      # 基本資料掛載
      - ./logs:/app/logs
      - ./database/gacha_waifu:/app/database/gacha_waifu
      - ./fonts:/app/fonts
      - ./auxiliary/services/image_processing:/app/auxiliary/services/image_processing
      - ./downloaded_gacha_master_cards:/app/downloaded_gacha_master_cards:ro

      # 配置文件掛載
      - ./config/config.yaml:/app/config/config.yaml:ro
      - ./config/gacha_settings.yaml:/app/config/gacha_settings.yaml:ro
      - ./config/ui_settings.yaml:/app/config/ui_settings.yaml:ro

      # 正式環境：可選的代碼熱更新（取消註釋以啟用）
      # - .:/app  # 如需代碼熱更新，取消此行註釋
    environment:
      - DISCORD_TOKEN=MTIyMTIzMDczNDYwMjE0MTcyNw.GLHcmU.PoVxxvjudUO-2TAihNpE_1jHwpzESDubNqaQPA
      - GACHA_DB_NAME=gacha_database
      - LOG_LEVEL=WARNING
      - PG_HOST=host.docker.internal  # 連接到主機的 PostgreSQL
      - PG_PORT=5432
      - PG_USER=postgres
      - PG_PASSWORD=26015792
      - PVE_DB_TYPE=postgresql
      - LADDER_DB_TYPE=postgresql
      - LADDER_DATABASE=ladder_database
      - DEV_MODE=false
      - PVE_DATABASE=pve_database
      - ENABLE_PVE_SYSTEM=false
      - ENABLE_LADDER_SYSTEM=true
      - ENABLE_GACHA_SYSTEM=true
      - AI_API_KEY=26015792
      - RPG=false
      - REDIS_HOST=host.docker.internal  # 連接到主機的 Redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=
      - REDIS_ENABLED=True
      - WEBHOOK_NOTIFICATIONS_ENABLED=true
      - SHARD_COUNT=2
      # 字體相關環境變數（確保Docker環境正確讀取字體）
      - FONTCONFIG_PATH=/etc/fonts
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data: 