"""
被動觸發處理器 (PassiveTriggerHandler)
處理被動技能的觸發條件檢查和效果應用
"""

from typing import Any, Dict, List

from utils.logger import logger

from ...config.loader import get_config_loader
from ...formula_engine.evaluator import evaluate_formula
from ..models.battle import Battle
from ..models.combatant import Combatant
from ..models.skill_instance import SkillInstance
from . import effect_applier, target_selector


async def check_and_apply_passives(
    event_type: str,
    event_data: Dict[str, Any],
    potential_passive_owners: List["Combatant"],
    battle_context: "Battle",
) -> List[Dict[str, Any]]:
    """
    檢查並應用被動技能
    """
    results = []
    for combatant in potential_passive_owners:
        if not combatant.is_alive():
            continue
        if combatant.innate_passive:
            passive_results = await _check_passive_skill(
                combatant,
                combatant.innate_passive,
                event_type,
                event_data,
                battle_context,
                is_innate=True,
            )
            results.extend(passive_results)
        for passive_skill in combatant.common_passives:
            passive_results = await _check_passive_skill(
                combatant,
                passive_skill,
                event_type,
                event_data,
                battle_context,
                is_innate=False,
            )
            results.extend(passive_results)
    return results


async def _check_passive_skill(
    owner: "Combatant",
    passive_skill: "SkillInstance",
    event_type: str,
    event_data: Dict[str, Any],
    battle_context: "Battle",
    is_innate: bool = False,
) -> List[Dict[str, Any]]:
    """
    檢查單個被動技能是否觸發
    """
    try:
        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        skill_definition = await passive_skill.get_definition(all_configs)
        if not skill_definition:
            return []

        if is_innate:
            star_level = owner.star_level
            effects_by_star = skill_definition.get("effects_by_star_level", {})
            applicable_star_levels = [
                int(k) for k in effects_by_star.keys() if int(k) <= star_level
            ]
            if applicable_star_levels:
                max_applicable_level = max(applicable_star_levels)
                star_config = effects_by_star.get(str(max_applicable_level), {})
                passive_effect_blocks = star_config.get("passive_effect_blocks", [])
            else:
                passive_effect_blocks = []
        else:
            passive_effect_blocks = skill_definition.get("base_effects", [])

        if not passive_effect_blocks:
            return []

        results = []
        for effect_block in passive_effect_blocks:
            block_results = await _process_passive_effect_block(
                owner,
                effect_block,
                event_type,
                event_data,
                battle_context,
                passive_skill,
                is_innate,
            )
            results.extend(block_results)
        return results
    except Exception as e:
        logger.error("檢查被動技能錯誤: %s", e)
        return []


async def _process_passive_effect_block(
    owner: "Combatant",
    effect_block: Dict[str, Any],
    event_type: str,
    event_data: Dict[str, Any],
    battle_context: "Battle",
    passive_skill: "SkillInstance",
    is_innate: bool,
) -> List[Dict[str, Any]]:
    """
    處理被動效果塊
    """
    try:
        trigger_condition = effect_block.get("trigger_condition", {})
        if not await _check_trigger_condition(
            trigger_condition,
            event_type,
            event_data,
            owner,
            battle_context,
            is_innate,
        ):
            return []

        if not await _check_trigger_chance(
            effect_block, owner, battle_context, is_innate
        ):
            return []

        targets = await _determine_passive_targets(
            owner, effect_block, event_data, battle_context
        )
        if not targets:
            return []

        effect_definitions = effect_block.get("effect_definitions", [])
        if not effect_definitions:
            return []

        custom_vars = _prepare_passive_context_vars(
            owner, event_data, passive_skill, is_innate
        )

        return await effect_applier.apply_effect_definitions(
            caster=owner,
            initial_targets=targets,
            effect_definitions=effect_definitions,
            battle_context=battle_context,
            source_skill_tags=effect_block.get("tags", []),
            source_skill_instance=passive_skill,
            custom_vars_from_source=custom_vars,
        )
    except Exception as e:
        logger.error("處理被動效果塊錯誤: %s", e)
        return []


async def _check_trigger_condition(
    trigger_condition: Dict[str, Any],
    event_type: str,
    event_data: Dict[str, Any],
    owner: "Combatant",
    battle_context: "Battle",
    is_innate: bool,
) -> bool:
    """
    檢查觸發條件
    """
    required_event_type = trigger_condition.get("type")
    if required_event_type and required_event_type != event_type:
        return False

    sub_type = trigger_condition.get("sub_type")
    if sub_type:
        event_sub_type = (
            event_data.get("damage_type")
            or event_data.get("heal_type")
            or event_data.get("sub_type")
        )
        if event_sub_type and sub_type != event_sub_type:
            return False

    additional_conditions = trigger_condition.get("additional_conditions", [])
    if not additional_conditions:
        return True

    context_vars = _prepare_condition_context(
        owner, event_data, battle_context, is_innate
    )

    for condition in additional_conditions:
        if not await _evaluate_single_condition(
            condition, context_vars, owner, event_data
        ):
            return False
    return True


async def _evaluate_single_condition(
    condition: Dict[str, Any],
    context_vars: Dict[str, Any],
    owner: "Combatant",
    event_data: Dict[str, Any],
) -> bool:
    """
    評估單個條件
    """
    try:
        condition_formula = condition.get("formula")
        if condition_formula:
            return bool(await evaluate_formula(condition_formula, context_vars))

        source_combatant = condition.get("source_combatant", "self")
        check_type = condition.get("check")
        value = condition.get("value", 0)

        if not check_type:
            return True

        if source_combatant == "self":
            target_combatant = owner
        elif source_combatant == "target":
            target_id = event_data.get("target_id")
            if not target_id:
                return False
            return False
        else:
            return False

        if check_type == "hp_below_percent":
            current_hp_percent = target_combatant.current_hp / max(
                target_combatant.max_hp, 1
            )
            return current_hp_percent < value
        elif check_type == "hp_above_percent":
            current_hp_percent = target_combatant.current_hp / max(
                target_combatant.max_hp, 1
            )
            return current_hp_percent > value
        elif check_type == "stat_above":
            stat_name = condition.get("stat_name", "patk")
            stat_value = target_combatant.current_stats.get(stat_name, 0)
            return stat_value > value
        elif check_type == "stat_below":
            stat_name = condition.get("stat_name", "patk")
            stat_value = target_combatant.current_stats.get(stat_name, 0)
            return stat_value < value
        else:
            logger.warning("未知的條件檢查類型: %s", check_type)
            return True
    except Exception as e:
        logger.error("評估條件時發生錯誤: %s", e)
        return False


async def _check_trigger_chance(
    effect_block: Dict[str, Any],
    owner: "Combatant",
    battle_context: "Battle",
    is_innate: bool,
) -> bool:
    """
    檢查觸發機率
    """
    chance_formula = (
        effect_block.get("chance_formula")
        or effect_block.get("trigger_condition", {}).get("chance_formula")
        or "1.0"
    )
    context_vars = {
        "caster_star_level": owner.star_level if is_innate else 0,
        "star_level": owner.star_level if is_innate else 0,
        "skill_level": 0 if is_innate else 1,
        "caster_stat_patk": owner.current_stats.get("patk", 0),
        "caster_stat_matk": owner.current_stats.get("matk", 0),
        "caster_stat_crit_rate": owner.current_stats.get("crit_rate", 0),
        "caster_current_hp_percent": owner.current_hp / max(owner.max_hp, 1),
    }
    trigger_chance = await evaluate_formula(chance_formula, context_vars)
    trigger_chance = max(0.0, min(1.0, trigger_chance))
    if hasattr(battle_context, "_rng"):
        roll = battle_context._rng.random()
    else:
        import random

        roll = random.random()
    return roll < trigger_chance


async def _determine_passive_targets(
    owner: "Combatant",
    effect_block: Dict[str, Any],
    event_data: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    確定被動技能的目標
    """
    target_override = effect_block.get("target_override")
    if target_override:
        return await target_selector.select_targets(
            owner,
            target_override,
            battle_context,
        )
    else:
        return [owner]


def _prepare_passive_context_vars(
    owner: "Combatant",
    event_data: Dict[str, Any],
    passive_skill: "SkillInstance",
    is_innate: bool,
) -> Dict[str, Any]:
    """
    準備被動技能的上下文變量
    """
    context_vars = {
        "passive_owner_id": owner.instance_id,
        "is_innate_passive": 1 if is_innate else 0,
    }
    if is_innate:
        context_vars["star_level"] = owner.star_level
    else:
        context_vars["skill_level"] = passive_skill.current_level
    context_vars.update(event_data)
    return context_vars


def _prepare_condition_context(
    owner: "Combatant",
    event_data: Dict[str, Any],
    battle_context: "Battle",
    is_innate: bool,
) -> Dict[str, Any]:
    """
    準備條件評估的上下文
    """
    context = {
        "owner_stat_hp": owner.current_hp,
        "owner_stat_max_hp": owner.max_hp,
        "owner_current_hp_percent": owner.current_hp / max(owner.max_hp, 1),
        "owner_missing_hp_percent": 1 - (owner.current_hp / max(owner.max_hp, 1)),
        "owner_stat_patk": owner.current_stats.get("patk", 0),
        "owner_stat_matk": owner.current_stats.get("matk", 0),
        "current_turn": battle_context.current_turn,
        "is_innate": 1 if is_innate else 0,
        "star_level": owner.star_level if is_innate else 0,
    }
    context.update(event_data)
    return context
