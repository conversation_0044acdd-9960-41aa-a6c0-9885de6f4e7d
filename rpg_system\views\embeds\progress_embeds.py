"""
RPG進度相關的嵌入構建器
負責創建進度查看、隊伍管理等相關的Discord嵌入
"""

from typing import Any, Dict, List, Union

import discord

from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class ProgressEmbedBuilder:
    """進度相關嵌入構建器"""

    @staticmethod
    def create_progress_embed(
        progress: Dict[str, Any], user: Union[discord.User, discord.Member]
    ) -> discord.Embed:
        """
        創建進度信息嵌入

        Args:
            progress: 用戶進度數據
            user: Discord用戶對象

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"🏰 {user.display_name} 的RPG進度",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow(),
            )

            # 基本進度信息
            embed.add_field(
                name="📊 當前進度",
                value=f"當前樓層：{progress['current_floor_unlocked']:,}\n"
                f"本樓層勝利：{progress['current_floor_wins']:,}\n"
                f"歷史最高：{progress['max_floor_cleared']:,}",
                inline=True,
            )

            # 下一樓層信息
            if progress["can_unlock_next_floor"]:
                next_floor_text = f"✅ 可以解鎖樓層 {progress['next_floor_id']:,}"
            else:
                wins_needed = progress["wins_needed_for_next_floor"]
                next_floor_text = f"🔒 還需 {wins_needed:,} 次勝利解鎖樓層 {progress['next_floor_id']:,}"

            embed.add_field(name="🎯 下一目標", value=next_floor_text, inline=True)

            # 隊伍配置狀態
            formation = progress.get("current_team_formation", [])
            if formation:
                team_text = f"已配置 {len(formation)} 張卡牌"
            else:
                team_text = "尚未配置隊伍"

            embed.add_field(name="⚔️ 隊伍狀態", value=team_text, inline=True)

            # 添加用戶頭像
            embed.set_thumbnail(url=user.display_avatar.url)

            # 添加腳註
            embed.set_footer(text="使用 /team 管理隊伍配置")

            return embed

        except Exception as e:
            logger.error("創建進度嵌入失敗: %s", e)
            return ProgressEmbedBuilder._create_error_embed("創建進度信息時發生錯誤")

    @staticmethod
    def create_team_embed(
        team_details: List[Dict[str, Any]], user: Union[discord.User, discord.Member]
    ) -> discord.Embed:
        """
        創建隊伍信息嵌入

        Args:
            team_details: 隊伍卡牌詳細信息列表
            user: Discord用戶對象

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"⚔️ {user.display_name} 的隊伍配置",
                color=discord.Color.green(),
                timestamp=discord.utils.utcnow(),
            )

            if not team_details:
                embed.description = (
                    "🔒 尚未配置隊伍\n\n使用 `/team set <卡牌ID>` 來設置隊伍"
                )
                embed.color = discord.Color.orange()
            else:
                # 顯示隊伍成員（目前只有一張卡牌）
                for i, card in enumerate(team_details, 1):
                    card_info = (
                        f"**{card['name']}**\n"
                        f"稀有度：{card['rarity']}\n"
                        f"RPG等級：{card['rpg_level']}\n"
                        f"星級：{card['star_level']}\n"
                        f"收藏ID：{card['id']}"
                    )

                    embed.add_field(
                        name=f"🎴 隊伍成員 {i}", value=card_info, inline=True
                    )

                # 添加隊伍統計
                avg_level = sum(card["rpg_level"] for card in team_details) / len(
                    team_details
                )
                total_stars = sum(card["star_level"] for card in team_details)

                embed.add_field(
                    name="📈 隊伍統計",
                    value=f"平均等級：{avg_level:.1f}\n"
                    f"總星級：{total_stars}\n"
                    f"隊伍人數：{len(team_details)}/1",
                    inline=True,
                )

            # 添加用戶頭像
            embed.set_thumbnail(url=user.display_avatar.url)

            # 添加腳註
            embed.set_footer(text="使用 /team set <卡牌ID> 更改隊伍配置")

            return embed

        except Exception as e:
            logger.error("創建隊伍嵌入失敗: %s", e)
            return ProgressEmbedBuilder._create_error_embed("創建隊伍信息時發生錯誤")

    @staticmethod
    def create_team_setup_result_embed(result_type: str, message: str) -> discord.Embed:
        """
        創建隊伍設置結果嵌入

        Args:
            result_type: 結果類型 (success, error, warning)
            message: 結果消息

        Returns:
            Discord嵌入對象
        """
        if result_type == "success":
            embed = SuccessEmbed(
                title="✅ 隊伍配置成功",
                description=message,
            )
        elif result_type == "error":
            embed = discord.Embed(
                title="❌ 隊伍配置失敗", description=message, color=discord.Color.red()
            )
        else:  # warning
            embed = discord.Embed(
                title="⚠️ 隊伍配置警告",
                description=message,
                color=discord.Color.orange(),
            )

        return embed

    @staticmethod
    def _create_error_embed(message: str) -> discord.Embed:
        """
        創建錯誤嵌入

        Args:
            message: 錯誤消息

        Returns:
            Discord錯誤嵌入
        """
        return discord.Embed(
            title="❌ 錯誤", description=message, color=discord.Color.red()
        )
