"""
提供可重用的「加入最愛」按鈕組件。
這個模塊定義了一個繼承自 discord.ui.Button 的 FavoriteButton 類，
它封裝了收藏/取消收藏的顯示邏輯和交互行為。
"""

from typing import TYPE_CHECKING, Any

import discord

from gacha.exceptions import BusinessError
from gacha.services import favorite_service
from utils.logger import logger

# 為了類型提示，避免循環導入
if TYPE_CHECKING:
    # 導入所有可能使用這個按鈕的 View 類型
    pass

# 我們約定，任何使用 FavoriteButton 的 View 都應該有以下屬性/方法：
# - user: discord.User
# - current_card: 一個包含 .card.card_id 和 .is_favorite 屬性的對象
# - update_favorite_state(card_id: int, is_favorite: bool) -> bool: 更新內部狀態的方法
# - get_current_page_embed() 或 get_combined_embed(): 獲取更新後 Embed 的方法


class FavoriteButton(discord.ui.Button):
    """
    一個可重用的「收藏/取消收藏」按鈕。
    它封裝了自身的顯示邏輯和點擊後的核心操作。
    """

    def __init__(
        self, is_favorite: bool, row: int = 1, custom_id: str = "toggle_favorite"
    ):
        """
        初始化最愛按鈕。

        Args:
            is_favorite (bool): 卡片的初始收藏狀態。
            row (int): 按鈕所在的行。
            custom_id (str): 按鈕的自定義 ID。
        """
        # 根據初始狀態設置外觀
        style = (
            discord.ButtonStyle.success
            if is_favorite
            else discord.ButtonStyle.secondary
        )
        label = "已收藏" if is_favorite else "加入最愛"
        # 你可以把 emoji 放在這裡或者從配置中讀取
        emoji = (
            "<a:pu:1365482490478989353>"
            if is_favorite
            else "<a:sw:1365447243863429273>"
        )

        super().__init__(
            style=style, label=label, emoji=emoji, row=row, custom_id=custom_id
        )

    async def callback(self, interaction: discord.Interaction):
        """
        按鈕被點擊時的回調邏輯。
        """
        # self.view 會自動指向此按鈕所在的 View 實例
        view: Any = self.view

        # 1. 從 View 獲取當前所需信息
        # 這種方式比之前的 FavoriteStateUpdatable 接口更靈活
        if not all(hasattr(view, attr) for attr in ["user", "current_card"]):
            # This is a programming error, let the default error handler catch it.
            raise AttributeError(
                f"View '{type(view).__name__}' is missing required attributes for FavoriteButton."
            )

        current_card_wrapper = view.current_card
        if (
            not current_card_wrapper
            or not hasattr(current_card_wrapper, "card")
            or not current_card_wrapper.card
        ):
            raise BusinessError("當前沒有可操作的卡片。")

        user_id = view.user.id
        card_id_to_toggle = current_card_wrapper.card.card_id

        # 2. 執行核心業務邏輯
        await interaction.response.defer()
        # 根據規範，移除 try-except，讓錯誤自然冒泡
        new_is_favorite = await favorite_service.toggle_favorite_card(
            user_id, card_id_to_toggle, interaction.user.id
        )

        # 3. 更新 View 的內部狀態
        if hasattr(view, "update_favorite_state") and callable(
            view.update_favorite_state
        ):
            view.update_favorite_state(card_id_to_toggle, new_is_favorite)

        # 4. 更新按鈕自身的外觀
        self.update_style(is_favorite=new_is_favorite)

        # 5. 更新整個消息（Embed + View）
        embed = None
        if hasattr(view, "get_combined_embed") and callable(
            getattr(view, "get_combined_embed", None)
        ):
            embed = await view.get_combined_embed()
        elif hasattr(view, "get_current_page_embed") and callable(
            getattr(view, "get_current_page_embed", None)
        ):
            embed = await view.get_current_page_embed()

        if embed:
            await interaction.edit_original_response(embed=embed, view=view)
        else:
            logger.warning(
                f"View '{type(view).__name__}' did not provide an embed for FavoriteButton UI update."
            )
            await interaction.edit_original_response(view=view)

    def update_style(self, is_favorite: bool):
        """一個輔助方法，用於更新按鈕的外觀。"""
        self.label = "已收藏" if is_favorite else "加入最愛"
        self.style = (
            discord.ButtonStyle.success
            if is_favorite
            else discord.ButtonStyle.secondary
        )
        self.emoji = (
            "<a:pu:1365482490478989353>"
            if is_favorite
            else "<a:sw:1365447243863429273>"
        )
