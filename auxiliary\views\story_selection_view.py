from typing import TYPE_CHECKING, Union

import discord
from discord.ext import commands

from auxiliary.exceptions import AuxiliaryError
from auxiliary.services import story_logic
from auxiliary.services.story.themes import STORY_THEMES
from auxiliary.views.story_master_view import StoryMasterView
from gacha.views.collection.collection_view.base_pagination import BasePaginationView

if TYPE_CHECKING:
    from auxiliary.cogs.story_cog import StoryCog


class StorySelectionView(BasePaginationView):
    def __init__(
        self,
        bot: commands.Bot,
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
    ):
        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=1,
            total_pages=len(STORY_THEMES),
            timeout=300,  # 5 minutes timeout instead of None
        )
        self.story_cog = story_cog
        self.add_story_buttons()

    def add_story_buttons(self):
        self.select_button = discord.ui.Button(
            label="選擇這個故事",
            style=discord.ButtonStyle.success,
            custom_id="select_story",
            row=1,
        )
        self.select_button.callback = self.select_story_callback
        self.add_item(self.select_button)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        theme = STORY_THEMES[page - 1]
        description_text = theme.get("description", "暫無詳細描述。")
        embed = discord.Embed(
            title=f"📜 故事主題：{theme['title']}",
            description=description_text,
            color=discord.Color.gold(),
        )
        if theme.get("image_url"):
            embed.set_image(url=theme["image_url"])

        embed.set_footer(text=f"主題 {page}/{self.total_pages}")

        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.edit_original_response(embed=embed, view=self)

    async def select_story_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        theme = STORY_THEMES[self.current_page - 1]

        # 清除舊故事並開始新故事
        await story_logic.clear_history(interaction.user.id)
        await story_logic.start_new_story(
            interaction.user.id, interaction.user.display_name, theme["title"]
        )
        history_data = await story_logic.get_user_history(interaction.user.id)

        if self.bot is None:
            raise AuxiliaryError("Bot instance is None")

        if not history_data:
            raise AuxiliaryError("Failed to start a new story.")

        view = StoryMasterView(self.bot, interaction.user, self.story_cog, history_data)
        await view._update_page(view.total_pages, interaction)
