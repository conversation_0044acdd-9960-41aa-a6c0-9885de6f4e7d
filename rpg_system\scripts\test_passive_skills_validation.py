"""
測試被動技能配置驗證
"""

import json
import os
import sys

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from rpg_system.config.pydantic_models.passive_skills import PassiveSkillsConfig


def main():
    """測試被動技能配置驗證"""
    try:
        # 讀取生成的被動技能配置
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "config",
            "data",
            "passive_skills.json",
        )

        with open(config_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # 驗證配置
        PassiveSkillsConfig(data)
        print("✅ 被動技能配置驗證成功！")
        print(f"📊 總共驗證了 {len(data)} 個被動技能")

        # 檢查每個技能的結構
        for skill_id, skill_config in list(data.items())[:3]:  # 檢查前3個
            print(f"\n🔍 技能 {skill_id}:")
            print(f"  名稱: {skill_config['name']}")
            print(f"  稀有度: {skill_config['skill_rarity']}")
            print(f"  最大等級: {skill_config['max_level']}")
            print(f"  效果塊數量: {len(skill_config['base_effects'])}")

            # 檢查第一個效果塊
            if skill_config["base_effects"]:
                effect_block = skill_config["base_effects"][0]
                trigger = effect_block["trigger_condition"]
                print(f"  觸發條件: {trigger['type']}")
                print(f"  效果數量: {len(effect_block['effect_definitions'])}")

        print("\n✅ 所有被動技能配置都符合模型要求！")
        return 0

    except Exception as e:
        print(f"❌ 被動技能配置驗證失敗: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
