"""
主動技能生成器輔助方法

包含主動技能生成器的輔助方法，用於處理複雜的技能生成邏輯。
重構後使用固定三種變體系統和配置化的組合變體。
"""

import itertools
import json
import os
import random
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from utils.logger import logger


class SkillVariantType(Enum):
    """技能變體類型枚舉"""

    LARGE_EFFECT = "large_effect"  # 大效果 + 長冷卻 + 高MP
    SMALL_EFFECT = "small_effect"  # 小效果 + 短冷卻 + 低MP
    MEDIUM_EFFECT = "medium_effect"  # 中間值


# 簡化的特性映射，只保留實際使用的
EFFECT_TRAIT_MAP = {
    "PHYSICAL": "PHYSICAL",
    "MAGICAL": "MAGICAL",
    "HEAL": "HEAL",
    "BOOST": "BOOST",
    "DEBUFF": "DEBUFF",
    "DAMAGE": "DAMAGE",
    "SPECIAL": "SPECIAL",
}


class ActiveSkillHelpers:
    """主動技能生成器的輔助方法類"""

    def __init__(self, skill_generator):
        """初始化輔助方法類

        Args:
            skill_generator: 主動技能生成器實例
        """
        self.generator = skill_generator
        self.variant_config = self._load_variant_config()
        # 按稀有度分組的可用組合池
        self.available_combinations_by_rarity = {}
        # 初始化組合池
        self._initialize_combination_pools()

    def _load_variant_config(self) -> Dict[str, Any]:
        """加載技能變體配置文件"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(
                current_dir, "config", "skill_variant_config.json"
            )

            if not os.path.exists(config_path):
                logger.warning("變體配置文件不存在: %s，使用默認配置", config_path)
                return self._get_default_variant_config()

            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error("加載變體配置文件時出錯: %s", e)
            return self._get_default_variant_config()

    def _get_default_variant_config(self) -> Dict[str, Any]:
        """獲取默認變體配置"""
        return {
            "variant_multipliers": {
                "large_effect": {
                    "effect_multiplier": 1.8,
                    "mp_cost_multiplier": 1.6,
                    "cooldown_multiplier": 1.5,
                    "duration_multiplier": 1.3,
                },
                "small_effect": {
                    "effect_multiplier": 0.6,
                    "mp_cost_multiplier": 0.7,
                    "cooldown_multiplier": 0.6,
                    "duration_multiplier": 0.8,
                },
                "medium_effect": {
                    "effect_multiplier": 1.0,
                    "mp_cost_multiplier": 1.0,
                    "cooldown_multiplier": 1.0,
                    "duration_multiplier": 1.0,
                },
            },
            "combination_variants": {},
        }

    def _initialize_combination_pools(self):
        """初始化每個稀有度的可用組合池"""
        for rarity in range(1, 8):
            self.available_combinations_by_rarity[rarity] = (
                self._generate_all_possible_combinations(rarity)
            )
            logger.info(
                "稀有度 %s 初始化了 %s 個可能的技能組合",
                rarity,
                len(self.available_combinations_by_rarity[rarity]),
            )

    def _generate_all_possible_combinations(
        self, rarity: int
    ) -> List[Tuple[str, str, str]]:
        """生成指定稀有度的所有可能組合

        Args:
            rarity: 稀有度等級

        Returns:
            所有可能的 (模板名稱, 變體類型, 次要效果組合) 列表
        """
        combinations = []

        # 獲取該稀有度可用的模板
        try:
            available_templates = self.generator.rarity_manager.get_effect_templates(
                rarity
            )
        except Exception as e:
            logger.error("無法獲取稀有度 %s 的模板: %s", rarity, e)
            return combinations

        # 獲取變體類型
        variant_types = [variant.value for variant in SkillVariantType]

        # 獲取組合變體配置
        rarity_str = str(rarity)
        combination_config = self.variant_config.get("combination_variants", {}).get(
            rarity_str, {}
        )
        combination_chance = combination_config.get("combination_chance", 0.0)
        available_secondary_effects = combination_config.get(
            "available_secondary_effects", []
        )
        max_additional = combination_config.get("max_additional_effects", 1)

        for template in available_templates:
            for variant in variant_types:
                # 無次要效果的組合
                combinations.append((template, variant, "NONE"))

                # 如果有組合變體機會，添加有次要效果的組合
                if combination_chance > 0 and available_secondary_effects:
                    # 單個次要效果
                    for secondary in available_secondary_effects:
                        combinations.append((template, variant, secondary))

                    # 多個次要效果（如果允許）
                    if max_additional > 1:
                        for num_effects in range(
                            2,
                            min(
                                max_additional + 1, len(available_secondary_effects) + 1
                            ),
                        ):
                            for secondary_combo in itertools.combinations(
                                available_secondary_effects, num_effects
                            ):
                                secondary_str = "+".join(sorted(secondary_combo))
                                combinations.append((template, variant, secondary_str))

        return combinations

    def generate_skill_with_variants(
        self, template_name: str, effect_category: str, rarity: int, name: Optional[str]
    ) -> Dict[str, Any]:
        """使用組合池系統生成唯一技能配置

        Args:
            template_name: 效果模板名稱（可能被重新選擇）
            effect_category: 效果分類
            rarity: 稀有度等級
            name: 技能名稱（如果為None，則根據最終模板生成）

        Returns:
            完整的技能配置字典
        """
        # 從可用組合池中選擇一個未使用的組合
        combination = self._select_unused_combination(rarity)

        if combination is None:
            # 所有組合都用完了，重置組合池
            self._reset_combination_pool(rarity)
            combination = self._select_unused_combination(rarity)

        if combination is None:
            # 如果還是沒有可用組合，使用回退方案
            logger.error("無法為稀有度 %s 選擇組合，使用回退方案", rarity)
            # 如果沒有傳入名稱，根據原始模板生成（使用默認變體類型）
            if name is None:
                name = self.generator._generate_name_by_template(
                    template_name, rarity, "medium_effect"
                )
            # 確保 name 不為 None
            assert name is not None, "Generated name should not be None"
            return self._generate_fallback_skill(
                template_name, effect_category, rarity, name
            )

        # 解析組合
        selected_template, variant_type_str, secondary_effects_str = combination
        variant_type = SkillVariantType(variant_type_str)

        # 重新計算效果分類（基於實際選擇的模板）
        actual_effect_category = self.generator.get_template_category(selected_template)

        # 如果沒有傳入名稱，根據最終確定的模板和變體類型生成名稱
        if name is None:
            # 將變體類型轉換為名稱庫中的鍵
            variant_key = (
                variant_type.value.lower()
            )  # 例如 SMALL_EFFECT -> small_effect
            name = self.generator._generate_name_by_template(
                selected_template, rarity, variant_key
            )

        # 確保 name 不為 None
        assert name is not None, "Name should not be None after generation"

        # 創建主要效果定義
        primary_effect_def = self._create_variant_effect_definition(
            selected_template, actual_effect_category, rarity, variant_type
        )

        # 計算基礎數值
        base_mp_cost, base_cooldown = self._calculate_base_values(
            actual_effect_category, rarity, variant_type
        )

        # 生成描述
        description = self._generate_variant_description(
            name, selected_template, actual_effect_category, rarity, primary_effect_def
        )

        # 處理次要效果
        all_effect_definitions = [primary_effect_def]
        final_mp_cost, final_cooldown, updated_description = (
            self._apply_predefined_secondary_effects(
                all_effect_definitions,
                rarity,
                description,
                base_mp_cost,
                base_cooldown,
                secondary_effects_str,
            )
        )

        # 標記組合為已使用
        self._mark_combination_used(rarity, combination)

        return self._build_skill_config(
            name,
            updated_description,
            rarity,
            all_effect_definitions,
            final_mp_cost,
            final_cooldown,
            actual_effect_category,
            variant_type,
        )

    def _select_unused_combination(self, rarity: int) -> Optional[Tuple[str, str, str]]:
        """從可用組合池中選擇一個未使用的組合

        Args:
            rarity: 稀有度等級

        Returns:
            (模板名稱, 變體類型, 次要效果組合) 或 None
        """
        available_combinations = self.available_combinations_by_rarity.get(rarity, [])

        if not available_combinations:
            logger.warning("稀有度 %s 沒有可用的組合", rarity)
            return None

        # 隨機選擇一個可用組合
        return random.choice(available_combinations)

    def _mark_combination_used(self, rarity: int, combination: Tuple[str, str, str]):
        """標記組合為已使用

        Args:
            rarity: 稀有度等級
            combination: 組合元組
        """
        if rarity in self.available_combinations_by_rarity:
            try:
                self.available_combinations_by_rarity[rarity].remove(combination)
                logger.debug(
                    "標記組合為已使用: %s, 剩餘: %s",
                    combination,
                    len(self.available_combinations_by_rarity[rarity]),
                )
            except ValueError:
                logger.warning("嘗試移除不存在的組合: %s", combination)

    def _reset_combination_pool(self, rarity: int):
        """重置指定稀有度的組合池

        Args:
            rarity: 稀有度等級
        """
        self.available_combinations_by_rarity[rarity] = (
            self._generate_all_possible_combinations(rarity)
        )
        logger.info(
            "重置稀有度 %s 的組合池，重新生成 %s 個組合",
            rarity,
            len(self.available_combinations_by_rarity[rarity]),
        )

    def _apply_predefined_secondary_effects(
        self,
        all_effect_definitions: List[Dict[str, Any]],
        rarity: int,
        description: str,
        base_mp_cost: int,
        base_cooldown: int,
        secondary_effects_str: str,
    ) -> Tuple[int, int, str]:
        """應用預定義的次要效果

        Args:
            all_effect_definitions: 效果定義列表
            rarity: 稀有度等級
            description: 基礎描述
            base_mp_cost: 基礎MP消耗
            base_cooldown: 基礎冷卻時間
            secondary_effects_str: 次要效果字符串

        Returns:
            (最終MP消耗, 最終冷卻時間, 更新後的描述)
        """
        if secondary_effects_str == "NONE":
            return base_mp_cost, base_cooldown, description

        # 解析次要效果
        secondary_effects = secondary_effects_str.split("+")

        # 獲取主要效果的目標類型以進行兼容性檢查
        primary_target_type = (
            self._infer_target_type_from_effect(all_effect_definitions[0])
            if all_effect_definitions
            else "ENEMY_SINGLE"
        )

        # 添加次要效果定義，但要檢查目標類型兼容性
        compatible_secondary_effects = []
        for effect_template in secondary_effects:
            secondary_category = self._get_effect_category_from_template(
                effect_template
            )
            secondary_effect_def = self.generator.create_effect_definition(
                effect_template, secondary_category, max(1, rarity - 1), 1.0
            )

            # 檢查目標類型兼容性
            secondary_target_type = self._infer_target_type_from_effect(
                secondary_effect_def
            )
            if self._are_target_types_compatible(
                primary_target_type, secondary_target_type
            ):
                all_effect_definitions.append(secondary_effect_def)
                compatible_secondary_effects.append(effect_template)
            else:
                logger.warning(
                    "次要效果 %s 的目標類型 %s 與主要效果的目標類型 %s 不兼容，跳過",
                    effect_template,
                    secondary_target_type,
                    primary_target_type,
                )

        # 調整數值（每個兼容的次要效果增加15%的MP和冷卻）
        mp_multiplier = 1.0 + (len(compatible_secondary_effects) * 0.15)
        cooldown_multiplier = 1.0 + (len(compatible_secondary_effects) * 0.10)

        final_mp_cost = int(base_mp_cost * mp_multiplier)
        final_cooldown = int(base_cooldown * cooldown_multiplier)

        # 更新描述 - 使用新的描述系統
        secondary_descriptions = []
        for effect_template in compatible_secondary_effects:
            secondary_desc = (
                self.generator.description_generator.generate_secondary_description(
                    effect_template
                )
            )
            secondary_descriptions.append(secondary_desc)

        if secondary_descriptions:
            updated_description = (
                f"{description} 並{' 並'.join(secondary_descriptions)}"
            )
        else:
            updated_description = description

        return final_mp_cost, final_cooldown, updated_description

    def _infer_target_type_from_effect(self, effect_def: Dict[str, Any]) -> str:
        """從效果定義推斷目標類型

        Args:
            effect_def: 效果定義字典

        Returns:
            推斷的目標類型
        """
        effect_template = effect_def.get("effect_template", "")

        # 首先嘗試從模板配置獲取effect_type
        try:
            all_configs = self.generator.config_loader.get_all_configs_sync()
            effect_templates = all_configs.get("effect_templates", {})
            template_config = effect_templates.get(effect_template, {})
            effect_type = template_config.get("effect_type", "")
        except Exception:
            effect_type = effect_def.get("effect_type", "")

        # 基於效果類型的語義分析
        if effect_type == "DISPEL_DEBUFF":
            return "SELF"  # 驅散負面效果通常是對自身
        elif effect_type == "DAMAGE":
            return "ENEMY_SINGLE"  # 傷害效果針對敵人
        elif effect_type == "HEAL":
            return "ALLY_SINGLE"  # 治療效果針對盟友
        elif effect_type == "APPLY_SHIELD":
            return "SELF"  # 護盾通常施加給自己
        elif effect_type == "SHIELD_REMOVAL":
            return "ENEMY_SINGLE"  # 移除敵人護盾
        elif effect_type == "LOSE_MP":
            return "ENEMY_SINGLE"  # 讓敵人失去MP
        elif effect_type == "TRANSFER_MP":
            return "ENEMY_SINGLE"  # 從敵人轉移MP

        # 基於模板名稱的語義分析
        template_upper = effect_template.upper()

        # 自身效果的關鍵詞
        self_keywords = ["DISPEL", "SHIELD", "BOOST", "REGEN", "BLESSING", "DEFENSE"]
        if any(keyword in template_upper for keyword in self_keywords):
            # 但是要排除一些例外情況
            if "REMOVAL" in template_upper or "DEBUFF" in template_upper:
                return "ENEMY_SINGLE"
            return "SELF"

        # 敵人效果的關鍵詞
        enemy_keywords = [
            "DAMAGE",
            "DEBUFF",
            "POISON",
            "BURN",
            "STUN",
            "FREEZE",
            "SILENCE",
            "WEAKEN",
            "REMOVAL",
        ]
        if any(keyword in template_upper for keyword in enemy_keywords):
            return "ENEMY_SINGLE"

        # 治療效果的關鍵詞
        heal_keywords = ["HEAL", "CURE", "RESTORE"]
        if any(keyword in template_upper for keyword in heal_keywords):
            return "ALLY_SINGLE"

        # 默認返回敵人單體
        return "ENEMY_SINGLE"

    def _are_target_types_compatible(
        self, primary_target: str, secondary_target: str
    ) -> bool:
        """檢查兩個目標類型是否兼容

        Args:
            primary_target: 主要效果的目標類型
            secondary_target: 次要效果的目標類型

        Returns:
            是否兼容
        """
        # 相同目標類型總是兼容
        if primary_target == secondary_target:
            return True

        # 定義兼容性規則
        compatibility_rules = {
            "ENEMY_SINGLE": ["ENEMY_SINGLE", "ENEMY_ALL"],  # 敵人效果可以組合
            "ALLY_SINGLE": [
                "ALLY_SINGLE",
                "ALLY_ALL",
                "SELF",
            ],  # 盟友效果可以與自身效果組合
            "SELF": ["SELF", "ALLY_SINGLE", "ALLY_ALL"],  # 自身效果可以與盟友效果組合
            "ENEMY_ALL": ["ENEMY_SINGLE", "ENEMY_ALL"],
            "ALLY_ALL": ["ALLY_SINGLE", "ALLY_ALL", "SELF"],
            "ALL": ["ALL"],  # 全體效果只能與全體效果組合
        }

        compatible_targets = compatibility_rules.get(primary_target, [])
        return secondary_target in compatible_targets

    def _generate_fallback_skill(
        self, template_name: str, effect_category: str, rarity: int, name: str
    ) -> Dict[str, Any]:
        """生成回退技能（當組合池系統失敗時使用）

        Args:
            template_name: 效果模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            name: 技能名稱

        Returns:
            技能配置字典
        """
        logger.warning("使用回退方案生成技能: %s", name)

        # 使用隨機變體
        import random

        variant_type = random.choice(list(SkillVariantType))

        # 創建基本效果定義
        primary_effect_def = self._create_variant_effect_definition(
            template_name, effect_category, rarity, variant_type
        )

        # 計算基礎數值
        base_mp_cost, base_cooldown = self._calculate_base_values(
            effect_category, rarity, variant_type
        )

        # 生成描述
        description = self._generate_variant_description(
            name, template_name, effect_category, rarity, primary_effect_def
        )

        return self._build_skill_config(
            name,
            description,
            rarity,
            [primary_effect_def],
            base_mp_cost,
            base_cooldown,
            effect_category,
            variant_type,
        )

    def _create_variant_effect_definition(
        self,
        template_name: str,
        effect_category: str,
        rarity: int,
        variant_type: SkillVariantType,
    ) -> Dict[str, Any]:
        """創建帶變體調整的效果定義 - 重構版，直接生成最終公式

        Args:
            template_name: 效果模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            variant_type: 變體類型

        Returns:
            最終效果定義（無需後續修改）
        """
        # 獲取變體倍率
        variant_multipliers = self.variant_config["variant_multipliers"][
            variant_type.value
        ]
        effect_multiplier = variant_multipliers["effect_multiplier"]

        # 直接創建包含變體倍率的最終效果定義
        # 不再需要後續的公式修改，避免雙重處理
        final_effect_def = self.generator.create_effect_definition(
            template_name, effect_category, rarity, effect_multiplier
        )

        return final_effect_def

    def _calculate_base_values(
        self, effect_category: str, rarity: int, variant_type: SkillVariantType
    ) -> Tuple[int, int]:
        """計算基礎MP消耗和冷卻時間 - 重構版，保持簡潔

        Args:
            effect_category: 效果分類
            rarity: 稀有度等級
            variant_type: 變體類型

        Returns:
            (MP消耗, 冷卻時間) 的元組
        """
        # 獲取基礎數值
        base_mp = self.generator.rarity_manager.get_mp_cost(rarity, effect_category)
        base_cooldown = self.generator.rarity_manager.get_cooldown(
            rarity, effect_category
        )

        # 獲取變體倍率
        variant_multipliers = self.variant_config["variant_multipliers"][
            variant_type.value
        ]
        mp_multiplier = variant_multipliers["mp_cost_multiplier"]
        cooldown_multiplier = variant_multipliers["cooldown_multiplier"]

        # 應用變體調整
        final_mp = max(1, round(base_mp * mp_multiplier))
        final_cooldown = max(0, round(base_cooldown * cooldown_multiplier))

        return final_mp, final_cooldown

    def _generate_variant_description(
        self,
        name: str,
        template_name: str,
        effect_category: str,
        rarity: int,
        effect_def: Dict[str, Any],
    ) -> str:
        """生成技能描述（不包含變體後綴）

        Args:
            name: 技能名稱
            template_name: 模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            effect_def: 效果定義

        Returns:
            生成的描述（無變體後綴）
        """
        # 使用原有的描述生成器，不添加變體後綴
        base_description = self.generator.description_generator.generate_description(
            name, template_name.lower(), effect_category, rarity, effect_def
        )

        # 移除變體後綴，直接返回基礎描述
        return base_description

    def _get_effect_category_from_template(self, template_name: str) -> str:
        """從緩存中獲取模板分類

        Args:
            template_name: 模板名稱

        Returns:
            效果分類
        """
        return self.generator.get_template_category(template_name)

    def _build_skill_config(
        self,
        name: str,
        description: str,
        rarity: int,
        all_effect_definitions: List[Dict[str, Any]],
        final_mp_cost: int,
        final_cooldown: int,
        effect_category: str,
        variant_type: SkillVariantType,
    ) -> Dict[str, Any]:
        """構建完整的技能配置

        Args:
            name: 技能名稱
            description: 技能描述
            rarity: 稀有度等級
            all_effect_definitions: 所有效果定義
            final_mp_cost: 最終MP消耗
            final_cooldown: 最終冷卻時間
            effect_category: 效果分類
            variant_type: 變體類型

        Returns:
            完整的技能配置字典
        """
        target_type = self.generator._get_target_type(effect_category)
        max_level = self.generator.rarity_manager.get_max_level(rarity)

        # 根據變體類型調整成長參數
        variant_multipliers = self.variant_config["variant_multipliers"][
            variant_type.value
        ]
        mp_multiplier = variant_multipliers["mp_cost_multiplier"]

        return {
            "name": name,
            "description_template": description,
            "skill_rarity": rarity,
            "max_level": max_level,
            "target_type": target_type,
            "base_mp_cost": final_mp_cost,
            "mp_cost_per_level": round((0.5 + rarity * 0.1) * mp_multiplier, 2),
            "base_cooldown_turns": final_cooldown,
            "cooldown_reduction_per_level": round(0.01 * rarity, 3),
            "base_effect_definitions": all_effect_definitions,
            "xp_gain_on_sacrifice": 5 + rarity * 3,
            "xp_to_next_level_config": {
                "base_xp": 30 + rarity * 20,
                "multiplier": 1.1 + rarity * 0.05,
            },
            "tags": [effect_category.upper(), "ACTIVE", variant_type.value.upper()],
        }

    def generate_descriptive_skill_id(self, skill_config: Dict[str, Any]) -> str:
        """根據技能配置生成描述性ID

        Args:
            skill_config: 技能配置字典

        Returns:
            生成的技能ID
        """
        # 獲取基本信息
        rarity = skill_config.get("skill_rarity", 1)
        effect_defs = skill_config.get("base_effect_definitions", [])
        tags = skill_config.get("tags", [])

        # 如果是基礎攻擊，則返回特定ID
        if "BASIC" in tags:
            return "BASIC_ATTACK"

        # 從主要效果定義中獲取模板名稱
        primary_effect_template = ""
        if effect_defs and "effect_template" in effect_defs[0]:
            primary_effect_template = effect_defs[0]["effect_template"]

        # 確定主要效果類別
        effect_category = "UNKNOWN"
        for tag in tags:
            if tag in ["DAMAGE", "HEAL", "BUFF", "DEBUFF", "CONTROL", "SPECIAL"]:
                effect_category = tag
                break

        # 從效果模板中提取特性
        effect_trait = self._extract_effect_trait(primary_effect_template)

        # 如果效果特性為空，根據效果類別設置默認值
        if not effect_trait:
            effect_trait = EFFECT_TRAIT_MAP.get(effect_category, "GENERIC")

        # 獲取變體類型
        variant_tag = ""
        for tag in tags:
            if tag.endswith("_EFFECT"):
                variant_tag = f"_{tag}"
                break

        # 獲取次要效果信息
        secondary_info = ""
        if len(effect_defs) > 1:
            secondary_templates = [
                eff.get("effect_template", "UNK") for eff in effect_defs[1:]
            ]
            secondary_info = f"_WITH_{'+'.join(sorted(secondary_templates))}"

        # 生成最終ID
        return f"ACTIVE_{effect_category}_{effect_trait}_R{rarity}{variant_tag}{secondary_info}"

    def _extract_effect_trait(self, primary_effect_template: str) -> str:
        """從效果模板中提取特性

        Args:
            primary_effect_template: 主要效果模板名稱

        Returns:
            提取的效果特性
        """
        if not primary_effect_template:
            return ""

        # 簡化的特性提取邏輯
        template_upper = primary_effect_template.upper()

        # 直接映射常見模板類型
        if "PHYSICAL" in template_upper or "PATK" in template_upper:
            return "PHYSICAL"
        elif "MAGICAL" in template_upper or "MATK" in template_upper:
            return "MAGICAL"
        elif "HEAL" in template_upper:
            return "HEAL"
        elif "BOOST" in template_upper or "ENHANCE" in template_upper:
            return "BOOST"
        elif "DEBUFF" in template_upper or "WEAKEN" in template_upper:
            return "DEBUFF"
        elif "DAMAGE" in template_upper:
            return "DAMAGE"
        else:
            # 使用模板名稱的第一個有意義部分
            parts = (
                primary_effect_template.replace("BASIC_", "")
                .replace("APPLY_", "")
                .split("_")
            )
            return parts[0] if parts else "GENERIC"
