"""
Pioneer System - Research Repository
研究相關的資料庫存取
"""

from typing import Optional

import asyncpg

from gacha.repositories._base_repo import execute_query, fetch_one
from pioneer.exceptions import PioneerDatabaseError, ResearchLevelNotFoundError
from pioneer.models.pioneer_models import ResearchLevel
from utils.logger import logger

# ========================================
# 研究相關
# ========================================


async def get_research_level(
    user_id: int, project_id: str, connection: Optional[asyncpg.Connection] = None
) -> ResearchLevel:
    """獲取研究等級"""
    query = (
        "SELECT * FROM pioneer_research_levels WHERE user_id = $1 AND project_id = $2"
    )
    result = await fetch_one(query, (user_id, project_id), connection=connection)

    if not result:
        raise ResearchLevelNotFoundError(user_id, project_id)

    return ResearchLevel(
        id=result["id"],
        user_id=result["user_id"],
        project_id=result["project_id"],
        level=result["level"],
        total_invested_oil=result["total_invested_oil"],
        last_upgraded_at=result["last_upgraded_at"],
    )


async def create_research_level(
    user_id: int, project_id: str, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """創建研究等級記錄"""
    query = """
        INSERT INTO pioneer_research_levels (user_id, project_id, level)
        VALUES ($1, $2, 0)
        ON CONFLICT (user_id, project_id) DO NOTHING
    """
    try:
        await execute_query(query, (user_id, project_id), connection=connection)
        return True
    except Exception as e:
        logger.error(
            f"創建研究等級失敗 user_id={user_id}, project_id={project_id}: {e}"
        )
        raise PioneerDatabaseError("create_research_level", str(e), e) from e


async def upgrade_research_level(
    user_id: int, project_id: str, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """升級研究等級"""
    query = """
        UPDATE pioneer_research_levels
        SET level = level + 1,
            last_upgraded_at = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND project_id = $2
    """
    try:
        await execute_query(query, (user_id, project_id), connection=connection)
        return True
    except Exception as e:
        logger.error(
            f"升級研究等級失敗 user_id={user_id}, project_id={project_id}: {e}"
        )
        raise PioneerDatabaseError("upgrade_research_level", str(e), e) from e


async def add_research_investment_oil(
    user_id: int,
    project_id: str,
    amount: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """增加研究油幣投資"""
    query = """
        UPDATE pioneer_research_levels
        SET total_invested_oil = total_invested_oil + $3
        WHERE user_id = $1 AND project_id = $2
    """
    try:
        await execute_query(query, (user_id, project_id, amount), connection=connection)
        return True
    except Exception as e:
        logger.error("增加研究油幣投資失敗: %s", e)
        raise PioneerDatabaseError("add_research_investment_oil", str(e), e) from e
