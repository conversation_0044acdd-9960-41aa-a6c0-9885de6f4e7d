"""
用戶服務模組 - 提供獲取和管理用戶資訊的統一介面
"""

from decimal import Decimal
from typing import Dict, Optional

import asyncpg

import gacha.repositories.history.balance_history_repository as balance_history_repo
import gacha.repositories.user.user_repository as user_repo
from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    InsufficientBalanceError,
    InsufficientOilTicketsError,
    InvalidOperationError,
    UserNotFoundError,
)
from gacha.models.models import GachaUser
from utils.logger import logger


async def get_user(
    user_id: int,
    create_if_missing: bool = False,
    nickname: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> GachaUser:
    """
    獲取用戶資訊，可選是否自動創建

    Args:
        user_id: 用戶ID
        create_if_missing: 如果用戶不存在是否創建
        nickname: 用戶暱稱（僅在創建時使用）
        connection: 可選的資料庫連接

    Returns:
        GachaUser: 用戶對象

    Raises:
        UserNotFoundError: 如果用戶不存在且不創建
        DatabaseOperationError: 如果資料庫操作失敗
    """
    # 根據規範，移除 try...except，讓異常自然冒泡
    user = await user_repo.get_user_optional(user_id, connection=connection)
    if not user and create_if_missing:
        user = await create_user(user_id, nickname, connection=connection)
    elif not user:
        raise UserNotFoundError(f"找不到用戶 {user_id}", user_id=user_id)
    return user


async def create_user(
    user_id: int,
    nickname: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> GachaUser:
    """創建新用戶"""
    return await user_repo.create_user(user_id, nickname, connection=connection)


async def get_user_for_update(
    user_id: int, connection: asyncpg.Connection
) -> GachaUser:
    """在事務中獲取用戶信息並鎖定行"""
    return await user_repo.get_user_for_update(user_id, connection=connection)


async def get_balance(
    user_id: int,
    nickname: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, int]:
    """
    獲取用戶油幣餘額和相關資訊（自動創建用戶）

    Args:
        user_id: 用戶ID
        nickname: 用戶暱稱（僅在創建時使用）
        connection: 可選的資料庫連接

    Returns:
        Dict[str, int]: 包含餘額和抽卡次數的字典

    Raises:
        UserNotFoundError: 如果用戶不存在且無法創建
        DatabaseOperationError: 如果資料庫操作失敗
    """
    user = await get_user(
        user_id, create_if_missing=True, nickname=nickname, connection=connection
    )
    return {"balance": user.oil_balance, "total_draws": user.total_draws}


async def get_user_balance(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """
    獲取用戶油幣餘額，主要用於 GachaService 的預檢查。
    如果用戶不存在，則拋出 UserNotFoundError。不會自動創建用戶。
    """
    # 根據規範，移除 try...except，讓異常自然冒泡
    user = await get_user(user_id, create_if_missing=False, connection=connection)
    return user.oil_balance


async def get_nickname(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[str]:
    """獲取用戶暱稱（不自動創建用戶）"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    user = await get_user(user_id, create_if_missing=False, connection=connection)
    return user.nickname


async def ensure_nickname_updated(
    user_id: int, nickname: str, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """確保用戶暱稱已更新"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    updated = await user_repo.update_nickname_if_changed(
        user_id, nickname, connection=connection
    )
    return updated


async def update_daily_claim(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶每日獎勵領取時間"""
    await user_repo.update_daily_claim(user_id, connection=connection)


async def award_balance(
    user_id: int,
    amount: int,
    transaction_type: str = "unknown",
    reason: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """增加或減少用戶餘額 (返回新餘額)，並記錄歷史"""

    async def _logic_with_transaction(conn: asyncpg.Connection) -> int:
        # 在事務中，先確保用戶存在，然後再執行餘額操作
        await get_user(user_id, create_if_missing=True, connection=conn)
        return await _award_balance_logic(
            user_id, amount, transaction_type, reason, conn
        )

    if not connection:
        pool = get_pool()
        async with pool.acquire() as conn_internal:
            async with conn_internal.transaction():
                return await _logic_with_transaction(conn_internal)
    else:
        # 如果傳入了外部連接，我們假設它已經在一個事務中
        return await _logic_with_transaction(connection)


async def _award_balance_logic(
    user_id: int,
    amount: int,
    transaction_type: str,
    reason: Optional[str],
    connection: asyncpg.Connection,
) -> int:
    """Internal logic for awarding balance and recording history within a transaction"""
    user = await user_repo.get_user_for_update(user_id, connection=connection)
    balance_before = user.oil_balance

    if amount < 0 and balance_before + amount < 0:
        raise InsufficientBalanceError(required=abs(amount), current=balance_before)

    new_balance = balance_before + amount
    await user_repo.update_balance(user_id, new_balance, connection=connection)

    # 寫入歷史記錄
    await balance_history_repo.create_balance_history_record(
        user_id=user_id,
        change_amount=amount,
        balance_before=balance_before,
        balance_after=new_balance,
        transaction_type=transaction_type,
        reason=reason,
        connection=connection,
    )

    return new_balance


async def get_oil_ticket_balance(
    user_id: int,
    create_if_missing: bool = True,
    connection: Optional[asyncpg.Connection] = None,
) -> Decimal:
    """獲取用戶的油票餘額 (使用 Decimal)"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    if create_if_missing:
        await get_user(user_id, create_if_missing=True, connection=connection)

    balance = await user_repo.get_oil_ticket_balance(user_id, connection=connection)
    return balance


async def update_oil_ticket_balance(
    user_id: int,
    delta: Decimal,
    connection: Optional[asyncpg.Connection] = None,
) -> Decimal:
    """
    更新用戶的油票餘額。

    Args:
        user_id: 用戶 ID。
        delta: 要增加 (正數) 或減少 (負數) 的油票數量。
        connection: 可選的資料庫連接。

    Returns:
        更新後的新油票餘額。

    Raises:
        ValueError: 如果餘額不足以扣除。
        DatabaseOperationError: 如果資料庫操作失敗。
    """

    async def _logic(conn: asyncpg.Connection) -> Decimal:
        # 鎖定用戶行以防止競爭條件
        current_balance = await user_repo.get_oil_ticket_balance_for_update(
            user_id, connection=conn
        )

        new_balance = current_balance + delta
        if new_balance < 0:
            raise InsufficientOilTicketsError(
                required_amount=-delta, current_balance=current_balance
            )

        await user_repo.update_oil_ticket_balance(user_id, new_balance, connection=conn)
        return new_balance

    # 根據規範，移除 try...except，讓異常自然冒泡
    if connection:
        # 如果傳入了連接，假設它已經在一個事務中
        return await _logic(connection)
    else:
        # 否則，創建一個新的事務
        pool = get_pool()
        async with pool.acquire() as conn:
            async with conn.transaction():
                return await _logic(conn)


async def deduct_oil_tickets(
    user_id: int, amount: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """扣除用戶的油票"""
    if not isinstance(amount, int) or amount <= 0:
        logger.warning(
            "[USER_SERVICE] 嘗試扣除非正數油票 (amount: %s) for user ID: %s",
            amount,
            user_id,
        )
        raise InvalidOperationError(f"扣除的油票數量必須為正整數，收到: {amount}")

    # 根據規範，移除 try...except，讓異常自然冒泡
    await update_oil_ticket_balance(user_id, -Decimal(amount), connection)


async def increment_draws(
    user_id: int, draw_count: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """
    增加用戶的抽卡總數。

    Args:
        user_id: 用戶 ID。
        draw_count: 要增加的抽卡次數。
        connection: 可選的資料庫連接。
    """
    # 根據規範，移除 try...except，讓異常自然冒泡
    await user_repo.increment_draws(user_id, draw_count, connection=connection)


async def increment_oil_ticket_balance(
    user_id: int,
    amount_to_add: Decimal,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """增加用戶的 oil_ticket_balance 小數餘額"""
    await user_repo.increment_oil_ticket_balance(
        user_id, amount_to_add, connection=connection
    )
