# RPG 技能系統概述

本文檔旨在闡明 RPG 系統中不同類型技能的結構、配置和行為，包括主動技能、被動技能和天賦技能。

## 1. 核心概念

### 1.1. Pydantic 模型

所有技能配置都基於 Pydantic 模型進行定義和驗證，確保數據的結構一致性和類型正確性。相關模型主要位於 `rpg_system/config/pydantic_models/` 目錄下。

### 1.2. JSON 配置文件

技能的具體數據存儲在 JSON 文件中，通常位於 `rpg_system/config/data/` 目錄下。系統在運行時會加載這些配置文件。

### 1.3. 效果定義 (`EffectDefinition`)

*   **模型位置**: `rpg_system/config/pydantic_models/base_models.py`
*   **核心作用**: `EffectDefinition` 是所有技能效果的基础。它描述了一個單一效果的行為，例如造成傷害、治療、施加狀態效果等。
*   **模板優先**: 強烈建議優先使用 `effect_templates.json` 中預定義的模板 (`effect_template` 字段)。模板提供了效果的基礎屬性。
*   **參數覆蓋與公式**:
    *   可以在技能配置中覆蓋模板中的參數，例如 `multiplier`, `duration_turns`, `chance`, `value` 等。
    *   這些動態參數應**始終以字符串形式提供**，即使它們代表數值。這允許使用公式字符串，例如 `"0.5 + 0.1 * skill_level"`，以實現隨等級成長的效果。公式中除了可以使用 `skill_level`、角色屬性 (如 `caster_patk`)、戰鬥事件數據 (`event_data`) 外，還可以包含 `custom:variable_name` 形式的自定義變量。這些自定義變量的值需要由遊戲的核心戰鬥邏輯或特定事件觸發器在運行時提供。
    *   系統的 `FormulaEvaluator` 會在运行时解析和计算这些公式。
*   **關鍵字段 (示例)**:
    *   `effect_template` (Optional[str]): 引用 `effect_templates.json` 中的模板ID。
    *   `effect_type` (Optional[str]): 如果不使用模板，則直接指定效果類型 (如 `DAMAGE`, `HEAL`, `APPLY_STATUS_EFFECT`, `STAT_MODIFICATION`)。`effect_template` 和 `effect_type` **不能同時定義**。
    *   `multiplier` (Optional[str]): 效果的倍率 (例如傷害倍率)。
    *   `duration_turns` (Optional[str]): 效果的持續回合數 (例如狀態效果)。
    *   `chance` (Optional[str]): 效果的觸發機率 (0.0 到 1.0)。
    *   `value` (Optional[str]): 效果的基礎值 (例如固定傷害值、治療量)。
    *   `stack_count` (Optional[int]): 效果的疊加層數 (例如某些狀態效果)。
    *   `can_crit` (Optional[bool]): 效果是否可以暴擊。
    *   `status_effect_id` (Optional[str]): 要施加的狀態效果ID (引用 `rpg_system/config/data/status_effects.json` 中定義的狀態)。`status_effects.json` 中定義的狀態本身可以非常複雜，包含在施加時、每回合、結束時或特定事件觸發時應用的效果列表，以及疊加、驅散等特殊邏輯。這使得 `APPLY_STATUS_EFFECT` 類型的效果模板非常強大。
    *   **傷害相關**:
        *   `damage_type` (Optional[Literal["PHYSICAL", "MAGICAL", "TRUE_DAMAGE"]]): 傷害類型。
        *   `base_power_multiplier` (Optional[str]): 基礎威力倍率 (常用於傷害計算)。
        *   `flat_damage_add` (Optional[float]): 固定的額外傷害值。
    *   **治療相關**:
        *   `heal_type` (Optional[Literal["FLAT", "PERCENT_MAX_HP", "PERCENT_CASTER_MATK", "PERCENT_DAMAGE_DEALT"]]): 治療類型。
        *   (`value` 字段也用於治療量)
    *   **屬性修改**:
        *   `modifications` (Optional[List[StatModificationItem]]): 屬性修改列表，用於直接增減角色屬性。
    *   **效果修正器**:
        *   `modifiers` (Optional[List[Modifier]]): 效果修正器列表，用於根據條件動態調整效果結果（如增傷、減傷）。
    *   **效果修正器 (`modifiers` Optional[List[Modifier]])**: 這是 `EffectDefinition` 中實現高級動態效果的關鍵。一個效果可以擁有多個修正器，它們按順序處理。每個修正器 (`Modifier`) 基於其 `modifier_type` 有不同的行為和參數：
        *   **`SCALING_MODIFIER`**: 允許效果的最終數值（如傷害量、治療量、甚至某些參數如持續回合或機率）根據特定來源進行縮放。
            *   `scaling_source`: 定義縮放的依據，例如：
                *   角色屬性: `"stat:patk"`, `"stat:matk"`, `"target_max_hp"`
                *   戰鬥計數器: `"hit_count"` (連擊數)
                *   自定義變量: `"custom:skill_hits_on_target_counter"`, `"custom:hp_percent_diff_caster_vs_target"`
            *   `source_combatant`: 指明 `scaling_source` 的來源是施法者 (`"caster"`) 還是目標 (`"target"`)。
            *   `scaling_type`: 縮放的計算方式，如 `"percentage_add"` (百分比增加), `"flat_add"` (固定值增加), `"flat_damage_add_per_stack"` (每層固定傷害增加)。
            *   `scaling_factor`: 縮放因子。
            *   `max_bonus_value`: 可選，限制縮放帶來的最大收益。
        *   **`CONDITIONAL_BOOST`**: 允許根據一組條件來動態調整效果的某個方面（如傷害倍率、觸發機率）。
            *   `condition_group`: 定義觸發此增益的條件組 (`ModifierConditionGroup`)。
                *   `conditions`: 一個或多個 `ModifierCondition` 列表。
                *   `type`: 條件之間的邏輯關係，可以是 `"AND"` 或 `"OR"`。
                *   每個 `ModifierCondition` 包含:
                    *   `source_combatant`: 條件檢查的主體。
                    *   `check`: 具體的檢查類型，如 `"has_status_effect"`, `"hp_below_percent"`, `"custom:last_action_had_tag"`。
                    *   `value`: 檢查時使用的值。
                    *   `status_effect_id`: 若檢查涉及特定狀態效果。
            *   `bonus_type`: 增益的類型，如 `"percentage_increase"`, `"multiplier_add"`, `"chance_increase"`。
            *   `bonus_value`: 增益的數值。
        *   **`CUSTOM_EFFECT_TRIGGER`**: (此類型在模板中觀察到，允許觸發一個內嵌的、更原子的效果定義)
            *   `triggered_effect`: 包含一個完整的迷你效果定義，例如：
                *   `effect_type`: 如 `"LOSE_MP"`, `"TRANSFER_MP"`。
                *   `value_formula`: 子效果的數值公式，可以引用施法者/目標屬性或使用如 `clamp()` 等函數。
                *   `target_override`, `beneficiary_override`: 用於指定子效果的目標和受益者。
        *   **重要性**: `modifiers` 的設計使得效果模板 (`effect_templates.json`) 極具靈活性。它們可以響應戰鬥中的細微變化，實現複雜的聯動和策略深度。例如，一個技能的傷害可以根據目標身上的debuff數量、施法者的連擊數，或者一個在戰鬥中通過其他途徑設置的 `custom:` 變量來動態變化。
    *   **命中追蹤**:
        *   `counts_for_combo` (Optional[bool]): 是否計入連擊追蹤。
        *   `counts_for_skill_hit_counter` (Optional[bool]): 是否計入技能命中次數追蹤。
    *   (注意: 上述僅為部分常用字段，更完整的定義請參閱 `rpg_system/config/pydantic_models/base_models.py` 中的 `EffectDefinition` 模型。)

### 1.4. 技能等級 (`skill_level`)

大多數技能的效果會隨著 `skill_level` 的提升而增強。這是通過在 `EffectDefinition` 的動態參數中使用包含 `skill_level` 變量的公式字符串來實現的。

### 1.5. 星級 (`star_level`)

星級主要影響角色的基礎屬性成長和某些天賦技能的解鎖或基礎效果。對於主動技能和常規被動技能，其效果成長主要依賴 `skill_level` 而非 `star_level`。

## 2. 技能類型詳解

### 2.1. 主動技能 (Active Skills)

*   **Pydantic 模型**: `ActiveSkillConfig` (定義於 `rpg_system/config/pydantic_models/active_skills.py`)
*   **JSON 配置文件**: `rpg_system/config/data/active_skills.json` (技能ID為JSON對象的鍵)
*   **核心特性**:
    *   由戰鬥中的角色主動選擇並釋放。
    *   消耗 MP (法力值) 並有冷卻時間 (cooldown)。
    *   通常具有直接的、顯著的戰鬥效果。
*   **結構關鍵字段**:
    *   `name` (str): 技能名稱。
    *   `description_template` (str): 技能描述模板。
    *   `skill_rarity` (int): 技能稀有度 (1-7)。
    *   `max_level` (int): 技能最高等級。
    *   `target_type` (str): 目標選擇邏輯的基礎類型 (如 `SINGLE_ENEMY`, `ALL_ALLIES`, `SELF`)。
    *   `base_mp_cost` (int): 基礎 MP 消耗。
    *   `mp_cost_per_level` (float): 每技能等級增加的 MP 消耗 (可以是負數表示減少)。
    *   `base_cooldown_turns` (int): 基礎冷卻回合。
    *   `cooldown_reduction_per_level` (float): 每技能等級減少的冷卻回合。
    *   `base_effect_definitions` (List[EffectDefinition]): **核心效果列表**。一個主動技能可以包含多個效果定義，它們會按順序應用。
    *   `xp_gain_on_sacrifice` (int): 作為祭品時提供的經驗值。
    *   `xp_to_next_level_config` (XpToNextLevelConfig): 技能升級所需經驗值配置。
        *   `base_xp` (int): 升級到下一級所需的基礎經驗。
        *   `multiplier` (float): 每次升級後，基礎經驗的乘數。
    *   `target_logic_details` (Optional[List[TargetLogicDetail]]): 詳細的目標選擇邏輯列表，按 `priority_score` 降序排列。
    *   `tags` (Optional[List[str]]): 技能標籤 (例如 `["attack", "fire"]`)，用於觸發特定邏輯或被動效果。
*   **成長方式**:
    *   MP 消耗和冷卻時間可以隨 `skill_level` 線性變化。
    *   效果的強度、持續時間、機率等通過 `base_effect_definitions` 中每個 `EffectDefinition` 的公式字符串隨 `skill_level` 成長。

### 2.2. 被動技能 (Passive Skills)

*   **Pydantic 模型**: `PassiveSkillConfig` (定義於 `rpg_system/config/pydantic_models/passive_skills.py`)
*   **JSON 配置文件**: `rpg_system/config/data/passive_skills.json` (技能ID為JSON對象的鍵)
*   **核心特性**:
    *   角色習得後自動生效，無需主動釋放。
    *   通常在滿足特定條件時觸發效果。
*   **結構關鍵字段**:
    *   `name` (str): 技能名稱。
    *   `description_template` (str): 技能描述模板。
    *   `skill_rarity` (int): 技能稀有度 (1-7)。
    *   `max_level` (int): 技能最高等級。
    *   `base_effects` (List[PassiveEffectBlock]): **核心效果塊列表**。每個效果塊包含獨立的觸發條件和效果定義。
        *   `PassiveEffectBlock` 包含:
            *   `trigger_condition` (TriggerCondition): 該效果塊的觸發條件。模型位於 `rpg_system/config/pydantic_models/base_models.py`。
                *   `type` (str): 主要觸發事件類型 (如 `ON_DEAL_DAMAGE`, `ON_TAKE_DAMAGE`, `ON_TURN_START`, `ON_HP_BELOW_THRESHOLD`)。
                *   `sub_type` (Optional[str]): 可選的子類型。
                *   `chance_formula` (Optional[str]): 觸發機率的公式字符串。默認為 `"1.0"`。
                *   `trigger_once_per_battle` (Optional[bool]): 是否每場戰鬥只觸發一次。
                *   `additional_conditions` (Optional[List[AdditionalCondition]]): 更複雜的額外條件列表。每個 `AdditionalCondition` 可以指定 `source_combatant`, `check`, `value`，並且**特別重要的是，可以包含一個 `formula` 字段**。這個 `formula` 字段允許直接編寫布爾表達式作為條件，例如檢查戰鬥事件的數據 (`event_data.damage_type == 'PHYSICAL'`) 或 `custom:` 變量，極大地增強了條件判斷的複雜度和精確度。
            *   `target_override` (Optional[TargetOverride]): 可選的目標覆蓋邏輯。