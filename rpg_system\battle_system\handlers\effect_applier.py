"""
效果應用器 (EffectApplier)
RPG系統的效果引擎核心，負責處理所有類型的效果應用
"""

from typing import Any, Dict, List, Optional

from utils.logger import logger

from ...config.loader import get_config_loader
from ...formula_engine.evaluator import evaluate_formula
from ..models.battle import Battle
from ..models.combatant import Combatant
from ..models.skill_instance import SkillInstance
from . import damage_handler, target_selector


async def apply_skill_effects(
    caster: "Combatant",
    targets: List["Combatant"],
    skill_instance: "SkillInstance",
    battle_context: "Battle",
) -> List[Dict[str, Any]]:
    """
    應用技能效果
    """
    try:
        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        skill_definition = await skill_instance.get_definition(all_configs)

        if not skill_definition:
            logger.warning("無法找到技能定義: %s", skill_instance.skill_id)
            return []

        caster.last_action_tags = skill_definition.get("tags", [])
        current_level = str(skill_instance.current_level)

        if "effects_by_level" in skill_definition:
            level_effects = skill_definition.get("effects_by_level", {}).get(
                current_level, {}
            )
            effect_definitions = level_effects.get("effect_definitions", [])
        elif "effect_definitions" in skill_definition:
            effect_definitions = skill_definition.get("effect_definitions", [])
        else:
            effect_definitions = []

        if not effect_definitions:
            return []

        is_combo_attack = False
        for effect_def_check in effect_definitions:
            processed_effect_check = await _process_effect_template(effect_def_check)
            if processed_effect_check.get(
                "type"
            ) == "DEAL_DAMAGE" and processed_effect_check.get(
                "counts_for_combo", False
            ):
                is_combo_attack = True
                break

        if not is_combo_attack:
            caster.consecutive_hits = 0

        return await apply_effect_definitions(
            caster=caster,
            initial_targets=targets,
            effect_definitions=effect_definitions,
            battle_context=battle_context,
            source_skill_tags=caster.last_action_tags,
            source_skill_instance=skill_instance,
        )

    except Exception as e:
        logger.error("應用技能效果錯誤: %s", e)
        return []


async def apply_effect_definitions(
    caster: "Combatant",
    initial_targets: List["Combatant"],
    effect_definitions: List[Dict[str, Any]],
    battle_context: "Battle",
    source_skill_tags: Optional[List[str]] = None,
    source_skill_instance: Optional["SkillInstance"] = None,
    custom_vars_from_source: Optional[Dict[str, Any]] = None,
) -> List[Dict[str, Any]]:
    """
    應用效果定義列表
    """
    results = []
    source_skill_tags = source_skill_tags or []
    custom_vars_from_source = custom_vars_from_source or {}

    for effect_def in effect_definitions:
        try:
            processed_effect = await _process_effect_template(effect_def)
            actual_targets = await _determine_effect_targets(
                caster, initial_targets, processed_effect, battle_context
            )

            for target in actual_targets:
                effect_result = await _apply_single_effect(
                    caster=caster,
                    target=target,
                    effect_def=processed_effect,
                    battle_context=battle_context,
                    source_skill_tags=source_skill_tags,
                    source_skill_instance=source_skill_instance,
                    custom_vars=custom_vars_from_source,
                )
                if effect_result:
                    results.append(effect_result)
        except Exception as e:
            logger.error("應用效果定義錯誤: %s", e)
            continue
    return results


async def _process_effect_template(effect_def: Dict[str, Any]) -> Dict[str, Any]:
    """
    處理效果模板
    """
    effect_template = effect_def.get("effect_template")
    if effect_template:
        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        effect_templates = all_configs.get("effect_templates", {})
        template_def = effect_templates.get(effect_template, {})
        processed_effect = template_def.copy()
        processed_effect.update(
            {k: v for k, v in effect_def.items() if k != "effect_template"}
        )
        return processed_effect
    return effect_def.copy()


async def _determine_effect_targets(
    caster: "Combatant",
    initial_targets: List["Combatant"],
    effect_def: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    確定效果的最終目標
    """
    target_override = effect_def.get("target_override")
    if target_override:
        return await target_selector.select_targets(
            caster,
            target_override,
            battle_context,
        )
    return initial_targets


async def _apply_single_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    battle_context: "Battle",
    source_skill_tags: List[str],
    source_skill_instance: Optional["SkillInstance"] = None,
    custom_vars: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    應用單個效果到單個目標
    """
    if not target.is_alive():
        return None

    try:
        context_vars = _prepare_effect_context(
            caster, target, battle_context, source_skill_instance, custom_vars
        )

        if not await _check_effect_conditions(effect_def, context_vars):
            return None

        effect_application_result: Optional[Dict[str, Any]] = None
        effect_type = effect_def.get("effect_type")

        if effect_type == "DAMAGE":
            effect_application_result = await _apply_damage_effect(
                caster,
                target,
                effect_def,
                context_vars,
                battle_context,
                source_skill_tags,
            )
        elif effect_type == "HEAL":
            effect_application_result = await _apply_heal_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "APPLY_STATUS_EFFECT":
            effect_application_result = await _apply_status_effect_application(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "STAT_MODIFICATION":
            effect_application_result = await _apply_stat_modification_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "APPLY_SHIELD":
            effect_application_result = await _apply_shield_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "SHIELD_REMOVAL":
            effect_application_result = await _apply_shield_removal_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "DISPEL_DEBUFF":
            effect_application_result = await _apply_dispel_debuff_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "LOSE_MP":
            effect_application_result = await _apply_lose_mp_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        elif effect_type == "TRANSFER_MP":
            effect_application_result = await _apply_transfer_mp_effect(
                caster, target, effect_def, context_vars, battle_context
            )
        else:
            logger.warning("未知的效果類型: %s", effect_type)
            return None

        if (
            effect_application_result
            and effect_application_result.get("success", True)
            and source_skill_instance
            and effect_def.get("counts_for_skill_hit_counter", False)
        ):
            skill_id = source_skill_instance.skill_id
            target_id = target.instance_id
            if target_id not in caster.skill_target_hit_counters:
                caster.skill_target_hit_counters[target_id] = {}
            if skill_id not in caster.skill_target_hit_counters[target_id]:
                caster.skill_target_hit_counters[target_id][skill_id] = 0
            caster.skill_target_hit_counters[target_id][skill_id] += 1

        return effect_application_result

    except Exception as e:
        logger.error("應用單個效果錯誤: %s", e)
        return None


def _prepare_effect_context(
    caster: "Combatant",
    target: "Combatant",
    battle_context: "Battle",
    source_skill_instance: Optional["SkillInstance"] = None,
    custom_vars: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    準備效果應用的上下文變量
    """
    context_vars = {}
    for stat_name, value in caster.current_stats.items():
        context_vars[f"caster_stat_{stat_name}"] = value
    context_vars.update(
        {
            "caster_id": caster.instance_id,
            "caster_name": caster.name,
            "target_id": target.instance_id,
            "target_name": target.name,
            "caster_max_hp": caster.max_hp,
            "caster_current_hp": caster.current_hp,
            "caster_hp_percent": (caster.current_hp / caster.max_hp)
            if caster.max_hp > 0
            else 0,
            "caster_max_mp": caster.max_mp,
            "caster_current_mp": caster.current_mp,
            "caster_mp_percent": (caster.current_mp / caster.max_mp)
            if caster.max_mp > 0
            else 0,
            "caster_level": caster.rpg_level,
            "caster_star_level": caster.star_level,
            "target_max_hp": target.max_hp,
            "target_current_hp": target.current_hp,
            "target_hp_percent": (target.current_hp / target.max_hp)
            if target.max_hp > 0
            else 0,
            "target_max_mp": target.max_mp,
            "target_current_mp": target.current_mp,
            "target_mp_percent": (target.current_mp / target.max_mp)
            if target.max_mp > 0
            else 0,
            "target_level": target.rpg_level,
            "target_star_level": target.star_level,
            "current_turn": battle_context.current_turn,
        }
    )
    for stat_name, value in target.current_stats.items():
        context_vars[f"target_stat_{stat_name}"] = value

    if source_skill_instance:
        context_vars["skill_id"] = source_skill_instance.skill_id
        context_vars["skill_level"] = source_skill_instance.current_level

    context_vars.update(
        {
            "custom_caster_was_attacked_last_turn": int(caster.was_attacked_last_turn),
            "custom_target_was_attacked_last_turn": int(target.was_attacked_last_turn),
            "custom_caster_last_action_tags": caster.last_action_tags,
            "custom_target_last_action_tags": target.last_action_tags,
            "custom_caster_consecutive_hits": caster.consecutive_hits,
            "custom_target_consecutive_hits": target.consecutive_hits,
            "custom_hp_percent_diff_caster_vs_target": context_vars["caster_hp_percent"]
            - context_vars["target_hp_percent"],
        }
    )

    current_skill_id_for_counter = context_vars.get("skill_id")
    if current_skill_id_for_counter and hasattr(caster, "skill_target_hit_counters"):
        context_vars["custom_skill_hits_on_target_counter"] = (
            caster.skill_target_hit_counters.get(target.instance_id, {}).get(
                current_skill_id_for_counter, 0
            )
        )
    else:
        context_vars["custom_skill_hits_on_target_counter"] = 0

    if custom_vars:
        context_vars.update(custom_vars)

    return context_vars


async def _check_effect_conditions(
    effect_def: Dict[str, Any], context_vars: Dict[str, Any]
) -> bool:
    """
    檢查效果應用條件
    """
    conditions = effect_def.get("conditions_to_apply", [])
    if not conditions:
        return True
    for condition in conditions:
        condition_formula = condition.get("formula", "1")
        if not await evaluate_formula(condition_formula, context_vars):
            return False
    return True


async def _apply_damage_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
    source_skill_tags: List[str],
) -> Dict[str, Any]:
    """
    應用傷害效果
    """
    try:
        base_damage = await _calculate_base_damage(effect_def, context_vars)
        damage_result = await damage_handler.calculate_and_apply_damage(
            caster=caster,
            target=target,
            damage_effect=effect_def,
            base_damage_value=base_damage,
            battle_context=battle_context,
            config_loader=get_config_loader(),
            skill_tags=source_skill_tags,
        )
        await _trigger_damage_events(
            caster, target, damage_result, effect_def, battle_context
        )
        return {
            "effect_type": "DAMAGE",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": True,
            **damage_result,
        }
    except Exception as e:
        logger.error("應用傷害效果錯誤: %s", e)
        return {
            "effect_type": "DAMAGE",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": False,
            "error": str(e),
        }


async def _calculate_base_damage(
    effect_def: Dict[str, Any], context_vars: Dict[str, Any]
) -> float:
    """
    計算基礎傷害值
    """
    value_formula = effect_def.get("value_formula")
    if value_formula:
        return max(0, await evaluate_formula(value_formula, context_vars))
    base_power_multiplier = 1.0
    multiplier = effect_def.get("multiplier")
    if multiplier is not None:
        base_power_multiplier = await evaluate_formula(str(multiplier), context_vars)
    flat_damage_add = effect_def.get("flat_damage_add", 0.0)
    damage_type = effect_def.get("damage_type", "PHYSICAL")
    if damage_type == "PHYSICAL":
        base_attack = context_vars.get("caster_stat_patk", 0)
    elif damage_type == "MAGICAL":
        base_attack = context_vars.get("caster_stat_matk", 0)
    else:
        base_attack = 0
    base_damage = (base_attack * base_power_multiplier) + flat_damage_add
    return max(0, base_damage)


async def _apply_heal_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用治療效果
    """
    try:
        heal_amount = await _calculate_heal_amount(effect_def, context_vars)
        actual_heal = await target.heal(heal_amount, False, battle_context)
        await _trigger_heal_events(
            caster, target, heal_amount, actual_heal, battle_context
        )
        return {
            "effect_type": "HEAL",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": True,
            "heal_amount": heal_amount,
            "actual_heal": actual_heal,
        }
    except Exception as e:
        logger.error("應用治療效果錯誤: %s", e)
        return {
            "effect_type": "HEAL",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": False,
            "error": str(e),
        }


async def _calculate_heal_amount(
    effect_def: Dict[str, Any], context_vars: Dict[str, Any]
) -> float:
    """
    計算治療量
    """
    value_formula = effect_def.get("value_formula")
    if value_formula:
        return await evaluate_formula(value_formula, context_vars)
    heal_type = effect_def.get("heal_type", "FLAT")
    value = effect_def.get("value", 0.0)
    if heal_type == "FLAT":
        heal_amount = value
    elif heal_type == "PERCENT_MAX_HP":
        max_hp = context_vars.get("target_stat_max_hp", 1)
        heal_amount = max_hp * value
    elif heal_type == "PERCENT_CASTER_MATK":
        caster_matk = context_vars.get("caster_stat_matk", 0)
        heal_amount = caster_matk * value
    else:
        heal_amount = value
    return max(0, heal_amount)


async def _apply_status_effect_application(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用狀態效果施加
    """
    try:
        status_effect_id = effect_def.get("status_effect_id")
        if not status_effect_id:
            return {
                "effect_type": "APPLY_STATUS_EFFECT",
                "error": "Missing status_effect_id",
            }
        chance = effect_def.get("chance", "1.0")
        chance = await evaluate_formula(str(chance), context_vars)
        chance = max(0.0, min(1.0, chance))
        if hasattr(battle_context, "_rng"):
            roll = battle_context._rng.random()
        else:
            import random

            roll = random.random()
        if roll > chance:
            return {
                "effect_type": "APPLY_STATUS_EFFECT",
                "caster_id": caster.instance_id,
                "target_id": target.instance_id,
                "failed": True,
                "reason": "Failed probability check",
            }
        from ..models.status_effect_instance import StatusEffectInstance

        duration_turns = effect_def.get("duration_turns", "3")
        duration_turns = int(await evaluate_formula(str(duration_turns), context_vars))
        duration_turns = max(1, duration_turns)
        stack_count = effect_def.get("stack_count", 1)
        value_overrides = {}
        overrides_def = effect_def.get("value_overrides", {})
        for stat_name, formula in overrides_def.items():
            evaluated_value = await evaluate_formula(str(formula), context_vars)
            value_overrides[stat_name] = str(evaluated_value)
        status_instance = StatusEffectInstance(
            status_effect_id=status_effect_id,
            caster_id=caster.instance_id,
            duration_turns=duration_turns,
            stack_count=stack_count,
            value_overrides=value_overrides,
        )
        await target.add_status_effect(status_instance, battle_context)
        await _trigger_status_effect_applied_event(
            caster,
            target,
            status_effect_id,
            duration_turns,
            stack_count,
            battle_context,
        )
        return {
            "effect_type": "APPLY_STATUS_EFFECT",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": True,
            "status_effect_id": status_effect_id,
            "duration_turns": duration_turns,
            "stack_count": stack_count,
        }
    except Exception as e:
        logger.error("應用狀態效果錯誤: %s", e)
        return {
            "effect_type": "APPLY_STATUS_EFFECT",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": False,
            "error": str(e),
        }


async def _apply_stat_modification_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用屬性修改效果
    """
    try:
        modifications = effect_def.get("modifications", [])
        if not modifications:
            return {
                "effect_type": "STAT_MODIFICATION",
                "success": False,
                "error": "No modifications specified",
            }
        applied_modifications = []
        for modification in modifications:
            stat_name = modification.get("stat_name")
            modification_type = modification.get("modification_type")
            value_formula = modification.get("value_formula", "0")
            if not stat_name or not modification_type:
                continue
            modification_value = await evaluate_formula(value_formula, context_vars)
            current_value = target.current_stats.get(stat_name, 0)
            if modification_type == "FLAT_ADD":
                new_value = current_value + modification_value
            elif modification_type == "PERCENTAGE_ADD":
                new_value = current_value * (1 + modification_value)
            else:
                new_value = current_value
            target.current_stats[stat_name] = max(0, new_value)
            applied_modifications.append(
                {
                    "stat_name": stat_name,
                    "modification_type": modification_type,
                    "value": modification_value,
                    "old_value": current_value,
                    "new_value": target.current_stats[stat_name],
                }
            )
        return {
            "effect_type": "STAT_MODIFICATION",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": True,
            "modifications": applied_modifications,
        }
    except Exception as e:
        logger.error("應用屬性修改效果錯誤: %s", e)
        return {
            "effect_type": "STAT_MODIFICATION",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "success": False,
            "error": str(e),
        }


async def _trigger_damage_events(
    caster: "Combatant",
    target: "Combatant",
    damage_result: Dict[str, Any],
    effect_def: Dict[str, Any],
    battle_context: "Battle",
) -> None:
    """
    觸發傷害相關事件
    """
    try:
        if damage_result.get("actual_damage_dealt", 0) <= 0:
            return
        damage_amount = damage_result.get("actual_damage_dealt", 0)
        damage_type = effect_def.get("damage_type", "PHYSICAL")
        is_crit = damage_result.get("was_crit", False)
        is_fatal = target.current_hp <= 0
        damage_dealt_event_data = {
            "source_attacker_id": caster.instance_id,
            "target_id": target.instance_id,
            "damage_amount": damage_amount,
            "damage_type": damage_type,
            "is_crit": is_crit,
            "is_fatal_to_target": is_fatal,
            "is_primary_attack_damage": False,
            "is_skill_damage": True,
        }
        damage_taken_event_data = {
            "target_id": target.instance_id,
            "source_attacker_id": caster.instance_id,
            "damage_amount": damage_amount,
            "damage_type": damage_type,
            "is_crit": is_crit,
            "was_fatal": is_fatal,
        }
        if hasattr(battle_context, "trigger_event"):
            await battle_context.trigger_event(
                "ON_DAMAGE_DEALT", damage_dealt_event_data
            )
            await battle_context.trigger_event(
                "ON_DAMAGE_TAKEN", damage_taken_event_data
            )
    except Exception as e:
        logger.error("觸發傷害事件錯誤: %s", e)


async def _trigger_heal_events(
    caster: "Combatant",
    target: "Combatant",
    heal_amount: float,
    actual_heal: float,
    battle_context: "Battle",
) -> None:
    """
    觸發治療相關事件
    """
    try:
        if actual_heal <= 0:
            return
        heal_dealt_event_data = {
            "source_healer_id": caster.instance_id,
            "target_id": target.instance_id,
            "heal_amount": actual_heal,
            "is_crit_heal": False,
        }
        heal_received_event_data = {
            "target_id": target.instance_id,
            "source_healer_id": caster.instance_id,
            "heal_amount": actual_heal,
            "is_crit_heal": False,
        }
        if hasattr(battle_context, "trigger_event"):
            await battle_context.trigger_event("ON_HEAL_DEALT", heal_dealt_event_data)
            await battle_context.trigger_event(
                "ON_HEAL_RECEIVED", heal_received_event_data
            )
    except Exception as e:
        logger.error("觸發治療事件錯誤: %s", e)


async def _trigger_status_effect_applied_event(
    caster: "Combatant",
    target: "Combatant",
    status_effect_id: str,
    duration_turns: int,
    stack_count: int,
    battle_context: "Battle",
) -> None:
    """
    觸發狀態效果施加事件
    """
    try:
        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        status_effects = all_configs.get("status_effects", {})
        status_config = status_effects.get(status_effect_id, {})
        is_buff = status_config.get("is_buff", False)
        status_applied_event_data = {
            "target_id": target.instance_id,
            "source_caster_id": caster.instance_id,
            "status_effect_id": status_effect_id,
            "is_buff": is_buff,
            "duration_turns": duration_turns,
            "stack_count": stack_count,
        }
        if hasattr(battle_context, "trigger_event"):
            await battle_context.trigger_event(
                "ON_STATUS_EFFECT_APPLIED", status_applied_event_data
            )
    except Exception as e:
        logger.error("觸發狀態效果施加事件錯誤: %s", e)


async def _apply_shield_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用護盾效果
    """
    try:
        shield_value = 0.0
        shield_value_formula = effect_def.get("shield_value_formula")
        if shield_value_formula:
            shield_value = await evaluate_formula(shield_value_formula, context_vars)
        else:
            shield_value = effect_def.get("shield_value", 50.0)
        shield_value = max(0, shield_value)
        duration_turns = effect_def.get("duration_turns", "3")
        duration_turns = int(await evaluate_formula(str(duration_turns), context_vars))
        duration_turns = max(1, duration_turns)
        chance = effect_def.get("chance", "1.0")
        chance = await evaluate_formula(str(chance), context_vars)
        chance = max(0.0, min(1.0, chance))
        if hasattr(battle_context, "_rng"):
            roll = battle_context._rng.random()
        else:
            import random

            roll = random.random()
        if roll > chance:
            return {
                "effect_type": "APPLY_SHIELD",
                "caster_id": caster.instance_id,
                "target_id": target.instance_id,
                "failed": True,
                "reason": "Failed probability check",
            }
        from ..models.shield_instance import ShieldInstance

        shield_type = effect_def.get("shield_type", "ABSORB_DAMAGE")
        shield_instance = ShieldInstance(
            shield_type=shield_type,
            caster_id=caster.instance_id,
            target_id=target.instance_id,
            current_value=shield_value,
            max_value=shield_value,
            duration_turns=duration_turns,
        )
        await target.add_shield(shield_instance, battle_context)
        return {
            "effect_type": "APPLY_SHIELD",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "shield_type": shield_type,
            "shield_value": shield_value,
            "duration_turns": duration_turns,
            "success": True,
        }
    except Exception as e:
        logger.error("應用護盾效果錯誤: %s", e)
        return {
            "effect_type": "APPLY_SHIELD",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "error": str(e),
        }


async def _apply_shield_removal_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用護盾移除效果
    """
    try:
        target_filter = effect_def.get("target_filter", "shield_effects")
        removed_count = await target.remove_shields(target_filter, battle_context)
        return {
            "effect_type": "SHIELD_REMOVAL",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "removed_count": removed_count,
            "success": True,
        }
    except Exception as e:
        logger.error("應用護盾移除效果錯誤: %s", e)
        return {
            "effect_type": "SHIELD_REMOVAL",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "error": str(e),
        }


async def _apply_dispel_debuff_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用驅散負面效果
    """
    try:
        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        status_effects = all_configs.get("status_effects", {})
        removed_effects = []
        effects_to_remove = []
        for effect in target.status_effects:
            status_config = status_effects.get(effect.status_effect_id, {})
            is_buff = status_config.get("is_buff", False)
            special_flags = status_config.get("special_flags", [])
            if (
                not is_buff
                and "DISPELLABLE" in special_flags
                and "UNDISPELLABLE" not in special_flags
            ):
                effects_to_remove.append(effect)
                removed_effects.append(effect.status_effect_id)
        for effect in effects_to_remove:
            target.status_effects.remove(effect)
            if battle_context and hasattr(battle_context, "trigger_event"):
                await battle_context.trigger_event(
                    "ON_STATUS_EFFECT_DISPELLED",
                    {
                        "target_id": target.instance_id,
                        "caster_id": caster.instance_id,
                        "status_effect_id": effect.status_effect_id,
                    },
                )
        return {
            "effect_type": "DISPEL_DEBUFF",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "removed_effects": removed_effects,
            "removed_count": len(removed_effects),
            "success": True,
        }
    except Exception as e:
        logger.error("應用驅散負面效果錯誤: %s", e)
        return {
            "effect_type": "DISPEL_DEBUFF",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "error": str(e),
        }


def _handle_mp_change(
    target: "Combatant", mp_change: float, is_loss: bool = True
) -> Dict[str, Any]:
    """
    統一處理MP變化（消耗或恢復）
    """
    old_mp = target.current_mp
    if is_loss:
        target.current_mp = max(0, target.current_mp - int(mp_change))
        actual_change = old_mp - target.current_mp
        effect_type = "LOSE_MP"
    else:
        target.current_mp = min(target.max_mp, target.current_mp + int(mp_change))
        actual_change = target.current_mp - old_mp
        effect_type = "GAIN_MP"
    return {
        "effect_type": effect_type,
        "target_id": target.instance_id,
        "mp_changed": actual_change,
        "success": True,
    }


async def _apply_lose_mp_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用MP消耗效果
    """
    try:
        value_formula = effect_def.get("value_formula")
        if value_formula:
            mp_loss = await evaluate_formula(value_formula, context_vars)
        else:
            mp_loss = effect_def.get("value", 20.0)
        mp_loss = max(0, mp_loss)
        result = _handle_mp_change(target, mp_loss, is_loss=True)
        result["caster_id"] = caster.instance_id
        return result
    except Exception as e:
        logger.error("應用MP消耗效果錯誤: %s", e)
        return {
            "effect_type": "LOSE_MP",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "error": str(e),
        }


async def _apply_transfer_mp_effect(
    caster: "Combatant",
    target: "Combatant",
    effect_def: Dict[str, Any],
    context_vars: Dict[str, Any],
    battle_context: "Battle",
) -> Dict[str, Any]:
    """
    應用MP轉移效果
    """
    try:
        transfer_amount = 0.0
        value_formula = effect_def.get("value_formula")
        if value_formula:
            transfer_amount = await evaluate_formula(value_formula, context_vars)
        else:
            transfer_amount = effect_def.get("value", 10.0)
        max_transfer = effect_def.get("max_transfer", float("inf"))
        transfer_amount = min(transfer_amount, max_transfer)
        transfer_amount = max(0, transfer_amount)
        actual_transfer = min(transfer_amount, target.current_mp)
        target.current_mp = max(0, target.current_mp - int(actual_transfer))
        caster.current_mp = min(caster.max_mp, caster.current_mp + int(actual_transfer))
        return {
            "effect_type": "TRANSFER_MP",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "mp_transferred": actual_transfer,
            "success": True,
        }
    except Exception as e:
        logger.error("應用MP轉移效果錯誤: %s", e)
        return {
            "effect_type": "TRANSFER_MP",
            "caster_id": caster.instance_id,
            "target_id": target.instance_id,
            "error": str(e),
        }
