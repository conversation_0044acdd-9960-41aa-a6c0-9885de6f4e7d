"""
系列緩存服務模組

提供高效的系列列表緩存機制，避免重複的數據庫查詢。
支持按 pool_type 過濾的系列緩存。

優化特性：
- ✅ 模組級全局緩存，啟動時預熱
- ✅ 支持按 pool_type 過濾
- ✅ 統一的緩存接口
- ✅ 手動刷新緩存功能
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional

from gacha.repositories._base_repo import fetch_all
from utils.logger import logger

# --- 模組級別的全局系列緩存 ---

# 系列緩存結構：Dict[Optional[str], List[str]]
# Key: pool_type (None 表示所有系列)
# Value: 系列名稱列表
_series_cache: Dict[Optional[str], List[str]] = {}


async def get_series_cache(pool_type: Optional[str] = None) -> List[str]:
    """
    獲取系列列表緩存

    Args:
        pool_type: 卡池類型過濾，None 表示獲取所有系列

    Returns:
        系列名稱列表
    """
    # 如果緩存為空，先初始化
    if not _series_cache:
        await _populate_series_cache()

    # 如果請求的 pool_type 不在緩存中，嘗試獲取
    if pool_type not in _series_cache:
        await _fetch_series_for_pool_type(pool_type)

    return _series_cache.get(pool_type, [])


async def refresh_series_cache() -> None:
    """
    手動刷新系列緩存
    清空現有緩存並重新從數據庫獲取
    """
    global _series_cache
    _series_cache.clear()
    await _populate_series_cache()
    logger.info("[SERIES_CACHE] 系列緩存已手動刷新")


async def _populate_series_cache() -> None:
    """
    從數據庫獲取所有系列並構建緩存
    預先獲取常用的 pool_type 組合
    """
    global _series_cache
    logger.info("[SERIES_CACHE] 開始構建系列緩存...")

    try:
        # 1. 獲取所有系列（不過濾 pool_type）
        await _fetch_series_for_pool_type(None)

        # 2. 預熱常用的 pool_type 緩存
        # 這裡可以根據實際使用情況添加常用的 pool_type
        common_pool_types = [
            "主卡池",
            "Hololive",
            "典藏",
            "泳裝",
            "情人節",
            "典藏女僕",
            "Ongeki",
            "UNION ARENA",
            "Pokemon TCG",
            "WIXOSS",
            "Shadowverse Evolve",
        ]

        for pool_type in common_pool_types:
            await _fetch_series_for_pool_type(pool_type)

        logger.info(
            "[SERIES_CACHE] 系列緩存構建完成，已緩存 %d 個 pool_type",
            len(_series_cache),
        )

    except Exception as e:
        logger.error("[SERIES_CACHE] 構建系列緩存失敗: %s", str(e), exc_info=True)
        # 即使失敗也要確保緩存字典存在
        if not _series_cache:
            _series_cache = {}


async def _fetch_series_for_pool_type(pool_type: Optional[str]) -> None:
    """
    為特定 pool_type 獲取系列列表並緩存

    Args:
        pool_type: 卡池類型，None 表示所有系列
    """
    try:
        params = []
        pool_type_condition = ""

        if pool_type:
            pool_type_condition = "WHERE pool_type = $1"
            params.append(pool_type)

        query = f"""
            SELECT DISTINCT series 
            FROM gacha_master_cards 
            {pool_type_condition} 
            ORDER BY series
        """

        results = await fetch_all(query, params)
        series_list = [result["series"] for result in results] if results else []

        # 緩存結果
        _series_cache[pool_type] = series_list

        logger.debug(
            "[SERIES_CACHE] 已緩存 pool_type='%s' 的 %d 個系列",
            pool_type or "ALL",
            len(series_list),
        )

    except Exception as e:
        logger.error(
            "[SERIES_CACHE] 獲取 pool_type='%s' 系列失敗: %s",
            pool_type or "ALL",
            str(e),
            exc_info=True,
        )
        # 失敗時設置空列表，避免重複查詢
        _series_cache[pool_type] = []


def get_cache_stats() -> Dict[str, Any]:
    """
    獲取緩存統計信息

    Returns:
        緩存統計字典
    """
    total_series = sum(len(series_list) for series_list in _series_cache.values())

    return {
        "cached_pool_types": len(_series_cache),
        "total_cached_series": total_series,
        "cache_keys": list(_series_cache.keys()),
        "cache_sizes": {
            str(pool_type or "ALL"): len(series_list)
            for pool_type, series_list in _series_cache.items()
        },
    }


def is_cache_initialized() -> bool:
    """
    檢查緩存是否已初始化

    Returns:
        True 如果緩存已初始化
    """
    return bool(_series_cache)
