# Pioneer System 經濟配置
# 核心經濟參數 - 系統的命脈

# 注意：已移除信用點系統，統一使用油幣作為唯一貨幣

# 基礎產出控制 (刻意設計得較低)
base_production:
  manual_gathering_efficiency: 0.5  # 手動採集效率係數
  shop_npc_purchase_rate: 0.3       # NPC購買頻率係數
  base_sell_prices: 0.8             # 基礎銷售價格係數
  facility_base_efficiency: 1.0     # 設施基礎效率
  skill_xp_multiplier: 1.0          # 技能經驗倍率

# 離線計算限制
offline_limits:
  max_offline_hours: 168           # 最大離線時間 (7天)
  offline_efficiency_penalty: 0.8  # 離線效率懲罰係數
  max_offline_cycles: 1000         # 最大離線處理輪次

# 能量系統
energy_system:
  max_energy: 100                  # 最大能量
  energy_regen_minutes: 5          # 每5分鐘恢復1點能量
  energy_regen_amount: 1           # 每次恢復的能量點數

# 技能系統
skill_system:
  base_xp_per_level: 100          # 每級所需基礎經驗 (level * base_xp_per_level)
  max_skill_level: 99             # 最大技能等級
  skill_bonus_per_level: 0.01     # 每級技能提供的加成 (1%)

# 設施系統
facility_system:
  max_facilities_per_user: 50     # 每用戶最大設施數量
  facility_slot_base_capacity: 100 # 設施槽位基礎容量
  production_time_variance: 0.1   # 生產時間變動範圍 (±10%)

# 倉庫系統
warehouse_system:
  base_capacity: 1000              # 基礎倉庫容量
  facility_base_capacity: 1000     # 每個倉庫設施的基礎容量
  level_multiplier: 1.5            # 每級容量倍率
  upgrade_multiplier: 1.5          # 每次升級容量倍率

# 研究系統
research_system:
  max_research_level: null        # 最大研究等級 (null = 無限制)
  research_effect_cap: 10.0       # 研究效果上限 (1000%)
  research_cost_base: 1000000     # 研究基礎成本

# 經濟平衡參數
balance_parameters:
  inflation_control: true         # 是否啟用通脹控制
  price_adjustment_factor: 1.0    # 價格調整係數
  supply_demand_sensitivity: 0.1  # 供需敏感度
