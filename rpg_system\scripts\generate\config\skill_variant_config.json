{"variant_multipliers": {"large_effect": {"description": "大效果變體：高倍率 + 長冷卻 + 高MP消耗", "effect_multiplier": 1.8, "mp_cost_multiplier": 1.6, "cooldown_multiplier": 1.5, "duration_multiplier": 1.3}, "small_effect": {"description": "小效果變體：低倍率 + 短冷卻 + 低MP消耗", "effect_multiplier": 0.6, "mp_cost_multiplier": 0.7, "cooldown_multiplier": 0.6, "duration_multiplier": 0.8}, "medium_effect": {"description": "中間變體：平衡的數值", "effect_multiplier": 1.0, "mp_cost_multiplier": 1.0, "cooldown_multiplier": 1.0, "duration_multiplier": 1.0}}, "combination_variants": {"1": {"description": "稀有度1：基礎技能，無組合變體", "combination_chance": 0.0, "max_additional_effects": 0, "available_secondary_effects": []}, "2": {"description": "稀有度2：低機率組合變體", "combination_chance": 0.15, "max_additional_effects": 1, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_WEAK_LIFESTEAL"]}, "3": {"description": "稀有度3：中等機率組合變體", "combination_chance": 0.25, "max_additional_effects": 1, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_WEAK_LIFESTEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_POISON_STATUS", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF"]}, "4": {"description": "稀有度4：較高機率組合變體", "combination_chance": 0.35, "max_additional_effects": 1, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_CRIT_BOOST", "APPLY_WEAK_LIFESTEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "APPLY_STUN_STATUS", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF"]}, "5": {"description": "稀有度5：高機率組合變體", "combination_chance": 0.5, "max_additional_effects": 2, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_WEAK_LIFESTEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF"]}, "6": {"description": "稀有度6：很高機率組合變體", "combination_chance": 0.65, "max_additional_effects": 2, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_WEAK_LIFESTEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_MAGIC_SHIELD", "APPLY_PHYSICAL_BARRIER", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "APPLY_ALL_STATS_DEBUFF"]}, "7": {"description": "稀有度7：極高機率組合變體", "combination_chance": 0.8, "max_additional_effects": 3, "available_secondary_effects": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_WEAK_LIFESTEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_MAGIC_SHIELD", "APPLY_PHYSICAL_BARRIER", "APPLY_SCALING_SHIELD", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "APPLY_EVASION_DEBUFF", "APPLY_ALL_STATS_DEBUFF", "DISPEL_ALL_DEBUFFS", "SHIELD_REMOVAL", "TRANSFER_MP_EFFECT", "LOSE_MP_EFFECT"]}}, "effect_category_mapping": {"damage_templates": ["BASIC_DAMAGE", "TRUE_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "EXECUTE_DAMAGE", "CONDITIONAL_DAMAGE_BOOST", "PERCENTAGE_HP_DAMAGE", "RESOURCE_ADVANTAGE_BLAST", "CRITICAL_MASS_EXPLOSION"], "heal_templates": ["BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "BASIC_HEAL_PERCENT_MAX_HP", "MISSING_HP_HEAL"], "buff_templates": ["APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_MAGIC_SHIELD", "APPLY_PHYSICAL_BARRIER", "APPLY_SCALING_SHIELD"], "debuff_templates": ["APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "APPLY_EVASION_DEBUFF", "APPLY_ALL_STATS_DEBUFF", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS"], "special_templates": ["DISPEL_ALL_DEBUFFS", "SHIELD_REMOVAL", "TRANSFER_MP_EFFECT", "LOSE_MP_EFFECT", "APPLY_WEAK_LIFESTEAL"]}}