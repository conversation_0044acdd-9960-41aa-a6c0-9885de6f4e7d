#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡片上傳管理腳本
用於管理卡片上傳進度、恢復上傳和更新數據庫
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional

# 將專案根目錄添加到 sys.path
project_root = Path(__file__).resolve().parents[1]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)
from utils.logger import initialize_logging  # noqa: E402

# 初始化日誌
initialize_logging()
logger = logging.getLogger(__name__)

# 載入環境變數
load_dotenv()


class UploadManager:
    def __init__(self):
        pass

    async def setup_database(self):
        """此方法現在是空的，因為連接由 async_manager 管理"""
        pass

    async def cleanup(self):
        """此方法現在是空的，因為連接由 async_manager 管理"""
        pass

    def load_progress(self) -> Optional[Dict]:
        """載入上傳進度"""
        progress_file = Path("upload_progress.json")
        if progress_file.exists():
            with open(progress_file, "r", encoding="utf-8") as f:
                return json.load(f)
        return None

    def load_cdn_links(self) -> Dict:
        """載入CDN連結數據"""
        cdn_file = Path("card_cdn_links.json")
        if cdn_file.exists():
            with open(cdn_file, "r", encoding="utf-8") as f:
                return json.load(f)
        return {}

    def show_progress(self):
        """顯示當前進度"""
        progress = self.load_progress()
        if not progress:
            logger.info("沒有找到進度文件")
            return

        logger.info("=== 上傳進度統計 ===")
        logger.info("已處理卡片數: %s", progress.get("processed_count", 0))
        logger.info("成功上傳: %s", progress.get("success_count", 0))
        logger.info("失敗數量: %s", progress.get("failure_count", 0))
        logger.info("最後更新時間: %s", progress.get("last_update", "N/A"))

        failed_cards = progress.get("failed_cards", [])
        if failed_cards:
            logger.info("\n=== 失敗的卡片 (%s 張) ===", len(failed_cards))
            for card in failed_cards[:10]:  # 只顯示前10張
                logger.info(
                    f"卡片 {card['card_id']} ({card['name']}): {card['reason']}"
                )
            if len(failed_cards) > 10:
                logger.info("... 還有 %s 張失敗的卡片", len(failed_cards) - 10)

    def export_failed_cards(self):
        """導出失敗的卡片列表"""
        progress = self.load_progress()
        if not progress:
            logger.info("沒有找到進度文件")
            return

        failed_cards = progress.get("failed_cards", [])
        if not failed_cards:
            logger.info("沒有失敗的卡片")
            return

        output_file = f"failed_cards_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(failed_cards, f, ensure_ascii=False, indent=2)

        logger.info("失敗的卡片列表已導出到: %s", output_file)

    async def update_database_from_cdn_links(self):
        """從CDN連結文件更新數據庫"""
        cdn_links = self.load_cdn_links()
        if not cdn_links:
            logger.info("沒有找到CDN連結文件")
            return

        logger.info("開始更新數據庫，共 %s 條記錄", len(cdn_links))

        update_query = """
        UPDATE gacha_master_cards
        SET image_url = $2
        WHERE card_id = $1
        """

        pool = get_pool()
        if not pool:
            logger.error("數據庫連接池未初始化")
            return

        try:
            updated_count = 0
            async with pool.acquire() as conn:
                for card_id_str, link_data in cdn_links.items():
                    card_id = int(card_id_str)
                    new_url = link_data["new_cdn_url"]

                    await conn.execute(update_query, card_id, new_url)
                    updated_count += 1

                    if updated_count % 100 == 0:
                        logger.info("已更新 %s 條記錄...", updated_count)
            logger.info("數據庫更新完成，共更新 %s 條記錄", updated_count)
        except Exception as e:
            logger.error("更新數據庫失敗: %s", e)
            raise

    async def verify_database_updates(self):
        """驗證數據庫更新結果"""
        cdn_links = self.load_cdn_links()
        if not cdn_links:
            logger.info("沒有找到CDN連結文件")
            return

        logger.info("開始驗證數據庫更新結果...")

        query = "SELECT card_id, image_url FROM gacha_master_cards WHERE card_id = $1"
        pool = get_pool()
        if not pool:
            logger.error("數據庫連接池未初始化")
            return

        try:
            verified_count = 0
            mismatch_count = 0
            async with pool.acquire() as conn:
                for card_id_str, link_data in cdn_links.items():
                    card_id = int(card_id_str)
                    expected_url = link_data["new_cdn_url"]

                    row = await conn.fetchrow(query, card_id)
                    if row:
                        actual_url = row["image_url"]
                        if actual_url == expected_url:
                            verified_count += 1
                        else:
                            mismatch_count += 1
                            logger.warning("卡片 %s URL不匹配:", card_id)
                            logger.warning("  期望: %s", expected_url)
                            logger.warning("  實際: %s", actual_url)
                    else:
                        logger.error("找不到卡片 %s", card_id)

            logger.info("驗證完成: 匹配 %s, 不匹配 %s", verified_count, mismatch_count)
        except Exception as e:
            logger.error("驗證過程中發生錯誤: %s", e)
            raise

    def backup_cdn_links(self):
        """備份CDN連結文件"""
        cdn_file = Path("card_cdn_links.json")
        if not cdn_file.exists():
            logger.info("沒有找到CDN連結文件")
            return

        backup_file = (
            f"card_cdn_links_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        cdn_file.rename(backup_file)
        logger.info("CDN連結文件已備份為: %s", backup_file)

    def merge_cdn_links(self, additional_file: str):
        """合併多個CDN連結文件"""
        main_links = self.load_cdn_links()

        additional_file_path = Path(additional_file)
        if not additional_file_path.exists():
            logger.error("找不到文件: %s", additional_file)
            return

        with open(additional_file_path, "r", encoding="utf-8") as f:
            additional_links = json.load(f)

        # 合併連結
        merged_links = {**main_links, **additional_links}

        # 保存合併結果
        with open("card_cdn_links_merged.json", "w", encoding="utf-8") as f:
            json.dump(merged_links, f, ensure_ascii=False, indent=2)

        logger.info(
            (
                f"已合併 {len(main_links)} + {len(additional_links)} = "
                f"{len(merged_links)} 條記錄"
            )
        )
        logger.info("合併結果已保存為: card_cdn_links_merged.json")


async def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description="管理卡片上傳進度")
    parser.add_argument(
        "action",
        choices=[
            "status",
            "export-failed",
            "update-db",
            "verify-db",
            "backup",
            "merge",
        ],
        help="要執行的操作",
    )
    parser.add_argument("--file", help="用於merge操作的額外文件路徑")

    args = parser.parse_args()

    manager = UploadManager()

    # 只有需要資料庫的操作才初始化連接
    db_actions = ["update-db", "verify-db"]
    if args.action in db_actions:
        await setup_connections()

    try:
        if args.action == "status":
            manager.show_progress()
        elif args.action == "export-failed":
            manager.export_failed_cards()
        elif args.action == "update-db":
            await manager.update_database_from_cdn_links()
        elif args.action == "verify-db":
            await manager.verify_database_updates()
        elif args.action == "backup":
            manager.backup_cdn_links()
        elif args.action == "merge":
            if not args.file:
                logger.error("merge操作需要指定--file參數")
                return
            manager.merge_cdn_links(args.file)
    except Exception as e:
        logger.error("操作失敗: %s", e)
    finally:
        if args.action in db_actions:
            await close_connections()


if __name__ == "__main__":
    asyncio.run(main())
