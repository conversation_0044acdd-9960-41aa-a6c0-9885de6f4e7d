"""
每小時獎勵系統 COG
處理 /hourly 指令，完全內置邏輯，無需服務層
"""

import random
import time
from typing import TYPE_CHECKING, Any, Dict, Union, cast

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from config.app_config import get_config
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import OnCooldownError
from gacha.services import (
    captcha_service,
    economy_service,
    user_service,
    validation_service,
)
from gacha.services.activity_service import get_captcha_chance
from gacha.views.captcha_view import CaptchaView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class HourlyCog(commands.Cog, name="每小時獎勵"):
    """處理每小時獎勵領取指令，內置所有邏輯"""

    def __init__(self, bot: BotType):
        self.bot = bot
        # 從配置獲取獎勵金額和Redis鍵前綴
        try:
            self.HOURLY_REWARD = int(
                cast(
                    Union[str, int],
                    get_config("gacha_core_settings.economy_hourly_reward", 2000),
                )
            )
        except (ValueError, TypeError):
            self.HOURLY_REWARD = 2000
        self.HOURLY_CLAIM_KEY = get_config(
            "gacha_core_settings.economy_hourly_claim_key_prefix", "gacha:hourly_claim:"
        )
        max_fails_config = get_config("gacha_core_settings.hourly_captcha_max_fails", 3)
        try:
            self.CAPTCHA_MAX_FAILS = int(cast(Union[str, int], max_fails_config))
        except (ValueError, TypeError):
            self.CAPTCHA_MAX_FAILS = 3
        logger.info("HourlyCog initialized.")

    async def _ensure_user_exists(self, user_id: int, nickname: str) -> None:
        """確保用戶存在，如果不存在則創建"""
        # 直接調用導入的服務模組
        await validation_service.ensure_user_exists(
            user_id, nickname=nickname, create_if_missing=True
        )
        await user_service.ensure_nickname_updated(user_id, nickname)

    async def _check_cooldown(self, user_id: int):
        """檢查使用者是否處於冷卻時間"""
        redis_client = get_redis_client()
        if not redis_client:
            raise RuntimeError("Redis client is not available for hourly rewards.")

        hourly_key = f"{self.HOURLY_CLAIM_KEY}{user_id}"
        remaining_seconds = await redis_client.ttl(hourly_key)

        if remaining_seconds > 0:
            current_time = int(time.time())
            next_claim_time = current_time + remaining_seconds
            timestamp_msg = f"冷卻中！請在 <t:{int(next_claim_time)}:R> 後再試"
            raise OnCooldownError(
                message=timestamp_msg, retry_after=float(remaining_seconds)
            )

    async def _claim_hourly_reward(self, user_id: int) -> Dict[str, Any]:
        """處理每小時獎勵領取邏輯"""
        pool = get_pool()
        redis_client = get_redis_client()
        if not redis_client:
            raise RuntimeError("Redis client is not available for hourly rewards.")

        async with pool.acquire() as conn:
            async with conn.transaction():
                await validation_service.ensure_user_exists(
                    user_id, create_if_missing=True, connection=conn
                )

                # 所有用戶都獲得滿額獎勵
                final_reward = self.HOURLY_REWARD

                new_balance = await economy_service.award_oil(
                    user_id=user_id,
                    amount=final_reward,
                    transaction_type="reward:hourly",
                    reason="Hourly claim",
                    connection=conn,
                )

        current_time = int(time.time())
        hourly_key = f"{self.HOURLY_CLAIM_KEY}{user_id}"
        await redis_client.setex(hourly_key, 3600, current_time)
        next_claim_time = current_time + 3600

        return {
            "new_balance": new_balance,
            "reward": final_reward,
            "next_claim_time": next_claim_time,
        }

    async def _send_success_message(
        self, interaction: discord.Interaction, result: Dict[str, Any]
    ):
        """發送成功的獎勵訊息"""
        from config.app_config import get_oil_emoji

        embed = SuccessEmbed(description=f"成功領取 **{result['reward']:,}** 油幣！")
        embed.title = "每小時獎勵"
        embed.add_field(
            name="當前餘額",
            value=f"{get_oil_emoji()} `{result['new_balance']:,}`",
            inline=False,
        )

        if "next_claim_time" in result:
            next_time = result["next_claim_time"]
            next_time_str = f"<t:{int(next_time)}:R>"
            embed.add_field(name="下次可領取時間", value=next_time_str, inline=True)

        if interaction.user.display_avatar:
            embed.set_thumbnail(url=interaction.user.display_avatar.url)

        await interaction.followup.send(embed=embed, ephemeral=False)

    @app_commands.command(name="hourly", description="領取每小時獎勵油幣")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def hourly(self, interaction: discord.Interaction):
        """處理每小時獎勵領取命令"""
        await interaction.response.defer(thinking=True, ephemeral=False)

        user_id = interaction.user.id

        # [CRITICAL-1] 修復：在指令開頭檢查是否有待處理的驗證碼
        temp_captcha_service = captcha_service.CaptchaService(
            bot=self.bot,
            cog_name="hourly",
            user_id=user_id,
            on_correct=lambda i: None,  # type: ignore
            on_final_fail=lambda i: None,  # type: ignore
            on_timeout=lambda: None,  # type: ignore
        )
        if await temp_captcha_service.is_captcha_pending():
            from gacha.exceptions import CaptchaPendingError

            raise CaptchaPendingError()

        current_nickname = interaction.user.display_name

        await self._check_cooldown(user_id)
        await self._ensure_user_exists(user_id, current_nickname)

        # [建議修改] 使用 activity_service 來決定是否觸發驗證碼
        captcha_chance = await get_captcha_chance(user_id)
        if random.random() < captcha_chance:
            await self._handle_captcha(interaction)
        else:
            result = await self._claim_hourly_reward(user_id)
            await self._send_success_message(interaction, result)

    async def _handle_captcha(self, interaction: discord.Interaction):
        """處理驗證碼流程"""
        redis_client = get_redis_client()
        if not redis_client:
            raise RuntimeError("Redis client is not available for captcha.")

        async def on_correct(inner_interaction: discord.Interaction) -> bool:
            await self._check_cooldown(interaction.user.id)
            result = await self._claim_hourly_reward(interaction.user.id)

            from config.app_config import get_oil_emoji

            embed = SuccessEmbed(
                description=f"✅ 驗證成功！\n\n成功領取 **{result['reward']:,}** 油幣！"
            )
            embed.title = "每小時獎勵"
            embed.add_field(
                name="當前餘額",
                value=f"{get_oil_emoji()} `{result['new_balance']:,}`",
                inline=False,
            )

            if "next_claim_time" in result:
                next_time = result["next_claim_time"]
                next_time_str = f"<t:{int(next_time)}:R>"
                embed.add_field(name="下次可領取時間", value=next_time_str, inline=True)

            if inner_interaction.user.display_avatar:
                embed.set_thumbnail(url=inner_interaction.user.display_avatar.url)

            await inner_interaction.response.edit_message(
                embed=embed, view=None, attachments=[]
            )
            return True

        async def on_final_fail(inner_interaction: discord.Interaction) -> bool:
            current_time = int(time.time())
            hourly_key = f"{self.HOURLY_CLAIM_KEY}{interaction.user.id}"
            await redis_client.setex(hourly_key, 3600, current_time)  # 1 hour
            embed = discord.Embed(
                title="❌ 驗證失敗",
                description="驗證失敗次數過多，已觸發 1 小時冷卻。",
                color=discord.Color.red(),
            )
            await inner_interaction.response.edit_message(
                embed=embed, view=None, attachments=[]
            )
            return True  # 停止 View

        async def on_timeout():
            current_time = int(time.time())
            hourly_key = f"{self.HOURLY_CLAIM_KEY}{interaction.user.id}"
            await redis_client.setex(hourly_key, 43200, current_time)  # 12 hours
            embed = discord.Embed(
                title="⌛️ 驗證超時",
                description="安全驗證已超時，已觸發 12 小時冷卻。",
                color=discord.Color.orange(),
            )
            try:
                await interaction.edit_original_response(
                    embed=embed, view=None, attachments=[]
                )
            except discord.NotFound:
                pass  # 訊息可能已被使用者刪除

        async def on_refresh(
            inner_interaction: discord.Interaction, view: "CaptchaView"
        ) -> None:
            """處理更換驗證碼的邏輯（目前由 Service 和 View 處理）。"""
            pass

        captcha_handler = captcha_service.CaptchaService(
            bot=self.bot,
            cog_name="hourly",
            user_id=interaction.user.id,
            on_correct=on_correct,
            on_final_fail=on_final_fail,
            on_timeout=on_timeout,
            on_refresh=on_refresh,
            max_fails=self.CAPTCHA_MAX_FAILS,
        )
        await captcha_handler.handle_captcha(interaction)


async def setup(bot: BotType):
    """載入 HourlyCog"""
    await bot.add_cog(HourlyCog(bot))
    logger.info("HourlyCog has been added to the bot.")
