"""
護盾實例模型
"""

import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, Optional


@dataclass
class ShieldInstance:
    """護盾實例模型"""

    instance_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    shield_type: str = "ABSORB_DAMAGE"  # 護盾類型
    caster_id: Optional[str] = None  # 施法者的instance_id
    target_id: str = ""  # 目標的instance_id

    # 護盾屬性
    current_value: float = 0.0  # 當前護盾值
    max_value: float = 0.0  # 最大護盾值
    duration_turns: int = 0  # 剩餘持續回合數

    # 護盾類型特性
    absorb_damage: bool = True  # 是否吸收普通傷害
    absorb_true_damage: bool = False  # 是否吸收真實傷害

    # 創建時的上下文信息
    creation_context: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """初始化後處理"""
        # 根據護盾類型設置吸收特性
        if self.shield_type == "ABSORB_DAMAGE":
            self.absorb_damage = True
            self.absorb_true_damage = False
        elif self.shield_type == "ABSORB_ALL":
            self.absorb_damage = True
            self.absorb_true_damage = True
        # 向後兼容舊的護盾類型
        elif self.shield_type in ["ABSORB_PHYSICAL_ONLY", "ABSORB_MAGICAL_ONLY"]:
            self.absorb_damage = True
            self.absorb_true_damage = False

    def can_absorb_damage_type(self, damage_type: str) -> bool:
        """
        檢查是否能吸收指定類型的傷害

        Args:
            damage_type: 傷害類型 (DAMAGE, TRUE_DAMAGE)

        Returns:
            是否能吸收
        """
        if damage_type == "DAMAGE":
            return self.absorb_damage
        elif damage_type == "TRUE_DAMAGE":
            return self.absorb_true_damage
        # 向後兼容
        elif damage_type in ["PHYSICAL", "MAGICAL"]:
            return self.absorb_damage
        else:
            return False

    def process_damage_absorption(
        self, damage_amount: float, damage_type: str
    ) -> tuple[float, float]:
        """
        吸收傷害

        Args:
            damage_amount: 傷害數值
            damage_type: 傷害類型

        Returns:
            (被吸收的傷害, 剩餘傷害)
        """
        if not self.can_absorb_damage_type(damage_type):
            return 0.0, damage_amount

        if self.current_value <= 0:
            return 0.0, damage_amount

        absorbed = min(self.current_value, damage_amount)
        self.current_value -= absorbed
        remaining_damage = damage_amount - absorbed

        return absorbed, remaining_damage

    def is_depleted(self) -> bool:
        """檢查護盾是否已耗盡"""
        return self.current_value <= 0

    def is_expired(self) -> bool:
        """檢查護盾是否已過期"""
        return self.duration_turns <= 0

    def tick_duration(self) -> None:
        """減少持續時間"""
        if self.duration_turns > 0:
            self.duration_turns -= 1

    def get_shield_info(self) -> Dict[str, Any]:
        """
        獲取護盾信息

        Returns:
            護盾信息字典
        """
        return {
            "instance_id": self.instance_id,
            "shield_type": self.shield_type,
            "current_value": self.current_value,
            "max_value": self.max_value,
            "duration_turns": self.duration_turns,
            "absorb_damage": self.absorb_damage,
            "absorb_true_damage": self.absorb_true_damage,
            "is_depleted": self.is_depleted(),
            "is_expired": self.is_expired(),
        }
