from datetime import datetime, timezone
from decimal import ROUND_HALF_UP, Decimal
from typing import Optional

import asyncpg

from config.app_config import get_gacha_stock_integration_config
from database.postgresql.async_manager import get_pool
from utils.logger import logger

# --- 模組級變量 ---
_anchor_price_task_failure_count = 0


# --- Helper Functions ---


def _validate_initial_anchor_price(
    asset_id: int, initial_anchor_price_db: Optional[Decimal]
) -> Decimal:
    """驗證並返回一個安全的初始錨定價格。"""
    if initial_anchor_price_db is None:
        logger.warning(
            "資產ID %s：資料庫中的 initial_anchor_price 為 NULL。使用 Decimal('0.01') 作為最低後備。",
            asset_id,
        )
        return Decimal("0.01")

    initial_price = Decimal(str(initial_anchor_price_db))
    if initial_price <= Decimal("0"):
        logger.warning(
            "資產ID %s：initial_anchor_price_db 為 %s。使用 Decimal('0.01') 以避免問題。",
            asset_id,
            initial_price,
        )
        return Decimal("0.01")
    return initial_price


async def _get_current_day_anchor_price(
    current_anchor_price_db: Optional[Decimal],
    anchor_price_updated_at_db: Optional[datetime],
) -> Optional[Decimal]:
    """檢查當天是否有有效的錨定價格。"""
    today_utc_date = datetime.now(timezone.utc).date()
    if current_anchor_price_db is not None and anchor_price_updated_at_db is not None:
        updated_at_aware = (
            anchor_price_updated_at_db.replace(tzinfo=timezone.utc)
            if anchor_price_updated_at_db.tzinfo is None
            else anchor_price_updated_at_db
        )
        if updated_at_aware.date() == today_utc_date:
            price = Decimal(str(current_anchor_price_db))
            if price > Decimal("0"):
                return price
    return None


async def _calculate_historical_average_price(
    conn: asyncpg.Connection,
    asset_id: int,
    fallback_days: int,
    min_trading_days: int,
) -> Optional[Decimal]:
    """從最近的歷史記錄中計算平均收盤價。"""
    query = f"""
    WITH DailyClosingPrices AS (
        SELECT
            DATE(timestamp AT TIME ZONE 'UTC') AS trade_date,
            (array_agg(price ORDER BY timestamp DESC))[1] AS closing_price
        FROM asset_price_history
        WHERE asset_id = $1
        GROUP BY DATE(timestamp AT TIME ZONE 'UTC')
        ORDER BY trade_date DESC
        LIMIT {fallback_days}
    )
    SELECT closing_price FROM DailyClosingPrices;
    """
    try:
        records = await conn.fetch(query, asset_id)
        prices = [
            Decimal(str(r["closing_price"]))
            for r in records
            if r["closing_price"] is not None and Decimal(str(r["closing_price"])) > 0
        ]
        if len(prices) >= min_trading_days:
            avg_price = sum(prices) / Decimal(len(prices))
            return avg_price.quantize(Decimal("0.0001"), rounding=ROUND_HALF_UP)
        else:
            logger.warning(
                "資產ID %s：用於 %s 天平均的數據不足（%s 個有效日，需要 %s 個）。",
                asset_id,
                fallback_days,
                len(prices),
                min_trading_days,
            )
    except Exception as e:
        logger.error(
            "資產ID %s：在計算 %s 天平均值時出錯：%s。",
            asset_id,
            fallback_days,
            e,
            exc_info=True,
        )
    return None


async def _get_stale_anchor_price(
    current_anchor_price_db: Optional[Decimal],
    anchor_price_updated_at_db: Optional[datetime],
    max_stale_days: int,
    asset_id: int,
) -> Optional[Decimal]:
    """檢查一個過期但仍可用的錨定價格。"""
    today_utc_date = datetime.now(timezone.utc).date()
    if current_anchor_price_db is not None and anchor_price_updated_at_db is not None:
        updated_at_aware = (
            anchor_price_updated_at_db.replace(tzinfo=timezone.utc)
            if anchor_price_updated_at_db.tzinfo is None
            else anchor_price_updated_at_db
        )
        days_since_update = (today_utc_date - updated_at_aware.date()).days
        if days_since_update <= max_stale_days:
            price = Decimal(str(current_anchor_price_db))
            if price > Decimal("0"):
                return price
        else:
            logger.warning(
                "資產ID %s：過期的預先計算錨定價格太舊（%s 天，最大 %s 天）。",
                asset_id,
                days_since_update,
                max_stale_days,
            )
    return None


async def get_effective_anchor_price(
    conn: asyncpg.Connection,
    asset_id: int,
    initial_anchor_price_db: Optional[Decimal],
    current_anchor_price_db: Optional[Decimal],
    anchor_price_updated_at_db: Optional[datetime],
) -> Decimal:
    """
    檢索或計算資產的有效錨定價格，實現增強的後備機制。
    """
    cfg = get_gacha_stock_integration_config().stock_market

    # 1. 當日價格
    price = await _get_current_day_anchor_price(
        current_anchor_price_db, anchor_price_updated_at_db
    )
    if price:
        logger.info("資產ID %s：使用當日預先計算的錨定價格：%s", asset_id, price)
        return price
    logger.info("資產ID %s：當日錨定價格無效或缺失。執行後備策略 1。", asset_id)

    # 2. 歷史平均
    price = await _calculate_historical_average_price(
        conn,
        asset_id,
        cfg.anchor_price_fallback_days_history,
        cfg.anchor_price_min_trading_days_for_avg,
    )
    if price:
        logger.info("資產ID %s：使用計算出的歷史平均價格：%s", asset_id, price)
        return price

    # 3. 過期價格
    price = await _get_stale_anchor_price(
        current_anchor_price_db,
        anchor_price_updated_at_db,
        cfg.anchor_price_max_stale_days,
        asset_id,
    )
    if price:
        logger.warning("資產ID %s：使用過期的預先計算錨定價格：%s", asset_id, price)
        return price
    logger.info("資產ID %s：過期錨定價格無效或太舊。執行後備策略 2。", asset_id)

    # 4. 初始價格（最終後備）
    initial_price = _validate_initial_anchor_price(asset_id, initial_anchor_price_db)
    logger.error(
        "資產ID %s：所有後備機制均已用盡。使用 initial_anchor_price：%s。",
        asset_id,
        initial_price,
    )
    return initial_price


async def _calculate_and_prepare_update_for_asset(
    conn: asyncpg.Connection,
    asset: asyncpg.Record,
    calc_days_history: int,
    current_time: datetime,
) -> Optional[tuple[Decimal, datetime, int]]:
    """為單個資產計算錨定價格並準備更新元組。"""
    asset_id = asset["asset_id"]
    initial_anchor_price_db = asset["initial_anchor_price"]

    initial_price = _validate_initial_anchor_price(asset_id, initial_anchor_price_db)

    query = f"""
        WITH DailyClosingPrices AS (
            SELECT
                DATE(timestamp AT TIME ZONE 'UTC') AS trade_date,
                (array_agg(price ORDER BY timestamp DESC))[1] AS closing_price
            FROM asset_price_history
            WHERE asset_id = $1
            GROUP BY DATE(timestamp AT TIME ZONE 'UTC')
            ORDER BY trade_date DESC
            LIMIT {calc_days_history}
        )
        SELECT closing_price FROM DailyClosingPrices;
    """
    records = await conn.fetch(query, asset_id)
    prices = [
        Decimal(str(r["closing_price"]))
        for r in records
        if r["closing_price"] is not None and Decimal(str(r["closing_price"])) > 0
    ]

    calculated_price = None
    if prices:
        calculated_price = sum(prices) / Decimal(len(prices))
        logger.debug(
            "資產ID %s：從 %s 天的數據計算出的錨定價格：%s",
            asset_id,
            len(prices),
            calculated_price,
        )
    else:
        calculated_price = initial_price
        logger.warning(
            "資產ID %s：沒有有效的交易歷史。使用 initial_anchor_price：%s",
            asset_id,
            calculated_price,
        )

    if calculated_price and calculated_price > Decimal("0"):
        final_price = calculated_price.quantize(
            Decimal("0.0001"), rounding=ROUND_HALF_UP
        )
        return (final_price, current_time, asset_id)

    logger.error(
        "資產ID %s：計算出的錨定價格為零或負數（%s）。改用初始錨定價格 %s。",
        asset_id,
        calculated_price,
        initial_price,
    )
    final_price = initial_price.quantize(Decimal("0.0001"), rounding=ROUND_HALF_UP)
    return (final_price, current_time, asset_id)


async def calculate_daily_anchor_prices():
    """每日計算並更新所有虛擬資產的錨定價格。"""
    global _anchor_price_task_failure_count
    logger.info("開始每日錨定價格計算。")
    task_successful = False
    try:
        pool = get_pool()
        if pool is None:
            logger.error("資料庫連線池未初始化，無法計算錨定價格。")
            _anchor_price_task_failure_count += 1
            return
        async with pool.acquire() as conn:
            assets = await conn.fetch(
                "SELECT asset_id, initial_anchor_price FROM virtual_assets"
            )
            if not assets:
                logger.info("找不到虛擬資產來計算錨定價格。")
                _anchor_price_task_failure_count = 0
                task_successful = True
                return

            updates = []
            current_time = datetime.now(timezone.utc)
            cfg = get_gacha_stock_integration_config().stock_market
            calc_days = cfg.anchor_price_fallback_days_history

            for asset in assets:
                if asset["initial_anchor_price"] is None:
                    logger.warning(
                        "資產ID %s 的 initial_anchor_price 為 NULL。跳過。",
                        asset["asset_id"],
                    )
                    continue

                update_data = await _calculate_and_prepare_update_for_asset(
                    conn, asset, calc_days, current_time
                )
                if update_data:
                    updates.append(update_data)

            if updates:
                update_query = """
                    UPDATE virtual_assets
                    SET current_anchor_price = $1,
                        anchor_price_updated_at = $2
                    WHERE asset_id = $3;
                """
                await conn.executemany(update_query, updates)
                logger.info("成功更新了 %s 個資產的錨定價格。", len(updates))
            else:
                logger.info("在此週期內無需更新錨定價格。")

            _anchor_price_task_failure_count = 0
            task_successful = True
    except Exception as e:
        _anchor_price_task_failure_count += 1
        logger.error(
            "在 calculate_daily_anchor_prices 中出錯（失敗次數 #%s）：%s",
            _anchor_price_task_failure_count,
            e,
            exc_info=True,
        )
    finally:
        if task_successful:
            logger.info("成功完成每日錨定價格計算。")
        else:
            logger.error("每日錨定價格計算完成但有錯誤。")
