{"BASIC_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": false, "modifiers": []}, "TRUE_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 1.0, "can_crit": false, "modifiers": []}, "MULTI_HIT_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.6, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "hit_count", "source_combatant": "caster", "scaling_type": "flat_add", "scaling_factor": 0.6, "max_bonus_value": 1.8}]}, "DRAIN_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "always_true", "value": 1}]}, "bonus_type": "percentage_increase", "bonus_value": 0.5}]}, "EXECUTE_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.5, "can_crit": false, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "target", "check": "hp_below_percent", "value": 0.3}]}, "bonus_type": "multiplier_add", "bonus_value": 1.0}]}, "CONDITIONAL_DAMAGE_BOOST": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "hp_above_percent", "value": 0.5}]}, "bonus_type": "percentage_increase", "bonus_value": 0.5}]}, "PERCENTAGE_HP_DAMAGE": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.0, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "target_max_hp", "source_combatant": "target", "scaling_type": "percentage_add", "scaling_factor": 0.15, "max_bonus_value": 0.3}]}, "PRECISION_STRIKE_1V1": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.2, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "stat:accuracy", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.015, "max_bonus_value": 0.6}]}, "RIPOSTING_STANCE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.7, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:was_targeted_and_attacked_last_turn"}]}, "bonus_type": "multiplier_add", "bonus_value": 1.5}]}, "SOUL_SIPHON": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.9, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "stat:atk", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.008, "max_bonus_value": 0.4}]}, "VITAL_POINT_EXPLOIT": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.5, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "OR", "conditions": [{"source_combatant": "target", "check": "has_status_effect", "status_effect_id": "ARMOR_BROKEN_STATUS"}, {"source_combatant": "target", "check": "has_status_effect", "status_effect_id": "PHYSICAL_VULNERABILITY"}]}, "bonus_type": "multiplier_add", "bonus_value": 0.75}]}, "MANA_BURN_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.7, "can_crit": false, "modifiers": [{"modifier_type": "CUSTOM_EFFECT_TRIGGER", "triggered_effect": {"effect_type": "LOSE_MP", "value_formula": "caster_stat_atk * 0.5", "target_override": "target"}}]}, "RESOURCE_VAMPIRE": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.2, "can_crit": false, "modifiers": [{"modifier_type": "CUSTOM_EFFECT_TRIGGER", "triggered_effect": {"effect_type": "TRA<PERSON><PERSON><PERSON>_<PERSON>", "value_formula": "clamp(target_current_mp * 0.1, 0, 20)", "target_override": "target", "beneficiary_override": "caster"}}]}, "OPPORTUNISTIC_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "OR", "conditions": [{"source_combatant": "target", "check": "custom:last_action_had_tag", "value": "HIGH_COST"}, {"source_combatant": "target", "check": "custom:last_action_had_tag", "value": "ULTIMATE_ABILITY"}, {"source_combatant": "target", "check": "has_status_effect", "status_effect_id": "COOLDOWN_INCREASED_DEBUFF"}]}, "bonus_type": "multiplier_add", "bonus_value": 0.75, "bonus_duration_turns": 0}]}, "COMPOUNDING_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:skill_hits_on_target_counter", "source_combatant": "caster", "scaling_type": "flat_damage_add_per_stack", "scaling_factor": 20, "max_bonus_value": 100}]}, "EQUALIZER_PAIN": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.5, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:hp_percent_diff_caster_vs_target", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.5, "max_bonus_value": 0.75, "min_bonus_value": -0.25}]}, "ATTACK_SUPERIORITY_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:stat_diff_caster_vs_target:atk", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.002, "max_bonus_value": 1.0}]}, "DEFENSE_BREAKER_ASSAULT": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.1, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:stat_higher_than_target", "stat_name": "atk", "comparison_stat": "def"}]}, "bonus_type": "percentage_increase", "bonus_value": 0.6}]}, "SPEED_ADVANTAGE_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.9, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:stat_diff_caster_vs_target:spd", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.003, "max_bonus_value": 0.8}]}, "ATTACK_SUPREMACY_BLAST": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:stat_diff_caster_vs_target:atk", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.002, "max_bonus_value": 1.0}]}, "VITALITY_DRAIN_STRIKE": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.6, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:hp_diff_caster_vs_target", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.001, "max_bonus_value": 0.5}]}, "OVERWHELMING_FORCE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.2, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:total_stats_higher_than_target"}]}, "bonus_type": "percentage_increase", "bonus_value": 0.4}]}, "PERFECT_HEALTH_ASSAULT": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "hp_above_percent", "value": 0.95}]}, "bonus_type": "percentage_increase", "bonus_value": 0.5}]}, "DESPERATE_LAST_STAND": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.7, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:missing_hp_percent_caster", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 1.5, "max_bonus_value": 1.2}]}, "MANA_OVERFLOW_BLAST": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:mp_above_percent", "value": 0.8}]}, "bonus_type": "percentage_increase", "bonus_value": 0.6}]}, "MANA_DEPLETION_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.1, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "target", "check": "custom:mp_below_percent", "value": 0.3}]}, "bonus_type": "percentage_increase", "bonus_value": 0.7}]}, "ACCURACY_PRECISION_SHOT": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.9, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:stat_diff_caster_vs_target:accuracy", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.01, "max_bonus_value": 0.8}]}, "EVASION_COUNTER_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.8, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:stat_higher_than_target", "stat_name": "evasion", "comparison_stat": "accuracy"}]}, "bonus_type": "percentage_increase", "bonus_value": 0.5}]}, "DEFENSIVE_SUPERIORITY": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:stat_diff_caster_vs_target:def", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.002, "max_bonus_value": 0.6}]}, "RESISTANCE_PIERCE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 1.0, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:stat_higher_than_target", "stat_name": "atk", "comparison_stat": "def"}]}, "bonus_type": "percentage_increase", "bonus_value": 0.5}]}, "BALANCED_COMBAT_STRIKE": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.9, "can_crit": true, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "AND", "conditions": [{"source_combatant": "caster", "check": "custom:stats_within_range", "min_ratio": 0.8, "max_ratio": 1.2}]}, "bonus_type": "percentage_increase", "bonus_value": 0.4}]}, "RESOURCE_ADVANTAGE_BLAST": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.7, "can_crit": false, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:resource_ratio_advantage", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.5, "max_bonus_value": 0.8}]}, "CRITICAL_MASS_EXPLOSION": {"effect_type": "DAMAGE", "damage_type": "DAMAGE", "base_power_multiplier": 0.6, "can_crit": true, "modifiers": [{"modifier_type": "SCALING_MODIFIER", "scaling_source": "custom:combined_offensive_stats", "source_combatant": "caster", "scaling_type": "percentage_add", "scaling_factor": 0.0005, "max_bonus_value": 1.0}]}, "WEAKNESS_EXPLOITATION": {"effect_type": "DAMAGE", "damage_type": "TRUE_DAMAGE", "base_power_multiplier": 0.8, "can_crit": false, "modifiers": [{"modifier_type": "CONDITIONAL_BOOST", "condition_group": {"type": "OR", "conditions": [{"source_combatant": "target", "check": "custom:lowest_defensive_stat"}, {"source_combatant": "target", "check": "hp_below_percent", "value": 0.5}]}, "bonus_type": "percentage_increase", "bonus_value": 0.6}]}}