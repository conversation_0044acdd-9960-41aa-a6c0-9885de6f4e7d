-- ========================================
-- Pioneer System 索引創建腳本
-- 為開拓者指令系統創建性能優化索引
-- 版本：V1.0.0 (2025-07-01)
-- ========================================

BEGIN;

-- ========================================
-- 1. pioneer_profiles 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_profiles_user_id ON public.pioneer_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_profiles_era ON public.pioneer_profiles(current_era);
CREATE INDEX IF NOT EXISTS idx_pioneer_profiles_energy_update ON public.pioneer_profiles(last_energy_update);

-- ========================================
-- 2. pioneer_skills 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_skills_user_id ON public.pioneer_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_skills_skill_id ON public.pioneer_skills(skill_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_skills_level ON public.pioneer_skills(user_id, level DESC);

-- ========================================
-- 3. pioneer_warehouse 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_warehouse_user_id ON public.pioneer_warehouse(user_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_warehouse_item_id ON public.pioneer_warehouse(item_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_warehouse_user_item ON public.pioneer_warehouse(user_id, item_id);

-- ========================================
-- 4. pioneer_quest_progress 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_quest_user_id ON public.pioneer_quest_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_quest_quest_id ON public.pioneer_quest_progress(quest_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_quest_completed ON public.pioneer_quest_progress(user_id, completed_at) WHERE completed_at IS NOT NULL;

-- ========================================
-- 5. pioneer_facilities 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_facilities_user_type ON public.pioneer_facilities(user_id, facility_type);
CREATE INDEX IF NOT EXISTS idx_pioneer_facilities_active ON public.pioneer_facilities(user_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_pioneer_facilities_production_time ON public.pioneer_facilities(last_production_time);
CREATE INDEX IF NOT EXISTS idx_pioneer_facilities_level ON public.pioneer_facilities(user_id, level DESC);

-- ========================================
-- 6. pioneer_facility_states 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_facility_states_facility ON public.pioneer_facility_states(facility_id);
CREATE INDEX IF NOT EXISTS idx_facility_states_item ON public.pioneer_facility_states(item_id) WHERE item_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_facility_states_slot_type ON public.pioneer_facility_states(facility_id, slot_type);

-- ========================================
-- 7. pioneer_facility_assignments 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_facility_assignments_facility ON public.pioneer_facility_assignments(facility_id);
CREATE INDEX IF NOT EXISTS idx_facility_assignments_collection ON public.pioneer_facility_assignments(user_collection_id);

-- ========================================
-- 8. pioneer_research_levels 表索引
-- ========================================
CREATE INDEX IF NOT EXISTS idx_pioneer_research_user_id ON public.pioneer_research_levels(user_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_research_project_id ON public.pioneer_research_levels(project_id);
CREATE INDEX IF NOT EXISTS idx_pioneer_research_level ON public.pioneer_research_levels(user_id, level DESC);
CREATE INDEX IF NOT EXISTS idx_pioneer_research_investment ON public.pioneer_research_levels(user_id, total_invested_oil DESC);

-- ========================================
-- 9. 複合索引用於複雜查詢
-- ========================================

-- 設施管理相關
CREATE INDEX IF NOT EXISTS idx_facilities_user_active_type ON public.pioneer_facilities(user_id, is_active, facility_type) WHERE is_active = true;

-- 倉庫查詢優化
CREATE INDEX IF NOT EXISTS idx_warehouse_user_quantity ON public.pioneer_warehouse(user_id, quantity DESC);

-- 技能排行榜
CREATE INDEX IF NOT EXISTS idx_skills_leaderboard ON public.pioneer_skills(skill_id, level DESC, xp DESC);

-- 研究進度排行榜
CREATE INDEX IF NOT EXISTS idx_research_leaderboard ON public.pioneer_research_levels(project_id, level DESC, total_invested_oil DESC);

COMMIT;

-- ========================================
-- 索引創建完成報告
-- ========================================
SELECT
    '🎉 Pioneer System 索引創建成功完成！' as status,
    '已為所有核心表創建性能優化索引' as description,
    '支援用戶查詢、設施管理、倉庫操作等高頻操作' as optimization_focus;
