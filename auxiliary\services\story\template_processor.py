# -*- coding: utf-8 -*-
"""
模板處理器
處理提示詞卡片中的特殊語法，包括變量替換和隨機功能。
"""

import logging
import random
import re
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class TemplateProcessor:
    """模板处理器，处理卡片内容中的特殊语法"""

    def __init__(self):
        # 编译正则表达式以提高性能
        self.random_pattern1 = re.compile(
            r"\{\{random:([^:][^}]*)\}\}"
        )  # {{random:1,2,3,4}} 但不匹配 ::
        self.random_pattern2 = re.compile(
            r"\{\{random::([^}]+)\}\}"
        )  # {{random::arg1::arg2}}
        self.pick_pattern = re.compile(r"\{\{pick::([^}]+)\}\}")  # {{pick::arg1::arg2}}
        self.roll_pattern = re.compile(
            r"\{\{roll:([^}]+)\}\}"
        )  # {{roll:d6}} or {{roll:2d6+3}}
        
        # 新增的變量系統正則表達式
        self.setvar_pattern = re.compile(r"\{\{setvar::(.*?)::(.*?)\}\}")  # {{setvar::name::value}}
        self.getvar_pattern = re.compile(r"\{\{getvar::(.*?)\}\}")        # {{getvar::name}}

        # 用于 pick 功能的缓存，确保同一内容的多次调用返回相同结果
        self.pick_cache = {}
        self._cache_max_size = 1000  # 最大缓存条目数
        self._cache_access_count = {}  # 跟踪访问次数

    def process_template(
        self, content: str, variables: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        处理模板内容，替换所有特殊语法

        Args:
            content: 原始模板内容
            variables: 变量字典，如 {'user_input': '用户输入', 'user_display_name': '用户名'}

        Returns:
            处理后的内容
        """
        if not content:
            return content
        
        # 確保我們有一個可修改的變量字典
        if variables is None:
            variables = {}

        # 0. 首先處理 setvar，將變量存入 variables 字典中
        content = self._process_setvar_syntax(content, variables)

        # 1. 處理隨機功能
        content = self._process_random_syntax(content)

        # 2. 處理選擇功能
        content = self._process_pick_syntax(content)

        # 3. 處理骰子功能
        content = self._process_roll_syntax(content)

        # 4. 處理 getvar，從 variables 字典中獲取變量值
        content = self._process_getvar_syntax(content, variables)

        # 5. 最後處理標準 Python 格式化變量（如 {user_input}）
        try:
            content = content.format(**variables)
        except KeyError as missing_keys:
            logger.debug("模板中缺失變量: %s", str(missing_keys))
        except ValueError as format_error:
            logger.warning("模板格式化錯誤: %s", str(format_error))
        except Exception as e:
            logger.error("模板處理發生意外錯誤: %s", str(e))

        return content

    def _manage_cache_size(self):
        """管理缓存大小，防止内存泄漏"""
        if len(self.pick_cache) > self._cache_max_size:
            # 删除最少使用的条目
            if self._cache_access_count:
                least_used = min(self._cache_access_count.items(), key=lambda x: x[1])
                key_to_remove = least_used[0]
                del self.pick_cache[key_to_remove]
                del self._cache_access_count[key_to_remove]
                logger.debug(
                    "緩存清理: 移除最少使用項目，緩存大小現為: %d", len(self.pick_cache)
                )

    def _process_setvar_syntax(self, content: str, variables: Dict[str, Any]) -> str:
        """處理 {{setvar::name::value}} 語法，並將其從內容中移除"""
        
        matches = list(self.setvar_pattern.finditer(content))
        
        # 從後往前替換，避免影響前面匹配項的索引
        for match in reversed(matches):
            var_name = match.group(1).strip()
            var_value = match.group(2).strip()
            
            if var_name:
                variables[var_name] = var_value
                logger.debug(f"Set variable '{var_name}' to '{var_value}'")
            
            # 從內容中移除 setvar 標籤
            content = content[:match.start()] + content[match.end():]
            
        return content

    def _process_getvar_syntax(self, content: str, variables: Dict[str, Any]) -> str:
        """處理 {{getvar::name}} 語法"""
        
        def replace_getvar(match):
            var_name = match.group(1).strip()
            # 如果變量存在，則返回值；否則返回空字符串
            value = variables.get(var_name, "")
            logger.debug(f"Get variable '{var_name}', found value: '{value[:50]}...'")
            return value

        return self.getvar_pattern.sub(replace_getvar, content)

    def _process_random_syntax(self, content: str) -> str:
        """处理 {{random:}} 语法"""

        # 处理 {{random::arg1::arg2::arg3}} 格式（优先处理双冒号）
        def replace_random2(match):
            args_str = match.group(1)
            args = [arg.strip() for arg in args_str.split("::") if arg.strip()]
            if args:
                choice = random.choice(args)
                return choice
            return ""

        content = self.random_pattern2.sub(replace_random2, content)

        # 处理 {{random:arg1,arg2,arg3}} 格式
        def replace_random1(match):
            args_str = match.group(1)
            args = [arg.strip() for arg in args_str.split(",") if arg.strip()]
            if args:
                return random.choice(args)
            return ""

        content = self.random_pattern1.sub(replace_random1, content)

        return content

    def _process_pick_syntax(self, content: str) -> str:
        """处理 {{pick::}} 语法（稳定选择）"""

        def replace_pick(match):
            args_str = match.group(1)
            args = [arg.strip() for arg in args_str.split("::")]

            # 使用原始字符串作为缓存键，确保相同内容返回相同结果
            cache_key = match.group(0)

            if cache_key not in self.pick_cache:
                # 使用原始字符串的哈希值作为种子，确保稳定性
                seed = hash(cache_key) % (2**32)
                temp_random = random.Random(seed)
                self.pick_cache[cache_key] = temp_random.choice(args)
                self._cache_access_count[cache_key] = 0
                self._manage_cache_size()

            # 更新访问计数
            self._cache_access_count[cache_key] += 1
            return self.pick_cache[cache_key]

        content = self.pick_pattern.sub(replace_pick, content)

        return content

    def _process_roll_syntax(self, content: str) -> str:
        """处理 {{roll:}} 语法（D&D 骰子）"""

        def replace_roll(match):
            formula = match.group(1).strip()
            return str(self._roll_dice(formula))

        content = self.roll_pattern.sub(replace_roll, content)

        return content

    def _roll_dice(self, formula: str) -> int:
        """
        处理 D&D 骰子公式，增强边界条件处理
        支持格式：d6, 2d6, 3d8+2, d20-1 等
        """
        if not formula or not isinstance(formula, str):
            logger.warning("骰子公式輸入無效")
            return 1

        formula = formula.strip()
        if not formula:
            return 1

        try:
            # 驗證公式長度，防止過長公式
            if len(formula) > 50:
                logger.warning("骰子公式過長: %s", formula[:20] + "...")
                return 1

            # 驗證字符集，只允許數字、d、+、-和空格
            if not re.match(r"^[0-9d+\-\s]+$", formula):
                logger.warning("骰子公式包含非法字符: %s", formula)
                return 1

            # 标准化公式，处理没有数量的情况（如 d6 -> 1d6）
            if formula.startswith("d"):
                formula = "1" + formula

            # 解析公式：XdY+Z 或 XdY-Z
            bonus = 0
            if "+" in formula:
                parts = formula.split("+", 1)
                if len(parts) != 2:
                    raise ValueError("Invalid formula format")
                dice_part, bonus_str = parts
                bonus = int(bonus_str.strip())
            elif "-" in formula:
                parts = formula.split("-", 1)
                if len(parts) != 2:
                    raise ValueError("Invalid formula format")
                dice_part, bonus_str = parts
                bonus = -int(bonus_str.strip())
            else:
                dice_part = formula

            dice_part = dice_part.strip()

            # 解析骰子部分：XdY
            if "d" not in dice_part:
                # 如果没有 d，当作固定数值
                fixed_value = int(dice_part)
                if fixed_value < 1 or fixed_value > 1000:
                    logger.warning("固定數值超出範圍: %d", fixed_value)
                    return 1
                return fixed_value + bonus

            dice_parts = dice_part.split("d", 1)
            if len(dice_parts) != 2:
                raise ValueError("Invalid dice format")

            count_str, sides_str = dice_parts
            count = int(count_str.strip()) if count_str.strip() else 1
            sides = int(sides_str.strip())

            # 驗證骰子數量和面數的合理範圍
            if count < 1 or count > 100:
                logger.warning("骰子數量無效: %d", count)
                return 1

            if sides < 1 or sides > 1000000:
                logger.warning("骰子面數無效: %d", sides)
                return 1

            # 驗證獎勵值範圍
            if bonus < -1000 or bonus > 1000:
                logger.warning("獎勵值超出範圍: %d", bonus)
                bonus = max(-1000, min(1000, bonus))

            # 投掷骰子
            total = 0
            for _ in range(count):
                total += random.randint(1, sides)

            result = total + bonus

            # 确保结果在合理范围内
            if result < 1:
                result = 1
            elif result > 1000000:
                logger.warning("骰子結果過大，限制為 1000000")
                result = 1000000

            return result

        except (ValueError, IndexError, OverflowError) as e:
            # 如果公式無效，返回 1
            logger.warning("骰子公式無效 '%s': %s，返回 1", formula, str(e))
            return 1
        except Exception as e:
            # 處理其他意外錯誤
            logger.error("骰子公式處理發生意外錯誤 '%s': %s，返回 1", formula, str(e))
            return 1

    def clear_pick_cache(self):
        """清空 pick 功能的缓存"""
        self.pick_cache.clear()


# 全局模板处理器实例
_template_processor = TemplateProcessor()


def process_card_content(
    content: str, variables: Optional[Dict[str, Any]] = None
) -> str:
    """
    处理卡片内容的便捷函数

    Args:
        content: 卡片内容
        variables: 变量字典

    Returns:
        处理后的内容
    """
    return _template_processor.process_template(content, variables)


def clear_template_cache():
    """清空模板缓存的便捷函数"""
    _template_processor.clear_pick_cache()
