"""
BattleCoordinatorService - 戰鬥協調服務

負責PVE戰鬥的準備、啟動、管理和結束。
這是連接戰鬥系統核心邏輯和用戶界面的關鍵服務。
"""

import logging
import random
from typing import Any, Dict, List

from rpg_system.battle_system.models.battle import Battle
from rpg_system.battle_system.models.combatant import Combatant
from rpg_system.battle_system.services import attribute_calculator
from rpg_system.config.loader import get_config_loader
from rpg_system.exceptions import BattleError, FloorNotUnlockedError
from rpg_system.repositories import player_collection_repository
from rpg_system.services import card_instance_service, user_progress_service

logger = logging.getLogger(__name__)


async def prepare_pve_battle(user_id: int, floor_id: str) -> "Battle":
    """
    準備PVE戰鬥
    """
    config_loader = get_config_loader()
    floor_config = await config_loader.get_floor_config(floor_id)
    if not floor_config:
        raise BattleError(f"樓層 {floor_id} 的配置不存在。")

    user_progress = await user_progress_service.get_user_progress(user_id)
    current_floor_unlocked = user_progress.get("current_floor_unlocked", 1)
    if current_floor_unlocked < int(floor_id):
        raise FloorNotUnlockedError(int(floor_id), current_floor_unlocked)

    if not floor_config.possible_encounters:
        raise BattleError(f"樓層 {floor_id} 沒有配置任何怪物。")

    monster_group_id = random.choice(floor_config.possible_encounters)
    monster_group_config = await config_loader.get_monster_group_config(
        monster_group_id
    )
    if not monster_group_config:
        raise BattleError(f"怪物組配置 {monster_group_id} 不存在。")

    player_team = await _create_player_team(user_id)
    monster_team = await _create_monster_team(monster_group_config)

    battle = Battle(
        battle_id=f"pve_{user_id}_{floor_id}_{random.randint(1000, 9999)}",
        player_team=player_team,
        monster_team=monster_team,
    )

    logger.info(
        "戰鬥準備完成: %s, 玩家隊伍: %s, 怪物隊伍: %s",
        battle.battle_id,
        len(player_team),
        len(monster_team),
    )
    return battle


async def start_battle(battle: "Battle") -> "Battle":
    """
    啟動戰鬥
    """
    await battle.start(get_config_loader(), attribute_calculator)
    logger.info("戰鬥已啟動: %s", battle.battle_id)
    return battle


async def process_player_action(
    battle: "Battle",
    acting_player_combatant_id: str,
    skill_id: str,
    target_combatant_ids: List[str],
) -> "Battle":
    """
    處理玩家行動
    """
    caster = battle.get_combatant_by_id(acting_player_combatant_id)
    if not caster:
        raise BattleError(f"找不到戰鬥單位: {acting_player_combatant_id}")

    if not caster.is_player_side:
        raise BattleError("只能控制玩家方戰鬥單位")

    current_actor = await battle.get_acting_combatant()
    if not current_actor or current_actor.instance_id != acting_player_combatant_id:
        raise BattleError("不是當前行動者的回合")

    await battle.process_action(caster, skill_id, target_combatant_ids)
    logger.info("玩家行動處理完成: %s 使用 %s", acting_player_combatant_id, skill_id)
    return battle


async def handle_pve_battle_completion(
    user_id: int, battle: "Battle", floor_id: str
) -> Dict[str, Any]:
    """
    處理PVE戰鬥完成
    """
    from rpg_system.battle_system.models.battle import BattleStatus

    result = {
        "battle_id": battle.battle_id,
        "battle_status": battle.battle_status.value,
        "is_victory": battle.battle_status == BattleStatus.PLAYER_WIN,
        "is_first_clear": False,
        "rewards": [],
        "floor_id": floor_id,
        "battle_log": [entry.to_dict() for entry in battle.battle_log],
    }

    config_loader = get_config_loader()
    floor_config = await config_loader.get_floor_config(floor_id)
    if not floor_config:
        logger.warning("找不到樓層配置: %s", floor_id)
        return result

    if battle.battle_status == BattleStatus.PLAYER_WIN:
        progress_result = await user_progress_service.handle_battle_victory(
            user_id, int(floor_id)
        )
        result.update(
            {
                "is_first_clear": progress_result.get("is_first_clear", False),
                "floor_unlocked": progress_result.get("floor_unlocked", False),
                "next_floor_unlocked": progress_result.get("next_floor_unlocked"),
                "wins_added": progress_result.get("wins_added", 0),
                "new_max_cleared": progress_result.get("new_max_cleared"),
            }
        )

        if result["is_first_clear"]:
            rewards_key = getattr(floor_config, "first_clear_rewards_key", None)
        else:
            rewards_key = getattr(floor_config, "repeatable_rewards_per_win_key", None)

        if rewards_key:
            try:
                all_rewards = await config_loader.get_config("reward_packages")
                reward_package = all_rewards.get(rewards_key)
                if reward_package:
                    result["rewards"] = reward_package.get("rewards", [])
            except Exception as reward_error:
                logger.warning("獲取獎勵包失敗: %s", reward_error)

        await _process_battle_experience(user_id, battle, floor_config)
        logger.info("玩家 %s 在樓層 %s 獲勝", user_id, floor_id)
    else:
        logger.info("玩家 %s 在樓層 %s 失敗", user_id, floor_id)

    return result


async def _create_player_team(user_id: int) -> List["Combatant"]:
    """
    創建玩家隊伍
    """
    player_cards = await player_collection_repository.get_all_user_cards(user_id)
    if not player_cards:
        raise BattleError("玩家沒有可用的RPG卡牌，請先配置隊伍。")

    test_card = player_cards[0]
    card_id = test_card.get("card_id")
    if not card_id:
        raise BattleError("測試卡牌缺少card_id")

    config_loader = get_config_loader()
    card_config = await config_loader.get_card_config(str(card_id))
    if not card_config:
        raise BattleError(f"找不到卡牌配置: {card_id}")

    skill_instances = _create_skill_instances_for_player_card(test_card, card_config)

    combatant = Combatant(
        instance_id=f"player_{test_card.get('id')}",
        definition_id=str(card_id),
        name=card_config.get("name", "未知卡牌"),
        is_player_side=True,
        rpg_level=test_card.get("rpg_level", 1),
        star_level=test_card.get("star_level", 0),
        skill_order_preference=test_card.get("equipped_active_skill_ids") or [],
        primary_attack_skill=skill_instances.get("primary_attack"),
        active_skills=skill_instances.get("active_skills", []),
        innate_passive=skill_instances.get("innate_passive"),
        common_passives=skill_instances.get("common_passives", []),
        position=0,
    )

    await combatant.calculate_final_stats(config_loader, attribute_calculator)
    logger.info("創建玩家隊伍完成: 1個戰鬥單位")
    return [combatant]


async def _create_monster_team(monster_group_config) -> List["Combatant"]:
    """
    創建怪物隊伍
    """
    monster_team = []
    config_loader = get_config_loader()

    for i, monster_in_group in enumerate(monster_group_config.monsters):
        monster_id = (
            monster_in_group.monster_id
            if hasattr(monster_in_group, "monster_id")
            else monster_in_group
        )

        monster_config = await config_loader.get_monster_config(monster_id)
        if not monster_config:
            raise BattleError(f"找不到怪物配置: {monster_id}")

        skill_instances = _create_skill_instances_for_monster(monster_config)

        combatant = Combatant(
            instance_id=f"monster_{monster_id}_{i}",
            definition_id=monster_id,
            name=monster_config.name,
            is_player_side=False,
            rpg_level=getattr(monster_config, "level", 1),
            star_level=0,
            skill_order_preference=getattr(monster_config, "active_skill_order", []),
            primary_attack_skill=skill_instances.get("primary_attack"),
            active_skills=skill_instances.get("active_skills", []),
            innate_passive=skill_instances.get("innate_passive"),
            common_passives=skill_instances.get("common_passives", []),
            position=i,
        )

        await combatant.calculate_final_stats(config_loader, attribute_calculator)
        monster_team.append(combatant)

    logger.info("創建怪物隊伍完成: %s個戰鬥單位", len(monster_team))
    return monster_team


def _create_skill_instances_for_player_card(player_card, card_config) -> Dict[str, Any]:
    """
    為玩家卡牌創建技能實例
    """
    from rpg_system.battle_system.models.skill_instance import (
        SkillInstance,
        SkillType,
    )

    skill_instances = {
        "primary_attack": None,
        "active_skills": [],
        "innate_passive": None,
        "common_passives": [],
    }

    if (
        hasattr(card_config, "primary_attack_skill_id")
        and card_config.primary_attack_skill_id
    ):
        skill_instances["primary_attack"] = SkillInstance(
            skill_id=card_config.primary_attack_skill_id,
            skill_type=SkillType.PRIMARY_ATTACK,
            current_level=1,
            current_cooldown=0,
        )

    if player_card.get("equipped_active_skill_ids"):
        for skill_id in player_card.get("equipped_active_skill_ids"):
            if skill_id:
                skill_instances["active_skills"].append(
                    SkillInstance(
                        skill_id=skill_id,
                        skill_type=SkillType.ACTIVE,
                        current_level=1,
                        current_cooldown=0,
                    )
                )

    if (
        hasattr(card_config, "innate_passive_skill_id")
        and card_config.innate_passive_skill_id
    ):
        skill_instances["innate_passive"] = SkillInstance(
            skill_id=card_config.innate_passive_skill_id,
            skill_type=SkillType.INNATE_PASSIVE,
            current_level=player_card.get("star_level", 0),
            current_cooldown=0,
        )

    if player_card.get("equipped_common_passives"):
        for (
            _,
            passive_data,
        ) in player_card.get("equipped_common_passives").items():
            if passive_data and isinstance(passive_data, dict):
                skill_id = passive_data.get("skill_id")
                if skill_id:
                    skill_instances["common_passives"].append(
                        SkillInstance(
                            skill_id=skill_id,
                            skill_type=SkillType.PASSIVE,
                            current_level=passive_data.get("level", 1),
                            current_cooldown=0,
                        )
                    )
    return skill_instances


def _create_skill_instances_for_monster(monster_config) -> Dict[str, Any]:
    """
    為怪物創建技能實例
    """
    from rpg_system.battle_system.models.skill_instance import (
        SkillInstance,
        SkillType,
    )

    skill_instances = {
        "primary_attack": None,
        "active_skills": [],
        "innate_passive": None,
        "common_passives": [],
    }

    if (
        hasattr(monster_config, "primary_attack_skill_id")
        and monster_config.primary_attack_skill_id
    ):
        skill_instances["primary_attack"] = SkillInstance(
            skill_id=monster_config.primary_attack_skill_id,
            skill_type=SkillType.PRIMARY_ATTACK,
            current_level=getattr(monster_config, "level", 1),
            current_cooldown=0,
        )

    if (
        hasattr(monster_config, "active_skill_order")
        and monster_config.active_skill_order
    ):
        for skill_id in monster_config.active_skill_order:
            if skill_id:
                skill_instances["active_skills"].append(
                    SkillInstance(
                        skill_id=skill_id,
                        skill_type=SkillType.ACTIVE,
                        current_level=getattr(monster_config, "level", 1),
                        current_cooldown=0,
                    )
                )

    if (
        hasattr(monster_config, "equipped_passives")
        and monster_config.equipped_passives
    ):
        for passive_data in monster_config.equipped_passives:
            if isinstance(passive_data, dict):
                skill_id = passive_data.get("skill_id")
                level = passive_data.get("level", 1)
                if skill_id:
                    skill_instances["common_passives"].append(
                        SkillInstance(
                            skill_id=skill_id,
                            skill_type=SkillType.PASSIVE,
                            current_level=level,
                            current_cooldown=0,
                        )
                    )
    return skill_instances


async def _process_battle_experience(
    user_id: int, battle: "Battle", floor_config
) -> None:
    """
    處理戰鬥後的卡牌經驗獲得和自動分離
    """
    base_exp = getattr(floor_config, "base_exp_reward", 10)
    player_team = battle.player_team

    for combatant in player_team:
        if combatant.is_player_side:
            original_card_id = int(combatant.definition_id)
            final_card_id = await card_instance_service.add_battle_experience(
                user_id, original_card_id, base_exp
            )
            logger.info(
                "戰鬥經驗處理完成: user_id=%s, 原card_id=%s, 最終card_id=%s, 經驗=%s",
                user_id,
                original_card_id,
                final_card_id,
                base_exp,
            )
