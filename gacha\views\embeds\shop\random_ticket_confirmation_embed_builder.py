import discord

from gacha.models.shop_models import ExchangeSessionData


def build_random_ticket_confirmation_embed(
    interaction: discord.Interaction, session_data: ExchangeSessionData
) -> discord.Embed:
    """
    建立隨機券兌換確認的 Embed

    Args:
        interaction: Discord 互動對象
        session_data: 從 ShopService 的 active_sessions 中獲取的會話數據 (ExchangeSessionData)
    """
    ticket_name = session_data.ticket_definition.display_name
    quantity = session_data.total_quantity_to_redeem
    total_oil_ticket_cost = session_data.ticket_definition.cost_oil_tickets * quantity
    embed = discord.Embed(
        title="隨機券兌換確認",
        description="您即將兌換以下隨機券：",
        color=discord.Color.blue(),
    )
    embed.add_field(name="🎫 券名稱", value=ticket_name, inline=False)
    embed.add_field(name="數量", value=f"{quantity} 張", inline=True)
    embed.add_field(name="總花費", value=f"{total_oil_ticket_cost} 油票", inline=True)
    embed.set_footer(text="請確認是否繼續兌換。")
    return embed
