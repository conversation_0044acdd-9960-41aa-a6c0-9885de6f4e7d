"""
被動技能生成器

專門用於生成被動技能的生成器，繼承自基礎技能生成器。
"""

import os
import random
import sys
from typing import Any, Dict, Tuple

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

from utils.logger import logger

from .error_handler import error_handler
from .skill_generator_base import SkillGeneratorBase


class PassiveSkillGenerator(SkillGeneratorBase):
    """被動技能生成器"""

    def __init__(self):
        """初始化被動技能生成器"""
        super().__init__()

        # 被動技能特有的觸發條件類型（1v1戰鬥適用）
        self.trigger_types = [
            "ON_BATTLE_START",
            "ON_TURN_START",
            "ON_TURN_END",
            "ON_DAMAGE_DEALT",
            "ON_DAMAGE_TAKEN",
            "ON_HEAL_RECEIVED",
            "ON_STATUS_EFFECT_APPLIED",
            "ON_HP_THRESHOLD_REACHED",
        ]

        # 初始化共享的輔助方法實例（在 trigger_types 定義之後）
        from .passive_skill_helpers import PassiveSkillHelpers

        self.helpers = PassiveSkillHelpers(self)

        # 被動技能適用的效果模板類型
        self.passive_effect_categories = [
            "stat_boost",
            "heal",
            "shield",
            "status_application",
            "damage",  # 反擊類被動技能
        ]

    def _generate_name_by_template(
        self, template_name: str, rarity: int, variant_type: str = "medium_effect"
    ) -> str:
        """根據效果模板名稱和變體類型生成匹配的技能名稱

        Args:
            template_name: 模板名稱
            rarity: 稀有度等級
            variant_type: 變體類型 (small_effect, medium_effect, large_effect)

        Returns:
            匹配的技能名稱
        """
        # 從 config/skill_name_library.json 獲取模板名稱映射
        template_names = self.name_library.get("template_names", {})
        rarity_prefixes = self.name_library.get("rarity_prefixes", {})
        fallback_names = self.name_library.get(
            "fallback_names", ["被動強化", "自動觸發", "戰鬥本能"]
        )

        # 獲取模板配置
        template_config = template_names.get(template_name)

        if template_config and isinstance(template_config, dict):
            # 根據變體類型選擇名稱
            variant_names = template_config.get(variant_type)
            if variant_names:
                chosen_name = random.choice(variant_names)
            else:
                # 如果沒有對應變體，使用 medium_effect 作為默認
                medium_names = template_config.get("medium_effect", fallback_names)
                chosen_name = random.choice(medium_names)
        else:
            # 沒有找到模板配置，使用回退名稱
            chosen_name = random.choice(fallback_names)

        # 根據稀有度添加前綴
        rarity_str = str(rarity)
        if rarity_str in rarity_prefixes:
            prefixes = rarity_prefixes[rarity_str]
            chosen_name = f"{random.choice(prefixes)}{chosen_name}"

        return chosen_name

    @error_handler(
        default_return=lambda: ("unknown", "APPLY_ATK_BOOST"),
        log_message="選擇效果模板時出錯",
    )
    def select_effect_template(
        self, effect_category: str, rarity: int
    ) -> Tuple[str, str]:
        """選擇效果模板

        Args:
            effect_category: 效果分類
            rarity: 稀有度等級

        Returns:
            (模板鍵, 模板名稱) 的元組
        """
        return self.template_processor.select_template(effect_category, rarity)

    @error_handler(default_return=lambda: ({}), log_message="創建效果定義時出錯")
    def create_effect_definition(
        self,
        template_name: str,
        effect_category: str,
        rarity: int,
        variant_multiplier: float = 1.0,
    ) -> Dict[str, Any]:
        """創建效果定義 - 重構版，支持變體倍率

        Args:
            template_name: 模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            variant_multiplier: 變體倍率（默認1.0，無變體）

        Returns:
            效果定義字典
        """
        return self.template_processor.create_effect_definition(
            template_name, effect_category, rarity, variant_multiplier
        )

    def _get_category_from_template_name(self, template_name: str) -> str:
        """從緩存中獲取模板分類

        Args:
            template_name: 模板名稱

        Returns:
            模板分類
        """
        return self.get_template_category(template_name)

    @error_handler(
        default_return=lambda: {"type": "ON_TURN_START", "chance_formula": "1.0"},
        log_message="生成觸發條件時出錯",
    )
    def generate_trigger_condition(self, rarity: int) -> Dict[str, Any]:
        """生成觸發條件

        Args:
            rarity: 稀有度等級

        Returns:
            觸發條件字典
        """
        trigger_type = random.choice(self.trigger_types)

        # 基礎觸發條件
        trigger_condition: Dict[str, Any] = {
            "type": trigger_type,
            "chance_formula": "1.0",
        }

        # 根據觸發類型添加特殊參數
        if trigger_type == "ON_HP_THRESHOLD_REACHED":
            # HP閾值觸發
            threshold_percent = random.choice([0.25, 0.5, 0.75])
            trigger_condition["params"] = {
                "threshold_percent_formula": str(threshold_percent),
                "check_direction": "below",
            }
            trigger_condition["trigger_once_per_battle"] = True

        elif trigger_type in ["ON_DAMAGE_DEALT", "ON_DAMAGE_TAKEN"]:
            # 傷害相關觸發，可能有子類型
            if random.random() < 0.3:  # 30% 機率有子類型
                damage_types = ["PHYSICAL", "MAGICAL"]
                trigger_condition["sub_type"] = random.choice(damage_types)

        elif trigger_type == "ON_STATUS_EFFECT_APPLIED":
            # 狀態效果觸發，降低觸發機率
            base_chance = 0.3 + (rarity - 1) * 0.1
            trigger_condition["chance_formula"] = f"{base_chance:.2f}"

        # 根據稀有度調整觸發機率
        if trigger_type not in ["ON_BATTLE_START", "ON_HP_THRESHOLD_REACHED"]:
            if random.random() < 0.2:  # 20% 機率有機率限制
                base_chance = 0.5 + (rarity - 1) * 0.1
                max_chance = min(1.0, base_chance)
                trigger_condition["chance_formula"] = f"{max_chance:.2f}"

        return trigger_condition

    @error_handler(
        default_return=lambda rarity=1: {
            "name": f"ErrorPassiveSkill_R{rarity}_{random.randint(100, 999)}",
            "description_template": "被動技能生成錯誤，請檢查日誌。",
            "skill_rarity": rarity,
            "max_level": 1,
            "base_effects": [],
            "xp_gain_on_sacrifice": 1,
            "xp_to_next_level_config": {"base_xp": 100, "multiplier": 2},
            "tags": ["ERROR", "PASSIVE"],
        },
        log_message="創建被動技能配置時出錯",
    )
    def generate_skill_config(self, rarity: int) -> Dict[str, Any]:
        """創建被動技能配置，使用新的組合池系統

        Args:
            rarity: 稀有度等級

        Returns:
            被動技能配置字典
        """
        # 選擇效果分類（用於初始模板選擇，但可能會被組合池系統覆蓋）
        effect_category = random.choice(self.passive_effect_categories)

        # 選擇效果模板（用於初始選擇，但可能會被組合池系統覆蓋）
        _, template_name = self.select_effect_template(effect_category, rarity)

        # 使用組合池系統生成技能（不傳入名稱，讓系統內部根據最終模板生成）
        return self.helpers.generate_passive_skill_with_variants(
            template_name, effect_category, rarity, None
        )

    def generate_all_skills(
        self, skills_per_rarity: int = 5
    ) -> Dict[str, Dict[str, Any]]:
        """生成所有稀有度的被動技能配置

        Args:
            skills_per_rarity: 每個稀有度生成的技能數量

        Returns:
            所有被動技能配置字典
        """
        passive_skills = {}
        total_skills = 0

        # 生成被動技能
        for rarity in range(1, 8):
            rarity_bucket = f"rarity_{rarity}"
            if rarity_bucket not in passive_skills:
                passive_skills[rarity_bucket] = {}
            for _ in range(skills_per_rarity):
                max_attempts = 20  # 每個技能最多嘗試20次
                skill_generated = False

                for _ in range(max_attempts):
                    # 創建技能配置
                    skill_config = self.generate_skill_config(rarity)

                    # 使用描述性ID
                    skill_id = self.helpers.generate_descriptive_skill_id(skill_config)

                    # 檢查是否已存在相同ID
                    if skill_id not in passive_skills[rarity_bucket]:
                        passive_skills[rarity_bucket][skill_id] = skill_config
                        total_skills += 1
                        skill_generated = True
                        break
                    # 技能ID重複，繼續嘗試生成

                # 如果所有嘗試都失敗，使用序號後綴
                if not skill_generated:
                    logger.warning("無法生成唯一技能ID，使用序號後綴")
                    skill_config = self.generate_skill_config(rarity)
                    base_skill_id = self.helpers.generate_descriptive_skill_id(
                        skill_config
                    )
                    counter = 1
                    skill_id = f"{base_skill_id}_{counter}"
                    while skill_id in passive_skills[rarity_bucket]:
                        counter += 1
                        skill_id = f"{base_skill_id}_{counter}"

                    passive_skills[rarity_bucket][skill_id] = skill_config
                    total_skills += 1

        logger.info("總共生成了 %s 個被動技能。", total_skills)
        return {"passive_skills": passive_skills}

    def save_skills_to_files(self, all_skills: Dict[str, Dict[str, Any]]):
        """保存所有被動技能到配置文件

        Args:
            all_skills: 所有被動技能配置字典
        """
        try:
            import json

            # 保存被動技能
            passive_skills = all_skills.get("passive_skills", {})

            # 將稀有度分組的技能合併為單一字典
            merged_passive_skills = {}
            for _, skills in passive_skills.items():
                merged_passive_skills.update(skills)

            passive_skills_file = os.path.join(self.output_dir, "passive_skills.json")
            with open(passive_skills_file, "w", encoding="utf-8") as f:
                json.dump(merged_passive_skills, f, ensure_ascii=False, indent=2)

            logger.info("被動技能已保存到: %s", passive_skills_file)
            print(f"✅ 被動技能已保存到: {passive_skills_file}")

            # 統計信息
            total_passive_skills = len(merged_passive_skills)
            print(f"📊 總共生成了 {total_passive_skills} 個被動技能")

            # 按稀有度統計
            rarity_counts = {}
            for skill_config in merged_passive_skills.values():
                rarity = skill_config.get("skill_rarity", 1)
                rarity_counts[rarity] = rarity_counts.get(rarity, 0) + 1

            print("📈 按稀有度統計:")
            for rarity in sorted(rarity_counts.keys()):
                count = rarity_counts[rarity]
                print(f"   稀有度 {rarity}: {count} 個技能")

        except Exception as e:
            logger.error("保存被動技能文件時出錯: %s", e, exc_info=True)
            print(f"❌ 保存被動技能文件時出錯: {e}")
