# Pioneer System 動作配置
# 統一的動作定義，支援數據驅動的遊戲邏輯

# 採集動作
gather_wood:
  name: "伐木"
  type: "gather"
  energy_cost: 10
  requirements:
    - type: "skill_level"
      skill: "woodcutting"
      min_level: 1
  inputs: []
  outputs:
    - skill: "woodcutting"
      level_range: [1, 9]
      items:
        wood:
          amount: [1, 3]
          chance: 1.0
          xp: 1
    - skill: "woodcutting"
      level_range: [10, 24]
      items:
        wood:
          amount: [2, 4]
          chance: 1.0
          xp: 1
        hardwood:
          amount: [1, 1]
          chance: 0.1
          xp: 5
    - skill: "woodcutting"
      level_range: [25, 999]
      items:
        wood:
          amount: [3, 5]
          chance: 1.0
          xp: 1
        hardwood:
          amount: [1, 2]
          chance: 0.2
          xp: 5

gather_mine:
  name: "採礦"
  type: "gather"
  energy_cost: 12
  requirements:
    - type: "skill_level"
      skill: "mining"
      min_level: 1
  inputs: []
  outputs:
    - skill: "mining"
      level_range: [1, 4]
      items:
        stone:
          amount: [1, 2]
          chance: 1.0
          xp: 1
        flint:
          amount: [1, 1]
          chance: 0.03
          xp: 2
    - skill: "mining"
      level_range: [5, 14]
      items:
        stone:
          amount: [1, 3]
          chance: 1.0
          xp: 1
        flint:
          amount: [1, 1]
          chance: 0.03
          xp: 2
        copper_ore:
          amount: [1, 1]
          chance: 0.2
          xp: 3
    - skill: "mining"
      level_range: [15, 999]
      items:
        stone:
          amount: [2, 3]
          chance: 1.0
          xp: 1
        flint:
          amount: [1, 1]
          chance: 0.03
          xp: 2
        copper_ore:
          amount: [1, 2]
          chance: 0.3
          xp: 3
        coal:
          amount: [1, 1]
          chance: 0.05
          xp: 5

# 設施建造動作 (通用)
build_facility:
  name: "建造設施"
  type: "build_facility"
  energy_cost: 50  # 建造是一個重要行為，需要消耗能量
  # 具體的需求 (物品, 貨幣) 將由 BuildFacilityProcessor 根據 facilities.yaml 動態檢查
  requirements:
    - type: "skill_level"
      skill: "construction"
      min_level: 1
  # 輸入也由 Processor 動態處理
  inputs: []
  # 輸出也由 Processor 動態處理
  outputs: []

# 設施升級動作
upgrade_facility_level:
  name: "升級設施等級"
  type: "upgrade_facility"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs:
    - type: "facility_upgrade"
      upgrade_type: "level"

upgrade_facility_auto_output:
  name: "解鎖自動輸出"
  type: "upgrade_facility"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs:
    - type: "facility_upgrade"
      upgrade_type: "auto_output"

upgrade_facility_auto_input:
  name: "解鎖自動輸入"
  type: "upgrade_facility"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs:
    - type: "facility_upgrade"
      upgrade_type: "auto_input"

# 通用研究動作
research:
  name: "進行研究"
  type: "research"
  energy_cost: 0 # 研究通常不消耗能量，而是消耗大量貨幣
  # Requirements are checked inside the processor based on the specific project
  requirements: []
  # Inputs/costs are also handled by the processor
  inputs: []
  # Outputs are the research level itself, handled by the processor
  outputs: []

# 研究動作 (保留用於向後兼容)
research_production_efficiency:
  name: "研究生產效率"
  type: "research"
  energy_cost: 0
  requirements:
    - type: "era"
      min_era: 2
  inputs: []
  outputs:
    - type: "research_level"
      project_id: "production_efficiency"

research_gathering_mastery:
  name: "研究採集精通"
  type: "research"
  energy_cost: 0
  requirements:
    - type: "era"
      min_era: 2
  inputs: []
  outputs:
    - type: "research_level"
      project_id: "gathering_mastery"

research_economic_optimization:
  name: "研究經濟優化"
  type: "research"
  energy_cost: 0
  requirements:
    - type: "era"
      min_era: 3
  inputs: []
  outputs:
    - type: "research_level"
      project_id: "economic_optimization"

# 通用製作動作
craft:
  name: "製作"
  type: "craft"
  # energy_cost 字段已移除，默認為0，製作不消耗能量
  requirements: []
  inputs: []
  outputs: []

# 收益相關動作
collect_earnings:
  name: "收取收益"
  type: "collect_earnings"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs: []

check_earnings:
  name: "查看收益"
  type: "check_earnings"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs: []

# 設施銷售動作
facility_sell:
  name: "商店銷售"
  type: "facility_sell"
  energy_cost: 0
  requirements: []
  inputs: []
  outputs: []
