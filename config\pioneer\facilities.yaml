# Pioneer System 設施配置
# 統一的設施定義，萬物皆設施的核心配置
  # 類型一：採集設施 (無中生有型)
  lumber_mill:
    name: "伐木小屋"
    is_buildable: true
    cost_oil: 200000
    required_items:
      - item_id: "wooden_plank"
        quantity: 100
      - item_id: "stone_brick"
        quantity: 50
    process_type: "generate"
    process_time: 3600        # 1小時

    inputs: []  # 無輸入需求
    outputs:
      - item_id: "wood"
        quantity: 20

    upgrades:
      level:
        cost_formula: "50000 * (1.25 ** level)"
        effect: "output * 1.15"
        description: "提升產出效率15%"
      auto_output:
        cost_oil: 250000
        description: "自動收集產品到主倉庫"
      capacity:
        cost_oil: 100000
        effect: "max_capacity * 1.5"
        description: "擴展輸出槽容量50%"

  quarry:
    name: "採石場"
    process_type: "generate"
    process_time: 4200        # 70分鐘
    cost_oil: 250000        # 25萬油幣

    inputs: []
    outputs:
      - item_id: "stone"
        quantity: 15

    upgrades:
      level:
        cost_formula: "60000 * (1.3 ** level)"
        effect: "output * 1.12"
        description: "提升產出效率12%"
      auto_output:
        cost_oil: 300000
        description: "自動收集產品到主倉庫"

  # 類型二：加工設施 (轉化型)
  forge:
    name: "熔爐"
    is_buildable: true
    cost_oil: 300000
    required_items:
      - item_id: "stone_brick"
        quantity: 200
      - item_id: "copper_ingot"
        quantity: 50
    process_type: "transform"
    process_time: 600         # 10分鐘

    inputs:
      - slot_type: "material"
        item_id: "copper_ore"
        quantity: 2
      - slot_type: "fuel"
        item_id: "coal"
        quantity: 1

    outputs:
      - item_id: "copper_ingot"
        quantity: 1

    upgrades:
      level:
        cost_formula: "80000 * (1.3 ** level)"
        effect: "time * 0.9"
        description: "減少處理時間10%"
      auto_input_material:
        cost_oil: 600000
        description: "自動從倉庫補充原料"
      auto_input_fuel:
        cost_oil: 300000
        description: "自動從倉庫補充燃料"
      auto_output:
        cost_oil: 800000
        description: "自動收集產品到主倉庫"
      efficiency:
        cost_oil: 50000
        effect: "output * 1.2"
        description: "提升產出效率20%"

  woodworking_bench:
    name: "木工台"
    process_type: "transform"
    process_time: 300         # 5分鐘
    cost_oil: 150000        # 15萬油幣

    inputs:
      - slot_type: "material"
        item_id: "wood"
        quantity: 1

    outputs:
      - item_id: "wooden_plank"
        quantity: 1

    upgrades:
      level:
        cost_formula: "400000 * (1.2 ** level)"
        effect: "time * 0.85"
        description: "減少處理時間15%"
      auto_input_material:
        cost_oil: 200000
        description: "自動從倉庫補充木材"
      auto_output:
        cost_oil: 250000
        description: "自動收集產品到主倉庫"

  # 類型三：銷售設施 (商店本身就是一個設施！)
  pioneer_shop:
    name: "開拓者商店"
    process_type: "sell"
    process_time: 60          # 1分鐘結算週期
    cost_oil: 100000        # 10萬油幣

    inputs:
      - slot_type: "shelf"
        capacity: 100
        filter: "any_product"

    outputs:
      - currency: "oil"
        formula: "price * sold_quantity"

    upgrades:
      level:
        cost_formula: "200000 * (1.5 ** level)"
        effect: "add_shelf_slot"
        description: "增加一個貨架槽位"
      auto_input_shelf_1:
        cost_oil: 400000
        description: "為1號貨架解鎖自動補貨"
      auto_input_shelf_2:
        cost_oil: 400000
        description: "為2號貨架解鎖自動補貨"
      auto_input_shelf_3:
        cost_oil: 400000
        description: "為3號貨架解鎖自動補貨"
      auto_output:
        cost_oil: 1000000
        description: "自動收款到油幣餘額"
      marketing:
        cost_oil: 75000
        effect: "sell_rate * 1.3"
        description: "提升銷售速度30%"

  # 類型四：研究設施
  research_lab:
    name: "研究實驗室"
    process_type: "research"
    process_time: 1800        # 30分鐘
    cost_oil: 500000        # 50萬油幣

    inputs:
      - slot_type: "research_material"
        capacity: 50
        filter: "research_items"

    outputs:
      - item_id: "research_points"
        quantity: 1

    upgrades:
      level:
        cost_formula: "1000000 * (2.0 ** level)"
        effect: "research_speed * 1.25"
        description: "提升研究速度25%"
      auto_input:
        cost_oil: 100000
        description: "自動消耗研究材料"
      advanced_equipment:
        cost_oil: 200000
        effect: "research_efficiency * 1.5"
        description: "提升研究效率50%"

  # 類型五：存儲設施
  warehouse:
    name: "倉庫"
    is_buildable: true        # 修復：添加可建造標記
    process_type: "storage"   # 恢復：倉庫應該是 storage 類型
    process_time: 1           # 修復：process_time 必須 >= 1（即使是存儲也需要處理時間）
    cost_oil: 80000           # 修復：統一使用 cost_oil 而不是 build_cost

    inputs:
      - slot_type: "storage"
        capacity: 1000
        filter: "any_item"

    outputs: []

    upgrades:
      level:
        cost_formula: "300000 * (1.4 ** level)"
        effect: "capacity * 1.5"
        description: "擴展存儲容量50%"
      sorting_system:
        cost_oil: 150000
        description: "自動分類存儲物品"

  # 類型六：高級設施
  advanced_assembly_line:
    name: "高級裝配線"
    process_type: "transform"
    process_time: 1200        # 20分鐘
    cost_oil: 1000000       # 100萬油幣

    inputs:
      - slot_type: "component_1"
        capacity: 200
      - slot_type: "component_2"
        capacity: 200
      - slot_type: "component_3"
        capacity: 200

    outputs:
      - item_id: "advanced_product"
        quantity: 1

    upgrades:
      level:
        cost_formula: "2000000 * (1.8 ** level)"
        effect: "efficiency * 1.3"
        description: "提升整體效率30%"
      full_automation:
        cost_oil: 500000
        description: "完全自動化生產線"
      quality_control:
        cost_oil: 300000
        effect: "quality_bonus * 1.25"
        description: "提升產品品質25%"

  power_plant:
    name: "發電廠"
    process_type: "generate"
    process_time: 1800        # 30分鐘
    cost_oil: 1500000       # 150萬油幣

    inputs:
      - slot_type: "fuel"
        item_id: "coal"
        quantity: 10

    outputs:
      - item_id: "electricity"
        quantity: 100

    upgrades:
      level:
        cost_formula: "3000000 * (2.0 ** level)"
        effect: "output * 1.4"
        description: "提升發電量40%"
      efficiency_upgrade:
        cost_oil: 800000
        effect: "fuel_consumption * 0.8"
        description: "減少燃料消耗20%"
      renewable_energy:
        cost_oil: 1000000
        effect: "remove_fuel_requirement"
        description: "轉換為可再生能源，無需燃料"
