# gacha/services/common/validation_service.py
from __future__ import annotations

from typing import List, Optional

import asyncpg

from gacha.exceptions import (
    CardNotFoundError,
    ConfigurationError,
    InvalidPoolTypeError,
)

# 假設的路徑，後續可能需要調整
from gacha.models.models import Card, GachaUser
from gacha.repositories.card import master_card_repository
from gacha.services import user_service


async def ensure_user_exists(
    user_id: int,
    nickname: Optional[str] = None,  # 在創建用戶時可能需要
    create_if_missing: bool = False,
    connection: Optional[asyncpg.Connection] = None,
) -> GachaUser:
    """
    Ensures a user exists. If not, and create_if_missing is True, creates the user.
    Otherwise, raises UserNotFoundError.
    """
    return await user_service.get_user(
        user_id,
        create_if_missing=create_if_missing,
        nickname=nickname,
        connection=connection,
    )


async def ensure_card_exists(
    card_id: int,
    connection: Optional[asyncpg.Connection] = None,  # 卡片主數據通常不需要事務連接
) -> Card:
    """
    Ensures a card exists in the master card table.
    Raises CardNotFoundError if the card does not exist.
    """
    # 實現細節：
    # 1. 嘗試使用 master_card_repository.get_card() 獲取卡片。
    # 2. 如果卡片不存在，拋出 CardNotFoundError。
    # 3. 如果存在，返回卡片對象。
    card = await master_card_repository.get_card(
        card_id
    )  # connection is usually not needed for master data
    if card is None:
        raise CardNotFoundError(
            f"Card with card_id: {card_id} not found.", card_id=card_id
        )
    return card


def validate_pool_types(pool_types: List[str]) -> List[str]:
    """
    Validates if the given pool types are known/configured.
    Returns a list of valid pool types.
    Raises InvalidPoolTypeError if no valid pool types are found or if any type is invalid.
    """
    # 延遲導入避免循環導入
    from config.app_config import get_pool_type_names

    known_pool_types = get_pool_type_names()
    if not known_pool_types:  # 防禦性程式碼，如果配置中沒有卡池名稱
        raise ConfigurationError("No pool types are configured in the system.")

    valid_input_pool_types = [
        ptype for ptype in pool_types if ptype in known_pool_types
    ]

    if not valid_input_pool_types:
        # 如果用戶提供的所有卡池類型都不在已知類型中
        raise InvalidPoolTypeError(
            f"None of the provided pool types are valid. Provided: {pool_types}, Known: {list(known_pool_types)}",
            pool_type=pool_types,
        )

    # 檢查是否所有提供的卡池類型都是有效的
    if len(valid_input_pool_types) != len(set(pool_types)):
        invalid_ones = [ptype for ptype in pool_types if ptype not in known_pool_types]
        raise InvalidPoolTypeError(
            f"Some provided pool types are invalid: {invalid_ones}. Known: {list(known_pool_types)}",
            pool_type=invalid_ones,
        )

    return valid_input_pool_types
