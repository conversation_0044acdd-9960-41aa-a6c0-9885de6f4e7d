"""
摸頭動畫處理模塊
實現將用戶頭像與摸頭動畫結合的功能
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import List, Optional, Tuple

from PIL import Image

from utils.logger import logger

from .avatar_gif_overlay import (
    IMAGEIO_AVAILABLE,
    create_global_palette_and_convert,
    fetch_avatar,
    save_gif,
)
from .config import get_gif_config


def get_resampling_filter(filter_name: str):
    """獲取 Pillow 重採樣濾鏡，兼容新舊版本"""
    try:
        # Pillow 10.0.0+ 使用 Image.Resampling
        return getattr(Image.Resampling, filter_name)
    except AttributeError:
        # 舊版本 Pillow 直接從 Image 獲取
        return getattr(Image, filter_name, 1)  # 1 是 LANCZOS 的默認值


def calculate_frame_transform(
    frame_index: int, config: dict
) -> Tuple[int, int, int, int]:
    """計算指定幀的頭像變形參數"""
    petpet_config = config["petpet_phase"]
    squish = petpet_config["squish"]
    scale = petpet_config["scale"]
    frame_offsets = petpet_config["frame_offsets"]
    sprite_base_pos = petpet_config["sprite_base_position"]
    avatar_size = config["avatar_size"]

    # 獲取當前幀的偏移參數
    if frame_index < len(frame_offsets):
        x_offset, y_offset, width_change, height_change = frame_offsets[frame_index]
    else:
        x_offset = y_offset = width_change = height_change = 0

    # 計算實際繪製參數
    x_position = sprite_base_pos[0] + (x_offset * (squish * 0.4))
    y_position = sprite_base_pos[1] + (y_offset * (squish * 0.9))
    width = (avatar_size[0] + (width_change * squish)) * scale
    height = (avatar_size[1] + (height_change * squish)) * scale

    return (int(x_position), int(y_position), int(width), int(height))


def load_hand_frames(config: dict) -> List[Image.Image]:
    """載入手部動畫幀"""
    source_pattern = config["source_path_pattern"]
    total_frames = config["total_source_frames"]
    output_size = config["output_gif_size"]

    hand_frames = []
    for i in range(1, total_frames + 1):  # 1-5
        frame_path = Path(source_pattern.format(i))
        if frame_path.exists():
            hand_frame = Image.open(frame_path).convert("RGBA")
            hand_frames.append(hand_frame)
        else:
            # 創建透明圖像作為佔位符
            placeholder = Image.new("RGBA", output_size, (0, 0, 0, 0))
            hand_frames.append(placeholder)

    return hand_frames


def _generate_single_petpet_frame(frame_data):
    """生成單個摸頭幀的輔助函數，用於並行處理"""
    frame_index, avatar_pil, config, hand_frame, output_size = frame_data

    # 創建新的幀畫布
    frame_canvas = Image.new("RGBA", output_size, (0, 0, 0, 0))

    # 計算當前幀的頭像變形參數
    x_pos, y_pos, width, height = calculate_frame_transform(frame_index, config)

    # 調整頭像大小和位置
    transformed_avatar = avatar_pil.resize(
        (width, height), get_resampling_filter("LANCZOS")
    )

    # 將變形後的頭像貼到畫布上（底層）
    frame_canvas.paste(transformed_avatar, (x_pos, y_pos), transformed_avatar)

    # 調整手部圖像尺寸
    if hand_frame.size != output_size:
        hand_frame = hand_frame.resize(output_size, get_resampling_filter("LANCZOS"))

    # 將手部圖像疊加到畫布上（頂層，覆蓋頭像）
    frame_canvas = Image.alpha_composite(frame_canvas, hand_frame)

    return frame_canvas


async def generate_petpet_frames(avatar_pil, config):
    """
    生成摸頭動畫的所有幀

    參數:
        avatar_pil (PIL.Image): 用戶頭像圖像
        config (dict): 摸頭動畫配置

    返回:
        list: 處理後的動畫幀列表
    """
    output_size = config["output_gif_size"]
    total_frames = config["total_source_frames"]

    # 載入手部動畫幀
    hand_frames = load_hand_frames(config)

    if len(hand_frames) != total_frames:
        raise ValueError(
            "手部動畫幀數量不匹配: 期望 %s, 實際 %s" % (total_frames, len(hand_frames))
        )

    # 並行生成所有動畫幀
    if total_frames <= 3:
        # 幀數較少時直接順序處理
        all_frames = []
        for frame_index in range(total_frames):
            frame_data = (
                frame_index,
                avatar_pil,
                config,
                hand_frames[frame_index],
                output_size,
            )
            all_frames.append(_generate_single_petpet_frame(frame_data))
    else:
        # 使用線程池並行處理
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=min(4, total_frames)) as executor:
            frame_data_list = [
                (frame_index, avatar_pil, config, hand_frames[frame_index], output_size)
                for frame_index in range(total_frames)
            ]
            all_frames = await loop.run_in_executor(
                executor,
                lambda: list(map(_generate_single_petpet_frame, frame_data_list)),
            )

    return all_frames


async def overlay_petpet_gif(
    gif_path_identifier: str,
    user,
    output_path: Optional[str] = None,
    use_fast_save: bool = False,
) -> str:
    """處理摸頭GIF的疊加邏輯"""
    try:
        # 獲取配置和頭像
        config = get_gif_config(gif_path_identifier)
        avatar_img = await fetch_avatar(user)
        if not avatar_img:
            raise ValueError("無法獲取用戶頭像")

        # 將PIL操作移到線程中避免阻塞
        if avatar_img:
            avatar_img = await asyncio.to_thread(
                lambda: avatar_img.convert("RGBA") if avatar_img else None
            )
        else:
            raise ValueError("無法獲取用戶頭像")

        # 生成所有動畫幀
        processed_frames = await generate_petpet_frames(avatar_img, config)
        if not processed_frames:
            raise ValueError("未能為 petpet 生成任何影格")

        # 創建輸出路徑 (移到線程中避免阻塞)
        if not output_path:
            output_dir = Path("auxiliary") / "services" / "image_processing" / "temp"
            await asyncio.to_thread(output_dir.mkdir, parents=True, exist_ok=True)
            output_path = str(output_dir / ("petpet_%s.gif" % user.id))

        # 根據選擇的保存方法處理
        if use_fast_save:
            # 嘗試使用imageio快速保存
            try:
                if IMAGEIO_AVAILABLE:
                    frame_duration = config.get("frame_duration", 60)
                    if not isinstance(frame_duration, int):
                        frame_duration = 60
                    await asyncio.to_thread(
                        save_gif,
                        processed_frames,
                        output_path,
                        frame_duration,
                        0,
                        use_imageio=True,
                    )
                else:
                    raise ImportError("imageio不可用")
            except ImportError:
                logger.info("imageio不可用，摸頭動畫回退到標準保存...")
                use_fast_save = False

        if not use_fast_save:
            # 使用標準方法：轉換調色板並保存 (移到線程中避免阻塞)
            converted_frames = await asyncio.to_thread(
                create_global_palette_and_convert, processed_frames
            )
            if not converted_frames:
                raise ValueError("converted_frames 為空，無法保存 GIF")
            frame_duration = config.get("frame_duration", 60)
            if not isinstance(frame_duration, int):
                frame_duration = 60
            await asyncio.to_thread(
                save_gif,
                converted_frames,
                output_path,
                frame_duration,
            )

        logger.info("摸頭GIF處理完成，返回路徑: %s", output_path)
        return output_path

    except Exception as e:
        raise ValueError("摸頭GIF處理失敗: %s" % e) from e
