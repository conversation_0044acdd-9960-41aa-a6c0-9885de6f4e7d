-- Migration to remove the inefficient command stats trigger and function.
-- This is replaced by a periodic background task to update the summary table.

-- Step 1: Drop the trigger from the command_usage_stats table.
DROP TRIGGER IF EXISTS trigger_update_command_stats_summary ON public.command_usage_stats;

-- Step 2: Drop the associated function that is no longer needed.
DROP FUNCTION IF EXISTS public.update_command_stats_summary();