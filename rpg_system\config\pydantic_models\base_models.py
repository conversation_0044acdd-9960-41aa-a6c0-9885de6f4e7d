"""
RPG配置的基礎Pydantic模型
"""

from typing import Any, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class ModifierCondition(BaseModel):
    """修正器條件模型"""

    source_combatant: Literal["caster", "target"]
    check: str  # e.g., "has_status_effect", "hp_below_percent"
    value: Optional[Any] = None
    status_effect_id: Optional[str] = None


class ModifierConditionGroup(BaseModel):
    """修正器條件組模型"""

    type: Optional[Literal["AND", "OR"]] = "AND"
    conditions: List[ModifierCondition]


class Modifier(BaseModel):
    """傷害修正器模型"""

    modifier_type: str  # e.g., "SCALING_MODIFIER", "CONDITIONAL_BOOST"

    # SCALING_MODIFIER 參數
    scaling_source: Optional[str] = None
    source_combatant: Optional[Literal["caster", "target"]] = None
    scaling_type: Optional[str] = None
    scaling_factor: Optional[float] = None
    max_bonus_value: Optional[float] = None
    apply_as_damage_reduction: Optional[bool] = False

    # CONDITIONAL_BOOST 參數
    condition_group: Optional[ModifierConditionGroup] = None
    bonus_type: Optional[str] = None
    bonus_value: Optional[float] = None

    @field_validator("source_combatant", mode="after")
    @classmethod
    def check_source_combatant_for_scaling_modifier(cls, v, info):
        if info.data.get("modifier_type") == "SCALING_MODIFIER" and v is None:
            raise ValueError(
                'source_combatant is required for modifier_type "SCALING_MODIFIER"'
            )
        return v


class StatModificationItem(BaseModel):
    """屬性修改項模型"""

    stat_name: str
    modification_type: str  # e.g., "PERCENTAGE_ADD", "FLAT_ADD"
    value_formula: str


class EffectDefinition(BaseModel):
    """效果定義模型"""

    effect_template: Optional[str] = None

    # 統一使用字符串支持公式，系統會自動處理純數值字符串
    multiplier: Optional[str] = None
    can_crit: Optional[bool] = None
    duration_turns: Optional[str] = None
    chance: Optional[str] = None
    stack_count: Optional[int] = Field(None, ge=1)

    effect_type: Optional[str] = None

    # Flags for custom hit tracking
    counts_for_combo: Optional[bool] = False  # For consecutive_hits
    counts_for_skill_hit_counter: Optional[bool] = (
        False  # For skill_target_hit_counters
    )

    damage_type: Optional[Literal["DAMAGE", "TRUE_DAMAGE"]] = None
    base_power_multiplier: Optional[str] = None
    flat_damage_add: Optional[float] = Field(None, ge=0)

    heal_type: Optional[
        Literal["FLAT", "PERCENT_MAX_HP", "PERCENT_CASTER_MATK", "PERCENT_DAMAGE_DEALT"]
    ] = None
    value: Optional[str] = None

    status_effect_id: Optional[str] = None

    modifications: Optional[List[StatModificationItem]] = None

    modifiers: Optional[List[Modifier]] = None

    @field_validator("effect_type", mode="after")
    @classmethod
    def check_definition_method(cls, v, info):
        effect_template_defined = info.data.get("effect_template") is not None
        effect_type_defined = v is not None

        if effect_template_defined and effect_type_defined:
            raise ValueError(
                "Cannot define both effect_template and effect_type simultaneously for an effect."
            )
        if not effect_template_defined and not effect_type_defined:
            raise ValueError(
                "Either effect_template or effect_type must be defined for an effect."
            )
        return v


class TargetLogicParams(BaseModel):
    """目標邏輯參數模型"""

    value: Optional[Union[float, int, str]] = None
    status_effect_id: Optional[str] = None


class TargetLogicCondition(BaseModel):
    """目標邏輯條件模型"""

    type: str
    params: TargetLogicParams


class TargetLogicDetail(BaseModel):
    """目標邏輯詳情模型"""

    priority_score: int
    condition: Optional[TargetLogicCondition] = None
    selector_type: str
    max_targets: int = Field(..., ge=1)


class BaseStats(BaseModel):
    """基礎屬性模型"""

    hp: int = Field(..., ge=0)
    max_mp: int = Field(..., ge=0)
    mp_regen_per_turn: int = Field(..., ge=0)
    atk: int = Field(..., ge=0)  # 統一的攻擊力
    def_: int = Field(..., ge=0, alias="def")  # 統一的防禦力，使用alias避免關鍵字衝突
    spd: int = Field(..., ge=0)
    crit_rate: float = Field(..., ge=0.0, le=1.0)
    crit_dmg_multiplier: float = Field(..., ge=1.0)
    accuracy: float = Field(..., ge=0.0, le=1.0)
    evasion: float = Field(..., ge=0.0, le=1.0)

    # 向後兼容的屬性
    patk: Optional[int] = Field(None, ge=0, description="向後兼容：物理攻擊力")
    pdef: Optional[int] = Field(None, ge=0, description="向後兼容：物理防禦力")
    matk: Optional[int] = Field(None, ge=0, description="向後兼容：魔法攻擊力")
    mdef: Optional[int] = Field(None, ge=0, description="向後兼容：魔法防禦力")


class GrowthStats(BaseModel):
    """成長屬性模型"""

    hp: float = Field(0.0, ge=0)
    max_mp: float = Field(0.0, ge=0)
    mp_regen_per_turn: float = Field(0.0, ge=0)
    atk: float = Field(0.0, ge=0)  # 統一的攻擊力成長
    def_: float = Field(0.0, ge=0, alias="def")  # 統一的防禦力成長
    spd: float = Field(0.0, ge=0)
    crit_rate: float = Field(0.0, ge=0.0)
    crit_dmg_multiplier: float = Field(0.0, ge=0.0)
    accuracy: float = Field(0.0, ge=0.0)
    evasion: float = Field(0.0, ge=0.0)

    # 向後兼容的屬性
    patk: Optional[float] = Field(None, ge=0, description="向後兼容：物理攻擊力成長")
    pdef: Optional[float] = Field(None, ge=0, description="向後兼容：物理防禦力成長")
    matk: Optional[float] = Field(None, ge=0, description="向後兼容：魔法攻擊力成長")
    mdef: Optional[float] = Field(None, ge=0, description="向後兼容：魔法防禦力成長")


class AdditionalCondition(BaseModel):
    """額外條件模型"""

    source_combatant: Optional[str] = None
    check: Optional[str] = None
    value: Optional[Union[float, int, str]] = None
    formula: Optional[str] = None


class TriggerConditionParams(BaseModel):
    """觸發條件參數模型"""

    threshold_percent_formula: Optional[str] = None
    check_direction: Optional[str] = None


class TriggerCondition(BaseModel):
    """觸發條件模型"""

    type: str
    sub_type: Optional[str] = None
    chance_formula: Optional[str] = "1.0"
    trigger_once_per_battle: Optional[bool] = False
    additional_conditions: Optional[List[AdditionalCondition]] = None
    params: Optional[TriggerConditionParams] = None


class TargetOverride(BaseModel):
    """目標覆蓋模型"""

    selector_type: str
    max_targets: int = Field(..., ge=1)
