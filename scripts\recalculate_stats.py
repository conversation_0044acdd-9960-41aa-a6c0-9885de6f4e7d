import asyncio
import json
from collections import defaultdict
from typing import Any, Dict, List

import asyncpg

# --- 從 gacha/core/game_registry.py 複製過來的邏輯 ---
# 為了讓腳本獨立運行，我們需要複製 GameRegistry 和相關的更新函數
# (在真實的專案中，您可能會考慮重構程式碼以避免複製)
from gacha.core.game_registry import GameRegistry

# --- 資料庫和目標使用者設定 ---
DATABASE_URL = "*******************************************************/gacha_database"
TARGET_USER_IDS = [
    604322126224424965,
    1389663359712100604,
    739120535149215846,
    976453893670178877,
    917391407524741163,
    1389660425917829292,
    1389636412080390274,
]


async def get_legal_game_history(
    pool: asyncpg.Pool, user_ids: List[int]
) -> List[Dict[str, Any]]:
    """獲取所有合法的遊戲歷史紀錄"""
    query = """
        SELECT * FROM game_history
        WHERE user_id = ANY($1::bigint[])
        ORDER BY created_at ASC
    """
    records = await pool.fetch(query, user_ids)
    return [dict(record) for record in records]


def recalculate_stats_for_user(
    history: List[Dict[str, Any]],
) -> Dict[str, Dict[str, Any]]:
    """根據歷史紀錄重新計算統計數據"""
    # user_id -> game_type -> stats
    recalculated_data = defaultdict(lambda: defaultdict(dict))

    for record in history:
        user_id = record["user_id"]
        game_type = record["game_type"]

        # 確保 game_data 是字典格式
        game_data = record["game_data"]
        if isinstance(game_data, str):
            game_data = json.loads(game_data)

        # 初始化統計數據
        if not recalculated_data[user_id][game_type]:
            recalculated_data[user_id][game_type] = {
                "total_games": 0,
                "total_wins": 0,
                "total_losses": 0,
                "total_pushes": 0,
                "total_bet_amount": 0,
                "total_payout_amount": 0,
                "total_profit_loss": 0,
                "max_win": 0,
                "max_loss": 0,
                "game_specific_stats": {},
                "last_played_at": None,
            }

        stats = recalculated_data[user_id][game_type]

        # --- 累加核心統計數據 ---
        stats["total_games"] += 1
        profit = record["profit_loss"]
        if profit > 0:
            stats["total_wins"] += 1
            stats["max_win"] = max(stats["max_win"], profit)
        elif profit < 0:
            stats["total_losses"] += 1
            stats["max_loss"] = min(stats["max_loss"], profit)
        else:
            stats["total_pushes"] += 1

        stats["total_bet_amount"] += record["bet_amount"]
        stats["total_payout_amount"] += record["payout_amount"]
        stats["total_profit_loss"] += profit
        stats["last_played_at"] = record["created_at"]

        # --- 更新 JSONB 欄位 ---
        game_config = GameRegistry.get_game(game_type)
        if game_config and game_config.stats_updater:
            # 傳入目前的 specific_stats 和該筆遊戲的 game_data
            stats["game_specific_stats"] = game_config.stats_updater(
                stats["game_specific_stats"], game_data
            )

    # 將 defaultdict 轉換為標準的 dict 以符合返回型別
    return {k: dict(v) for k, v in recalculated_data.items()}


async def update_database(
    pool: asyncpg.Pool, recalculated_data: Dict[str, Dict[str, Any]]
):
    """將重新計算的數據更新到資料庫"""
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 為了安全起見，我們先刪除這些使用者的所有統計數據
            await conn.execute(
                "DELETE FROM user_game_stats WHERE user_id = ANY($1::bigint[])",
                TARGET_USER_IDS,
            )

            # 然後插入新的、乾淨的數據
            for user_id, game_stats in recalculated_data.items():
                for game_type, stats in game_stats.items():
                    await conn.execute(
                        """
                        INSERT INTO user_game_stats (
                            user_id, game_type, total_games, total_wins, total_losses, total_pushes,
                            total_bet_amount, total_payout_amount, total_profit_loss,
                            max_win, max_loss, game_specific_stats, last_played_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, CURRENT_TIMESTAMP)
                    """,
                        user_id,
                        game_type,
                        stats["total_games"],
                        stats["total_wins"],
                        stats["total_losses"],
                        stats["total_pushes"],
                        stats["total_bet_amount"],
                        stats["total_payout_amount"],
                        stats["total_profit_loss"],
                        stats["max_win"],
                        stats["max_loss"],
                        json.dumps(stats["game_specific_stats"]),
                        stats["last_played_at"],
                    )
    print(f"成功更新 {len(recalculated_data)} 位使用者的遊戲統計數據。")


async def main(dry_run: bool = True):
    """主函數"""
    # 必須手動註冊遊戲，因為這是一個獨立腳本
    # (這部分應與 gacha/core/game_registry.py 保持同步)
    # ... 此處省略了所有 GameRegistry.register_game(...) 的呼叫，假設它們已在某處被執行 ...
    # 在真實的應用中，您需要確保 GameRegistry 被正確初始化

    pool = await asyncpg.create_pool(DATABASE_URL)
    if not pool:
        print("無法連接到資料庫")
        return

    print(f"將要處理的使用者 ID: {TARGET_USER_IDS}")

    # 1. 獲取所有合法的歷史紀錄
    history = await get_legal_game_history(pool, TARGET_USER_IDS)
    print(f"從資料庫中獲取了 {len(history)} 筆合法的遊戲紀錄。")

    # 2. 重新計算統計
    recalculated_data = recalculate_stats_for_user(history)
    print("重新計算統計數據完成。")

    # 3. 預覽或執行
    if dry_run:
        print("\n--- 預覽模式 ---")
        print("將會更新的數據如下 (JSON 格式):")
        # 使用 lambda 來處理 datetime 物件的序列化
        print(json.dumps(recalculated_data, indent=2, default=str))
        print("\n若要執行更新，請使用 --execute 參數運行腳本。")
    else:
        print("\n--- 執行模式 ---")
        await update_database(pool, recalculated_data)

    await pool.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="重新計算遊戲統計數據")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="預覽將要更新的數據，不寫入資料庫",
        default=True,
    )
    parser.add_argument(
        "--execute", action="store_false", dest="dry_run", help="實際執行更新操作"
    )

    args = parser.parse_args()

    # 為了讓這個獨立腳本能找到 gacha 模組，我們需要手動將專案根目錄加到 sys.path
    import os
    import sys

    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # 現在我們可以安全地從 gacha 模組導入
    from gacha.core.game_registry import (
        GameRegistry,
    )

    # 由於我們已經從 gacha.core.game_registry 導入了 GameRegistry，
    # 並且該檔案在被導入時就已經執行了註冊邏輯，
    # 所以我們不需要在這裡手動重複註冊。
    # 確保執行此腳本的環境可以正確解析 gacha 模組即可。
    pass

    asyncio.run(main(args.dry_run))
