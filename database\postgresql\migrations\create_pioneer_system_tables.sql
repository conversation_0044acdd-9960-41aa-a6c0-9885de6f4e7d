-- ========================================
-- Pioneer System 數據庫遷移腳本
-- 創建開拓者指令系統的所有數據表
-- 版本：V1.0.0 (2025-07-01)
-- ========================================

BEGIN;

-- ========================================
-- 1. 玩家在「開拓者指令」中的核心狀態與經濟
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_profiles (
    user_id BIGINT NOT NULL,
    energy INTEGER DEFAULT 100 NOT NULL,
    max_energy INTEGER DEFAULT 100 NOT NULL,
    last_energy_update TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    pending_oil_earnings BIGINT DEFAULT 0 NOT NULL, -- 待收取的油幣收益
    total_oil_earnings BIGINT DEFAULT 0 NOT NULL, -- 累計油幣收益記錄
    current_era INTEGER DEFAULT 1 NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT pioneer_profiles_pkey PRIMARY KEY (user_id),
    CONSTRAINT fk_pioneer_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE
);

-- ========================================
-- 2. 玩家的技能等級與經驗
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_skills (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    skill_id VARCHAR(50) NOT NULL, -- e.g., 'woodcutting', 'mining'
    level INTEGER DEFAULT 1 NOT NULL,
    xp INTEGER DEFAULT 0 NOT NULL,
    CONSTRAINT fk_pioneer_skills_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT unique_user_skill UNIQUE (user_id, skill_id)
);

-- ========================================
-- 3. 玩家的倉庫
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_warehouse (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    item_id VARCHAR(50) NOT NULL, -- 來自 config/items.yaml 的物品 ID
    quantity INTEGER NOT NULL,
    CONSTRAINT fk_pioneer_warehouse_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT unique_user_item UNIQUE (user_id, item_id),
    CONSTRAINT check_quantity_positive CHECK (quantity > 0)
);

-- ========================================
-- 4. 通用的任務進度追蹤表
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_quest_progress (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    quest_id VARCHAR(100) NOT NULL, -- 例如 'era1_craft_chairs'
    progress INTEGER DEFAULT 0 NOT NULL,
    target INTEGER NOT NULL,
    completed_at TIMESTAMPTZ,
    CONSTRAINT fk_pioneer_quest_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT unique_user_quest UNIQUE (user_id, quest_id)
);

-- ========================================
-- 5. 統一設施系統：萬物皆「設施」的核心表
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_facilities (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    facility_type VARCHAR(50) NOT NULL, -- 指向 facilities.yaml 的設施ID
    facility_name VARCHAR(100), -- 自定義名稱，可為NULL使用默認名稱
    level INTEGER DEFAULT 1 NOT NULL, -- 設施等級，影響產出效率
    upgrades JSONB DEFAULT '{}'::jsonb NOT NULL, -- 已解鎖的升級 {"auto_input": true, "auto_output": true}
    efficiency_bonus DECIMAL(5,2) DEFAULT 0.00 NOT NULL, -- 效率加成百分比
    last_production_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 離線計算基準時間
    is_active BOOLEAN DEFAULT true NOT NULL, -- 設施是否啟用
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_pioneer_facilities_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE
);

-- ========================================
-- 6. 統一槽位系統：支持所有設施的輸入/輸出/存儲槽位
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_facility_states (
    facility_id INTEGER NOT NULL,
    slot_type VARCHAR(50) NOT NULL, -- 'input', 'fuel', 'output', 'storage', 'shelf' 等
    slot_index INTEGER DEFAULT 0 NOT NULL, -- 槽位索引 (支援多個同類型槽位)
    item_id VARCHAR(50), -- 當前槽位中的物品ID
    quantity INTEGER DEFAULT 0 NOT NULL, -- 當前槽位中的物品數量
    max_capacity INTEGER DEFAULT 100 NOT NULL, -- 槽位最大容量
    slot_config JSONB DEFAULT '{}'::jsonb, -- 槽位特殊配置 (如商店價格)
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT pioneer_facility_states_pkey PRIMARY KEY (facility_id, slot_type, slot_index),
    CONSTRAINT fk_facility_state FOREIGN KEY (facility_id) REFERENCES public.pioneer_facilities(id) ON DELETE CASCADE,
    CONSTRAINT check_quantity_valid CHECK (quantity >= 0 AND quantity <= max_capacity)
);

-- ========================================
-- 7. 儲存指派到設施上的卡片
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_facility_assignments (
    facility_id INTEGER NOT NULL,
    user_collection_id INTEGER NOT NULL, -- 指向 gacha_user_collections.id
    assigned_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT pioneer_facility_assignments_pkey PRIMARY KEY (facility_id),
    CONSTRAINT fk_assignment_facility FOREIGN KEY (facility_id) REFERENCES public.pioneer_facilities(id) ON DELETE CASCADE,
    CONSTRAINT fk_assignment_card FOREIGN KEY (user_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE CASCADE
);

-- ========================================
-- 8. 儲存玩家在工坊研究室中各項目的等級
-- ========================================
CREATE TABLE IF NOT EXISTS public.pioneer_research_levels (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    project_id VARCHAR(50) NOT NULL, -- 來自 config/research_projects.yaml 的項目ID
    level INTEGER DEFAULT 0 NOT NULL, -- 從0級開始，0級表示未研究
    total_invested_oil BIGINT DEFAULT 0 NOT NULL, -- 累計投資的油幣數量
    last_upgraded_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_pioneer_research_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE,
    CONSTRAINT unique_user_project UNIQUE (user_id, project_id)
);

COMMIT;
