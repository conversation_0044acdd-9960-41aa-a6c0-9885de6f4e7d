-- 遷移腳本：最終清理並統一所有 game_type 的約束

-- 1. 清理 user_game_stats 表
-- 移除所有可能的舊約束，無論它們叫什麼名字
ALTER TABLE user_game_stats DROP CONSTRAINT IF EXISTS check_game_type;
ALTER TABLE user_game_stats DROP CONSTRAINT IF EXISTS check_stats_game_type;

-- 添加最終的、統一命名的約束
ALTER TABLE user_game_stats ADD CONSTRAINT check_game_type CHECK (game_type IN (
    'blackjack', 
    'dice', 
    'mines', 
    'tower', 
    'slot', 
    'poker1v1',
    'spin_wheel',
    'baccarat'
));

-- 2. 清理 game_history 表 (為確保一致性)
-- 移除舊約束
ALTER TABLE game_history DROP CONSTRAINT IF EXISTS check_game_type;

-- 添加最終的、統一命名的約束
ALTER TABLE game_history ADD CONSTRAINT check_game_type CHECK (game_type IN (
    'blackjack', 
    'dice', 
    'mines', 
    'tower', 
    'slot', 
    'poker1v1',
    'spin_wheel',
    'baccarat'
));