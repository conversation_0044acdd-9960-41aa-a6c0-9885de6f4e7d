# 確認對話框重構報告

## 概述

本次重構將所有使用 `ConfirmationFactory` 的地方重構為使用 discord.py 最佳實踐的 `ConfirmationView`，遵循錯誤與異常處理開發規範。

## 重構目標

1. **遵循 discord.py 最佳實踐**：使用 `@discord.ui.button` 裝飾器而不是手動設置 `callback`
2. **統一錯誤處理**：所有視圖繼承自 `BaseView` 以獲得集中式錯誤處理
3. **代碼一致性**：統一確認對話框的實現方式
4. **移除舊實現**：完全移除 `ConfirmationFactory`，使用現代化的 `ConfirmationView`

## 主要變更

### 1. 更新 `gacha/views/ui_components/confirmation.py`

#### 新增功能
- **ConfirmationView**：使用 `@discord.ui.button` 裝飾器的最佳實踐實現
- **移除 ConfirmationFactory**：完全移除舊的工廠類實現

#### 關鍵改進
- 繼承自 `BaseView` 以獲得統一的錯誤處理和權限檢查
- 使用裝飾器定義按鈕，避免手動設置回調
- 改進的按鈕禁用邏輯，正確處理訊息編輯
- 支持自定義按鈕標籤、樣式和超時時間

### 2. 重構的文件列表

#### 完全重構（使用 ConfirmationView）
1. **`gacha/cogs/transfer_cog.py`**
   - 移除 `ConfirmationFactory` 導入
   - 直接使用 `ConfirmationView` 創建確認對話框
   - 手動創建嵌入和視圖

2. **`gacha/cogs/wish_cog.py`**
   - 重構兩個確認對話框（擴充槽位和提升力度）
   - 使用 `ConfirmationView` 替代 `ConfirmationFactory`

3. **`rpg_system/cogs/skill_management_cog.py`**
   - 重構獻祭確認對話框
   - 添加正確的 `ConfirmationView` 導入
   - 改進錯誤處理邏輯

#### 架構改進
4. **`gacha/views/shop/random_ticket_confirmation_view.py`**
   - 重構為繼承 `BaseView`
   - 保持特殊的會話驗證邏輯
   - 修復未使用參數的警告

5. **`gacha/views/modals/ticket_exchange_modal.py`**
   - 更新 `RandomTicketConfirmationView` 實例化
   - 傳遞必要的 `bot` 參數

6. **`gacha/views/collection/collection_view/__init__.py`**
   - 移除 `ConfirmationFactory` 導出
   - 清理不必要的導入

## 技術細節

### ConfirmationView 特性

```python
class ConfirmationView(BaseView):
    def __init__(
        self,
        *,
        bot: commands.Bot,
        user_id: int,
        on_confirm: Callable[[discord.Interaction], Awaitable[None]],
        on_cancel: Optional[Callable[[discord.Interaction], Awaitable[None]]] = None,
        timeout: int = 60,
        confirm_label: str = "確認",
        cancel_label: str = "取消",
        confirm_style: discord.ButtonStyle = discord.ButtonStyle.success,
        cancel_style: discord.ButtonStyle = discord.ButtonStyle.danger,
    ):
```

### 使用範例

```python
# 新的最佳實踐方式
async def on_confirm_callback(interaction: discord.Interaction):
    await interaction.response.send_message("操作已確認！", ephemeral=True)

view = ConfirmationView(
    bot=self.bot,
    user_id=interaction.user.id,
    on_confirm=on_confirm_callback,
    confirm_label="確認操作",
    cancel_label="取消",
)

await interaction.followup.send(embed=embed, view=view, ephemeral=True)
```

### 完全移除舊實現

`ConfirmationFactory` 已完全移除，所有代碼都必須使用新的 `ConfirmationView`：

```python
# 舊的方式已不再支持
# await ConfirmationFactory.create_confirmation(...)  # ❌ 已移除

# 新的標準方式
view = ConfirmationView(
    bot=self.bot,
    user_id=interaction.user.id,
    on_confirm=on_confirm_callback,
    on_cancel=on_cancel_callback,
)
await interaction.followup.send(embed=embed, view=view, ephemeral=True)  # ✅
```

## 錯誤處理改進

### 統一錯誤處理
- 所有視圖繼承自 `BaseView`
- 自動獲得集中式錯誤處理
- 遵循錯誤與異常處理開發規範

### 權限檢查
- 自動驗證用戶權限
- 防止其他用戶操作他人的確認對話框

### 按鈕狀態管理
- 防止重複點擊
- 正確禁用按鈕
- 改進的訊息編輯邏輯

## 測試建議

1. **功能測試**
   - 測試所有重構的確認對話框
   - 驗證確認和取消功能
   - 檢查權限控制

2. **錯誤處理測試**
   - 測試異常情況下的行為
   - 驗證錯誤訊息顯示
   - 檢查日誌記錄

3. **重構完整性測試**
   - 確保所有使用確認對話框的功能都正常工作
   - 驗證新 API 的穩定性

## 後續工作

1. **測試驗證**：全面測試所有重構的確認對話框功能
2. **文檔更新**：更新開發文檔以反映新的最佳實踐
3. **代碼審查**：確保所有重構符合項目標準

## 結論

本次重構成功地將確認對話框系統完全現代化，遵循了 discord.py 的最佳實踐。通過移除舊的 `ConfirmationFactory` 並統一使用 `ConfirmationView`，新的實現更加健壯、易於維護，並提供了更好的錯誤處理機制。所有相關文件都已正確導入並使用新的實現。
