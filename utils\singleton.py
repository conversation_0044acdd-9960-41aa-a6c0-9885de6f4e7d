import threading
from abc import ABCMeta


class SingletonType(ABCMeta):
    """
    線程安全的單例元類（Singleton Metaclass）

    使用方法:
    ```python
    class MyClass(metaclass=SingletonType):
        pass

    # 每次創建的都是同一個實例
    instance1 = MyClass()
    instance2 = MyClass()
    assert instance1 is instance2  # True
    ```
    """

    def __new__(cls, name, bases, attrs):
        """為每個使用此元類的類創建獨立的_instance和_instance_lock屬性"""
        attrs["_instance"] = None
        attrs["_instance_lock"] = threading.Lock()
        return super(SingletonType, cls).__new__(cls, name, bases, attrs)

    def __call__(cls, *args, **kwargs):
        """確保每次調用類都返回同一個實例"""
        instance = getattr(cls, "_instance", None)
        if instance is None:
            lock = cls._instance_lock  # type: ignore[attr-defined]
            with lock:
                instance = getattr(cls, "_instance", None)
                if instance is None:
                    instance = super(SingletonType, cls).__call__(*args, **kwargs)
                    cls._instance = instance
        return instance
