#!/usr/bin/env python3
"""
模板覆蓋率驗證腳本
提取所有effect_templates定義的模板，並驗證在generate/config/skill_name_library.json和generate/config/skill_descriptions.json中是否有對應的映射
"""

import json
import os
import sys
from typing import Any, Dict, Set


def load_json_file(file_path: str) -> Dict[str, Any]:
    """載入JSON文件"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析錯誤 {file_path}: {e}")
        return {}
    except Exception as e:
        print(f"❌ 載入文件失敗 {file_path}: {e}")
        return {}


def extract_all_effect_templates() -> Set[str]:
    """從所有effect_templates文件中提取模板名稱"""
    templates = set()

    # 效果模板文件列表
    template_files = [
        "damage_templates.json",
        "heal_templates.json",
        "status_application_templates.json",
        "stat_modification_templates.json",
        "apply_stat_boost_templates.json",
        "shield_templates.json",
        "special_templates.json",
    ]

    # 獲取effect_templates目錄路徑
    script_dir = os.path.dirname(os.path.abspath(__file__))
    templates_dir = os.path.join(script_dir, "..", "config", "data", "effect_templates")

    print(f"📁 掃描效果模板目錄: {templates_dir}")

    for template_file in template_files:
        template_path = os.path.join(templates_dir, template_file)

        if os.path.exists(template_path):
            print(f"  📄 載入: {template_file}")
            template_data = load_json_file(template_path)

            # 直接提取頂層鍵作為模板名稱
            file_templates = set(template_data.keys())
            templates.update(file_templates)

            print(f"    ✅ 找到 {len(file_templates)} 個模板")
            for template in sorted(file_templates):
                print(f"      - {template}")
        else:
            print(f"  ⚠️  文件不存在: {template_file}")

    return templates


def extract_name_library_templates(name_library: Dict[str, Any]) -> Set[str]:
    """從generate/config/skill_name_library.json中提取模板名稱"""
    templates = set()

    template_names = name_library.get("template_names", {})
    templates.update(template_names.keys())

    return templates


def extract_description_templates(skill_descriptions: Dict[str, Any]) -> Set[str]:
    """從generate/config/skill_descriptions.json中提取模板名稱"""
    templates = set()

    # skill_descriptions.json是分類結構
    for _, category_templates in skill_descriptions.items():
        if isinstance(category_templates, dict):
            templates.update(category_templates.keys())

    return templates


def print_template_analysis(
    all_templates: Set[str], name_templates: Set[str], desc_templates: Set[str]
):
    """打印模板分析結果"""

    print("\n📊 模板統計:")
    print(f"  🎯 效果模板總數: {len(all_templates)}")
    print(f"  📝 名稱庫模板數: {len(name_templates)}")
    print(f"  📖 描述模板數: {len(desc_templates)}")

    # 檢查缺失的名稱映射
    missing_names = all_templates - name_templates
    if missing_names:
        print(f"\n❌ 缺失名稱映射的模板 ({len(missing_names)} 個):")
        for template in sorted(missing_names):
            print(f"  - {template}")
    else:
        print("\n✅ 所有模板都有名稱映射")

    # 檢查缺失的描述映射
    missing_descriptions = all_templates - desc_templates
    if missing_descriptions:
        print(f"\n❌ 缺失描述映射的模板 ({len(missing_descriptions)} 個):")
        for template in sorted(missing_descriptions):
            print(f"  - {template}")
    else:
        print("\n✅ 所有模板都有描述映射")

    # 檢查多餘的名稱映射
    extra_names = name_templates - all_templates
    if extra_names:
        print(f"\n⚠️  名稱庫中多餘的模板 ({len(extra_names)} 個):")
        for template in sorted(extra_names):
            print(f"  - {template}")

    # 檢查多餘的描述映射
    extra_descriptions = desc_templates - all_templates
    if extra_descriptions:
        print(f"\n⚠️  描述庫中多餘的模板 ({len(extra_descriptions)} 個):")
        for template in sorted(extra_descriptions):
            print(f"  - {template}")


def print_all_templates_list(all_templates: Set[str]):
    """打印所有模板的完整列表"""
    print(f"\n📋 所有效果模板列表 ({len(all_templates)} 個):")
    print("=" * 50)

    for i, template in enumerate(sorted(all_templates), 1):
        print(f"{i:2d}. {template}")


def generate_missing_templates_suggestions(
    missing_names: Set[str], missing_descriptions: Set[str]
):
    """生成缺失模板的建議"""

    if missing_names:
        print("\n🔧 建議添加到 generate/config/skill_name_library.json 的模板名稱:")
        print("=" * 60)
        for template in sorted(missing_names):
            # 根據模板名稱生成建議的中文名稱
            suggested_names = generate_template_name_suggestions(template)
            print(f'    "{template}": {suggested_names},')

    if missing_descriptions:
        print("\n🔧 建議添加到 generate/config/skill_descriptions.json 的模板描述:")
        print("=" * 60)
        for template in sorted(missing_descriptions):
            # 根據模板名稱生成建議的描述
            category = determine_template_category(template)
            suggested_desc = generate_template_description_suggestion(template)
            print(f'  // 在 "{category}" 分類中添加:')
            print(f'    "{template}": "{suggested_desc}",')


def generate_template_name_suggestions(template: str) -> str:
    """根據模板名稱生成建議的中文名稱"""
    name_mapping = {
        "APPLY_BASIC_SHIELD": '["基礎護盾", "防護屏障", "保護之盾", "守護結界"]',
        "APPLY_BURN_STATUS": '["燃燒狀態", "火焰灼燒", "烈焰之痛", "燃燒效果"]',
        "APPLY_MAGIC_SHIELD": '["魔法護盾", "奧術屏障", "法術防護", "魔力護罩"]',
        "APPLY_PHYSICAL_BARRIER": '["物理屏障", "實體護盾", "物理防護", "堅固壁壘"]',
        "APPLY_SCALING_SHIELD": '["成長護盾", "適應性防護", "動態屏障", "強化護盾"]',
        "CONDITIONAL_DAMAGE_BOOST": '["條件傷害", "情境增強", "條件加成", "狀況強化"]',
        "DUELIST_FOCUS": '["決鬥專注", "單挑集中", "決鬥者之心", "專注決鬥"]',
        "EXECUTE_DAMAGE": '["處決傷害", "斬殺攻擊", "終結一擊", "處決之刃"]',
        "MULTI_HIT_DAMAGE": '["多段攻擊", "連續打擊", "多重攻擊", "連擊術"]',
        "PERCENTAGE_HP_DAMAGE": '["百分比傷害", "生命比例傷害", "血量百分比", "比例攻擊"]',
        "STAT_BOOST_ACCURACY": '["命中強化", "精準提升", "準確度增強", "瞄準強化"]',
        "STAT_BOOST_CRIT_DMG": '["爆擊傷害強化", "暴擊威力提升", "致命傷害增強", "爆傷強化"]',
        "STAT_BOOST_CRIT_RATE": '["爆擊率強化", "暴擊機率提升", "致命機率增強", "爆率強化"]',
        "STAT_BOOST_HP": '["生命強化", "血量提升", "體力增強", "生命力強化"]',
        "STAT_BOOST_ATK": '["攻擊強化", "攻擊威力", "攻擊力增強", "攻擊強化"]',
        "STAT_BOOST_MP": '["魔力強化", "法力提升", "魔法值增強", "魔力增幅"]',
        "STAT_BOOST_SPD": '["速度強化", "敏捷提升", "迅捷增強", "速度激發"]',
        "VITAL_POINT_EXPLOIT": '["要害攻擊", "弱點利用", "致命點打擊", "要害突擊"]',
    }

    return name_mapping.get(
        template, '["技能名稱1", "技能名稱2", "技能名稱3", "技能名稱4"]'
    )


def determine_template_category(template: str) -> str:
    """確定模板應該歸屬的分類"""
    if any(keyword in template for keyword in ["DAMAGE", "STRIKE", "HIT", "EXECUTE"]):
        return "damage"
    elif any(keyword in template for keyword in ["HEAL"]):
        return "heal"
    elif any(keyword in template for keyword in ["SHIELD", "BARRIER"]):
        return "shield"
    elif any(keyword in template for keyword in ["STAT_BOOST", "STAT_DEBUFF", "BOOST"]):
        return "buff"
    elif any(keyword in template for keyword in ["APPLY_", "STATUS"]):
        return "status"
    else:
        return "special"


def generate_template_description_suggestion(template: str) -> str:
    """根據模板名稱生成建議的描述"""
    desc_mapping = {
        "CONDITIONAL_DAMAGE_BOOST": "{prefix}在滿足特定條件時，{name}造成額外 {power} 傷害",
        "MULTI_HIT_DAMAGE": "{prefix}施展{name}，連續多次攻擊，總計造成 {power} 傷害",
    }

    return desc_mapping.get(template, "{prefix}施展{name}，產生相應效果")


def main():
    """主函數"""
    print("🔍 開始模板覆蓋率驗證...")

    # 獲取文件路徑
    script_dir = os.path.dirname(os.path.abspath(__file__))
    skill_name_library_path = os.path.join(
        script_dir, "generate", "config", "skill_name_library.json"
    )
    skill_descriptions_path = os.path.join(
        script_dir, "generate", "config", "skill_descriptions.json"
    )

    print("\n📁 載入配置文件...")
    print(f"  📄 名稱庫: {skill_name_library_path}")
    print(f"  📄 描述庫: {skill_descriptions_path}")

    # 載入配置文件
    skill_name_library = load_json_file(skill_name_library_path)
    skill_descriptions = load_json_file(skill_descriptions_path)

    # 提取所有模板
    print("\n🎯 提取效果模板...")
    all_templates = extract_all_effect_templates()

    print("\n📝 提取名稱庫模板...")
    name_templates = extract_name_library_templates(skill_name_library)

    print("\n📖 提取描述庫模板...")
    desc_templates = extract_description_templates(skill_descriptions)

    # 分析結果
    print_template_analysis(all_templates, name_templates, desc_templates)

    # 打印完整模板列表
    print_all_templates_list(all_templates)

    # 檢查是否有問題並生成建議
    missing_names = all_templates - name_templates
    missing_descriptions = all_templates - desc_templates

    if missing_names or missing_descriptions:
        generate_missing_templates_suggestions(missing_names, missing_descriptions)
        print("\n❌ 發現模板覆蓋率問題！")
        return 1
    else:
        print("\n✅ 所有模板都有完整的名稱和描述映射！")
        return 0


if __name__ == "__main__":
    sys.exit(main())
