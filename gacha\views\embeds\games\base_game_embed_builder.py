# -*- coding: utf-8 -*-
"""
遊戲Embed構建器基類 - 統一遊戲embed的通用功能
"""

from typing import Optional, Union

import discord

from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder


class BaseGameEmbedBuilder(BaseEmbedBuilder):
    """遊戲Embed構建器基類 - 提供通用的遊戲embed功能"""

    # 子類應該覆蓋這些常量
    GAME_IMAGE_URL: str = ""
    GAME_NAME: str = "遊戲"

    def __init__(self, user: Union[discord.User, discord.Member], **kwargs):
        """初始化遊戲embed構建器"""
        super().__init__(data={"user": user, **kwargs})
        self.user = user

    def _create_game_embed(
        self, title: str, description: str, color: Optional[discord.Color] = None
    ) -> discord.Embed:
        """創建基礎遊戲embed"""
        embed = self._create_base_embed(
            title=title,
            description=description,
            color=color if color is not None else self.DEFAULT_EMBED_COLOR,
        )

        # 設置遊戲圖片
        if self.GAME_IMAGE_URL:
            embed.set_thumbnail(url=self.GAME_IMAGE_URL)

        return embed

    def _set_user_footer(self, embed: discord.Embed, additional_text: str = ""):
        """設置包含用戶頭像的footer"""
        footer_text = additional_text if additional_text else f"{self.GAME_NAME}遊戲"
        embed.set_footer(text=footer_text, icon_url=self.user.display_avatar.url)

    def _format_money_display(self, amount: int, label: str = "") -> str:
        """格式化金錢顯示"""
        if label:
            return f"{label}: {amount:,}"
        return f"{amount:,}"

    def _build_money_info_section(
        self, bet: int, balance: Optional[int] = None, profit_loss: Optional[int] = None
    ) -> list[str]:
        """構建金錢信息部分"""
        parts = []

        if bet > 0:
            parts.append(f"下注: {bet:,}")

        if balance is not None:
            parts.append(f"餘額: {balance:,}")

        if profit_loss is not None:
            parts.append(f"總盈虧: {profit_loss:,}")

        return parts
