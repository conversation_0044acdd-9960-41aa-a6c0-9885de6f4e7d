"""
Pioneer System 基礎處理器
所有動作處理器的基礎類
"""

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any, Dict, Optional

from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader


class BaseProcessor(ABC):
    """動作處理器基礎類"""

    def __init__(self, game_data: "GameDataLoader", repository: Any):
        """初始化處理器

        Args:
            game_data: 遊戲數據載入器
            repository: 數據庫存取層
        """
        self.game_data = game_data
        self.repository = repository

        # 為 repository 添加連接管理方法
        def set_connection(conn):
            self.repository._conn = conn

        def clear_connection():
            if hasattr(self.repository, "_conn"):
                delattr(self.repository, "_conn")

        self.repository.set_connection = set_connection
        self.repository.clear_connection = clear_connection

    @abstractmethod
    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """執行動作

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 動作參數

        Returns:
            ActionResult: 執行結果
        """
        pass

    async def _calculate_output_quantity(
        self,
        base_quantity: int,
        user_id: int,
        skill_id: Optional[str] = None,
        facility_id: Optional[int] = None,
    ) -> int:
        """計算產出數量（考慮技能加成、卡片加成等）

        Args:
            base_quantity: 基礎數量
            user_id: 用戶ID
            skill_id: 相關技能ID
            facility_id: 設施ID（用於卡片加成）

        Returns:
            int: 最終產出數量
        """
        final_quantity = float(base_quantity)

        # 技能加成
        if skill_id:
            skill = await self.repository.get_user_skill(user_id, skill_id)
            # 技能已在需求檢查階段確保存在
            skill_bonus = skill.level * 0.01  # 每級技能提供 1% 產出加成
            final_quantity *= 1 + skill_bonus

        # 卡片加成
        if facility_id:
            card_bonus = await self._calculate_card_bonus(
                facility_id, "production_efficiency"
            )
            final_quantity *= 1 + card_bonus

        # 研究加成
        research_bonus = await self._calculate_research_bonus(
            user_id, "production_efficiency"
        )
        final_quantity *= 1 + research_bonus

        return max(1, int(final_quantity))  # 至少產出1個

    async def _calculate_xp_gain(
        self,
        base_xp: int,
        user_id: int,
        skill_id: Optional[str] = None,
        facility_id: Optional[int] = None,
    ) -> int:
        """計算經驗獲得（考慮各種加成）

        Args:
            base_xp: 基礎經驗
            user_id: 用戶ID
            skill_id: 相關技能ID
            facility_id: 設施ID（用於卡片加成）

        Returns:
            int: 最終經驗獲得
        """
        final_xp = float(base_xp)

        # 卡片加成
        if facility_id:
            card_bonus = await self._calculate_card_bonus(facility_id, "xp_bonus")
            final_xp *= 1 + card_bonus

        # 研究加成
        research_bonus = await self._calculate_research_bonus(user_id, "xp_bonus")
        final_xp *= 1 + research_bonus

        return max(1, int(final_xp))  # 至少獲得1點經驗

    async def _calculate_card_bonus(self, facility_id: int, bonus_type: str) -> float:
        """計算卡片加成

        Args:
            facility_id: 設施ID
            bonus_type: 加成類型 ('production_efficiency', 'xp_bonus', 'speed_bonus')

        Returns:
            float: 加成係數 (0.0 = 無加成, 0.1 = 10%加成)
        """
        try:
            if facility_id is None:
                return 0.0
            # 獲取指派到設施的卡片
            assignment = await self.repository.get_facility_card_assignment(facility_id)
            if not assignment:
                return 0.0

            # 獲取卡片信息
            from gacha.repositories.collection import user_collection_repository

            card_collection = (
                await user_collection_repository.get_user_collection_by_id(
                    assignment.user_collection_id
                )
            )
            if not card_collection or not card_collection.card:
                return 0.0

            card = card_collection.card

            # 計算加成（基於卡片稀有度和星級）
            base_bonus = 0.0

            # 稀有度加成
            rarity_bonuses = {
                1: 0.05,  # C卡 5%
                2: 0.10,  # R卡 10%
                3: 0.15,  # SR卡 15%
                4: 0.25,  # SSR卡 25%
                5: 0.40,  # UR卡 40%
            }
            base_bonus += rarity_bonuses.get(card.rarity.value, 0.0)

            # 星級加成（每星+2%）
            star_bonus = card_collection.star_level * 0.02
            base_bonus += star_bonus

            # 根據加成類型調整
            type_multipliers = {
                "production_efficiency": 1.0,
                "xp_bonus": 0.8,  # 經驗加成稍低
                "speed_bonus": 0.6,  # 速度加成更低
            }

            final_bonus = base_bonus * type_multipliers.get(bonus_type, 1.0)
            return min(final_bonus, 2.0)  # 最大200%加成

        except Exception as e:
            logger.error("計算卡片加成失敗: %s", e)
            return 0.0

    async def _calculate_research_bonus(self, user_id: int, bonus_type: str) -> float:
        """計算研究加成

        Args:
            user_id: 用戶ID
            bonus_type: 加成類型

        Returns:
            float: 加成係數
        """
        try:
            # 獲取相關研究項目
            research_projects = {
                "production_efficiency": "production_efficiency",
                "xp_bonus": "learning_enhancement",
                "speed_bonus": "automation_tech",
            }

            project_id = research_projects.get(bonus_type)
            if not project_id:
                return 0.0

            # 獲取研究等級
            research = await self.repository.get_research_level(user_id, project_id)
            if not research:
                return 0.0

            # 獲取研究配置 - 使用正確的方法名稱
            research_config = self.game_data.get_research_project_config(project_id)
            if not research_config:
                return 0.0

            # 計算加成
            bonus = research.level * research_config.effect_per_level
            return min(bonus, 10.0)  # 最大1000%加成

        except Exception as e:
            logger.error("計算研究加成失敗: %s", e)
            return 0.0

    async def _check_skill_level_requirement(
        self, user_id: int, skill_id: str, min_level: int
    ) -> bool:
        """檢查技能等級需求

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            min_level: 最低等級需求

        Returns:
            bool: 是否滿足需求
        """
        skill = await self.repository.get_user_skill(user_id, skill_id)
        if not skill:
            return False

        return skill.level >= min_level

    async def _get_drop_table_results(
        self, user_id: int, skill_id: str, drop_tables: list
    ) -> list:
        """根據掉落表計算產出結果

        Args:
            user_id: 用戶ID
            skill_id: 相關技能ID
            drop_tables: 掉落表配置

        Returns:
            list: 產出結果列表
        """
        import random

        skill = await self.repository.get_user_skill(user_id, skill_id)
        skill_level = skill.level  # 技能已在需求檢查階段確保存在

        results = []

        for drop_table in drop_tables:
            level_range = drop_table.get("level_range", [1, 999])
            if level_range[0] <= skill_level <= level_range[1]:
                items = drop_table.get("items", {})

                for item_id, item_config in items.items():
                    chance = item_config.get("chance", 1.0)
                    if random.random() <= chance:
                        amount_range = item_config.get("amount", [1, 1])
                        quantity: int
                        if isinstance(amount_range, list) and len(amount_range) == 2:
                            quantity = random.randint(amount_range[0], amount_range[1])
                        elif isinstance(amount_range, int):
                            quantity = amount_range
                        else:
                            quantity = 1

                        # 應用技能加成
                        final_quantity = await self._calculate_output_quantity(
                            quantity, user_id, skill_id
                        )

                        results.append(
                            {
                                "item_id": item_id,
                                "quantity": final_quantity,
                                "xp": item_config.get("xp", 1),
                            }
                        )

        return results
