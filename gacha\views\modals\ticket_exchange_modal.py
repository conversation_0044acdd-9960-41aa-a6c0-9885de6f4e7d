from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import TextInput

from gacha.exceptions import BusinessError, InvalidQuantityError
from gacha.services import shop_service
from gacha.views.embeds.shop.random_ticket_confirmation_embed_builder import (
    build_random_ticket_confirmation_embed,
)
from gacha.views.embeds.shop.random_ticket_result_embed_builder import (
    build_random_ticket_result_embed,
)
from gacha.views.shop.specific_ticket_selection_view import SpecificTicketSelectionView
from gacha.views.ui_components.confirmation import ConfirmationView
from utils.base_modal import BaseModal
from utils.logger import logger

if TYPE_CHECKING:
    from utils.error_handler import BotType


class TicketExchangeModal(BaseModal, title="輸入兌換數量"):
    def __init__(
        self,
        bot: "BotType",
        shop_item_id: str,
        original_message_to_edit: discord.Message,
        ticket_name: Optional[str] = None,
        timeout: int = 300,
    ):
        super().__init__(bot=bot, title="輸入兌換數量", timeout=timeout)
        self.shop_item_id = shop_item_id
        self.original_message_to_edit = original_message_to_edit
        self.ticket_name = ticket_name
        self.quantity_input = TextInput(
            label="請輸入兌換數量",
            placeholder="例如: 1",
            default="1",
            min_length=1,
            max_length=3,
            required=True,
        )
        self.add_item(self.quantity_input)

    async def on_submit(self, interaction: discord.Interaction):
        """處理 Modal 提交"""
        quantity_str = self.quantity_input.value.strip()
        user_id = interaction.user.id

        try:
            quantity = int(quantity_str)
            if quantity <= 0:
                raise InvalidQuantityError(quantity)
        except ValueError as e:
            raise InvalidQuantityError(quantity_str) from e

        await interaction.response.defer(ephemeral=True)

        # 啟動兌換流程
        exchange_result = await shop_service.initiate_ticket_exchange(
            user_id=user_id, shop_item_id=self.shop_item_id, quantity=quantity
        )

        if not exchange_result:
            raise BusinessError("處理您的請求時發生未預期的錯誤。")

        # 獲取會話ID和下一步操作
        session_id = exchange_result.get("session_id")
        next_step = exchange_result.get("next_step")
        message = exchange_result.get("message", "")

        # 如果沒有會話ID，直接返回結果
        if not session_id:
            await interaction.followup.send(message)
            return

        # 獲取會話數據
        session_data_model = shop_service.get_session_data(session_id)
        current_ticket_name = (
            self.ticket_name or session_data_model.ticket_definition.display_name
        )

        # 根據下一步操作處理不同的兌換流程
        if next_step == "confirm_random":

            async def on_confirm(confirm_interaction: discord.Interaction):
                await self._process_random_ticket_confirmation(
                    confirm_interaction, session_id
                )

            async def on_cancel(cancel_interaction: discord.Interaction):
                await shop_service.cancel_exchange_session(session_id=session_id)
                await cancel_interaction.response.edit_message(
                    content="兌換已取消。", embed=None, view=None
                )

            confirm_embed = build_random_ticket_confirmation_embed(
                interaction, session_data_model
            )

            confirm_view = ConfirmationView(
                bot=self.bot,
                user_id=interaction.user.id,
                on_confirm=on_confirm,
                on_cancel=on_cancel,
                confirm_label="確認兌換",
                cancel_label="取消",
            )

            if self.original_message_to_edit:
                await interaction.followup.edit_message(
                    self.original_message_to_edit.id,
                    embed=confirm_embed,
                    view=confirm_view,
                )
            else:
                logger.error(
                    "TicketExchangeModal session %s: original_message_to_edit was None for confirm_random. Sending new public message via followup as fallback.",
                    session_id,
                )
                await interaction.followup.send(
                    embed=confirm_embed, view=confirm_view, ephemeral=False
                )

        elif next_step == "select_specific_card":
            specific_selection_view = SpecificTicketSelectionView(
                bot=self.bot,
                original_interaction=interaction,
                session_id=session_id,
                initial_session_data=session_data_model,
            )

            (
                initial_embed,
                configured_view,
            ) = await specific_selection_view.prepare_initial_message_payload(
                interaction
            )

            if self.original_message_to_edit:
                await interaction.followup.edit_message(
                    self.original_message_to_edit.id,
                    embed=initial_embed,
                    view=configured_view,
                )
                if (
                    hasattr(configured_view, "message")
                    and configured_view.message is None
                ):
                    configured_view.message = self.original_message_to_edit
                elif not hasattr(configured_view, "message"):
                    logger.warning(
                        "TicketExchangeModal: configured_view %s does not have .message attribute to set after edit.",
                        type(configured_view),
                    )
            else:
                logger.error(
                    "TicketExchangeModal session %s: original_message_to_edit was None. Sending new message instead.",
                    session_id,
                )
                if isinstance(
                    interaction.channel,
                    (discord.TextChannel, discord.Thread, discord.VoiceChannel),
                ):
                    new_message = await interaction.channel.send(
                        embed=initial_embed, view=configured_view
                    )
                else:
                    logger.error(
                        "Cannot send message in channel of type %s",
                        type(interaction.channel),
                    )
                    try:
                        new_message = await interaction.user.send(
                            embed=initial_embed, view=configured_view
                        )
                    except discord.Forbidden:
                        logger.error("Cannot send DM to user %s", interaction.user.id)
                        new_message = None
                if (
                    hasattr(configured_view, "message")
                    and configured_view.message is None
                ):
                    configured_view.message = new_message
                elif not hasattr(configured_view, "message"):
                    logger.warning(
                        "TicketExchangeModal: configured_view %s does not have .message attribute to set after new send.",
                        type(configured_view),
                    )
        else:
            response_message = f"成功初始化 {current_ticket_name} 兌換！\n{message}"
            if session_id:
                response_message += f"\n會話 ID: `{session_id}`"
            response_message += "\n請按指示完成後續操作。"
            await interaction.followup.send(response_message, ephemeral=False)

    async def _process_random_ticket_confirmation(
        self, interaction: discord.Interaction, session_id: str
    ):
        initial_session_data = shop_service.get_session_data(session_id)
        ticket_name = initial_session_data.ticket_definition.display_name
        quantity_used = initial_session_data.total_quantity_to_redeem
        ticket_def_pool_type = getattr(
            initial_session_data.ticket_definition, "pool_type", None
        )

        # 根據規範，service 在失敗時會 raise BusinessError，由 on_error 自動處理
        result = await shop_service.process_random_ticket_confirmation(session_id)

        if not result:
            logger.error(
                "process_random_ticket_confirmation returned None for session %s, which is unexpected.",
                session_id,
            )
            raise BusinessError("處理兌換時發生未預期的錯誤，未能獲取結果。")

        drawn_cards_objects = result.get("drawn_cards", [])
        num_drawn_cards = len(drawn_cards_objects)

        if num_drawn_cards == 0:
            result_embed = build_random_ticket_result_embed(
                interaction=interaction,
                ticket_name=ticket_name,
                quantity_used=quantity_used,
                displayed_card=None,
                current_page=0,
                total_pages=0,
                total_cards_drawn=0,
                ticket_definition_pool_type=ticket_def_pool_type,
            )
            await interaction.response.edit_message(embed=result_embed, view=None)
        elif num_drawn_cards == 1:
            result_embed = build_random_ticket_result_embed(
                interaction=interaction,
                ticket_name=ticket_name,
                quantity_used=quantity_used,
                displayed_card=drawn_cards_objects[0],
                current_page=1,
                total_pages=1,
                total_cards_drawn=1,
                ticket_definition_pool_type=ticket_def_pool_type,
            )
            card = drawn_cards_objects[0]
            from gacha.repositories.collection import user_collection_repository

            is_favorite = await user_collection_repository.get_card_favorite_status(
                interaction.user.id, card.card_id
            )
            result_view = discord.ui.View(timeout=180)
            from gacha.views.shop.favorite_button import FavoriteButton

            result_view.add_item(
                FavoriteButton(
                    user_id=interaction.user.id,
                    card_id=card.card_id,
                    is_favorite=is_favorite,
                )
            )
            await interaction.response.edit_message(
                embed=result_embed, view=result_view
            )
        else:
            from gacha.views.shop.random_ticket_result_view import (
                RandomTicketResultView,
            )

            result_pagination_view = RandomTicketResultView(
                original_interaction=interaction,
                ticket_name=ticket_name,
                quantity_used=quantity_used,
                drawn_cards=drawn_cards_objects,
                ticket_definition_pool_type=ticket_def_pool_type,
            )
            (
                initial_embed,
                configured_view,
            ) = await result_pagination_view.prepare_initial_message_payload(
                interaction
            )
            # 根據規範，移除手動錯誤處理。如果 prepare_initial_message_payload 失敗，
            # 它應該拋出異常，由 on_error 處理。
            if initial_embed and configured_view:
                await interaction.response.edit_message(
                    embed=initial_embed,
                    view=configured_view,
                )
            else:
                # 此 else 區塊已被移除，因為失敗情況應由 prepare_initial_message_payload
                # 內部拋出異常來處理，而不是返回 None。
                # 這避免了在 UI 層進行手動錯誤處理和日誌記錄。
                logger.error(
                    "ConfirmButton: Failed to get initial embed/view from RandomTicketResultView for user %s.",
                    interaction.user.id,
                )
                raise BusinessError("處理兌換結果時發生內部錯誤，已通知管理員。")
