"""
天賦被動技能配置的Pydantic模型（星級結構格式）
"""

from typing import Dict, List, Optional

from pydantic import BaseModel, Field, RootModel

from .base_models import EffectDefinition, TargetOverride, TriggerCondition


class PassiveEffectBlock(BaseModel):
    """被動技能效果塊模型"""

    trigger_condition: TriggerCondition
    target_override: Optional[TargetOverride] = None
    effect_definitions: List[EffectDefinition]


class StarLevelEffects(BaseModel):
    """星級效果配置模型"""

    passive_effect_blocks: List[PassiveEffectBlock]


class InnatePassiveSkillConfig(BaseModel):
    """天賦被動技能配置模型（星級結構格式）"""

    name: str
    description_template_by_star_level: Dict[str, str]
    skill_rarity: int = Field(..., ge=1, le=7)
    effects_by_star_level: Dict[str, StarLevelEffects]
    tags: Optional[List[str]] = None


class InnatePassiveSkillsConfig(RootModel[Dict[str, InnatePassiveSkillConfig]]):
    """天賦被動技能配置文件模型"""

    root: Dict[str, InnatePassiveSkillConfig]

    def __getitem__(self, item):
        return self.root[item]

    def get(self, key, default=None):
        return self.root.get(key, default)

    def keys(self):
        return self.root.keys()

    def values(self):
        return self.root.values()

    def items(self):
        return self.root.items()
