from typing import TYPE_CHECKING, Awaitable, Callable, Optional, Union

import discord
from discord.ext import commands

if TYPE_CHECKING:
    BotType = Union[commands.Bot, commands.AutoShardedBot]
else:
    BotType = commands.Bot

from utils.base_view import BaseView
from utils.response_embeds import SuccessEmbed


class ConfirmationView(BaseView):
    """
    一個可重用的確認視圖，遵循 discord.py 的最佳實踐。

    這個視圖使用按鈕裝飾器，並接受回調函數來處理確認和取消操作。

    用法:
    ```python
    async def on_confirm_callback(interaction: discord.Interaction):
        embed = SuccessEmbed(description="操作已確認！")
        await interaction.response.send_message(embed=embed, ephemeral=True)

    async def on_cancel_callback(interaction: discord.Interaction):
        embed = SuccessEmbed(description="操作已取消。")
        await interaction.response.send_message(embed=embed, ephemeral=True)

    view = ConfirmationView(
        bot=bot,
        user_id=interaction.user.id,
        on_confirm=on_confirm_callback,
        on_cancel=on_cancel_callback,
    )
    await interaction.response.send_message("請確認您的操作：", view=view, embed=embed)
    ```
    """

    def __init__(
        self,
        *,
        bot: BotType,
        user_id: int,
        on_confirm: Callable[[discord.Interaction], Awaitable[None]],
        on_cancel: Optional[Callable[[discord.Interaction], Awaitable[None]]] = None,
        timeout: int = 60,
        confirm_label: str = "確認",
        cancel_label: str = "取消",
        confirm_style: discord.ButtonStyle = discord.ButtonStyle.success,
        cancel_style: discord.ButtonStyle = discord.ButtonStyle.danger,
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self._on_confirm = on_confirm
        self._on_cancel = on_cancel

        # 動態修改裝飾器定義的按鈕屬性
        self.confirm_button.label = confirm_label
        self.confirm_button.style = confirm_style
        self.cancel_button.label = cancel_label
        self.cancel_button.style = cancel_style

    def _disable_buttons(self):
        """僅禁用所有按鈕並停止視圖，但不發送回應。"""
        for child in self.children:
            if isinstance(child, discord.ui.Button):
                child.disabled = True
        self.stop()

    @discord.ui.button(
        label="確認",
        style=discord.ButtonStyle.success,
        custom_id="confirmation_view:confirm",
    )
    async def confirm_button(
        self, interaction: discord.Interaction, _button: discord.ui.Button
    ):
        # 禁用按鈕，但不發送回應
        self._disable_buttons()
        # 執行回調，由回調函數負責與 Discord API 的交互（例如，edit_message）
        if self._on_confirm:
            await self._on_confirm(interaction)

    @discord.ui.button(
        label="取消",
        style=discord.ButtonStyle.danger,
        custom_id="confirmation_view:cancel",
    )
    async def cancel_button(
        self, interaction: discord.Interaction, _button: discord.ui.Button
    ):
        # 禁用按鈕，但不發送回應
        self._disable_buttons()
        if self._on_cancel:
            # 執行回調，由回調函數負責交互
            await self._on_cancel(interaction)
        else:
            # 如果沒有提供 on_cancel 回調，提供一個默認的取消行為
            # 這裡需要自己回應，因為沒有回調可以依賴
            embed = SuccessEmbed(description="操作已取消。")
            await interaction.response.edit_message(embed=embed, view=self)
