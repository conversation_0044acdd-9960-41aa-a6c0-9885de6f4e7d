# pre_download_master_card_images.py

import asyncio
import os
import re
import sys
from urllib.parse import urlparse

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
if project_root not in sys.path:
    sys.path.append(project_root)

import aiohttp  # noqa: E402
from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# 尝试从 .env 文件加载环境变量 (從專案根目錄)
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)

# DATABASE_URL = os.environ.get("DATABASE_URL",
# "postgresql://user:password@host:port/database") # 不再使用单一URL
IMAGE_BASE_PATH = "downloaded_gacha_master_cards"  # 图片下载的基础目录
CARD_TABLE_NAME = "gacha_master_cards"

# 稀有度整数到名称的映射
RARITY_MAP = {
    1: "C",
    2: "R",
    3: "SR",
    4: "SSR",
    5: "UR",
    6: "LR",
    7: "EX",
    # 根据您的系统添加更多
}
DEFAULT_RARITY_NAME = "UnknownRarity"

BATCH_SIZE = 1000  # 每批处理的记录数
MAX_RETRIES = 2  # 下载失败时的最大重试次数
RETRY_DELAY = 5  # 重试前等待秒数 (可以考虑指数退避)
DOWNLOAD_TIMEOUT_SECONDS = 180  # 单个文件下载超时时间

# --- 脚本实现 ---


def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其适合作为文件名的一部分（最终统一标准）"""
    if not text:
        return "unknown"

    # 移除换行符
    text = text.replace("\r", "").replace("\n", "")

    # 统一处理特殊替换规则
    text = text.replace("//", "__")
    text = text.replace(", ", ",_")
    # 将冒号替换为下划线，而不是可能包含冒号的'__'
    text = text.replace(":", "_")

    # 将所有空格替换为下划线
    text = text.replace(" ", "_")

    # 移除其他不合法字符
    text = re.sub(r'[\\/*?"<>|]', "", text)

    # 限制长度
    return text[:max_length]


def _save_image_sync(filepath: str, data: bytes):
    """同步保存圖片檔案。"""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "wb") as f:
        f.write(data)


async def download_image(
    session: aiohttp.ClientSession, url: str, filepath: str, retries: int = MAX_RETRIES
):
    """下載單個圖片並保存到文件，包含重試機制。"""
    if not url or not url.startswith(("http://", "https://")):
        print(f"[-] 無效或為空的 URL (跳過): {url}")
        return False

    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/91.0.4472.124 Safari/537.36"
        )
    }
    timeout = aiohttp.ClientTimeout(total=DOWNLOAD_TIMEOUT_SECONDS)

    for attempt in range(retries + 1):
        try:
            if attempt > 0:
                print(f"[*] 重試 {attempt}/{retries} 下載: {url}")
                await asyncio.sleep(RETRY_DELAY)

            async with session.get(url, timeout=timeout, headers=headers) as response:
                if response.status == 200:
                    image_data = await response.read()
                    await asyncio.to_thread(_save_image_sync, filepath, image_data)
                    return True
                if response.status == 404:
                    print(f"[-] 文件未找到 (404)，不重試: {url}")
                    return False

                print(
                    f"[-] 下載失敗 (嘗試 {attempt + 1}，狀態碼 {response.status}): {url}"
                )

        except (asyncio.TimeoutError, aiohttp.ClientError) as e:
            print(
                f"[-] 下載時發生錯誤 (嘗試 {attempt + 1}): {url} "
                f"({type(e).__name__}: {e})"
            )
        except Exception as e:
            print(
                f"[-] 下載時發生未知錯誤 (嘗試 {attempt + 1}): {url} "
                f"({type(e).__name__}: {e})"
            )
            if attempt >= retries:
                return False

    print(f"[!] 達到最大重試次數後下載依然失敗: {url}")
    return False


async def _process_batch(session, rows):
    """處理單一批次的下載任務。"""
    tasks = []
    skipped_count = 0
    failed_meta_count = 0

    for row in rows:
        card_id, card_name, card_series, image_url, rarity_int = (
            row["card_id"],
            row["name"],
            row["series"],
            row["image_url"],
            row["rarity"],
        )
        rarity_name = RARITY_MAP.get(rarity_int, DEFAULT_RARITY_NAME)

        try:
            parsed_url = urlparse(image_url)
            _, ext = os.path.splitext(os.path.basename(parsed_url.path))
            if not ext or ext.lower() not in {".jpg", ".jpeg", ".png", ".gif", ".webp"}:
                ext = ".png"

            name_part = sanitize_filename_part(card_name or card_series)
            image_filename = f"{card_id}_{name_part}{ext}"
            rarity_dir = os.path.join(IMAGE_BASE_PATH, rarity_name)
            filepath = os.path.join(rarity_dir, image_filename)

            if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                skipped_count += 1
                continue

            tasks.append(download_image(session, image_url, filepath))
        except Exception as e:
            print(f"[-] 處理元數據 (ID: {card_id}, URL: {image_url}) 時出錯: {e}")
            failed_meta_count += 1

    successful = 0
    failed = 0
    if tasks:
        print(f"本批次準備下載 {len(tasks)} 張新圖片...")
        results = await asyncio.gather(*tasks)
        successful = sum(1 for r in results if r)
        failed = len(tasks) - successful
        print(f"本批次下載嘗試完成: {successful} 成功, {failed} 失敗.")
    else:
        print("本批次沒有新的圖片需要下載。")

    return successful, failed + failed_meta_count, skipped_count


async def main():
    """主執行函數。"""
    print("--- 開始 gacha_master_cards 圖片預下載腳本 (分批處理) ---")
    os.makedirs(IMAGE_BASE_PATH, exist_ok=True)
    stats = {"success": 0, "fail": 0, "skip": 0, "processed": 0}

    await setup_connections()
    try:
        pool = get_pool()
        if not pool:
            print("[DB-Error] 資料庫連接池初始化失敗。")
            return

        async with pool.acquire() as conn:
            print("[DB] 成功從連接池獲取連接。")

            offset = 0
            while True:
                print(f"--- 處理批次: 從記錄 {offset} 開始，最多 {BATCH_SIZE} 條 ---")
                query = (
                    f"SELECT card_id, name, series, image_url, rarity "
                    f"FROM {CARD_TABLE_NAME} "
                    "WHERE image_url IS NOT NULL AND image_url != '' "
                    f"ORDER BY card_id LIMIT {BATCH_SIZE} OFFSET {offset}"
                )
                rows = await conn.fetch(query)

                if not rows:
                    print(
                        "所有記錄已處理完畢。"
                        if offset > 0
                        else "資料庫中無可下載的卡片。"
                    )
                    break

                stats["processed"] += len(rows)
                connector = aiohttp.TCPConnector(limit_per_host=15, limit=75)
                async with aiohttp.ClientSession(connector=connector) as session:
                    s, f, k = await _process_batch(session, rows)
                    stats["success"] += s
                    stats["fail"] += f
                    stats["skip"] += k

                offset += len(rows)
                if len(rows) < BATCH_SIZE:
                    break
                await asyncio.sleep(2)

    except Exception as e:
        print(f"[Error] 發生意外錯誤: {type(e).__name__} - {e}")
        import traceback

        traceback.print_exc()
    finally:
        await close_connections()
        print("[DB] 資料庫連接已關閉。")

    print("--- 圖片預下載腳本執行完畢 ---")
    print(f"總計處理記錄: {stats['processed']}")
    print(f"成功下載: {stats['success']}")
    print(f"下載失敗: {stats['fail']}")
    print(f"已存在跳過: {stats['skip']}")


if __name__ == "__main__":
    if os.name == "nt":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    # It's generally better to get the loop and run until complete, then close.
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main())
    finally:
        # Allow time for SSL connections to close gracefully if any were made.
        # loop.run_until_complete(asyncio.sleep(0.25))
        # Optional: small delay for cleanup
        loop.close()
