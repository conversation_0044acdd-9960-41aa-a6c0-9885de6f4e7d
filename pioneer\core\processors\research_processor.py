"""
Pioneer System 研究處理器
處理研究室相關動作
"""

from typing import Any, Dict

import gacha.services.economy_service as economy_service
from pioneer.exceptions import (
    PioneerActionError,
    PioneerInsufficientFundsError,
    PioneerNotFoundError,
    PioneerResearchMaxLevelError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

from .base_processor import BaseProcessor


class ResearchProcessor(BaseProcessor):
    """研究動作處理器"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """執行研究動作

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 動作參數

        Returns:
            ActionResult: 執行結果
        """
        project_id = params.get("project_id")
        if not project_id:
            raise PioneerActionError("未指定研究項目")

        # 獲取研究項目配置
        project_config = self.game_data.get_research_project_config(project_id)
        if not project_config:
            raise PioneerNotFoundError(f"研究項目: {project_id}")

        # 獲取用戶當前研究等級
        research_level = await self.repository.get_research_level(user_id, project_id)
        if not research_level:
            # 創建初始研究記錄
            await self.repository.create_research_level(user_id, project_id)
            research_level = await self.repository.get_research_level(
                user_id, project_id
            )

        if not research_level:
            raise PioneerNotFoundError(f"無法獲取或創建研究等級: {project_id}")

        current_level = research_level.level
        next_level = current_level + 1

        # 檢查最大等級限制
        if project_config.max_level and current_level >= project_config.max_level:
            raise PioneerResearchMaxLevelError(
                project_config.name, project_config.max_level
            )

        # 計算升級成本
        upgrade_cost = await self._calculate_research_cost(project_config, next_level)

        # 檢查成本（統一使用油幣）
        balance_info = await economy_service.get_balance(user_id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < upgrade_cost:
            raise PioneerInsufficientFundsError(
                f"油幣不足，需要 {upgrade_cost:,}，可用 {current_balance:,}"
            )

        # 消耗成本
        await economy_service.award_oil(
            user_id=user_id,
            amount=-upgrade_cost,
            transaction_type="pioneer:research",
            reason=f"Research project {project_id}",
        )
        await self.repository.add_research_investment_oil(
            user_id, project_id, upgrade_cost
        )

        # 升級研究等級
        await self.repository.upgrade_research_level(user_id, project_id)

        # 計算新的效果
        new_effect = project_config.effect_per_level * next_level
        total_effect = new_effect * 100  # 轉換為百分比

        return ActionResult.success_result(
            message=f"研究項目 {project_config.name} 升級到 {next_level} 級！\n"
            f"當前效果: +{total_effect:.1f}%",
            costs=[{"type": project_config.currency, "amount": upgrade_cost}],
            rewards=[
                {
                    "type": "research_level",
                    "project_id": project_id,
                    "level": next_level,
                    "effect": new_effect,
                }
            ],
        )

    async def _calculate_research_cost(self, project_config, target_level: int) -> int:
        """計算研究升級成本"""
        formula = ""
        try:
            # 解析成本公式
            formula = project_config.cost_formula

            # 替換變量
            formula = formula.replace("level", str(target_level))

            # 安全的數學表達式求值
            import ast
            import math
            import operator

            # 支援的操作符和函數
            ops = {
                ast.Add: operator.add,
                ast.Sub: operator.sub,
                ast.Mult: operator.mul,
                ast.Div: operator.truediv,
                ast.Pow: operator.pow,
                ast.USub: operator.neg,
            }

            # 支援的函數
            funcs = {
                "pow": pow,
                "sqrt": math.sqrt,
                "log": math.log,
                "exp": math.exp,
            }

            def eval_expr(node) -> float:
                if isinstance(node, ast.Num):
                    if isinstance(node.n, (int, float)):
                        return float(node.n)
                    else:
                        raise TypeError(f"不支援的數字類型: {type(node.n)}")
                elif isinstance(node, ast.Constant):  # Python 3.8+
                    if isinstance(node.value, (int, float)):
                        return float(node.value)
                    else:
                        raise TypeError(f"不支援的常數類型: {type(node.value)}")
                elif isinstance(node, ast.BinOp):
                    return ops[type(node.op)](
                        eval_expr(node.left), eval_expr(node.right)
                    )
                elif isinstance(node, ast.UnaryOp):
                    return ops[type(node.op)](eval_expr(node.operand))
                elif isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        func_name = node.func.id
                        if func_name in funcs:
                            args = [eval_expr(arg) for arg in node.args]
                            return funcs[func_name](*args)
                        else:
                            raise ValueError(f"不支援的函數: {func_name}")
                    else:
                        raise TypeError(f"不支援的函數呼叫類型: {type(node.func)}")
                else:
                    raise TypeError(f"不支援的節點類型: {type(node)}")

            result = eval_expr(ast.parse(formula, mode="eval").body)
            return int(result)

        except Exception as e:
            logger.error("研究成本計算失敗: %s, 錯誤: %s", formula, e)
            # 拋出異常而不是靜默使用後備值，讓管理員知道配置錯誤
            raise PioneerActionError(
                f"研究項目成本公式配置錯誤: {formula}，請聯繫管理員修復"
            ) from e
