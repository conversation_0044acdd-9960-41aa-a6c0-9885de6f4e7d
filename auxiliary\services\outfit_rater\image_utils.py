"""
圖像工具模組 - 提供圖像處理功能
"""

import io
import logging
from typing import Tuple

from PIL import Image


def get_resampling_filter(filter_name: str):
    """獲取 Pillow 重採樣濾鏡，兼容新舊版本"""
    try:
        # Pillow 10.0.0+ 使用 Image.Resampling
        return getattr(Image.Resampling, filter_name)
    except AttributeError:
        # 舊版本 Pillow 直接從 Image 獲取
        return getattr(Image, filter_name, 1)  # 1 是 LANCZOS 的默認值


# 設置日誌
logger = logging.getLogger("ImageUtils")


class ImageUtils:
    """提供圖像處理功能的工具類"""

    @staticmethod
    async def ensure_compatible_format(image_data: bytes) -> bytes:
        """確保圖像格式兼容，將webp等特殊格式轉換為標準格式"""
        if not image_data:
            logger.error("ensure_compatible_format: 接收到空的圖像數據")
            raise ValueError("圖像數據不能為空")

        if not isinstance(image_data, bytes):
            logger.error(
                "ensure_compatible_format: 接收到非bytes類型的數據: %s",
                type(image_data),
            )
            raise TypeError("圖像數據必須是bytes類型")

        logger.info("ensure_compatible_format: 處理 %s 字節的圖像數據", len(image_data))

        def _sync_convert():
            input_buffer = io.BytesIO(image_data)
            input_buffer.seek(0)  # 確保指針在開始位置

            with Image.open(input_buffer) as img:
                img_format = img.format.lower() if img.format else "unknown"
                logger.info(
                    "ensure_compatible_format: 檢測到圖像格式: %s, 模式: %s, 尺寸: %s",
                    img_format,
                    img.mode,
                    img.size,
                )

                # 只處理需要轉換的格式
                if img_format in ["webp", "tiff", "bmp"]:
                    logger.info("轉換%s格式", img_format)
                    output = io.BytesIO()

                    # 根據圖像模式選擇適當的格式
                    if img.mode == "RGBA":
                        img.save(output, format="PNG")
                        logger.info("轉換為PNG格式（保留透明度）")
                    else:
                        img.save(output, format="JPEG", quality=95)
                        logger.info("轉換為JPEG格式")

                    converted_data = output.getvalue()
                    logger.info("格式轉換完成: %s 字節", len(converted_data))
                    return converted_data

                logger.info("無需轉換: %s", img_format)
                return image_data

        try:
            import asyncio

            return await asyncio.to_thread(_sync_convert)

        except Exception as e:
            logger.error("格式轉換失敗: %s", str(e))
            logger.error(
                "圖像數據前32字節: %s",
                image_data[:32].hex() if len(image_data) >= 32 else image_data.hex(),
            )

            # 嘗試檢測是否是損壞的圖像數據
            if len(image_data) < 100:
                logger.error("圖像數據太小，可能已損壞")
                raise ValueError("圖像數據太小或已損壞") from e

            # 重新拋出原始錯誤
            raise ValueError(f"無法處理圖像格式: {str(e)}") from e

    @staticmethod
    async def resize_image(
        image_data: bytes,
        max_size: int = 2 * 1024 * 1024,
        max_dimensions: Tuple[int, int] = (1000, 1000),
    ) -> bytes:
        """調整圖像大小以符合API限制"""

        def _sync_resize():
            # 打開圖像並調整尺寸
            img = Image.open(io.BytesIO(image_data))
            width, height = img.size

            # 需要調整尺寸
            if width > max_dimensions[0] or height > max_dimensions[1]:
                ratio = min(max_dimensions[0] / width, max_dimensions[1] / height)
                new_width, new_height = int(width * ratio), int(height * ratio)
                logger.info(
                    "調整尺寸: %sx%s → %sx%s", width, height, new_width, new_height
                )
                img = img.resize(
                    (new_width, new_height), get_resampling_filter("LANCZOS")
                )

            # 調整品質
            resized_data = image_data
            for quality in [85, 80, 75, 70, 65, 60, 55, 50]:
                output = io.BytesIO()
                img.save(output, format="JPEG", quality=quality)
                resized_data = output.getvalue()

                if len(resized_data) <= max_size:
                    logger.info(
                        "調整完成: %s 字節, 品質: %s", len(resized_data), quality
                    )
                    return resized_data

                logger.debug(
                    "尺寸仍超過限制: %s > %s, 品質: %s",
                    len(resized_data),
                    max_size,
                    quality,
                )

            # 返回最後一次調整的結果
            logger.warning("無法達到目標大小: %s > %s", len(resized_data), max_size)
            return resized_data

        try:
            # 確保格式兼容
            image_data = await ImageUtils.ensure_compatible_format(image_data)

            # 已符合大小限制
            if len(image_data) <= max_size:
                logger.info("圖像大小已符合要求: %s 字節", len(image_data))
                return image_data

            import asyncio

            return await asyncio.to_thread(_sync_resize)

        except Exception as e:
            logger.error("調整圖像失敗: %s", str(e))
            return image_data
