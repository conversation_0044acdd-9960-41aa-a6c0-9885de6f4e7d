"""
RPG 技能生成器模組

這個模組包含了用於生成 RPG 技能的通用組件，支援主動技能、被動技能和天賦技能的生成。

主要組件：
- error_handler: 錯誤處理裝飾器
- template_processor: 模板處理基類
- rarity_manager: 稀有度管理器
- description_generator: 描述生成器
- skill_generator_base: 基礎技能生成器
- active_skill_generator: 主動技能生成器
- passive_skill_generator: 被動技能生成器
"""

from .active_skill_generator import ActiveSkillGenerator
from .active_skill_helpers import ActiveSkillHelpers
from .description_generator import DescriptionGenerator
from .error_handler import error_handler
from .passive_skill_generator import PassiveSkillGenerator
from .passive_skill_helpers import PassiveSkillHelpers
from .rarity_manager import <PERSON><PERSON>Manager
from .skill_generator_base import SkillGeneratorBase
from .template_processor import TemplateProcessor

__all__ = [
    "error_handler",
    "TemplateProcessor",
    "RarityManager",
    "DescriptionGenerator",
    "SkillGeneratorBase",
    "ActiveSkillGenerator",
    "ActiveSkillHelpers",
    "PassiveSkillGenerator",
    "PassiveSkillHelpers",
]

__version__ = "1.0.0"
