# 🔍 強大的篩選管理系統使用指南

## 概述

這個強大的篩選管理系統為卡冊提供了完全自訂的篩選功能，讓用戶可以精確控制要顯示或隱藏的卡片內容。

## 功能特點

### ✨ 主要功能
- 🌟 **稀有度篩選**: 自由選擇要顯示的稀有度 (C, R, SR, SSR, UR, LR, EX)
- 🎯 **卡池篩選**: 選擇特定卡池的卡片 (主卡池、典藏卡池、Hololive等)
- 📚 **系列篩選**: 按系列名稱篩選卡片
- ❤️ **最愛篩選**: 只顯示最愛或非最愛卡片
- 🔢 **數量篩選**: 篩選重複卡片或單張卡片
- 🔄 **組合篩選**: 同時應用多種篩選條件

## 使用方法

### 進入篩選模式
1. 使用 `/mw` 命令查看卡冊
2. 點擊 **功能** 按鈕進入功能模式
3. 點擊 **🔍 篩選** 按鈕進入篩選設定界面

### 篩選設定界面
篩選界面提供兩種操作方式：

#### 方式一：類別選單
1. 從主選單選擇篩選類別：
   - 🌟 稀有度篩選
   - 🎯 卡池篩選
   - 📚 系列篩選
   - ❤️ 最愛篩選
   - 🔢 數量篩選

2. 在對應的選單中進行多選設


### 篩選狀態管理
- **📋 查看狀態**: 查看當前所有篩選條件
- **🔄 重置**: 清除所有篩選條件
- **❌ 取消**: 取消設定，不做任何更改


## 技術實現

### 核心組件
- `CollectionFilters`: 篩選條件數據模型
- `FilterManagementView`: 主篩選管理界面
- `RarityFilterSelect`: 稀有度篩選選單
- `PoolFilterSelect`: 卡池篩選選單
- `SeriesFilterSelect`: 系列篩選選單

### 篩選邏輯
- **包含篩選**: 只顯示選中的項目 (`rarity_in`, `pool_type_in`)
- **排除篩選**: 隱藏選中的項目 (`rarity_not_in`, `series_not_in`)
- **組合篩選**: 多個條件同時生效
- **狀態持久化**: 篩選條件在會話期間保持

### 數據結構
```python
@dataclass
class CollectionFilters:
    # 基本篩選
    pool_type: Optional[str] = None
    rarity_in: Optional[List[int]] = None
    series: Optional[str] = None
    
    # 高級篩選
    pool_type_in: Optional[List[str]] = None
    rarity_not_in: Optional[List[int]] = None
    series_not_in: Optional[List[str]] = None
    
    # 特殊篩選
    only_duplicates: bool = False
    quantity_greater_than: Optional[int] = None
    extra_filters: Optional[dict] = None
```

## 擴展性

### 添加新的篩選類型
1. 在 `CollectionFilters` 中添加新字段
2. 在 `FilterMainSelect` 中添加新選項
3. 創建對應的篩選選單組件
4. 在 `FilterManagementView` 中添加處理邏輯

### 自訂快速篩選
在 `FilterManagementView._add_quick_filter_buttons()` 中添加新的快速按鈕。

## 注意事項

1. **Discord限制**: 每個選單最多25個選項
2. **性能考慮**: 複雜篩選可能影響查詢性能
3. **用戶體驗**: 篩選狀態會在卡冊標題中顯示
4. **數據一致性**: 篩選條件會自動驗證和清理

## 故障排除

### 常見問題
1. **篩選不生效**: 檢查是否點擊了 **應用篩選** 按鈕
2. **選單選項過多**: 系列篩選限制為23個選項
3. **篩選衝突**: 某些篩選組合可能導致無結果


