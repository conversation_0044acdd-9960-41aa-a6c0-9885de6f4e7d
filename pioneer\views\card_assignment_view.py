"""
Pioneer System 卡片指派視圖
讓玩家將 Gacha 卡片指派到設施上以獲得加成
"""

from typing import TYPE_CHECKING, cast

import discord

from database.postgresql.async_manager import get_redis_client
from pioneer.exceptions import (
    PioneerError,
    PioneerInvalidSelectionError,
)
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader
    from utils.base_view import BotType


class CardAssignmentView(BaseView):
    """卡片指派視圖"""

    def __init__(
        self,
        user_id: int,
        facility_id: int,
        game_data: "GameDataLoader",
        repository,
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facility_id = facility_id
        self.repository = repository
        self.available_cards = []
        self.current_page = 0
        self.cards_per_page = 5

    async def update_select_options(self):
        """更新選擇下拉選單的選項"""
        # 清除現有的選擇器
        for item in self.children:
            if isinstance(item, discord.ui.Select):
                self.remove_item(item)

        # 如果有可用卡片，創建新的選擇器
        if self.available_cards:
            start_idx = self.current_page * self.cards_per_page
            end_idx = start_idx + self.cards_per_page
            page_cards = self.available_cards[start_idx:end_idx]

            options = []
            for i, (card, collection) in enumerate(page_cards):
                rarity_emojis = {1: "🤍", 2: "💙", 3: "💜", 4: "💛", 5: "❤️"}
                rarity_emoji = rarity_emojis.get(card.rarity.value, "⚪")
                star_display = "⭐" * collection.star_level
                bonus = self._calculate_card_bonus_display(card, collection)

                options.append(
                    discord.SelectOption(
                        label=f"{card.name} {star_display}",
                        description=f"基礎效率 +{bonus:.1f}%",  # 明確指出是基礎效率
                        emoji=rarity_emoji,
                        value=str(i),
                    )
                )

            if options:
                select = discord.ui.Select(
                    placeholder="選擇要指派的卡片...",
                    min_values=1,
                    max_values=1,
                    options=options,
                    row=2,
                )
                select.callback = self._card_select_callback
                self.add_item(select)

    async def _card_select_callback(self, interaction: discord.Interaction):
        """卡片選擇回調"""
        try:
            data = cast(dict, interaction.data)
            card_index = int(data["values"][0])
            start_idx = self.current_page * self.cards_per_page
            actual_index = start_idx + card_index

            if actual_index >= len(self.available_cards):
                raise PioneerInvalidSelectionError("卡片", "索引超出範圍")

            card, collection = self.available_cards[actual_index]

            # 指派卡片
            await self.repository.assign_card_to_facility(
                self.facility_id, collection.id
            )

            rarity_emojis = {1: "🤍", 2: "💙", 3: "💜", 4: "💛", 5: "❤️"}
            rarity_emoji = rarity_emojis.get(card.rarity.value, "⚪")
            star_display = "⭐" * collection.star_level
            bonus = self._calculate_card_bonus_display(card, collection)

            embed = SuccessEmbed(
                title="✅ 指派成功",
                description=f"已將 {rarity_emoji} {card.name} {star_display} 指派到設施\n"
                f"基礎效率加成: +{bonus:.1f}%",  # 明確指出是基礎效率
            )
            await interaction.response.edit_message(embed=embed, view=None)

        except PioneerError:
            # 業務邏輯異常會被 BaseView.on_error 處理
            raise

    async def create_assignment_embed(self) -> discord.Embed:
        """創建卡片指派 Embed"""
        try:
            # 獲取設施信息
            facility = await self.repository.get_facility(self.facility_id)
            facility_config = self.game_data.get_facility_config(facility.facility_type)

            # 獲取當前指派的卡片
            current_assignment = await self.repository.get_facility_card_assignment(
                self.facility_id
            )

            embed = discord.Embed(
                title=f"🎴 卡片指派 - {facility_config.name if facility_config else facility.facility_type}",
                description="選擇要指派到此設施的卡片以獲得生產加成",
                color=0x2B2D31,
            )

            # 顯示當前指派
            if current_assignment:
                from pioneer.repositories.gacha_bridge_repo import (
                    get_user_collection_by_id,
                )

                card_collection = await get_user_collection_by_id(
                    current_assignment.user_collection_id
                )
                if card_collection and card_collection.card:
                    card = card_collection.card
                    rarity_emojis = {1: "🤍", 2: "💙", 3: "💜", 4: "💛", 5: "❤️"}
                    rarity_emoji = rarity_emojis.get(card.rarity.value, "⚪")
                    star_display = "⭐" * card_collection.star_level

                    embed.add_field(
                        name="🎯 當前指派",
                        value=f"{rarity_emoji} {card.name} {star_display}\n"
                        f"基礎效率: +{self._calculate_card_bonus_display(card, card_collection):.1f}%",  # 明確指出是基礎效率
                        inline=False,
                    )
            else:
                embed.add_field(name="🎯 當前指派", value="無卡片指派", inline=False)

            # 獲取可用卡片
            await self._load_available_cards()
            await self.update_select_options()

            # 顯示可用卡片列表
            if self.available_cards:
                start_idx = self.current_page * self.cards_per_page
                end_idx = start_idx + self.cards_per_page
                page_cards = self.available_cards[start_idx:end_idx]

                card_list = []
                for i, (card, collection) in enumerate(page_cards):
                    rarity_emojis = {1: "🤍", 2: "💙", 3: "💜", 4: "💛", 5: "❤️"}
                    rarity_emoji = rarity_emojis.get(card.rarity.value, "⚪")
                    star_display = "⭐" * collection.star_level
                    bonus = self._calculate_card_bonus_display(card, collection)

                    card_list.append(
                        f"{i + 1}. {rarity_emoji} {card.name} {star_display} (+{bonus:.1f}%)"
                    )

                embed.add_field(
                    name=f"📋 可用卡片 (第 {self.current_page + 1} 頁)",
                    value="\n".join(card_list) if card_list else "無可用卡片",
                    inline=False,
                )

                # 分頁信息
                total_pages = (
                    len(self.available_cards) + self.cards_per_page - 1
                ) // self.cards_per_page
                embed.set_footer(
                    text=f"第 {self.current_page + 1}/{total_pages} 頁 • 總共 {len(self.available_cards)} 張卡片"
                )
            else:
                embed.add_field(
                    name="📋 可用卡片", value="您沒有可指派的卡片", inline=False
                )

            return embed

        except Exception as e:
            logger.error("創建卡片指派 Embed 失敗: %s", e)
            raise PioneerError("無法載入卡片指派信息") from e

    async def _load_available_cards(self):
        """載入可用的卡片"""
        try:
            from pioneer.repositories.gacha_bridge_repo import get_user_collections

            redis_client = get_redis_client()
            if not redis_client:
                logger.error("Redis client is not available for loading cards.")
                self.available_cards = []
                return

            # 獲取用戶的所有卡片
            result = await get_user_collections(
                redis_client=redis_client, user_id=self.user_id
            )
            collections = result.get("cards", [])

            self.available_cards = []
            for collection in collections:
                if not collection.card:
                    continue
                # 檢查卡片是否已被指派到其他設施
                existing_assignment = (
                    await self.repository.get_card_assignment_by_collection_id(
                        collection.id
                    )
                )
                if (
                    existing_assignment
                    and existing_assignment.facility_id != self.facility_id
                ):
                    continue  # 已被指派到其他設施

                self.available_cards.append((collection.card, collection))

            # 按稀有度和星級排序
            self.available_cards.sort(
                key=lambda x: (x[0].rarity.value, x[1].star_level), reverse=True
            )

        except Exception as e:
            logger.error("載入可用卡片失敗: %s", e)
            self.available_cards = []

    def _calculate_card_bonus_display(self, card, collection) -> float:
        """計算卡片加成用於顯示，與核心邏輯保持一致"""
        """
        注意：此函數僅用於在 UI 上顯示最核心的加成值（生產效率）。
        實際生效的完整加成計算（包括 xp_bonus, speed_bonus 等）
        位於 pioneer.core.processors.base_processor._calculate_card_bonus 中。
        
        如果未來需要在 UI 上顯示所有類型的加成，需要擴展此函數的邏輯
        以匹配 base_processor 中的計算。
        """
        # 稀有度加成（與 base_processor.py 保持一致）
        rarity_bonuses = {
            1: 0.05,  # C卡 5%
            2: 0.10,  # R卡 10%
            3: 0.15,  # SR卡 15%
            4: 0.25,  # SSR卡 25%
            5: 0.40,  # UR卡 40%
        }
        base_bonus = rarity_bonuses.get(card.rarity.value, 0.0)

        # 星級加成（每星+2%）
        star_bonus = collection.star_level * 0.02

        # 這裡只計算主要加成類型 'production_efficiency' 的值用於顯示
        # 因為這是設施最核心的加成
        type_multiplier = 1.0  # production_efficiency 的乘數

        final_bonus = (base_bonus + star_bonus) * type_multiplier
        return final_bonus * 100  # 轉換為百分比顯示

    @discord.ui.button(
        label="上一頁", style=discord.ButtonStyle.secondary, emoji="◀️", row=0
    )
    async def previous_page(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """上一頁按鈕"""
        if self.current_page > 0:
            self.current_page -= 1
            await self.update_select_options()
            embed = await self.create_assignment_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(
        label="下一頁", style=discord.ButtonStyle.secondary, emoji="▶️", row=0
    )
    async def next_page(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """下一頁按鈕"""
        total_pages = (
            len(self.available_cards) + self.cards_per_page - 1
        ) // self.cards_per_page
        if self.current_page < total_pages - 1:
            self.current_page += 1
            await self.update_select_options()
            embed = await self.create_assignment_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(
        label="移除指派", style=discord.ButtonStyle.danger, emoji="🗑️", row=1
    )
    async def remove_assignment(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """移除卡片指派"""
        # 移除指派
        await self.repository.remove_facility_card_assignment(self.facility_id)

        embed = SuccessEmbed(title="✅ 移除成功", description="已移除設施的卡片指派")
        await interaction.response.edit_message(embed=embed, view=None)
