# Story系統 World Info 完整開發指南

## 🎯 概覽

World Info是Story系統的世界書功能，支持根據上下文動態注入背景信息到AI對話中。具備順序控制、關鍵字觸發、連鎖反應等高級功能。

## 🔧 核心機制

### 注入方式

1. **Prompt模板注入** (`insertion_target: "prompt_library"`)
   - 注入到AI的系統提示中，優先級最高
   - 通過`insertion_id`指定注入位置
   - 目前支持`world_info_content`變量
   - **支持關鍵字觸發**和**順序控制**
   - **不支持連鎖觸發**（因為在歷史記錄處理之前）

2. **歷史記錄注入** (`insertion_target: "history"`)  
   - 注入到對話歷史中，模擬真實對話內容
   - 通過`insertion_depth`控制注入深度
   - 支持不同的`role`（system/user/assistant）
   - **支持關鍵字觸發**、**連鎖觸發**和**順序控制**

### 觸發機制

1. **基礎觸發**：`enabled`和`trigger_probability`
2. **關鍵字觸發**：`trigger_keywords`數組匹配歷史記錄
3. **連鎖觸發**：已觸發的world info內容可觸發其他world info
4. **順序控制**：`order`參數控制同位置多個world info的排列

## 📝 完整參數說明

### 必需參數

```python
{
    "id": "unique_identifier",           # 唯一標識符，用於防重複觸發
    "name": "Display Name",              # 顯示名稱，用於管理界面
    "content": "World info content",     # 實際注入的內容
    "enabled": True,                     # 是否啟用此規則
}
```

### 注入控制參數

```python
{
    # 注入目標（二選一）
    "insertion_target": "prompt_library",  # 注入到prompt模板
    "insertion_id": "world_info_content",  # 對應模板變量名
    
    # 或者
    "insertion_target": "history",         # 注入到歷史記錄
    "insertion_depth": 0,                  # 注入深度（0=最新位置）
    "role": "system",                      # 角色：system/user/assistant
}
```

### 觸發控制參數

```python
{
    "trigger_probability": 1.0,            # 觸發機率（0.0-1.0）
    "trigger_keywords": ["關鍵字1", "關鍵字2"],  # 關鍵字觸發列表（可選）
    "order": 0,                           # 排序順序，數字越小越在前
}
```

### 主題級別配置

```python
THEME_CONFIG = {
    "world_info_chain_depth": 3,          # 連鎖觸發最大深度（默認3）
    "custom_world_info_rules": [
        # world info規則列表
    ]
}
```

## 🎨 創建新劇本指南

### 基礎劇本結構

```python
# auxiliary/services/story/themes/my_theme.py

THEME_CONFIG = {
    # === 基本信息 ===
    "title": "我的劇本",
    "description": "劇本描述",
    "prompt_library": "beilu",              # 使用的提示詞庫
    "image_url": "https://example.com/image.png",
    
    # === 劇情設定 ===
    "opening_line": "故事開始了...",
    "initial_options": [
        "選項1",
        "選項2", 
        "選項3",
        "選項4",
    ],
    
    # === 角色設定 ===
    "character_sheet": """
# 主角設定
name: 角色名
age: 年齡
background: 背景故事
""",
    
    # === 狀態欄設定 ===
    "status_block_type": "rpg",            # 狀態欄類型
    "status_block_rules": """
顯示角色的基本狀態信息
""",
    
    # === World Info 規則 ===
    "world_info_chain_depth": 3,           # 連鎖觸發深度
    "custom_world_info_rules": [
        # 在這裡添加world info規則
    ],
}
```

### World Info 規則示例

#### 1. 基礎世界觀注入

```python
{
    "id": "basic_world_setting",
    "name": "基礎世界觀",
    "content": """<世界設定>
這是一個魔法與科技並存的世界。
古老的魔法學院坐落在現代化都市的中心。
</世界設定>""",
    "insertion_target": "prompt_library",
    "insertion_id": "world_info_content",
    "order": 1,
    "trigger_probability": 1.0,
    "enabled": True,
},
```

#### 2. 情境觸發規則

```python
{
    "id": "combat_system",
    "name": "戰鬥系統",
    "content": """<戰鬥提示>
進入戰鬥模式。考慮使用技能組合。
</戰鬥提示>""",
    "insertion_target": "history",
    "insertion_depth": 0,
    "role": "system",
    "order": 1,
    "trigger_keywords": ["戰鬥", "攻擊", "敵人", "怪物"],
    "trigger_probability": 0.8,
    "enabled": True,
},
```

#### 3. 連鎖觸發規則

```python
# 第一環：檢測魔法
{
    "id": "magic_detected",
    "name": "魔法檢測",
    "content": "感受到魔法能量的波動，周圍的魔法防護啟動。",
    "insertion_target": "history",
    "insertion_depth": 0,
    "role": "assistant",
    "order": 1,
    "trigger_keywords": ["魔法", "法術", "咒語"],
    "trigger_probability": 1.0,
    "enabled": True,
},

# 第二環：響應防護
{
    "id": "protection_active",
    "name": "防護啟動",
    "content": "魔法防護屏障展開，需要小心行動。",
    "insertion_target": "history", 
    "insertion_depth": 0,
    "role": "system",
    "order": 2,
    "trigger_keywords": ["防護", "屏障"],
    "trigger_probability": 1.0,
    "enabled": True,
},
```

#### 4. 多層次信息

```python
# 基礎信息
{
    "id": "location_basic",
    "name": "地點基礎信息",
    "content": "這裡是古老的圖書館。",
    "insertion_target": "history",
    "insertion_depth": 1,
    "role": "assistant",
    "order": 1,
    "trigger_keywords": ["圖書館", "書籍"],
    "trigger_probability": 1.0,
    "enabled": True,
},

# 詳細信息
{
    "id": "location_detail",
    "name": "地點詳細信息", 
    "content": "書架上的古籍散發著神秘的光芒。",
    "insertion_target": "history",
    "insertion_depth": 1,
    "role": "assistant", 
    "order": 5,
    "trigger_keywords": ["圖書館", "書籍"],
    "trigger_probability": 0.6,
    "enabled": True,
},
```

## 🔍 調試和測試

### 測試關鍵字觸發

```python
# 在主題文件中添加測試規則
{
    "id": "debug_trigger",
    "name": "調試觸發器",
    "content": "DEBUG: 檢測到關鍵字觸發",
    "insertion_target": "history",
    "insertion_depth": 0,
    "role": "system",
    "order": -1,  # 優先顯示
    "trigger_keywords": ["測試", "debug"],
    "trigger_probability": 1.0,
    "enabled": True,
},
```

### 監控連鎖觸發

```python
# 添加連鎖觸發標記
{
    "id": "chain_monitor",
    "name": "連鎖監控",
    "content": "CHAIN: 第1級觸發 -> 啟動後續檢查",
    "trigger_keywords": ["連鎖"],
    # ... 其他設定
},
```

## ⚡ 效能優化建議

### 關鍵字設計

- **精確關鍵字**：使用具體詞彙而非通用詞
- **關鍵字數量**：每個規則建議不超過5個關鍵字
- **同義詞覆蓋**：包含常見變體（如"戰鬥"和"戰斗"）

### 連鎖控制

```python
# 效能優先配置
"world_info_chain_depth": 1,    # 禁用連鎖觸發

# 平衡配置（推薦）
"world_info_chain_depth": 3,    # 適度連鎖效果

# 功能豐富配置  
"world_info_chain_depth": 5,    # 複雜連鎖效果
```

### 觸發機率策略

```python
# 核心信息：高機率
"trigger_probability": 1.0,

# 補充信息：中等機率
"trigger_probability": 0.6,

# 裝飾性信息：低機率
"trigger_probability": 0.3,
```

## 📚 實際使用案例

### RPG冒險劇本

```python
"custom_world_info_rules": [
    # 世界觀基礎
    {
        "id": "rpg_world_base",
        "content": "經典奇幻RPG世界，有勇者、魔王、公會系統。",
        "insertion_target": "prompt_library",
        "insertion_id": "world_info_content",
        "order": 1,
        "trigger_probability": 1.0,
        "enabled": True,
    },
    
    # 戰鬥系統
    {
        "id": "combat_system",
        "content": "戰鬥採用回合制，可使用技能、魔法、道具。",
        "insertion_target": "history",
        "insertion_depth": 0,
        "role": "system",
        "trigger_keywords": ["戰鬥", "敵人", "攻擊"],
        "trigger_probability": 0.9,
        "enabled": True,
    },
    
    # 經濟系統
    {
        "id": "economy_system", 
        "content": "城鎮中有商店、酒館、公會，可購買裝備和接任務。",
        "insertion_target": "history",
        "insertion_depth": 1,
        "role": "assistant",
        "trigger_keywords": ["商店", "購買", "金幣", "裝備"],
        "trigger_probability": 0.7,
        "enabled": True,
    },
]
```

### 現代都市劇本

```python
"custom_world_info_rules": [
    # 現代設定
    {
        "id": "modern_setting",
        "content": "現代都市背景，有手機、網絡、現代交通工具。",
        "insertion_target": "prompt_library", 
        "insertion_id": "world_info_content",
        "trigger_probability": 1.0,
        "enabled": True,
    },
    
    # 科技元素
    {
        "id": "tech_elements",
        "content": "可以使用智能手機查資料、叫車、支付等。",
        "insertion_target": "history",
        "insertion_depth": 0,
        "role": "assistant",
        "trigger_keywords": ["手機", "網絡", "科技", "APP"],
        "trigger_probability": 0.8,
        "enabled": True,
    },
]
```

## 🔍 代碼審查總結

### 核心實現文件
- `auxiliary/services/story/prompts.py`: 主要邏輯實現
- `auxiliary/services/story/themes/`: 劇本主題目錄
- `auxiliary/services/story/WORLD_INFO_ENHANCEMENT.md`: 功能文檔

### 已修復的問題
1. **Prompt庫關鍵字觸發**：現在prompt_library注入也支持trigger_keywords
2. **連鎖觸發安全性**：防重複觸發和無限循環機制
3. **效能優化**：早期返回和可配置連鎖深度

### 執行流程
1. **Prompt階段**：處理`insertion_target: "prompt_library"`的規則
2. **歷史階段**：處理`insertion_target: "history"`的規則，支持連鎖觸發
3. **排序注入**：按depth和order排序後注入到消息列表

### 限制說明
- Prompt模板注入不支持連鎖觸發（執行時間點限制）
- 連鎖觸發最多5輪，可通過`world_info_chain_depth`配置
- 關鍵字匹配使用簡單字符串包含，不支持正則表達式

### 測試建議
```python
# 在開發劇本時可添加調試規則
{
    "id": "debug_all_triggers",
    "name": "全觸發調試",
    "content": "DEBUG: 所有條件都滿足",
    "insertion_target": "history",
    "insertion_depth": 0,
    "role": "system",
    "order": -999,  # 最高優先級
    "trigger_probability": 1.0,
    "enabled": True,  # 設為False來關閉調試
},
```

這個完整指南涵蓋了World Info系統的所有功能和使用方法，可以幫助創建豐富動態的劇本體驗！