"""
Gacha系統尋寶礦區遊戲 COG - 簡化版
處理尋寶礦區遊戲的Discord命令和交互
所有邏輯都在 COG 內部，便於熱重載
"""

import random
import uuid
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.economy_service as economy_service
from gacha.exceptions import (
    GameError,
    InsufficientBalanceError,
    InvalidBetAmountError,
)
from gacha.services import game_stats_service
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger

# 遊戲常數 - 直接硬編碼
BOARD_ROWS = 4
BOARD_COLS = 5
TOTAL_TILES = BOARD_ROWS * BOARD_COLS  # 20

TILE_HIDDEN = "❓"
TILE_MINE = "💥"
TILE_SAFE = "💎"
TILE_SPECIAL_COIN = "💰"
TILE_SPECIAL_STAR = "⭐"

MIN_BET = 100
MAX_BET = 250000

# 指定賠率相關
HOUSE_EDGE = 0.07  # 7%
PAYOUT_FACTOR = 1.0 - HOUSE_EDGE  # 0.93

# 難度模式映射
MODE_MAP = {3: "簡單 (3 雷)", 7: "中等 (7 雷)", 12: "困難 (12 雷)"}

# 遊戲圖片
GAME_IMAGE_URL = "https://cdn.dev.conquest.bot/thumbnails/explore_empty.png"


class MinesCustomBetModal(BaseModal):
    """尋寶礦區自訂下注金額模態框"""

    def __init__(self, cog, user_id: int, current_balance: int):
        super().__init__(bot=cog.bot, title="自訂金額")
        self.cog = cog
        self.user_id = user_id
        self.current_balance = current_balance

        self.bet_amount = discord.ui.TextInput(
            label=f"請輸入下注金額 ({MIN_BET:,}-{MAX_BET:,})",
            placeholder=f"請輸入{MIN_BET:,}-{MAX_BET:,}之間的整數 (您的餘額: {current_balance:,})",
            min_length=len(str(MIN_BET)),
            max_length=len(str(MAX_BET)),
            required=True,
        )
        self.add_item(self.bet_amount)

    async def on_submit(self, interaction: discord.Interaction):
        bet_val_str = self.bet_amount.value
        bet_amount = int(bet_val_str)

        if not (MIN_BET <= bet_amount <= MAX_BET):
            raise InvalidBetAmountError(
                f"下注金額必須在 {MIN_BET:,} 至 {MAX_BET:,} 之間。"
            )

        # 檢查餘額是否足夠（使用傳入的餘額）
        if self.current_balance < bet_amount:
            raise InsufficientBalanceError(
                required=bet_amount, current=self.current_balance
            )

        # 顯示模式選擇界面，使用自訂金額
        # 這裡會編輯原訊息顯示新的模式選擇界面
        # Errors from this point on should bubble up.
        await self.cog._show_mode_selection_with_custom_bet(interaction, bet_amount)


class MinesGame:
    """尋寶礦區遊戲實例"""

    def __init__(
        self, user_id: int, bet_amount: int, mine_count: int, game_instance_id: str
    ):
        self.user_id: int = user_id
        self.bet_amount: int = bet_amount
        self.mine_count: int = mine_count
        self.game_instance_id: str = game_instance_id
        self.message_id: Optional[int] = None
        self.board_display: List[List[str]] = []
        self.mine_positions: Set[Tuple[int, int]] = set()
        self.special_coin_pos: Optional[Tuple[int, int]] = None
        self.special_star_pos: Optional[Tuple[int, int]] = None
        self._generate_board()
        self.revealed_tiles: List[Tuple[int, int]] = []
        self.current_multiplier: float = 1.0
        self.found_safe_tiles_count: int = 0
        self.start_time: float = discord.utils.utcnow().timestamp()
        self.game_over: bool = False
        self.final_winnings: Optional[float] = None
        self.last_result: Optional[str] = None

    def _generate_board(self) -> None:
        if not 0 <= self.mine_count < TOTAL_TILES:
            raise ValueError("Invalid number of mines")
        self.board_display = [
            [TILE_HIDDEN for _ in range(BOARD_COLS)] for _ in range(BOARD_ROWS)
        ]
        all_positions = [(r, c) for r in range(BOARD_ROWS) for c in range(BOARD_COLS)]
        random.shuffle(all_positions)
        self.mine_positions = set(all_positions[: self.mine_count])
        safe_positions = [
            pos for pos in all_positions if pos not in self.mine_positions
        ]
        self.special_coin_pos = None
        self.special_star_pos = None
        if len(safe_positions) >= 1:
            idx = random.randrange(len(safe_positions))
            self.special_coin_pos = safe_positions.pop(idx)
        if len(safe_positions) >= 1:
            idx = random.randrange(len(safe_positions))
            self.special_star_pos = safe_positions.pop(idx)

    def get_state_summary(self) -> Dict[str, Any]:
        """獲取遊戲狀態摘要"""
        return {
            "user_id": self.user_id,
            "bet_amount": self.bet_amount,
            "mine_count": self.mine_count,
            "game_instance_id": self.game_instance_id,
            "board_display": self.board_display,
            "current_multiplier": self.current_multiplier,
            "found_safe_tiles_count": self.found_safe_tiles_count,
            "game_over": self.game_over,
            "final_winnings": self.final_winnings,
            "last_result": self.last_result,
        }


class MinesModeSelectView(BaseView):
    """尋寶礦區模式選擇視圖"""

    def __init__(self, user_id: int, current_bet: int, cog, timeout=180):
        super().__init__(bot=cog.bot, user_id=user_id, timeout=timeout)
        self.current_bet = current_bet
        self.cog = cog
        self.interaction_message: Optional[discord.InteractionMessage] = None

    @discord.ui.button(
        label="🟢 簡單 (3 雷)",
        style=discord.ButtonStyle.success,
        custom_id="mines_mode_easy_3",
        row=0,
    )
    async def easy_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """簡單模式按鈕回調"""
        await self._mode_callback(interaction, 3)

    @discord.ui.button(
        label="🟡 中等 (7 雷)",
        style=discord.ButtonStyle.primary,
        custom_id="mines_mode_medium_7",
        row=0,
    )
    async def medium_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """中等模式按鈕回調"""
        await self._mode_callback(interaction, 7)

    @discord.ui.button(
        label="🔴 困難 (12 雷)",
        style=discord.ButtonStyle.danger,
        custom_id="mines_mode_hard_12",
        row=0,
    )
    async def hard_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """困難模式按鈕回調"""
        await self._mode_callback(interaction, 12)

    @discord.ui.button(
        label="⚙️ 自訂金額",
        style=discord.ButtonStyle.secondary,
        custom_id="mines_custom_bet",
        row=1,
    )
    async def custom_bet_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """自訂金額按鈕回調"""
        await self._custom_bet_callback(interaction)

    async def _mode_callback(self, interaction: discord.Interaction, mine_count: int):
        """模式選擇回調"""
        # 餘額檢查已移至 _start_mines_game 中，此處直接調用
        await self.cog._start_mines_game(interaction, self.current_bet, mine_count)

    async def _custom_bet_callback(self, interaction: discord.Interaction):
        """自訂金額按鈕回調"""
        # 獲取當前餘額
        balance_data = await economy_service.get_balance(self.user_id)
        current_balance = balance_data.get("balance", 0)

        modal = MinesCustomBetModal(self.cog, self.user_id, current_balance)
        await interaction.response.send_modal(modal)


class TileButton(discord.ui.Button):
    """尋寶礦區遊戲格子的按鈕"""

    def __init__(self, row: int, col: int, game_instance_id: str, **kwargs):
        super().__init__(
            custom_id=f"mines_tile_{row}_{col}_{game_instance_id}", row=row, **kwargs
        )
        self.row = row
        self.col = col

    async def callback(self, interaction: discord.Interaction):
        """格子點擊的回調"""
        # self.view is the MinesGameView instance
        if (
            self.view
            and hasattr(self.view, "cog")
            and hasattr(self.view, "game_instance_id")
        ):
            await self.view.cog._handle_tile_click(
                interaction, self.row, self.col, self.view.game_instance_id, self.view
            )
        else:
            raise GameError("遊戲已結束或無效")


class MinesGameView(BaseView):
    """尋寶礦區遊戲進行中的 View (4x5 棋盤 + 提現按鈕)"""

    # The decorated buttons are added to the class.
    @discord.ui.button(
        label="💰 提現",
        style=discord.ButtonStyle.success,
        custom_id="mines_cash_out",
        row=4,
    )
    async def cash_out_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """提現按鈕回調"""
        await self._cash_out_callback(interaction)

    @discord.ui.button(
        label="再來一局 (相同設定)",
        style=discord.ButtonStyle.primary,
        custom_id="mines_play_again_same",
        row=4,
    )
    async def play_again_same_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """再來一局 (相同設定) 按鈕回調"""
        await self._play_again_same_callback(interaction)

    @discord.ui.button(
        label="調整設定並重玩",
        style=discord.ButtonStyle.secondary,
        custom_id="mines_play_again_new",
        row=4,
    )
    async def play_again_new_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """調整設定並重玩按鈕回調"""
        await self._play_again_new_callback(interaction)

    def __init__(
        self,
        game_state: Dict[str, Any],
        cog,
        player: Union[discord.User, discord.Member],
        timeout=600,
    ):
        super().__init__(bot=cog.bot, user_id=player.id, timeout=timeout)
        self.game_state = game_state
        self.cog = cog
        self.player = player
        self.game_instance_id = game_state.get("game_instance_id", "unknown")

        # 創建4x5棋盤按鈕
        board_display = game_state.get("board_display", [])
        for r in range(BOARD_ROWS):
            for c in range(BOARD_COLS):
                if r < len(board_display) and c < len(board_display[r]):
                    tile_display = board_display[r][c]
                else:
                    tile_display = TILE_HIDDEN

                # 根據格子類型設定按鈕樣式
                if tile_display == TILE_HIDDEN:
                    button_style = discord.ButtonStyle.secondary
                elif tile_display == TILE_SAFE:
                    button_style = discord.ButtonStyle.success  # 綠色 - 安全格
                elif tile_display == TILE_SPECIAL_COIN:
                    button_style = discord.ButtonStyle.primary  # 藍色 - 金幣格
                elif tile_display == TILE_SPECIAL_STAR:
                    button_style = discord.ButtonStyle.primary  # 藍色 - 星星格
                elif tile_display == TILE_MINE:
                    button_style = discord.ButtonStyle.danger  # 紅色 - 地雷
                else:
                    button_style = discord.ButtonStyle.secondary

                button = TileButton(
                    row=r,
                    col=c,
                    game_instance_id=self.game_instance_id,
                    emoji=tile_display,
                    style=button_style,
                    disabled=tile_display != TILE_HIDDEN
                    or game_state.get("game_over", False),
                )
                self.add_item(button)

        # 根據遊戲狀態配置按鈕
        game_over = self.game_state.get("game_over", False)
        if game_over:
            self.remove_item(self.cash_out_button)
        else:
            self.cash_out_button.disabled = (
                self.game_state.get("found_safe_tiles_count", 0) == 0
            )
            self.remove_item(self.play_again_same_button)
            self.remove_item(self.play_again_new_button)

    async def _cash_out_callback(self, interaction: discord.Interaction):
        """提現按鈕回調"""
        await self.cog._handle_cash_out(interaction, self.game_instance_id, self)

    async def _play_again_same_callback(self, interaction: discord.Interaction):
        """再來一局 (相同設定) 按鈕回調"""
        bet_amount = self.game_state.get("bet_amount")
        mine_count = self.game_state.get("mine_count")

        # Ensure bet_amount is valid
        if not isinstance(bet_amount, (int, float)) or bet_amount <= 0:
            raise InvalidBetAmountError("無效的下注金額")

        # 餘額檢查已移至 _start_mines_game_new_message，此處直接調用
        await self.cog._start_mines_game_new_message(
            interaction, bet_amount, mine_count
        )

    async def _play_again_new_callback(self, interaction: discord.Interaction):
        """調整設定並重玩按鈕回調"""
        # 直接顯示模式選擇界面，餘額檢查在選擇難度時進行
        await self.cog._show_mode_selection_new_message(
            interaction, self.game_state.get("bet_amount")
        )


class MinesCog(commands.Cog):
    """處理尋寶礦區遊戲命令的COG - 簡化版"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.active_games: Dict[int, Dict[str, MinesGame]] = {}

        # 使用Discord.py官方的CooldownMapping來處理按鈕冷卻（1.0秒，針對用戶）
        self.button_cooldown_mapping = commands.CooldownMapping.from_cooldown(
            1.0, 1.0, lambda interaction: interaction.user
        )

        logger.info("MinesCog initialized (簡化版).")

    @staticmethod
    def _create_mode_selection_embed(
        current_bet: int,
        current_balance: int,
        player: Union[discord.User, discord.Member],
    ) -> discord.Embed:
        """構建尋寶礦區模式選擇的 Embed"""
        embed = discord.Embed(
            title="⛏️ 尋寶礦區 - 模式選擇",
            description="歡迎來到尋寶礦區！選擇一個難度開始遊戲。\n\n**遊戲規則：**\n1. 點擊格子來揭開它。\n2. 每揭開一個 **💎安全格**，您的獎金乘數會增加。\n3. 如果揭開 **💥地雷**，遊戲結束，您將失去賭注。\n4. 您可以隨時點擊 **💰提現** 來領取當前乘數對應的獎金。\n5. 特殊格：\n   - **💰金幣格**：立即獲得少量額外油幣。\n   - **⭐星星格**：立即獲得較多額外油幣。\n\n**請選擇難度：**",
            color=discord.Color.gold(),
        )
        embed.set_author(
            name=f"{player.display_name}",
            icon_url=player.display_avatar.url if player.display_avatar else None,
        )
        embed.add_field(name="💰 當前賭注", value=f"{current_bet:,} 油", inline=True)
        embed.add_field(
            name="🏦 您的餘額", value=f"{current_balance:,} 油", inline=True
        )
        embed.add_field(name="\u200b", value="\u200b", inline=True)
        embed.set_thumbnail(url=GAME_IMAGE_URL)
        embed.set_footer(
            text="選擇難度開始遊戲",
            icon_url="https://cdn.discordapp.com/emojis/1067486117894574221.png",
        )
        return embed

    def _determine_embed_props(
        self, game_state: Dict[str, Any]
    ) -> Tuple[str, discord.Color]:
        game_over = game_state.get("game_over", False)
        last_result = game_state.get("last_result")
        if game_over:
            if last_result == "LOSS":
                return "💥 踩到地雷了！", discord.Color.red()
            if last_result in ("CASH_OUT", "WIN"):
                return "💰 成功提現！", discord.Color.green()
            return "⛏️ 尋寶礦區 - 遊戲結束", discord.Color.orange()
        return "⛏️ 尋寶礦區 - 進行中", discord.Color.blue()

    def _build_embed_description(
        self,
        game_state: Dict[str, Any],
        current_balance: Optional[int],
        custom_message: Optional[str],
    ) -> str:
        description = ""
        if game_state.get("game_over"):
            if custom_message:
                description = custom_message
            elif game_state.get("last_result") == "LOSS":
                description = f"您損失了 **{game_state.get('bet_amount', 0):,}** 油幣。"
            elif game_state.get("last_result") in ("CASH_OUT", "WIN"):
                winnings = game_state.get("final_winnings", 0)
                multiplier = game_state.get("current_multiplier", 1.0)
                description = f"您贏得了 **{winnings:,.0f}** 油幣！({multiplier:.3f}x)"

        if current_balance is not None:
            balance_msg = f"💰 **目前餘額**: {current_balance:,.0f} 油幣"
            description = (
                f"{description}\n{balance_msg}" if description else balance_msg
            )
        return description

    def _add_game_info_fields(self, embed: discord.Embed, game_state: Dict[str, Any]):
        mine_count = game_state.get("mine_count", 0)
        bet_amount = game_state.get("bet_amount", 0)
        multiplier = game_state.get("current_multiplier", 1.0)

        embed.add_field(
            name="模式", value=MODE_MAP.get(mine_count, f"{mine_count} 雷"), inline=True
        )
        embed.add_field(name="賭注", value=f"{bet_amount:,} 油幣", inline=True)
        embed.add_field(name="目前乘數", value=f"{multiplier:.3f}x", inline=True)

        if not game_state.get("game_over"):
            potential_winnings = bet_amount * multiplier
            found_tiles = game_state.get("found_safe_tiles_count", 0)
            total_safe = TOTAL_TILES - mine_count
            embed.add_field(
                name="潛在獎金", value=f"{potential_winnings:,.0f} 油幣", inline=True
            )
            embed.add_field(
                name="進度", value=f"{found_tiles} / {total_safe}", inline=True
            )
            embed.add_field(name="\u200b", value="\u200b", inline=True)

    def _create_mines_game_embed(
        self,
        game_state: Dict[str, Any],
        player: Union[discord.User, discord.Member],
        current_balance: Optional[int] = None,
        custom_message_on_end: Optional[str] = None,
    ) -> discord.Embed:
        title, color = self._determine_embed_props(game_state)
        description = self._build_embed_description(
            game_state, current_balance, custom_message_on_end
        )

        embed = discord.Embed(title=title, description=description, color=color)
        embed.set_author(
            name=f"{player.display_name} 的遊戲", icon_url=player.display_avatar.url
        )
        embed.set_thumbnail(url=GAME_IMAGE_URL)

        self._add_game_info_fields(embed, game_state)
        return embed

    def _calculate_multiplier(
        self, safe_tiles_found: int, total_safe_tiles: int, mine_count: int
    ) -> float:
        """計算當前乘數 - 修正版"""
        if safe_tiles_found == 0:
            return 1.0

        # 核心修正：計算累積成功概率 (cumulative success probability)
        # 這是連續 n 次都點中安全格的總概率
        cumulative_probability = 1.0
        for i in range(safe_tiles_found):
            # 第 i+1 次點擊時，剩餘的總格子數
            current_remaining_total = TOTAL_TILES - i
            # 第 i+1 次點擊時，剩餘的安全格子數
            current_remaining_safe = total_safe_tiles - i

            # 如果剩餘格子數少於或等於地雷數，概率計算無意義，但為了避免除以零，提前返回
            if current_remaining_total <= mine_count:
                # 這種情況理論上不應該產生更高的倍率，返回一個合理的上限或當前值
                # 為了簡化，我們直接使用上一步的計算結果。這裡的邏輯可以進一步優化，但目前已足夠。
                break

            # 當前這一步成功的概率
            step_probability = current_remaining_safe / current_remaining_total
            # 將每一步的成功概率相乘，得到累積成功概率
            cumulative_probability *= step_probability

        # 如果累積概率為0，意味著不可能發生，返回1.0
        if cumulative_probability == 0:
            return 1.0

        # 公平倍率 = 1 / 累積成功概率
        fair_multiplier = 1.0 / cumulative_probability

        # 最終倍率 = 公平倍率 * 莊家抽水因子 (只乘一次！)
        final_multiplier = fair_multiplier * PAYOUT_FACTOR

        return max(1.0, final_multiplier)

    def has_active_game(self, user_id: int) -> bool:
        """檢查用戶是否有進行中的遊戲"""
        user_games_dict = self.active_games.get(user_id)
        if not user_games_dict:
            return False
        for game in user_games_dict.values():
            if not game.game_over:
                return True
        return False

    async def _create_game(
        self, user_id: int, bet_amount: int, mine_count: int
    ) -> MinesGame:
        """創建新遊戲"""
        # 扣除下注金額（餘額已在前面檢查過）
        await economy_service.award_oil(
            user_id=user_id,
            amount=-bet_amount,
            transaction_type="game:mines_bet",
            reason="Mines bet",
        )

        # 創建遊戲實例
        game_instance_id = uuid.uuid4().hex
        new_game_instance = MinesGame(
            user_id=user_id,
            bet_amount=bet_amount,
            mine_count=mine_count,
            game_instance_id=game_instance_id,
        )

        if user_id not in self.active_games:
            self.active_games[user_id] = {}
        self.active_games[user_id][game_instance_id] = new_game_instance

        return new_game_instance

    async def _start_mines_game(
        self,
        interaction: discord.Interaction,
        bet_amount: int,
        mine_count: int,
        edit_original: bool = True,
    ):
        """開始尋寶礦區遊戲

        Args:
            interaction: Discord 互動
            bet_amount: 下注金額
            mine_count: 地雷數量
            edit_original: 是否編輯原訊息（True）或發送新訊息（False）
        """
        # 統一先defer
        await interaction.response.defer()

        user_id = interaction.user.id

        # 創建遊戲（餘額已在前面檢查過）
        new_game_instance = await self._create_game(user_id, bet_amount, mine_count)
        game_state = new_game_instance.get_state_summary()

        # 獲取當前餘額
        balance_data = await economy_service.get_balance(user_id)
        current_balance = balance_data.get("balance", 0)

        # 創建遊戲embed和view
        game_embed = self._create_mines_game_embed(
            game_state, interaction.user, current_balance=current_balance
        )
        game_view = MinesGameView(
            game_state=game_state, cog=self, player=interaction.user
        )

        message = f"尋寶礦區遊戲已開始！您的賭注是 {bet_amount:,.0f} 油幣，共有 {mine_count} 個地雷。祝您好運！"

        if edit_original:
            # 編輯原訊息（從模式選擇進入遊戲）
            await interaction.edit_original_response(
                content=message, embed=game_embed, view=game_view
            )
        else:
            # 發送新訊息（再來一局功能）
            await interaction.followup.send(
                content=message, embed=game_embed, view=game_view
            )

    async def _handle_mine_hit(self, game: MinesGame, row: int, col: int) -> str:
        game.game_over = True
        game.last_result = "LOSS"
        for r, c in game.mine_positions:
            game.board_display[r][c] = TILE_MINE
        await self._record_game_loss_stats(game, game.user_id)
        return "哎呀！您踩到了地雷 💥！遊戲結束，本次未能獲得任何油幣。"

    async def _handle_safe_tile(self, game: MinesGame, row: int, col: int) -> str:
        game.found_safe_tiles_count += 1
        message = "安全！您找到了一個安全格 💎。"
        immediate_reward = 0
        if (row, col) == game.special_coin_pos:
            game.board_display[row][col] = TILE_SPECIAL_COIN
            immediate_reward = max(10, game.bet_amount // 20)
            message = (
                f"太棒了！您找到了金幣格 💰！立即獲得 {immediate_reward} 油幣獎勵。"
            )
        elif (row, col) == game.special_star_pos:
            game.board_display[row][col] = TILE_SPECIAL_STAR
            immediate_reward = max(25, game.bet_amount // 10)
            message = f"驚喜！您找到了星星格 ⭐！立即獲得 {immediate_reward} 油幣獎勵。"
        else:
            game.board_display[row][col] = TILE_SAFE

        if immediate_reward > 0:
            await economy_service.award_oil(
                user_id=game.user_id,
                amount=immediate_reward,
                transaction_type="game:mines_special_reward",
                reason="Mines special tile reward",
            )

        total_safe = TOTAL_TILES - game.mine_count
        game.current_multiplier = self._calculate_multiplier(
            game.found_safe_tiles_count, total_safe, game.mine_count
        )
        return f"{message} 目前乘數: {game.current_multiplier:.2f}x。"

    async def _update_game_view(
        self, interaction: discord.Interaction, game: MinesGame, message: str
    ):
        balance = (await economy_service.get_balance(game.user_id)).get("balance", 0)
        game_state = game.get_state_summary()
        embed = self._create_mines_game_embed(
            game_state, interaction.user, balance, message
        )
        view = MinesGameView(game_state, self, interaction.user)
        await interaction.edit_original_response(content=None, embed=embed, view=view)

    async def _handle_tile_click(
        self,
        interaction: discord.Interaction,
        row: int,
        col: int,
        game_instance_id: str,
        view=None,
    ):
        await interaction.response.defer()
        user_id = interaction.user.id
        game = self.active_games.get(user_id, {}).get(game_instance_id)
        if not game or game.game_over or (row, col) in game.revealed_tiles:
            raise GameError("遊戲不存在、已結束或格子已被揭開。")

        game.revealed_tiles.append((row, col))
        message = ""
        if (row, col) in game.mine_positions:
            message = await self._handle_mine_hit(game, row, col)
        else:
            message = await self._handle_safe_tile(game, row, col)

        await self._update_game_view(interaction, game, message)

    async def _process_cash_out(
        self, game: MinesGame, user_id: int, auto_cash_out: bool = False
    ):
        """處理提現邏輯"""
        if game.game_over:
            return

        game.game_over = True
        game.last_result = "CASH_OUT"

        # 計算最終獎金
        final_winnings = game.bet_amount * game.current_multiplier
        game.final_winnings = final_winnings

        # 發放獎金
        await economy_service.award_oil(
            user_id=user_id,
            amount=int(final_winnings),
            transaction_type="game:mines_win",
            reason="Mines cash out",
        )

        # 更新遊戲統計
        try:
            profit_loss = final_winnings - game.bet_amount

            # 檢查是否找到特殊格子
            special_coin_found = 0
            special_star_found = 0
            if game.special_coin_pos and game.special_coin_pos in game.revealed_tiles:
                special_coin_found = 1
            if game.special_star_pos and game.special_star_pos in game.revealed_tiles:
                special_star_found = 1

            game_data = {
                "bet": game.bet_amount,
                "payout": int(final_winnings),
                "profit": profit_loss,
                "result": "win",
                "mine_count": game.mine_count,
                "multiplier": game.current_multiplier,
                "early_cashout": not auto_cash_out,  # 手動提現=True, 自動提現=False
                "tiles_revealed": len(game.revealed_tiles),
                "special_coin_found": bool(special_coin_found),
                "special_star_found": bool(special_star_found),
            }

            await game_stats_service.record_game_result(user_id, "mines", game_data)
        except Exception as e:
            logger.error("更新遊戲統計失敗: %s", e)

    async def _record_game_loss_stats(self, game: MinesGame, user_id: int):
        """記錄遊戲失敗統計"""
        try:
            # 檢查是否找到特殊格子
            special_coin_found = 0
            special_star_found = 0
            if game.special_coin_pos and game.special_coin_pos in game.revealed_tiles:
                special_coin_found = 1
            if game.special_star_pos and game.special_star_pos in game.revealed_tiles:
                special_star_found = 1

            game_data = {
                "bet": game.bet_amount,
                "payout": 0,
                "profit": -game.bet_amount,
                "result": "lose",
                "mine_count": game.mine_count,
                "multiplier": game.current_multiplier,
                "early_cashout": False,
                "tiles_revealed": len(game.revealed_tiles),
                "special_coin_found": bool(special_coin_found),
                "special_star_found": bool(special_star_found),
            }

            await game_stats_service.record_game_result(user_id, "mines", game_data)
        except Exception as e:
            logger.error("記錄失敗統計失敗: %s", e)

    async def _handle_cash_out(
        self, interaction: discord.Interaction, game_instance_id: str, view=None
    ):
        """處理提現按鈕"""
        await interaction.response.defer()

        user_id = interaction.user.id

        # 獲取遊戲實例
        if (
            user_id not in self.active_games
            or game_instance_id not in self.active_games[user_id]
        ):
            raise GameError("遊戲不存在或已結束。")

        game = self.active_games[user_id][game_instance_id]

        if game.game_over:
            raise GameError("遊戲已結束。")

        if game.found_safe_tiles_count == 0:
            raise GameError("您還沒有找到任何安全格，無法提現。")

        # 處理提現（手動提現，early_cashout = True）
        await self._process_cash_out(game, user_id, auto_cash_out=False)

        # 獲取當前餘額
        balance_data = await economy_service.get_balance(user_id)
        current_balance = balance_data.get("balance", 0)

        # 更新embed和view (保持棋盤顯示，添加重玩按鈕)
        game_state = game.get_state_summary()
        message = f"成功提現！您獲得了 {game.final_winnings:,.0f} 油幣！({game.current_multiplier:.3f}x)"
        embed = self._create_mines_game_embed(
            game_state,
            interaction.user,
            current_balance=current_balance,
            custom_message_on_end=message,
        )

        # 創建新的view，顯示重玩按鈕
        new_view = MinesGameView(
            game_state=game_state, cog=self, player=interaction.user
        )
        await interaction.edit_original_response(
            content=None, embed=embed, view=new_view
        )

    async def _show_mode_selection(
        self, interaction: discord.Interaction, current_bet: int
    ):
        """顯示模式選擇界面"""
        await interaction.response.defer()

        user_id = interaction.user.id

        # 獲取餘額
        balance_data = await economy_service.get_balance(user_id)
        current_balance = balance_data.get("balance", 0)

        # 創建模式選擇embed和view
        embed = self._create_mode_selection_embed(
            current_bet=current_bet,
            current_balance=current_balance,
            player=interaction.user,
        )
        view = MinesModeSelectView(user_id=user_id, current_bet=current_bet, cog=self)

        await interaction.edit_original_response(embed=embed, view=view)

    async def _show_mode_selection_with_custom_bet(
        self, interaction: discord.Interaction, custom_bet: int
    ):
        """顯示模式選擇界面（使用自訂金額）"""
        # 這個方法是從自訂金額模態框調用的，統一先defer
        await interaction.response.defer()

        user_id = interaction.user.id

        # 獲取餘額
        balance_data = await economy_service.get_balance(user_id)
        current_balance = balance_data.get("balance", 0)

        # 創建模式選擇embed和view
        embed = self._create_mode_selection_embed(
            current_bet=custom_bet,
            current_balance=current_balance,
            player=interaction.user,
        )
        view = MinesModeSelectView(user_id=user_id, current_bet=custom_bet, cog=self)

        # 編輯原訊息顯示新的模式選擇界面
        await interaction.edit_original_response(embed=embed, view=view)

    async def _start_mines_game_new_message(
        self, interaction: discord.Interaction, bet_amount: int, mine_count: int
    ):
        """開始尋寶礦區遊戲（發送新訊息）"""
        # 使用統一的 _start_mines_game 方法，但指定發送新訊息
        await self._start_mines_game(
            interaction, bet_amount, mine_count, edit_original=False
        )

    async def _show_mode_selection_new_message(
        self, interaction: discord.Interaction, current_bet: int
    ):
        """顯示模式選擇界面（發送新訊息）"""
        await interaction.response.defer()

        user_id = interaction.user.id

        # 獲取餘額
        balance_data = await economy_service.get_balance(user_id)
        current_balance = balance_data.get("balance", 0)

        # 創建模式選擇embed和view
        embed = self._create_mode_selection_embed(
            current_bet=current_bet,
            current_balance=current_balance,
            player=interaction.user,
        )
        view = MinesModeSelectView(user_id=user_id, current_bet=current_bet, cog=self)

        await interaction.followup.send(embed=embed, view=view)

    @app_commands.command(name="mines", description="開始一局尋寶礦區遊戲或查看說明")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        mine_count="選擇地雷數量 (難度，可選)",
        bet_amount=f"您想下注的油幣金額 (可選，預設 {MIN_BET}，範圍 {MIN_BET}-{MAX_BET})",
    )
    @app_commands.choices(
        mine_count=[
            app_commands.Choice(name="🟢 簡單 (3 雷)", value=3),
            app_commands.Choice(name="🟡 中等 (7 雷)", value=7),
            app_commands.Choice(name="🔴 困難 (12 雷)", value=12),
        ]
    )
    async def mines_command(
        self,
        interaction: discord.Interaction,
        mine_count: Optional[app_commands.Choice[int]] = None,
        bet_amount: app_commands.Range[int, MIN_BET, MAX_BET] = MIN_BET,
    ):
        """處理 /mines 指令"""
        user_id = interaction.user.id
        final_bet = bet_amount

        if mine_count is not None:
            selected_mine_count = mine_count.value
            # 檢查餘額是否足夠
            balance_data = await economy_service.get_balance(user_id)
            current_balance = balance_data.get("balance", 0)

            if current_balance < final_bet:
                raise InsufficientBalanceError(
                    required=final_bet, current=current_balance
                )

            # 餘額足夠，開始遊戲
            await self._start_mines_game(
                interaction, final_bet, selected_mine_count, edit_original=True
            )
        else:
            # 顯示模式選擇界面，使用原訊息回應
            # 使用 defer + followup 模式避免 View 註冊競爭條件
            await interaction.response.defer()

            # 獲取餘額
            balance_data = await economy_service.get_balance(user_id)
            current_balance = balance_data.get("balance", 0)

            embed = self._create_mode_selection_embed(
                current_bet=final_bet,
                current_balance=current_balance,
                player=interaction.user,
            )
            view = MinesModeSelectView(user_id=user_id, current_bet=final_bet, cog=self)
            # 使用 followup 顯示模式選擇界面
            await interaction.followup.send(embed=embed, view=view)


async def setup(bot: commands.Bot):
    """將COG添加到Bot中 - 簡化版"""
    await bot.add_cog(MinesCog(bot))
    logger.info("MinesCog 已成功加載並註冊（簡化版）。")
