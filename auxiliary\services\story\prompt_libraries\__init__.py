"""
多套提示詞卡片庫管理模組

支援按檔案區分的多套提示詞卡片庫，每個故事模板可以選擇使用不同的卡片庫。
"""

import importlib
import logging
import os
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

# 存儲所有已載入的卡片庫
_prompt_libraries: Dict[str, List[Dict[str, Any]]] = {}

def get_available_libraries() -> List[str]:
    """取得所有可用的提示詞卡片庫名稱列表"""
    import re
    
    library_dir = os.path.dirname(__file__)
    libraries = []
    
    try:
        for file in os.listdir(library_dir):
            # 验证文件名格式和安全性
            if (file.endswith('_library.py') and 
                file != '__init__.py' and
                re.match(r'^[a-zA-Z0-9_]+_library\.py$', file)):
                library_name = file[:-11]  # 移除 '_library.py' 后缀
                # 进一步验证库名称，防止路径遍历攻击
                if re.match(r'^[a-zA-Z0-9_]+$', library_name) and len(library_name) <= 50:
                    libraries.append(library_name)
    except OSError as e:
        logger.error("無法列出卡片庫目錄: %s", str(e))
        return []
    
    return sorted(libraries)

def load_prompt_library(library_name: str) -> List[Dict[str, Any]]:
    """
    載入指定的提示詞卡片庫
    
    Args:
        library_name: 卡片庫名稱，如 'default', 'beilu', 'custom'
        
    Returns:
        提示詞卡片列表
    """
    import re
    
    # 驗證庫名稱安全性
    if not library_name or not isinstance(library_name, str):
        logger.error("卡片庫名稱格式錯誤 - 空值或非字串類型")
        raise ValueError("卡片庫名稱必須為非空字串")
    
    if not re.match(r'^[a-zA-Z0-9_]+$', library_name) or len(library_name) > 50:
        logger.error("卡片庫名稱格式不符合規範: %s", library_name)
        raise ValueError(f"卡片庫名稱格式無效: {library_name}")
    
    if library_name in _prompt_libraries:
        return _prompt_libraries[library_name]
    
    try:
        # 尝试相对导入
        try:
            module_name = f".{library_name}_library"
            module = importlib.import_module(module_name, package=__name__)
        except ImportError:
            # 回退到绝对导入
            module_name = f"auxiliary.services.story.prompt_libraries.{library_name}_library"
            module = importlib.import_module(module_name)
        
        if hasattr(module, 'PROMPT_LIBRARY'):
            _prompt_libraries[library_name] = module.PROMPT_LIBRARY
            logger.info("成功載入卡片庫: %s (包含 %d 張卡片)", library_name, len(module.PROMPT_LIBRARY))
            return module.PROMPT_LIBRARY
        else:
            logger.error("模組 %s 缺少 PROMPT_LIBRARY 屬性", module_name)
            raise ValueError(f"模組 {module_name} 缺少 PROMPT_LIBRARY 屬性")
            
    except ImportError as e:
        logger.error("無法載入提示詞卡片庫 '%s': %s", library_name, str(e))
        raise ValueError(f"無法載入提示詞卡片庫 '{library_name}': {e}") from e

def get_prompt_library(library_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    获取提示词卡片库，支持回退机制
    
    Args:
        library_name: 卡片库名称，如果为None则使用默认库
        
    Returns:
        提示词卡片列表
    """
    # 如果没有指定库名，使用默认库
    if not library_name:
        library_name = 'default'
    
    try:
        return load_prompt_library(library_name)
    except ValueError:
        # 如果指定的庫載入失敗，回退到預設庫
        if library_name != 'default':
            logger.warning("載入卡片庫 '%s' 失敗，回退至預設庫", library_name)
            return load_prompt_library('default')
        else:
            logger.critical("無法載入預設提示詞卡片庫")
            raise ValueError("無法載入預設提示詞卡片庫") from None

def list_libraries_info() -> Dict[str, Dict[str, Any]]:
    """获取所有卡片库的信息"""
    info = {}
    
    for library_name in get_available_libraries():
        try:
            library = load_prompt_library(library_name)
            info[library_name] = {
                'name': library_name,
                'count': len(library),
                'description': f"提示词卡片库: {library_name}",
                'available': True
            }
        except Exception as e:
            info[library_name] = {
                'name': library_name,
                'count': 0,
                'description': f"加载失败: {str(e)}",
                'available': False
            }
    
    return info