import json
from pathlib import Path
from typing import Any, Set


def extract_status_effect_ids_from_value(value: Any, extracted_ids: Set[str]) -> None:
    """
    Recursively extracts status_effect_id from a given value.
    Handles dictionaries, lists, and direct string values.
    """
    if isinstance(value, dict):
        for key, sub_value in value.items():
            if key == "status_effect_id" and isinstance(sub_value, str):
                extracted_ids.add(sub_value)
            else:
                extract_status_effect_ids_from_value(sub_value, extracted_ids)
    elif isinstance(value, list):
        for item in value:
            extract_status_effect_ids_from_value(item, extracted_ids)


def get_referenced_status_effect_ids(effect_templates_path: Path) -> Set[str]:
    """
    Reads the effect_templates directory and extracts all referenced
    status_effect_ids from all JSON files.
    """
    referenced_ids: Set[str] = set()

    if not effect_templates_path.exists():
        print(f"Error: Effect templates directory not found at {effect_templates_path}")
        return referenced_ids

    if not effect_templates_path.is_dir():
        print(f"Error: {effect_templates_path} is not a directory")
        return referenced_ids

    # 動態讀取目錄下所有 JSON 文件
    json_files = list(effect_templates_path.glob("*.json"))

    if not json_files:
        print(
            f"Error: No JSON files found in effect templates directory: "
            f"{effect_templates_path}"
        )
        return referenced_ids

    for template_file_path in json_files:
        try:
            with open(template_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # The top level is a dictionary of effect templates
            for _template_id, template_definition in data.items():
                extract_status_effect_ids_from_value(
                    template_definition, referenced_ids
                )

            print(f"Processed effect templates from: {template_file_path.name}")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {template_file_path}: {e}")
        except Exception as e:
            print(f"Error reading file {template_file_path}: {e}")

    print(f"Total effect templates processed from {len(json_files)} files")
    return referenced_ids


def get_defined_status_effect_ids(status_effects_path: Path) -> Set[str]:
    """
    Reads the status_effects directory and extracts all defined status effect
    IDs from all JSON files.
    """
    defined_ids: Set[str] = set()

    if not status_effects_path.exists():
        print(f"Error: Status effects directory not found at {status_effects_path}")
        return defined_ids

    if not status_effects_path.is_dir():
        print(f"Error: {status_effects_path} is not a directory")
        return defined_ids

    # 動態讀取目錄下所有 JSON 文件
    json_files = list(status_effects_path.glob("*.json"))

    if not json_files:
        print(
            f"Error: No JSON files found in status effects directory: "
            f"{status_effects_path}"
        )
        return defined_ids

    for status_file_path in json_files:
        try:
            with open(status_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            defined_ids.update(data.keys())
            print(f"Loaded status effects from: {status_file_path.name}")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {status_file_path}: {e}")
        except Exception as e:
            print(f"Error reading file {status_file_path}: {e}")

    print(
        f"Total status effects loaded: {len(defined_ids)} from {len(json_files)} files"
    )
    return defined_ids


def main():
    """
    Main function to perform the validation.
    """
    base_path = (
        Path(__file__).resolve().parent.parent.parent / "rpg_system" / "config" / "data"
    )
    effect_templates_path = base_path / "effect_templates"
    status_effects_path = base_path / "status_effects"

    print("Starting validation...")
    print(f"Effect templates directory: {effect_templates_path}")
    print(f"Status effects directory: {status_effects_path}")

    referenced_ids = get_referenced_status_effect_ids(effect_templates_path)
    if not referenced_ids:  # Abort if no references found
        return

    defined_ids = get_defined_status_effect_ids(status_effects_path)
    if not defined_ids:  # Abort if no status effects found
        return

    print(
        f"\nFound {len(referenced_ids)} unique status_effect_ids referenced in "
        f"{effect_templates_path.name}."
    )
    # print(f"Referenced IDs: {referenced_ids}") # Optional: for debugging
    print(
        f"Found {len(defined_ids)} unique status_effect_ids defined in "
        f"{status_effects_path.name}."
    )
    # print(f"Defined IDs: {defined_ids}") # Optional: for debugging

    missing_ids = referenced_ids - defined_ids

    if not missing_ids:
        print("\nValidation successful: All referenced status_effect_ids are defined.")
    else:
        print(
            f"\nValidation failed: Found {len(missing_ids)} status_effect_id(s) "
            f"referenced in {effect_templates_path.name} but not defined in "
            f"{status_effects_path.name}:"
        )
        for missing_id in sorted(missing_ids):
            print(f"  - {missing_id}")


if __name__ == "__main__":
    main()
