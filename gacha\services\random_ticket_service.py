from typing import Any, Dict, List

# database
from database.postgresql.async_manager import get_pool

# gacha models
from gacha.constants import RarityLevel

# gacha exceptions
from gacha.exceptions import (
    InvalidRarityError,
    NoCardsDeterminedError,
    TransactionError,
)
from gacha.models.models import Card
from gacha.models.shop_models import ShopItemDefinition

# gacha repositories
from gacha.repositories.card import master_card_repository

# gacha services
from gacha.services import base_ticket_service, draw_engine_service

# utils
from utils.logger import logger


async def process_random_ticket_exchange(
    user_id: int, ticket_definition: ShopItemDefinition, quantity: int, session_id: str
) -> Dict[str, Any]:
    """處理隨機票券兌換流程 - 優化版"""

    # 驗證並獲取票券參數
    pool_type, rarity_enum = _validate_ticket_definition(ticket_definition, session_id)

    # 抽取卡片
    cards_to_grant = await _select_random_ticket_cards(
        rarity_enum, pool_type, quantity, session_id
    )

    if not cards_to_grant:
        logger.warning(
            "Session %s: No cards drawn for %s from %s",
            session_id,
            rarity_enum,
            pool_type,
        )
        raise NoCardsDeterminedError(
            f"成功兌換 {ticket_definition.display_name}，但本次未抽中任何卡片。"
        )

    # 執行交易
    async with get_pool().acquire() as conn:
        transaction_result = await base_ticket_service.execute_exchange_transaction(
            conn,
            user_id,
            session_id,
            ticket_definition,
            quantity,
            cards_to_grant,
        )

    return {
        "drawn_cards": cards_to_grant,
        "granted_cards_info_for_embed": transaction_result.get(
            "granted_cards_info_for_embed", []
        ),
    }


def _validate_ticket_definition(
    ticket_definition: ShopItemDefinition, session_id: str
) -> tuple[str, RarityLevel]:
    """驗證票券定義並返回參數"""
    if not ticket_definition.pool_type:
        logger.error(
            "Missing 'pool_type' in ticket %s (session %s)",
            ticket_definition.id,
            session_id,
        )
        raise TransactionError("兌換券定義不完整，缺少 'pool_type'。")

    if ticket_definition.rarity is None:
        logger.error(
            "Missing 'rarity' in ticket %s (session %s)",
            ticket_definition.id,
            session_id,
        )
        raise TransactionError("兌換券定義不完整，缺少 'rarity'。")

    try:
        rarity_enum = RarityLevel(ticket_definition.rarity)
    except ValueError:
        logger.error(
            "Invalid rarity %s in ticket %s (session %s)",
            ticket_definition.rarity,
            ticket_definition.id,
            session_id,
        )
        raise InvalidRarityError(rarity=ticket_definition.rarity) from None

    return ticket_definition.pool_type, rarity_enum


async def _select_random_ticket_cards(
    rarity: RarityLevel, pool_type: str, quantity: int, session_id: str
) -> List[Card]:
    """使用優化的抽卡引擎進行隨機券抽取 - 支援重複抽取"""
    # 使用優化的批量獲取方法
    pool_types = [pool_type]  # 只需要當前的pool_type
    prefetched_card_pools = await draw_engine_service._get_all_card_pools(pool_types)

    # 檢查是否有可用卡片
    all_card_ids = prefetched_card_pools.get(pool_type, {}).get(rarity, [])

    # 【關鍵修復】對從快取獲取的固定順序列表進行隨機洗牌，確保隨機性。
    # 這修復了與主抽卡系統同樣的漏洞。
    if all_card_ids:
        draw_engine_service.random.shuffle(all_card_ids)

    if not all_card_ids:
        logger.error(
            "Session %s: No cards available. Rarity: %s, Pool: %s",
            session_id,
            rarity,
            pool_type,
        )
        raise NoCardsDeterminedError(f"卡池 {pool_type} 中沒有稀有度 {rarity} 的卡片")

    # 進行多次獨立抽取
    cards = []
    for i in range(quantity):
        # 隨機選擇一張卡片
        selected_card_id = draw_engine_service._randomly_select_card(all_card_ids)

        if selected_card_id:
            # 獲取卡片詳細信息
            card = await master_card_repository.get_card(selected_card_id)
            if card:
                cards.append(card)
            logger.debug(
                "Session %s: Draw %d/%d - Selected card ID: %s",
                session_id,
                i + 1,
                quantity,
                selected_card_id,
            )

    card_ids = [c.card_id for c in cards]
    logger.info(
        "Session %s: Drew %s cards (with possible duplicates). IDs: %s",
        session_id,
        len(cards),
        card_ids,
    )

    return cards
