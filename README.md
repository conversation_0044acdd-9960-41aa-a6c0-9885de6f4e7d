# DICKPK - Discord 多功能遊戲機器人

一個功能豐富的 Discord 機器人專案，提供抽卡遊戲、RPG 戰鬥系統、經濟模擬和 AI 助手等多種互動功能。

## 🎮 主要功能

### 🎲 Gacha 抽卡系統
- **抽卡機制**: 使用油幣抽取不同稀有度的卡片（T1-T6, TS）
- **收藏管理**: 查看已收集的卡片，了解系列收集進度
- **經濟系統**: 通過每日簽到、遊戲和賣卡獲取油幣
- **21點遊戲**: 通過遊戲贏取額外油幣
- **股票系統**: 卡片價格動態變化，支援買賣交易

### ⚔️ RPG 戰鬥系統
- **戰鬥機制**: 基於 Gacha 卡片的完整 RPG 戰鬥系統
- **技能系統**: 主動技能和被動技能裝備
- **屬性計算**: 支援基礎屬性、RPG 等級成長、星級效果
- **公式引擎**: 使用 asteval 進行安全的公式求值

### 🏗️ Pioneer 開拓者系統
- **數據驅動設計**: 所有遊戲邏輯由配置文件驅動
- **統一設施系統**: 商店、工廠、研究室都是設施的不同配置
- **無限升級研究**: 支援複雜的成本計算公式
- **經濟平衡機制**: 負期望值設計確保遊戲平衡

### 🤖 AI 助手系統
- **多 API 支援**: 支援多個 AI API 的自動切換
- **服裝評分**: AI 驅動的服裝評分功能
- **問答系統**: 智能問答和圖片分析
- **故事生成**: AI 生成的互動故事內容

### 🛠️ 輔助功能
- **健康檢查**: 系統狀態監控
- **圖像處理**: 自動圖像處理和 GIF 生成
- **問題回報**: 用戶反饋收集系統

## 🏗️ 技術架構

### 核心技術棧
- **Python 3.11+**: 主要開發語言
- **Discord.py 2.5+**: Discord 機器人框架
- **PostgreSQL**: 主要數據庫
- **Redis**: 緩存和會話管理
- **Playwright**: 網頁自動化和圖像生成
- **Docker**: 容器化部署

### 架構設計
```
DICKPK/
├── bot.py                    # 機器人主程式
├── command_registry.py       # 命令註冊管理器
├── gacha/                    # 抽卡系統
├── rpg_system/              # RPG 戰鬥系統
├── pioneer/                 # 開拓者經濟系統
├── auxiliary/               # 輔助功能（包含 AI 助手模組）

├── database/                # 數據庫管理
├── utils/                   # 工具函數
└── scripts/                 # 管理腳本
```

## 🚀 快速開始

### 環境需求
- Python 3.11 或更高版本
- PostgreSQL 12+
- Redis 5+
- Node.js（用於某些圖像處理功能）

### 安裝步驟

1. **克隆專案**
```bash
git clone <repository-url>
cd DICKPK
```

2. **安裝依賴**
```bash
pip install -r requirements.txt
```

3. **配置環境變數**
```bash
# 複製並編輯環境變數文件
cp .env.example .env
# 編輯 .env 文件，設置必要的配置
```

4. **初始化數據庫**
```bash
# 確保 PostgreSQL 服務運行
# 數據庫表會在首次運行時自動創建
```

5. **啟動機器人**
```bash
python bot.py
```

### Docker 部署

**開發環境**
```bash
docker-compose -f docker-compose.test.yml up -d
```

**生產環境**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## ⚙️ 配置說明

### 主要配置文件
- `config/config.yaml`: 主要系統配置
- `config/gacha_settings.yaml`: Gacha 系統專用配置
- `config/ui_settings.yaml`: UI 界面配置
- `.env`: 環境變數和敏感信息

### 重要環境變數
```bash
DISCORD_TOKEN=your_discord_bot_token
PG_HOST=localhost
PG_PORT=5432
PG_USER=postgres
PG_PASSWORD=your_password
GACHA_DB_NAME=gacha_database
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 📋 主要指令

### Gacha 系統
- `/w` - 抽卡（消耗30油幣）
- `/ws` - 抽取典藏卡片（消耗120油幣）
- `/mw` - 查看卡冊
- `/sw <card_id>` - 賣出卡片
- `/balance` - 查看油幣餘額
- `/blackjack` - 21點遊戲

### 系統管理
- `/sync` - 同步 Discord 指令
- `/health` - 系統健康檢查
- `/report` - 問題回報

## 🔧 開發指南

### 代碼風格
- 使用 Black 進行代碼格式化
- 遵循 PEP 8 編碼規範
- 使用 Type Hints

### 測試
```bash
# 運行 RPG 系統測試
cd rpg_system
python run_all_rpg_tests.py
```

### 添加新功能
1. 在對應的模組目錄下創建新功能
2. 在 `command_registry.py` 中註冊新命令
3. 更新相關配置文件
4. 添加必要的測試

## 📊 系統監控

### 日誌系統
- 日誌文件位於 `logs/` 目錄
- 支援不同級別的日誌記錄
- 自動日誌輪轉

### 性能監控
- Redis 緩存使用情況
- 數據庫連接池狀態
- API 響應時間監控

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支
3. 提交變更
4. 發起 Pull Request

## 📄 許可證

本專案僅供內部使用，未經許可不得分發或用於商業用途。

## 🆘 支援

如遇問題，請使用機器人的 `/report` 指令回報，或聯繫開發團隊。
