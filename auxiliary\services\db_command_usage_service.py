"""
數據庫指令使用統計服務
替代原有的文件系統統計，使用PostgreSQL數據庫存儲指令使用數據
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import asyncpg

from utils.logger import logger


@dataclass
class CommandUsageRecord:
    """指令使用記錄數據類"""

    command_name: str
    user_id: int
    guild_id: Optional[int] = None
    channel_id: Optional[int] = None
    command_type: str = "slash"  # 只有 App Commands，統一為 'slash'
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class CommandStats:
    """指令統計數據類"""

    command_name: str
    total_uses: int
    unique_users: int
    unique_guilds: int
    first_used: Optional[datetime]
    last_used: Optional[datetime]
    success_rate: float


class DatabaseCommandUsageService:
    """數據庫指令使用統計服務（批量插入版本）"""

    def __init__(
        self, pool: asyncpg.Pool, batch_size: int = 50, flush_interval: float = 5.0
    ):
        """
        初始化服務

        Args:
            pool: asyncpg 連接池
            batch_size: 批量插入大小（默認50條）
            flush_interval: 強制刷新間隔（秒，默認5秒）
        """
        self.pool = pool
        self.batch_size = batch_size
        self.flush_interval = flush_interval

        # 批量記錄緩存
        self._batch_records = deque()
        self._batch_lock = asyncio.Lock()
        self._last_flush_time = time.time()

        # 啟動後台刷新任務
        self._flush_task = None
        self._shutdown = False

    async def start_background_flush(self):
        """啟動後台刷新任務"""
        if self._flush_task is None or self._flush_task.done():
            self._flush_task = asyncio.create_task(self._background_flush_loop())
            logger.info("指令統計批量刷新任務已啟動")

    async def stop_background_flush(self):
        """停止後台刷新任務並刷新剩餘記錄"""
        self._shutdown = True
        if self._flush_task and not self._flush_task.done():
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass

        # 刷新剩餘記錄
        await self._flush_batch_records()
        logger.info("指令統計批量刷新任務已停止")

    async def _background_flush_loop(self):
        """後台刷新循環"""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.flush_interval)
                current_time = time.time()

                # 檢查是否需要強制刷新（基於時間）
                if current_time - self._last_flush_time >= self.flush_interval:
                    await self._flush_batch_records()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("後台刷新任務發生錯誤: %s", e)
                await asyncio.sleep(1)  # 避免錯誤循環

    async def record_command_usage(self, record: CommandUsageRecord) -> bool:
        """
        記錄指令使用（批量模式）

        Args:
            record: 指令使用記錄

        Returns:
            是否記錄成功
        """
        try:
            async with self._batch_lock:
                self._batch_records.append(record)

                # 檢查是否需要刷新（基於數量）
                if len(self._batch_records) >= self.batch_size:
                    await self._flush_batch_records()

            return True

        except Exception as e:
            logger.error(
                "添加指令記錄到批量緩存失敗: %s, error=%s", record.command_name, e
            )
            return False

    async def _flush_batch_records(self):
        """刷新批量記錄到數據庫"""
        if not self._batch_records:
            return

        # 複製並清空緩存
        records_to_flush = list(self._batch_records)
        self._batch_records.clear()
        self._last_flush_time = time.time()

        if not records_to_flush:
            return

        try:
            async with self.pool.acquire() as conn:
                # 準備批量插入數據
                values = []
                for record in records_to_flush:
                    values.append(
                        (
                            record.command_name,
                            record.user_id,
                            record.guild_id,
                            record.channel_id,
                            record.command_type,
                            record.success,
                            record.error_message,
                        )
                    )

                # 批量插入
                await conn.executemany(
                    """
                    INSERT INTO command_usage_stats (
                        command_name, user_id, guild_id, channel_id,
                        command_type, success, error_message
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                    values,
                )

                logger.debug("批量插入 %s 條指令使用記錄", len(records_to_flush))

        except Exception as e:
            logger.error("批量插入指令使用記錄失敗: %s", e)
            # 如果批量插入失敗，嘗試逐條插入（容錯處理）
            await self._fallback_individual_insert(records_to_flush)

    async def _fallback_individual_insert(self, records: List[CommandUsageRecord]):
        """容錯處理：逐條插入記錄"""
        logger.warning("嘗試逐條插入 %s 條記錄作為容錯處理", len(records))
        success_count = 0

        async with self.pool.acquire() as conn:
            for record in records:
                try:
                    await conn.execute(
                        """
                        INSERT INTO command_usage_stats (
                            command_name, user_id, guild_id, channel_id,
                            command_type, success, error_message
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    """,
                        record.command_name,
                        record.user_id,
                        record.guild_id,
                        record.channel_id,
                        record.command_type,
                        record.success,
                        record.error_message,
                    )
                    success_count += 1
                except Exception as e:
                    logger.error("逐條插入失敗: %s, error=%s", record.command_name, e)

        logger.info("容錯處理完成，成功插入 %s/%s 條記錄", success_count, len(records))

    async def get_command_stats(self, command_name: str) -> Optional[CommandStats]:
        """
        獲取特定指令的統計數據

        Args:
            command_name: 指令名稱

        Returns:
            指令統計數據或None
        """
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(
                    """
                    SELECT
                        command_name,
                        total_uses,
                        unique_users,
                        unique_guilds,
                        first_used,
                        last_used,
                        success_rate
                    FROM command_stats_summary
                    WHERE command_name = $1
                """,
                    command_name,
                )

                if row:
                    return CommandStats(
                        command_name=row["command_name"],
                        total_uses=row["total_uses"],
                        unique_users=row["unique_users"],
                        unique_guilds=row["unique_guilds"],
                        first_used=row["first_used"],
                        last_used=row["last_used"],
                        success_rate=float(row["success_rate"]),
                    )
                return None

        except Exception as e:
            logger.error("獲取指令統計失敗: %s, error=%s", command_name, e)
            return None

    async def get_all_command_stats(self, limit: int = 100) -> List[CommandStats]:
        """
        獲取所有指令的統計數據

        Args:
            limit: 返回結果數量限制

        Returns:
            指令統計數據列表
        """
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT
                        command_name,
                        total_uses,
                        unique_users,
                        unique_guilds,
                        first_used,
                        last_used,
                        success_rate
                    FROM command_stats_summary
                    ORDER BY total_uses DESC
                    LIMIT $1
                """,
                    limit,
                )

                return [
                    CommandStats(
                        command_name=row["command_name"],
                        total_uses=row["total_uses"],
                        unique_users=row["unique_users"],
                        unique_guilds=row["unique_guilds"],
                        first_used=row["first_used"],
                        last_used=row["last_used"],
                        success_rate=float(row["success_rate"]),
                    )
                    for row in rows
                ]

        except Exception as e:
            logger.error("獲取所有指令統計失敗: error=%s", e)
            return []

    async def get_user_command_stats(
        self, user_id: int, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        獲取特定用戶的指令使用統計

        Args:
            user_id: 用戶ID
            limit: 返回結果數量限制

        Returns:
            用戶指令統計數據列表
        """
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT
                        command_name,
                        COUNT(*) as usage_count,
                        MIN(used_at) as first_used,
                        MAX(used_at) as last_used,
                        (
                            COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)
                        ) as success_rate
                    FROM command_usage_stats
                    WHERE user_id = $1
                    GROUP BY command_name
                    ORDER BY usage_count DESC
                    LIMIT $2
                """,
                    user_id,
                    limit,
                )

                return [dict(row) for row in rows]

        except Exception as e:
            logger.error("獲取用戶指令統計失敗: user_id=%s, error=%s", user_id, e)
            return []

    async def get_total_stats(self) -> Dict[str, Any]:
        """
        獲取總體統計數據

        Returns:
            總體統計數據字典
        """
        try:
            async with self.pool.acquire() as conn:
                # 獲取總體統計
                total_stats = await conn.fetchrow(
                    """
                    SELECT
                        COUNT(*) as total_commands,
                        COUNT(DISTINCT command_name) as unique_commands,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT guild_id)
                        FILTER (WHERE guild_id IS NOT NULL) as unique_guilds,
                        MIN(used_at) as first_command,
                        MAX(used_at) as last_command,
                        (
                            COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)
                        ) as overall_success_rate
                    FROM command_usage_stats
                """
                )

                # 獲取今日統計
                today_stats = await conn.fetchrow(
                    """
                    SELECT
                        COUNT(*) as today_commands,
                        COUNT(DISTINCT command_name) as today_unique_commands,
                        COUNT(DISTINCT user_id) as today_unique_users
                    FROM command_usage_stats
                    WHERE DATE(used_at AT TIME ZONE 'Asia/Taipei') = CURRENT_DATE
                """
                )

                return {
                    "total_commands": total_stats["total_commands"],
                    "unique_commands": total_stats["unique_commands"],
                    "unique_users": total_stats["unique_users"],
                    "unique_guilds": total_stats["unique_guilds"],
                    "first_command": total_stats["first_command"],
                    "last_command": total_stats["last_command"],
                    "overall_success_rate": float(total_stats["overall_success_rate"]),
                    "today_commands": today_stats["today_commands"],
                    "today_unique_commands": today_stats["today_unique_commands"],
                    "today_unique_users": today_stats["today_unique_users"],
                }

        except Exception as e:
            logger.error("獲取總體統計失敗: error=%s", e)
            return {}

    async def get_guild_stats(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        獲取伺服器使用統計排行榜

        Args:
            limit: 返回結果數量限制

        Returns:
            伺服器統計數據列表
        """
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT
                        guild_id,
                        COUNT(*) as total_commands,
                        COUNT(DISTINCT command_name) as unique_commands,
                        COUNT(DISTINCT user_id) as unique_users,
                        MIN(used_at) as first_used,
                        MAX(used_at) as last_used,
                        (
                            COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)
                        ) as success_rate
                    FROM command_usage_stats
                    WHERE guild_id IS NOT NULL
                    GROUP BY guild_id
                    ORDER BY total_commands DESC
                    LIMIT $1
                """,
                    limit,
                )

                return [dict(row) for row in rows]

        except Exception as e:
            logger.error("獲取伺服器統計失敗: error=%s", e)
            return []

    async def get_guild_command_stats(
        self, guild_id: int, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        獲取特定伺服器的指令使用統計

        Args:
            guild_id: 伺服器ID
            limit: 返回結果數量限制

        Returns:
            伺服器指令統計數據列表
        """
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT
                        command_name,
                        COUNT(*) as usage_count,
                        COUNT(DISTINCT user_id) as unique_users,
                        MIN(used_at) as first_used,
                        MAX(used_at) as last_used,
                        (
                            COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)
                        ) as success_rate
                    FROM command_usage_stats
                    WHERE guild_id = $1
                    GROUP BY command_name
                    ORDER BY usage_count DESC
                    LIMIT $2
                """,
                    guild_id,
                    limit,
                )

                return [dict(row) for row in rows]

        except Exception as e:
            logger.error("獲取伺服器指令統計失敗: guild_id=%s, error=%s", guild_id, e)
            return []

    async def update_summary_table(self) -> None:
        """
        重新計算並更新整個指令統計摘要表。
        這是一個資源密集型操作，應該定期在後台運行，而不是由觸發器觸發。
        """
        logger.info("開始執行指令統計摘要表的批量更新任務...")
        try:
            async with self.pool.acquire() as conn:
                # 一次性計算所有指令的統計數據
                # 使用 CTE (Common Table Expression) 來提高可讀性
                query = """
                WITH new_stats AS (
                    SELECT
                        command_name,
                        COUNT(*) as total_uses,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT guild_id) FILTER (WHERE guild_id IS NOT NULL) as unique_guilds,
                        MIN(used_at) as first_used,
                        MAX(used_at) as last_used,
                        (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate
                    FROM command_usage_stats
                    GROUP BY command_name
                )
                INSERT INTO command_stats_summary (
                    command_name, total_uses, unique_users, unique_guilds,
                    first_used, last_used, success_rate, last_updated
                )
                SELECT
                    ns.command_name, ns.total_uses, ns.unique_users, ns.unique_guilds,
                    ns.first_used, ns.last_used, ns.success_rate, CURRENT_TIMESTAMP
                FROM new_stats ns
                ON CONFLICT (command_name) DO UPDATE SET
                    total_uses = EXCLUDED.total_uses,
                    unique_users = EXCLUDED.unique_users,
                    unique_guilds = EXCLUDED.unique_guilds,
                    first_used = EXCLUDED.first_used,
                    last_used = EXCLUDED.last_used,
                    success_rate = EXCLUDED.success_rate,
                    last_updated = EXCLUDED.last_updated;
                """
                status = await conn.execute(query)
                logger.info("指令統計摘要表批量更新任務完成。狀態: %s", status)

        except Exception as e:
            logger.error("更新指令統計摘要表失敗: %s", e, exc_info=True)


# 全局實例
_db_command_usage_service = None


async def get_db_command_usage_service(
    pool: Optional[asyncpg.Pool] = None,
    batch_size: int = 50,
    flush_interval: float = 5.0,
) -> Optional[DatabaseCommandUsageService]:
    """獲取全局數據庫指令使用統計服務實例"""
    global _db_command_usage_service

    if _db_command_usage_service is None and pool is not None:
        _db_command_usage_service = DatabaseCommandUsageService(
            pool, batch_size, flush_interval
        )
        # 啟動後台刷新任務
        await _db_command_usage_service.start_background_flush()

    return _db_command_usage_service


async def shutdown_db_command_usage_service():
    """關閉數據庫指令使用統計服務"""
    global _db_command_usage_service

    if _db_command_usage_service is not None:
        await _db_command_usage_service.stop_background_flush()
        _db_command_usage_service = None
