ui_settings:
  # ui_settings.yaml
  # UI 相關配置

  # 預設稀有度表情符號
  default_rarity_emojis:
    7: "<a:t7:1367204143713882275>"  # TS
    6: "<a:t6:1367204167063437462>"  # T6
    5: "<a:t5:1367204150898724935>"  # T5
    4: "<a:t4:1367204172906369034>"  # T4
    3: "<a:t3:1367589351327928552>"  # T3
    2: "<a:t2:1367204161728282675>"  # T2
    1: "<a:t1:1367204136528904295>"   # T1

  # 特定卡池的稀有度相關表情符號 (僅定義與 default_rarity_emojis 不同的部分)
  # 根據原始 Python 文件，此處為空，因為所有卡池都使用預設值
  pool_specific_rarity_emojis: {}

  # 預設全圖鑑專用稀有度表情符號
  default_encyclopedia_rarity_emojis:
    7: "<a:RARITYcyan:1357560510006165615>"    # 青色 (TS)
    6: "<a:RARITYred:1357560510006165615>"   # 紅色 (T6)
    5: "<a:RARITYorange:1357560546617987202>"     # 紅色 (T5)<a:RARITYorange:1357560546617987202>
    4: "<a:RARITYyellow:1357560475302494228>"  # 黃色 (T4)
    3: "<a:RARITYpurple:1357560468906049607>"  # 紫色 (T3)
    2: "<a:RARITYblue:1357560553991700500>"    # 藍色 (T2)
    1: "<a:RARITYgreen:1357560562682433686>"   # 綠色 (T1)

  # 特定卡池的全圖鑑專用稀有度表情符號 (僅定義與 default_encyclopedia_rarity_emojis 不同的部分)
  # 根據原始 Python 文件，此處為空
  pool_specific_encyclopedia_rarity_emojis: {}

  # 稀有度對應的GIF圖片URL
  rarity_images:
    all_pools: # 或者 "default"
      1: "https://cdn.discordapp.com/emojis/1365424608496324779.gif?size=32" # T1 / C-T1
      2: "https://cdn.discordapp.com/emojis/1365424342732640337.gif?size=32" # T2 / C-T2
      3: "https://cdn.discordapp.com/emojis/1365424561817911436.gif?size=32" # T3 / C-T3
      4: "https://cdn.discordapp.com/emojis/1365424519031820308.gif?size=32" # T4 / C-T4
      5: "https://cdn.discordapp.com/emojis/1365424754042867873.gif?size=32" # T5 / C-T5
      6: "https://cdn.discordapp.com/emojis/1365424399338967132.gif?size=32" # T6
      7: "https://cdn.discordapp.com/emojis/1365424940958093423.gif?size=32"  # TS

  # 星級相關表情符號
  star_emojis:
    1: "<a:diamante6:1366109166158217226>"
    2: "<a:diamante5:1366109193118941284>"
    3: "<a:diamante7:1366109201398628452>"
    4: "<a:diamante3:1366109173481209856>"
    5: "<a:diamante2:1366109178493534331>"
    6: "<a:diamante4:1366109211586465856>"
    7: "<a:diamante:1366109158616596500>"

  # UI 按鈕相關的表情符號
  ui_button_emojis:
    old_card: "<a:sw:1365447243863429273>"
    heart: "<a:pu:1365482490478989353>"
    new_card: "<a:sr:1357714854244515936>"
    sell: "<:sellall:1366135697941205163>"

  # 卡池類型表情符號
  pool_type_emojis:
    special: '<a:serverboost:1365442742561275966> '
    summer: '<a:summer:1367020204168843325> '
    vd: '<a:vd:1371096635102330881>'
    special_maid: '<a:md:1371096614118359092>'
    hololive: '<a:holo:1371476350367698976>'
    ua: '<a:kin:1372307002356797461>'
    ptcg: '<a:ptcg:1373744985111003227>'
    wixoss: '<a:wixoss:1381346876628009093>'
    ongeki: '<a:ongeki:1388852744076660736>'
    sve: '<a:sve:1392613399879225425>'

  # 用戶友好的稀有度顯示名稱 (代碼 -> 名稱)
  user_friendly_rarity_display_names:
    C: "普通 (C)"
    R: "稀有 (R)"
    SR: "超稀有 (SR)"
    SSR: "特殊稀有 (SSR)"
    UR: "極稀有 (UR)"
    LR: "傳說稀有 (LR)"
    EX: "特殊限定 (EX)"

  # 稀有度顯示代碼 (內部數字代碼 -> 用戶友好代碼)
  rarity_display_codes:
    1: "C"
    2: "R"
    3: "SR"
    4: "SSR"
    5: "UR"
    6: "LR"
    7: "EX"

  # 稀有度顏色整數值 (按卡池類型和稀有度數字鍵)
  rarity_colors_int:
    main:
      1: 11584734  # 0xB0C4DE (COMMON)
      2: 9029355   # 0x87CEEB (RARE)
      3: 9662683   # 0x9370DB (SUPER_RARE)
      4: 16766720  # 0xFFD700 (SSR)
      5: 16729344  # 0xFF4500 (ULTRA_RARE)
      6: 16711680  # 0xFF0000 (LEGENDARY_RARE)
      7: 65535     # 0x00FFFF (EXCLUSIVE)
    special: # 與 main 相同
      1: 11584734
      2: 9029355
      3: 9662683
      4: 16766720
      5: 16729344
      6: 16711680
      7: 65535
    summer: # 與 main 相同
      1: 11584734
      2: 9029355
      3: 9662683
      4: 16766720
      5: 16729344
      6: 16711680
      7: 65535
    default: # 與 main 相同
      1: 11584734
      2: 9029355
      3: 9662683
      4: 16766720
      5: 16729344
      6: 16711680
      7: 65535

  # 完成度指示器表情符號
  completion_indicator_emojis:
    "100": "<a:check2:1357795058782441766>"
    "75+": "<a:check5:1365494516966228058>"
    "50+": "<a:check4:1365494507163877457>"
    "0+": "<a:check3:1365494496577458256>"
    "0": "<:nocheck:1357796970160455892>"

  # 新聞類型可讀名稱
  news_type_readable_names:
    PTT_ANALYSIS: "鄉民分析"
    CITIZEN_STORY: "平民新聞"
    INSIDER_TIP: "內線情報"
    ANALYST_REPORT: "分析師日報"
    CORPORATE_ANNOUNCEMENT: "公司重大公告"
    REGULATORY_CHANGE: "行業監管政策變動"
    MARKET_RUMOR: "市場謠言/耳語"
    TECHNICAL_SIGNAL: "技術分析指標訊號"
    GENERAL_MARKET_NEWS: "市場綜合快訊"
    LEGACY_CONTENT: "歷史新聞"
    STOCK_DELISTED: "股票下市"
    STOCK_ST_WARNING: "ST警示"
    STOCK_ST_RECOVERY: "ST摘帽"
    STOCK_NEW_LISTING: "新股上市"

  # 角色原型可讀名稱
  character_archetype_readable_names:
    PTT_USER: "PTT鄉民"
    ORDINARY_CITIZEN: "虛擬平民"
    MYSTERIOUS_INFORMANT: "神秘線人"
    MARKET_ANALYST: "市場分析師"
    CORPORATE_SPOKESPERSON: "公司發言人"
    OFFICIAL_PRESS_RELEASE: "官方新聞稿"
    REGULATORY_BODY_BULLETIN: "監管機構"
    INDUSTRY_ANALYST_INTERPRETATION: "行業分析師"
    GOSSIP_MONGER: "市場八卦通"
    TA_BOT: "AI技術分析師"
    AI_NEWSCASTER_STANDARD: "AI標準播報員"
    ARCHIVED_SOURCE: "歷史存檔來源"
    SYSTEM_ANNOUNCER: "系統公告"

  # 新聞類型顏色整數值
  news_type_colors_int:
    PTT_ANALYSIS: 16753920    # discord.Color.orange().value
    CITIZEN_STORY: 3066993     # discord.Color.green().value
    INSIDER_TIP: 15158332      # discord.Color.red().value
    ANALYST_REPORT: 3447003    # discord.Color.blue().value
    CORPORATE_ANNOUNCEMENT: 12749483 # discord.Color.dark_gold().value
    REGULATORY_CHANGE: 7419530  # discord.Color.dark_purple().value
    MARKET_RUMOR: 15105570     # discord.Color.magenta().value
    TECHNICAL_SIGNAL: 2123412   # discord.Color.teal().value
    GENERAL_MARKET_NEWS: 9807270 # discord.Color.light_grey().value
    STOCK_DELISTED: 10038562    # discord.Color.dark_red().value
    STOCK_ST_WARNING: 15158332   # discord.Color.red().value
    STOCK_ST_RECOVERY: 3066993  # discord.Color.green().value
    STOCK_NEW_LISTING: 10181046 # discord.Color.purple().value
    default_news_color: 8359053 # discord.Color.dark_grey().value (adjusted from light_grey based on typical use for default)

  # 小遊戲表情符號 (統一設定，與game_registry.py保持一致)
  minigame_emojis:
    baccarat: "🎴"      # 百家樂
    blackjack: "🃏"     # 21點
    dice: "🎲"          # 三骰子大小
    mines: "💎"         # 尋寶礦區
    slot: "🎰"          # 拉霸機
    poker1v1: "🃏"      # 德州撲克1v1
    spin_wheel: "🎡"    # 轉盤遊戲
    tower: "🏗️"         # 爬塔

  # 油幣表情符號
  oil_emoji: "<:oi:1382174314811363470>"

  # 通用UI顏色配置 (使用Discord顏色的整數值)
  common_ui_colors:
    success: 5763719        # discord.Color.green().value
    error: 15158332         # discord.Color.red().value  
    warning: 16753920       # discord.Color.orange().value
    info: 3447003          # discord.Color.blue().value
    secondary: 9807270     # discord.Color.light_grey().value
    default: 8359053       # discord.Color.dark_grey().value
    
  # 表單驗證相關配置
  form_validation:
    max_card_name_length: 100      # 卡片名稱最大長度
    max_series_name_length: 100    # 系列名稱最大長度
    max_description_length: 500    # 描述最大長度
    
  # UI 超時設定
  ui_timeouts:
    view_timeout: 600              # 視圖超時時間（秒）
    modal_timeout: 300             # Modal超時時間（秒）
    
  # 卡片相關設定
  card_settings:
    min_description_star_level: 10 # 設置描述所需的最低星級