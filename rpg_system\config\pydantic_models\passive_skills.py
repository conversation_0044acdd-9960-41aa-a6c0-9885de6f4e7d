"""
被動技能配置的Pydantic模型（增量式格式）
"""

from typing import Dict, List, Optional

from pydantic import BaseModel, Field, RootModel

from .base_models import EffectDefinition, TargetOverride, TriggerCondition


class XpToNextLevelConfig(BaseModel):
    """技能升級XP配置模型"""

    base_xp: int = Field(..., ge=1)
    multiplier: float = Field(..., gt=1.0)


class PassiveEffectBlock(BaseModel):
    """被動技能效果塊模型"""

    trigger_condition: TriggerCondition
    target_override: Optional[TargetOverride] = None
    effect_definitions: List[EffectDefinition]


class PassiveSkillConfig(BaseModel):
    """被動技能配置模型（增量式格式）"""

    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    base_effects: List[PassiveEffectBlock]
    xp_gain_on_sacrifice: int = Field(..., ge=0)
    xp_to_next_level_config: XpToNextLevelConfig
    tags: Optional[List[str]] = None


class PassiveSkillsConfig(RootModel[Dict[str, PassiveSkillConfig]]):
    """被動技能配置文件模型"""

    root: Dict[str, PassiveSkillConfig]

    def __getitem__(self, item):
        return self.root[item]

    def get(self, key, default=None):
        return self.root.get(key, default)

    def keys(self):
        return self.root.keys()

    def values(self):
        return self.root.values()

    def items(self):
        return self.root.items()
