# -*- coding: utf-8 -*-
"""
戰力分析功能的設定檔
"""

import os
from dataclasses import dataclass, field
from typing import Dict, <PERSON><PERSON>


def _get_font_path(font_filename: str) -> str:
    """獲取字體文件的正確路徑，支持Docker和非Docker環境"""
    # 嘗試多個可能的路徑
    possible_paths = [
        os.path.join("fonts", font_filename),  # 相對路徑（Docker環境）
        os.path.join(os.getcwd(), "fonts", font_filename),  # 當前工作目錄
        os.path.join("/app", "fonts", font_filename),  # Docker容器內路徑
        os.path.join(r"D:\DICKPK", "fonts", font_filename),  # Windows開發環境路徑
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果都找不到，返回相對路徑作為fallback
    return os.path.join("fonts", font_filename)


@dataclass
class PowerAnalysisConfig:
    """戰力分析配置類"""

    webhook_url: str = (
        "https://discord.com/api/webhooks/1383902917571903549/A64h1nL3mtWz589j3x23TDgY9K-G-PDasrTUHpIfFrBixWjRnLS2DiBHK9b-WkVbvS3u"
    )
    webhook_color: int = 0xFF6B35
    template_path: str = "auxiliary/cogs/templates/image.png"
    chinese_font_path: str = field(
        default_factory=lambda: _get_font_path("NotoSansTC-Regular.ttf")
    )
    chinese_bold_font_path: str = field(
        default_factory=lambda: _get_font_path("NotoSansTC-Bold.ttf")
    )
    emoji_font_path: str = field(
        default_factory=lambda: _get_font_path("NotoEmoji-VariableFont_wght.ttf")
    )
    pixelcraft_font_path: str = field(
        default_factory=lambda: _get_font_path("Pixelcraft - Personal Use.ttf")
    )

    # 圖像位置配置
    avatar_position: Tuple[int, int] = (22, 72)
    avatar_size: Tuple[int, int] = (347, 262)
    username_position: Tuple[int, int] = (40, 333)
    analysis_area: Tuple[int, int, int, int] = (422, 124, 916, 286)
    conclusion_position: Tuple[int, int] = (423, 380)
    power_index_position: Tuple[int, int] = (928, 460)
    progress_bar_area: Tuple[int, int, int, int] = (22, 487, 937, 498)
    timestamp_position: Tuple[int, int] = (734, 580)

    # 字體大小配置
    font_sizes: Dict[str, int] = field(
        default_factory=lambda: {"large": 32, "medium": 24, "small": 22, "tiny": 16}
    )


@dataclass
class PowerLevel:
    """戰力等級配置"""

    name: str
    color: str
    power_range: Tuple[int, int]

    @classmethod
    def get_all_levels(cls) -> Dict[str, "PowerLevel"]:
        """獲取所有戰力等級配置"""
        return {
            "SSS": cls("SSS", "#ff0080", (10000, 10000)),
            "SS": cls("SS", "#ff6b6b", (7500, 9999)),
            "S": cls("S", "#ffd166", (5000, 7499)),
            "A": cls("A", "#06d6a0", (2500, 4999)),
            "B": cls("B", "#118ab2", (1000, 2499)),
            "C": cls("C", "#9370db", (0, 999)),
            "D": cls("D", "#808080", (0, 500)),
        }


# 實例化一個預設設定
default_config = PowerAnalysisConfig()
power_levels = PowerLevel.get_all_levels()
