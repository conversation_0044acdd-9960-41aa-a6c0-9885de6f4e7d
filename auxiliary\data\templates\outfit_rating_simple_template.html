<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭評分報告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['"Noto Sans TC"', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'brand': {
                            50: '#fcf4ff',
                            100: '#f9e9fe',
                            200: '#f5d4fe',
                            300: '#f0b1fc',
                            400: '#e687f8',
                            500: '#db5cf2',
                            600: '#c934e5',
                            700: '#ae27c6',
                            800: '#9222a3',
                            900: '#7a1e85',
                        },
                        'pastel': {
                            'pink': '#ffb6c1',
                            'lavender': '#e6e6fa',
                            'mauve': '#d8bfd8',
                            'lilac': '#c8a2c8',
                            'rose': '#fadadd',
                        }
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Noto Sans TC', system-ui, sans-serif;
            background-color: #fff8fb;
        }
        #rating-content, #suggestion-content {
            word-break: break-word;
            overflow-wrap: anywhere;
            line-height: 1.6;
        }
        .rating-badge {
            box-shadow: 0 4px 10px rgba(226, 113, 243, 0.3);
            border-radius: 1rem;
            background: linear-gradient(135deg, #ff85a2 0%, #ff6677 100%);
            box-shadow: 0 4px 10px rgba(255, 133, 162, 0.4);
            border: 2px solid #ffa8c0;
            position: relative;
        }
        
        /* Add shine effect to rating badge */
        .rating-badge::after {
            content: '';
            position: absolute;
            top: 0;
            left: -50%;
            width: 200%;
            height: 100%;
            background: linear-gradient(
                to right,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            transform: rotate(25deg);
            pointer-events: none;
        }
        
        /* Decorative frame for image */
        .decorative-frame {
            position: relative;
            border-radius: 1.5rem;
            padding: 6px;
            background: linear-gradient(45deg, #ffb6c1, #ffd1dc, #ffb6c1);
            box-shadow: 
                0 6px 16px rgba(255, 182, 193, 0.3),
                0 0 0 3px rgba(255, 255, 255, 0.8) inset;
            display: flex;
            flex-direction: column;
        }
        
        .decorative-frame::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(135deg, #ffcce0, transparent, #ffe0dd);
            border-radius: 1.75rem;
            z-index: -1;
            opacity: 0.7;
        }
        
        /* Decorative dots */
        .dot-top-right, .dot-bottom-left {
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            z-index: 20;
        }
        
        .dot-top-right {
            top: 0px;
            right: 0px;
            background: #ffafaf;
            box-shadow: 0 0 0 3px white, 0 3px 8px rgba(255, 175, 175, 0.4);
        }
        
        .dot-bottom-left {
            bottom: 0px;
            left: 0px;
            background: #ffb6c1;
            box-shadow: 0 0 0 3px white, 0 3px 8px rgba(255, 182, 193, 0.4);
        }
        
        /* Card with texture background */
        .card-with-texture {
            background-color: white;
            position: relative;
        }
        
        .card-with-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(45deg, #ff85a2 8%, transparent 8%, transparent 92%, #ff85a2 92%),
                linear-gradient(135deg, #ff85a2 8%, transparent 8%, transparent 92%, #ff85a2 92%);
            background-size: 30px 30px;
            background-position: 0 0;
            opacity: 0.07;
            z-index: 1;
            pointer-events: none;
            border-radius: inherit;
        }
        
        /* Content areas texture */
        .content-texture {
            position: relative;
        }
        
        .content-texture::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(#ff85a2 1px, transparent 1px);
            background-size: 12px 12px;
            background-position: 0 0;
            opacity: 0.1;
            pointer-events: none;
            border-radius: inherit;
            z-index: 1;
        }
        
        /* Rating score text shadow effect */
        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 6px rgba(255, 255, 255, 0.3);
        }
        
        /* 用戶圖片特效 - 時尚濾鏡 */
        .fashion-image-effect {
            position: relative;
            overflow: hidden;
        }
        
        /* 圖片光影效果 */
        .fashion-image-effect::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(45deg, rgba(255, 186, 195, 0.3) 0%, transparent 40%, transparent 60%, rgba(255, 186, 195, 0.3) 100%),
                linear-gradient(to right, rgba(255, 107, 156, 0.2) 0%, transparent 20%, transparent 80%, rgba(255, 107, 156, 0.2) 100%);
            z-index: 4;
            pointer-events: none;
        }
        
        /* 時尚紋理覆蓋 */
        .fashion-texture {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 70%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 14%),
                radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 14%);
            mix-blend-mode: overlay;
            opacity: 0.7;
            z-index: 3;
            pointer-events: none;
        }
        
        /* 細微顆粒質感 */
        .grain-texture {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.03;
            background-image: url('data:image/svg+xml,%3Csvg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"%3E%3Cfilter id="noiseFilter"%3E%3CfeTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch"/%3E%3C/filter%3E%3Crect width="100%25" height="100%25" filter="url(%23noiseFilter)"/%3E%3C/svg%3E');
            z-index: 2;
            pointer-events: none;
        }
        
        /* 雜誌風格裝飾標籤 */
        .magazine-tag {
            position: absolute;
            background: linear-gradient(135deg, #ff85a2, #ff6b6b);
            color: white;
            font-size: 0.7rem;
            font-weight: bold;
            padding: 3px 8px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.5);
            transform: rotate(-3deg);
            z-index: 10;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }
        
        .tag-top-right {
            top: 15px;
            right: 15px;
            transform: rotate(3deg);
        }
        
        /* 投影效果 */
        .image-shadow-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            box-shadow: inset 0 0 30px rgba(255, 107, 156, 0.15);
            z-index: 3;
            pointer-events: none;
        }
        
        /* 水彩噴濺效果 */
        .watercolor-splash {
            position: absolute;
            width: 80px;
            height: 80px;
            background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M47.3 9.1C49.8 5.3 53.2 5.7 55.6 9.4C59.5 15.5 59.5 22.6 61.3 30.1C61.9 32.4 62.8 34.7 64.4 36.5C68.9 41.9 74.1 41.1 78.6 36.1C81.9 32.5 85.3 29 88.6 25.4C91.4 22.4 94.5 21.6 96.6 25.1C99.1 29.2 96.3 32.5 93.8 35.8C88.7 42.6 83.2 49.1 78 55.9C75.5 59.2 75.1 63.4 75.7 67.6C76.8 75.8 76.8 83.9 71.8 91.1C70.3 93.2 67.8 94.5 65.2 94.6C62.9 94.7 61 93.5 59.8 91.4C55.4 83.8 53.3 75.4 52.1 66.7C51.6 63.4 50.8 60.2 48.5 57.5C45.3 53.9 40.5 54.7 37.2 58.4C33.4 62.8 29.7 67.1 26.1 71.6C24.3 73.9 22.3 75.9 19.1 75.1C16.1 74.3 15.3 71.8 15.1 69C14.9 65.3 16.9 62.4 18.9 59.6C23.9 52.3 29.4 45.3 34.5 38C36.3 35.6 37.1 32.7 36.9 29.5C36.4 20.5 39.3 13.3 47.3 9.1Z" fill="%23FFC1D4" fill-opacity="0.6"/%3E%3C/svg%3E');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0.3;
            mix-blend-mode: soft-light;
            z-index: 2;
            pointer-events: none;
        }
        
        .splash-bottom-left {
            bottom: -20px;
            left: -20px;
            transform: rotate(120deg);
        }
        
        /* 時尚貼紙風格元素 */
        .fashion-sticker {
            position: absolute;
            background: white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            z-index: 7;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #ff6b9c;
            transform: rotate(-8deg);
            font-size: 0.65rem;
            line-height: 1;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }
        
        .sticker-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            bottom: 15px;
            left: 15px;
        }
        
        /* 光暈效果 */
        .image-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%);
            mix-blend-mode: overlay;
            opacity: 0.7;
            z-index: 2;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-pink-50 to-red-50 min-h-screen p-3 sm:p-6 flex items-center justify-center">
    <div class="max-w-md w-full mx-auto rounded-xl shadow-lg overflow-hidden card-with-texture border border-pink-200">
        <!-- 頂部標題欄 - 更緊湊 -->
        <div class="bg-gradient-to-r from-pink-400 to-red-300 text-white py-2 px-3 flex items-center justify-between relative z-10">
            <!-- 左側標題區域重組 -->
            <div class="flex items-center space-x-1.5">
                <!-- 用戶名稱 (從右側移到左側) -->
                <div id="username-display" class="relative transform -rotate-1 z-20">
                    <div class="bg-white px-3 py-1 rounded-bl-xl rounded-tr-xl shadow-md border-2 border-pink-300 relative overflow-hidden">
                        <!-- 裝飾性背景圖案 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-pink-100 to-red-50 opacity-60"></div>
                        <!-- 裝飾性角落 -->
                        <div class="absolute -top-1 -right-1 w-6 h-6 bg-pink-400 rounded-full transform translate-x-2 translate-y-2 opacity-60"></div>
                        
                        <div class="flex items-center relative z-10">
                            <div class="bg-pink-400 text-white p-1 rounded-full mr-1.5 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div>
                            <span id="username-text" class="text-pink-600 text-sm font-bold">Honkomagake</span>
                        </div>
                        
                        <!-- 裝飾性貼紙效果 -->
                        <div class="absolute -bottom-0.5 -left-0.5 w-4 h-4 bg-yellow-300 rounded-tr-lg transform rotate-12 opacity-70"></div>
                    </div>
                </div>
                
                <!-- 連接的標題文字 -->
                <span class="text-sm font-medium text-white self-center ml-1">的穿搭評分報告</span>
            </div>
            
            <!-- 右側空白區域，保持佈局平衡 -->
            <div class="w-4"></div>
        </div>

        <div class="p-3 flex flex-col md:flex-row gap-3 relative z-10">
            <!-- 左側：用戶圖片和評分 -->
            <div class="md:w-2/5 flex flex-col md:justify-between md:self-stretch gap-2">
                <!-- 用戶上傳的圖片區域 -->
                <div class="decorative-frame flex-grow md:flex md:flex-col">
                    <div class="dot-top-right"></div>
                    <div class="dot-bottom-left"></div>
                    <div id="user-image-container" class="w-full max-w-full bg-gradient-to-br from-pink-50 to-red-50 rounded-xl flex items-center justify-center overflow-hidden shadow-inner border border-pink-100 md:flex-grow fashion-image-effect">
                        <img id="user-image" src="15aed2b44aed2e73ef2e8f0bc101a18b86d6fa22.jpg" alt="用戶上傳的穿搭照片" class="w-full h-full object-cover" style="z-index: 1;">
                        
                        <!-- 時尚紋理覆蓋層 -->
                        <div class="fashion-texture"></div>
                        
                        <!-- 細微顆粒質感 -->
                        <div class="grain-texture"></div>
                        
                        <!-- 投影效果 -->
                        <div class="image-shadow-effect"></div>
                        
                        <!-- 水彩噴濺效果 -->
                        <div class="watercolor-splash splash-bottom-left"></div>
                        
                        <!-- 光暈效果 -->
                        <div class="image-glow"></div>
                        
                        <!-- 用戶圖片將替換此處 -->
                    </div>
                </div>
                
                <!-- 評分移至圖片下方 -->
                <div id="rating-badge" class="rating-badge text-white rounded-2xl py-2 px-3 flex items-center justify-center relative overflow-hidden">
                    <!-- Decorative elements -->
                    <div class="absolute inset-0 bg-gradient-to-br from-pink-400 to-red-300 opacity-80 z-0"></div>
                    <div class="absolute -top-4 -right-4 w-12 h-12 bg-white opacity-10 rounded-full"></div>
                    <div class="absolute -bottom-4 -left-4 w-10 h-10 bg-white opacity-10 rounded-full"></div>
                    
                    <!-- Sparkle dots -->
                    <div class="absolute top-1 left-3 w-1.5 h-1.5 bg-white rounded-full opacity-70"></div>
                    <div class="absolute bottom-1 right-6 w-1 h-1 bg-white rounded-full opacity-70"></div>
                    
                    <!-- Rating content with enhanced styling -->
                    <div class="flex items-center justify-center relative z-10">
                        <!-- Star icon on the left -->
                        <div class="mr-2 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-100 filter drop-shadow" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="0.5">
                                <path d="M12 2l2.4 7.5H22l-6 4.5 2.3 7-6.3-4.5L5.7 21l2.3-7-6-4.5h7.6z"/>
                            </svg>
                        </div>
                        
                        <!-- Score -->
                        <span id="rating-score" class="text-xl font-bold text-shadow">8.5</span>
                        <span class="text-xs font-normal">/10</span>
                        
                        <!-- Vertical divider -->
                        <div class="mx-2 h-6 w-px bg-white bg-opacity-30"></div>
                        
                        <!-- Rating text -->
                        <span class="text-xs font-medium tracking-wide">評分</span>
                    </div>
                    
                    <!-- Decorative corner accent -->
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-pink-300 rounded-tl-lg transform rotate-45"></div>
                </div>
            </div>

            <!-- 右側：評價和建議區域 -->
            <div class="md:w-3/5 space-y-3 flex flex-col">
                <!-- 穿搭評價 -->
                <div class="bg-white rounded-lg border border-pink-200 shadow-md overflow-hidden flex-grow relative">
                    <!-- 時尚雜誌風格裝飾元素 -->
                    <div class="absolute top-0 left-0 w-24 h-2 bg-gradient-to-r from-pink-400 to-red-300"></div>
                    <div class="absolute top-0 right-0 w-16 h-16 bg-pastel-pink opacity-10 rounded-full -translate-x-2 -translate-y-6"></div>
                    
                    <!-- 大型裝飾性引號 -->
                    <div class="absolute top-8 left-4 text-6xl text-pink-200 font-serif opacity-20 leading-none">"</div>
                    
                    <!-- 雜誌風格標題區 -->
                    <div class="pt-5 px-5 pb-2 relative z-10">
                        <h3 class="text-sm font-bold text-pink-500 tracking-wider uppercase flex items-center border-b border-pink-100 pb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                            </svg>
                            穿搭評價
                        </h3>
                    </div>
                    
                    <!-- 雜誌風格內容區 -->
                    <div class="px-5 pt-0 pb-5 relative z-10">
                        <div id="rating-content" class="text-gray-700 text-sm leading-relaxed font-medium">
                            您的穿搭展現出清新自然的風格，整體色調協調且符合季節感。上衣的剪裁與下裝搭配合宜，展現出良好的比例感。配飾選擇簡約而不失個性，為整體造型增添亮點。
                        </div>
                        
                        <!-- 雜誌風格小裝飾元素 -->
                        <div class="flex items-center justify-end mt-3">
                            <div class="w-12 h-0.5 bg-gradient-to-r from-pink-300 to-pink-100 rounded-full mr-1"></div>
                            <div class="w-8 h-0.5 bg-gradient-to-r from-pink-300 to-pink-100 rounded-full"></div>
                        </div>
                    </div>
                </div>

                <!-- 改進建議 - 社交媒體風格 -->
                <div class="bg-white rounded-lg border border-red-100 shadow-md overflow-hidden flex-grow-0 relative">
                    <!-- 社交媒體風格頂部裝飾 -->
                    <div class="absolute top-0 right-0 w-full h-1.5 bg-gradient-to-r from-red-200 via-pink-300 to-red-200"></div>
                    
                    <!-- 社交媒體風格標題欄 -->
                    <div class="flex items-center px-4 pt-4 pb-2 relative z-10">
                        <div class="bg-red-400 text-white p-1.5 rounded-full shadow-md mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-sm font-bold text-red-500">改進建議</h3>
                        <!-- 社交媒體風格動作按鈕 - 純裝飾 -->
                        <div class="ml-auto flex space-x-1.5">
                            <div class="w-1.5 h-1.5 bg-red-300 rounded-full"></div>
                            <div class="w-1.5 h-1.5 bg-red-300 rounded-full"></div>
                            <div class="w-1.5 h-1.5 bg-red-300 rounded-full"></div>
                        </div>
                    </div>
                    
                    <!-- 社交媒體風格內容 -->
                    <div class="px-4 pt-0 pb-4 relative z-10">
                        <div id="suggestion-content" class="text-gray-700 text-sm leading-relaxed pl-8 border-l-2 border-red-100 relative">
                            <!-- 小裝飾元素 - 引導閱讀 -->
                            <div class="absolute left-3 top-0 w-1 h-full bg-gradient-to-b from-red-200 via-pink-100 to-transparent"></div>
                            建議可以嘗試加入更多層次感的單品，如外套或配飾。鞋子選擇可考慮更鮮明的色彩，以提升整體造型的視覺焦點。
                        </div>
                        
                        <!-- 社交媒體互動區 - 純裝飾 -->
                        <div class="flex items-center mt-3 pt-2 border-t border-red-50">
                            <!-- 頭像 -->
                            <div class="w-8 h-8 rounded-full bg-gradient-to-br from-pink-400 to-red-300 shadow-sm flex items-center justify-center text-white font-bold text-sm border-2 border-white">
                                牛
                            </div>
                            
                            <!-- 對話氣泡 -->
                            <div class="ml-2 bg-pink-50 rounded-lg rounded-tl-none px-3 py-1.5 shadow-sm relative max-w-[calc(100%-40px)] flex items-center">
                                <!-- 小三角形 -->
                                <div class="absolute -left-2 top-0 w-0 h-0 border-t-8 border-r-8 border-b-0 border-l-0 border-solid border-pink-50"></div>
                                
                                <!-- 評論文字 -->
                                <span class="text-sm text-pink-600 font-medium">哇操!超讚!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部簽名 -->
        <div class="bg-gradient-to-r from-pink-50 to-red-50 p-2 border-t border-pink-100 flex justify-between items-center text-xs relative z-10">
            <div class="text-pink-500 font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                由 米米的牛牛 顧問分析
            </div>
            <div class="text-red-400 font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="4" width="18" height="16" rx="2" ry="2"></rect>
                    <line x1="8" y1="2" x2="8" y2="4"></line>
                    <line x1="16" y1="2" x2="16" y2="4"></line>
                    <circle cx="12" cy="12" r="3"></circle>
                    <line x1="12" y1="9" x2="12" y2="15"></line>
                    <line x1="9" y1="12" x2="15" y2="12"></line>
                </svg>
                使用/rate 指令並上傳照片即可評分!
            </div>
        </div>
    </div>
</body>
</html> 