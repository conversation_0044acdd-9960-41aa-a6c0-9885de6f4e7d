"""
Pioneer System 時代處理器
處理時代晉升相關的邏輯
"""

from typing import Any, Dict, List

import gacha.services.economy_service as economy_service
from pioneer.exceptions import (
    PioneerActionError,
    PioneerInsufficientFundsError,
    PioneerInsufficientItemsError,
    PioneerNotFoundError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

from .base_processor import BaseProcessor


class EraProcessor(BaseProcessor):
    """時代晉升動作處理器"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """
        執行時代晉升動作。
        """
        try:
            # 1. 獲取玩家當前時代
            profile = await self.repository.get_pioneer_profile(user_id)
            if not profile:
                raise PioneerNotFoundError(f"找不到玩家 {user_id} 的開拓者檔案。")
            current_era = profile.current_era
            next_era = current_era + 1

            # 2. 獲取下一個時代的配置
            next_era_config = self.game_data.get_era_config(next_era)
            if not next_era_config:
                raise PioneerActionError("你已經達到了時代的終點，無法再晉升！")

            # 3. 檢查所有晉升需求
            from pioneer.utils.requirement_checker import check_requirements

            requirements_check = await check_requirements(
                user_id, next_era_config.requirements, self.game_data, self.repository
            )
            if not requirements_check or not requirements_check.success:
                # The message from check_requirements is already formatted.
                raise PioneerActionError(
                    f"晉升失敗，未滿足以下條件：\n{requirements_check.message if requirements_check else '未知需求。'}"
                )

            # 4. 檢查並消耗晉升成本
            await self._check_and_consume_costs(user_id, next_era_config.costs)

            # 5. 更新玩家時代
            await self.repository.update_user_era(user_id, next_era)

            # 6. 解鎖新時代的任務
            from pioneer.services.task_initializer import task_initializer

            await task_initializer.check_and_unlock_new_quests(user_id, next_era)

            # TODO: 處理 unlocks 字段，解鎖新的動作、設施等

            return ActionResult.success_result(
                f"🎉 恭喜！你已成功晉升到 **{next_era_config.name}**！"
            )

        except (
            PioneerNotFoundError,
            PioneerActionError,
            PioneerInsufficientFundsError,
            PioneerInsufficientItemsError,
        ) as e:
            raise e
        except Exception as e:
            logger.error("時代晉升處理時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError("時代晉升時發生系統錯誤，請聯繫管理員。") from e

    async def _check_and_consume_costs(
        self, user_id: int, costs: List[Dict[str, Any]]
    ) -> None:
        """檢查並消耗所有晉升成本。如果資源不足，則拋出異常。"""
        if not costs:
            return

        balance_info = await economy_service.get_balance(user_id)
        current_balance = balance_info.get("balance", 0)
        missing_costs = []

        # 階段一：檢查所有成本
        for cost in costs:
            cost_type = cost.get("type")
            amount = cost.get("amount")
            if not amount or amount <= 0:
                continue

            if cost_type == "currency" and cost.get("currency") == "oil":
                if current_balance < amount:
                    missing_costs.append(
                        f"• 油幣不足: 需要 {amount:,}, 現有 {current_balance:,}"
                    )
            elif cost_type == "item":
                item_id = cost.get("item_id")
                if not item_id:
                    continue
                current_quantity = await self.repository.get_item_quantity(
                    user_id, item_id
                )
                if current_quantity < amount:
                    item_name = self.game_data.get_item_name(item_id) or item_id
                    missing_costs.append(
                        f"• {item_name} 不足: 需要 {amount}, 現有 {current_quantity}"
                    )

        if missing_costs:
            raise PioneerInsufficientFundsError(
                "晉升失敗，資源不足：\n" + "\n".join(missing_costs)
            )

        # 階段二：消耗所有成本
        profile = await self.repository.get_pioneer_profile(user_id)
        if not profile:
            raise PioneerNotFoundError(f"在消耗成本時找不到玩家 {user_id} 的檔案。")
        next_era = profile.current_era + 1

        for cost in costs:
            cost_type = cost.get("type")
            amount = cost.get("amount", 0)
            if amount <= 0:
                continue

            if cost_type == "currency" and cost.get("currency") == "oil":
                await economy_service.award_oil(
                    user_id=user_id,
                    amount=-amount,
                    transaction_type="pioneer:era_advancement",
                    reason=f"Advance to era {next_era}",
                )
            elif cost_type == "item":
                item_id = cost.get("item_id")
                if item_id:
                    await self.repository.consume_warehouse_item(
                        user_id, item_id, amount
                    )
