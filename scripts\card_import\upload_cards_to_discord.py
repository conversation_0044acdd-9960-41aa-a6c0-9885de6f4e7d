import asyncio
import json
import logging
import os
import re

import aiohttp
import discord

# --- 設定 ---
BOT_TOKEN = "MTMzNzEzMDg4MTY4NDM0NDg0Mw.GiN58S.8dXetrG80Us6_LEDbNesT7yqm7sD07FUjdPPrM"
CHANNEL_ID = 1397426147117105193
CARDS_JSON_PATH = r"D:\DICKPK\exported_cards\cards_main.json"
IMAGE_BASE_PATH = r"D:\DICKPK\downloaded_gacha_master_cards"
OUTPUT_JSON_PATH = r"D:\DICKPK\exported_cards\cards_with_cdn_urls.json"

# 設定日誌
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

# 稀有度對應資料夾名稱
RARITY_MAP = {1: "C", 2: "R", 3: "SR", 4: "SSR", 5: "UR", 6: "LR", 7: "EX"}

intents = discord.Intents.default()
client = discord.Client(intents=intents)


def sanitize_filename(name):
    """清理檔名，將空格替換為底線，並移除 Windows 非法字元"""
    name = name.replace(" ", "_")
    name = re.sub(r'[<>:"/\\|?*]', "", name)
    return name.rstrip("_")


def load_progress():
    """載入已有的進度"""
    if os.path.exists(OUTPUT_JSON_PATH):
        try:
            with open(OUTPUT_JSON_PATH, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logging.warning(f"無法讀取進度檔案 {OUTPUT_JSON_PATH}，將從頭開始。")
            return []
    return []


def save_progress(data):
    """儲存進度"""
    try:
        with open(OUTPUT_JSON_PATH, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
    except Exception as e:
        logging.error(f"儲存進度到 {OUTPUT_JSON_PATH} 失敗: {e}")


async def upload_card_images():
    """主函數，用於讀取資料、尋找圖片並上傳，支援斷點續傳和重試"""
    await client.wait_until_ready()
    logging.info(f"以 {client.user} 登入")

    channel = client.get_channel(CHANNEL_ID)
    if not channel or not isinstance(
        channel, (discord.TextChannel, discord.Thread, discord.ForumChannel)
    ):
        logging.error(f"頻道 ID {CHANNEL_ID} 無效或不是可傳送訊息的頻道。")
        await client.close()
        return

    try:
        with open(CARDS_JSON_PATH, "r", encoding="utf-8") as f:
            all_cards_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logging.error(f"讀取或解析主卡片 JSON 失敗: {e}")
        await client.close()
        return

    # 斷點續傳邏輯 (修正)
    progress_data = load_progress()
    # 只將擁有有效 CDN URL 的視為已處理
    processed_ids = {
        card.get("card_id")
        for card in progress_data
        if isinstance(card.get("image_url"), str)
        and card["image_url"].startswith("https://cdn.discordapp.com")
    }

    cards_to_process = {card["card_id"]: card for card in all_cards_data}
    for card in progress_data:
        if card.get("card_id") in cards_to_process:
            cards_to_process[card["card_id"]] = card

    total_cards = len(all_cards_data)
    remaining_cards_list = [
        cid for cid in cards_to_process.keys() if cid not in processed_ids
    ]
    remaining_count = len(remaining_cards_list)

    logging.info(
        f"總共 {total_cards} 張卡片，已成功處理 {len(processed_ids)} 張，剩餘 {remaining_count} 張。"
    )

    if remaining_count == 0:
        logging.info("所有卡片皆已成功處理。")
        await client.close()
        return

    save_counter = 0
    for i, card_id in enumerate(remaining_cards_list):
        card = cards_to_process[card_id]
        card_name = card.get("name")
        rarity = card.get("rarity")

        if not all([card_id, card_name, rarity]):
            logging.warning(f"卡片資料不完整，跳過: {card}")
            continue

        rarity_folder = RARITY_MAP.get(rarity)
        if not rarity_folder:
            card["image_url"] = "RARITY_UNKNOWN"
            continue

        sanitized_name = sanitize_filename(card_name)
        base_filename = f"{card_id}_{sanitized_name}"

        # 支援多種圖片格式，按優先順序查找
        supported_extensions = [".gif", ".png", ".jpg", ".jpeg", ".webp"]
        image_path = None

        for ext in supported_extensions:
            potential_path = os.path.join(
                IMAGE_BASE_PATH, rarity_folder, f"{base_filename}{ext}"
            )
            if os.path.exists(potential_path):
                image_path = potential_path
                break

        if not image_path:
            # 列出所有嘗試的路徑以便調試
            attempted_paths = [
                os.path.join(IMAGE_BASE_PATH, rarity_folder, f"{base_filename}{ext}")
                for ext in supported_extensions
            ]
            logging.warning(
                f"找不到卡片圖片: ID {card_id}, 名稱 '{card_name}'. 嘗試的路徑: {attempted_paths}"
            )
            card["image_url"] = "NOT_FOUND"
            continue

        upload_success = False
        for attempt in range(3):
            try:
                with open(image_path, "rb") as img_file:
                    discord_file = discord.File(
                        img_file, filename=os.path.basename(image_path)
                    )
                    message = None
                    if isinstance(channel, discord.ForumChannel):
                        thread_name = f"卡片: {card_id} - {sanitized_name}"
                        available_tags = [
                            t for t in channel.available_tags if t.name == "卡片圖片"
                        ]
                        thread_with_message = await channel.create_thread(
                            name=thread_name,
                            file=discord_file,
                            applied_tags=available_tags,
                        )
                        message = thread_with_message.message
                    else:
                        message = await channel.send(file=discord_file)

                    if message and message.attachments:
                        cdn_url = message.attachments[0].url
                        logging.info(
                            f"({i + 1}/{remaining_count}) 成功上傳卡片 {card_id}: {cdn_url}"
                        )
                        card["image_url"] = cdn_url
                        upload_success = True
                        break
            except (
                discord.errors.HTTPException,
                aiohttp.ClientError,
                asyncio.TimeoutError,
            ) as e:
                logging.warning(f"網路錯誤 (嘗試 {attempt + 1}/3) 卡片 {card_id}: {e}")
                if attempt < 2:
                    await asyncio.sleep(5 * (attempt + 1))
            except Exception as e:
                logging.error(f"未預期錯誤 卡片 {card_id}: {e}", exc_info=True)
                break

        if not upload_success:
            card["image_url"] = "UPLOAD_FAILED"

        save_counter += 1
        if save_counter >= 100:
            save_progress(list(cards_to_process.values()))
            logging.info(f"--- 已儲存進度，目前處理到: {i + 1}/{remaining_count} ---")
            save_counter = 0

        await asyncio.sleep(1.5)

    save_progress(list(cards_to_process.values()))
    logging.info("全部處理完成！最終結果已儲存。")
    await client.close()


@client.event
async def on_ready():
    asyncio.create_task(upload_card_images())


if __name__ == "__main__":
    try:
        client.run(BOT_TOKEN)
    except discord.errors.LoginFailure:
        logging.error("登入失敗：Bot Token 不正確或無效。")
    except Exception as e:
        logging.error(f"啟動機器人時發生未預期的錯誤: {e}", exc_info=True)
