"""
被動技能輔助方法

提供被動技能生成的輔助功能，包括變體系統、ID生成等。
"""

import json
import os
import random
import re
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from utils.logger import logger

from .error_handler import error_handler


class PassiveSkillVariantType(Enum):
    """被動技能變體類型枚舉"""

    LARGE_EFFECT = "large_effect"  # 大效果 + 高觸發機率
    SMALL_EFFECT = "small_effect"  # 小效果 + 低觸發機率
    MEDIUM_EFFECT = "medium_effect"  # 中間值


class PassiveSkillHelpers:
    """被動技能輔助方法類"""

    def __init__(self, generator):
        """初始化輔助方法

        Args:
            generator: 被動技能生成器實例
        """
        self.generator = generator
        self.variant_config = self._load_variant_config()
        # 按稀有度分組的可用組合池
        self.available_combinations_by_rarity = {}
        # 初始化組合池
        self._initialize_combination_pools()

    def _load_variant_config(self) -> Dict[str, Any]:
        """加載被動技能變體配置文件"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(
                current_dir, "config", "passive_skill_variant_config.json"
            )

            if not os.path.exists(config_path):
                logger.warning(
                    "被動技能變體配置文件不存在: %s，使用默認配置", config_path
                )
                return self._get_default_variant_config()

            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error("加載被動技能變體配置文件時出錯: %s", e)
            return self._get_default_variant_config()

    def _get_default_variant_config(self) -> Dict[str, Any]:
        """獲取默認被動技能變體配置"""
        return {
            "variant_multipliers": {
                "large_effect": {
                    "effect_multiplier": 1.8,
                    "chance_multiplier": 1.2,
                    "duration_multiplier": 1.3,
                },
                "small_effect": {
                    "effect_multiplier": 0.6,
                    "chance_multiplier": 0.8,
                    "duration_multiplier": 0.8,
                },
                "medium_effect": {
                    "effect_multiplier": 1.0,
                    "chance_multiplier": 1.0,
                    "duration_multiplier": 1.0,
                },
            },
            "combination_variants": {},
        }

    def _initialize_combination_pools(self):
        """初始化每個稀有度的可用組合池"""
        for rarity in range(1, 8):
            self.available_combinations_by_rarity[rarity] = (
                self._generate_all_possible_combinations(rarity)
            )
            logger.info(
                "被動技能稀有度 %s 初始化了 %s 個可能的組合",
                rarity,
                len(self.available_combinations_by_rarity[rarity]),
            )

    def _generate_all_possible_combinations(
        self, rarity: int
    ) -> List[Tuple[str, str, str]]:
        """生成指定稀有度的所有可能組合

        Args:
            rarity: 稀有度等級

        Returns:
            所有可能的 (觸發條件, 效果模板, 變體類型) 列表
        """
        combinations = []

        # 獲取該稀有度可用的效果模板
        try:
            available_templates = self.generator.rarity_manager.get_effect_templates(
                rarity
            )
        except Exception as e:
            logger.error("無法獲取稀有度 %s 的效果模板: %s", rarity, e)
            return combinations

        # 獲取變體類型
        variant_types = [variant.value for variant in PassiveSkillVariantType]

        # 獲取觸發條件
        trigger_types = self.generator.trigger_types

        for trigger_type in trigger_types:
            for template in available_templates:
                for variant in variant_types:
                    combinations.append((trigger_type, template, variant))

        return combinations

    def _select_unused_combination(self, rarity: int) -> Optional[Tuple[str, str, str]]:
        """從可用組合池中選擇一個未使用的組合

        Args:
            rarity: 稀有度等級

        Returns:
            (觸發條件, 效果模板, 變體類型) 或 None
        """
        available_combinations = self.available_combinations_by_rarity.get(rarity, [])

        if not available_combinations:
            logger.warning("被動技能稀有度 %s 沒有可用的組合", rarity)
            return None

        # 隨機選擇一個可用組合
        return random.choice(available_combinations)

    def _mark_combination_used(self, rarity: int, combination: Tuple[str, str, str]):
        """標記組合為已使用

        Args:
            rarity: 稀有度等級
            combination: 組合元組
        """
        if rarity in self.available_combinations_by_rarity:
            try:
                self.available_combinations_by_rarity[rarity].remove(combination)
                logger.debug(
                    "標記被動技能組合為已使用: %s, 剩餘: %s",
                    combination,
                    len(self.available_combinations_by_rarity[rarity]),
                )
            except ValueError:
                logger.warning("嘗試移除不存在的被動技能組合: %s", combination)

    def _reset_combination_pool(self, rarity: int):
        """重置指定稀有度的組合池

        Args:
            rarity: 稀有度等級
        """
        self.available_combinations_by_rarity[rarity] = (
            self._generate_all_possible_combinations(rarity)
        )
        logger.info(
            "重置被動技能稀有度 %s 的組合池，重新生成 %s 個組合",
            rarity,
            len(self.available_combinations_by_rarity[rarity]),
        )

    @error_handler(
        default_return=lambda: "PASSIVE_UNKNOWN_R1_001", log_message="生成技能ID時出錯"
    )
    def generate_descriptive_skill_id(self, skill_config: Dict[str, Any]) -> str:
        """生成描述性的技能ID

        Args:
            skill_config: 技能配置字典

        Returns:
            描述性技能ID
        """
        try:
            # 獲取基本信息
            rarity = skill_config.get("skill_rarity", 1)

            # 獲取主要效果類型
            base_effects = skill_config.get("base_effects", [])
            if base_effects:
                first_effect_block = base_effects[0]
                effect_definitions = first_effect_block.get("effect_definitions", [])
                if effect_definitions:
                    effect_template = effect_definitions[0].get(
                        "effect_template", "UNKNOWN"
                    )
                else:
                    effect_template = "UNKNOWN"

                # 獲取觸發條件
                trigger_condition = first_effect_block.get("trigger_condition", {})
                trigger_type = trigger_condition.get("type", "UNKNOWN")
            else:
                effect_template = "UNKNOWN"
                trigger_type = "UNKNOWN"

            # 簡化效果模板名稱
            effect_short = self._simplify_template_name(effect_template)
            trigger_short = self._simplify_trigger_name(trigger_type)

            # 生成ID
            skill_id = f"PASSIVE_{trigger_short}_{effect_short}_R{rarity}"

            # 清理ID中的特殊字符
            skill_id = re.sub(r"[^A-Z0-9_]", "", skill_id.upper())

            return skill_id

        except Exception as e:
            logger.error("生成技能ID時出錯: %s", e)
            return f"PASSIVE_ERROR_R{skill_config.get('skill_rarity', 1)}_{random.randint(100, 999)}"

    def _simplify_template_name(self, template_name: str) -> str:
        """簡化模板名稱用於ID生成

        Args:
            template_name: 原始模板名稱

        Returns:
            簡化後的模板名稱
        """
        # 移除常見前綴
        simplified = template_name.replace("APPLY_", "").replace("BASIC_", "")

        # 縮短常見詞彙
        replacements = {
            "DAMAGE": "DMG",
            "BOOST": "BST",
            "DEBUFF": "DBF",
            "SHIELD": "SHD",
            "STATUS_EFFECT": "STS",
            "HEAL": "HL",
        }

        for old, new in replacements.items():
            simplified = simplified.replace(old, new)

        return simplified[:8]  # 限制長度

    def _simplify_trigger_name(self, trigger_type: str) -> str:
        """簡化觸發條件名稱用於ID生成

        Args:
            trigger_type: 原始觸發條件類型

        Returns:
            簡化後的觸發條件名稱
        """
        # 移除ON_前綴
        simplified = trigger_type.replace("ON_", "")

        # 縮短常見詞彙
        replacements = {
            "BATTLE_START": "BTLSTR",
            "TURN_START": "TRNSTR",
            "TURN_END": "TRNEND",
            "DAMAGE_DEALT": "DMGDLT",
            "DAMAGE_TAKEN": "DMGTKN",
            "HEAL_RECEIVED": "HLRCV",
            "STATUS_EFFECT_APPLIED": "STSAPP",
            "ALLY_DEATH": "ALYDTH",
            "ENEMY_DEATH": "ENMDTH",
            "HP_THRESHOLD_REACHED": "HPTHR",
        }

        return replacements.get(simplified, simplified[:6])

    @error_handler(
        default_return=lambda rarity=1: {
            "name": f"錯誤被動技能_R{rarity}",
            "description_template": "被動技能生成錯誤",
            "skill_rarity": rarity,
            "max_level": 1,
            "base_effects": [],
            "xp_gain_on_sacrifice": 1,
            "xp_to_next_level_config": {"base_xp": 100, "multiplier": 2},
            "tags": ["ERROR", "PASSIVE"],
        },
        log_message="生成被動技能變體時出錯",
    )
    def generate_passive_skill_with_variants(
        self, template_name: str, effect_category: str, rarity: int, name: Optional[str]
    ) -> Dict[str, Any]:
        """使用組合池系統生成唯一被動技能配置

        Args:
            template_name: 效果模板名稱（可能被重新選擇）
            effect_category: 效果分類
            rarity: 稀有度等級
            name: 技能名稱（如果為None，則自動生成）

        Returns:
            完整的被動技能配置字典
        """
        # 從可用組合池中選擇一個未使用的組合
        combination = self._select_unused_combination(rarity)

        if combination is None:
            # 所有組合都用完了，重置組合池
            self._reset_combination_pool(rarity)
            combination = self._select_unused_combination(rarity)

        if combination is None:
            # 如果還是沒有可用組合，使用回退方案
            logger.error("無法為被動技能稀有度 %s 選擇組合，使用回退方案", rarity)
            return self._generate_fallback_passive_skill(
                template_name, effect_category, rarity, name
            )

        # 解析組合
        selected_trigger_type, selected_template, variant_type_str = combination
        variant_type = PassiveSkillVariantType(variant_type_str)

        # 重新計算效果分類（基於實際選擇的模板）
        actual_effect_category = self.generator.get_template_category(selected_template)

        # 如果沒有傳入名稱，根據最終確定的模板和變體類型生成名稱
        if name is None:
            name = self.generator._generate_name_by_template(
                selected_template, rarity, variant_type.value
            )

        # 生成帶變體調整的觸發條件
        trigger_condition = self._create_variant_trigger_condition(
            selected_trigger_type, rarity, variant_type
        )

        # 創建帶變體調整的效果定義
        effect_definition = self._create_variant_effect_definition(
            selected_template, actual_effect_category, rarity, variant_type
        )

        # 創建被動效果塊
        passive_effect_block = {
            "trigger_condition": trigger_condition,
            "effect_definitions": [effect_definition],
        }

        # 根據稀有度決定是否添加目標覆蓋
        if rarity >= 4 and random.random() < 0.3:  # 高稀有度30%機率有目標覆蓋
            target_override = self._generate_target_override(actual_effect_category)
            if target_override:
                passive_effect_block["target_override"] = target_override

        # 生成描述
        description = self._generate_passive_description(
            trigger_condition, effect_definition, rarity
        )

        # 計算等級和XP配置
        max_level = self._calculate_max_level(rarity)
        xp_config = self._generate_xp_config(rarity)

        # 生成標籤
        tags = self._generate_tags(trigger_condition, effect_definition, rarity)

        # 標記組合為已使用
        self._mark_combination_used(rarity, combination)

        return {
            "name": name,
            "description_template": description,
            "skill_rarity": rarity,
            "max_level": max_level,
            "base_effects": [passive_effect_block],
            "xp_gain_on_sacrifice": self._calculate_xp_gain(rarity),
            "xp_to_next_level_config": xp_config,
            "tags": tags,
        }

    def _generate_target_override(
        self, effect_category: str
    ) -> Optional[Dict[str, Any]]:
        """生成目標覆蓋配置

        Args:
            effect_category: 效果分類

        Returns:
            目標覆蓋配置或None
        """
        if effect_category in ["stat_boost", "heal", "shield"]:
            # 增益效果通常針對友方
            return {
                "selector_type": "ALL_ALLIES",
                "max_targets": random.choice([2, 3, 4]),
            }
        elif effect_category in ["status_application", "damage"]:
            # 負面效果通常針對敵方
            return {
                "selector_type": "ALL_ENEMIES",
                "max_targets": random.choice([1, 2, 3]),
            }
        return None

    def _determine_variant_type(self, rarity: int) -> str:
        """根據稀有度確定變體類型

        Args:
            rarity: 稀有度等級

        Returns:
            變體類型字符串
        """
        if rarity <= 2:
            return "small_effect"
        elif rarity <= 5:
            return "medium_effect"
        else:
            return "large_effect"

    def _generate_passive_description(
        self,
        trigger_condition: Dict[str, Any],
        effect_definition: Dict[str, Any],
        rarity: int,
    ) -> str:
        """生成被動技能描述

        Args:
            trigger_condition: 觸發條件
            effect_definition: 效果定義
            rarity: 稀有度等級

        Returns:
            技能描述模板
        """
        # 獲取觸發描述
        trigger_type = trigger_condition.get("type", "")
        trigger_desc = self._get_trigger_description(trigger_type, trigger_condition)

        # 獲取效果描述
        effect_template = effect_definition.get("effect_template", "")
        effect_desc = self._get_effect_description(effect_template, effect_definition)

        # 組合描述
        if trigger_desc and effect_desc:
            return f"{trigger_desc}時，{effect_desc}"
        else:
            return f"被動技能效果：{effect_desc or '未知效果'}"

    def _get_trigger_description(
        self, trigger_type: str, trigger_condition: Dict[str, Any]
    ) -> str:
        """獲取觸發條件描述（1v1戰鬥適用）

        Args:
            trigger_type: 觸發類型
            trigger_condition: 觸發條件配置

        Returns:
            觸發條件描述
        """
        descriptions = {
            "ON_BATTLE_START": "戰鬥開始",
            "ON_TURN_START": "回合開始",
            "ON_TURN_END": "回合結束",
            "ON_DAMAGE_DEALT": "造成傷害",
            "ON_DAMAGE_TAKEN": "受到傷害",
            "ON_HEAL_RECEIVED": "受到治療",
            "ON_STATUS_EFFECT_APPLIED": "狀態效果生效",
            "ON_HP_THRESHOLD_REACHED": "生命值低於閾值",
        }

        base_desc = descriptions.get(trigger_type, "特定條件")

        # 添加子類型描述
        sub_type = trigger_condition.get("sub_type")
        if sub_type:
            if sub_type == "PHYSICAL":
                base_desc += "（物理）"
            elif sub_type == "MAGICAL":
                base_desc += "（魔法）"

        return base_desc

    def _get_effect_description(
        self, effect_template: str, effect_definition: Dict[str, Any]
    ) -> str:
        """獲取效果描述，使用和主動技能一樣的描述系統

        Args:
            effect_template: 效果模板名稱
            effect_definition: 效果定義

        Returns:
            效果描述
        """
        # 使用主動技能的描述生成器來獲取準確的效果描述
        try:
            # 使用描述生成器的次要描述功能
            secondary_desc = (
                self.generator.description_generator.generate_secondary_description(
                    effect_template
                )
            )

            if secondary_desc and secondary_desc != "產生特殊效果":
                return secondary_desc

        except Exception as e:
            logger.debug("使用描述生成器失敗: %s", e)

        # 回退到基於模板名稱的描述映射
        return self._get_fallback_effect_description(effect_template)

    def _get_fallback_effect_description(self, effect_template: str) -> str:
        """回退的效果描述方法，基於模板名稱映射

        Args:
            effect_template: 效果模板名稱

        Returns:
            效果描述
        """
        # 詳細的模板名稱映射
        template_descriptions = {
            # 傷害類
            "BASIC_DAMAGE": "造成傷害",
            "TRUE_DAMAGE": "造成真實傷害",
            "MULTI_HIT_DAMAGE": "進行多段攻擊",
            "DRAIN_DAMAGE": "造成傷害並吸血",
            "CONDITIONAL_DAMAGE_BOOST": "在特定條件下造成額外傷害",
            "CRITICAL_MASS_EXPLOSION": "集中攻擊力爆炸",
            "RESOURCE_ADVANTAGE_BLAST": "利用資源優勢攻擊",
            "EXECUTE_DAMAGE": "對低血量敵人造成斬殺傷害",
            "PERCENTAGE_HP_DAMAGE": "造成百分比生命值傷害",
            "VITAL_POINT_EXPLOIT": "針對弱點攻擊",
            # 增益類
            "APPLY_ATK_BOOST": "提升攻擊力",
            "APPLY_DEF_BOOST": "提升防禦力",
            "APPLY_SPD_BOOST": "提升速度",
            "APPLY_HP_BOOST": "提升最大生命值",
            "APPLY_MP_BOOST": "提升最大法力值",
            "APPLY_CRIT_BOOST": "提升暴擊能力",
            "APPLY_ACCURACY_BOOST": "提升命中率",
            "APPLY_EVASION_BOOST": "提升閃避率",
            "APPLY_ALL_STATS_BOOST": "提升全屬性",
            # 減益類
            "APPLY_ATK_DEBUFF": "降低敵方攻擊力",
            "APPLY_DEF_DEBUFF": "降低敵方防禦力",
            "APPLY_SPD_DEBUFF": "降低敵方速度",
            "APPLY_ACCURACY_DEBUFF": "降低敵方命中率",
            "APPLY_EVASION_DEBUFF": "降低敵方閃避率",
            "APPLY_HP_DEBUFF": "降低敵方最大生命值",
            "APPLY_MP_DEBUFF": "降低敵方最大法力值",
            "APPLY_CRIT_DEBUFF": "降低敵方暴擊能力",
            "APPLY_ALL_STATS_DEBUFF": "降低敵方全屬性",
            # 狀態效果類
            "APPLY_POISON_STATUS": "使敵方中毒",
            "APPLY_BURN_STATUS": "使敵方燃燒",
            "APPLY_FREEZE_STATUS": "冰凍敵方",
            "APPLY_STUN_STATUS": "擊暈敵方",
            "APPLY_SILENCE_STATUS": "沉默敵方",
            "APPLY_REGEN_STATUS_WEAK": "持續回復生命",
            "APPLY_INVINCIBLE_STATUS_SHORT": "短時間無敵",
            "APPLY_WEAK_LIFESTEAL": "獲得微弱吸血效果",
            # 護盾類
            "APPLY_BASIC_SHIELD": "獲得基礎護盾",
            "APPLY_MAGIC_SHIELD": "獲得魔法護盾",
            "APPLY_PHYSICAL_BARRIER": "獲得物理屏障",
            "APPLY_SCALING_SHIELD": "獲得適應護盾",
            # 治療類
            "BASIC_HEAL_FLAT": "恢復固定生命值",
            "BASIC_HEAL_PERCENT_CASTER_MATK": "恢復基於魔攻的生命值",
            "BASIC_HEAL_PERCENT_MAX_HP": "恢復百分比最大生命值",
            "MISSING_HP_HEAL": "治癒已損失生命值",
            # 特殊類
            "DISPEL_ALL_DEBUFFS": "驅散負面效果",
            "SHIELD_REMOVAL": "移除敵方護盾",
            "TRANSFER_MP_EFFECT": "轉移法力值",
            "LOSE_MP_EFFECT": "燃燒敵方法力值",
            "DUELIST_FOCUS": "進入決鬥者專注狀態",
            "BERSERKER_TRADE": "進入狂戰士狀態",
            "MOMENTUM_SHIFT": "根據戰況獲得增益",
            "REFLECT_DAMAGE": "反射傷害",
        }

        # 直接查找模板描述
        if effect_template in template_descriptions:
            return template_descriptions[effect_template]

        # 基於關鍵詞的回退邏輯
        template_upper = effect_template.upper()

        if "ATK" in template_upper and "BOOST" in template_upper:
            return "提升攻擊力"
        elif "DEF" in template_upper and "BOOST" in template_upper:
            return "提升防禦力"
        elif "SPD" in template_upper and "BOOST" in template_upper:
            return "提升速度"
        elif "HP" in template_upper and "BOOST" in template_upper:
            return "提升最大生命值"
        elif "MP" in template_upper and "BOOST" in template_upper:
            return "提升最大法力值"
        elif "CRIT" in template_upper and "BOOST" in template_upper:
            return "提升暴擊能力"
        elif "ACCURACY" in template_upper and "BOOST" in template_upper:
            return "提升命中率"
        elif "EVASION" in template_upper and "BOOST" in template_upper:
            return "提升閃避率"
        elif "DEBUFF" in template_upper:
            return "降低敵方屬性"
        elif "HEAL" in template_upper:
            return "恢復生命值"
        elif "SHIELD" in template_upper:
            return "獲得護盾"
        elif "DAMAGE" in template_upper:
            return "造成傷害"
        elif "POISON" in template_upper:
            return "使敵方中毒"
        elif "BURN" in template_upper:
            return "使敵方燃燒"
        elif "FREEZE" in template_upper:
            return "冰凍敵方"
        elif "STUN" in template_upper:
            return "擊暈敵方"
        elif "SILENCE" in template_upper:
            return "沉默敵方"
        else:
            return "觸發特殊效果"

    def _calculate_max_level(self, rarity: int) -> int:
        """計算最大等級

        Args:
            rarity: 稀有度等級

        Returns:
            最大等級
        """
        base_level = 5 + rarity * 2
        return min(20, base_level)

    def _generate_xp_config(self, rarity: int) -> Dict[str, Any]:
        """生成XP配置

        Args:
            rarity: 稀有度等級

        Returns:
            XP配置字典
        """
        base_xp = 40 + rarity * 15
        multiplier = 1.15 + rarity * 0.05

        return {"base_xp": base_xp, "multiplier": round(multiplier, 2)}

    def _calculate_xp_gain(self, rarity: int) -> int:
        """計算犧牲獲得的XP

        Args:
            rarity: 稀有度等級

        Returns:
            XP獲得量
        """
        return 8 + rarity * 3

    def _generate_tags(
        self,
        trigger_condition: Dict[str, Any],
        effect_definition: Dict[str, Any],
        rarity: int,
    ) -> List[str]:
        """生成技能標籤

        Args:
            trigger_condition: 觸發條件
            effect_definition: 效果定義
            rarity: 稀有度等級

        Returns:
            標籤列表
        """
        tags = ["PASSIVE"]

        # 根據觸發條件添加標籤
        trigger_type = trigger_condition.get("type", "")
        if "DAMAGE" in trigger_type:
            tags.append("COMBAT")
        elif "TURN" in trigger_type:
            tags.append("TURN_BASED")
        elif "BATTLE_START" in trigger_type:
            tags.append("BATTLE_START")

        # 根據效果添加標籤
        effect_template = effect_definition.get("effect_template", "")
        if "BOOST" in effect_template:
            tags.append("BUFF")
        elif "DEBUFF" in effect_template:
            tags.append("DEBUFF")
        elif "HEAL" in effect_template:
            tags.append("HEAL")
        elif "SHIELD" in effect_template:
            tags.append("SHIELD")
        elif "DAMAGE" in effect_template:
            tags.append("DAMAGE")

        # 根據稀有度添加標籤
        if rarity >= 6:
            tags.append("LEGENDARY")
        elif rarity >= 4:
            tags.append("RARE")

        return tags

    def _generate_fallback_passive_skill(
        self, template_name: str, effect_category: str, rarity: int, name: Optional[str]
    ) -> Dict[str, Any]:
        """生成回退被動技能（當組合池系統失敗時使用）

        Args:
            template_name: 效果模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            name: 技能名稱

        Returns:
            被動技能配置字典
        """
        logger.warning("使用回退方案生成被動技能: %s", name)

        # 使用隨機變體和觸發條件
        variant_type = random.choice(list(PassiveSkillVariantType))
        trigger_type = random.choice(self.generator.trigger_types)

        # 生成技能名稱
        if name is None:
            name = self.generator._generate_name_by_template(
                template_name, rarity, variant_type.value
            )

        # 生成觸發條件和效果定義
        trigger_condition = self._create_variant_trigger_condition(
            trigger_type, rarity, variant_type
        )
        effect_definition = self._create_variant_effect_definition(
            template_name, effect_category, rarity, variant_type
        )

        # 創建被動效果塊
        passive_effect_block = {
            "trigger_condition": trigger_condition,
            "effect_definitions": [effect_definition],
        }

        # 生成描述
        description = self._generate_passive_description(
            trigger_condition, effect_definition, rarity
        )

        return {
            "name": name,
            "description_template": description,
            "skill_rarity": rarity,
            "max_level": self._calculate_max_level(rarity),
            "base_effects": [passive_effect_block],
            "xp_gain_on_sacrifice": self._calculate_xp_gain(rarity),
            "xp_to_next_level_config": self._generate_xp_config(rarity),
            "tags": self._generate_tags(trigger_condition, effect_definition, rarity),
        }

    def _create_variant_trigger_condition(
        self, trigger_type: str, rarity: int, variant_type: PassiveSkillVariantType
    ) -> Dict[str, Any]:
        """創建帶變體調整的觸發條件

        Args:
            trigger_type: 觸發條件類型
            rarity: 稀有度等級
            variant_type: 變體類型

        Returns:
            觸發條件字典
        """
        # 獲取變體倍率
        variant_multipliers = self.variant_config["variant_multipliers"][
            variant_type.value
        ]
        chance_multiplier = variant_multipliers["chance_multiplier"]

        # 基礎觸發條件
        trigger_condition: Dict[str, Any] = {
            "type": trigger_type,
            "chance_formula": "1.0",
        }

        # 根據觸發類型添加特殊參數
        if trigger_type == "ON_HP_THRESHOLD_REACHED":
            # HP閾值觸發
            threshold_percent = random.choice([0.25, 0.5, 0.75])
            trigger_condition["params"] = {
                "threshold_percent_formula": str(threshold_percent),
                "check_direction": "below",
            }
            trigger_condition["trigger_once_per_battle"] = True

        elif trigger_type in ["ON_DAMAGE_DEALT", "ON_DAMAGE_TAKEN"]:
            # 傷害相關觸發，可能有子類型
            if random.random() < 0.3:  # 30% 機率有子類型
                damage_types = ["PHYSICAL", "MAGICAL"]
                trigger_condition["sub_type"] = random.choice(damage_types)

        elif trigger_type == "ON_STATUS_EFFECT_APPLIED":
            # 狀態效果觸發，調整觸發機率
            base_chance = 0.3 + (rarity - 1) * 0.1
            adjusted_chance = min(1.0, base_chance * chance_multiplier)
            trigger_condition["chance_formula"] = f"{adjusted_chance:.2f}"

        # 根據稀有度和變體調整觸發機率
        if trigger_type not in ["ON_BATTLE_START", "ON_HP_THRESHOLD_REACHED"]:
            if random.random() < 0.2:  # 20% 機率有機率限制
                base_chance = 0.5 + (rarity - 1) * 0.1
                adjusted_chance = min(1.0, base_chance * chance_multiplier)
                trigger_condition["chance_formula"] = f"{adjusted_chance:.2f}"

        return trigger_condition

    def _create_variant_effect_definition(
        self,
        template_name: str,
        effect_category: str,
        rarity: int,
        variant_type: PassiveSkillVariantType,
    ) -> Dict[str, Any]:
        """創建帶變體調整的效果定義

        Args:
            template_name: 效果模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            variant_type: 變體類型

        Returns:
            效果定義字典
        """
        # 獲取變體倍率
        variant_multipliers = self.variant_config["variant_multipliers"][
            variant_type.value
        ]
        effect_multiplier = variant_multipliers["effect_multiplier"]

        # 直接創建包含變體倍率的最終效果定義
        final_effect_def = self.generator.create_effect_definition(
            template_name, effect_category, rarity, effect_multiplier
        )

        return final_effect_def
