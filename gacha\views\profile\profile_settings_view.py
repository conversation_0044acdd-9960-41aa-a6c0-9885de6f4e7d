"""
Profile Settings View - 處理檔案設定相關的 Discord UI 互動
"""

from typing import Optional

import discord
from discord import ui

from gacha.exceptions import (
    BusinessError,
)
from gacha.repositories.profile import profile_repository
from gacha.services import profile_service
from utils.base_modal import BaseModal
from utils.base_view import BaseView, BotType
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class SetFrameOffsetModal(BaseModal):
    """設定動畫卡片預覽影格的模態框"""

    offset_input = ui.TextInput(
        label="影格編號",
        placeholder="例如: 10 (代表第 10 影格)。留空則重置。",
        required=False,
        max_length=10,
    )

    def __init__(self, view: "ProfileSettingsView", slot: str):
        self.view = view
        self.slot = slot
        slot_name_map = {
            "main": "主卡片",
            "sub_1": "副卡片 1",
            "sub_2": "副卡片 2",
            "sub_3": "副卡片 3",
            "sub_4": "副卡片 4",
        }
        title = f"設定 {slot_name_map.get(self.slot, '未知卡槽')} 的預覽影格"
        super().__init__(bot=view.bot, title=title)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=False)

        input_value = self.offset_input.value.strip()
        frame_number: Optional[int] = None

        try:
            if input_value:
                frame_number = int(input_value)
        except ValueError as e:
            raise BusinessError("❌ 無效的格式。請輸入一個整數 (例如 10)。") from e

        if frame_number is not None and frame_number < 0:
            raise BusinessError("❌ 影格編號不能為負數。")

        await profile_repository.update_frame_offset(
            self.view.user_id, self.slot, frame_number
        )
        await profile_service.invalidate_profile_cache(self.view.user_id)
        await self.view.update_settings_embed(interaction)

        success_message = (
            f"已重置卡槽 `{self.slot}` 的預覽影格設定。"
            if frame_number is None
            else f"已設定卡槽 `{self.slot}` 的預覽為第 {frame_number} 影格。"
        )
        embed = SuccessEmbed(description=success_message)
        await interaction.followup.send(embed=embed, ephemeral=True)


class CardSlotSelect(ui.Select):
    """選擇卡槽的下拉選單"""

    def __init__(self, view: "ProfileSettingsView"):
        self.settings_view = view
        options = [
            discord.SelectOption(
                label="主展示卡片", value="main", description="設定主卡片的預覽影格"
            ),
            discord.SelectOption(label="副卡片 1", value="sub_1"),
            discord.SelectOption(label="副卡片 2", value="sub_2"),
            discord.SelectOption(label="副卡片 3", value="sub_3"),
            discord.SelectOption(label="副卡片 4", value="sub_4"),
        ]
        super().__init__(
            placeholder="請選擇要設定預覽影格的卡槽...",
            min_values=1,
            max_values=1,
            options=options,
        )

    async def callback(self, interaction: discord.Interaction):
        selected_slot = self.values[0]
        modal = SetFrameOffsetModal(self.settings_view, selected_slot)
        await interaction.response.send_modal(modal)


class CardSlotSelectView(BaseView):
    """包含卡槽選擇下拉選單的視圖"""

    def __init__(
        self, bot: BotType, user_id: int, settings_view: "ProfileSettingsView"
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=60)
        self.add_item(CardSlotSelect(settings_view))


class SetMainCardModal(BaseModal):
    """設定主展示卡片的模態框"""

    card_id = ui.TextInput(
        label="卡片ID",
        placeholder="請輸入您擁有的卡片ID",
        required=True,
        min_length=1,
        max_length=20,
    )

    def __init__(self, view: "ProfileSettingsView"):
        super().__init__(bot=view.bot, title="設置主卡片")
        self.view = view

    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)

        try:
            card_id = int(self.card_id.value.strip())
        except ValueError as e:
            raise BusinessError("❌ 卡片ID必須是有效的數字。") from e

        await profile_service.set_main_card(self.view.user_id, card_id)
        await profile_service.invalidate_profile_cache(self.view.user_id)
        await self.view.update_settings_embed(interaction)


class SetSubCardsModal(BaseModal):
    """設定副展示卡片的模態框，使用多個獨立輸入框"""

    sub_card1 = ui.TextInput(
        label="副卡片槽位 1", placeholder="請輸入卡片ID", required=False, max_length=20
    )

    sub_card2 = ui.TextInput(
        label="副卡片槽位 2", placeholder="請輸入卡片ID", required=False, max_length=20
    )

    sub_card3 = ui.TextInput(
        label="副卡片槽位 3", placeholder="請輸入卡片ID", required=False, max_length=20
    )

    sub_card4 = ui.TextInput(
        label="副卡片槽位 4", placeholder="請輸入卡片ID", required=False, max_length=20
    )

    def __init__(self, view: "ProfileSettingsView"):
        super().__init__(bot=view.bot, title="設置副卡片")
        self.view = view

    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)

        raw_inputs = [
            self.sub_card1.value.strip(),
            self.sub_card2.value.strip(),
            self.sub_card3.value.strip(),
            self.sub_card4.value.strip(),
        ]

        parsed_card_ids_for_setting: dict[int, int] = {}
        all_entered_card_ids_for_validation = []

        for i, card_id_str in enumerate(raw_inputs):
            slot_number = i + 1
            if card_id_str:
                try:
                    card_id = int(card_id_str)
                    parsed_card_ids_for_setting[slot_number] = card_id
                    all_entered_card_ids_for_validation.append(card_id)
                except ValueError as e:
                    raise BusinessError(
                        f"❌ 副卡片槽位 {slot_number} 的ID '{card_id_str}' 不是有效的數字。"
                    ) from e

        if not parsed_card_ids_for_setting:
            return

        if len(all_entered_card_ids_for_validation) != len(
            set(all_entered_card_ids_for_validation)
        ):
            raise BusinessError("❌ 您輸入的副展示卡片ID中存在重複。")

        profile_data = await profile_service.get_profile_data(
            self.view.user_id,
            interaction.user.display_name,
            str(interaction.user.avatar.url) if interaction.user.avatar else None,
        )
        main_card_id = (
            profile_data.main_card.card_id if profile_data.main_card else None
        )

        if main_card_id:
            for slot, sub_card_id in parsed_card_ids_for_setting.items():
                if sub_card_id == main_card_id:
                    raise BusinessError(
                        f"❌ 卡片ID {main_card_id} (在副卡槽位 {slot}) 已被設定為主展示卡，不能同時設定為副展示卡。"
                    )

        for slot, card_id in parsed_card_ids_for_setting.items():
            await profile_service.set_sub_card(self.view.user_id, slot, card_id)

        await profile_service.invalidate_profile_cache(self.view.user_id)
        await self.view.update_settings_embed(interaction)


class ClearSubCardSlotModal(BaseModal):
    """清除副展示卡片槽位的模態框"""

    slot_number = ui.TextInput(
        label="要清除的副卡片位置 (1-4)",
        placeholder="請輸入1到4的數字",
        required=True,
        min_length=1,
        max_length=1,
    )

    def __init__(self, view: "ProfileSettingsView"):
        super().__init__(bot=view.bot, title="清除副卡片")
        self.view = view

    async def on_submit(self, interaction: discord.Interaction) -> None:
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)

        slot_to_clear_str = self.slot_number.value.strip()
        if not slot_to_clear_str:
            raise BusinessError(
                "🤔 您沒有輸入任何內容。請輸入要清除的副卡片位置 (1-4)。"
            )

        try:
            slot_to_clear = int(slot_to_clear_str)
            if not 1 <= slot_to_clear <= 4:
                raise BusinessError("❌ 副卡片位置必須是 1 到 4 之間的數字。")
        except ValueError as e:
            raise BusinessError("❌ 副卡片位置必須是有效的數字。") from e

        # 讓 service 層處理業務邏輯和拋出錯誤
        await profile_service.clear_sub_card(self.view.user_id, slot_to_clear)
        await profile_service.invalidate_profile_cache(self.view.user_id)
        await self.view.update_settings_embed(interaction)
        embed = SuccessEmbed(description=f"已成功清除副卡片位置 {slot_to_clear}。")
        await interaction.followup.send(embed=embed, ephemeral=True)


class SetStatusModal(BaseModal):
    """設定個性簽名的模態框"""

    status_text = ui.TextInput(
        label="個性簽名 (最多150字)",
        placeholder="請輸入您的個性簽名...",
        required=False,
        max_length=150,
        style=discord.TextStyle.paragraph,
    )

    def __init__(self, view: "ProfileSettingsView"):
        super().__init__(bot=view.bot, title="設定個性簽名")
        self.view = view

    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)

        status_text = self.status_text.value

        # 驗證字符長度
        if len(status_text) > 150:
            raise BusinessError("個性簽名不能超過150個字符。")

        # 調用設定個性簽名的服務，讓錯誤冒泡
        await profile_service.set_user_status(self.view.user_id, status_text)

        # 設定成功，清除緩存
        await profile_service.invalidate_profile_cache(self.view.user_id)

        # 更新原始嵌入消息
        await self.view.update_settings_embed(interaction)


class ProfileSettingsView(BaseView):
    """個人資料設定視圖"""

    def __init__(self, bot: BotType, user_id: int):
        super().__init__(bot=bot, user_id=user_id, timeout=180)
        self.message: Optional[discord.Message] = None

    @ui.button(label="🎴 設置主卡片", style=discord.ButtonStyle.primary, row=0)
    async def set_main_card_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """打開設定主展示卡片的模態框"""
        modal = SetMainCardModal(self)
        await interaction.response.send_modal(modal)

    @ui.button(label="🃏 設置副卡片", style=discord.ButtonStyle.primary, row=0)
    async def set_sub_cards_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """打開設定副展示卡片的模態框"""
        modal = SetSubCardsModal(self)
        await interaction.response.send_modal(modal)

    @ui.button(label="🗑️ 清除副卡片", style=discord.ButtonStyle.secondary, row=0)
    async def clear_sub_card_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """打開清除副展示卡片的模態框"""
        modal = ClearSubCardSlotModal(self)
        await interaction.response.send_modal(modal)

    @ui.button(label="📝 設定個性簽名", style=discord.ButtonStyle.primary, row=1)
    async def set_status_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """打開設定個性簽名的模態框"""
        modal = SetStatusModal(self)
        await interaction.response.send_modal(modal)

    @ui.button(label="🖼️ 設定預覽影格", style=discord.ButtonStyle.secondary, row=1)
    async def set_frame_offset_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """發送一個帶有卡槽選擇下拉選單的消息"""
        # defer() 確保交互不會超時
        await interaction.response.defer(ephemeral=True, thinking=False)

        # 創建包含下拉選單的視圖
        select_view = CardSlotSelectView(
            bot=self.bot, user_id=self.user_id, settings_view=self
        )

        # 發送一個新的 followup 消息，因為原始消息是嵌入，我們不能直接在上面添加組件
        await interaction.followup.send(
            "請從下方選擇您想為哪個卡槽設定動畫的預覽影格：",
            view=select_view,
            ephemeral=True,
        )

    @ui.button(label="🖼️ 重置背景圖片", style=discord.ButtonStyle.secondary, row=1)
    async def reset_background_button(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """處理重置背景圖片的邏輯"""
        await interaction.response.defer(ephemeral=True)

        # 調用重置背景的服務，讓錯誤冒泡
        await profile_service.reset_background(self.user_id)

        # 重置成功，清除緩存
        await profile_service.invalidate_profile_cache(self.user_id)

        # 更新原始嵌入消息
        await self.update_settings_embed(interaction)

    async def update_settings_embed(self, interaction: discord.Interaction):
        """更新設定嵌入消息。錯誤將由 BaseView.on_error 處理。"""
        if not self.message:
            logger.warning("ProfileSettingsView message not set, cannot update embed.")
            raise BusinessError(
                "❌ 更新設定顯示時發生內部錯誤 (無法找到原始訊息)，但您的更改可能已保存。"
            )

        new_embed = await self.create_settings_embed()
        await self.message.edit(embed=new_embed, view=self)

    async def create_settings_embed(self) -> discord.Embed:
        """創建顯示當前設定信息的嵌入消息。錯誤將由 BaseView.on_error 處理。"""
        embed = discord.Embed(
            title="📝 個人檔案設定",
            description="以下是您當前的個人檔案設定情況，您可以通過下方按鈕進行修改。",
            color=discord.Color.blue(),
        )

        settings_data = await profile_service.get_profile_settings_view_data(
            self.user_id
        )

        profile_settings = settings_data["profile_settings"]
        main_card = settings_data["main_card"]
        sub_cards_dict = settings_data["sub_cards"]

        # 主卡信息
        main_card_value = "未設定"
        if main_card:
            main_card_value = f"**{main_card['name']}** (ID: {main_card['card_id']})"
            if profile_settings.showcased_card_frame_number is not None:
                main_card_value += f"\n*預覽影格: 第 {profile_settings.showcased_card_frame_number} 影格*"
        embed.add_field(name="🎴 主展示卡片", value=main_card_value, inline=False)

        # 副卡信息
        sub_cards_lines = []
        for i in range(1, 5):
            card = sub_cards_dict.get(i)
            frame_number = getattr(profile_settings, f"sub_card_{i}_frame_number", None)
            line = f"槽位 {i}: "
            if card:
                line += f"**{card['name']}** (ID: {card['card_id']})"
                if frame_number is not None:
                    line += f" (🖼️ 第 {frame_number} 影格)"
            elif frame_number is not None:
                line += f"未設定卡片 (🖼️ 第 {frame_number} 影格)"
            else:
                line += "未設定"
            sub_cards_lines.append(line)
        sub_cards_value = "\n".join(sub_cards_lines)
        embed.add_field(name="🃏 副展示卡片", value=sub_cards_value, inline=False)

        # 背景信息
        background_url = settings_data.get("background_image_url")
        background_value = (
            "已設定自定義背景"
            if background_url and background_url != "default"
            else "使用預設背景"
        )
        embed.add_field(name="🖼️ 背景圖片", value=background_value, inline=False)

        # 個性簽名
        status = settings_data.get("user_status")
        status_value = f"「{status}」" if status else "未設定"
        embed.add_field(name="📝 個性簽名", value=status_value, inline=False)

        # 點讚數
        like_count_value = settings_data.get("like_count", 0)
        embed.add_field(name="❤️ 收到的點讚", value=str(like_count_value), inline=True)

        embed.set_footer(
            text="使用下方按鈕修改設定 • 設定完成後可使用 /profile 查看效果"
        )
        return embed
