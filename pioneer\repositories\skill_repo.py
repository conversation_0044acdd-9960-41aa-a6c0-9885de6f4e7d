"""
Pioneer System - Skill Repository
技能相關的資料庫存取
"""

from typing import List, Optional

import asyncpg

from gacha.repositories._base_repo import execute_query, fetch_all, fetch_one
from pioneer.exceptions import PioneerDatabaseError, SkillNotFoundError
from pioneer.models.pioneer_models import PioneerSkill
from utils.logger import logger

# ========================================
# 技能相關
# ========================================


async def get_user_skill(
    user_id: int, skill_id: str, connection: Optional[asyncpg.Connection] = None
) -> PioneerSkill:
    """獲取用戶技能"""
    query = "SELECT * FROM pioneer_skills WHERE user_id = $1 AND skill_id = $2"
    result = await fetch_one(query, (user_id, skill_id), connection=connection)

    if not result:
        raise SkillNotFoundError(user_id, skill_id)

    return PioneerSkill(
        id=result["id"],
        user_id=result["user_id"],
        skill_id=result["skill_id"],
        level=result["level"],
        xp=result["xp"],
    )


async def add_skill_xp(
    user_id: int,
    skill_id: str,
    xp_amount: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """增加技能經驗並處理升級"""
    # 技能已在需求檢查階段確保存在
    skill = await get_user_skill(user_id, skill_id, connection)

    new_xp = skill.xp + xp_amount
    new_level = skill.level

    # 計算新等級（每級需要 level * 100 經驗）
    while new_xp >= new_level * 100:
        new_xp -= new_level * 100
        new_level += 1

    query = """
        UPDATE pioneer_skills 
        SET level = $3, xp = $4
        WHERE user_id = $1 AND skill_id = $2
    """
    try:
        await execute_query(
            query, (user_id, skill_id, new_level, new_xp), connection=connection
        )

        # 觸發任務更新
        from pioneer.services.task_updater import task_updater

        # 1. 觸發 skill_xp 事件
        await task_updater.check_and_update_tasks(
            user_id,
            "skill_xp",
            skill_id=skill_id,
            amount=xp_amount,
            connection=connection,
        )
        # 2. 如果升級，觸發 skill_level 事件
        if new_level > skill.level:
            await task_updater.check_and_update_tasks(
                user_id,
                "skill_level",
                skill_id=skill_id,
                level=new_level,
                connection=connection,
            )

        return True
    except Exception as e:
        logger.error("增加技能經驗失敗 user_id=%s, skill_id=%s: %s", user_id, skill_id, e)
        raise PioneerDatabaseError("add_skill_xp", str(e), e) from e


async def get_user_skills(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[PioneerSkill]:
    """獲取用戶所有技能"""
    query = (
        "SELECT * FROM pioneer_skills WHERE user_id = $1 ORDER BY level DESC, xp DESC"
    )
    results = await fetch_all(query, (user_id,), connection=connection)

    return [
        PioneerSkill(
            id=row["id"],
            user_id=row["user_id"],
            skill_id=row["skill_id"],
            level=row["level"],
            xp=row["xp"],
        )
        for row in results
    ]
