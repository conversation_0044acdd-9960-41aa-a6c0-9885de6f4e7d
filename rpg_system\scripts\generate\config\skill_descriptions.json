{"damage": {"TRUE_DAMAGE": {"main_description": "{prefix}釋放{name}，造成無視防禦的 {power} 真實傷害", "secondary_description": "造成真實傷害"}, "EXECUTE_DAMAGE": {"main_description": "{prefix}使用{name}，對低血量敵人造成 {power_doubled} 斬殺傷害", "secondary_description": "對低血量敵人造成斬殺傷害"}, "PERCENTAGE_HP_DAMAGE": {"main_description": "{prefix}發動{name}，造成敵方最大生命值 15% 的真實傷害", "secondary_description": "造成百分比生命值傷害"}, "BASIC_DAMAGE": {"main_description": "{prefix}施展{name}，造成 {power} 傷害", "secondary_description": "造成傷害"}, "PRECISION_STRIKE_1V1": {"main_description": "{prefix}發動{name}，精準地攻擊單個敵人，造成 {power} 物理傷害，命中率越高傷害越高", "secondary_description": "進行精準攻擊"}, "RIPOSTING_STANCE": {"main_description": "{prefix}採取{name}，若上回合受到攻擊，則反擊造成 {power} 物理傷害", "secondary_description": "進行反擊攻擊"}, "SOUL_SIPHON": {"main_description": "{prefix}施展{name}，吸取目標靈魂，造成 {power} 魔法傷害，自身魔攻越高吸取效果越強", "secondary_description": "吸取目標靈魂"}, "VITAL_POINT_EXPLOIT": {"main_description": "{prefix}使出{name}，針對目標弱點進行攻擊，對帶有特定負面狀態的敵人造成額外 {power} 物理傷害", "secondary_description": "針對弱點攻擊"}, "MANA_BURN_STRIKE": {"main_description": "{prefix}釋放{name}，對目標造成 {power} 魔法傷害，並燃燒其法力值", "secondary_description": "造成傷害並燃燒法力"}, "RESOURCE_VAMPIRE": {"main_description": "{prefix}使用{name}，對目標造成少量真實傷害並汲取其部分法力值為己用", "secondary_description": "汲取法力值"}, "OPPORTUNISTIC_STRIKE": {"main_description": "{prefix}抓住{name}的機會，對剛釋放大招或受特定減益影響的敵人造成 {power} 物理傷害", "secondary_description": "抓住機會攻擊"}, "COMPOUNDING_STRIKE": {"main_description": "{prefix}發動{name}，連續攻擊同一目標，每次命中傷害都會提升，總計造成 {power} 物理傷害", "secondary_description": "進行連續攻擊"}, "EQUALIZER_PAIN": {"main_description": "{prefix}釋放{name}，根據自身與目標的生命百分比差異造成真實傷害，生命差距越大，傷害越高或越低", "secondary_description": "根據生命差距造成傷害"}, "DRAIN_PHYSICAL": {"main_description": "{prefix}發動{name}，造成 {power} 物理傷害並吸取50%傷害作為生命值", "secondary_description": "造成傷害並吸血"}, "CONDITIONAL_DAMAGE_BOOST": {"main_description": "{prefix}在滿足特定條件時，{name}造成額外 {power} 傷害", "secondary_description": "在特定條件下造成額外傷害"}, "MULTI_HIT_PHYSICAL": {"main_description": "{prefix}施展{name}，連續多次攻擊，總計造成 {power} 物理傷害", "secondary_description": "進行多段攻擊"}, "ATTACK_SUPERIORITY_STRIKE": {"main_description": "{prefix}發動{name}，根據攻擊力優勢造成 {power} 物理傷害，攻擊力越高傷害越強", "secondary_description": "根據攻擊力優勢造成傷害"}, "DEFENSE_BREAKER_ASSAULT": {"main_description": "{prefix}使用{name}，當攻擊力超過敵方防禦力時造成 {power_boosted} 破防傷害", "secondary_description": "進行破防攻擊"}, "SPEED_ADVANTAGE_STRIKE": {"main_description": "{prefix}施展{name}，根據速度優勢造成 {power} 物理傷害，速度越快傷害越高", "secondary_description": "根據速度優勢造成傷害"}, "MAGICAL_SUPREMACY_BLAST": {"main_description": "{prefix}釋放{name}，根據魔法攻擊力優勢造成 {power} 魔法傷害", "secondary_description": "根據魔攻優勢造成傷害"}, "VITALITY_DRAIN_STRIKE": {"main_description": "{prefix}發動{name}，根據生命值差距造成 {power} 真實傷害", "secondary_description": "根據生命值差距造成傷害"}, "OVERWHELMING_FORCE": {"main_description": "{prefix}展現{name}，當全面屬性優勢時造成 {power_boosted} 物理傷害", "secondary_description": "在屬性優勢時造成強力傷害"}, "PERFECT_HEALTH_ASSAULT": {"main_description": "{prefix}在完美狀態下發動{name}，造成 {power_boosted} 物理傷害", "secondary_description": "在完美狀態下造成強力傷害"}, "DESPERATE_LAST_STAND": {"main_description": "{prefix}在絕境中使用{name}，根據自身血量損失造成 {power} 真實傷害", "secondary_description": "在絕境中造成傷害"}, "MANA_OVERFLOW_BLAST": {"main_description": "{prefix}在魔力充盈時釋放{name}，造成 {power_boosted} 魔法傷害", "secondary_description": "在魔力充盈時造成強力傷害"}, "MANA_DEPLETION_STRIKE": {"main_description": "{prefix}對法力不足的敵人使用{name}，造成 {power_boosted} 魔法傷害", "secondary_description": "對法力不足敵人造成強力傷害"}, "ACCURACY_PRECISION_SHOT": {"main_description": "{prefix}發動{name}，根據命中率優勢造成 {power} 精準物理傷害", "secondary_description": "進行精準射擊"}, "EVASION_COUNTER_STRIKE": {"main_description": "{prefix}利用閃避優勢發動{name}，造成 {power_boosted} 反擊傷害", "secondary_description": "利用閃避優勢反擊"}, "DEFENSIVE_SUPERIORITY": {"main_description": "{prefix}憑藉防禦優勢使用{name}，造成 {power} 物理傷害", "secondary_description": "憑藉防禦優勢攻擊"}, "MAGICAL_RESISTANCE_PIERCE": {"main_description": "{prefix}穿透魔法抗性發動{name}，造成 {power_boosted} 魔法傷害", "secondary_description": "穿透魔法抗性攻擊"}, "BALANCED_COMBAT_STRIKE": {"main_description": "{prefix}在均衡狀態下施展{name}，造成 {power_boosted} 物理傷害", "secondary_description": "在均衡狀態下攻擊"}, "RESOURCE_ADVANTAGE_BLAST": {"main_description": "{prefix}利用資源優勢釋放{name}，造成 {power} 真實傷害", "secondary_description": "利用資源優勢攻擊"}, "CRITICAL_MASS_EXPLOSION": {"main_description": "{prefix}集中所有攻擊力發動{name}，造成 {power} 魔法爆炸傷害", "secondary_description": "集中攻擊力爆炸"}, "WEAKNESS_EXPLOITATION": {"main_description": "{prefix}利用敵方弱點發動{name}，造成 {power_boosted} 真實傷害", "secondary_description": "利用敵方弱點攻擊"}, "default": {"main_description": "{prefix}施展{name}，造成 {power} {damage_type}傷害", "secondary_description": "造成傷害"}}, "heal": {"MISSING_HP_HEAL": {"main_description": "{prefix}使用{name}，治癒 {value} 已損失生命值", "secondary_description": "治癒已損失生命值"}, "BASIC_HEAL_FLAT": {"main_description": "{prefix}施展{name}，恢復 {value} 點固定生命值", "secondary_description": "恢復固定生命值"}, "BASIC_HEAL_PERCENT_CASTER_MATK": {"main_description": "{prefix}施展{name}，恢復 {value} 魔法攻擊力的生命值", "secondary_description": "恢復基於魔攻的生命值"}, "BASIC_HEAL_PERCENT_MAX_HP": {"main_description": "{prefix}施展{name}，恢復目標 {value}% 最大生命值的生命", "secondary_description": "恢復百分比最大生命值"}, "default": {"main_description": "{prefix}施展{name}，恢復 {power} 魔法攻擊力的生命值", "secondary_description": "恢復生命值"}}, "buff": {"APPLY_ATK_BOOST": {"main_description": "{prefix}獲得{name}效果，攻擊力提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升攻擊力"}, "APPLY_DEF_BOOST": {"main_description": "{prefix}獲得{name}效果，防禦力提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升防禦力"}, "APPLY_SPD_BOOST": {"main_description": "{prefix}獲得{name}效果，速度提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升速度"}, "APPLY_HP_BOOST": {"main_description": "{prefix}獲得{name}效果，最大生命值提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升最大生命值"}, "APPLY_MP_BOOST": {"main_description": "{prefix}獲得{name}效果，最大法力值提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升最大法力值"}, "APPLY_CRIT_BOOST": {"main_description": "{prefix}獲得{name}效果，暴擊能力提升，持續 {duration} 回合", "secondary_description": "提升暴擊能力"}, "APPLY_ACCURACY_BOOST": {"main_description": "{prefix}獲得{name}效果，命中率提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升命中率"}, "APPLY_EVASION_BOOST": {"main_description": "{prefix}獲得{name}效果，閃避率提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升閃避率"}, "APPLY_ALL_STATS_BOOST": {"main_description": "{prefix}獲得{name}效果，全屬性提升 {value_percent}，持續 {duration} 回合", "secondary_description": "提升全屬性"}, "BERSERKER_TRADE": {"main_description": "{prefix}進入{name}狀態，攻擊力大幅提升，防禦力略微下降", "secondary_description": "進入狂戰士狀態"}, "DUELIST_FOCUS": {"main_description": "{prefix}進入{name}狀態，提升物理攻擊、物理防禦和命中", "secondary_description": "進入決鬥者專注狀態"}, "MOMENTUM_SHIFT": {"main_description": "{prefix}把握{name}，根據戰況（如連續命中、成功閃避）獲得不同的屬性增益", "secondary_description": "根據戰況獲得增益"}, "REFLECT_DAMAGE": {"main_description": "{prefix}獲得{name}效果，反射受到的部分傷害，持續 {duration} 回合", "secondary_description": "反射傷害"}, "APPLY_REGEN_STATUS_WEAK": {"main_description": "{prefix}獲得{name}效果，每回合持續回復少量生命，持續 {duration} 回合", "secondary_description": "持續回復生命"}, "APPLY_INVINCIBLE_STATUS_SHORT": {"main_description": "{prefix}獲得{name}效果，短時間內免疫所有傷害，持續 {duration} 回合", "secondary_description": "短時間無敵"}, "APPLY_WEAK_LIFESTEAL": {"main_description": "{prefix}獲得{name}效果，攻擊附帶微弱的吸血效果，持續 {duration} 回合，觸發機率 {chance}", "secondary_description": "獲得微弱吸血效果"}, "default": {"main_description": "{prefix}獲得{name}效果，能力獲得增強，持續 {duration} 回合", "secondary_description": "獲得增益效果"}}, "debuff": {"APPLY_ATK_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，攻擊力降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方攻擊力"}, "APPLY_DEF_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，防禦力降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方防禦力"}, "APPLY_SPD_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，速度降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方速度"}, "APPLY_ACCURACY_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，命中率降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方命中率"}, "APPLY_EVASION_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，閃避率降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方閃避率"}, "APPLY_HP_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，最大生命值降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方最大生命值"}, "APPLY_MP_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，最大法力值降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方最大法力值"}, "APPLY_CRIT_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，暴擊能力降低，持續 {duration} 回合", "secondary_description": "降低敵方暴擊能力"}, "APPLY_ALL_STATS_DEBUFF": {"main_description": "{prefix}對敵方施加{name}，全屬性降低 {value_percent}，持續 {duration} 回合", "secondary_description": "降低敵方全屬性"}, "APPLY_POISON_STATUS": {"main_description": "{prefix}對敵方施加{name}，使其陷入中毒狀態，每回合損失生命，持續 {duration} 回合", "secondary_description": "使敵方中毒"}, "APPLY_BURN_STATUS": {"main_description": "{prefix}對敵方施加{name}，使其陷入燃燒狀態，每回合損失生命，持續 {duration} 回合", "secondary_description": "使敵方燃燒"}, "default": {"main_description": "{prefix}對敵方施加{name}，使其屬性下降 {value_percent}，持續 {duration} 回合", "secondary_description": "削弱敵方"}}, "control": {"APPLY_FREEZE_STATUS": {"main_description": "{prefix}對敵方施展{name}，將其冰凍，無法行動，持續 {duration} 回合", "secondary_description": "冰凍敵方"}, "APPLY_STUN_STATUS": {"main_description": "{prefix}對敵方施展{name}，將其擊暈，無法行動，持續 {duration} 回合", "secondary_description": "擊暈敵方"}, "APPLY_SILENCE_STATUS": {"main_description": "{prefix}對敵方施展{name}，使其沉默，無法施法，持續 {duration} 回合", "secondary_description": "沉默敵方"}, "TARGETED_DISABLE_STRIKE": {"main_description": "{prefix}施展{name}，嘗試擊暈高生命值的目標，使其無法行動，持續 {duration} 回合", "secondary_description": "針對高血量目標擊暈"}, "default": {"main_description": "{prefix}對敵方施展{name}，造成{effect_name}效果，持續 {duration} 回合", "secondary_description": "控制敵方"}}, "shield": {"APPLY_BASIC_SHIELD": {"main_description": "{prefix}施展{name}，獲得護盾，可吸收 {value} 點傷害，持續 {duration} 回合", "secondary_description": "獲得基礎護盾"}, "APPLY_MAGIC_SHIELD": {"main_description": "{prefix}施展{name}，獲得魔法護盾，可吸收 {value} 點魔法傷害，持續 {duration} 回合", "secondary_description": "獲得魔法護盾"}, "APPLY_PHYSICAL_BARRIER": {"main_description": "{prefix}施展{name}，獲得物理屏障，可吸收 {value} 點物理傷害，持續 {duration} 回合", "secondary_description": "獲得物理屏障"}, "APPLY_SCALING_SHIELD": {"main_description": "{prefix}施展{name}，獲得適應護盾，護盾強度基於施法者魔攻，持續 {duration} 回合", "secondary_description": "獲得適應護盾"}, "default": {"main_description": "{prefix}施展{name}，獲得護盾效果，持續 {duration} 回合", "secondary_description": "獲得護盾"}}, "special": {"DISPEL_ALL_DEBUFFS": {"main_description": "{prefix}發動{name}，驅散自身所有負面效果", "secondary_description": "驅散負面效果"}, "SHIELD_REMOVAL": {"main_description": "{prefix}發動{name}，移除敵人的所有盾牌效果", "secondary_description": "移除敵方護盾"}, "PREDICTIVE_DEFENSE_SETUP": {"main_description": "{prefix}進入{name}，準備進行預判防禦，下回合可能觸發特殊防禦效果，持續 {duration} 回合", "secondary_description": "準備預判防禦"}, "OVERWHELMING_PRESSURE_APPLY": {"main_description": "{prefix}釋放{name}，對目標施加壓力，降低其作戰效能，可疊加，持續 {duration} 回合，觸發機率 {chance}", "secondary_description": "施加壓力效果"}, "TRANSFER_MP_EFFECT": {"main_description": "{prefix}發動{name}，轉移 {value} 點法力值給自己", "secondary_description": "轉移法力值"}, "LOSE_MP_EFFECT": {"main_description": "{prefix}發動{name}，燃燒目標 {value} 點法力值", "secondary_description": "燃燒敵方法力值"}, "default": {"main_description": "{prefix}發動{name}，產生特殊戰術效果", "secondary_description": "產生特殊效果"}}}