"""
基礎數據庫操作模組
提供所有 Repository 通用的模組級函數
"""

from typing import Any, Dict, List, Optional, Sequence, Tuple

import asyncpg

from database.postgresql.async_manager import get_pool
from utils.logger import logger


async def execute_query(
    query: str,
    params: Optional[Sequence] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """執行 INSERT, UPDATE, DELETE 等不返回多行的查詢

    參數:
        query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
        params: 查詢參數
        connection: (可選) 要使用的 asyncpg 連接

    返回:
        int: 受影響的行數

    Raises:
        DatabaseIntegrityError: 當資料庫完整性約束被違反時
        DatabaseOperationError: 當其他資料庫操作錯誤發生時
    """
    params = params or []
    conn_to_use = connection

    try:
        if conn_to_use:
            status = await conn_to_use.execute(query, *params)
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                status = await pool_conn.execute(query, *params)
        # 處理 status，可能是 "UPDATE 1" 或直接是 1
        if isinstance(status, str):
            return int(status.split()[-1])
        return status
    except asyncpg.UniqueViolationError as e:
        logger.error(
            "唯一約束違反: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseIntegrityError

        raise DatabaseIntegrityError(f"唯一約束違反: {e}") from e
    except asyncpg.ForeignKeyViolationError as e:
        logger.error(
            "外鍵約束違反: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseIntegrityError

        raise DatabaseIntegrityError(f"外鍵約束違反: {e}") from e
    except asyncpg.PostgresError as e:
        logger.error(
            "執行查詢失敗: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"資料庫操作失敗: {e}", original_exception=e
        ) from e


async def fetch_all(
    query: str,
    params: Optional[Sequence] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[asyncpg.Record]:
    """執行 SELECT 查詢並返回所有行

    參數:
        query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
        params: 查詢參數
        connection: (可選) 要使用的 asyncpg 連接

    返回:
        List[asyncpg.Record]: 查詢結果列表，每行是一個 Record 對象 (類似字典)

    Raises:
        DatabaseOperationError: 當資料庫操作錯誤發生時
    """
    params = params or []
    conn_to_use = connection

    try:
        if conn_to_use:
            results = await conn_to_use.fetch(query, *params)
            return results
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                results = await pool_conn.fetch(query, *params)
                return results
    except asyncpg.PostgresError as e:
        logger.error(
            "獲取查詢失敗: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"資料庫查詢失敗: {e}", original_exception=e
        ) from e


async def fetch_one(
    query: str,
    params: Optional[Sequence] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[asyncpg.Record]:
    """執行 SELECT 查詢並返回第一行

    參數:
        query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
        params: 查詢參數
        connection: (可選) 要使用的 asyncpg 連接

    返回:
        Optional[asyncpg.Record]: 查詢結果的第一行，如果沒有結果則返回 None

    Raises:
        DatabaseOperationError: 當資料庫操作錯誤發生時
    """
    params = params or []
    conn_to_use = connection

    try:
        if conn_to_use:
            result = await conn_to_use.fetchrow(query, *params)
            return result
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                result = await pool_conn.fetchrow(query, *params)
                return result
    except asyncpg.PostgresError as e:
        logger.error(
            "獲取單行查詢失敗: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"資料庫查詢失敗: {e}", original_exception=e
        ) from e


async def fetch_value(
    query: str,
    params: Optional[Sequence] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Any:
    """執行 SELECT 查詢並返回第一行第一列的值

    參數:
        query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
        params: 查詢參數
        connection: (可選) 要使用的 asyncpg 連接

    返回:
        Any: 查詢結果的第一行第一列的值

    Raises:
        DatabaseOperationError: 當資料庫操作錯誤發生時
    """
    params = params or []
    conn_to_use = connection

    try:
        if conn_to_use:
            result = await conn_to_use.fetchval(query, *params)
            return result
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                result = await pool_conn.fetchval(query, *params)
                return result
    except asyncpg.PostgresError as e:
        logger.error(
            "獲取值查詢失敗: %s - 查詢: %s - 參數: %s",
            str(e),
            query,
            params,
            exc_info=True,
        )
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"資料庫查詢失敗: {e}", original_exception=e
        ) from e


async def execute_many(
    query: str,
    params_list: List[Sequence],
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """批量執行相同的查詢

    參數:
        query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
        params_list: 參數列表，每個元素是一組查詢參數
        connection: (可選) 要使用的 asyncpg 連接

    Raises:
        DatabaseIntegrityError: 當資料庫完整性約束被違反時
        DatabaseOperationError: 當其他資料庫操作錯誤發生時
    """
    conn_to_use = connection

    try:
        if conn_to_use:
            await conn_to_use.executemany(query, params_list)
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                await pool_conn.executemany(query, params_list)
    except asyncpg.UniqueViolationError as e:
        logger.error(
            "批量執行唯一約束違反: %s - 查詢: %s", str(e), query, exc_info=True
        )
        from gacha.exceptions import DatabaseIntegrityError

        raise DatabaseIntegrityError(f"唯一約束違反: {e}") from e
    except asyncpg.ForeignKeyViolationError as e:
        logger.error(
            "批量執行外鍵約束違反: %s - 查詢: %s", str(e), query, exc_info=True
        )
        from gacha.exceptions import DatabaseIntegrityError

        raise DatabaseIntegrityError(f"外鍵約束違反: {e}") from e
    except asyncpg.PostgresError as e:
        logger.error("批量執行查詢失敗: %s - 查詢: %s", str(e), query, exc_info=True)
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"資料庫操作失敗: {e}", original_exception=e
        ) from e


async def batch_upsert(
    table: str,
    records: List[Dict[str, Any]],
    key_field: str,
    connection: Optional[asyncpg.Connection] = None,
) -> Tuple[int, int]:
    """批量插入或更新（upsert）數據

    參數:
        table: 表名
        records: 記錄字典列表
        key_field: 鍵字段 (用於 ON CONFLICT)
        connection: (可選) 要使用的 asyncpg 連接

    返回:
        Tuple[int, int]: (執行的語句數, 0) - asyncpg 不直接返回插入/更新計數

    Raises:
        ValueError: 當參數無效時
        DatabaseOperationError: 當資料庫操作錯誤發生時
    """
    if not records:
        return (0, 0)

    first_record = records[0]
    all_fields = list(first_record.keys())
    if key_field not in all_fields:
        raise ValueError(f"鍵字段 '{key_field}' 不在記錄的字段列表中")
    if not all(set(record.keys()) == set(all_fields) for record in records):
        raise ValueError("所有記錄必須有相同的字段")

    update_fields = [f for f in all_fields if f != key_field]
    if not update_fields:
        raise ValueError("需要至少一個非鍵字段進行更新")

    field_list = ", ".join(all_fields)
    placeholders = ", ".join([f"${i + 1}" for i in range(len(all_fields))])
    update_clause = ", ".join(
        [f"{field} = EXCLUDED.{field}" for field in update_fields]
    )

    query = f"""
        INSERT INTO {table} ({field_list})
        VALUES ({placeholders})
        ON CONFLICT ({key_field})
        DO UPDATE SET {update_clause}
    """

    params_list = [
        tuple(record.get(field) for field in all_fields) for record in records
    ]

    conn_to_use = connection

    try:
        if conn_to_use:
            await conn_to_use.executemany(query, params_list)
        else:
            pool = get_pool()
            async with pool.acquire() as pool_conn:
                await pool_conn.executemany(query, params_list)
        return (len(params_list), 0)
    except asyncpg.PostgresError as e:
        logger.error("批量 Upsert 失敗: %s - 表: %s", str(e), table, exc_info=True)
        from gacha.exceptions import DatabaseOperationError

        raise DatabaseOperationError(
            f"批量 Upsert 失敗: {e}", original_exception=e
        ) from e
