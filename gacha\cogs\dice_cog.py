"""
Gacha系統三骰子大小遊戲 COG - 最終版
處理三骰子大小遊戲的Discord命令和交互。
核心玩法為「押大小」和「押圍骰」，內建莊家優勢。
支持快捷命令和完整的UI交互。
"""

import secrets  # 使用加密級別的安全隨機數生成器
from typing import Any, Dict, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.exceptions as gacha_exc
import gacha.services.economy_service as economy_service
import gacha.services.game_stats_service as game_stats_service
from gacha.exceptions import OnCooldownError
from utils.base_view import BaseView
from utils.logger import logger


class TripleDiceView(BaseView):
    """三骰子遊戲視圖"""

    def __init__(
        self,
        bot: commands.Bot,
        user: Union[discord.User, discord.Member],
        bet: int,
        cog: "DiceCog",
        is_result: bool = False,
        current_balance: Optional[int] = None,
        last_choice_data: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(bot=bot, user_id=user.id, timeout=180)
        self.user = user
        self.bet = bet
        self.cog = cog
        self.message: Optional[discord.InteractionMessage] = None
        self.is_result = is_result
        self.current_balance = current_balance
        self.last_choice_data = last_choice_data

        if is_result:
            self._add_replay_buttons()
        else:
            self._add_choice_buttons()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查互動是否來自遊戲擁有者並處理冷卻"""
        if not await super().interaction_check(interaction):
            return False

        retry_after = self.cog.button_cooldown_mapping.update_rate_limit(interaction)
        if retry_after:
            raise OnCooldownError(f"⏰ 請稍等 {retry_after:.1f} 秒後再試", retry_after)

        return True

    def _add_choice_buttons(self):
        """添加初始選擇按鈕：大、小、押圍骰"""
        big_button = discord.ui.Button(
            label="大 (11-17)", style=discord.ButtonStyle.green, custom_id="dice_big"
        )
        big_button.callback = self._size_callback
        self.add_item(big_button)

        small_button = discord.ui.Button(
            label="小 (4-10)", style=discord.ButtonStyle.red, custom_id="dice_small"
        )
        small_button.callback = self._size_callback
        self.add_item(small_button)

        triple_button = discord.ui.Button(
            label="押圍骰 (三個相同)",
            style=discord.ButtonStyle.blurple,
            custom_id="dice_triple",
        )
        triple_button.callback = self._triple_callback
        self.add_item(triple_button)

    async def _size_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        # 從按鈕的 custom_id 判斷選擇
        custom_id = interaction.data.get("custom_id") if interaction.data else None
        choice = "big" if custom_id == "dice_big" else "small"
        await self.cog.handle_game_logic_and_response(
            interaction, self.bet, "size", choice, edit_mode=True
        )

    async def _triple_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        await self.cog.handle_game_logic_and_response(
            interaction, self.bet, "triple", choice=None, edit_mode=True
        )

    def _create_replay_callback(self, bet: int, game_type: str, choice: Optional[str]):
        """創建一個處理重玩邏輯的回調函數。"""

        async def callback(interaction: discord.Interaction):
            # 點擊重玩按鈕是新的交互，所以在這裡 defer
            await interaction.response.defer()
            # edit_mode=False 因為重玩總是發送新消息
            await self.cog.handle_game_logic_and_response(
                interaction, bet, game_type, choice, edit_mode=False
            )

        return callback

    def _create_menu_callback(self):
        """創建返回主菜單的回調函數，保持當前下注金額。"""

        async def callback(interaction: discord.Interaction):
            await interaction.response.defer()
            # 使用當前 view 的 bet 金額，調用現有方法
            await self.cog.show_choice_interface(interaction, self.bet)

        return callback

    def _add_replay_buttons(self):
        """使用更數據驅動的方式添加重玩按鈕。"""
        if not self.last_choice_data:
            return
        last_game_type = self.last_choice_data.get("type")
        last_choice = self.last_choice_data.get("choice")

        button_configs = []
        game_type_for_callback: str
        choice_for_callback: Optional[str]

        if last_game_type == "size" and last_choice:
            choice_text = "大" if last_choice == "big" else "小"
            button_configs = [
                {
                    "label": f"再來一局 ({choice_text}, {self.bet:,} 油幣)",
                    "bet": self.bet,
                    "style": discord.ButtonStyle.success,
                },
                {
                    "label": f"雙倍下注 ({choice_text}, {self.bet * 2:,} 油幣)",
                    "bet": self.bet * 2,
                    "style": discord.ButtonStyle.danger,
                },
            ]
            game_type_for_callback = "size"
            choice_for_callback = last_choice
        elif last_game_type == "triple":
            button_configs = [
                {
                    "label": f"再來一局 (押圍骰, {self.bet:,} 油幣)",
                    "bet": self.bet,
                    "style": discord.ButtonStyle.success,
                },
                {
                    "label": f"雙倍下注 (押圍骰, {self.bet * 2:,} 油幣)",
                    "bet": self.bet * 2,
                    "style": discord.ButtonStyle.danger,
                },
            ]
            game_type_for_callback = "triple"
            choice_for_callback = None
        else:
            # 初始化變數以避免 pyright 報錯
            game_type_for_callback = "size"
            choice_for_callback = "big"

        for config in button_configs:
            bet_amount = int(config["bet"])
            current_balance = self.current_balance or 0
            has_balance = current_balance >= bet_amount
            button_style = config["style"]
            if not isinstance(button_style, discord.ButtonStyle):
                button_style = discord.ButtonStyle.secondary

            button = discord.ui.Button(
                label=str(config["label"]),
                style=button_style if has_balance else discord.ButtonStyle.gray,
                disabled=not has_balance,
            )
            button.callback = self._create_replay_callback(
                bet_amount, game_type_for_callback, choice_for_callback
            )
            self.add_item(button)

        # 返回主菜單按鈕 - 檢查是否有足夠餘額進行當前下注金額
        current_balance = self.current_balance or 0
        has_current_bet_balance = current_balance >= self.bet
        menu_button = discord.ui.Button(
            label="返回主菜單",
            style=(
                discord.ButtonStyle.secondary
                if has_current_bet_balance
                else discord.ButtonStyle.gray
            ),
            disabled=not has_current_bet_balance,
        )
        menu_button.callback = self._create_menu_callback()
        self.add_item(menu_button)


class DiceCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.MIN_BET = 10
        self.button_cooldown_mapping = commands.CooldownMapping.from_cooldown(
            1.0, 1.5, lambda i: i.user.id
        )
        self.DICE_EMOJI = {
            1: "<:d1:1368848535604170814>",
            2: "<:d2:1368848527744041041>",
            3: "<:d3:1368848539278639205>",
            4: "<:d4:1368848542860574822>",
            5: "<:d5:1368848531699273831>",
            6: "<:d6:1368848546295582810>",
        }
        self.GAME_IMAGE_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1368848475118108702/dice.png"

    def _create_game_embed(
        self, user: Union[discord.User, discord.Member], bet: int, current_balance: int
    ) -> discord.Embed:
        embed = discord.Embed(
            title="🎲 三骰子遊戲",
            description=f"""選擇你的玩法，贏取獎勵！

**遊戲規則**
- 遊戲會擲出**三個骰子**並計算總和。
⬆️ **大**: 總點數 11-17
⬇️ **小**: 總點數 4-10
🎲 **押圍骰**: 三個點數相同 (賠率 **1:30**)
⚠️ **注意**: 如果開出**圍骰**，押注「大」或「小」均視為**莊家贏**！

**下注信息**
💰 當前下注: {bet:,} 油幣""",
            color=discord.Color.blue(),
        )
        from config.app_config import get_oil_emoji

        embed.add_field(
            name="💳 當前餘額",
            value=f"餘額:`{current_balance:,}`{get_oil_emoji()}",
            inline=True,
        )
        embed.set_thumbnail(url=self.GAME_IMAGE_URL)
        embed.set_footer(text="點擊按鈕開始遊戲", icon_url=user.display_avatar.url)
        return embed

    async def _create_result_embed(
        self,
        user: Union[discord.User, discord.Member],
        bet: int,
        new_balance: int,
        game_result: Dict[str, Any],
    ) -> discord.Embed:
        """創建遊戲結果 embed。"""
        # 從結果字典中解包變量
        dice_rolls = game_result["dice_rolls"]
        dice_total = game_result["dice_total"]
        is_win = game_result["is_win"]
        is_triple = game_result["is_triple"]
        game_type = game_result["game_type"]
        choice = game_result["choice"]
        payout = game_result["payout"]

        # 1. 決定標題和顏色
        if is_triple and game_type == "size" and not is_win:
            title = "💥 圍骰通殺！莊家獲勝！"
            color = discord.Color.red()
        elif is_win:
            title = "🎯 遊戲獲勝！"
            color = discord.Color.green()
        else:
            title = "💥 遊戲失敗！"
            color = discord.Color.red()

        # 2. 構建描述
        description_parts = []
        win_loss_amount = payout - bet

        if win_loss_amount >= 0 and is_win:
            description_parts.append(
                f"✅ 贏了 {win_loss_amount:,} <:oi:1382174314811363470>！"
            )
        else:
            description_parts.append(
                f"❌ 輸了 {abs(win_loss_amount):,} <:oi:1382174314811363470>！"
            )

        # 獲取並添加統計信息
        stats = await game_stats_service.get_user_game_stats(user.id, "dice")
        if stats:
            total_games = stats.get("total_games", 0)
            total_wins = stats.get("total_wins", 0)
            win_rate = (
                round((total_wins / total_games) * 100, 1) if total_games > 0 else 0
            )
            description_parts.append(
                f"<a:a_:1382444741798658228> 勝率`{win_rate}`% 總場次`{total_games:,}`"
            )

        description_parts.append("")

        # 骰子結果
        dice_emojis = " ".join([self.DICE_EMOJI.get(r, "🎲") for r in dice_rolls])
        description_parts.extend(
            [
                "**🎲 擲骰結果:**",
                f"# **{dice_emojis}**",
                f"## 總點數: **{dice_total}**",
                "",
            ]
        )

        # 玩家選擇
        your_choice_text = (
            f"你的選擇: {'⬆️ 大' if choice == 'big' else '⬇️ 小'}"
            if game_type == "size"
            else "你的選擇: 🎲 押圍骰"
        )
        description_parts.append(f"**{your_choice_text}**")

        # 3. 創建 Embed
        embed = discord.Embed(
            title=title, description="\n".join(description_parts), color=color
        )
        embed.set_thumbnail(url=self.GAME_IMAGE_URL)

        # 4. 設置 Footer
        footer_parts = [f"下注: {bet:,}", f"餘額: {new_balance:,}"]
        if stats:
            total_profit_loss = stats.get("total_profit_loss", 0)
            footer_parts.append(f"總盈虧: {total_profit_loss:,}")

        embed.set_footer(
            text=" | ".join(footer_parts), icon_url=user.display_avatar.url
        )

        return embed

    async def _update_game_stats(
        self, user_id: int, bet: int, game_result: Dict[str, Any]
    ):
        """更新遊戲統計數據【與其他遊戲保持統一格式】"""
        try:
            payout = game_result["payout"]
            profit = payout - bet

            # 統一的結果判斷邏輯（與其他遊戲一致）
            if profit > 0:
                result = "win"
            elif profit == 0:
                result = "push"  # 雖然三骰子遊戲目前沒有平手，但保持一致性
            else:
                result = "lose"

            # 統一的遊戲數據格式（只包含統計需要的字段）
            game_data = {
                "bet": bet,
                "payout": payout,
                "profit": profit,
                "result": result,
                # 三骰子遊戲特定數據
                "game_type": game_result["game_type"],
                "choice": game_result["choice"],
                "dice_rolls": game_result["dice_rolls"],
                "dice_total": game_result["dice_total"],
                "is_triple": game_result["is_triple"],
            }

            await game_stats_service.record_game_result(user_id, "dice", game_data)
        except Exception as e:
            logger.error("更新遊戲統計失敗: %s", e)

    def _run_game_round(self, game_type: str, choice: Optional[str]) -> Dict[str, Any]:
        """
        執行一輪遊戲的核心邏輯（擲骰、判斷輸贏）。
        這是一個純函數，不執行任何IO或API調用。
        """
        dice_rolls = [secrets.randbelow(6) + 1 for _ in range(3)]
        dice_total = sum(dice_rolls)
        is_triple = len(set(dice_rolls)) == 1
        is_win = False

        if game_type == "size":
            if not is_triple and (
                (choice == "big" and 11 <= dice_total <= 17)
                or (choice == "small" and 4 <= dice_total <= 10)
            ):
                is_win = True
        elif game_type == "triple" and is_triple:
            is_win = True

        payout_multiplier = 0
        if is_win:
            payout_multiplier = 31 if game_type == "triple" else 2

        return {
            "dice_rolls": dice_rolls,
            "dice_total": dice_total,
            "is_triple": is_triple,
            "is_win": is_win,
            "payout_multiplier": payout_multiplier,
            "game_type": game_type,
            "choice": choice,
        }

    async def handle_game_logic_and_response(
        self,
        interaction: discord.Interaction,
        bet: int,
        game_type: str,
        choice: Optional[str] = None,
        edit_mode: bool = True,
    ):
        """
        協調完整的遊戲流程：扣款、運行遊戲、發獎、更新統計、顯示結果。
        假定 interaction 已經被 defer。
        """
        # 根據開發規範，讓 BusinessError 自然拋出，由 BaseView 處理
        # 1. 檢查餘額並扣款
        current_balance = (await economy_service.get_balance(interaction.user.id)).get(
            "balance", 0
        )
        if current_balance < bet:
            raise gacha_exc.InsufficientBalanceError(
                required=bet, current=current_balance
            )

        await economy_service.award_oil(
            user_id=interaction.user.id,
            amount=-bet,
            transaction_type="game:dice_bet",
            reason="Dice game bet",
        )

        # 2. 運行核心遊戲邏輯
        game_result = self._run_game_round(game_type, choice)

        # 3. 計算並發放獎勵
        payout = bet * game_result["payout_multiplier"]
        game_result["payout"] = payout  # 將payout存入結果，方便後續使用
        if game_result["is_win"] and payout > 0:
            await economy_service.award_oil(
                user_id=interaction.user.id,
                amount=payout,
                transaction_type="game:dice_win",
                reason="Dice game win",
            )

        # 4. 更新統計和顯示結果
        new_balance = (await economy_service.get_balance(interaction.user.id)).get(
            "balance", 0
        )
        await self._update_game_stats(interaction.user.id, bet, game_result)

        embed = await self._create_result_embed(
            interaction.user, bet, new_balance, game_result
        )

        last_choice_data = {"type": game_type, "choice": choice}
        view = TripleDiceView(
            bot=self.bot,
            user=interaction.user,
            bet=bet,
            cog=self,
            is_result=True,
            current_balance=new_balance,
            last_choice_data=last_choice_data,
        )

        if edit_mode:
            await interaction.edit_original_response(embed=embed, view=view)
        else:
            await interaction.followup.send(embed=embed, view=view)

    async def show_choice_interface(self, interaction: discord.Interaction, bet: int):
        """顯示初始選擇界面。假定 interaction 已經被 defer。"""
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        view = TripleDiceView(bot=self.bot, user=interaction.user, bet=bet, cog=self)
        embed = self._create_game_embed(interaction.user, bet, current_balance)
        await interaction.followup.send(embed=embed, view=view)

    async def show_choice_interface_new_message(self, interaction: discord.Interaction):
        """顯示初始選擇界面 - 發送新消息 (用於返回主菜單按鈕)"""
        await interaction.response.defer()
        # 這裡我們用默認的最小下注金額
        last_bet = self.MIN_BET
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        view = TripleDiceView(
            bot=self.bot, user=interaction.user, bet=last_bet, cog=self
        )
        embed = self._create_game_embed(interaction.user, last_bet, current_balance)
        await interaction.followup.send(embed=embed, view=view)

    async def show_choice_interface_with_bet(
        self, interaction: discord.Interaction, bet: int
    ):
        """顯示初始選擇界面 - 發送新消息，使用指定的下注金額"""
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        view = TripleDiceView(bot=self.bot, user=interaction.user, bet=bet, cog=self)
        embed = self._create_game_embed(interaction.user, bet, current_balance)
        await interaction.followup.send(embed=embed, view=view)

    @app_commands.command(name="dice", description="開始一局三骰子遊戲")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        bet="下注金額（最低10油幣）",
        choice="（可選）直接選擇大、小或押圍骰，跳過選擇菜單",
    )
    @app_commands.choices(
        choice=[
            app_commands.Choice(name="大 (11-17)", value="big"),
            app_commands.Choice(name="小 (4-10)", value="small"),
            app_commands.Choice(name="押圍骰", value="triple"),
        ]
    )
    @app_commands.checks.cooldown(1, 3.0, key=lambda i: i.user.id)
    async def dice(
        self,
        interaction: discord.Interaction,
        bet: app_commands.Range[int, 10],
        choice: Optional[str] = None,
    ):
        # 統一在命令入口點 defer 一次
        await interaction.response.defer(thinking=True)

        # 根據開發規範，讓錯誤自然拋出，由全域錯誤處理器捕獲
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < bet:
            raise gacha_exc.InsufficientBalanceError(
                required=bet, current=current_balance
            )

        if choice:
            game_type = "triple" if choice == "triple" else "size"
            actual_choice = choice if game_type == "size" else None
            # edit_mode=False 因為我們是從斜杠命令發起，總是發送新消息
            await self.handle_game_logic_and_response(
                interaction, bet, game_type, actual_choice, edit_mode=False
            )
        else:
            # 如果沒有快捷選擇，顯示選擇界面
            await self.show_choice_interface(interaction, bet)


async def setup(bot: commands.Bot):
    await bot.add_cog(DiceCog(bot))
    logger.info("DiceCog (三骰子最終版) 已成功加載並註冊。")
