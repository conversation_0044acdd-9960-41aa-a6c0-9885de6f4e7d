"""
問題回報系統 COG
提供 /問題回報 斜線命令，讓用戶可以透過 Modal 提交問題回報
"""

from datetime import datetime

import aiohttp
import discord
from discord import app_commands
from discord.ext import commands

from utils.base_modal import BaseModal
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class ReportModal(BaseModal, title="問題回報"):
    """問題回報 Modal"""

    def __init__(self, webhook_url: str, bot: commands.Bot):
        super().__init__(bot=bot, title="問題回報", timeout=300)
        self.webhook_url = webhook_url

        # 問題回報輸入框
        self.report_input = discord.ui.TextInput(
            label="請描述您遇到的問題",
            style=discord.TextStyle.paragraph,
            placeholder=(
                "請詳細描述您遇到的問題，包括：\n"
                "1. 問題發生的情況\n"
                "2. 預期的結果\n"
                "3. 實際發生的結果\n"
                "4. 重現步驟（如果有的話）"
            ),
            required=True,
            max_length=2000,
            min_length=10,
        )
        self.add_item(self.report_input)

    async def on_submit(self, interaction: discord.Interaction):
        """處理 Modal 提交"""
        await interaction.response.defer(ephemeral=True)

        # 獲取回報內容
        report_content = self.report_input.value.strip()
        user = interaction.user

        # 創建 webhook 內容
        embed = discord.Embed(
            title="🐛 新問題回報",
            description=report_content,
            color=discord.Color.red(),
            timestamp=datetime.utcnow(),
        )

        # 添加用戶信息
        embed.add_field(
            name="回報用戶",
            value=f"{user.mention} ({user.display_name})",
            inline=True,
        )
        embed.add_field(name="用戶ID", value=str(user.id), inline=True)
        embed.add_field(
            name="伺服器",
            value=interaction.guild.name if interaction.guild else "私訊",
            inline=True,
        )

        # 設置用戶頭像
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        embed.set_footer(text="問題回報系統")

        # 發送到 webhook
        success = await self._send_webhook(embed)

        if success:
            embed = SuccessEmbed(
                description="您的問題回報已成功提交！感謝您的回饋，我們會盡快處理。"
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            logger.info("問題回報已提交 - 用戶: %s (%s)", user.id, user.display_name)
        else:
            from auxiliary.exceptions import AuxiliaryError

            raise AuxiliaryError("提交問題回報時發生錯誤，請稍後再試或聯繫管理員。")

    async def _send_webhook(self, embed: discord.Embed) -> bool:
        """發送 webhook 通知"""
        try:
            webhook_payload = {"embeds": [embed.to_dict()]}

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url, json=webhook_payload
                ) as response:
                    if 200 <= response.status < 300:
                        logger.info(
                            "問題回報 webhook 已成功發送，狀態碼: %s", response.status
                        )
                        return True
                    else:
                        logger.error(
                            "問題回報 webhook 發送失敗，狀態碼: %s, 回應: %s",
                            response.status,
                            await response.text(),
                        )
                        return False

        except aiohttp.ClientError as e:
            logger.error("發送問題回報 webhook 時發生網路錯誤: %s", e, exc_info=True)
            return False
        except Exception as e:
            logger.error("發送問題回報 webhook 時發生未知錯誤: %s", e, exc_info=True)
            return False


class ReportCog(commands.Cog, name="問題回報"):
    """問題回報系統 COG"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # 設置 webhook URL
        self.webhook_url = "https://discord.com/api/webhooks/1379405116188262480/RayHeWsU9bU2rOImG9glE14NYlcQ7s-z_caib6KZk8mdT8nvcRXVdy_cikNCcM7bJKP4"

    @app_commands.command(name="問題回報", description="回報系統問題或錯誤")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def report_issue(self, interaction: discord.Interaction):
        """問題回報斜線命令"""
        # 創建並發送 Modal
        modal = ReportModal(self.webhook_url, self.bot)
        await interaction.response.send_modal(modal)


async def setup(bot: commands.Bot):
    """註冊問題回報 COG"""
    await bot.add_cog(ReportCog(bot))
    logger.info("ReportCog 已成功載入")
