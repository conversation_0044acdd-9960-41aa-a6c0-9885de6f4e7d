# 錯誤與成功處理重構指南

本文檔旨在提供一個清晰、具體的指南，說明如何根據 `docs/錯誤與異常處理開發規範.md` 來重構現有程式碼。遵循本指南可以確保程式碼的一致性、健壯性和可維護性。

## 核心原則

- **UI 層保持乾淨**：`cogs` 和 `views` 中的程式碼應專注於接收使用者輸入和更新 UI，不應包含業務邏輯或手動的錯誤/成功訊息處理。
- **業務邏輯集中**：所有業務規則的判斷都應在 `services` 層中進行。
- **錯誤透過異常傳遞**：`services` 層透過 `raise BusinessError(...)` 來通知 UI 層發生了可預期的錯誤。
- **框架處理響應**：`BaseView` 和 `BaseModal` 會自動捕捉這些異常，並以統一的格式回覆給使用者。

---

## 1. 通用成功訊息 (`SuccessEmbed`)

對於簡單的操作成功回覆，應使用標準的 `SuccessEmbed` 而不是手動發送字串訊息。

**修改前 (Before):**
```python
# in a cog or view
await some_service.do_something()
await interaction.followup.send("操作成功！", ephemeral=True)
```

**修改後 (After):**
```python
# in a cog or view
from utils.response_embeds import SuccessEmbed

await some_service.do_something()
embed = SuccessEmbed(description="操作成功！")
await interaction.followup.send(embed=embed, ephemeral=True)
```
**重點**:
- 導入 `SuccessEmbed`。
- 將成功訊息的字串包裝在 `SuccessEmbed` 的 `description` 中。

---

## 2. 拋出錯誤 (`raise`) 而非手動回覆

**絕對禁止** 在 UI 層（`cogs`, `views`）手動檢查並回覆錯誤。這些檢查必須移至 `services` 層，並以拋出異常（`raise`）的方式處理。

**修改前 (Before):**
```python
# in a view's on_submit or a button's callback
user_balance = await get_balance(user_id)
if user_balance < 100:
    await interaction.response.send_message("餘額不足！", ephemeral=True)
    return

await some_service.deduct_balance(user_id, 100)
# ...
```

**修改後 (After):**

**A. 在 `services` 層加入檢查與拋錯：**
```python
# gacha/services/some_service.py
from gacha.exceptions import InsufficientBalanceError

async def deduct_balance(user_id: int, amount: int):
    user_balance = await get_balance(user_id)
    if user_balance < amount:
        # 拋出一個具體的業務錯誤
        raise InsufficientBalanceError(required=amount, current=user_balance)
    
    # ... 執行扣款邏輯 ...
```

**B. 簡化 UI 層的呼叫：**
```python
# in a view's on_submit or a button's callback
# 直接呼叫，不需要 try-except 或 if 檢查
# 如果餘額不足，service 會拋出 InsufficientBalanceError，
# BaseView/BaseModal 的 on_error 會自動捕捉並回覆。
await some_service.deduct_balance(interaction.user.id, 100)
# ...
```
**重點**:
- 刪除 UI 層的 `if` 判斷和 `send_message`。
- 在對應的 `service` 方法中加入相同的 `if` 判斷，但將 `send_message` 改為 `raise BusinessError(...)`。

---

## 3. 繼承基底類別 (`BaseView` / `BaseModal`)

為了讓自動錯誤處理機制生效，所有的 `discord.ui.View` 和 `discord.ui.Modal` 都**必須**繼承自專案提供的 `utils.base_view.BaseView` 和 `utils.base_modal.BaseModal`。

**修改前 (Before):**
```python
import discord

class MyView(discord.ui.View):
    # ...
```

**修改後 (After):**
```python
from utils.base_view import BaseView

class MyView(BaseView):
    # ...
```
**重點**:
- 確保 `View` 繼承自 `BaseView`。
- 確保 `Modal` 繼承自 `BaseModal`。

---

## 4. 其他要點

根據 `錯誤與異常處理開發規範.md`，還需注意：

- **`defer()` 的使用**：在指令或按鈕回呼函式中，應盡早呼叫 `await interaction.response.defer()`，以避免 Discord API 逾時。
- **不要捕捉 `BusinessError`**：UI 層**不應該**使用 `try...except BusinessError`，這會繞過框架的統一處理。
- **不要捕捉通用 `Exception`**：UI 層**絕對禁止**使用 `try...except Exception`，這會隱藏真正的 Bug，使除錯變得極其困難。

---

## 5. 驗證業務邏輯層的 `raise`

在刪除 UI 層的 `if` 判斷或 `try...except` 區塊時，**必須**執行以下檢查：

1.  **定位 Service**：找到被呼叫的 `service` 方法。
2.  **追蹤程式碼**：閱讀該 `service` 方法的程式碼，以及它可能呼叫的其他 `service` 或 `repository`。
3.  **確認拋錯路徑**：確保你刪除的那個錯誤條件，在 `service` 層的某處確實有一個對應的 `raise SomeBusinessError`。
4.  **新增拋錯**：如果 `service` 層沒有處理該錯誤條件（例如，它只是返回 `None` 或 `False`），你**必須**修改 `service` 層，加入 `if` 判斷和 `raise` 語句。

**範例檢查流程**:
- **UI 層修改**: 刪除了 `if not result: await interaction.response.send_message(...)`。
- **檢查步驟**:
    1.  查看 `result` 是從哪個 `service` 方法來的，例如 `some_service.do_stuff()`。
    2.  打開 `some_service.py`，找到 `do_stuff` 方法。
    3.  閱讀 `do_stuff`，發現它在失敗時 `return None`。
    4.  **採取行動**: 將 `return None` 修改為 `raise BusinessError("操作失敗，因為...")`。

只有這樣，才能保證重構後的程式碼在功能上是等價且安全的。

---

## 6. 已驗證模組

以下模組的 UI 層 (`cogs`, `views`) 已經過檢查，確認符合本重構指南的規範。

- **`gacha`**:
  - `cogs`: 全部檢查完畢，符合規範。
  - `views`: 全部檢查完畢，符合規範。
- **`pioneer`**:
  - `cogs`: 全部檢查完畢，符合規範。
  - `views`: 全部檢查完畢，符合規範。
- **`auxiliary`**:
  - `cogs`: 全部檢查完畢，符合規範。
- **`rpg_system`**:
  - `cogs`: 全部檢查完畢，符合規範。
  - `views`: 全部檢查完畢，符合規範。