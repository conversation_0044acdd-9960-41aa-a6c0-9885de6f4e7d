# 統一提示詞模組

本模組提供了統一的提示詞管理，解決了原先系統中提示詞不統一的問題。

## 設計理念

1. **統一風格**：所有系統的提示詞統一使用繁體中文，保持一致的表達風格和格式
2. **集中管理**：將所有提示詞集中在 `prompts` 目錄下管理，方便修改和維護
3. **功能分離**：不同系統的提示詞分開定義，便於獨立修改和擴展
4. **易於使用**：提供簡單的函數介面，讓其他模組輕鬆獲取提示詞

## 目錄結構

```
auxiliary/data/prompts/prompts/
├── __init__.py        # 導入所有模組元素
├── unified_prompts.py # 統一提示詞定義
└── README.md          # 說明文件
```

## 使用方法

### 1. 直接導入提示詞常數

```python
from auxiliary.data.prompts.prompts.unified_prompts import (
    BASE_SYSTEM_PROMPT,    # 基礎系統提示詞
    OUTFIT_SYSTEM_PROMPT,  # 穿搭評分系統提示詞
    OUTFIT_USER_PROMPT,    # 穿搭評分用戶提示詞
    QA_SYSTEM_PROMPT       # 問答系統提示詞
)
```

### 2. 使用函數獲取提示詞

```python
from auxiliary.data.prompts.prompts.unified_prompts import get_system_prompt, get_user_prompt

# 獲取系統提示詞
qa_prompt = get_system_prompt("qa")
outfit_prompt = get_system_prompt("outfit")
default_prompt = get_system_prompt()  # 默認返回基礎提示詞

# 獲取用戶提示詞
outfit_user_prompt = get_user_prompt("outfit")
```

### 3. 構建包含用戶歷史的提示詞

```python
from auxiliary.data.prompts.prompts.unified_prompts import build_prompt_with_history, OUTFIT_SYSTEM_PROMPT

# 獲取用戶歷史資料
user_history = "..."

# 構建包含歷史資料的提示詞
complete_prompt = build_prompt_with_history(OUTFIT_SYSTEM_PROMPT, user_history)
```

## 維護與擴展

### 添加新的提示詞

1. 在 `unified_prompts.py` 中定義新的提示詞常數
2. 在 `get_system_prompt` 或 `get_user_prompt` 函數中添加相應的選項
3. 在 `__init__.py` 中導出新添加的提示詞常數

### 修改現有提示詞

直接在 `unified_prompts.py` 文件中修改相應的提示詞常數即可，所有使用該提示詞的模組都將自動獲取更新後的內容。 