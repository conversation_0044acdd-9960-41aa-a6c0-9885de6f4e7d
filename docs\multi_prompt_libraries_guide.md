# 多套提示詞卡片庫系統使用指南

## 概述

多套提示詞卡片庫系統允許你為不同的故事主題使用不同的提示詞配置，大幅提升了系統的擴展性和靈活性。每個卡片庫都是一個獨立的文件，包含了特定的提示詞組合和配置。

## 系統架構

```
auxiliary/services/story/
├── prompt_libraries/           # 多卡片庫目錄
│   ├── __init__.py            # 管理器模組
│   ├── default_library.py     # 默認卡片庫（向後兼容）
│   ├── beilu_library.py       # beilu AI 專用卡片庫
│   └── [custom]_library.py    # 你的自定義卡片庫
├── prompts.py                 # 提示詞構建器（已更新）
└── themes/                    # 主題配置目錄
    ├── beilu_example.py       # beilu 卡片庫示例主題
    └── ...                    # 其他主題
```

## 核心功能

### 1. 卡片庫管理

- **自動發現**: 系統自動掃描並加載所有 `*_library.py` 文件
- **動態加載**: 根據需要動態加載指定的卡片庫
- **回退機制**: 如果指定卡片庫加載失敗，自動回退到默認庫
- **向後兼容**: 現有代碼無需修改即可使用

### 2. 主題集成

每個主題配置可以通過 `prompt_library` 字段指定使用的卡片庫：

```python
THEME_CONFIG = {
    "title": "我的主題",
    "description": "主題描述",
    "prompt_library": "beilu",  # 指定使用 beilu 卡片庫
    # ... 其他配置
}
```

## 使用方法

### 創建新的卡片庫

1. 在 `auxiliary/services/story/prompt_libraries/` 目錄下創建新文件，命名格式為 `[name]_library.py`

2. 定義 `PROMPT_LIBRARY` 變量：

```python
# my_custom_library.py
PROMPT_LIBRARY = [
    {
        "id": "my_custom_prompt",
        "name": "我的自定義提示",
        "role": "system",
        "content": "這是我的自定義提示內容...",
        "enabled": True,
    },
    # ... 更多卡片
]
```

### 在主題中使用卡片庫

```python
# themes/my_theme.py
THEME_CONFIG = {
    "title": "我的主題",
    "description": "使用自定義卡片庫的主題",
    "prompt_library": "my_custom",  # 指定卡片庫名稱（不含 _library.py）
    # ... 其他配置
}
```

### 程式化調用

```python
from auxiliary.services.story.prompts import build_prompt_messages

# 方法1: 通過主題配置自動選擇
messages = build_prompt_messages(
    theme_settings={"prompt_library": "beilu"},
    # ... 其他參數
)

# 方法2: 直接指定卡片庫
messages = build_prompt_messages(
    theme_settings=theme_settings,
    library_name="beilu",  # 直接指定
    # ... 其他參數
)
```

## 管理工具

### 命令行工具

使用提供的管理工具查看和管理卡片庫：

```bash
# 進入互動模式
python auxiliary/tools/prompt_library_manager.py

# 列出所有卡片庫
python auxiliary/tools/prompt_library_manager.py list

# 查看特定卡片庫
python auxiliary/tools/prompt_library_manager.py show beilu

# 查看特定卡片
python auxiliary/tools/prompt_library_manager.py card beilu core_system
```

### 編程接口

```python
from auxiliary.services.story.prompts import (
    get_available_prompt_libraries,
    get_prompt_library_info
)

# 獲取可用卡片庫列表
libraries = get_available_prompt_libraries()
print(libraries)  # ['beilu', 'default']

# 獲取詳細信息
info = get_prompt_library_info()
for name, details in info.items():
    print(f"{name}: {details['count']} 張卡片")
```

## 卡片格式規範

每張卡片必須包含以下字段：

```python
{
    "id": "unique_identifier",     # 唯一標識符（字符串）
    "name": "顯示名稱",            # 人類可讀名稱
    "role": "system|user|assistant|marker",  # 消息角色
    "content": "提示詞內容...",     # 具體內容，支持變量占位符
    "enabled": True,              # 是否啟用（布爾值）
    "condition": "optional_condition"  # 可選的觸發條件
}
```

### 角色類型說明

- **system**: 系統提示詞
- **user**: 用戶消息
- **assistant**: 助手響應
- **marker**: 動態內容標記（由代碼注入內容）

### 變量占位符

支持以下變量占位符：

- `{user_display_name}`: 用戶顯示名稱
- `{character_sheet}`: 角色設定表
- `{long_term_summary_block}`: 長期記憶摘要
- `{user_input}`: 用戶輸入
- `{status_block_rules}`: 狀態欄規則

## 現有卡片庫

### default (默認庡)

- **用途**: 向後兼容，基於原有 prompt_library.py
- **特點**: 適用於一般的故事創作場景
- **卡片數量**: 36 張（全部啟用）

### beilu (beilu AI 專用庫)

- **用途**: 專為 beilu AI 角色扮演系統設計
- **特點**: 包含角色扮演核心、思維鏈、NSFW 處理等專業功能
- **卡片數量**: 31 張（24 張啟用）

## 測試和驗證

運行測試腳本驗證系統功能：

```bash
python test_prompt_libraries.py
```

測試包括：
- ✅ 卡片庫加載功能
- ✅ 提示詞構建功能  
- ✅ 主題配置集成
- ✅ 向後兼容性

## 故障排除

### 常見問題

1. **卡片庫加載失敗**
   - 檢查文件名是否符合 `*_library.py` 格式
   - 確認文件中定義了 `PROMPT_LIBRARY` 變量
   - 檢查 Python 語法是否正確

2. **字符編碼問題**
   - 確保文件以 UTF-8 編碼保存
   - 在文件頂部添加 `# -*- coding: utf-8 -*-`

3. **主題無法使用指定卡片庫**
   - 檢查 `prompt_library` 字段拼寫是否正確
   - 確認對應的卡片庫文件存在

### 調試建議

1. 使用管理工具檢查卡片庫狀態
2. 查看控制台輸出的警告信息
3. 運行測試腳本診斷問題

## 最佳實踐

1. **命名規範**: 卡片庫文件使用描述性名稱，如 `roleplay_library.py`
2. **模組化設計**: 將相關功能的卡片組織在同一個庫中
3. **文檔說明**: 為每個自定義卡片庫添加詳細的文檔註釋
4. **測試驗證**: 創建新卡片庫後及時測試功能
5. **版本控制**: 對卡片庫文件進行版本控制管理

## 擴展性

系統設計支持無限擴展：

- **新增卡片庫**: 只需創建新的 `*_library.py` 文件
- **動態切換**: 可以根據用戶需求動態切換不同的卡片庫
- **組合使用**: 未來可以支持多個卡片庫的組合使用
- **插件系統**: 可以基於此架構開發插件系統

## 總結

多套提示詞卡片庫系統提供了：

- 🔧 **強化擴展性**: 支持無限數量的自定義卡片庫
- 📁 **文件區分**: 每個卡片庫獨立文件，便於管理
- 🎯 **靈活選擇**: 每個主題可以選擇使用不同的卡片庫
- 🔄 **向後兼容**: 現有代碼無需修改
- 🛠️ **管理工具**: 提供完整的管理和調試工具

通過這個系統，你可以為不同的使用場景創建專門的提示詞配置，大幅提升系統的靈活性和可維護性。