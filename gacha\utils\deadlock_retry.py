"""
死鎖重試機制工具
提供自動重試死鎖的裝飾器和函數
"""

import asyncio
import logging
from functools import wraps
from typing import Awaitable, Callable, TypeVar

import asyncpg

logger = logging.getLogger(__name__)

T = TypeVar("T")


async def with_deadlock_retry(
    func: Callable[..., Awaitable[T]],
    max_retries: int = 3,
    base_delay: float = 0.1,
    *args,
    **kwargs,
) -> T:
    """
    執行函數並在遇到死鎖時自動重試

    Args:
        func: 要執行的異步函數
        max_retries: 最大重試次數
        base_delay: 基礎延遲時間(秒)
        *args, **kwargs: 傳遞給函數的參數

    Returns:
        函數執行結果

    Raises:
        DeadlockRetryError: 重試次數用盡後仍然失敗
    """
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            return await func(*args, **kwargs)
        except asyncpg.DeadlockDetectedError as e:
            last_error = e
            if attempt == max_retries:
                logger.error(
                    "死鎖重試失敗，已達到最大重試次數 %d: %s", max_retries, str(e)
                )
                # 使用Python原生異常
                raise RuntimeError(f"死鎖重試失敗，已嘗試 {max_retries + 1} 次") from e

            # 指數退避延遲
            delay = base_delay * (2**attempt)
            logger.warning(
                "檢測到死鎖，第 %d 次重試，延遲 %.2f 秒: %s", attempt + 1, delay, str(e)
            )
            await asyncio.sleep(delay)
        except Exception as e:
            # 非死鎖異常直接拋出
            raise e

    # 理論上不會到達這裡
    raise RuntimeError("未知錯誤，重試失敗") from last_error


def deadlock_retry(max_retries: int = 3, base_delay: float = 0.1):
    """
    死鎖重試裝飾器

    Args:
        max_retries: 最大重試次數
        base_delay: 基礎延遲時間(秒)
    """

    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            return await with_deadlock_retry(
                func, max_retries, base_delay, *args, **kwargs
            )

        return wrapper

    return decorator


# 常用的重試配置
async def quick_retry(func: Callable[..., Awaitable[T]], *args, **kwargs) -> T:
    """快速重試：最多2次，延遲50ms"""
    return await with_deadlock_retry(
        func, *args, max_retries=2, base_delay=0.05, **kwargs
    )


async def standard_retry(func: Callable[..., Awaitable[T]], *args, **kwargs) -> T:
    """標準重試：最多3次，延遲100ms"""
    return await with_deadlock_retry(
        func, *args, max_retries=3, base_delay=0.1, **kwargs
    )


async def patient_retry(func: Callable[..., Awaitable[T]], *args, **kwargs) -> T:
    """耐心重試：最多5次，延遲200ms"""
    return await with_deadlock_retry(
        func, *args, max_retries=5, base_delay=0.2, **kwargs
    )
