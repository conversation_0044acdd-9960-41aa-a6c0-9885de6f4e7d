"""
戰鬥單位（Combatant）領域模型
"""

import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple

from utils.logger import logger

from .battle import Battle
from .shield_instance import ShieldInstance
from .skill_instance import SkillInstance
from .status_effect_instance import StatusEffectInstance


@dataclass
class Combatant:
    """戰鬥單位領域模型"""

    instance_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    definition_id: str = ""
    name: str = ""
    is_player_side: bool = True
    rpg_level: int = 1
    star_level: int = 0
    position: int = 0
    skill_order_preference: List[str] = field(default_factory=list)
    primary_attack_skill: Optional[SkillInstance] = None
    active_skills: List[SkillInstance] = field(default_factory=list)
    innate_passive: Optional[SkillInstance] = None
    common_passives: List[SkillInstance] = field(default_factory=list)
    max_hp: int = 100
    current_hp: int = 100
    max_mp: int = 50
    current_mp: int = 50
    consecutive_hits: int = 0
    current_stats: Dict[str, float] = field(default_factory=dict)
    status_effects: List[StatusEffectInstance] = field(default_factory=list)
    was_attacked_last_turn: bool = False
    last_action_tags: List[str] = field(default_factory=list)
    skill_target_hit_counters: Dict[str, Dict[str, int]] = field(default_factory=dict)
    shields: List["ShieldInstance"] = field(default_factory=list)

    def __post_init__(self):
        """初始化後處理"""
        if not self.current_stats:
            self.current_stats = {
                "atk": 10.0,
                "def": 10.0,
                "spd": 10.0,
                "crit_rate": 0.05,
                "crit_dmg_multiplier": 1.5,
                "accuracy": 0.95,
                "evasion": 0.05,
            }

    async def calculate_final_stats(
        self, config_loader, attribute_calculator=None
    ) -> None:
        """
        根據戰鬥單位自身的屬性和裝備的技能等級，計算最終戰鬥屬性
        """
        from ..services import attribute_calculator as ac

        combatant_type = "CARD" if self.is_player_side else "MONSTER"
        calculated_attributes = await ac.calculate_attributes(
            combatant_definition_id=self.definition_id,
            combatant_type=combatant_type,
            rpg_level=self.rpg_level,
            star_level=self.star_level,
        )

        if calculated_attributes:
            self.max_hp = int(calculated_attributes.get("max_hp", 100))
            self.max_mp = int(calculated_attributes.get("max_mp", 50))
            self.current_hp = self.max_hp
            self.current_mp = self.max_mp
            self.current_stats.update(
                {
                    "atk": calculated_attributes.get(
                        "atk",
                        calculated_attributes.get(
                            "patk", calculated_attributes.get("matk", 10.0)
                        ),
                    ),
                    "def": calculated_attributes.get(
                        "def",
                        max(
                            calculated_attributes.get("pdef", 0),
                            calculated_attributes.get("mdef", 0),
                        )
                        or 10.0,
                    ),
                    "spd": calculated_attributes.get("spd", 10.0),
                    "crit_rate": calculated_attributes.get("crit_rate", 0.05),
                    "crit_dmg_multiplier": calculated_attributes.get(
                        "crit_dmg_multiplier", 1.5
                    ),
                    "accuracy": calculated_attributes.get("accuracy", 0.95),
                    "evasion": calculated_attributes.get("evasion", 0.05),
                    "mp_regen_per_turn": calculated_attributes.get(
                        "mp_regen_per_turn", 0.0
                    ),
                }
            )

    async def _get_effect_applier_and_configs(
        self,
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        獲取 EffectApplier 和配置的統一方法
        """
        from rpg_system.battle_system.utils.effect_applier_factory import (
            create_effect_applier_with_shared_config,
        )

        eff_applier = await create_effect_applier_with_shared_config()
        all_configs = await eff_applier.config_loader.get_all_configs()
        return eff_applier, all_configs

    async def _get_status_effect_definition(self, effect_id: str) -> Dict[str, Any]:
        """
        獲取狀態效果定義的統一方法
        """
        _, all_configs = await self._get_effect_applier_and_configs()
        status_effects_config = all_configs.get("status_effects", {})
        return status_effects_config.get(effect_id, {})

    async def take_damage(
        self,
        amount: float,
        damage_type: str,
        is_crit: bool,
        battle_context: "Battle",
    ) -> float:
        """
        處理傷害
        """
        incoming_damage = max(0, amount)
        old_hp = self.current_hp
        remaining_damage = await self._apply_shield_absorption(
            incoming_damage, damage_type, battle_context
        )
        actual_hp_damage = remaining_damage
        self.current_hp = max(0, self.current_hp - int(actual_hp_damage))
        self.was_attacked_last_turn = True
        if old_hp > 0 and self.current_hp <= 0:
            if battle_context and hasattr(battle_context, "trigger_death_event"):
                await battle_context.trigger_death_event(self)
        return incoming_damage

    async def heal(self, amount: float, is_crit: bool, battle_context: Any) -> float:
        """
        處理治療
        """
        actual_heal = max(0, amount)
        old_hp = self.current_hp
        self.current_hp = min(self.max_hp, self.current_hp + int(actual_heal))
        actual_heal = self.current_hp - old_hp
        return actual_heal

    def consume_mp(self, amount: int) -> bool:
        """
        消耗法力值
        """
        if self.current_mp >= amount:
            self.current_mp = max(0, self.current_mp - amount)
            return True
        return False

    def restore_mp(self, amount: int) -> int:
        """
        恢復法力值
        """
        old_mp = self.current_mp
        self.current_mp = min(self.max_mp, self.current_mp + amount)
        return self.current_mp - old_mp

    async def add_status_effect(
        self, status_effect_instance: StatusEffectInstance, battle_context: Any
    ) -> None:
        """
        添加狀態效果
        """
        existing_effect = None
        for effect in self.status_effects:
            if effect.status_effect_id == status_effect_instance.status_effect_id:
                existing_effect = effect
                break
        if existing_effect:
            await self._handle_status_effect_stacking(
                existing_effect, status_effect_instance
            )
        else:
            self.status_effects.append(status_effect_instance)
            await self._trigger_status_effect_on_apply(
                status_effect_instance, battle_context
            )

    async def _trigger_status_effect_on_apply(
        self, status_effect_instance: StatusEffectInstance, battle_context: Any
    ) -> None:
        """觸發狀態效果施加時的效果"""
        eff_applier, _ = await self._get_effect_applier_and_configs()
        status_definition = await self._get_status_effect_definition(
            status_effect_instance.status_effect_id
        )
        effect_definitions_on_apply = status_definition.get(
            "effect_definitions_on_apply", []
        )
        if effect_definitions_on_apply:
            caster = (
                battle_context.get_combatant_by_id(status_effect_instance.caster_id)
                if status_effect_instance.caster_id
                else self
            )
            if not caster:
                caster = self
            await eff_applier.apply_effect_definitions(
                caster=caster,
                initial_targets=[self],
                effect_definitions=effect_definitions_on_apply,
                battle_context=battle_context,
                source_skill_tags=[],
                source_skill_instance=None,
                custom_vars_from_source={},
            )

    async def remove_status_effect(self, status_effect_id: str) -> bool:
        """
        移除狀態效果
        """
        for i, effect in enumerate(self.status_effects):
            if effect.status_effect_id == status_effect_id:
                self.status_effects.pop(i)
                return True
        return False

    async def get_next_action(self) -> Tuple[str, List[str]]:
        """
        根據技能優先級順序獲取下一個可用行動
        """
        for skill_id in self.skill_order_preference:
            skill_instance = self.get_skill_instance(skill_id)
            if skill_instance and skill_instance.is_usable(self.current_mp):
                return skill_id, []
        if self.primary_attack_skill:
            return self.primary_attack_skill.skill_id, []
        return "", []

    async def apply_turn_start_effects(self, battle_context: Any) -> None:
        """應用回合開始時觸發的被動和狀態效果"""
        self.was_attacked_last_turn = False
        await self.tick_status_effects(battle_context)
        await self.tick_shields(battle_context)
        self.tick_cooldowns()

    async def apply_turn_end_effects(self, battle_context: Any) -> None:
        """應用回合結束時觸發的被動和狀態效果"""
        await self.tick_status_effects_at_turn_end(battle_context)

    def tick_cooldowns(self) -> None:
        """更新所有主動技能的冷卻時間"""
        for skill in self.active_skills:
            if skill.current_cooldown > 0:
                skill.current_cooldown -= 1
        if self.primary_attack_skill and self.primary_attack_skill.current_cooldown > 0:
            self.primary_attack_skill.current_cooldown -= 1

    async def _process_status_effects_by_timing(
        self, timing: str, battle_context: Any
    ) -> None:
        """
        統一處理不同時機的狀態效果
        """
        effects_to_remove = []
        eff_applier, all_configs = await self._get_effect_applier_and_configs()
        for effect in self.status_effects:
            status_definition = effect.get_definition(all_configs)
            should_trigger = False
            if timing == "turn_start" and status_definition.get(
                "tick_at_turn_start", False
            ):
                should_trigger = True
            elif timing == "turn_end" and status_definition.get(
                "tick_at_turn_end", False
            ):
                should_trigger = True
            if should_trigger:
                await self._trigger_status_effect_tick(
                    effect, status_definition, eff_applier, battle_context
                )
            if timing == "turn_start":
                if effect.duration_turns > 0:
                    effect.duration_turns -= 1
                    if effect.duration_turns <= 0:
                        effects_to_remove.append(effect)
        for effect in effects_to_remove:
            self.status_effects.remove(effect)

    async def tick_status_effects(self, battle_context: Any) -> None:
        """更新狀態效果的持續時間並觸發其周期性效果"""
        await self._process_status_effects_by_timing("turn_start", battle_context)

    async def _trigger_status_effect_tick(
        self, effect, status_definition, effect_applier, battle_context
    ):
        """觸發狀態效果的周期性效果"""
        effect_definitions_per_tick = status_definition.get(
            "effect_definitions_per_tick", []
        )
        if effect_definitions_per_tick:
            caster = (
                battle_context.get_combatant_by_id(effect.caster_id)
                if effect.caster_id
                else self
            )
            if not caster:
                caster = self
            await effect_applier.apply_effect_definitions(
                caster=caster,
                initial_targets=[self],
                effect_definitions=effect_definitions_per_tick,
                battle_context=battle_context,
                source_skill_tags=[],
                source_skill_instance=None,
                custom_vars_from_source={},
            )

    async def tick_status_effects_at_turn_end(self, battle_context: Any) -> None:
        """處理回合結束時觸發的狀態效果"""
        await self._process_status_effects_by_timing("turn_end", battle_context)

    def is_alive(self) -> bool:
        """判斷是否存活"""
        return self.current_hp > 0

    async def can_act(self) -> bool:
        """判斷是否能夠行動"""
        if not self.is_alive():
            return False
        try:
            _, all_configs = await self._get_effect_applier_and_configs()
            status_effects_config = all_configs.get("status_effects", {})
            for effect in self.status_effects:
                status_definition = status_effects_config.get(
                    effect.status_effect_id, {}
                )
                if "CONTROL" in status_definition.get("special_flags", []):
                    return False
        except Exception as e:
            logger.warning("檢查can_act時發生錯誤: %s", e)
        return True

    async def _apply_shield_absorption(
        self, damage: float, damage_type: str, battle_context: Any
    ) -> float:
        """
        應用護盾吸收傷害
        """
        remaining_damage = damage
        shields_to_remove = []
        for shield in self.shields:
            if shield.is_depleted() or shield.is_expired():
                shields_to_remove.append(shield)
                continue
            absorbed, remaining_damage = shield.process_damage_absorption(
                remaining_damage, damage_type
            )
            if absorbed > 0:
                if battle_context and hasattr(battle_context, "trigger_event"):
                    await battle_context.trigger_event(
                        "ON_SHIELD_ABSORBED",
                        {
                            "target_id": self.instance_id,
                            "shield_id": shield.instance_id,
                            "absorbed_damage": absorbed,
                            "damage_type": damage_type,
                            "remaining_shield": shield.current_value,
                        },
                    )
            if shield.is_depleted():
                shields_to_remove.append(shield)
            if remaining_damage <= 0:
                break
        for shield in shields_to_remove:
            self.shields.remove(shield)
            if battle_context and hasattr(battle_context, "trigger_event"):
                await battle_context.trigger_event(
                    "ON_SHIELD_EXPIRED",
                    {"target_id": self.instance_id, "shield_id": shield.instance_id},
                )
        return max(0, remaining_damage)

    async def add_shield(
        self, shield_instance: ShieldInstance, battle_context: Any
    ) -> None:
        """
        添加護盾
        """
        existing_shield = None
        for shield in self.shields:
            if shield.shield_type == shield_instance.shield_type:
                existing_shield = shield
                break
        if existing_shield:
            existing_shield.current_value = max(
                existing_shield.current_value, shield_instance.current_value
            )
            existing_shield.max_value = max(
                existing_shield.max_value, shield_instance.max_value
            )
            existing_shield.duration_turns = max(
                existing_shield.duration_turns, shield_instance.duration_turns
            )
        else:
            self.shields.append(shield_instance)
        if battle_context and hasattr(battle_context, "trigger_event"):
            await battle_context.trigger_event(
                "ON_SHIELD_APPLIED",
                {
                    "target_id": self.instance_id,
                    "shield_id": shield_instance.instance_id,
                    "shield_type": shield_instance.shield_type,
                    "shield_value": shield_instance.current_value,
                    "duration_turns": shield_instance.duration_turns,
                },
            )

    async def remove_shields(
        self, shield_filter: str = "all", battle_context: Any = None
    ) -> int:
        """
        移除護盾
        """
        removed_count = 0
        shields_to_remove = []
        for shield in self.shields:
            should_remove = False
            if shield_filter == "all":
                should_remove = True
            elif shield_filter == "shield_effects":
                should_remove = True
            elif shield_filter == shield.shield_type:
                should_remove = True
            if should_remove:
                shields_to_remove.append(shield)
        for shield in shields_to_remove:
            self.shields.remove(shield)
            removed_count += 1
            if battle_context and hasattr(battle_context, "trigger_event"):
                await battle_context.trigger_event(
                    "ON_SHIELD_REMOVED",
                    {
                        "target_id": self.instance_id,
                        "shield_id": shield.instance_id,
                        "shield_type": shield.shield_type,
                    },
                )
        return removed_count

    async def tick_shields(self, battle_context: Any) -> None:
        """更新護盾的持續時間"""
        shields_to_remove = []
        for shield in self.shields:
            shield.tick_duration()
            if shield.is_expired():
                shields_to_remove.append(shield)
        for shield in shields_to_remove:
            self.shields.remove(shield)
            if battle_context and hasattr(battle_context, "trigger_event"):
                await battle_context.trigger_event(
                    "ON_SHIELD_EXPIRED",
                    {
                        "target_id": self.instance_id,
                        "shield_id": shield.instance_id,
                        "shield_type": shield.shield_type,
                    },
                )

    def get_total_shield_value(self) -> float:
        """獲取總護盾值"""
        return sum(
            shield.current_value for shield in self.shields if not shield.is_depleted()
        )

    def has_shield_type(self, shield_type: str) -> bool:
        """檢查是否有指定類型的護盾"""
        return any(
            shield.shield_type == shield_type and not shield.is_depleted()
            for shield in self.shields
        )

    def get_skill_instance(self, skill_id: str) -> Optional[SkillInstance]:
        """根據技能ID獲取技能實例"""
        for skill in self.active_skills:
            if skill.skill_id == skill_id:
                return skill
        if self.primary_attack_skill and self.primary_attack_skill.skill_id == skill_id:
            return self.primary_attack_skill
        return None

    async def _handle_status_effect_stacking(
        self, existing: StatusEffectInstance, new: StatusEffectInstance
    ) -> None:
        """處理狀態效果疊加邏輯"""
        existing.duration_turns = new.duration_turns
