"""
餘額歷史記錄存儲庫模組
"""

from decimal import Decimal
from typing import Optional

import asyncpg

from gacha.exceptions import DatabaseOperationError
from gacha.repositories._base_repo import execute_query
from utils.logger import logger

TABLE_NAME = "balance_history"


async def create_balance_history_record(
    user_id: int,
    change_amount: int,
    balance_before: int,
    balance_after: int,
    transaction_type: str,
    reason: Optional[str],
    connection: asyncpg.Connection,
) -> None:
    """
    在 balance_history 表中創建一條新的記錄。

    Args:
        user_id: 用戶 ID
        change_amount: 變動金額
        balance_before: 變動前餘額
        balance_after: 變動後餘額
        transaction_type: 交易類型
        reason: 交易原因
        connection: 必須提供一個正在進行中的 asyncpg 連接
    """
    if not connection:
        raise ValueError(
            "create_balance_history_record 必須在一個現有的事務連接中執行。"
        )

    query = f"""
        INSERT INTO {TABLE_NAME} (user_id, change_amount, balance_before, balance_after, transaction_type, reason)
        VALUES ($1, $2, $3, $4, $5, $6)
    """
    try:
        # 注意：我們將整數轉換為 Decimal 以匹配資料庫模式
        await execute_query(
            query,
            (
                user_id,
                Decimal(change_amount),
                Decimal(balance_before),
                Decimal(balance_after),
                transaction_type,
                reason,
            ),
            connection=connection,
        )
    except Exception as e:
        logger.error(
            "在為用戶 %s 創建餘額歷史記錄時失敗: %s",
            user_id,
            e,
            exc_info=True,
        )
        # 重新拋出異常，以便事務可以回滾
        raise DatabaseOperationError(f"無法為用戶 {user_id} 插入餘額歷史記錄") from e
