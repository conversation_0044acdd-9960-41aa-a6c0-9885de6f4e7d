from typing import List

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.exceptions import PioneerError
from pioneer.modules import action_module
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


def get_gather_action_choices() -> List[app_commands.Choice[str]]:
    """
    從 GameDataLoader 生成採集動作的 choices 選項

    Returns:
        List[app_commands.Choice[str]]: 採集動作選項列表
    """
    choices = []
    try:
        if game_data:
            actions = game_data.get_all_actions()
            for action_id, action_config in actions.items():
                if action_config.type == "gather":
                    choices.append(
                        app_commands.Choice(name=action_config.name, value=action_id)
                    )

        if not choices:
            logger.warning("在 actions.yaml 中未找到任何 'gather' 類型的動作。")
            # 可以提供一個空的或者提示性的選項
            # choices.append(app_commands.Choice(name="無可用採集動作", value="no_action"))

    except Exception as e:
        logger.error("從 game_data 生成採集動作 choices 時發生錯誤: %s", e)

    return choices


class GatherCog(commands.Cog):
    """開拓者採集指令"""

    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(
        name="gather", description="執行採集動作 - 伐木、採礦等資源收集"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(action="選擇要執行的採集動作")
    @app_commands.choices(action=get_gather_action_choices())
    async def gather(self, interaction: discord.Interaction, action: str):
        """執行採集動作"""
        await interaction.response.defer(thinking=False)

        user_id = interaction.user.id
        await repositories.create_pioneer_profile(user_id)
        result = await action_module.execute_action(user_id, action)

        if result.success:
            embed = SuccessEmbed(title="🌲 採集成功", description=result.message)

            if result.rewards:
                reward_text = []
                for reward in result.rewards:
                    if reward["type"] == "item":
                        item_name = reward["item_id"]
                        if game_data:
                            item_config = game_data.get_item_config(reward["item_id"])
                            if item_config:
                                item_name = item_config.name
                        reward_text.append(f"• {item_name} x{reward['quantity']}")

                if reward_text:
                    embed.add_field(
                        name="獲得物品", value="\n".join(reward_text), inline=False
                    )

            if result.xp_gained:
                xp_text = []
                for skill_id, xp in result.xp_gained.items():
                    skill_name = skill_id
                    if game_data:
                        skill_name = game_data.get_skill_name(skill_id)
                    xp_text.append(f"• {skill_name}: +{xp} XP")

                if xp_text:
                    embed.add_field(
                        name="獲得經驗", value="\n".join(xp_text), inline=False
                    )
            await interaction.followup.send(embed=embed)
        else:
            raise PioneerError(result.message)


async def setup(bot):
    """設置 Cog"""
    await bot.add_cog(GatherCog(bot))
