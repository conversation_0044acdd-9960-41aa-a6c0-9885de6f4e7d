"""
開發工具 COG - 僅限測試伺服器使用
提供標準Extension重載功能
"""

import os
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Optional

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button

from gacha.exceptions import BusinessError
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# 測試伺服器ID - 限制開發功能只在此伺服器可見
TEST_GUILD_ID = 1067486117894574221


class GuildRankingView(BaseView):
    def __init__(self, bot, service, user_id, timeout=180):
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.service = service
        self.current_page = 0
        self.items_per_page = 10
        self.guild_stats = []

    async def update_and_send(self, interaction: discord.Interaction):
        self.guild_stats = await self.service.get_guild_stats(
            limit=100
        )  # 獲取足夠的數據
        if not self.guild_stats:
            embed = discord.Embed(
                title="📊 伺服器指令使用排行榜",
                description="目前沒有伺服器統計數據",
                color=discord.Color.orange(),
            )
            await interaction.followup.send(embed=embed)
            return

        self.total_pages = (
            len(self.guild_stats) + self.items_per_page - 1
        ) // self.items_per_page
        embed = self.create_embed()
        self.update_buttons()
        await interaction.followup.send(embed=embed, view=self)

    def create_embed(self):
        start_index = self.current_page * self.items_per_page
        end_index = start_index + self.items_per_page
        page_stats = self.guild_stats[start_index:end_index]

        embed = discord.Embed(
            title="🏆 伺服器指令使用排行榜",
            description="顯示伺服器排名",
            color=discord.Color.gold(),
        )

        ranking_text = ""
        for i, guild_stat in enumerate(page_stats, start_index + 1):
            guild_id = guild_stat["guild_id"]
            total_commands = guild_stat["total_commands"]
            unique_users = guild_stat["unique_users"]
            success_rate = guild_stat["success_rate"]

            try:
                guild = self.bot.get_guild(guild_id)
                guild_name = guild.name if guild else "未知伺服器"
            except Exception:
                guild_name = "未知伺服器"

            if len(guild_name) > 25:
                guild_name = guild_name[:22] + "..."

            rank_icon = (
                "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            )
            ranking_text += f"{rank_icon} **{guild_name}**\n"
            ranking_text += (
                f"   指令: {total_commands:,} | 用戶: {unique_users:,} | "
                f"成功率: {success_rate:.1f}%\n"
            )
            ranking_text += f"   ID: `{guild_id}`\n\n"

        embed.add_field(
            name=f"排行榜 (第 {self.current_page + 1}/{self.total_pages} 頁)",
            value=ranking_text or "沒有更多數據了。",
            inline=False,
        )

        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M UTC")
        embed.set_footer(text=f"統計時間: {timestamp}")
        return embed

    def update_buttons(self):
        # 類型斷言來幫助 pyright 理解這些是按鈕
        if len(self.children) >= 2:
            prev_button = self.children[0]
            next_button = self.children[1]
            if isinstance(prev_button, discord.ui.Button):
                prev_button.disabled = self.current_page == 0
            if isinstance(next_button, discord.ui.Button):
                next_button.disabled = self.current_page >= self.total_pages - 1

    @discord.ui.button(label="上一頁", style=discord.ButtonStyle.blurple)
    async def previous_page(self, interaction: discord.Interaction, button: Button):
        self.current_page -= 1
        embed = self.create_embed()
        self.update_buttons()
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="下一頁", style=discord.ButtonStyle.blurple)
    async def next_page(self, interaction: discord.Interaction, button: Button):
        self.current_page += 1
        embed = self.create_embed()
        self.update_buttons()
        await interaction.response.edit_message(embed=embed, view=self)


class DevToolsCog(commands.Cog, name="開發工具"):
    """開發工具 COG - 標準Extension重載"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("DevToolsCog 正在初始化...")

        # 動態掃描可重載的Extension列表
        self.available_extensions = self._scan_available_cogs()

        # 啟動市場統計服務
        logger.info("DevToolsCog 準備啟動市場統計服務...")
        self.bot.loop.create_task(self.start_market_stats_service())

    async def start_market_stats_service(self):
        """異步啟動市場統計服務"""
        try:
            logger.info("DevToolsCog 開始導入市場統計維護服務...")
            from gacha.services import market_stats_maintenance_service

            logger.info("DevToolsCog 成功導入市場統計維護服務，準備啟動...")

            # 檢查服務狀態
            is_active_before = await market_stats_maintenance_service.is_active()
            logger.info("啟動前服務狀態: %s", is_active_before)

            await market_stats_maintenance_service.start()

            # 檢查啟動後狀態
            is_active_after = await market_stats_maintenance_service.is_active()
            queue_size = await market_stats_maintenance_service.get_queue_size()
            logger.info("啟動後服務狀態: %s, 隊列大小: %s", is_active_after, queue_size)

            logger.info("✅ MarketStatsMaintenanceService 已由 DevToolsCog 啟動")
        except Exception as e:
            logger.error(
                "❌ DevToolsCog 啟動 MarketStatsMaintenanceService 失敗: %s",
                e,
                exc_info=True,
            )

    async def cog_unload(self):
        """在 Cog 卸載時停止服務"""
        logger.info("DevToolsCog 正在卸載，準備停止市場統計服務...")
        try:
            from gacha.services import market_stats_maintenance_service

            await market_stats_maintenance_service.stop()
            logger.info("✅ MarketStatsMaintenanceService 已成功停止")
        except Exception as e:
            logger.error(
                "❌ DevToolsCog 卸載時停止 MarketStatsMaintenanceService 失敗: %s",
                e,
                exc_info=True,
            )

    def _scan_available_cogs(self) -> List[str]:
        """掃描所有可用的 cog 文件並返回模組路徑列表"""
        available_cogs = []

        # 定義要掃描的目錄
        cog_directories = [
            ("gacha/cogs", "gacha.cogs"),
            ("auxiliary/cogs", "auxiliary.cogs"),
            ("rpg_system/cogs", "rpg_system.cogs"),
            ("pioneer/cogs", "pioneer.cogs"),
        ]

        for dir_path, module_prefix in cog_directories:
            if os.path.exists(dir_path):
                for file_path in Path(dir_path).glob("*.py"):
                    if file_path.name != "__init__.py":
                        cog_name = file_path.stem
                        module_path = f"{module_prefix}.{cog_name}"
                        available_cogs.append(module_path)

        return sorted(available_cogs)

    def _get_all_available_extensions(self) -> List[str]:
        """獲取所有可用的extensions，包括當前已載入和可掃描到的"""
        all_extensions = set()

        # 添加當前已載入的extensions
        all_extensions.update(self.bot.extensions.keys())

        # 添加可掃描到的extensions
        all_extensions.update(self.available_extensions)

        # 過濾掉不相關的extensions（只保留我們系統的）
        system_extensions = []
        for ext in all_extensions:
            if ext.startswith(("gacha.cogs", "auxiliary.cogs", "rpg_system.cogs")):
                system_extensions.append(ext)

        return sorted(system_extensions)

    async def _check_dev_permission(self, interaction: discord.Interaction) -> bool:
        """檢查用戶是否有開發工具權限"""
        # 檢查是否為機器人擁有者
        if await self.bot.is_owner(interaction.user):
            return True

        # 檢查是否為管理員（在測試伺服器中）
        if (
            isinstance(interaction.user, discord.Member)
            and interaction.user.guild_permissions.administrator
        ):
            return True

        raise BusinessError(
            "❌ 你沒有權限使用開發工具。\n只有機器人擁有者或管理員可以使用此功能。"
        )

    async def _send_error_embed(
        self, interaction: discord.Interaction, title: str, description: str
    ):
        """發送統一格式的錯誤 embed"""
        embed = discord.Embed(
            title=f"❌ {title}", description=description, color=discord.Color.red()
        )
        embed.set_footer(text=f"執行者: {interaction.user.display_name}")
        await interaction.followup.send(embed=embed, ephemeral=True)

    async def _send_success_embed(
        self,
        interaction: discord.Interaction,
        title: str,
        description: str,
        fields: list[dict] | None = None,
    ):
        """發送統一格式的成功 embed"""
        embed = SuccessEmbed(title=f"✅ {title}", description=description)
        if fields:
            for field in fields:
                embed.add_field(**field)
        embed.set_footer(text=f"執行者: {interaction.user.display_name}")
        await interaction.followup.send(embed=embed, ephemeral=True)

    async def _reload_cog_for_service(
        self,
        interaction: discord.Interaction,
        service_name: str,
        cog_to_reload: str,
        success_message: str,
    ):
        """Helper to reload a cog for a specific service."""
        logger.info("偵測到 %s 重載請求，將轉為重載 %s...", service_name, cog_to_reload)
        await self.bot.reload_extension(cog_to_reload)
        await self._send_success_embed(interaction, "服務重載成功", success_message)

    async def _handle_module_reload(
        self, interaction: discord.Interaction, module_name: str
    ):
        """處理任意模組的重載，增加對特定服務的特殊處理"""
        await self._check_dev_permission(interaction)

        logger.info("請求重載模組: %s", module_name)

        # --- Special handlers for services that require a cog reload ---
        service_to_cog_map = {
            "gacha.services.market_stats_maintenance_service": (
                "市場統計服務",
                "auxiliary.cogs.dev_tools_cog",
                "已透過重載 `DevToolsCog` 來安全地重啟市場統計服務。",
            ),
            "gacha.services.draw_notifier": (
                "抽卡通知服務",
                "gacha.cogs.draw_cog",
                "已透過重載 `DrawCog` 來安全地重啟抽卡通知服務。",
            ),
            "gacha.services.price_update_service": (
                "股票相關服務",
                "gacha.cogs.stock_cog",
                "已透過重載 `StockCog` 來安全地重啟股票相關服務。",
            ),
            "gacha.services.scheduled_task_orchestrator": (
                "股票相關服務",
                "gacha.cogs.stock_cog",
                "已透過重載 `StockCog` 來安全地重啟股票相關服務。",
            ),
            "gacha.services.highest_star_maintenance_service": (
                "最高星級維護服務",
                "gacha.cogs.collection_cog",
                "已透過重載 `CollectionCog` 來安全地重啟最高星級維護服務。",
            ),
        }

        if module_name in service_to_cog_map:
            service_name, cog_to_reload, success_message = service_to_cog_map[
                module_name
            ]
            await self._reload_cog_for_service(
                interaction, service_name, cog_to_reload, success_message
            )
            return

        # --- Special handler for PlaywrightManager ---
        if module_name == "utils.playwright_manager":
            await self._reload_playwright_manager(interaction)
            return

        # --- Generic module reload ---
        import importlib
        import sys

        if module_name in sys.modules:
            importlib.reload(sys.modules[module_name])
            logger.info("成功重載模組: %s", module_name)
            await self._send_success_embed(
                interaction, "模組重載成功", f"成功重載模組 `{module_name}`"
            )
        else:
            raise BusinessError(f"模組 `{module_name}` 未載入，無法重載")

    # 舊的 _reload_market_stats_service 不再需要，
    # 因為邏輯已轉移到 cog_unload 和 __init__ 中
    # async def _reload_market_stats_service(self, interaction: discord.Interaction):
    #     ...

    async def _reload_playwright_manager(self, interaction: discord.Interaction):
        """特殊處理 PlaywrightManager 的重載"""
        try:
            import importlib
            import sys

            logger.info("開始重載 PlaywrightManager...")

            # 1. 先關閉現有的 PlaywrightManager 實例
            from utils import playwright_manager

            logger.info("關閉現有的 PlaywrightManager 實例...")
            try:
                await playwright_manager.close()
            except Exception as e:
                logger.warning("關閉 PlaywrightManager 時出錯: %s", e)

            # 2. 重載模組
            if "utils.playwright_manager" in sys.modules:
                importlib.reload(sys.modules["utils.playwright_manager"])
                logger.info("成功重載 utils.playwright_manager 模組")

            # 3. 重新初始化 PlaywrightManager
            from utils import playwright_manager

            # 重新初始化
            await playwright_manager.initialize()
            logger.info("PlaywrightManager 重新初始化完成")

            # 4. 重新初始化相關服務
            await self._reinitialize_playwright_services()

            embed = discord.Embed(
                title="✅ PlaywrightManager 重載成功",
                description=(
                    "PlaywrightManager 已成功重載並重新初始化\n\n"
                    "**現在 Playwright 相關功能應該能正常工作！**"
                ),
                color=discord.Color.green(),
            )
            embed.add_field(
                name="🔄 執行的操作",
                value=(
                    "```\n1. 關閉舊的 PlaywrightManager\n2. 重載模組代碼\n"
                    "3. 清除單例實例\n4. 重新初始化瀏覽器\n5. 重新初始化相關服務\n```"
                ),
                inline=False,
            )
            embed.set_footer(text=f"執行者: {interaction.user.display_name}")
            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error("重載 PlaywrightManager 時發生錯誤: %s", e, exc_info=True)
            await self._send_error_embed(
                interaction,
                "PlaywrightManager 重載失敗",
                f"重載 PlaywrightManager 時發生錯誤:\n```\n{str(e)}\n```",
            )

    async def _reinitialize_playwright_services(self):
        """重新初始化依賴 PlaywrightManager 的服務"""
        try:
            # 重新初始化 ProfileImageGenerator
            if hasattr(self.bot, "playwright_manager"):
                # ProfileImageGenerator is a module with functions, not a class
                # Just set a reference to the module for compatibility
                import gacha.services.ui.profile_image_generator as profile_image_generator

                self.bot.image_generator = profile_image_generator  # type: ignore
                logger.info("ProfileImageGenerator 重新初始化完成")

            # 重新初始化 AI 助手服務 (暫時註釋，因為沒有 init_services 函數)
            # try:
            #     from auxiliary.services.outfit_rater import outfit_rater
            #     # 穿搭評分服務不需要特殊的初始化，它會自動使用 playwright_manager
            #     logger.info("穿搭評分服務已準備就緒")
            # except Exception as e:
            #     logger.warning("檢查穿搭評分服務時出錯: %s", e)

        except Exception as e:
            logger.error("重新初始化 Playwright 相關服務時出錯: %s", e, exc_info=True)

    def _get_autocomplete_options(self) -> List[str]:
        """Helper to get all possible options for autocomplete."""
        all_options = set(self.available_extensions)
        import sys

        for module_name in sys.modules.keys():
            if any(
                module_name.startswith(prefix)
                for prefix in [
                    "gacha.",
                    "utils.",
                    "auxiliary.",
                    "rpg_system.",
                    "pioneer.",
                    "database.",
                    "config.",
                    "command_registry",
                ]
            ):
                # 移除對 cogs 目錄的排除，讓 poker 子模組可以被包含
                if not any(
                    exclude in module_name
                    for exclude in ["__pycache__", ".pyc", "test_", "_test"]
                ):
                    all_options.add(module_name)
        return sorted(all_options)

    def _filter_autocomplete_options(
        self, options: List[str], current: str
    ) -> List[str]:
        """Helper to filter autocomplete options based on user input."""
        if not current:
            return options[:25]
        return [opt for opt in options if current.lower() in opt.lower()][:25]

    def _create_autocomplete_choices(
        self, options: List[str]
    ) -> List[app_commands.Choice[str]]:
        """Helper to create Choice objects from a list of options."""
        choices = []
        for option in options:
            description_map = {
                "gacha.cogs": "Gacha系統",
                "auxiliary.cogs": "輔助功能",
                "rpg_system.cogs": "RPG系統",
                "utils.": "工具模組",
                "views.embeds": "Embed構建器",
                "services": "服務模組",
                "repositories": "數據庫倉庫",
                "database.": "數據庫模組",
                "auxiliary.services.ai_core.": "AI助手模組",
                "config.": "配置模組",
                "models": "數據模型",
            }
            description = "Python模組"
            for key, value in description_map.items():
                if key in option:
                    description = f"{value} - {option.split('.')[-1]}"
                    break

            full_name = f"{option} ({description})"
            if len(full_name) > 100:
                max_module_length = 100 - len(f" ({description})") - 4
                display_name = (
                    f"{option[:max_module_length]}... ({description})"
                    if max_module_length > 0
                    else option[:97] + "..."
                )
            else:
                display_name = full_name

            if 1 <= len(display_name) <= 100:
                choices.append(app_commands.Choice(name=display_name, value=option))
        return choices

    async def _extension_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        """為reload命令提供自動補全"""
        all_options = self._get_autocomplete_options()
        filtered_options = self._filter_autocomplete_options(all_options, current)
        return self._create_autocomplete_choices(filtered_options)

    async def _module_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        """為reload_module命令提供自動補全 - 動態掃描所有已載入的模組"""
        all_modules = self._get_autocomplete_options()
        filtered_options = self._filter_autocomplete_options(all_modules, current)
        return self._create_autocomplete_choices(filtered_options)

    @app_commands.command(
        name="reload_module", description="重載任意Python模組（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.describe(module_name="要重載的模組名稱（支持自動補全）")
    @app_commands.autocomplete(module_name=_module_autocomplete)
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def reload_module(self, interaction: discord.Interaction, module_name: str):
        """重載任意Python模組"""
        await interaction.response.defer(thinking=True)

        try:
            # 檢查開發權限
            if not await self._check_dev_permission(interaction):
                return

            logger.info("開始重載模組: %s", module_name)

            # 調用通用模組重載處理
            await self._handle_module_reload(interaction, module_name)

        except Exception as e:
            logger.error("重載模組時發生未預期錯誤: %s", e, exc_info=True)
            await self._send_error_embed(
                interaction,
                "模組重載失敗",
                f"重載模組時發生未預期錯誤:\n```\n{str(e)}\n```",
            )

    @app_commands.command(
        name="reload", description="重載Extension或模組（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.describe(extension_name="要重載的Extension或模組名稱（支持自動補全）")
    @app_commands.autocomplete(extension_name=_extension_autocomplete)
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def reload(self, interaction: discord.Interaction, extension_name: str):
        """重載Extension或utils模組"""
        await interaction.response.defer(thinking=True)

        # 特殊處理：任意模組重載（不是 cog 的模組）
        if not any(
            extension_name.startswith(prefix)
            for prefix in ["gacha.cogs.", "auxiliary.cogs.", "rpg_system.cogs."]
        ):
            await self._handle_module_reload(interaction, extension_name)
            return

        # 特殊處理：任意模組重載（不是 cog 的模組）
        if not any(
            extension_name.startswith(prefix)
            for prefix in ["gacha.cogs.", "auxiliary.cogs.", "rpg_system.cogs."]
        ):
            await self._handle_module_reload(interaction, extension_name)
            return

        # 檢查開發權限
        await self._check_dev_permission(interaction)

        # 檢查Extension是否在可用列表中
        if extension_name not in self.available_extensions:
            available_extensions = "\n".join(self.available_extensions)
            utils_modules = [
                "utils.playwright_manager",
                "utils.logger",
                "utils.singleton",
            ]
            utils_list = "\n".join(utils_modules)
            raise BusinessError(
                f"❌ 找不到Extension: `{extension_name}`\n\n"
                f"可用的Extensions:\n```\n{available_extensions}\n```\n\n"
                f"可用的Utils模組:\n```\n{utils_list}\n```"
            )

        # 重載Extension
        await self.bot.reload_extension(extension_name)
        logger.info("成功重載Extension: %s", extension_name)

        embed = discord.Embed(
            title="✅ Extension重載成功",
            description=(
                f"成功重載Extension `{extension_name}`\n\n"
                "**現在重新執行命令應該會看到修改！**"
            ),
            color=discord.Color.green(),
        )
        embed.set_footer(text=f"執行者: {interaction.user.display_name}")
        await interaction.followup.send(embed=embed, ephemeral=True)

    async def _reload_or_load_extensions(self, all_extensions: List[str]):
        """Helper to reload or load a list of extensions."""
        success_count = 0
        errors = []
        reloaded_extensions = []
        for extension_name in all_extensions:
            try:
                if extension_name in self.bot.extensions:
                    await self.bot.reload_extension(extension_name)
                    reloaded_extensions.append(extension_name)
                else:
                    await self.bot.load_extension(extension_name)
                    reloaded_extensions.append(f"{extension_name} (新載入)")
                success_count += 1
                logger.info("成功處理 Extension: %s", extension_name)
            except Exception as e:
                error_msg = f"處理 Extension {extension_name} 失敗: {e}"
                errors.append(error_msg)
                logger.error(error_msg, exc_info=True)
        return success_count, errors, reloaded_extensions

    def _create_reload_all_embed(
        self,
        success_count: int,
        errors: List[str],
        reloaded_extensions: List[str],
        total_extensions: int,
        interaction: discord.Interaction,
    ) -> discord.Embed:
        """Helper to create the embed for the reload_all command."""
        if errors:
            embed = discord.Embed(
                title="⚠️ Extension重載部分成功",
                description=f"成功重載 {success_count}/{total_extensions} 個Extension",
                color=discord.Color.orange(),
            )
            error_text = "\n".join(errors[:3])
            if len(errors) > 3:
                error_text += f"\n... 還有 {len(errors) - 3} 個錯誤"
            embed.add_field(
                name="❌ 錯誤信息", value=f"```\n{error_text}\n```", inline=False
            )
        else:
            embed = SuccessEmbed(
                title="✅ 全Extension重載成功",
                description=(
                    f"成功重載所有 {total_extensions} 個Extension\n\n"
                    "**所有修改現在都已生效！**"
                ),
            )

        if reloaded_extensions:
            display_extensions = reloaded_extensions[:10]
            if len(reloaded_extensions) > 10:
                display_extensions.append(
                    f"... 還有 {len(reloaded_extensions) - 10} 個"
                )
            embed.add_field(
                name="🔧 重載的Extensions",
                value=f"```\n{chr(10).join(display_extensions)}\n```",
                inline=False,
            )
        embed.add_field(
            name="📊 統計信息",
            value=f"成功: {success_count}\n錯誤: {len(errors)}",
            inline=True,
        )
        embed.set_footer(text=f"執行者: {interaction.user.display_name}")
        return embed

    @app_commands.command(
        name="reload_all", description="重新載入所有Extensions（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 10.0, key=lambda i: i.user.id)
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def reload_all(self, interaction: discord.Interaction):
        """重新載入所有Extensions"""
        await interaction.response.defer(thinking=True)
        await self._check_dev_permission(interaction)

        logger.info("開始執行全Extension重載...")
        all_extensions = self._get_all_available_extensions()
        if not all_extensions:
            raise BusinessError("⚠️ 沒有找到任何可重載的Extension")

        logger.info(
            "找到 %s 個Extension需要重載: %s", len(all_extensions), all_extensions
        )
        (
            success_count,
            errors,
            reloaded_extensions,
        ) = await self._reload_or_load_extensions(all_extensions)
        embed = self._create_reload_all_embed(
            success_count,
            errors,
            reloaded_extensions,
            len(all_extensions),
            interaction,
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="sync", description="同步 Discord 命令（僅測試伺服器）")
    @app_commands.checks.cooldown(1, 10.0, key=lambda i: i.user.id)
    @app_commands.describe(force="是否強制完整同步（清空+重載+同步），預設為智能同步")
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def sync_commands(
        self, interaction: discord.Interaction, force: bool = False
    ):
        """同步 Discord 命令 - 支援智能同步和強制完整同步"""
        await interaction.response.defer(thinking=True)

        # 檢查開發權限
        if not await self._check_dev_permission(interaction):
            return

        start_time = discord.utils.utcnow()
        test_guild = discord.Object(id=TEST_GUILD_ID)

        from command_registry import get_command_registry

        registry = get_command_registry(self.bot)

        if force:
            # 強制完整同步 (force=True)
            logger.info("開始強制完整同步...")
            sync_type = "強制完整同步"
            color = discord.Color.orange()
            description_prefix = "已透過 **強制重載** 註冊了"

            # 1. 清空所有現有命令
            self.bot.tree.clear_commands(guild=None)
            self.bot.tree.clear_commands(guild=test_guild)
            logger.info("已清空本地命令樹。")

            # 2. 重新載入所有擴展
            reload_success = await registry.register_all_commands(force_reload=True)
            if not reload_success:
                logger.warning("部分擴展重新載入失敗")
            else:
                logger.info("所有擴展已重新載入。")

        else:
            # 智能同步 (預設行為, force=False)
            logger.info("開始智能同步...")
            sync_type = "智能同步"
            color = discord.Color.green()
            description_prefix = "已透過 **智能載入** 註冊了"

            # 1. 註冊所有尚未載入的命令
            load_success = await registry.register_all_commands(force_reload=False)
            if not load_success:
                logger.warning("部分擴展載入失敗")
            else:
                logger.info("所有擴展已載入。")

        # 3. 同步命令到 Discord
        logger.info("正在同步命令到 Discord...")
        global_synced = await self.bot.tree.sync()
        guild_synced = await self.bot.tree.sync(guild=test_guild)
        logger.info("命令同步完成。")

        end_time = discord.utils.utcnow()
        duration = (end_time - start_time).total_seconds()
        total_commands = len(global_synced) + len(guild_synced)

        logger.info(
            "%s完成: 全局 %s 個，測試伺服器 %s 個，執行者: %s",
            sync_type,
            len(global_synced),
            len(guild_synced),
            interaction.user.display_name,
        )

        embed = discord.Embed(
            title=f"🔄 {sync_type}完成",
            description=(f"{description_prefix} **{total_commands}** 個命令到 Discord"),
            color=color,
        )
        embed.add_field(
            name="🌍 全局命令", value=f"{len(global_synced)} 個", inline=True
        )
        embed.add_field(
            name="🏠 測試伺服器命令", value=f"{len(guild_synced)} 個", inline=True
        )
        embed.add_field(name="⏱️ 執行時間", value=f"{duration:.2f} 秒", inline=True)

        if force:
            embed.add_field(
                name="🔧 強制同步流程",
                value=(
                    "• **清空** 所有本地命令\n"
                    "• **重載** 所有擴展 (Cogs)\n"
                    "• **同步** 所有命令到 Discord"
                ),
                inline=False,
            )
        else:
            embed.add_field(
                name="💡 智能同步流程",
                value=(
                    "• **載入** 所有未啟動的擴展 (Cogs)\n"
                    "• **跳過** 已載入的擴展\n"
                    "• **同步** 所有命令到 Discord"
                ),
                inline=False,
            )

        embed.set_footer(text=f"執行者: {interaction.user.display_name}")
        await interaction.followup.send(embed=embed, ephemeral=True)

    async def _clear_poker_state_validate_and_get_user(
        self,
        interaction: discord.Interaction,
        user: Optional[discord.User],
        user_id: Optional[str],
    ) -> Optional[discord.User]:
        if not user and not user_id:
            await interaction.followup.send(
                "❌ 請提供 `user` 或 `user_id` 參數其中之一", ephemeral=True
            )
            return None
        if user_id:
            try:
                target_user_id = int(user_id)
                return await self.bot.fetch_user(target_user_id)
            except (ValueError, discord.NotFound):
                await interaction.followup.send(
                    "❌ 無效的用戶ID格式或找不到用戶", ephemeral=True
                )
                return None
        return user

    def _clear_poker_matchmaking_queue(
        self, poker_cog, target_user_id: int
    ) -> List[str]:
        cleared = []
        if hasattr(poker_cog, "matchmaking_queue"):
            for tier in list(poker_cog.matchmaking_queue.queues.keys()):
                original_count = len(poker_cog.matchmaking_queue.queues[tier])
                poker_cog.matchmaking_queue.queues[tier] = [
                    entry
                    for entry in poker_cog.matchmaking_queue.queues[tier]
                    if entry.user_id != target_user_id
                ]
                if len(poker_cog.matchmaking_queue.queues[tier]) < original_count:
                    cleared.append(f"匹配隊列 ({tier.value})")
        return cleared

    def _clear_poker_active_games(self, poker_cog, target_user_id: int) -> List[str]:
        cleared = []
        if hasattr(poker_cog, "matchmaking_queue") and hasattr(
            poker_cog.matchmaking_queue, "active_games"
        ):
            games_to_remove = [
                game_id
                for game_id, game_state in (
                    poker_cog.matchmaking_queue.active_games.items()
                )
                if game_state.player1.user_id == target_user_id
                or game_state.player2.user_id == target_user_id
            ]
            for game_id in games_to_remove:
                del poker_cog.matchmaking_queue.active_games[game_id]
                cleared.append(f"活躍遊戲 ({game_id[:8]}...)")
        return cleared

    async def _clear_poker_processors(
        self, poker_cog, target_user_id: int
    ) -> List[str]:
        cleared = []
        if hasattr(poker_cog, "active_processors"):
            processors_to_remove = [
                game_id
                for game_id, processor in poker_cog.active_processors.items()
                if hasattr(processor, "game_state")
                and (
                    processor.game_state.player1.user_id == target_user_id
                    or processor.game_state.player2.user_id == target_user_id
                )
            ]
            for game_id in processors_to_remove:
                processor = poker_cog.active_processors.pop(game_id, None)
                if processor and hasattr(processor, "stop"):
                    await processor.stop()
                cleared.append(f"遊戲處理器 ({game_id[:8]}...)")
        return cleared

    @app_commands.command(
        name="clear_poker_state",
        description="清除指定玩家的1v1遊戲狀態（僅測試伺服器）",
    )
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.describe(
        user="要清除遊戲狀態的玩家（可選）",
        user_id="要清除遊戲狀態的玩家ID（可選，優先於user參數）",
    )
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def clear_poker_state(
        self,
        interaction: discord.Interaction,
        user: Optional[discord.User] = None,
        user_id: Optional[str] = None,
    ):
        """清除指定玩家的1v1遊戲狀態"""
        await interaction.response.defer(thinking=True)
        try:
            if not await self._check_dev_permission(interaction):
                return

            target_user = await self._clear_poker_state_validate_and_get_user(
                interaction, user, user_id
            )
            if not target_user:
                return

            poker_cog = self.bot.get_cog("Poker1v1Cog")
            if not poker_cog:
                await interaction.followup.send(
                    "❌ 找不到 Poker1v1Cog，請確認該 COG 已載入", ephemeral=True
                )
                return

            target_user_id = target_user.id
            cleared_states = []
            cleared_states.extend(
                self._clear_poker_matchmaking_queue(poker_cog, target_user_id)
            )
            cleared_states.extend(
                self._clear_poker_active_games(poker_cog, target_user_id)
            )
            # 檢查並清除玩家操作狀態
            try:
                from gacha.cogs.poker import game_manager

                if target_user_id in game_manager.players_in_operation:
                    game_manager.players_in_operation.discard(target_user_id)
                    cleared_states.append("操作狀態")
            except ImportError:
                # 如果 poker 模組不存在，跳過
                pass
            cleared_states.extend(
                await self._clear_poker_processors(poker_cog, target_user_id)
            )

            if cleared_states:
                embed = discord.Embed(
                    title="✅ 玩家遊戲狀態已清除",
                    description=f"已清除 {target_user.mention} 的以下狀態：",
                    color=discord.Color.green(),
                )
                embed.add_field(
                    name="清除的狀態",
                    value="\n".join(f"• {state}" for state in cleared_states),
                    inline=False,
                )
                embed.add_field(
                    name="玩家信息",
                    value=(
                        f"用戶ID: `{target_user_id}`\n"
                        f"用戶名: `{target_user.display_name}`"
                    ),
                    inline=False,
                )
            else:
                embed = discord.Embed(
                    title="ℹ️ 無需清除",
                    description=f"{target_user.mention} 目前沒有任何1v1遊戲狀態",
                    color=discord.Color.blue(),
                )

            logger.info(
                (
                    f"清除玩家 {target_user.display_name} ({target_user_id}) "
                    f"的1v1遊戲狀態: {cleared_states}"
                )
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error("清除玩家遊戲狀態時發生錯誤: %s", e, exc_info=True)
            await self._send_error_embed(
                interaction, "清除遊戲狀態失敗", f"清除玩家遊戲狀態時發生錯誤: {e}"
            )

    @app_commands.command(
        name="reload_config", description="重新載入配置文件（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 10.0, key=lambda i: i.user.id)
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def reload_config(self, interaction: discord.Interaction):
        """重新載入配置文件，清除配置緩存並重新讀取所有 YAML 配置"""
        await interaction.response.defer(thinking=True)
        try:
            if not await self._check_dev_permission(interaction):
                return

            # 重新載入配置
            from config.app_config import reload_settings

            logger.info("開始重新載入配置文件...")
            start_time = discord.utils.utcnow()

            # 清除配置緩存並重新載入
            reload_settings()

            end_time = discord.utils.utcnow()
            duration = (end_time - start_time).total_seconds()

            # 創建成功回應
            embed = discord.Embed(
                title="✅ 配置重新載入成功",
                description="已成功重新載入所有配置文件",
                color=discord.Color.green(),
                timestamp=discord.utils.utcnow(),
            )
            embed.add_field(name="⏱️ 載入時間", value=f"{duration:.2f} 秒", inline=True)
            embed.add_field(
                name="📁 配置文件",
                value="• config.yaml\n• gacha_settings.yaml\n• ui_settings.yaml",
                inline=True,
            )
            embed.add_field(
                name="🔄 影響範圍",
                value="• 黑名單配置\n• 遊戲設定\n• UI 設定\n• 其他所有配置",
                inline=False,
            )
            embed.set_footer(text=f"執行者: {interaction.user.display_name}")

            await interaction.followup.send(embed=embed, ephemeral=True)
            logger.info("配置重新載入完成，耗時 %.2f 秒", duration)

        except Exception as e:
            logger.error("重新載入配置時發生錯誤: %s", e, exc_info=True)
            await self._send_error_embed(
                interaction, "配置重新載入失敗", f"重新載入配置時發生錯誤: {e}"
            )

    @app_commands.command(
        name="指令統計", description="查看指令使用統計（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 30.0, key=lambda i: i.user.id)
    @app_commands.describe(command_name="特定指令名稱（可選）")
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def command_stats(
        self, interaction: discord.Interaction, command_name: Optional[str] = None
    ):
        """
        查看指令使用統計

        Args:
            command_name: 特定指令名稱（可選）
        """
        await interaction.response.defer()

        # 檢查統計服務是否可用
        service = getattr(self.bot, "command_usage_service", None)
        if not service:
            embed = discord.Embed(
                title="❌ 指令統計服務不可用",
                description="指令統計服務尚未初始化或不可用",
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        if command_name:
            # 查看特定指令統計
            await self._show_specific_command_stats(interaction, service, command_name)
        else:
            # 查看總體統計
            await self._show_overall_stats(interaction, service)

    async def _show_specific_command_stats(
        self, interaction, service, command_name: str
    ):
        """顯示特定指令的統計"""
        stats = await service.get_command_stats(command_name)

        if not stats:
            embed = discord.Embed(
                title="❌ 指令未找到",
                description=f"找不到指令 `{command_name}` 的統計數據",
                color=discord.Color.orange(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        embed = discord.Embed(
            title=f"📊 指令統計: {command_name}", color=discord.Color.blue()
        )

        # 基本統計
        embed.add_field(
            name="📈 使用統計",
            value=f"總使用次數: **{stats.total_uses:,}**\n"
            f"唯一用戶: **{stats.unique_users:,}**\n"
            f"唯一伺服器: **{stats.unique_guilds:,}**",
            inline=True,
        )

        # 時間統計
        first_used = (
            stats.first_used.strftime("%Y-%m-%d %H:%M") if stats.first_used else "未知"
        )
        last_used = (
            stats.last_used.strftime("%Y-%m-%d %H:%M") if stats.last_used else "未知"
        )

        embed.add_field(
            name="⏰ 時間統計",
            value=f"首次使用: {first_used}\n最後使用: {last_used}",
            inline=True,
        )

        # 性能統計
        embed.add_field(
            name="⚡ 性能統計",
            value=f"成功率: **{stats.success_rate:.1f}%**",
            inline=True,
        )

        embed.set_footer(
            text=(
                f"統計時間: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M UTC')}"
            )
        )
        await interaction.followup.send(embed=embed)

    async def _show_overall_stats(self, interaction, service):
        """顯示總體統計"""
        # 獲取總體統計
        total_stats = await service.get_total_stats()
        top_commands = await service.get_all_command_stats(limit=10)
        top_guilds = await service.get_guild_stats(limit=5)

        embed = discord.Embed(
            title="📊 機器人指令使用統計", color=discord.Color.green()
        )

        # 總體數據
        embed.add_field(
            name="📈 總體統計",
            value=f"總指令執行: **{total_stats.get('total_commands', 0):,}**\n"
            f"唯一指令: **{total_stats.get('unique_commands', 0):,}**\n"
            f"唯一用戶: **{total_stats.get('unique_users', 0):,}**\n"
            f"唯一伺服器: **{total_stats.get('unique_guilds', 0):,}**",
            inline=True,
        )

        # 今日統計
        embed.add_field(
            name="📅 今日統計",
            value=f"今日指令: **{total_stats.get('today_commands', 0):,}**\n"
            f"今日唯一指令: **{total_stats.get('today_unique_commands', 0):,}**\n"
            f"今日唯一用戶: **{total_stats.get('today_unique_users', 0):,}**",
            inline=True,
        )

        # 性能統計
        success_rate = total_stats.get("overall_success_rate", 0)

        embed.add_field(
            name="⚡ 性能統計",
            value=f"整體成功率: **{success_rate:.1f}%**",
            inline=True,
        )

        # 熱門指令
        if top_commands:
            top_commands_text = ""
            for i, cmd in enumerate(top_commands[:5], 1):
                top_commands_text += (
                    f"{i}. `{cmd.command_name}` - {cmd.total_uses:,} 次\n"
                )

            embed.add_field(
                name="🔥 熱門指令 (前5名)", value=top_commands_text, inline=False
            )

        # 活躍伺服器排行榜
        if top_guilds:
            top_guilds_text = ""
            for i, guild_stat in enumerate(top_guilds[:5], 1):
                guild_id = guild_stat["guild_id"]
                total_commands = guild_stat["total_commands"]
                unique_users = guild_stat["unique_users"]

                # 嘗試獲取伺服器名稱
                try:
                    guild = self.bot.get_guild(guild_id)
                    guild_name = guild.name if guild else f"伺服器 {guild_id}"
                except Exception:
                    guild_name = f"伺服器 {guild_id}"

                # 限制伺服器名稱長度
                if len(guild_name) > 20:
                    guild_name = guild_name[:17] + "..."

                top_guilds_text += f"{i}. **{guild_name}**\n"
                top_guilds_text += (
                    f"   指令數: {total_commands:,} | 用戶數: {unique_users:,}\n"
                )

            embed.add_field(
                name="🏆 活躍伺服器排行榜 (前5名)", value=top_guilds_text, inline=False
            )

        # 時間範圍
        first_command = total_stats.get("first_command")
        last_command = total_stats.get("last_command")

        if first_command and last_command:
            first_str = first_command.strftime("%Y-%m-%d")
            last_str = last_command.strftime("%Y-%m-%d %H:%M")
            embed.add_field(
                name="📅 統計範圍", value=f"從 {first_str} 到 {last_str}", inline=False
            )

        embed.set_footer(
            text=(
                f"統計時間: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M UTC')}"
            )
        )
        await interaction.followup.send(embed=embed)

    @app_commands.command(
        name="用戶指令統計", description="查看用戶的指令使用統計（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 60.0, key=lambda i: i.user.id)
    @app_commands.describe(user="要查看的用戶（可選，默認為自己）")
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def user_command_stats(
        self, interaction: discord.Interaction, user: Optional[discord.Member] = None
    ):
        """
        查看用戶的指令使用統計

        Args:
            user: 要查看的用戶（可選，默認為自己）
        """
        await interaction.response.defer()

        target_user = user or interaction.user

        try:
            # 檢查統計服務是否可用
            service = getattr(self.bot, "command_usage_service", None)
            if not service:
                embed = discord.Embed(
                    title="❌ 指令統計服務不可用",
                    description="指令統計服務尚未初始化或不可用",
                    color=discord.Color.red(),
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
                return
            user_stats = await service.get_user_command_stats(target_user.id, limit=10)

            if not user_stats:
                embed = discord.Embed(
                    title="📊 用戶指令統計",
                    description=f"{target_user.display_name} 還沒有使用過任何指令",
                    color=discord.Color.orange(),
                )
                await interaction.followup.send(embed=embed)
                return

            embed = discord.Embed(
                title=f"📊 {target_user.display_name} 的指令使用統計",
                color=discord.Color.blue(),
            )

            # 總使用次數
            total_usage = sum(stat["usage_count"] for stat in user_stats)
            embed.add_field(
                name="📈 總體統計",
                value=f"總使用次數: **{total_usage:,}**\n"
                f"使用過的指令: **{len(user_stats)}**",
                inline=True,
            )

            # 最常用指令
            top_commands_text = ""
            for i, stat in enumerate(user_stats[:5], 1):
                cmd_name = stat["command_name"]
                usage_count = stat["usage_count"]
                percentage = (usage_count / total_usage * 100) if total_usage > 0 else 0
                top_commands_text += (
                    f"{i}. `{cmd_name}` - {usage_count:,} 次 ({percentage:.1f}%)\n"
                )

            embed.add_field(
                name="🔥 最常用指令 (前5名)", value=top_commands_text, inline=False
            )

            # 最近活動
            if user_stats:
                latest_stat = max(
                    user_stats,
                    key=lambda x: (
                        x["last_used"]
                        if x["last_used"]
                        else datetime.min.replace(tzinfo=timezone.utc)
                    ),
                )
                if latest_stat["last_used"]:
                    last_used = latest_stat["last_used"].strftime("%Y-%m-%d %H:%M")
                    embed.add_field(
                        name="⏰ 最近活動",
                        value=f"最後使用: {last_used}\n"
                        f"指令: `{latest_stat['command_name']}`",
                        inline=True,
                    )

            embed.set_thumbnail(url=target_user.display_avatar.url)
            embed.set_footer(
                text=(
                    f"統計時間: "
                    f"{datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M UTC')}"
                )
            )
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error("查看用戶指令統計時發生錯誤: %s", e, exc_info=True)
            embed = discord.Embed(
                title="❌ 查看統計失敗",
                description=f"發生錯誤: {str(e)}",
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(
        name="伺服器指令統計", description="查看伺服器的指令使用統計（僅測試伺服器）"
    )
    @app_commands.checks.cooldown(1, 60.0, key=lambda i: i.user.id)
    @app_commands.describe(
        guild_id="要查看的伺服器ID（可選，默認為當前伺服器）",
        show_ranking="是否顯示伺服器排行榜（默認為False）",
    )
    @app_commands.guilds(discord.Object(id=TEST_GUILD_ID))
    async def guild_command_stats(
        self,
        interaction: discord.Interaction,
        guild_id: Optional[str] = None,
        show_ranking: bool = False,
    ):
        """
        查看伺服器的指令使用統計

        Args:
            guild_id: 要查看的伺服器ID（可選，默認為當前伺服器）
            show_ranking: 是否顯示伺服器排行榜
        """
        await interaction.response.defer()

        try:
            # 檢查統計服務是否可用
            service = getattr(self.bot, "command_usage_service", None)
            if not service:
                embed = discord.Embed(
                    title="❌ 指令統計服務不可用",
                    description="指令統計服務尚未初始化或不可用",
                    color=discord.Color.red(),
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
                return

            if show_ranking:
                # 顯示伺服器排行榜
                await self._show_guild_ranking(interaction, service)
            else:
                # 顯示特定伺服器統計
                if guild_id:
                    try:
                        target_guild_id = int(guild_id)
                    except ValueError:
                        embed = discord.Embed(
                            title="❌ 無效的伺服器ID",
                            description="請提供有效的數字伺服器ID",
                            color=discord.Color.red(),
                        )
                        await interaction.followup.send(embed=embed, ephemeral=True)
                        return
                else:
                    # 使用當前伺服器
                    if not interaction.guild:
                        embed = discord.Embed(
                            title="❌ 無法獲取伺服器信息",
                            description="此指令需要在伺服器中使用，或提供伺服器ID參數",
                            color=discord.Color.red(),
                        )
                        await interaction.followup.send(embed=embed, ephemeral=True)
                        return
                    target_guild_id = interaction.guild.id

                await self._show_specific_guild_stats(
                    interaction, service, target_guild_id
                )

        except Exception as e:
            logger.error("查看伺服器指令統計時發生錯誤: %s", e, exc_info=True)
            embed = discord.Embed(
                title="❌ 查看統計失敗",
                description=f"發生錯誤: {str(e)}",
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _show_guild_ranking(self, interaction, service):
        """顯示伺服器排行榜"""
        view = GuildRankingView(
            bot=self.bot, service=service, user_id=interaction.user.id
        )
        await view.update_and_send(interaction)

    async def _show_specific_guild_stats(self, interaction, service, guild_id: int):
        """顯示特定伺服器的統計"""
        # 獲取伺服器基本統計
        guild_stats_list = await service.get_guild_stats(
            limit=1000
        )  # 獲取所有伺服器統計
        guild_basic_stats = None
        guild_rank = None

        # 找到目標伺服器的統計和排名
        for i, stats in enumerate(guild_stats_list, 1):
            if stats["guild_id"] == guild_id:
                guild_basic_stats = stats
                guild_rank = i
                break

        if not guild_basic_stats:
            embed = discord.Embed(
                title="❌ 伺服器統計未找到",
                description=f"找不到伺服器 ID `{guild_id}` 的統計數據",
                color=discord.Color.orange(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # 獲取伺服器詳細指令統計
        guild_commands = await service.get_guild_command_stats(guild_id, limit=10)

        # 獲取伺服器信息
        try:
            guild = self.bot.get_guild(guild_id)
            guild_name = guild.name if guild else "未知伺服器"
            guild_member_count = guild.member_count if guild else "未知"
        except Exception:
            guild = None
            guild_name = "未知伺服器"
            guild_member_count = "未知"

        embed = discord.Embed(
            title=f"📊 {guild_name} 的指令使用統計",
            description=f"伺服器ID: `{guild_id}`",
            color=discord.Color.blue(),
        )

        # 基本統計
        total_commands = guild_basic_stats["total_commands"]
        unique_commands = guild_basic_stats["unique_commands"]
        unique_users = guild_basic_stats["unique_users"]
        success_rate = guild_basic_stats["success_rate"]

        embed.add_field(
            name="📈 基本統計",
            value=f"總指令數: **{total_commands:,}**\n"
            f"唯一指令: **{unique_commands:,}**\n"
            f"活躍用戶: **{unique_users:,}**\n"
            f"成功率: **{success_rate:.1f}%**",
            inline=True,
        )

        # 排名和伺服器信息
        rank_text = f"全域排名: **#{guild_rank}**" if guild_rank else "排名: 未知"
        embed.add_field(
            name="🏆 排名信息",
            value=(
                f"{rank_text}\n"
                f"伺服器成員: **{guild_member_count}**\n"
                f"用戶參與率: **{(unique_users / guild_member_count * 100):.1f}%**"
                if isinstance(guild_member_count, int)
                else "用戶參與率: 無法計算"
            ),
            inline=True,
        )

        # 時間統計
        first_used = (
            guild_basic_stats["first_used"].strftime("%Y-%m-%d %H:%M")
            if guild_basic_stats["first_used"]
            else "未知"
        )
        last_used = (
            guild_basic_stats["last_used"].strftime("%Y-%m-%d %H:%M")
            if guild_basic_stats["last_used"]
            else "未知"
        )

        embed.add_field(
            name="⏰ 時間統計",
            value=f"首次使用: {first_used}\n最後使用: {last_used}",
            inline=True,
        )

        # 熱門指令
        if guild_commands:
            top_commands_text = ""
            for i, cmd_stat in enumerate(guild_commands[:8], 1):
                cmd_name = cmd_stat["command_name"]
                usage_count = cmd_stat["usage_count"]
                cmd_unique_users = cmd_stat["unique_users"]
                percentage = (
                    (usage_count / total_commands * 100) if total_commands > 0 else 0
                )

                top_commands_text += (
                    f"{i}. `{cmd_name}` - {usage_count:,} 次 ({percentage:.1f}%)\n"
                )
                top_commands_text += f"   用戶數: {cmd_unique_users:,}\n"

            embed.add_field(
                name="🔥 熱門指令 (前8名)", value=top_commands_text, inline=False
            )

        # 設置伺服器圖標
        if guild and guild.icon:
            embed.set_thumbnail(url=guild.icon.url)

        embed.set_footer(
            text=(
                f"統計時間: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M UTC')}"
            )
        )
        await interaction.followup.send(embed=embed)


async def setup(bot: commands.Bot):
    """註冊開發工具相關命令"""
    await bot.add_cog(DevToolsCog(bot))
    logger.info("DevToolsCog 已成功載入（僅限測試伺服器）")
