import discord

from gacha.exceptions import BusinessError


class FavoriteButton(discord.ui.Button):
    """一個動態的收藏按鈕，封裝了自己的狀態和回呼邏輯。"""

    def __init__(self, user_id: int, card_id: int, is_favorite: bool):
        self.user_id = user_id
        self.card_id = card_id

        # 根據初始狀態設置按鈕外觀
        label = "已收藏" if is_favorite else "加入最愛"
        style = (
            discord.ButtonStyle.success
            if is_favorite
            else discord.ButtonStyle.secondary
        )
        emoji = (
            "<a:pu:1365482490478989353>"
            if is_favorite
            else "<a:sw:1365447243863429273>"
        )

        super().__init__(
            label=label,
            style=style,
            emoji=emoji,
            custom_id=f"shop_fav_{user_id}_{card_id}",
        )

    async def callback(self, interaction: discord.Interaction):
        """處理收藏按鈕的點擊事件。"""
        if interaction.user.id != self.user_id:
            raise BusinessError("只有卡片擁有者才能將其加入最愛。")

        await interaction.response.defer()
        from gacha.services import favorite_service

        # 切換收藏狀態
        new_is_favorite = await favorite_service.toggle_favorite_card(
            self.user_id, self.card_id, interaction.user.id
        )

        # 更新按鈕自身的外觀
        self.label = "已收藏" if new_is_favorite else "加入最愛"
        self.style = (
            discord.ButtonStyle.success
            if new_is_favorite
            else discord.ButtonStyle.secondary
        )
        self.emoji = (
            "<a:pu:1365482490478989353>"
            if new_is_favorite
            else "<a:sw:1365447243863429273>"
        )

        # 更新原始訊息，self.view 會包含更新後的按鈕
        await interaction.edit_original_response(view=self.view)
