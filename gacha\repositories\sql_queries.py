"""
SQL Queries for Gacha Repositories
"""

# Query for UserCollectionRepository.get_user_card_position
GET_USER_CARD_POSITION_SQL = """
WITH ranked_cards_with_total AS (
    SELECT
        uc.card_id,
        ROW_NUMBER() OVER (ORDER BY {order_clause}) as position,
        COUNT(*) OVER () as total_count_in_cte --  使用窗口函數計算總數
    FROM {table_name} uc
    JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
    WHERE {where_clause} -- where_clause 已經包含 user_id = $1 等條件
)
SELECT position, total_count_in_cte as total -- 直接從 CTE 選取計算好的總數
FROM ranked_cards_with_total
WHERE card_id = {card_id_placeholder_index}
"""
