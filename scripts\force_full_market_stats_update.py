#!/usr/bin/env python3
"""
強制執行全量市場統計更新的工具腳本

這個腳本可以在需要時手動執行，強制對所有卡片進行全量市場統計更新。
通常在以下情況下使用：
1. 數據庫結構變更後
2. 發現統計數據不一致時
3. 定期維護時

使用方法：
python scripts/force_full_market_stats_update.py
"""

import asyncio
import os
import sys

# 改進路徑處理，確保能夠找到項目根目錄
current_script_path = os.path.abspath(__file__)
scripts_dir = os.path.dirname(current_script_path)
project_root = os.path.dirname(scripts_dir)

# 添加項目根目錄到Python模塊搜索路徑
sys.path.insert(0, project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)
from utils.logger import logger  # noqa: E402

# Load environment variables from .env file
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)


async def main():
    """執行強制全量市場統計更新"""
    logger.info("=" * 60)
    logger.info("開始執行強制全量市場統計更新")
    logger.info("=" * 60)

    await setup_connections()
    try:
        # 導入並執行全量更新
        import scripts.initialize_market_stats

        pool = get_pool()
        if not pool:
            logger.error("數據庫連接池初始化失敗")
            return

        async with pool.acquire() as conn:
            await scripts.initialize_market_stats.main(conn, force_full_update=True)

        logger.info("=" * 60)
        logger.info("強制全量市場統計更新成功完成")
        logger.info("=" * 60)

    except Exception as e:
        logger.error("=" * 60)
        logger.error("強制全量市場統計更新失敗: %s", e, exc_info=True)
        logger.error("=" * 60)
        sys.exit(1)
    finally:
        await close_connections()


if __name__ == "__main__":
    print("正在執行強制全量市場統計更新...")
    print("這可能需要幾分鐘時間，請耐心等待...")

    try:
        asyncio.run(main())
        print("✅ 強制全量市場統計更新完成！")
    except KeyboardInterrupt:
        print("\n❌ 用戶中斷了更新過程")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 更新過程中發生錯誤: {e}")
        sys.exit(1)
