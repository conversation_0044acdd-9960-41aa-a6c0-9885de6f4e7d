"""
Builds the Discord Embed for the Gacha Collection View.
"""

from decimal import Decimal
from typing import TYPE_CHECKING, List

import discord

from config.app_config import get_config
from gacha.constants import RarityLevel
from gacha.models.models import UserCard
from gacha.views import utils as view_utils
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder


# 使用統一的排序配置
def _get_sort_field_label(sort_by: str) -> str:
    """從統一的排序選項中獲取標籤"""
    from gacha.config.sorting_config import SortingConfig

    try:
        sort_option = SortingConfig.get_sort_option(sort_by)
        return sort_option.label
    except ValueError:
        # 未知排序選項，返回原值
        return sort_by


def _get_sort_order_label(sort_order: str) -> str:
    """從統一的排序順序選項中獲取標籤"""
    from gacha.config.sorting_config import SortingConfig

    for sort_option in SortingConfig.SORT_ORDER_OPTIONS:
        if sort_option.value == sort_order:
            return sort_option.label

    return sort_order


if TYPE_CHECKING:
    from .card_view import CollectionView


class CollectionEmbedBuilder(BaseEmbedBuilder):
    """Handles the creation of the Embed for the CollectionView."""

    def __init__(self, view: "CollectionView"):
        interaction = getattr(view, "interaction", None)
        super().__init__(data=view, interaction=interaction)
        self.view = view

    def build_embed(self) -> discord.Embed:
        """Builds the embed for the current page."""
        nickname = self.view.user_nickname
        embed = self._create_base_embed()
        title = self._get_formatted_title(nickname)
        embed.set_author(name=title, icon_url=self.view.user_avatar_url)

        page_info = self._build_page_info()

        if not self.view.cards or len(self.view.cards) == 0:
            self._add_empty_page_info(embed, page_info)
        elif self.view.list_mode:
            self._add_card_list_info(embed, self.view.cards, page_info)
        elif not self.view.current_card:
            self._add_empty_page_info(embed, page_info)
        else:
            self._add_card_info(embed, self.view.current_card, page_info)

        return embed

    def _build_page_info(self) -> str:
        """構建頁面信息，包含持有人數、排序方式"""
        info_parts = []

        # 添加持有人數（如果有當前卡片）
        if self.view.current_card and hasattr(self.view.current_card, "owner_count"):
            owner_count = self.view.current_card.owner_count
            if owner_count is not None:
                info_parts.append(f"擁有者: {owner_count}")

        # 添加排序方式（移除 "排序:" 前綴）
        sort_text = self._get_sort_text()
        info_parts.append(sort_text)

        return " • ".join(info_parts)

    def _get_formatted_title(self, nickname: str) -> str:
        """Gets the formatted embed title."""
        if self.view.list_mode:
            title_parts = [f"{nickname}的卡冊 (列表模式)"]
        else:
            title_parts = [f"{nickname}的卡冊"]
        filters = self.view.filters

        if filters.pool_type:
            from config.app_config import get_pool_type_names

            pool_name = get_pool_type_names().get(
                filters.pool_type, filters.pool_type.capitalize()
            )
            title_parts.append(f"[{pool_name}]")

        if filters.rarity_in and len(filters.rarity_in) > 0:
            from config.app_config import get_rarity_display_codes

            display_rarities = [
                get_rarity_display_codes().get(r, str(r)) for r in filters.rarity_in
            ]
            if len(set(display_rarities)) == 1:
                try:
                    rarity_int = filters.rarity_in[0]
                    rarity_level_enum = RarityLevel(rarity_int)
                    friendly_name = view_utils.get_user_friendly_rarity_name(
                        rarity_level_enum
                    )
                    title_parts.append(f"[{friendly_name}]")
                except (ValueError, IndexError):
                    title_parts.append(f"[{display_rarities[0]}]")
            else:
                title_parts.append(f"[{', '.join(sorted(set(display_rarities)))}]")

        if filters.series:
            title_parts.append(f"[系列: {filters.series}]")
        if filters.card_name:
            title_parts.append(f"[名稱: {filters.card_name}]")
        if filters.quantity_greater_than and filters.quantity_greater_than > 0:
            title_parts.append("[僅重複]")
        return " ".join(title_parts)

    def _add_empty_page_info(self, embed: discord.Embed, page_info: str):
        """Adds information for an empty page."""
        embed.add_field(name="當前頁沒有卡片", value="請嘗試其他頁碼", inline=False)
        # 使用與圖鑑相同的 footer icon
        footer_icon_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1382158954649489522/question.png"
        self._set_common_footer(
            embed, page_info=page_info, footer_icon_url=footer_icon_url
        )
        embed.description = (
            f"收集: {self.view.unique_cards:,}/{self.view.total_cards:,}張"
        )

    def _add_card_info(self, embed: discord.Embed, user_card: UserCard, page_info: str):
        """Adds card information to the embed."""
        if not user_card.card:
            self._add_empty_page_info(embed, page_info)
            return

        card = user_card.card

        rarity_level_enum = None
        try:
            if isinstance(card.rarity, int):
                rarity_level_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                rarity_level_enum = card.rarity
        except ValueError:
            pass

        color_int = view_utils.get_rarity_color(
            rarity_level=rarity_level_enum, pool_type=card.pool_type
        )
        embed.color = discord.Color(value=color_int)

        star_display = ""
        if hasattr(user_card, "star_level") and user_card.star_level > 0:
            star_display = f"{view_utils.get_star_emoji_string(user_card.star_level)}"

        prefix_emoji = (
            view_utils.get_ui_emoji("heart")
            if user_card.is_favorite
            else view_utils.get_ui_emoji("old_card")
        )

        if star_display:
            card_info_value = (
                f"{prefix_emoji} **{card.name}**\n{prefix_emoji} *{card.series}*"
            )
            if (
                hasattr(user_card, "custom_description")
                and user_card.custom_description
            ):
                card_info_value += f"\n「{user_card.custom_description}」"
            embed.add_field(name=f"{star_display}", value=card_info_value, inline=False)
        else:
            card_info_value = f"{prefix_emoji} *{card.series}*"
            if (
                hasattr(user_card, "custom_description")
                and user_card.custom_description
            ):
                card_info_value += f"\n「{user_card.custom_description}」"
            embed.add_field(
                name=f"{prefix_emoji} **{card.name}**",
                value=card_info_value,
                inline=False,
            )

        # 市場價格處理
        price_text_label = "市場價"
        sell_price_display_text = "價格計算中"

        if (
            hasattr(card, "current_market_sell_price")
            and card.current_market_sell_price is not None
        ):
            stored_price = card.current_market_sell_price
            if not isinstance(stored_price, Decimal):
                try:
                    stored_price = Decimal(str(stored_price))
                except BaseException:
                    stored_price = None

            if stored_price is not None:
                from config.app_config import get_oil_emoji

                sell_price_display_text = f"`{int(stored_price)}` {get_oil_emoji()}"

        rarity_display = view_utils.get_user_friendly_rarity_name(rarity_level_enum)
        pool_prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
        pool_display_text = ""
        if pool_prefixes and card.pool_type:
            pool_display_text = (
                pool_prefixes.get(card.pool_type, "")
                if isinstance(pool_prefixes, dict)
                else ""
            )
        description_lines = [
            f"<:ReplyCont:1383146319425699931>{rarity_display} | {pool_display_text}",
            f"<:Reply:1357534074830590143>持有: `{user_card.quantity:,}`張 | {price_text_label}: {sell_price_display_text}",
        ]

        card_details_value = "\n".join(description_lines)

        embed.add_field(
            name=f"<a:RARITYyellow:1357560475302494228> 卡片信息 `{self.view.unique_cards:,}/{self.view.total_cards:,}張`",
            value=card_details_value,
            inline=False,
        )

        if hasattr(card, "image_url") and card.image_url:
            embed.set_image(url=card.image_url)

        self._set_rarity_thumbnail(embed, rarity_level=rarity_level_enum)
        # 使用與圖鑑相同的 footer icon
        footer_icon_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1382158954649489522/question.png"
        self._set_common_footer(
            embed, card=card, page_info=page_info, footer_icon_url=footer_icon_url
        )

    def _add_card_list_info(
        self, embed: discord.Embed, cards: List[UserCard], page_info: str
    ):
        """Adds card list information to the embed for list mode."""
        if not cards:
            self._add_empty_page_info(embed, page_info)
            return

        # 設置基本顏色為藍色（列表模式）
        embed.color = discord.Color.blue()

        # 構建卡片列表
        start_index = (self.view.current_page - 1) * self.view.CARDS_PER_PAGE + 1
        card_list_text = self._build_card_list_lines(cards, start_index)

        # 將卡片列表放到 description 中（可容納4096字符）
        if card_list_text:
            # 在卡片列表前添加統計信息
            stats_header = f"<a:RARITYyellow:1357560475302494228> **卡片統計** - 擁有: `{self.view.unique_cards:,}` 種卡片，共 `{self.view.total_cards:,}` 張\n"
            description_content = stats_header + "\n".join(card_list_text)
            # Discord embed description 限制為4096字符
            if len(description_content) > 4096:
                # 最後防線：縮短系列名再試一次
                shortened_card_list = self._build_card_list_lines(
                    cards, start_index, shorten_series=True
                )
                description_content = stats_header + "\n".join(shortened_card_list)

                # 如果縮短系列名後還是超過限制，則截斷
                if len(description_content) > 4096:
                    truncated_lines = []
                    current_length = 0
                    for line in shortened_card_list:
                        if (
                            current_length + len(line) + 1 > 4000
                        ):  # 留一些空間給省略提示
                            break
                        truncated_lines.append(line)
                        current_length += len(line) + 1
                    description_content = (
                        "\n".join(truncated_lines) + "\n...(內容過長，已截斷)"
                    )

            embed.description = description_content

        # 設置頁腳
        footer_icon_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1382158954649489522/question.png"
        embed.set_footer(
            text=f"第 {self.view.current_page}/{self.view.total_pages} 頁 • {page_info}",
            icon_url=footer_icon_url,
        )

    def _build_card_list_lines(
        self, cards: List[UserCard], start_index: int, shorten_series: bool = False
    ) -> List[str]:
        """構建卡片列表行，避免重複代碼"""
        card_list_text = []
        for i, user_card in enumerate(cards, start_index):
            if not user_card.card:
                continue

            card = user_card.card

            # 獲取稀有度emoji顯示
            try:
                rarity_level = (
                    RarityLevel(card.rarity)
                    if isinstance(card.rarity, int)
                    else card.rarity
                )
                rarity_emoji = view_utils.get_rarity_display_code(
                    rarity_level, card.pool_type
                )
            except (ValueError, TypeError):
                rarity_emoji = "❓"

            # 獲取星級顯示
            star_display = self._get_star_display(user_card)

            # 獲取最愛標記
            favorite_emoji = (
                view_utils.get_ui_emoji("heart") if user_card.is_favorite else ""
            )

            # 處理系列名（是否縮短）
            series_name = card.series
            if shorten_series and len(series_name) > 10:
                series_name = series_name[:10] + "..."

            # 構建單行顯示
            line_parts = [
                f"`{i:2d}.` `{card.card_id:>5}`",
                favorite_emoji,
                rarity_emoji,
                f"**{card.name}**",
                f"*{series_name}*",
                f"x{user_card.quantity}",
                star_display,
            ]
            card_line = " ".join(part for part in line_parts if part)
            card_list_text.append(card_line)

        return card_list_text

    def _get_star_display(self, user_card: UserCard) -> str:
        """獲取星級顯示字符串"""
        if not hasattr(user_card, "star_level") or user_card.star_level <= 0:
            return ""

        original_star_string = view_utils.get_star_emoji_string(user_card.star_level)
        if not original_star_string:
            return ""

        # 計算這個tier的星數
        from config.app_config import get_config

        gacha_core_settings = get_config("gacha_core_settings")
        stars_per_tier = getattr(gacha_core_settings, "stars_per_tier", 5)
        stars_count = (user_card.star_level - 1) % stars_per_tier + 1

        # 提取emoji
        if original_star_string.startswith("<"):
            # Discord自定義emoji
            end_pos = original_star_string.find(">")
            first_emoji = original_star_string[: end_pos + 1] if end_pos != -1 else "⭐"
        else:
            # 普通emoji
            first_emoji = original_star_string[0] if original_star_string else "⭐"

        return f"{first_emoji}x{stars_count:,}"

    def _get_sort_text(self) -> str:
        """Gets the text description of the current sorting."""
        if self.view.sort_by == "position":
            return "自訂排序"

        field = _get_sort_field_label(self.view.sort_by)
        order = _get_sort_order_label(self.view.sort_order)
        mode = "自定義排序" if self.view.favorite_priority else "統一排序"
        return f"{field} {order} ({mode})"
