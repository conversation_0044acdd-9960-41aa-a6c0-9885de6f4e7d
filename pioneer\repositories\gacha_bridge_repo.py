"""
Gacha Bridge Repository
This module acts as a bridge to access gacha repositories from the pioneer module,
avoiding circular dependencies and keeping the modules decoupled.
"""

from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import user_collection_repository

# Re-exporting the functions so they can be imported from this bridge
get_card_by_id = master_card_repository.get_card
get_user_collection_by_id = user_collection_repository.get_user_collection_by_id
get_user_collections = user_collection_repository.get_user_cards_paginated

__all__ = [
    "get_card_by_id",
    "get_user_collection_by_id",
    "get_user_collections",
]
