# ===== 第一階段：基礎環境和依賴安裝 =====
FROM python:3.12-slim as base

# 設定工作目錄
WORKDIR /app

# 設定環境變數
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PLAYWRIGHT_BROWSERS_PATH=/usr/local/ms-playwright \
    PYTHONDONTWRITEBYTECODE=1

# 安裝系統依賴和字體支援
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libpq-dev \
    curl \
    wget \
    gnupg \
    unzip \
    # 字體相關依賴
    fontconfig \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    # Playwright 依賴
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libatspi2.0-0 \
    libxshmfence1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir uv \
    && mkdir -p logs fonts \
    && fc-cache -fv

# ===== 第二階段：Python依賴安裝 =====
FROM base as dependencies

# 先複製 requirements.txt（利用 Docker 層快取）
COPY requirements.txt .

# 使用 uv 安裝 Python 依賴（比 pip 快 10-100 倍）
RUN uv pip install --system -r requirements.txt && \
    # 安裝 Playwright 瀏覽器 (僅 Chromium，不含其他瀏覽器)
    playwright install --with-deps chromium

# ===== 第三階段：最終運行環境 =====
FROM dependencies as runtime

# 創建必要的目錄
RUN mkdir -p /app/logs /app/fonts /app/database/gacha_waifu /app/image_processing

# 複製項目代碼（開發環境會通過volume覆蓋）
COPY . .

# 暴露端口
EXPOSE 8000

# 執行應用程式
CMD ["python", "bot.py"]