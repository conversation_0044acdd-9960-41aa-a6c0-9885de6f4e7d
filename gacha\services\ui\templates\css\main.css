/* main.css - 主要CSS檔案，引入所有模組 */

/* 基礎樣式 */
@import 'base/reset.css';
@import 'base/variables.css';

/* 布局 */
@import 'layout/containers.css';

/* 組件 */
@import 'components/cards.css';
@import 'components/user-info.css';

/* 工具類 - animations.css 和 themes/dark-theme.css imports removed as obsolete */

/* 第三方庫樣式覆蓋 */
/* 在此處覆蓋第三方庫的默認樣式 */

/* 全局自定義樣式 */
/* .hidden class removed - Use Tailwind CSS '.hidden' class */

/* .text-truncate - REMOVED - Use Tailwind CSS '.truncate' class instead */

/* .transition-all - REMOVED - Covered by global styles in HTML for screenshots */

/* 增強用戶體驗的聚焦樣式 */
*:focus-visible {
  outline: none; /* This overrides reset.css focus style. Confirm if this is the desired behavior for screenshots. */
}

/* 自定義滾動條 - REMOVED - Covered by global styles in HTML for screenshots */

/* 列印樣式（可選） */
@media print {
  body {
    width: 100%;
    height: auto;
  }
  
  .profile-container {
    background: white;
    color: black;
  }
  
  .info-panel {
    border: 1px solid #ddd;
    box-shadow: none;
  }
} 