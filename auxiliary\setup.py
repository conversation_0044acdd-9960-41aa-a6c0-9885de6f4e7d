"""
輔助功能統一設置模組
負責註冊所有輔助功能 COG
"""

import os
from pathlib import Path
from typing import Any, Dict, List

from discord.ext import commands

from utils.logger import logger


class AuxiliarySetup:
    """輔助功能設置管理器"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.registration_status = {}  # 動態初始化

    def _scan_auxiliary_cogs(self) -> List[str]:
        """掃描 auxiliary/cogs 目錄中的所有 cog 文件

        Returns:
            List[str]: cog 名稱列表
        """
        cogs = []
        cogs_dir = "auxiliary/cogs"

        if os.path.exists(cogs_dir):
            for file_path in Path(cogs_dir).glob("*.py"):
                if file_path.name != "__init__.py":
                    cog_name = file_path.stem
                    cogs.append(cog_name)

        return sorted(cogs)

    async def setup_all_auxiliary_cogs(self) -> bool:
        """設置所有輔助功能 COG"""
        logger.info("開始註冊輔助功能 COG...")

        # 動態掃描 auxiliary cogs
        cog_names = self._scan_auxiliary_cogs()

        if not cog_names:
            logger.warning("⚠️ 沒有找到任何輔助功能 Cog 文件")
            return True  # 返回 True 因為這不是錯誤，只是沒有 cog 需要載入

        logger.info(
            "📁 找到 %s 個輔助功能 Cog: %s", len(cog_names), ", ".join(cog_names)
        )

        # 初始化註冊狀態字典
        for cog_name in cog_names:
            self.registration_status[cog_name] = False

        overall_success = True

        # 註冊各個 COG
        for cog_name in cog_names:
            module_path = f"auxiliary.cogs.{cog_name}"
            success = await self._register_cog(cog_name, module_path)
            overall_success = overall_success and success

        if overall_success:
            logger.info("✅ 所有輔助功能 COG 註冊成功")
        else:
            logger.warning("⚠️ 部分輔助功能 COG 註冊失敗")

        return overall_success

    async def _register_cog(self, cog_name: str, module_path: str) -> bool:
        """註冊單個 COG"""
        try:
            logger.info("➤ 註冊 %s COG...", cog_name)

            # 使用 Discord.py 原生的 load_extension 方法
            await self.bot.load_extension(module_path)
            self.registration_status[cog_name] = True
            logger.info("✓ %s COG 已註冊", cog_name)
            return True
        except commands.errors.ExtensionAlreadyLoaded:
            logger.info("ℹ️ %s COG 已載入，跳過。", cog_name)
            self.registration_status[cog_name] = True  # 標記為成功，因為它已經載入了
            return True
        except Exception as e:
            logger.error("❌ 註冊 %s COG 時發生錯誤: %s", cog_name, e, exc_info=True)
            return False

    def get_registration_status(self) -> Dict[str, Any]:
        """獲取註冊狀態報告"""
        return {
            "auxiliary_cogs": self.registration_status,
            "total_registered": sum(self.registration_status.values()),
            "total_cogs": len(self.registration_status),
            "success_rate": sum(self.registration_status.values())
            / len(self.registration_status)
            * 100,
        }


async def setup_auxiliary_cogs(bot: commands.Bot) -> bool:
    """設置輔助功能 COG 的入口函數"""
    auxiliary_setup = AuxiliarySetup(bot)
    return await auxiliary_setup.setup_all_auxiliary_cogs()
