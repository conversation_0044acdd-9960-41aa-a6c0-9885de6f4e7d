"""
Pioneer System - Quest Repository
任務進度相關的資料庫存取
"""

from typing import List, Optional

import asyncpg

from gacha.repositories._base_repo import execute_query, fetch_all, fetch_one
from pioneer.exceptions import PioneerDatabaseError
from pioneer.models.pioneer_models import QuestProgress
from utils.logger import logger

# ========================================
# 任務進度相關 (Quest Progress)
# ========================================


async def create_quest_progress(
    user_id: int,
    quest_id: str,
    target: int,
    connection: Optional[asyncpg.Connection] = None,
) -> QuestProgress:
    """為用戶創建一個新的任務進度記錄"""
    query = """
        INSERT INTO pioneer_quest_progress (user_id, quest_id, progress, target)
        VALUES ($1, $2, 0, $3)
        ON CONFLICT (user_id, quest_id) DO NOTHING
        RETURNING *
    """
    try:
        result = await fetch_one(
            query, (user_id, quest_id, target), connection=connection
        )
        if not result:
            # 如果已存在，則獲取現有記錄
            existing_query = "SELECT * FROM pioneer_quest_progress WHERE user_id = $1 AND quest_id = $2"
            result = await fetch_one(
                existing_query, (user_id, quest_id), connection=connection
            )

        if not result:
            # 這不應該發生，但作為一個保險措施
            raise PioneerDatabaseError(
                "create_quest_progress",
                f"無法創建或找到 user_id={user_id}, quest_id={quest_id} 的任務進度",
            )

        return QuestProgress(**result)
    except Exception as e:
        if isinstance(e, PioneerDatabaseError):
            raise
        logger.error("創建任務進度失敗 user_id=%s, quest_id=%s: %s", user_id, quest_id, e)
        raise PioneerDatabaseError("create_quest_progress", str(e), e) from e


async def get_user_active_quests(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[QuestProgress]:
    """獲取用戶所有未完成的任務"""
    query = "SELECT * FROM pioneer_quest_progress WHERE user_id = $1 AND completed_at IS NULL"
    try:
        results = await fetch_all(query, (user_id,), connection=connection)
        return [QuestProgress(**row) for row in results]
    except Exception as e:
        logger.error("獲取活躍任務失敗 user_id=%s: %s", user_id, e)
        raise PioneerDatabaseError("get_user_active_quests", str(e), e) from e


async def update_quest_progress(
    quest_progress_id: int,
    new_progress: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """更新任務進度"""
    query = """
        UPDATE pioneer_quest_progress
        SET progress = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
    """
    try:
        await execute_query(
            query, (quest_progress_id, new_progress), connection=connection
        )
        return True
    except Exception as e:
        logger.error("更新任務進度失敗 quest_progress_id=%s: %s", quest_progress_id, e)
        raise PioneerDatabaseError("update_quest_progress", str(e), e) from e


async def complete_quest(
    quest_progress_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """將任務標記為已完成"""
    query = """
        UPDATE pioneer_quest_progress
        SET completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
    """
    try:
        await execute_query(query, (quest_progress_id,), connection=connection)
        return True
    except Exception as e:
        logger.error("完成任務失敗 quest_progress_id=%s: %s", quest_progress_id, e)
        raise PioneerDatabaseError("complete_quest", str(e), e) from e
