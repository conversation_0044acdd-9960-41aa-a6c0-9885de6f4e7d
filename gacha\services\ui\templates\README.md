# 用戶檔案模板使用說明

## 簡介

這是一個用於生成用戶資料卡的HTML/CSS模板，專為截圖目的設計。模板包含現代化UI元素和動畫效果，使用了模組化CSS結構，便於維護和定制。

## 目錄結構

```
templates/
├── css/
│   ├── base/              # 基礎樣式
│   │   ├── reset.css      # 重置與全局樣式
│   │   └── variables.css  # CSS變數
│   ├── components/        # 元件樣式
│   │   ├── cards.css      # 卡片相關樣式
│   │   └── user-info.css  # 用戶資訊相關樣式
│   ├── layout/            # 布局樣式
│   │   └── containers.css # 容器相關樣式
│   ├── utilities/         # 工具樣式
│   │   └── animations.css # 動畫效果相關樣式
│   ├── themes/            # 主題樣式
│   │   └── dark-theme.css # 黑暗主題
│   └── main.css           # 主CSS文件，引入所有模組
├── profile_template.html  # 主HTML模板文件
├── background.png         # 背景圖
├── avatar.png             # 默認頭像
└── [1-5].gif              # 卡片圖片
```

## 特點

1. **模組化CSS**：
   - 採用分類清晰的CSS架構
   - 使用CSS變數實現一致的設計風格和簡化調整
   - 將不同功能的樣式分離到獨立文件

2. **良好視覺效果**：
   - 半透明背景濾鏡效果
   - 簡潔現代的卡片設計
   - 動畫效果增強視覺體驗

3. **針對截圖優化**：
   - 移除不必要的互動元素
   - 固定尺寸設計
   - 優化背景顯示，適合各種背景圖

## 使用方法

### 基本使用

1. 將用戶數據替換到HTML中對應ID的元素
2. 將用戶頭像和卡片圖片替換為實際圖片
3. 截圖生成最終的用戶資料卡

### 自定義背景

模板使用黑色玻璃濾鏡效果處理背景，減少色彩豐富的背景圖對內容的干擾。背景設置在 `containers.css` 中，可以調整透明度參數：

```css
.profile-container {
  background: 
      linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), /* 調整此處透明度 */
      url('../../background.png');
}
```

### 卡片圖片

模板支持展示5張卡片（1張主卡片和4張副卡片）：
- 主卡片：`1.gif`
- 副卡片：`2.gif`、`3.gif`、`4.gif`、`5.gif`

### 星級顯示

星級顯示由JavaScript控制，可在script標籤中的 `fillStars` 函數修改：

```javascript
fillStars('main-card-stars', 5);
fillStars('sub-card-stars-1', 4);
// 以此類推
```

## 技術說明

模板使用以下技術：

1. **HTML5/CSS3**：基礎結構和樣式
2. **Tailwind CSS**：快速建立UI組件和布局
3. **Font Awesome**：提供圖標
4. **CSS變數**：實現主題和樣式的統一管理
5. **JavaScript**：處理動態元素顯示 