-- 階段 1: 資料庫結構變更

-- 創建 stories 表
-- 這張表儲存每個故事的元數據
CREATE TABLE stories (
    id UUID PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255),
    theme_title VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 創建 story_turns 表
-- 這張表儲存故事中的每一個回合
CREATE TABLE story_turns (
    id SERIAL PRIMARY KEY,
    story_id UUID NOT NULL,
    turn_number INTEGER NOT NULL,
    role VARCHAR(50) NOT NULL,
    content TEXT,
    options TEXT[],
    summary TEXT,
    status_block TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_story
        FOREIGN KEY(story_id) 
        REFERENCES stories(id)
        ON DELETE CASCADE
);

-- 為新表創建索引以優化查詢效能
-- 索引 stories(user_id, created_at) 用於快速查找特定用戶的故事列表
CREATE INDEX idx_stories_user_id_created_at ON stories(user_id, created_at DESC);

-- 索引 story_turns(story_id) 用於快速獲取特定故事的所有回合
CREATE INDEX idx_story_turns_story_id ON story_turns(story_id);

-- 觸發器函數，用於在 stories 表更新時自動更新 updated_at 時間戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- 將觸發器應用於 stories 表
CREATE TRIGGER update_stories_updated_at
BEFORE UPDATE ON stories
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
