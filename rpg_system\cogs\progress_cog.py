"""
RPG進度管理系統的Discord Cog
處理進度查看、隊伍配置等相關的Discord命令和交互
使用純異常模式進行錯誤處理
"""

from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from bot import CustomAutoShardedBot
from rpg_system.exceptions import RPGSystemError
from rpg_system.services import user_progress_service
from rpg_system.views.embeds.progress_embeds import ProgressEmbedBuilder
from utils.logger import logger


class ProgressCog(commands.Cog, name="RPG進度"):
    """處理RPG進度相關指令"""

    def __init__(self, bot: CustomAutoShardedBot):
        self.bot = bot
        logger.info("ProgressCog initialized.")

    @app_commands.command(name="rpg_progress", description="查看RPG進度")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def rpg_progress(self, interaction: discord.Interaction):
        """
        查看玩家的RPG進度信息
        """
        await interaction.response.defer()
        user_id = interaction.user.id
        progress = await user_progress_service.get_user_progress(user_id)
        embed = ProgressEmbedBuilder.create_progress_embed(progress, interaction.user)
        await interaction.followup.send(embed=embed)

    @app_commands.command(name="team", description="隊伍配置和信息")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        action="操作類型：view(查看) 或 set(設置)",
        card_collection_id="要設置的卡牌收藏ID（僅在action=set時使用）",
    )
    async def team(
        self,
        interaction: discord.Interaction,
        action: str = "view",
        card_collection_id: Optional[int] = None,
    ):
        """
        隊伍配置和信息管理
        """
        await interaction.response.defer()
        user_id = interaction.user.id

        if action.lower() == "view":
            await self._handle_team_view(interaction, user_id)
        elif action.lower() == "set":
            await self._handle_team_set(interaction, user_id, card_collection_id)
        else:
            raise RPGSystemError("無效的操作類型。請使用 'view' 或 'set'。")

    async def _handle_team_view(self, interaction: discord.Interaction, user_id: int):
        """
        處理隊伍查看
        """
        team_details = await user_progress_service.get_team_details(user_id)
        embed = ProgressEmbedBuilder.create_team_embed(team_details, interaction.user)
        await interaction.followup.send(embed=embed)

    async def _handle_team_set(
        self,
        interaction: discord.Interaction,
        user_id: int,
        card_collection_id: Optional[int],
    ):
        """
        處理隊伍設置
        """
        if card_collection_id is None:
            raise RPGSystemError("請提供要設置的卡牌收藏ID。")

        await user_progress_service.set_team_formation(user_id, [card_collection_id])
        embed = ProgressEmbedBuilder.create_team_setup_result_embed(
            "success", f"隊伍配置設置成功！卡牌ID: {card_collection_id}"
        )
        await interaction.followup.send(embed=embed)


async def setup(bot: CustomAutoShardedBot):
    """設置Cog"""
    await bot.add_cog(ProgressCog(bot))
    logger.info("ProgressCog added successfully.")
