"""
用戶許願存儲庫模組 - 管理 gacha_user_wishes 表的模組級函數
"""

from typing import Any, Dict, List, Optional, Set

import asyncpg

from gacha.constants import RarityLevel
from gacha.exceptions import DatabaseOperationError
from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)
from utils.logger import logger

# 表名常量
TABLE_NAME = "gacha_user_wishes"


async def get_user_wishes(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取用戶的所有許願卡片"""
    query = f"SELECT * FROM {TABLE_NAME} WHERE user_id = $1 ORDER BY slot_index"
    results = await fetch_all(query, (user_id,), connection=connection)
    return [dict(row) for row in results] if results else []


async def get_user_wishes_by_rarity(
    user_id: int, rarity: RarityLevel, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取用戶特定稀有度的許願卡片 (使用 RarityLevel Enum)"""
    query = f"\n            SELECT uw.* FROM {TABLE_NAME} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1 AND mc.rarity = $2\n            ORDER BY uw.slot_index\n        "
    results = await fetch_all(query, (user_id, rarity.value), connection=connection)
    return [dict(row) for row in results] if results else []


async def get_user_wish_ids_by_rarity(
    user_id: int, rarity: RarityLevel, connection: Optional[asyncpg.Connection] = None
) -> List[int]:
    """獲取用戶特定稀有度的許願卡片ID列表 (使用 RarityLevel Enum)

    參數:
        user_id: 用戶ID
        rarity: 卡片稀有度 (RarityLevel Enum)

    返回:
        List[int]: 符合條件的卡片ID列表
    """
    query = f"\n            SELECT uw.card_id FROM {TABLE_NAME} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1 AND mc.rarity = $2\n        "
    results = await fetch_all(query, (user_id, rarity.value), connection=connection)
    return [row["card_id"] for row in results] if results else []


async def get_all_user_wishes_with_rarity_grouped(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[RarityLevel, List[int]]:
    """獲取用戶所有許願卡片的 ID，按稀有度分組"""
    query = f"\n            SELECT mc.rarity, uw.card_id\n            FROM {TABLE_NAME} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1\n        "
    results = await fetch_all(query, (user_id,), connection=connection)
    wishes_by_rarity: Dict[RarityLevel, List[int]] = {}
    if results:
        for row in results:
            try:
                rarity_level = RarityLevel(row["rarity"])
                card_id = row["card_id"]
                if rarity_level not in wishes_by_rarity:
                    wishes_by_rarity[rarity_level] = []
                wishes_by_rarity[rarity_level].append(card_id)
            except ValueError:
                logger.warning(
                    "[UserWishRepository] Invalid rarity value '%s' for card_id %s wished by user %s. Skipping.",
                    row["rarity"],
                    row["card_id"],
                    user_id,
                )
                continue
    return wishes_by_rarity


async def add_user_wish(
    user_id: int,
    card_id: int,
    slot_index: int,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """向用戶的許願列表添加一張卡片。成功返回記錄ID，失敗則拋出異常"""
    query = f"\n            INSERT INTO {TABLE_NAME} (user_id, card_id, slot_index)\n            VALUES ($1, $2, $3)\n            RETURNING id\n        "
    result = await fetch_value(
        query, (user_id, card_id, slot_index), connection=connection
    )
    if result is None:
        raise DatabaseOperationError(
            f"添加許願記錄失敗: user_id={user_id}, card_id={card_id}, slot_index={slot_index}"
        )
    return result


async def remove_user_wish(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """從用戶的許願列表中移除一張卡片。成功返回影響的行數 (應為1)，失敗則拋出異常"""
    query = f"DELETE FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
    status = await execute_query(query, (user_id, card_id), connection=connection)
    return status


async def get_user_wishes_with_details(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取用戶的所有許願卡片及其詳細信息 (JOIN gacha_master_cards)"""
    query = f"\n            SELECT\n                uw.user_id,\n                uw.card_id,\n                uw.slot_index,\n                uw.created_at,\n                mc.name AS card_name,\n                mc.series AS card_series,\n                mc.rarity AS card_rarity,\n                mc.image_url AS card_image_url,\n                mc.pool_type AS card_pool_type\n                -- Add other fields from gacha_master_cards as needed\n            FROM {TABLE_NAME} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1\n            ORDER BY uw.slot_index\n        "
    results = await fetch_all(query, (user_id,), connection=connection)
    detailed_wishes = []
    if results:
        for row in results:
            card_info = {
                "card_id": row["card_id"],
                "name": row["card_name"],
                "series": row["card_series"],
                "rarity": row["card_rarity"],
                "image_url": row["card_image_url"],
                "pool_type": row["card_pool_type"],
            }
            detailed_wishes.append(
                {
                    "user_id": row["user_id"],
                    "card_id": row["card_id"],
                    "slot_index": row["slot_index"],
                    "created_at": row["created_at"],
                    "card_info": card_info,
                }
            )
    return detailed_wishes


async def get_wished_card_ids_for_user(
    user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Set[int]:
    """檢查提供的卡片ID列表中，哪些是該用戶許願了的。返回許願了的卡片ID集合"""
    if not card_ids:
        return set()
    placeholders = ", ".join([f"${i + 2}" for i in range(len(card_ids))])
    query = f"\n            SELECT card_id\n            FROM {TABLE_NAME}\n            WHERE user_id = $1 AND card_id IN ({placeholders})\n        "
    params = [user_id] + card_ids
    results = await fetch_all(query, params, connection=connection)
    return {row["card_id"] for row in results} if results else set()


async def get_card_wishers_list(
    card_id: int,
    page: int = 1,
    per_page: int = 20,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 獲取指定卡片的許願者列表（分頁）"""
    offset = (page - 1) * per_page

    # 獲取總數
    count_query = f"""
        SELECT COUNT(DISTINCT uw.user_id) as total_count
        FROM {TABLE_NAME} uw
        WHERE uw.card_id = $1
    """
    count_result = await fetch_one(count_query, (card_id,), connection=connection)
    total_count = count_result["total_count"] if count_result else 0

    # 獲取分頁數據
    query = f"""
        SELECT
            uw.user_id,
            uw.slot_index,
            uw.created_at
        FROM {TABLE_NAME} uw
        WHERE uw.card_id = $1
        ORDER BY uw.created_at ASC
        LIMIT $2 OFFSET $3
    """
    results = await fetch_all(query, (card_id, per_page, offset), connection=connection)

    wishers = []
    if results:
        for row in results:
            wishers.append(
                {
                    "user_id": row["user_id"],
                    "slot_index": row["slot_index"],
                    "created_at": row["created_at"],
                }
            )

    total_pages = (total_count + per_page - 1) // per_page

    return {
        "wishers": wishers,
        "current_page": page,
        "total_pages": max(1, total_pages),
        "total_count": total_count,
        "per_page": per_page,
    }


async def get_user_wish_power_level(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """獲取用戶的許願強度

    參數:
        user_id: 用戶ID
        connection: 可選的數據庫連接

    返回:
        int: 用戶的許願強度，默認為1
    """
    from gacha.repositories._base_repo import fetch_value

    query = "SELECT wish_power_level FROM gacha_users WHERE user_id = $1"
    try:
        result = await fetch_value(query, (user_id,), connection=connection)
        if result is not None:
            logger.debug(
                "[UserWishRepository] 獲取用戶 %s 的許願強度: %s", user_id, result
            )
            return result
        else:
            logger.debug(
                "[UserWishRepository] 用戶 %s 不存在，返回默認許願強度1", user_id
            )
            return 1
    except Exception as e:
        logger.warning(
            "[UserWishRepository] 獲取用戶 %s 許願強度失敗: %s，返回默認值1", user_id, e
        )
        return 1
