import asyncio
import random
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.economy_service as economy_service

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot

from gacha.exceptions import (
    GameError,
    InsufficientBalanceError,
    MinBetNotMetError,
    OnCooldownError,
)
from gacha.services import game_stats_service
from utils.base_view import BaseView
from utils.logger import logger

# --- 遊戲配置 ---

# 遊戲常數
MIN_BET = 10
GRID_COLS = 3
GRID_ROWS = 3

# 形狀倍數 - RTP 約 97.1% (莊家優勢 2.9%)
SHAPE_MULTIPLIERS = {
    "3_diag": 3.8,  # 3 個對角線
    "3_straight": 7.0,  # 3 個水平或垂直
    "full_screen": 250.0,  # 全螢幕 (9個一樣)
}

# 符號配置：高勝率、低波動模型
# 權重總和為 1000
SYMBOL_CONFIG = {
    # --- 高價值符號 (低頻率，高倍數) ---
    "seven": {
        "weight": 5,
        "emoji": "<:seven:1383148228874207363>",
        "multiplier": 100.0,
    },
    "clover": {
        "weight": 15,
        "emoji": "<:clover:1383148196179738734>",
        "multiplier": 30.0,
    },
    "diamond": {
        "weight": 30,
        "emoji": "<:diamond:1383148219701395556>",
        "multiplier": 15.0,
    },
    "bell": {"weight": 50, "emoji": "<:bell:1383148192123850752>", "multiplier": 8.0},
    # --- 低價值符號 (高頻率，低倍數) ---
    "cherry": {
        "weight": 250,
        "emoji": "<:cherry:1383148188009238619>",
        "multiplier": 0.5,
    },
    "grapes": {
        "weight": 250,
        "emoji": "<:grapes:1383148212042596402>",
        "multiplier": 0.4,
    },
    "watermelon": {
        "weight": 200,
        "emoji": "<:watermelon:1383148233336819794>",
        "multiplier": 0.3,
    },
    "lemon": {
        "weight": 200,
        "emoji": "<:lemon:1383148200973697054>",
        "multiplier": 0.2,
    },
}

# 動畫 emoji 列表
ANIMATION_EMOJIS = [
    "<a:RNG1:1383397491134693377>",
    "<a:RNG2:1383397504661323896>",
    "<a:RNG3:1383397511791906866>",
    "<a:RNG4:1383397518704119851>",
    "<a:RNG5:1383397530993426442>",
    "<a:RNG6:1383397537091682324>",
    "<a:RNG7:1383397542087229521>",
    "<a:RNG8:1383397548781211749>",
]


# --- SlotMachineView (繼承自 BaseView 以統一錯誤處理) ---
class SlotMachineView(BaseView):
    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        cog: "SlotMachineCog",
        bet_amount: int,
    ):
        # 調用 BaseView 的 __init__
        super().__init__(bot=cog.bot, user_id=user.id, timeout=300)
        self.user = user
        self.cog = cog
        self.bet_amount = bet_amount
        self.message: Optional[discord.Message] = None
        self.double_bet.label = f"雙倍下注({bet_amount * 2})"

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # 首先執行父類的檢查 (user_id)
        if not await super().interaction_check(interaction):
            return False

        # 接著檢查此視圖特定的冷卻邏輯
        retry_after = self.cog.button_cooldown_mapping.update_rate_limit(interaction)
        if retry_after:
            # 拋出業務異常，讓 BaseView.on_error 統一處理
            raise OnCooldownError(
                message=f"⏰ 請稍等 {retry_after:.1f} 秒後再試", retry_after=retry_after
            )
        return True

    # 移除自定義的 on_error，完全依賴 BaseView 的實現

    @discord.ui.button(label="再玩一局", style=discord.ButtonStyle.primary, emoji="🎰")
    async def play_again(
        self, interaction: discord.Interaction, _button: discord.ui.Button
    ):
        await interaction.response.defer()
        await self.cog._start_slot_game_deferred(interaction, self.bet_amount)

    @discord.ui.button(
        label="雙倍下注", style=discord.ButtonStyle.secondary, emoji="💰"
    )
    async def double_bet(
        self, interaction: discord.Interaction, _button: discord.ui.Button
    ):
        await interaction.response.defer()
        await self.cog._start_slot_game_deferred(interaction, self.bet_amount * 2)

    def _update_buttons_state(self, current_balance: int):
        # 再玩一局按鈕 - 餘額不足時變灰色但文字不變
        self.play_again.disabled = current_balance < self.bet_amount
        self.play_again.style = (
            discord.ButtonStyle.gray
            if self.play_again.disabled
            else discord.ButtonStyle.primary
        )

        # 雙倍下注按鈕 - 餘額不足時變灰色但文字不變
        double_bet_amount = self.bet_amount * 2
        self.double_bet.disabled = current_balance < double_bet_amount
        self.double_bet.style = (
            discord.ButtonStyle.gray
            if self.double_bet.disabled
            else discord.ButtonStyle.secondary
        )


# --- SlotMachineCog ---
class SlotMachineCog(commands.Cog):
    def __init__(self, bot: BotType):
        self.bot = bot
        # 設置符號權重
        self.symbol_weights = []
        for symbol, config in SYMBOL_CONFIG.items():
            weight = (
                int(config["weight"])
                if isinstance(config["weight"], (str, float))
                else config["weight"]
            )
            self.symbol_weights.extend([symbol] * weight)

        self.button_cooldown_mapping = commands.CooldownMapping.from_cooldown(
            1.0, 2.5, lambda interaction: interaction.user
        )
        logger.info("SlotMachineCog 已載入")

    def _get_oil_emoji(self) -> str:
        from config.app_config import get_oil_emoji

        return get_oil_emoji()

    # --- 網格生成 ---
    def _generate_random_grid(self) -> List[List[str]]:
        """生成隨機動畫網格"""
        grid = []
        for _ in range(GRID_ROWS):
            row_symbols = [random.choice(ANIMATION_EMOJIS) for _ in range(GRID_COLS)]
            grid.append(row_symbols)
        return grid

    def _spin_reels(self) -> List[List[str]]:
        """生成最終結果"""
        grid = []
        for _ in range(GRID_ROWS):
            row_symbols = [random.choice(self.symbol_weights) for _ in range(GRID_COLS)]
            grid.append(row_symbols)
        return grid

    def _format_grid_display(self, grid: List[List[str]], convert_symbols: bool) -> str:
        """格式化網格顯示"""
        display_grid = []
        for row in grid:
            display_row = []
            for item in row:
                if convert_symbols and item in SYMBOL_CONFIG:
                    display_row.append(SYMBOL_CONFIG[item]["emoji"])
                else:
                    display_row.append(str(item))
            display_grid.append(display_row)

        # 使用 Markdown # 放大 emoji
        lines = [f"# {' '.join(row)}" for row in display_grid]
        return "\n".join(lines)

    # --- 核心邏輯：任意位置乘法檢查 ---
    def _check_wins(
        self, grid: List[List[str]], bet_amount: int
    ) -> Tuple[List[Dict], int]:
        """
        檢查網格的獲勝組合 (Slots Info 規則)
        """
        winning_combinations = []
        total_payout = 0

        # 1. 檢查 Full Screen (全螢幕)
        first_symbol = grid[0][0]
        # 檢查所有 9 個格子是否相同
        is_full_screen = all(
            grid[r][c] == first_symbol
            for r in range(GRID_ROWS)
            for c in range(GRID_COLS)
        )

        if is_full_screen:
            symbol_config = SYMBOL_CONFIG.get(first_symbol)
            if symbol_config:
                symbol_mult = (
                    float(symbol_config["multiplier"])
                    if isinstance(symbol_config["multiplier"], (str, int))
                    else symbol_config["multiplier"]
                )
                shape_mult = (
                    float(SHAPE_MULTIPLIERS["full_screen"])
                    if isinstance(SHAPE_MULTIPLIERS["full_screen"], (str, int))
                    else SHAPE_MULTIPLIERS["full_screen"]
                )
                # 總賠付 = 賭注 * 形狀倍數 * 符號倍數
                payout = round(bet_amount * shape_mult * symbol_mult)
                total_payout += payout
                winning_combinations.append(
                    {
                        "line_name": "全螢幕 (Full Screen)",
                        "symbol": first_symbol,
                        "symbol_emoji": symbol_config["emoji"],
                        "shape_mult": shape_mult,
                        "symbol_mult": symbol_mult,
                        "payout": payout,
                    }
                )
                # 中了全螢幕，就不再計算其他連線
                return winning_combinations, total_payout

        # 2. 定義所有 8 條可能的 3 連線
        lines_to_check = [
            # 水平 (Straight: 1.0x)
            {
                "type": "3_straight",
                "name": "上排水平",
                "cells": [(0, 0), (0, 1), (0, 2)],
            },
            {
                "type": "3_straight",
                "name": "中排水平",
                "cells": [(1, 0), (1, 1), (1, 2)],
            },
            {
                "type": "3_straight",
                "name": "下排水平",
                "cells": [(2, 0), (2, 1), (2, 2)],
            },
            # 垂直 (Straight: 1.0x)
            {
                "type": "3_straight",
                "name": "左列垂直",
                "cells": [(0, 0), (1, 0), (2, 0)],
            },
            {
                "type": "3_straight",
                "name": "中列垂直",
                "cells": [(0, 1), (1, 1), (2, 1)],
            },
            {
                "type": "3_straight",
                "name": "右列垂直",
                "cells": [(0, 2), (1, 2), (2, 2)],
            },
            # 對角線 (Diagonal: 0.5x)
            {"type": "3_diag", "name": "對角線 \\", "cells": [(0, 0), (1, 1), (2, 2)]},
            {"type": "3_diag", "name": "對角線 /", "cells": [(2, 0), (1, 1), (0, 2)]},
        ]

        # 3. 檢查每一條線
        for line_info in lines_to_check:
            cells = line_info["cells"]
            # Type assertion to help type checker understand cells structure
            if not isinstance(cells, list) or len(cells) < 3:
                continue
            # 獲取這條線上的三個符號
            cell0 = (
                cells[0]
                if isinstance(cells[0], (tuple, list)) and len(cells[0]) >= 2
                else (0, 0)
            )
            cell1 = (
                cells[1]
                if isinstance(cells[1], (tuple, list)) and len(cells[1]) >= 2
                else (0, 0)
            )
            cell2 = (
                cells[2]
                if isinstance(cells[2], (tuple, list)) and len(cells[2]) >= 2
                else (0, 0)
            )
            s1 = grid[cell0[0]][cell0[1]]
            s2 = grid[cell1[0]][cell1[1]]
            s3 = grid[cell2[0]][cell2[1]]

            # 如果三個符號都一樣
            if s1 == s2 == s3:
                symbol_config = SYMBOL_CONFIG.get(s1)
                if symbol_config:
                    # 計算獎勵
                    symbol_mult = (
                        float(symbol_config["multiplier"])
                        if isinstance(symbol_config["multiplier"], (str, int))
                        else symbol_config["multiplier"]
                    )
                    line_type = (
                        line_info["type"]
                        if isinstance(line_info["type"], str)
                        else str(line_info["type"])
                    )
                    shape_mult = (
                        float(SHAPE_MULTIPLIERS[line_type])
                        if isinstance(SHAPE_MULTIPLIERS[line_type], (str, int))
                        else SHAPE_MULTIPLIERS[line_type]
                    )

                    # 總賠付 = 總賭注 * 形狀倍數 * 符號倍數
                    payout = round(bet_amount * shape_mult * symbol_mult)
                    total_payout += payout

                    winning_combinations.append(
                        {
                            "line_name": line_info["name"],
                            "symbol": s1,
                            "symbol_emoji": symbol_config["emoji"],
                            "shape_mult": shape_mult,
                            "symbol_mult": symbol_mult,
                            "payout": payout,
                        }
                    )

        return winning_combinations, total_payout

    # --- Embed 顯示 (適應新規則) ---
    async def _create_game_embed(
        self,
        user: Union[discord.User, discord.Member],
        grid: List[List[str]],
        winning_combinations: List[Dict],
        total_payout: int,
        bet_amount: int,
        current_balance: int,
        stage: str = "result",
    ) -> discord.Embed:
        oil_emoji = self._get_oil_emoji()
        user_stats = await game_stats_service.get_user_game_stats(user.id, "slot")
        total_profit_loss = user_stats.get("total_profit_loss", 0) if user_stats else 0

        # --- 動畫階段 ---
        if stage in ["animation", "mixed"]:
            description = self._format_grid_display(grid, convert_symbols=False)
            embed = discord.Embed(description=description, color=0xFFD700)  # 金色
            embed.set_author(
                name=f"{user.display_name} 轉動中...", icon_url=user.display_avatar.url
            )
            footer_text = f"下注: {bet_amount} | 餘額: {current_balance} | 總盈虧: {total_profit_loss}"

        # --- 結果階段 ---
        else:
            profit = total_payout - bet_amount
            updated_total_profit_loss = total_profit_loss + profit

            # 設定顏色
            if profit > 0:
                embed_color = 0x00FF00  # 綠色
                if any(
                    win["line_name"] == "全螢幕 (Full Screen)"
                    for win in winning_combinations
                ):
                    embed_color = 0xFF0000  # 紅色 - 全螢幕頭獎
            else:
                embed_color = 0x808080  # 灰色

            # 構建結果網格 (需要轉換符號)
            grid_display = self._format_grid_display(grid, convert_symbols=True)

            # 🌟 構建新的結果資訊顯示
            result_info = []
            if winning_combinations:
                for win in winning_combinations:
                    # 格式: [線路類型]: [符號] = [獎金] (形狀倍數 x 符號倍數)
                    info_text = f"{win['line_name']}: {win['symbol_emoji']}×3 = `{win['payout']:,}` {oil_emoji} "
                    info_text += f"({win['shape_mult']}x × {win['symbol_mult']}x)"
                    result_info.append(info_text)

                result_text = "\n".join(result_info)

                # 全螢幕提示
                if any(
                    win["line_name"] == "全螢幕 (Full Screen)"
                    for win in winning_combinations
                ):
                    result_text += "\n💥 **全螢幕！ FULL SCREEN！** 恭喜獲得超級大獎！"
                elif profit > 0:
                    result_text += f"\n🎉 **總計贏得：{total_payout:,} {oil_emoji}**"

            else:
                # 沒中獎
                result_text = f"什麼都沒中 損失 `{bet_amount:,}` {oil_emoji}"

            # 組合完整描述 (結果資訊在上，網格在下)
            full_description = f"{result_text}\n{grid_display}"

            embed = discord.Embed(description=full_description, color=embed_color)
            embed.set_author(
                name=f"{user.display_name} 的拉霸機結果",
                icon_url=user.display_avatar.url,
            )
            footer_text = f"下注: {bet_amount} | 餘額: {current_balance} | 總盈虧: {updated_total_profit_loss}"

        # 設置頁腳
        embed.set_footer(
            text=footer_text,
            icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1382681804896473138/biometric.png",
        )
        return embed

    async def _record_game_stats(
        self,
        user_id: int,
        bet_amount: int,
        total_payout: int,
        grid: List[List[str]],
        winning_combinations: List[Dict],
    ):
        """記錄遊戲統計數據 (3x3 網格版本)"""
        profit = total_payout - bet_amount
        result = "win" if profit > 0 else "push" if profit == 0 else "lose"
        is_full_screen = any(
            win["line_name"] == "全螢幕 (Full Screen)" for win in winning_combinations
        )

        # 分析獲勝類型 (根據實際線路名稱)
        diagonal_wins = sum(
            1 for win in winning_combinations if "對角線" in win["line_name"]
        )
        straight_wins = sum(
            1
            for win in winning_combinations
            if ("水平" in win["line_name"] or "垂直" in win["line_name"])
        )

        # 統計符號出現次數 (3x3 網格)
        symbol_counts = {}
        for row in grid:
            for symbol in row:
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1

        game_data = {
            "bet": bet_amount,
            "payout": total_payout,
            "profit": profit,
            "result": result,
            "grid_symbols": str(grid),  # 3x3 網格符號
            "winning_lines": len(winning_combinations),
            "max_consecutive": 3 if winning_combinations else 0,  # 3x3 最多3個連續
            "is_jackpot": is_full_screen,
            "diagonal_wins": diagonal_wins,
            "straight_wins": straight_wins,
            "symbol_counts": symbol_counts,
            "grid_size": "3x3",  # 標記網格大小
        }
        await game_stats_service.record_game_result(user_id, "slot", game_data)

    # --- 遊戲流程 ---
    async def _execute_slot_game_row_by_row(
        self,
        interaction: discord.Interaction,
        message: Optional[discord.Message],
        animation_grid: List[List[str]],
        final_grid: List[List[str]],
        bet_amount: int,
        current_balance: int,
    ) -> tuple:
        # Safety check for message
        if message is None:
            raise ValueError("Message cannot be None")
        """執行拉霸機遊戲邏輯（逐排停止動畫）"""

        # 預先將最終結果的 symbol 轉換成 emoji
        final_grid_emojis = [
            [SYMBOL_CONFIG[symbol]["emoji"] for symbol in row] for row in final_grid
        ]

        # 初始等待
        await asyncio.sleep(0.5)

        # 逐排停止 (共 3 排)
        for row_idx in range(GRID_ROWS):
            await asyncio.sleep(0.8)  # 動畫間隔

            mixed_grid_display = []
            for r in range(GRID_ROWS):
                if r <= row_idx:
                    mixed_grid_display.append(final_grid_emojis[r])  # 顯示已停止的
                else:
                    mixed_grid_display.append(animation_grid[r])  # 顯示動畫

            # 如果不是最後一排，更新畫面
            if row_idx < GRID_ROWS - 1:
                mixed_embed = await self._create_game_embed(
                    interaction.user,
                    mixed_grid_display,
                    [],
                    0,
                    bet_amount,
                    current_balance,
                    stage="mixed",
                )
                # 統一使用 followup.edit_message
                await interaction.followup.edit_message(message.id, embed=mixed_embed)

        # 所有排停止後，稍微等待一下
        await asyncio.sleep(0.5)

        # 🌟 檢查獲勝
        winning_combinations, total_payout = self._check_wins(final_grid, bet_amount)

        # 發放獎勵
        if total_payout > 0:
            await economy_service.award_oil(
                user_id=interaction.user.id,
                amount=total_payout,
                transaction_type="game:slot_win",
                reason="Slot machine win",
            )
            current_balance += total_payout

        return final_grid, winning_combinations, total_payout, current_balance

    async def _start_slot_game_deferred(
        self, interaction: discord.Interaction, bet_amount: int
    ):
        """統一的拉霸機遊戲邏輯（已經defer過）"""
        # 檢查並扣除餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        if current_balance < bet_amount:
            raise InsufficientBalanceError(required=bet_amount, current=current_balance)

        await economy_service.award_oil(
            user_id=interaction.user.id,
            amount=-bet_amount,
            transaction_type="game:slot_bet",
            reason="Slot machine bet",
        )
        current_balance -= bet_amount

        # 1. 顯示初始全轉動動畫
        animation_grid = self._generate_random_grid()
        animation_embed = await self._create_game_embed(
            interaction.user,
            animation_grid,
            [],
            0,
            bet_amount,
            current_balance,
            stage="animation",
        )
        message = await interaction.followup.send(embed=animation_embed)

        # Check if message was created successfully
        if message is None:
            raise GameError("無法創建遊戲消息，請稍後再試。")

        # 生成最終結果
        final_grid = self._spin_reels()

        # 2. 執行動畫並計算結果
        (
            final_grid,
            winning_combinations,
            total_payout,
            current_balance,
        ) = await self._execute_slot_game_row_by_row(
            interaction,
            message,
            animation_grid,
            final_grid,
            bet_amount,
            current_balance,
        )

        # 3. 顯示最終結果
        final_embed = await self._create_game_embed(
            interaction.user,
            final_grid,
            winning_combinations,
            total_payout,
            bet_amount,
            current_balance,
            stage="result",
        )

        # 設置互動按鈕
        view = SlotMachineView(interaction.user, self, bet_amount)
        view._update_buttons_state(current_balance)
        # 統一使用 followup.edit_message
        await interaction.followup.edit_message(
            message.id, embed=final_embed, view=view
        )
        view.message = message

        # 記錄統計
        await self._record_game_stats(
            interaction.user.id,
            bet_amount,
            total_payout,
            final_grid,
            winning_combinations,
        )

    async def _start_slot_game(self, interaction: discord.Interaction, bet_amount: int):
        await interaction.response.defer()
        await self._start_slot_game_deferred(interaction, bet_amount)

    # --- 指令 ---
    @commands.cooldown(1, 2.5, commands.BucketType.user)
    @app_commands.command(
        name="slot", description="🎰 拉霸機遊戲 - 任意方向連線贏取獎勵！"
    )
    @app_commands.describe(amount=f"下注金額（最低{MIN_BET}油幣）")
    @app_commands.checks.cooldown(1, 2.5, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def slot_command(self, interaction: discord.Interaction, amount: int):
        if amount < MIN_BET:
            raise MinBetNotMetError(bet_placed=amount, min_bet=MIN_BET)
        # 注意：由於計算是基於總賭注，不需要檢查是否為線數的倍數
        await self._start_slot_game(interaction, amount)


async def setup(bot: BotType):
    await bot.add_cog(SlotMachineCog(bot))
    logger.info("SlotMachineCog has been added to the bot.")
