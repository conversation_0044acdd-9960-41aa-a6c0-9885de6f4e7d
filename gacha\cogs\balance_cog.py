"""
餘額查詢系統 COG
處理 /balance 指令
"""

from datetime import datetime
from typing import TYPE_CHECKING, Union

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from gacha.services import user_service  # 直接導入服務模組
from utils.logger import logger


class BalanceCog(commands.Cog, name="餘額查詢"):
    """處理餘額查詢指令"""

    def __init__(self, bot: BotType):
        self.bot = bot
        logger.info("BalanceCog initialized.")

    async def cog_load(self):
        logger.info("BalanceCog has been loaded.")

    @app_commands.command(name="balance", description="查看你的油幣餘額")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def balance(self, interaction: discord.Interaction):
        """處理餘額查詢命令"""
        await interaction.response.defer(ephemeral=False)
        user_id = interaction.user.id
        current_nickname = interaction.user.display_name

        # 直接調用服務層函數，確保用戶存在並獲取最新資訊
        # 任何異常將由全局錯誤處理器捕獲
        user = await user_service.get_user(
            user_id, create_if_missing=True, nickname=current_nickname
        )
        await user_service.ensure_nickname_updated(user_id, current_nickname)

        # 獲取油票餘額
        oil_ticket_balance = await user_service.get_oil_ticket_balance(user_id)

        # 創建嵌入式訊息
        embed = discord.Embed(color=discord.Color.blue(), timestamp=datetime.now())
        from config.app_config import get_oil_emoji

        embed.add_field(
            name="油幣餘額",
            value=f"{get_oil_emoji()} 餘額:`{user.oil_balance:,}`",
            inline=False,
        )

        available_tickets = int(oil_ticket_balance or 0)
        embed.add_field(
            name="油票餘額", value=f"💰 油票:`{available_tickets:,}`", inline=False
        )
        embed.add_field(
            name="總抽卡次數", value=f"{user.total_draws:,}次", inline=False
        )
        embed.add_field(
            name="獎勵系統",
            value="使用`/daily`領取每日獎勵，`/hourly`領取每小時獎勵",
            inline=False,
        )

        embed.set_thumbnail(url="https://cdn.dev.conquest.bot/thumbnails/transfer.png")
        embed.set_author(
            name=f"{current_nickname}的帳戶信息",
            icon_url=(
                interaction.user.display_avatar.url
                if interaction.user.display_avatar
                else None
            ),
        )
        embed.set_footer(text="此指令即將被刪除，請改用 /stats 指令查詢。")

        await interaction.followup.send(embed=embed)


async def setup(bot: BotType):
    """載入 BalanceCog"""
    await bot.add_cog(BalanceCog(bot))
    logger.info("BalanceCog has been added to the bot.")
