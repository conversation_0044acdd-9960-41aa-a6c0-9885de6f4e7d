"""
RPG技能相關的嵌入構建器
負責創建技能管理、獻祭等相關的Discord嵌入
"""

from datetime import datetime
from typing import Any, Dict, List

import discord

from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class SkillEmbedBuilder:
    """技能相關嵌入構建器"""

    @staticmethod
    def create_card_details_embed(card_details: Dict[str, Any]) -> discord.Embed:
        """
        創建卡牌詳細信息嵌入

        Args:
            card_details: 卡牌詳細信息

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"🃏 {card_details.get('name', 'Unknown Card')}",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow(),
            )

            # 基本信息
            embed.add_field(
                name="📊 基本信息",
                value=f"稀有度：{card_details.get('rarity', 'N')}\n"
                f"RPG等級：{card_details.get('rpg_level', 1)}\n"
                f"星級：{card_details.get('star_level', 0)}\n"
                f"收藏ID：{card_details.get('collection_id', 'Unknown')}",
                inline=True,
            )

            # 基礎屬性
            base_stats = card_details.get("base_stats", {})
            if base_stats:
                stats_text = "\n".join(
                    [
                        f"{stat_name}：{stat_value}"
                        for stat_name, stat_value in base_stats.items()
                    ]
                )
                embed.add_field(name="⚔️ 基礎屬性", value=stats_text, inline=True)

            # 裝備的技能
            equipped_skills = card_details.get("equipped_skills", {})
            if equipped_skills:
                active_skills = equipped_skills.get("active", [])
                passive_skills = equipped_skills.get("passive", {})

                if active_skills:
                    active_text = "\n".join([f"• {skill}" for skill in active_skills])
                    embed.add_field(name="⚔️ 主動技能", value=active_text, inline=False)

                if passive_skills:
                    passive_text = "\n".join(
                        [
                            f"• {slot}: {skill}"
                            for slot, skill in passive_skills.items()
                            if skill
                        ]
                    )
                    if passive_text:
                        embed.add_field(
                            name="🛡️ 被動技能", value=passive_text, inline=False
                        )

            return embed

        except Exception as e:
            logger.error("創建卡牌詳細嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建卡牌詳細信息時發生錯誤")

    @staticmethod
    def create_skill_proficiency_embed(
        learned_skills: List[Dict[str, Any]],
    ) -> discord.Embed:
        """
        創建技能熟練度嵌入

        Args:
            learned_skills: 已學習的技能列表

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title="🎓 技能熟練度",
                color=discord.Color.purple(),
                timestamp=discord.utils.utcnow(),
            )

            if not learned_skills:
                embed.description = "您還沒有學習任何技能。"
                return embed

            # 分類顯示技能
            active_skills = [
                s for s in learned_skills if s.get("skill_type") == "ACTIVE"
            ]
            passive_skills = [
                s for s in learned_skills if s.get("skill_type") == "PASSIVE"
            ]

            if active_skills:
                active_text = "\n".join(
                    [
                        f"• {skill.get('skill_id', 'Unknown')} (Lv.{skill.get('skill_level', 1)})"
                        for skill in active_skills[:10]  # 限制顯示數量
                    ]
                )
                embed.add_field(name="⚔️ 主動技能", value=active_text, inline=True)

            if passive_skills:
                passive_text = "\n".join(
                    [
                        f"• {skill.get('skill_id', 'Unknown')} (Lv.{skill.get('skill_level', 1)})"
                        for skill in passive_skills[:10]  # 限制顯示數量
                    ]
                )
                embed.add_field(name="🛡️ 被動技能", value=passive_text, inline=True)

            embed.set_footer(text=f"總共學習了 {len(learned_skills)} 個技能")

            return embed

        except Exception as e:
            logger.error("創建技能熟練度嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("無法顯示技能熟練度信息")

    @staticmethod
    def create_global_skills_page_embed(
        skills_data: List[Dict[str, Any]],
        current_page: int,
        total_pages: int,
        start_index: int,
    ) -> discord.Embed:
        """
        創建全局技能分頁嵌入

        Args:
            skills_data: 當前頁面的技能數據
            current_page: 當前頁碼
            total_pages: 總頁數
            start_index: 起始索引

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"🎯 全局技能管理 (第 {current_page}/{total_pages} 頁)",
                color=discord.Color.purple(),
                timestamp=discord.utils.utcnow(),
            )

            if not skills_data:
                embed.description = "📭 您還沒有學習任何全局技能"
                embed.color = discord.Color.orange()
            else:
                for i, skill in enumerate(skills_data, 1):
                    skill_info = (
                        f"**類型**: {skill.get('skill_type', 'Unknown')}\n"
                        f"**等級**: {skill.get('level', 1)}\n"
                        f"**經驗**: {skill.get('experience', 0)}\n"
                        f"**下級所需**: {skill.get('next_level_exp', 'Max')}"
                    )

                    embed.add_field(
                        name=f"{start_index + i}. {skill.get('skill_id', 'Unknown')} (ID: {skill.get('skill_id', 'N/A')})",
                        value=skill_info,
                        inline=True,
                    )

            # 添加說明
            embed.set_footer(text="使用下方按鈕進行分頁導航或選擇技能獻祭")

            return embed

        except Exception as e:
            logger.error("創建技能頁面嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能信息時發生錯誤")

    @staticmethod
    def create_sacrifice_options_embed(skill_id: str) -> discord.Embed:
        """
        創建獻祭選項嵌入

        Args:
            skill_id: 技能ID

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title=f"🔥 獻祭技能: {skill_id}",
            description="請選擇獻祭模式：",
            color=discord.Color.red(),
        )

        embed.add_field(
            name="🔥 獻祭所有同名技能卡牌",
            value="獻祭所有擁有此技能的卡牌（不包含最愛卡片）",
            inline=False,
        )

        embed.add_field(
            name="💎 包含最愛但保留一張",
            value="獻祭所有擁有此技能的卡牌（包含最愛，但至少保留一張）",
            inline=False,
        )

        return embed

    @staticmethod
    def create_sacrifice_result_embed(
        operation_type: str, result: Dict[str, Any]
    ) -> discord.Embed:
        """
        創建獻祭結果嵌入（View層職責）

        Args:
            operation_type: 操作類型
            result: 獻祭結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title=f"🔥 卡牌獻祭成功 ({operation_type})",
                description=result.get("message", "獻祭操作已完成"),
                timestamp=datetime.now(),
            )
            embed.color = discord.Color.orange()

            # 添加獻祭統計
            sacrificed_count = result.get("sacrificed_count", 0)
            total_exp = sum(result.get("skill_exp_gained", {}).values())

            embed.add_field(
                name="獻祭統計",
                value=f"獻祭卡牌: {sacrificed_count} 張\n獲得技能經驗: {total_exp} 點",
                inline=False,
            )

            # 添加技能經驗分配詳情
            skill_exp_gained = result.get("skill_exp_gained", {})
            if skill_exp_gained:
                # 這裡是 RPG 系統的技能，不是 Pioneer 系統的技能
                # RPG 系統的技能 ID 本身就是顯示名稱，不需要轉換
                skill_exp_text = "\n".join(
                    [
                        f"• {skill_id}: +{exp:,} 經驗"
                        for skill_id, exp in skill_exp_gained.items()
                    ]
                )
                embed.add_field(
                    name="技能經驗分配",
                    value=skill_exp_text[:1024],  # Discord限制
                    inline=False,
                )

            # 添加技能升級信息
            skills_upgraded = result.get("skills_upgraded", [])
            if skills_upgraded:
                embed.add_field(
                    name="🎉 技能升級",
                    value=f"升級技能: {', '.join(skills_upgraded)}",
                    inline=False,
                )

            # 添加詳細獻祭信息
            sacrifice_details = result.get("sacrifice_details", [])
            if sacrifice_details and len(sacrifice_details) <= 5:  # 只顯示前5個
                details_text = "\n".join(
                    [
                        f"• {detail['card_name']}: {detail['quantity_sacrificed']}張"
                        for detail in sacrifice_details[:5]
                    ]
                )
                embed.add_field(
                    name="獻祭詳情", value=details_text[:1024], inline=False
                )

            return embed

        except Exception as e:
            logger.error("創建獻祭結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建獻祭結果時發生錯誤")

    @staticmethod
    def create_sacrifice_error_embed(
        operation_type: str, error_message: str
    ) -> discord.Embed:
        """
        創建獻祭錯誤嵌入（View層職責）

        Args:
            operation_type: 操作類型
            error_message: 錯誤信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title=f"獻祭操作 ({operation_type}) 失敗",
            description=error_message,
            color=discord.Color.red(),
            timestamp=datetime.now(),
        )

        embed.add_field(
            name="建議", value="請檢查篩選條件是否正確，或稍後再試。", inline=False
        )

        return embed

    @staticmethod
    def create_sacrifice_warning_embed(operation: str, message: str) -> discord.Embed:
        """
        創建獻祭警告嵌入（View層職責）

        Args:
            operation: 操作類型
            message: 警告信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title=f"獻祭操作 ({operation}) 提醒",
            description=message,
            color=discord.Color.gold(),
            timestamp=datetime.now(),
        )

        embed.add_field(
            name="💡 提示",
            value="這通常是因為沒有符合條件的卡牌可以獻祭。",
            inline=False,
        )

        return embed

    @staticmethod
    def create_skill_equip_result_embed(result: Dict[str, Any]) -> discord.Embed:
        """
        創建技能裝備結果嵌入（View層職責）

        Args:
            result: 裝備結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title="⚔️ 技能裝備成功",
                description=result.get("message", "技能裝備操作已完成"),
                timestamp=datetime.now(),
            )

            # 添加卡牌信息
            embed.add_field(
                name="卡牌信息",
                value=f"收藏ID: {result.get('collection_id')}\n"
                f"卡牌ID: {result.get('card_id')}",
                inline=True,
            )

            # 添加技能信息
            embed.add_field(
                name="技能信息",
                value=f"技能ID: {result.get('skill_id')}\n"
                f"技能名稱: {result.get('skill_name', 'N/A')}\n"
                f"槽位: {result.get('slot_index')}",
                inline=True,
            )

            # 添加之前的技能信息（如果有）
            previous_skill = result.get("previous_skill")
            if previous_skill:
                embed.add_field(
                    name="替換信息", value=f"之前的技能: {previous_skill}", inline=False
                )

            return embed

        except Exception as e:
            logger.error("創建技能裝備結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能裝備結果時發生錯誤")

    @staticmethod
    def create_skill_equip_error_embed(error_message: str) -> discord.Embed:
        """
        創建技能裝備錯誤嵌入（View層職責）

        Args:
            error_message: 錯誤信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title="❌ 技能裝備失敗",
            description=error_message,
            color=discord.Color.red(),
            timestamp=datetime.now(),
        )

        embed.add_field(
            name="建議",
            value="請檢查卡牌ID、技能ID和槽位是否正確，或稍後再試。",
            inline=False,
        )

        return embed

    @staticmethod
    def create_skill_transfer_result_embed(result: Dict[str, Any]) -> discord.Embed:
        """
        創建技能轉移結果嵌入（View層職責）

        Args:
            result: 轉移結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title="🔄 技能轉移成功",
                description=result.get("message", "技能轉移操作已完成"),
                timestamp=datetime.now(),
            )
            embed.color = discord.Color.blue()

            # 添加轉移信息
            embed.add_field(
                name="轉移詳情",
                value=f"源卡牌: {result.get('source_collection_id')}\n"
                f"目標卡牌: {result.get('target_collection_id')}\n"
                f"轉移技能: {result.get('transferred_skill')}",
                inline=True,
            )

            # 添加槽位信息
            embed.add_field(
                name="槽位信息",
                value=f"源槽位: {result.get('source_slot')}\n"
                f"目標槽位: {result.get('target_slot')}",
                inline=True,
            )

            # 添加重要提醒
            embed.add_field(
                name="⚠️ 重要提醒", value="源卡牌已被刪除（按照文檔設計）", inline=False
            )

            # 添加之前的技能信息（如果有）
            previous_skill = result.get("previous_target_skill")
            if previous_skill:
                embed.add_field(
                    name="替換信息",
                    value=f"目標槽位之前的技能: {previous_skill}",
                    inline=False,
                )

            return embed

        except Exception as e:
            logger.error("創建技能轉移結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能轉移結果時發生錯誤")

    @staticmethod
    def create_skill_transfer_error_embed(error_message: str) -> discord.Embed:
        """
        創建技能轉移錯誤嵌入（View層職責）

        Args:
            error_message: 錯誤信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title="❌ 技能轉移失敗",
            description=error_message,
            color=discord.Color.red(),
            timestamp=datetime.now(),
        )

        embed.add_field(
            name="建議",
            value="請檢查卡牌ID、槽位格式是否正確，確保源槽位有技能且槽位類型匹配。",
            inline=False,
        )

        embed.add_field(
            name="槽位格式",
            value="主動技能: 0, 1, 2\n被動技能: slot_0, slot_1, slot_2",
            inline=False,
        )

        return embed

    @staticmethod
    def create_skill_unequip_result_embed(result: Dict[str, Any]) -> discord.Embed:
        """
        創建技能卸下結果嵌入（View層職責）

        Args:
            result: 卸下結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title="🔓 技能卸下成功",
                description=result.get("message", "技能卸下操作已完成"),
                timestamp=datetime.now(),
            )
            embed.color = discord.Color.orange()

            # 添加卸下信息
            embed.add_field(
                name="卸下詳情",
                value=f"卡牌ID: {result.get('card_id')}\n"
                f"收藏ID: {result.get('collection_id')}\n"
                f"卸下技能: {result.get('unequipped_skill')}",
                inline=True,
            )

            # 添加槽位信息
            embed.add_field(
                name="槽位信息",
                value=f"槽位: {result.get('slot')}\n類型: {result.get('slot_type')}",
                inline=True,
            )

            return embed

        except Exception as e:
            logger.error("創建技能卸下結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能卸下結果時發生錯誤")

    @staticmethod
    def create_skill_unequip_error_embed(error_message: str) -> discord.Embed:
        """
        創建技能卸下錯誤嵌入（View層職責）

        Args:
            error_message: 錯誤信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title="❌ 技能卸下失敗",
            description=error_message,
            color=discord.Color.red(),
            timestamp=datetime.now(),
        )

        embed.add_field(
            name="建議",
            value="請檢查卡牌ID和槽位是否正確，確保槽位有技能可以卸下。",
            inline=False,
        )

        return embed

    @staticmethod
    def _create_error_embed(message: str) -> discord.Embed:
        """
        創建錯誤嵌入
        """
        return discord.Embed(
            title="❌ 錯誤", description=message, color=discord.Color.red()
        )

    @staticmethod
    def create_sacrifice_help_embed() -> discord.Embed:
        """
        創建獻祭幫助嵌入
        """
        help_embed = discord.Embed(
            title="🔥 卡牌獻祭使用指南",
            description="歡迎使用卡牌獻祭功能！通過獻祭卡牌來獲取技能經驗：",
            color=discord.Color.orange(),
        )
        help_embed.add_field(
            name="🎯 獻祭單張卡片",
            value=(
                "**方法1：** 指定卡片ID\n"
                "`/sacrifice card_id=123`\n\n"
                "**方法2：** 指定卡片名稱\n"
                '`/sacrifice card_name="某卡片"`\n\n'
                "💡 **小提示：** 卡片名稱支持模糊搜尋"
            ),
            inline=False,
        )
        help_embed.add_field(
            name="📦 批量獻祭操作",
            value=(
                "**獻祭所有：** `/sacrifice operation=all`\n"
                "• 獻祭符合條件的所有卡片\n\n"
                "**保留一張：** `/sacrifice operation=keep_one`\n"
                "• 每種卡片保留1張，其餘全部獻祭\n\n"
                "**獻祭一張：** `/sacrifice operation=single`\n"
                "• 每種卡片只獻祭1張"
            ),
            inline=False,
        )
        help_embed.add_field(
            name="🔍 篩選條件組合",
            value=(
                '**按系列：** `series="某系列"`\n'
                "**按稀有度：** `rarity=C` (C/R/SR/SSR/UR/LR/EX)\n"
                '**按技能：** `skill_id="某技能"`\n\n'
                "**組合範例：**\n"
                '`/sacrifice operation=all series="某系列" rarity=C`\n'
                "獻祭某系列的所有C卡"
            ),
            inline=False,
        )
        help_embed.add_field(
            name="❤️ 收藏卡片處理",
            value=(
                "**預設：** 不會獻祭收藏卡片\n"
                "**強制獻祭：** 加上 `include_favorites=True`"
            ),
            inline=False,
        )
        help_embed.add_field(
            name="💫 獻祭收益",
            value=(
                "• 獻祭卡片可獲得技能經驗\n"
                "• 高稀有度卡片提供更多經驗\n"
                "• 高星級卡片額外加成經驗"
            ),
            inline=False,
        )
        help_embed.set_footer(
            text="🔥 所有獻祭操作都會顯示預覽並需要確認，請放心使用！"
        )
        return help_embed

    # ==================== 卡牌实例系统专用方法 ====================

    @staticmethod
    def create_card_instances_embed(
        instances: List[Dict[str, Any]], card_name: str
    ) -> discord.Embed:
        """
        創建卡牌實例列表嵌入

        Args:
            instances: 卡牌實例列表
            card_name: 卡牌名稱

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"🃏 {card_name} - 卡牌實例",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow(),
            )

            if not instances:
                embed.description = "沒有找到該卡牌的實例"
                embed.color = discord.Color.orange()
                return embed

            for i, instance in enumerate(instances, 1):
                # 實例基本信息
                instance_info = (
                    f"**類型**: {instance.get('display_type', '未知')}\n"
                    f"**收藏ID**: {instance.get('id')}\n"
                    f"**數量**: {instance.get('quantity', 1)}\n"
                    f"**RPG等級**: {instance.get('rpg_level', 1)}\n"
                    f"**星級**: {instance.get('star_level', 0)}"
                )

                # 添加特殊實例信息
                if instance.get("is_special"):
                    instance_info += f"\n**實例名**: {instance.get('special_instance_name', 'Unknown')}"

                # 技能信息
                active_skills = instance.get("formatted_active_skills", [])
                passive_skills = instance.get("formatted_passive_skills", [])

                if active_skills:
                    active_skill_names = [
                        skill.get("name", "Unknown")
                        for skill in active_skills
                        if skill.get("skill_id")
                    ]
                    if active_skill_names:
                        instance_info += (
                            f"\n**主動技能**: {', '.join(active_skill_names)}"
                        )

                if passive_skills:
                    passive_skill_names = [
                        skill.get("name", "Unknown")
                        for skill in passive_skills
                        if skill.get("skill_id")
                    ]
                    if passive_skill_names:
                        instance_info += (
                            f"\n**被動技能**: {', '.join(passive_skill_names)}"
                        )

                embed.add_field(
                    name=f"{i}. {instance.get('instance_description', '實例')}",
                    value=instance_info,
                    inline=True,
                )

            embed.set_footer(text=f"總共 {len(instances)} 個實例")
            return embed

        except Exception as e:
            logger.error("創建卡牌實例嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建卡牌實例信息時發生錯誤")

    @staticmethod
    def create_skill_modification_result_embed(result: Dict[str, Any]) -> discord.Embed:
        """
        創建技能修改結果嵌入

        Args:
            result: 修改結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title="⚔️ 技能配置修改成功",
                description=result.get("message", "技能配置修改操作已完成"),
                timestamp=discord.utils.utcnow(),
            )

            # 基本信息
            embed.add_field(
                name="卡牌信息",
                value=f"卡牌ID: {result.get('card_id')}\n"
                f"收藏ID: {result.get('collection_id')}\n"
                f"配置類型: {result.get('display_type', '未知')}",
                inline=True,
            )

            # 修改類型信息
            source_type = result.get("source_type")
            if source_type == "default":
                embed.add_field(
                    name="🆕 新實例創建",
                    value=f"從默認配置分離創建了新的特殊實例\n"
                    f"實例名: {result.get('special_instance_name', 'Unknown')}",
                    inline=True,
                )
            else:
                embed.add_field(
                    name="🔄 配置更新",
                    value=f"更新了特殊配置\n"
                    f"實例名: {result.get('source_id', 'Unknown')}",
                    inline=True,
                )

            # 技能配置信息
            new_skills = result.get("new_skills", {})
            if new_skills:
                skill_info = ""

                active_skills = new_skills.get("active", [])
                if active_skills and any(active_skills):
                    active_list = [skill for skill in active_skills if skill]
                    skill_info += f"**主動技能**: {', '.join(active_list)}\n"

                passive_skills = new_skills.get("passive", {})
                if passive_skills:
                    passive_list = [
                        f"{slot}: {skill}"
                        for slot, skill in passive_skills.items()
                        if skill
                    ]
                    if passive_list:
                        skill_info += f"**被動技能**: {', '.join(passive_list)}"

                if skill_info:
                    embed.add_field(name="新技能配置", value=skill_info, inline=False)

            return embed

        except Exception as e:
            logger.error("創建技能修改結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能修改結果時發生錯誤")

    @staticmethod
    def create_instance_transfer_result_embed(result: Dict[str, Any]) -> discord.Embed:
        """
        創建實例間技能轉移結果嵌入

        Args:
            result: 轉移結果

        Returns:
            Discord嵌入對象
        """
        try:
            embed = SuccessEmbed(
                title="🔄 實例間技能轉移成功",
                description=result.get("message", "技能轉移操作已完成"),
                timestamp=discord.utils.utcnow(),
            )
            embed.color = discord.Color.blue()

            # 轉移詳情
            embed.add_field(
                name="轉移詳情",
                value=f"源實例ID: {result.get('source_collection_id')}\n"
                f"目標卡牌ID: {result.get('target_card_id')}\n"
                f"新實例ID: {result.get('target_collection_id')}",
                inline=True,
            )

            # 技能信息
            transferred_skill = result.get("transferred_skill", {})
            embed.add_field(
                name="轉移技能",
                value=f"技能ID: {transferred_skill.get('skill_id', 'Unknown')}\n"
                f"源槽位: {result.get('source_slot')}\n"
                f"目標槽位: {result.get('target_slot')}",
                inline=True,
            )

            # 重要提醒
            embed.add_field(
                name="⚠️ 重要提醒",
                value="• 源卡牌實例已被刪除\n"
                "• 為目標卡牌創建了新的特殊配置\n"
                "• 此操作不可逆",
                inline=False,
            )

            return embed

        except Exception as e:
            logger.error("創建實例轉移結果嵌入失敗: %s", e)
            return SkillEmbedBuilder._create_error_embed("創建技能轉移結果時發生錯誤")
