import asyncio
import os
import sys

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# --- 配置 ---
# 從 .env 檔案載入環境變數
load_dotenv()

# 專案根目錄的絕對路徑
BASE_DIR = project_root

# 要移除的前綴 (根據您的範例)
# 這會將 /root/mimipolice/database/background_images/...
# 轉換為 database/background_images/...
PATH_PREFIX_TO_REMOVE = "/root/mimipolice/"


async def convert_paths():
    """連接到資料庫並將 background_image_url 中的絕對路徑轉換為相對路徑"""
    pool = get_pool()
    if not pool:
        print("數據庫連接池未初始化。")
        return

    async with pool.acquire() as conn:
        print("成功從連接池獲取連接。")

        # 獲取所有需要更新的 user_profiles, 包含 Linux 和 Windows 格式的絕對路徑
        rows = await conn.fetch(
            "SELECT user_id, background_image_url FROM user_profiles "
            "WHERE background_image_url IS NOT NULL AND "
            "(background_image_url LIKE '/%' OR background_image_url LIKE '_:%')"
        )

        if not rows:
            print("沒有找到需要轉換的絕對路徑。")
            return

        print(f"找到 {len(rows)} 個需要轉換的路徑。")

        updates = []
        for row in rows:
            user_id = row["user_id"]
            abs_path = row["background_image_url"]
            rel_path = None

            if abs_path.startswith(PATH_PREFIX_TO_REMOVE):
                # 處理 Linux 格式的路徑
                rel_path = abs_path[len(PATH_PREFIX_TO_REMOVE) :]
            elif os.path.isabs(abs_path):
                # 處理 Windows 格式的路徑
                try:
                    rel_path = os.path.relpath(abs_path, BASE_DIR)
                except ValueError:
                    print(
                        f"Skipping user {user_id}: path '{abs_path}' "
                        "is on a different drive."
                    )
                    continue

            if rel_path:
                # 將路徑分隔符統一為 /
                rel_path = rel_path.replace("\\", "/")
                updates.append((rel_path, user_id))
                print(f"  - User {user_id}: '{abs_path}' -> '{rel_path}'")
            else:
                print(
                    f"Skipping user {user_id}: path '{abs_path}' "
                    "could not be converted."
                )

        if updates:
            # 執行批量更新
            await conn.executemany(
                "UPDATE user_profiles SET background_image_url = $1 WHERE user_id = $2",
                updates,
            )
            print(f"\n成功更新 {len(updates)} 筆記錄。")
        else:
            print("\n沒有需要更新的記錄。")


async def main():
    print(f"專案根目錄 (BASE_DIR): {BASE_DIR}")
    print(f"將移除的路徑前綴: {PATH_PREFIX_TO_REMOVE}")
    await setup_connections()
    try:
        await convert_paths()
    except Exception as e:
        print(f"發生錯誤: {e}")
    finally:
        await close_connections()
        print("資料庫連接已關閉。")


if __name__ == "__main__":
    # 運行異步函數
    asyncio.run(main())
