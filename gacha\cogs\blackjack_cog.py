"""
Gacha系統21點遊戲 COG - 簡化版
處理21點遊戲的Discord命令和交互
所有邏輯都在 COG 內部，便於熱重載
"""

import random
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.economy_service as economy_service
import gacha.services.game_stats_service as game_stats_service
from gacha.exceptions import (
    GameError,
    InsufficientBalanceError,
    MinBetNotMetError,
    OnCooldownError,
)
from utils.base_view import BaseView
from utils.logger import logger


class GameResult(Enum):
    """遊戲結果枚舉"""

    BLACKJACK = "blackjack"
    DEALER_BLACKJACK = "dealer_blackjack"
    BUST = "bust"
    DEALER_BUST = "dealer_bust"
    PLAYER_WIN = "player_win"
    DEALER_WIN = "dealer_win"
    PUSH = "push"
    FIVE_CARD_TRICK = "five_card_trick"  # 新增：過五關


# 撲克牌 emoji 映射 - 提取為模組級常量
CARD_EMOJIS = {
    ("diamonds", "A"): "<:D1:1367810486250508299>",
    ("diamonds", "2"): "<:D2:1367810491153383496>",
    ("diamonds", "3"): "<:D3:1367810497176404049>",
    ("diamonds", "4"): "<:D4:1367810508203360256>",
    ("diamonds", "5"): "<:D5:1367810514591154216>",
    ("diamonds", "6"): "<:D6:1367810519951478815>",
    ("diamonds", "7"): "<:D7:1367810525056208896>",
    ("diamonds", "8"): "<:D8:1367810530500149359>",
    ("diamonds", "9"): "<:D9:1367810536758317097>",
    ("diamonds", "10"): "<:D10:1367810543020281926>",
    ("diamonds", "J"): "<:D11:1367810548686655639>",
    ("diamonds", "Q"): "<:D12:1367810555687079986>",
    ("diamonds", "K"): "<:D13:1367810561579946075>",
    ("spades", "A"): "<:S1:1367810413135269922>",
    ("spades", "2"): "<:S2:1367810419330252921>",
    ("spades", "3"): "<:S3:1367810424145313916>",
    ("spades", "4"): "<:S4:1367810429509697639>",
    ("spades", "5"): "<:S5:1367810434492797008>",
    ("spades", "6"): "<:S6:1367810439353864252>",
    ("spades", "7"): "<:S7:1367810444596875284>",
    ("spades", "8"): "<:S8:1367810449650880542>",
    ("spades", "9"): "<:S9:1367810454902276192>",
    ("spades", "10"): "<:S10:1367810459662684259>",
    ("spades", "J"): "<:S11:1367810466344079450>",
    ("spades", "Q"): "<:S12:1367810475378872400>",
    ("spades", "K"): "<:S13:1367810480999235675>",
    ("clubs", "A"): "<:C1:1367810267848773673>",
    ("clubs", "2"): "<:C2:1367810277525033061>",
    ("clubs", "3"): "<:C3:1367810294914748426>",
    ("clubs", "4"): "<:C4:1367810299985661993>",
    ("clubs", "5"): "<:C5:1367810304695730196>",
    ("clubs", "6"): "<:C6:1367810310202720366>",
    ("clubs", "7"): "<:C7:1367810314564931645>",
    ("clubs", "8"): "<:C8:1367810319610675272>",
    ("clubs", "9"): "<:C9:1367810324706623598>",
    ("clubs", "10"): "<:C10:1367810329895243868>",
    ("clubs", "J"): "<:C11:1367810333892280320>",
    ("clubs", "Q"): "<:C12:1367810337805701230>",
    ("clubs", "K"): "<:C13:1367810342515769385>",
    ("hearts", "A"): "<:H1:1367810346802348143>",
    ("hearts", "2"): "<:H2:1367810351818870896>",
    ("hearts", "3"): "<:H3:1367810356247920670>",
    ("hearts", "4"): "<:H4:1367810361448726649>",
    ("hearts", "5"): "<:H5:1367810366444142634>",
    ("hearts", "6"): "<:H6:1367810372156915782>",
    ("hearts", "7"): "<:H7:1367810377202532392>",
    ("hearts", "8"): "<:H8:1367810382135033896>",
    ("hearts", "9"): "<:H9:1367810387818450975>",
    ("hearts", "10"): "<:H10:1367810391257907200>",
    ("hearts", "J"): "<:H11:1367810396844462104>",
    ("hearts", "Q"): "<:H12:1367810402255110188>",
    ("hearts", "K"): "<:H13:1367810407791722516>",
    ("unknown", "?"): "❓",
}
# 隱藏牌 emoji
HIDDEN_CARD_EMOJI = "<:hd:1367815959645130762>"


@dataclass
class PlayingCard:
    """撲克牌類 - 使用 dataclass 簡化"""

    suit: str
    value: str

    @property
    def blackjack_value(self) -> int:
        """計算21點遊戲中的牌值"""
        if self.value in ["J", "Q", "K"]:
            return 10
        elif self.value == "A":
            return 11
        else:
            return int(self.value)

    def __str__(self) -> str:
        """返回牌的 Emoji 字符串表示"""
        return CARD_EMOJIS.get((self.suit, self.value), CARD_EMOJIS[("unknown", "?")])


class Deck:
    """牌組"""

    def __init__(self):
        suits = ["hearts", "spades", "diamonds", "clubs"]
        values = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        self.cards = [PlayingCard(suit, value) for suit in suits for value in values]
        self.shuffle()

    def shuffle(self):
        """洗牌"""
        random.shuffle(self.cards)

    def deal(self) -> Optional[PlayingCard]:
        """發牌"""
        if not self.cards:
            return None
        return self.cards.pop()


class Hand:
    """玩家或莊家的手牌"""

    def __init__(self):
        self.cards: list[PlayingCard] = []
        self._hard_value = 0

    def add_card(self, card: PlayingCard) -> None:
        """添加一張牌到手牌"""
        self.cards.append(card)
        self._recalculate_value()

    def _recalculate_value(self) -> None:
        """重新計算手牌值 - 更 Pythonic 的實現"""
        # 分離 A 和非 A 牌
        aces = [card for card in self.cards if card.value == "A"]
        non_aces = [card for card in self.cards if card.value != "A"]

        # 計算非 A 牌的總值
        non_ace_sum = sum(card.blackjack_value for card in non_aces)
        ace_count = len(aces)

        # 計算最佳的 A 值組合
        value = non_ace_sum + ace_count * 11
        aces_as_eleven = ace_count

        # 將 A 從 11 調整為 1，直到不爆牌
        while value > 21 and aces_as_eleven > 0:
            value -= 10
            aces_as_eleven -= 1

        self._hard_value = value

    @property
    def hard_value(self) -> int:
        """獲取手牌硬值"""
        return self._hard_value

    @property
    def is_blackjack(self) -> bool:
        """檢查是否為BlackJack"""
        return len(self.cards) == 2 and self._hard_value == 21

    @property
    def is_busted(self) -> bool:
        """檢查是否爆牌"""
        return self._hard_value > 21

    @property
    def is_five_card_trick(self) -> bool:
        """檢查是否為過五關 (Five Card Trick)"""
        return len(self.cards) == 5 and not self.is_busted

    def __str__(self) -> str:
        return " ".join(str(card) for card in self.cards)

    def __len__(self) -> int:
        return len(self.cards)


class BlackjackGame:
    """21點遊戲"""

    def __init__(self, user_id: int, bet: int, game_id: str):
        self.user_id = user_id
        self.bet = bet
        self.game_id = game_id  # 添加遊戲ID
        self.deck = Deck()
        self.player_hand = Hand()
        self.dealer_hand = Hand()
        self.game_over = False
        self.result: Optional[str] = None
        self.payout = 0

        # 追蹤玩家動作
        self.hit_count = 0
        self.stand_count = 0

        # 發初始牌
        for _ in range(2):
            player_card = self.deck.deal()
            dealer_card = self.deck.deal()
            if player_card:
                self.player_hand.add_card(player_card)
            if dealer_card:
                self.dealer_hand.add_card(dealer_card)

        self._check_initial_blackjack()

    def _check_initial_blackjack(self) -> None:
        """檢查初始發牌是否有Blackjack"""
        player_bj = self.player_hand.is_blackjack
        dealer_bj = self.dealer_hand.is_blackjack

        if player_bj and dealer_bj:
            self._end_game(GameResult.PUSH, self.bet)
        elif player_bj:
            self._end_game(GameResult.BLACKJACK, int(self.bet * 2.5))
        elif dealer_bj:
            self._end_game(GameResult.DEALER_BLACKJACK, 0)

    def _end_game(self, result: GameResult, payout: int) -> None:
        """結束遊戲的通用方法"""
        self.game_over = True
        self.result = result.value
        self.payout = payout

    def player_hit(self) -> Dict[str, Any]:
        """玩家要牌"""
        if self.game_over:
            return self.get_game_state()

        self.hit_count += 1  # 記錄要牌次數
        card = self.deck.deal()
        if card:
            self.player_hand.add_card(card)
        if self.player_hand.is_five_card_trick:
            self._end_game(GameResult.FIVE_CARD_TRICK, self.bet * 3)
        elif self.player_hand.is_busted:
            self._end_game(GameResult.BUST, 0)

        return self.get_game_state()

    def player_stand(self) -> Dict[str, Any]:
        """玩家結束"""
        if self.game_over:
            return self.get_game_state()

        self.stand_count += 1  # 記錄結束次數

        # 莊家補牌邏輯
        while self.dealer_hand.hard_value < 17:
            card = self.deck.deal()
            if card:
                self.dealer_hand.add_card(card)

        # 判斷勝負
        self._determine_winner()
        return self.get_game_state()

    def _determine_winner(self) -> None:
        """判斷勝負"""
        dealer_value = self.dealer_hand.hard_value
        player_value = self.player_hand.hard_value

        if self.dealer_hand.is_busted:
            self._end_game(GameResult.DEALER_BUST, self.bet * 2)
        elif dealer_value > player_value:
            self._end_game(GameResult.DEALER_WIN, 0)
        elif player_value > dealer_value:
            self._end_game(GameResult.PLAYER_WIN, self.bet * 2)
        else:
            self._end_game(GameResult.PUSH, self.bet)

    def _create_hand_state(
        self, hand: Hand, show_first_only: bool = False
    ) -> Dict[str, Any]:
        """創建手牌狀態的通用方法"""
        return {
            "cards": [{"suit": card.suit, "value": card.value} for card in hand.cards],
            "cards_emojis": [str(card) for card in hand.cards],
            "hard_value": hand.hard_value,
            "value": hand.hard_value,  # 保持兼容性
            "is_blackjack": hand.is_blackjack,
            "is_busted": hand.is_busted,
            "show_first_only": show_first_only,
        }

    def get_game_state(self) -> Dict[str, Any]:
        """獲取遊戲狀態"""
        return {
            "user_id": self.user_id,
            "game_id": self.game_id,  # 添加遊戲ID到狀態
            "game_over": self.game_over,
            "result": self.result,
            "payout": self.payout,
            "bet": self.bet,
            "player_hand": self._create_hand_state(self.player_hand),
            "dealer_hand": self._create_hand_state(
                self.dealer_hand, not self.game_over
            ),
            "hit_count": self.hit_count,
            "stand_count": self.stand_count,
        }


# --- Button Subclasses ---
class HitButton(discord.ui.Button):
    """要牌按鈕"""

    def __init__(self):
        super().__init__(
            label="要牌",
            style=discord.ButtonStyle.blurple,
            custom_id="blackjack:action:hit",
        )

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if not isinstance(view, BlackjackView):
            return
        await interaction.response.defer()
        game_id = view.game_state.get("game_id")
        if game_id:
            await view.cog._handle_game_action(interaction, "hit", str(game_id))


class StandButton(discord.ui.Button):
    """結束按鈕"""

    def __init__(self):
        super().__init__(
            label="結束",
            style=discord.ButtonStyle.red,
            custom_id="blackjack:action:stand",
        )

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if not isinstance(view, BlackjackView):
            return
        await interaction.response.defer()
        game_id = view.game_state.get("game_id")
        if game_id:
            await view.cog._handle_game_action(interaction, "stand", str(game_id))


class ReplayButton(discord.ui.Button):
    """重玩按鈕"""

    def __init__(
        self, *, label: str, style: discord.ButtonStyle, bet_amount: int, disabled: bool
    ):
        import time

        timestamp = str(int(time.time() * 1000))
        super().__init__(
            label=label,
            style=style,
            custom_id=f"blackjack:replay:{bet_amount}:{timestamp}",
            disabled=disabled,
        )
        self.bet_amount = bet_amount

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if not isinstance(view, BlackjackView):
            return
        await interaction.response.defer()
        await view.cog._start_new_game(interaction, self.bet_amount)


class BlackjackView(BaseView):
    """21點遊戲視圖"""

    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        game_state: Dict[str, Any],
        cog: "BlackjackCog",
        original_bet: int,
    ):
        super().__init__(bot=cog.bot, user_id=user.id, timeout=180)
        self.user = user
        self.game_state = game_state
        self.cog = cog
        self.original_bet = original_bet
        self.message: Optional[discord.Message] = None

        # 根據遊戲狀態添加按鈕
        if game_state.get("game_over", False):
            self._add_replay_buttons()
        else:
            self._add_game_buttons()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查互動是否來自遊戲擁有者並處理冷卻"""
        # 首先執行父類的檢查 (user_id)
        if not await super().interaction_check(interaction):
            return False

        # 接著檢查此視圖特定的冷卻邏輯
        retry_after = self.cog.button_cooldown_mapping.update_rate_limit(interaction)
        if retry_after:
            # 拋出異常，讓 on_error 統一處理
            raise OnCooldownError(f"⏰ 請稍等 {retry_after:.1f} 秒後再試", retry_after)

        return True

    def _add_game_buttons(self):
        """添加遊戲按鈕（要牌、結束）"""
        self.add_item(HitButton())
        self.add_item(StandButton())

    def _add_replay_buttons(self):
        """添加重玩按鈕 - 始終顯示，餘額不足時禁用按鈕"""
        current_balance = self.game_state.get("new_balance", 0)
        current_bet = self.original_bet

        # 按鈕配置：(標籤, 下注金額, 樣式, 是否有足夠餘額)
        button_configs = [
            (
                f"再來一局 ({current_bet} 油幣)",
                current_bet,
                discord.ButtonStyle.green,
                current_balance >= current_bet,
            ),
            (
                f"雙倍下注 ({current_bet * 2} 油幣)",
                current_bet * 2,
                discord.ButtonStyle.blurple,
                current_balance >= current_bet * 2,
            ),
        ]

        for label, bet_amount, style, has_balance in button_configs:
            self.add_item(
                ReplayButton(
                    label=label,
                    style=style if has_balance else discord.ButtonStyle.gray,
                    bet_amount=bet_amount,
                    disabled=not has_balance,
                )
            )


class BlackjackCog(commands.Cog):
    """處理21點遊戲命令的COG - 簡化版"""

    # 類常量
    MIN_BET = 10
    BLACKJACK_IMAGE_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1367925599309664366/blackjack.png"

    # 結果標題和顏色映射
    RESULT_TITLES = {
        "blackjack": "🎉 21點完美!",
        "dealer_blackjack": "🔸 莊家獲得21點!",
        "bust": "🔸 點數過高!",
        "dealer_bust": "🎉 莊家點數過高! 你獲勝!",
        "player_win": "🎉 你的點數更高! 獲勝!",
        "dealer_win": "🔸 莊家點數更高! 遊戲結束!",
        "push": "🔄 點數相同! 平局!",
        "five_card_trick": "👑 過五關! 神奇的勝利!",
    }

    RESULT_COLORS = {
        "blackjack": discord.Color.gold(),
        "dealer_blackjack": discord.Color.dark_red(),
        "bust": discord.Color.red(),
        "dealer_bust": discord.Color.green(),
        "dealer_win": discord.Color.red(),
        "player_win": discord.Color.green(),
        "push": discord.Color.blue(),
        "five_card_trick": discord.Color.purple(),
    }

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.active_games: Dict[str, BlackjackGame] = {}

        # 使用Discord.py官方的CooldownMapping來處理按鈕冷卻（1.0秒，針對用戶）
        self.button_cooldown_mapping = commands.CooldownMapping.from_cooldown(
            1.0, 1.0, lambda interaction: interaction.user
        )

    def _format_hand_display(self, value_str: str, cards_str: str) -> str:
        """格式化手牌顯示 - 按照原版格式"""
        value_int = int(value_str) if value_str.isdigit() else 0

        if value_int == 21:
            return f"# **{value_str}** 🔥 {cards_str}"
        elif value_int > 21:
            return f"# **{value_str}** 💥 {cards_str}"
        else:
            return f"# **{value_str}** {cards_str}"

    def _get_hand_display_info(
        self, hand_data: Dict[str, Any], hide_first_card: bool = False
    ) -> tuple[str, str]:
        """獲取手牌顯示信息 - 返回 (點數, 卡片字符串)"""
        cards_emojis = hand_data.get("cards_emojis", [])

        if not cards_emojis:
            return "0", "N/A"

        if hide_first_card and len(cards_emojis) > 1:
            # 只顯示第一張牌的點數和卡片
            cards = hand_data.get("cards", [])
            if cards:
                first_card = cards[0]
                value_map = {"J": "10", "Q": "10", "K": "10", "A": "11"}
                value_str = value_map.get(first_card["value"], str(first_card["value"]))
                cards_str = f"{cards_emojis[0]} {HIDDEN_CARD_EMOJI}"
                return value_str, cards_str
            return "0", f"{cards_emojis[0]} {HIDDEN_CARD_EMOJI}"
        else:
            # 顯示完整手牌
            value_str = str(hand_data.get("hard_value", hand_data.get("value", 0)))
            cards_str = " ".join(cards_emojis)
            return value_str, cards_str

    async def create_game_embed(
        self,
        user: Union[discord.User, discord.Member],
        game_state: Dict[str, Any],
        balance: int,
    ) -> discord.Embed:
        """創建遊戲 embed"""
        game_over = game_state.get("game_over", False)
        result = game_state.get("result")
        bet = game_state.get("bet", 0)
        payout = game_state.get("payout", 0)

        # 確定標題和顏色
        if game_over and result:
            title = self.RESULT_TITLES.get(result, "🎲 遊戲結束")
            color = self.RESULT_COLORS.get(result, discord.Color.blue())
        elif game_over:
            title = "21點遊戲結束"
            color = discord.Color.blue()
        else:
            title = "⏱️ 21點遊戲進行中"
            color = discord.Color.blue()

        # 構建描述
        description_parts = []

        # 添加輸贏信息（僅在遊戲結束時）
        if game_over and result:
            win_loss_amount = payout - bet
            if win_loss_amount > 0:
                description_parts.append(
                    f"✅ 獲得{win_loss_amount:,} <:oi:1382174314811363470>！"
                )
            elif win_loss_amount < 0:
                description_parts.append(
                    f"❌ 損失{abs(win_loss_amount):,} <:oi:1382174314811363470>！"
                )
            else:
                description_parts.append(
                    f"🔄 平局，返還{bet:,} <:oi:1382174314811363470>"
                )

            # 添加玩家統計（只在遊戲結束時顯示）
            stats = await self._get_user_stats(user.id)
            if stats:
                total_games = stats.get("total_games", 0)
                total_wins = stats.get("total_wins", 0)
                win_rate = (
                    round((total_wins / total_games) * 100, 1) if total_games > 0 else 0
                )
                description_parts.append(
                    f"<a:a_:1382444741798658228> 勝率`{win_rate}`% 總場次`{total_games:,}`"
                )

            description_parts.append("")  # 空行分隔

        # 手牌信息
        player_hand = game_state.get("player_hand", {})
        dealer_hand = game_state.get("dealer_hand", {})

        # 莊家手牌
        description_parts.append("**🎰 莊家手牌:**")
        hide_dealer_card = not game_over
        dealer_value_str, dealer_cards_str = self._get_hand_display_info(
            dealer_hand, hide_dealer_card
        )
        dealer_display = self._format_hand_display(dealer_value_str, dealer_cards_str)
        description_parts.append(dealer_display)
        description_parts.append("")  # 空行

        # 玩家手牌
        description_parts.append("**你的手牌:**")
        player_value_str, player_cards_str = self._get_hand_display_info(
            player_hand, False
        )
        player_display = self._format_hand_display(player_value_str, player_cards_str)
        description_parts.append(player_display)

        # 創建 embed
        embed = discord.Embed(
            title=title, description="\n".join(description_parts), color=color
        )

        # 設置縮圖
        embed.set_thumbnail(url=self.BLACKJACK_IMAGE_URL)

        # 設置footer - 常駐顯示所有信息
        footer_parts = [f"下注: {bet}"]
        footer_parts.append(f"餘額: {balance}")

        # 獲取總盈虧並添加到頁腳（常駐顯示）
        stats = await self._get_user_stats(user.id)
        if stats:
            total_profit_loss = stats.get("total_profit_loss", 0)
            footer_parts.append(f"總盈虧: {total_profit_loss}")

        footer_text = " | ".join(footer_parts)
        embed.set_footer(text=footer_text, icon_url=user.display_avatar.url)

        return embed

    async def _get_user_stats(self, user_id: int) -> Optional[Dict[str, Any]]:
        """獲取用戶統計數據"""
        stats = await game_stats_service.get_user_game_stats(user_id, "blackjack")
        return stats

    async def _update_game_stats(self, user_id: int, game_state: Dict[str, Any]):
        """更新遊戲統計"""
        bet = game_state.get("bet", 0)
        payout = game_state.get("payout", 0)
        original_result = game_state.get("result", "")

        # 獲取手牌信息
        player_hand = game_state.get("player_hand", {})
        dealer_hand = game_state.get("dealer_hand", {})

        profit_loss = payout - bet

        # 將詳細的遊戲結果轉換為標準的統計結果
        standardized_result = self._standardize_result(original_result, profit_loss)

        # 構建遊戲數據 - 使用標準化的result用於統計
        game_data = {
            "bet": bet,
            "payout": payout,
            "profit": profit_loss,
            "result": standardized_result,  # 使用標準化結果
            "original_result": original_result,  # 保留原始結果用於詳細統計
            "is_bust": player_hand.get("is_busted", False),
            "is_blackjack": player_hand.get("is_blackjack", False),
            "dealer_is_bust": dealer_hand.get("is_busted", False),
            "dealer_hand_value": dealer_hand.get("value", 0),
            "player_hand_value": player_hand.get("value", 0),
            "hit_count": game_state.get("hit_count", 0),
            "stand_count": game_state.get("stand_count", 0),
        }

        await game_stats_service.record_game_result(user_id, "blackjack", game_data)

    def _standardize_result(self, original_result: str, profit: int) -> str:
        """將21點的詳細結果轉換為標準的統計結果"""
        # 勝利結果
        win_results = {"blackjack", "dealer_bust", "player_win", "five_card_trick"}
        # 失敗結果
        loss_results = {"bust", "dealer_win", "dealer_blackjack"}

        if original_result in win_results:
            return "win"
        elif original_result in loss_results:
            return "lose"
        elif original_result == "push":
            return "push"
        else:
            # 備用邏輯：基於盈利判斷
            if profit > 0:
                return "win"
            elif profit == 0:
                return "push"
            else:
                return "lose"

    async def _start_new_game(self, interaction: discord.Interaction, bet: int):
        """開始新遊戲"""
        # 不在這裡 defer，因為調用者已經處理了響應

        # 檢查餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        if current_balance < bet:
            raise InsufficientBalanceError(required=bet, current=current_balance)

        # 扣除下注金額
        await economy_service.award_oil(
            user_id=interaction.user.id,
            amount=-bet,
            transaction_type="game:blackjack_bet",
            reason="Blackjack bet",
        )

        # 創建新遊戲 - 使用時間戳和用戶ID生成唯一遊戲ID
        import time

        game_id = f"{interaction.user.id}_{int(time.time() * 1000)}"
        game = BlackjackGame(interaction.user.id, bet, game_id)
        self.active_games[game_id] = game

        # 獲取遊戲狀態
        game_state = game.get_game_state()

        # 獲取新餘額
        new_balance_info = await economy_service.get_balance(interaction.user.id)
        new_balance = new_balance_info.get("balance", 0)
        game_state["new_balance"] = new_balance

        # 如果遊戲立即結束（如初始21點）
        if game_state.get("game_over", False):
            # 處理獎勵
            payout = game_state.get("payout", 0)
            if payout > 0:
                await economy_service.award_oil(
                    user_id=interaction.user.id,
                    amount=payout,
                    transaction_type="game:blackjack_win",
                    reason="Blackjack win",
                )
                # 更新餘額
                final_balance_info = await economy_service.get_balance(
                    interaction.user.id
                )
                game_state["new_balance"] = final_balance_info.get("balance", 0)

            # 更新統計
            await self._update_game_stats(interaction.user.id, game_state)

        # 創建視圖和embed
        view = BlackjackView(interaction.user, game_state, self, bet)
        embed = await self.create_game_embed(
            interaction.user, game_state, game_state["new_balance"]
        )

        # 發送訊息（調用者已經 defer，所以使用 followup）
        message = await interaction.followup.send(embed=embed, view=view)
        view.message = message

    async def _handle_game_action(
        self, interaction: discord.Interaction, action: str, game_id: str
    ):
        """處理遊戲動作（要牌、結束）"""
        game = self.active_games.get(game_id)
        if not game:
            raise GameError("找不到您的遊戲！可能已結束或因錯誤而中斷。")

        if game.user_id != interaction.user.id:
            raise GameError("這不是您的遊戲！")

        game_state: Dict[str, Any] = {}  # 確保變數總是被綁定
        try:
            # 執行遊戲邏輯
            game_state = game.player_hit() if action == "hit" else game.player_stand()
            is_game_over = game_state.get("game_over", False)

            # 如果遊戲結束，處理獎勵和統計
            if is_game_over:
                payout = game_state.get("payout", 0)
                if payout > 0:
                    await economy_service.award_oil(
                        user_id=interaction.user.id,
                        amount=payout,
                        transaction_type="game:blackjack_win",
                        reason="Blackjack win (split hand)",
                    )

                await self._update_game_stats(interaction.user.id, game_state)

            # 獲取最新餘額用於顯示
            final_balance_info = await economy_service.get_balance(interaction.user.id)
            game_state["new_balance"] = final_balance_info.get("balance", 0)

            # 準備UI更新
            view = BlackjackView(interaction.user, game_state, self, game.bet)
            embed = await self.create_game_embed(
                interaction.user, game_state, game_state["new_balance"]
            )

            # 嘗試更新訊息（這是最可能出錯的地方）
            await interaction.edit_original_response(embed=embed, view=view)

        finally:
            # **最終保證**：只要遊戲在邏輯上結束了，就必須從活躍遊戲中移除，防止記憶體洩漏。
            if game_state and game_state.get("game_over", False):
                if game_id in self.active_games:
                    del self.active_games[game_id]
                    logger.info(f"遊戲 {game_id} 已結束並成功從 active_games 中移除。")

    @app_commands.command(name="blackjack", description="開始一局21點遊戲")
    @app_commands.describe(bet="下注金額（最低10油幣）")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def blackjack_command(
        self, interaction: discord.Interaction, bet: app_commands.Range[int, 10]
    ):
        """處理21點遊戲命令 - 支援多局遊戲"""
        await interaction.response.defer(thinking=True)

        # 檢查下注金額，不符合則拋出業務異常
        if bet < self.MIN_BET:
            raise MinBetNotMetError(bet_placed=bet, min_bet=self.MIN_BET)

        # 移除活躍遊戲檢查，允許多局同時進行
        # 開始新遊戲，讓所有錯誤自然冒泡
        await self._start_new_game(interaction, bet)


async def setup(bot: commands.Bot):
    """將COG添加到Bot中 - 簡化版"""
    await bot.add_cog(BlackjackCog(bot))
    logger.info("BlackjackCog 已成功加載並註冊（簡化版）。")
