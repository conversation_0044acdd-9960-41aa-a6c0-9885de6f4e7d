from typing import TYPE_CHECKING, Any, Callable, Coroutine, Dict, List, Optional, Union

import discord
from discord.ext import commands

from gacha.exceptions import BusinessError, CardNotFoundError, InvalidOperationError
from gacha.models.filters import CollectionFilters
from gacha.models.models import UserCard

# 導入所有需要的服務模組
from gacha.services import (
    collection_service,
    economy_service,
    favorite_service,
    sorting_service,
)
from gacha.views.collection.collection_view.sort_position_modal import SortPositionModal
from gacha.views.collection.favorite_component import FavoriteButton
from gacha.views.ui_components.confirmation import ConfirmationView
from gacha.views.utils import get_user_friendly_rarity_name

if TYPE_CHECKING:
    pass
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

from .base_pagination import BasePaginationJumpModal, BasePaginationView
from .button_factory import UnifiedButtonFactory
from .collection_embed_builder import CollectionEmbedBuilder

# 常數定義
MIN_DESCRIPTION_STAR_LEVEL = 10


class CollectionView(BasePaginationView):
    """卡冊分頁視圖 (簡化版本)"""

    def __init__(
        self,
        bot: commands.Bot,
        user: Union[discord.User, discord.Member],
        cards: List[UserCard],
        current_page: int,
        total_pages: int,
        total_cards: int,
        unique_cards: int,
        sort_by: str,
        sort_order: str,
        filters: Optional[CollectionFilters] = None,
        interaction: Optional[discord.Interaction] = None,
        target_user: Optional[Union[discord.User, discord.Member]] = None,
        is_viewing_others: bool = False,
        favorite_priority: bool = True,
        list_mode: bool = False,
    ):
        """初始化卡冊分頁視圖"""
        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=current_page,
            total_pages=total_pages,
            timeout=600,
        )

        # 基本屬性
        self.user = user
        self.interaction = interaction
        self.cards = cards
        self.total_cards = total_cards
        self.unique_cards = unique_cards
        self.filters = filters or CollectionFilters()
        # 使用傳入的排序參數，而不是 filters 中的默認值
        self.sort_by = sort_by
        self.sort_order = sort_order
        # 同時更新 filters 中的排序信息以保持一致性
        self.filters.sort_by = sort_by
        self.filters.sort_order = sort_order
        self.favorite_priority = favorite_priority
        self.list_mode = list_mode
        self.CARDS_PER_PAGE = (
            collection_service.CARDS_PER_PAGE_LIST_MODE
            if list_mode
            else collection_service.CARDS_PER_PAGE
        )

        # 查看模式相關
        self.target_user = target_user or user
        self.is_viewing_others = is_viewing_others
        self.target_user_id = self.target_user.id

        # 狀態管理
        self.show_function_mode = False
        self.in_sort_mode = False
        self.current_card = None
        self.has_duplicates = self.total_cards > self.unique_cards
        self.cache = {}
        self.message = None

        # 初始化當前卡片和用戶信息
        self._sync_current_card()

        # 創建嵌入式訊息建構器
        self.embed_builder = CollectionEmbedBuilder(self)

        # 創建按鈕
        self._create_buttons()

    async def async_init(self):
        """異步初始化方法"""
        self.clear_items()
        self.add_pagination_buttons()

        # 列表模式下只顯示分頁按鈕，不添加其他功能按鈕
        if not self.list_mode and not self.is_viewing_others:
            self._add_main_mode_buttons()

        # 所有模式都需要更新按鈕狀態
        self._update_button_states()

    def _update_button_states(self):
        """更新按鈕狀態"""
        self._refresh_button_states()

        # 列表模式下不需要更新功能按鈕，只需要分頁按鈕狀態（已在 _refresh_button_states 中處理）
        if self.list_mode:
            return

        has_cards = bool(self.current_card)
        is_favorite = (
            has_cards and self.current_card.is_favorite if self.current_card else False
        )

        # 更新收藏按鈕
        if hasattr(self, "favorite_button"):
            self.favorite_button.disabled = not has_cards
            if has_cards:
                self.favorite_button.update_style(is_favorite=is_favorite)

        # 更新其他按鈕
        for button_name in ["sell_one_button", "sell_all_button", "function_button"]:
            button = getattr(self, button_name, None)
            if button:
                button.disabled = not has_cards

        # 更新功能模式下的動態按鈕
        self._update_function_mode_buttons(has_cards, is_favorite)

    def _update_function_mode_buttons(self, has_cards: bool, is_favorite: bool):
        """更新功能模式下的動態按鈕狀態"""
        if not self.show_function_mode:
            return

        # 創建回調函數到更新邏輯的映射，簡化更新邏輯
        button_update_rules = {
            self._sort_mode_callback: self._update_sort_mode_button,
            self._batch_favorite_callback: lambda btn: setattr(
                btn, "disabled", not has_cards
            ),
            self._batch_unfavorite_callback: lambda btn: setattr(
                btn, "disabled", not has_cards
            ),
            self._enhance_callback: lambda btn: setattr(
                btn, "disabled", not (has_cards and self.has_duplicates)
            ),
            self._description_callback: lambda btn: setattr(
                btn, "disabled", not has_cards
            ),
        }

        # 統一更新所有按鈕
        for item in self.children:
            if isinstance(item, discord.ui.Button) and hasattr(item, "callback"):
                update_func = button_update_rules.get(item.callback)
                if update_func:
                    if item.callback == self._sort_mode_callback:
                        # 排序按鈕需要特殊處理
                        update_func(item, has_cards, is_favorite)
                    else:
                        # 其他按鈕的通用處理
                        update_func(item)

    def _update_sort_mode_button(
        self, button: discord.ui.Button, has_cards: bool, is_favorite: bool
    ):
        """更新排序模式按鈕的專用方法"""
        has_filters = self.filters and self.filters.has_any_filter()
        UnifiedButtonFactory.update_sort_mode_button_state(
            button,
            has_cards,
            is_favorite,
            has_filters,
            self.favorite_priority,
        )

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed"""
        self.user_nickname = self.target_user.display_name
        return self.embed_builder.build_embed()

    async def _fetch_and_apply_page_data(self, page: int):
        """獲取指定頁面的數據並更新視圖的核心狀態"""
        # 重新獲取頁面數據 - 使用目標用戶ID
        collection_data = await collection_service.get_user_cards_paginated(
            user_id=self.target_user_id,
            page=page,
            sort_by=self.sort_by,
            sort_order=self.sort_order,
            filters=self.filters,
            favorite_priority=self.favorite_priority,
            list_mode=self.list_mode,
        )

        # 服務層應該在出錯時拋出異常，而不是返回帶有 'error' 鍵的字典
        if "error" in collection_data:
            raise BusinessError(f"獲取頁面數據失敗: {collection_data['error']}")

        # 更新數據
        self.cards = collection_data.get("cards", [])
        self.total_cards = collection_data.get("total_cards", 0)
        self.unique_cards = collection_data.get("unique_cards", 0)
        self.total_pages = collection_data.get("total_pages", 1)
        self.current_page = page
        self.has_duplicates = self.total_cards > self.unique_cards

        # 同步當前卡片
        self._sync_current_card()

        logger.debug(
            f"[GACHA][PAGE_DATA] 成功獲取頁面 {page} 數據: {len(self.cards)} 張卡片"
        )

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面內容 - 用於分頁按鈕"""
        # 1. 獲取並應用頁面數據
        await self._fetch_and_apply_page_data(page)

        # 2. 更新按鈕狀態和嵌入
        self._update_button_states()
        embed = await self.get_current_page_embed()

        # 3. 使用 interaction.response 更新消息（分頁按鈕的標準流程）
        await interaction.response.edit_message(embed=embed, view=self)

    async def _send_ephemeral_message(
        self, interaction: discord.Interaction, message: str
    ):
        """發送臨時消息"""
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=True)

    def _clear_page_cache(self, reason: str = ""):
        """清除頁面緩存"""
        self.cache.clear()
        logger.debug("[GACHA][CACHE] 清除緩存: %s", reason)

    def _create_jump_modal(self):
        """創建跳頁輸入框"""
        return BasePaginationJumpModal(self.bot, self, title="跳轉到指定卡片頁")

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        """實現FavoriteStateUpdatable介面，更新最愛狀態"""
        if not hasattr(self, "cards") or not self.cards:
            return False
        updated = False
        for user_card in self.cards:
            if (
                hasattr(user_card, "card")
                and user_card.card is not None
                and hasattr(user_card.card, "card_id")
                and (user_card.card.card_id == card_id)
            ):
                if hasattr(user_card, "is_favorite"):
                    user_card.is_favorite = is_favorite
                    updated = True
        return updated

    def _sync_current_card(self) -> None:
        """同步當前卡片狀態"""
        if self.list_mode:
            self.current_card = None
        else:
            self.current_card = self.cards[0] if self.cards else None
        self.user_nickname = self.target_user.display_name
        self.user_avatar_url = (
            getattr(self.target_user.display_avatar, "url", None)
            if hasattr(self.target_user, "display_avatar")
            else None
        )
        self.has_duplicates = self.total_cards > self.unique_cards

    def _create_buttons(self):
        """創建所有按鈕"""
        has_cards = bool(self.current_card)
        is_favorite = bool(
            has_cards and self.current_card and self.current_card.is_favorite
        )

        self.favorite_button = FavoriteButton(
            is_favorite=is_favorite, row=1, custom_id="collection_toggle_favorite"
        )
        self.favorite_button.disabled = not has_cards
        self.favorite_button.callback = self._favorite_callback

        self.sell_one_button = UnifiedButtonFactory.create_sell_button(has_cards, row=1)
        self.sell_one_button.callback = self._sell_one_callback

        self.sell_all_button = UnifiedButtonFactory.create_sell_all_button(
            has_cards, row=1
        )
        self.sell_all_button.callback = self._sell_all_callback

        self.function_button = UnifiedButtonFactory.create_function_button(
            has_cards, row=1
        )
        self.function_button.callback = self._function_callback

    def _add_main_mode_buttons(self):
        """添加主模式按鈕"""
        self.add_item(self.favorite_button)
        self.add_item(self.sell_one_button)
        self.add_item(self.sell_all_button)
        self.add_item(self.function_button)

    # ==================== 按鈕回調方法 ====================

    async def _favorite_callback(self, interaction: discord.Interaction):
        """收藏按鈕回調"""
        await interaction.response.defer()
        if not self.current_card or not self.current_card.card:
            return

        if not self.user:
            raise BusinessError("User not found.")
        await favorite_service.toggle_favorite_card(
            self.user.id, self.current_card.card.card_id, interaction.user.id
        )
        await self._refresh_page_data_only(interaction)

    async def _sell_one_callback(self, interaction: discord.Interaction):
        """賣一張按鈕回調"""
        await self._handle_sell_callback(interaction, 1)

    async def _sell_all_callback(self, interaction: discord.Interaction):
        """賣全部按鈕回調"""
        await self._handle_sell_callback(
            interaction, self.current_card.quantity if self.current_card else 0
        )

    async def _handle_sell_callback(
        self, interaction: discord.Interaction, quantity: int
    ):
        """處理賣卡回調的通用邏輯"""
        if not self.current_card:
            raise CardNotFoundError("沒有選定的卡片。")
        await self._process_sell_card(interaction, quantity)

    async def _function_callback(self, interaction: discord.Interaction):
        """功能按鈕回調"""
        await interaction.response.defer()
        self.show_function_mode = True
        self.clear_items()
        self.add_pagination_buttons()
        self._add_function_mode_buttons()
        self._update_button_states()
        await interaction.edit_original_response(view=self)

    def _add_function_mode_buttons(self):
        """添加功能模式按鈕"""
        has_cards = bool(self.current_card)
        is_favorite = bool(
            has_cards and self.current_card and self.current_card.is_favorite
        )

        batch_favorite_button = UnifiedButtonFactory.create_batch_favorite_button(
            has_cards, row=1
        )
        batch_favorite_button.callback = self._batch_favorite_callback
        self.add_item(batch_favorite_button)

        batch_unfavorite_button = UnifiedButtonFactory.create_batch_unfavorite_button(
            has_cards, row=1
        )
        batch_unfavorite_button.callback = self._batch_unfavorite_callback
        self.add_item(batch_unfavorite_button)

        back_button = UnifiedButtonFactory.create_back_button(row=1)
        back_button.callback = self._back_to_main_callback
        self.add_item(back_button)

        enhance_button = UnifiedButtonFactory.create_enhance_button(
            has_cards, self.has_duplicates, row=2
        )
        enhance_button.callback = self._enhance_callback
        self.add_item(enhance_button)

        has_filters = self.filters and self.filters.has_any_filter()
        sort_button = UnifiedButtonFactory.create_sort_mode_button(
            has_cards, is_favorite, has_filters, self.favorite_priority, row=2
        )
        sort_button.callback = self._sort_mode_callback
        self.add_item(sort_button)

        filter_button = UnifiedButtonFactory.create_filter_button(has_filters, row=2)
        filter_button.callback = self._filter_management_callback
        self.add_item(filter_button)

        description_button = UnifiedButtonFactory.create_description_button(
            has_cards, row=2
        )
        description_button.callback = self._description_callback
        self.add_item(description_button)

    async def _back_to_main_callback(self, interaction: discord.Interaction):
        """返回主模式按鈕回調"""
        await interaction.response.defer()
        self.show_function_mode = False
        self.clear_items()
        self.add_pagination_buttons()
        self._add_main_mode_buttons()
        self._update_button_states()
        await interaction.edit_original_response(view=self)

    async def _filter_management_callback(self, interaction: discord.Interaction):
        """篩選管理器按鈕回調"""
        from gacha.views.collection.filter_management_view import FilterManagementView

        await interaction.response.defer()
        filter_view = FilterManagementView(
            bot=self.bot,
            user=self.user,
            previous_view=self,
            filters=self.filters,
        )
        await interaction.edit_original_response(view=filter_view)

    # ==================== 功能模式回調方法 ====================

    async def _batch_favorite_callback(self, interaction: discord.Interaction):
        """批量收藏按鈕回調"""
        await interaction.response.defer()
        await self._handle_batch_operation(
            interaction, "加入最愛", self._confirm_batch_favorite_action
        )

    async def _handle_batch_operation(
        self,
        interaction: discord.Interaction,
        operation_type: str,
        confirm_callback_action,
    ):
        """處理批量最愛操作的通用邏輯"""
        target_card_ids = await collection_service.get_filtered_card_ids(
            self.target_user_id, self.filters
        )
        if not target_card_ids:
            raise BusinessError("目前篩選條件下沒有卡片可操作")

        num_cards = len(target_card_ids)
        filter_desc = favorite_service.get_filtered_cards_description(
            self.filters.pool_type, self.filters.rarity_in, self.filters.series
        )
        confirm_message = f"你確定要將 **{num_cards}** 張符合目前篩選條件 ({filter_desc}) 的卡片 **全部{operation_type}** 嗎？\n"
        if operation_type == "加入最愛":
            confirm_message += "這可能會影響部分卡片的自定義排序。"

        async def on_confirm(confirm_interaction: discord.Interaction):
            # The action is now directly awaited here
            await confirm_callback_action(confirm_interaction, target_card_ids)

        async def on_cancel(cancel_interaction: discord.Interaction):
            await self._handle_batch_operation_cancel(
                cancel_interaction, operation_type
            )

        embed = discord.Embed(
            title=f"確認批量{operation_type}",
            description=confirm_message,
            color=discord.Color.orange(),
        )

        if not self.bot:
            raise BusinessError("Bot is not available.")
        if not self.user:
            raise BusinessError("User not found.")
        view = ConfirmationView(
            bot=self.bot,
            user_id=self.user.id,
            on_confirm=on_confirm,
            on_cancel=on_cancel,
            confirm_label="確認",
            cancel_label="取消",
        )

        await interaction.followup.send(embed=embed, view=view, ephemeral=True)

    async def _confirm_batch_favorite_action(
        self, interaction: discord.Interaction, card_ids: list
    ):
        """確認批量加入最愛的操作"""
        await interaction.response.defer()
        await self._execute_batch_favorite(interaction, card_ids)

    async def _execute_batch_favorite(
        self, interaction: discord.Interaction, card_ids: list
    ):
        """執行批量加入最愛"""
        await self._execute_batch_db_operation(
            interaction, card_ids, favorite_service.batch_favorite_cards, "加入最愛"
        )

    async def _batch_unfavorite_callback(self, interaction: discord.Interaction):
        """批量取消收藏按鈕回調"""
        await interaction.response.defer()
        await self._handle_batch_operation(
            interaction, "取消最愛", self._confirm_batch_unfavorite_action
        )

    async def _confirm_batch_unfavorite_action(
        self, interaction: discord.Interaction, card_ids: list
    ):
        """確認批量取消最愛的操作"""
        await interaction.response.defer()
        await self._execute_batch_unfavorite(interaction, card_ids)

    async def _execute_batch_unfavorite(
        self, interaction: discord.Interaction, card_ids: list
    ):
        """執行批量取消最愛"""
        await self._execute_batch_db_operation(
            interaction, card_ids, favorite_service.batch_unfavorite_cards, "取消最愛"
        )

    async def _handle_batch_operation_cancel(
        self, interaction: discord.Interaction, operation_type: str
    ):
        """處理批量操作取消"""
        await interaction.response.defer()
        cancel_embed = discord.Embed(
            title=f"批量{operation_type}已取消",
            description="已取消批量操作。",
            color=discord.Color.light_grey(),
        )
        await interaction.edit_original_response(embed=cancel_embed, view=None)

    async def _execute_batch_db_operation(
        self,
        interaction: discord.Interaction,
        card_ids: list,
        service_method,
        operation_type: str,
    ):
        """通用批量資料庫操作執行器"""
        if not card_ids:
            raise BusinessError("找不到要操作的卡片 (ID列表為空)")

        if not self.user:
            raise BusinessError("User not found.")
        updated_count = await service_method(self.user.id, card_ids)
        await self._show_batch_operation_success(
            interaction, updated_count, operation_type
        )

    async def _show_batch_operation_success(
        self, interaction: discord.Interaction, updated_count: int, operation_type: str
    ):
        """顯示批量操作成功結果"""
        embed_title = f"批量{operation_type}成功"
        message = f"已成功{operation_type} {updated_count} 張卡片"
        embed = SuccessEmbed(title=embed_title, description=message)

        await interaction.edit_original_response(embed=embed, view=None)

        self._clear_page_cache(f"批量{operation_type}後清空緩存")
        await self._refresh_view_after_batch_operation()

    async def _refresh_view_after_batch_operation(self):
        """批次操作後刷新視圖，保持當前模式"""
        await self._fetch_and_apply_page_data(self.current_page)
        self._update_button_states()
        if self.message:
            embed = await self.get_current_page_embed()
            await self.message.edit(embed=embed, view=self)

    async def _show_batch_operation_error(
        self, interaction: discord.Interaction, error_message: str, operation_type: str
    ):
        """顯示批量操作錯誤結果"""
        embed_title = f"批量{operation_type}失敗"
        embed_color = discord.Color.red()
        embed = discord.Embed(
            title=embed_title, description=error_message, color=embed_color
        )
        await interaction.edit_original_response(embed=embed, view=None)

    async def _enhance_callback(self, interaction: discord.Interaction):
        """升星按鈕回調"""
        await interaction.response.defer()
        if not self.current_card:
            raise CardNotFoundError("沒有選定的卡片。")

        from gacha.views.collection.collection_view.enhance_confirm_view import (
            EnhanceConfirmView,
        )

        if not self.user:
            raise BusinessError("User not found.")
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("沒有選定的卡片")
        enhance_confirm_view = await EnhanceConfirmView.create_with_validation(
            bot=self.bot,
            original_interaction=interaction,
            user_id=self.user.id,
            card_id=self.current_card.card.card_id,
            collection_view=self,
        )
        await enhance_confirm_view.show_confirmation(interaction)

    async def _sort_mode_callback(self, interaction: discord.Interaction):
        """排序模式按鈕回調"""
        await interaction.response.defer()
        if not self.current_card or not self.current_card.is_favorite:
            raise BusinessError("只有收藏卡片才能進行排序。")
        await self._enter_custom_sort_mode(interaction)

    async def _enter_custom_sort_mode(self, interaction: discord.Interaction):
        """進入排序功能模式"""
        self.in_sort_mode = True
        await self._refresh_view_for_mode_change(interaction, "進入排序功能模式")

    async def _refresh_view_for_mode_change(
        self, interaction: discord.Interaction, reason: str
    ):
        """刷新視圖以應對模式變更"""
        logger.debug("[SORT_MODE] 刷新視圖: %s", reason)
        self._clear_page_cache(reason)
        await self._fetch_and_apply_page_data(self.current_page)
        self.clear_items()
        self.add_pagination_buttons()
        self._add_sort_mode_buttons()
        self._update_button_states()
        embed = await self.get_current_page_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    def _add_sort_mode_buttons(self):
        """添加排序模式按鈕"""
        has_cards = bool(self.current_card)

        move_top_button = UnifiedButtonFactory.create_move_to_top_button(
            has_cards, row=2
        )
        move_top_button.callback = self._move_to_top_callback
        self.add_item(move_top_button)

        move_up_button = UnifiedButtonFactory.create_move_up_button(has_cards, row=2)
        move_up_button.callback = self._move_up_callback
        self.add_item(move_up_button)

        move_down_button = UnifiedButtonFactory.create_move_down_button(
            has_cards, row=2
        )
        move_down_button.callback = self._move_down_callback
        self.add_item(move_down_button)

        move_bottom_button = UnifiedButtonFactory.create_move_to_bottom_button(
            has_cards, row=2
        )
        move_bottom_button.callback = self._move_to_bottom_callback
        self.add_item(move_bottom_button)

        move_position_button = UnifiedButtonFactory.create_move_to_position_button(
            has_cards, row=3
        )
        move_position_button.callback = self._move_to_position_callback
        self.add_item(move_position_button)

        back_to_function_button = UnifiedButtonFactory.create_back_to_function_button(
            row=3
        )
        back_to_function_button.callback = self._back_to_function_callback
        self.add_item(back_to_function_button)

    async def _description_callback(self, interaction: discord.Interaction):
        """設置描述按鈕回調"""
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("沒有選定的卡片。")

        card_info = await self._get_current_card_info()
        card_data = card_info["user_card"]
        current_star = card_info["star_level"]

        from gacha.views.collection.collection_view.card_description_modal import (
            CardDescriptionModal,
        )

        if not self.user:
            raise BusinessError("User not found.")
        if not isinstance(card_data, UserCard) or not card_data.card:
            raise CardNotFoundError("卡片資料無效")

        # 業務邏輯檢查已移至 service 層，此處直接創建 Modal
        modal = CardDescriptionModal(
            bot=self.bot,
            user_id=self.user.id,
            card_id=card_data.card.card_id,
            card_name=card_data.card.name,
            current_star=current_star,
            min_star=MIN_DESCRIPTION_STAR_LEVEL,
        )
        await interaction.response.send_modal(modal)

    async def _get_current_card_info(self):
        """獲取當前卡片信息"""
        if not self.current_card:
            raise CardNotFoundError("沒有選定的卡片")
        return {
            "user_card": self.current_card,
            "star_level": self.current_card.star_level,
        }

    # ==================== 賣卡處理方法 ====================

    async def _process_sell_card(self, interaction: discord.Interaction, quantity: int):
        """處理賣卡操作"""
        current_card = self.current_card
        if not current_card:
            raise CardNotFoundError("沒有選定的卡片")

        # 檢查是否為最愛卡片（優先級最高）
        if current_card.is_favorite:
            await self._show_sell_confirmation(interaction, quantity, "favorite")
            return

        # 檢查是否為稀有度5以上的卡片
        card = current_card.card
        if card and card.rarity.value >= 5:
            await self._show_sell_confirmation(interaction, quantity, "high_rarity")
            return

        # 直接賣出
        await self._execute_sell_operation(interaction, quantity)

    async def _show_sell_confirmation(
        self, interaction: discord.Interaction, quantity: int, confirmation_type: str
    ):
        """顯示賣卡確認對話框的通用方法

        Args:
            interaction: Discord 互動對象
            quantity: 賣出數量
            confirmation_type: 確認類型 ('favorite' 或 'high_rarity')
        """
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("沒有選定的卡片。")

        card_name = self.current_card.card.name

        # 根據確認類型設置不同的標題和描述
        if confirmation_type == "favorite":
            title = "⚠️ 確認賣出收藏卡片"
            description = f"您即將賣出 **{card_name}** x{quantity}，這是您的收藏卡片！\n\n確定要繼續嗎？"
            force_sell = True
            edit_original = False
        elif confirmation_type == "high_rarity":
            rarity_name = get_user_friendly_rarity_name(self.current_card.card.rarity)
            title = "⚠️ 確認賣出高稀有度卡片"
            description = f"您即將賣出 **{card_name}** x{quantity}，這是 **{rarity_name}** 卡片！\n\n確定要繼續嗎？"
            force_sell = False
            edit_original = True
        else:
            raise ValueError(f"不支援的確認類型: {confirmation_type}")

        embed = discord.Embed(
            title=title,
            description=description,
            color=discord.Color.orange(),
        )

        confirm_button = UnifiedButtonFactory.create_confirm_button(label="確認賣出")
        cancel_button = UnifiedButtonFactory.create_cancel_button(label="取消")

        async def confirm_callback(interaction: discord.Interaction):
            if not self.user or interaction.user.id != self.user.id:
                raise InvalidOperationError("只有原始命令發起者才能操作此按鈕。")
            await self._execute_sell_operation(interaction, quantity, force_sell=force_sell, edit_original=edit_original)

        async def cancel_callback(interaction: discord.Interaction):
            if not self.user or interaction.user.id != self.user.id:
                raise InvalidOperationError("只有原始命令發起者才能操作此按鈕。")
            await interaction.response.edit_message(
                content="已取消賣卡操作。", embed=None, view=None
            )

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback

        view = discord.ui.View()
        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)



    async def _execute_sell_operation(
        self, interaction: discord.Interaction, quantity: int, force_sell: bool = False, edit_original: bool = False
    ):
        """執行賣卡操作"""
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("沒有選定的卡片")
        if not self.user:
            raise BusinessError("User not found.")
        filters = CollectionFilters()
        filters.set_card_id(self.current_card.card.card_id)
        operation_type = "ALL" if quantity == self.current_card.quantity else "ONE"

        sell_result = await economy_service.sell_cards_universal(
            user_id=self.user.id,
            filters=filters,
            operation_type=operation_type,
            force_sell=force_sell,
        )

        from gacha.cogs.economy_cog import EconomyCog

        success_embed = EconomyCog._create_sell_embed(
            interaction, sell_result, "success"
        )

        if edit_original or force_sell:
            await interaction.response.edit_message(embed=success_embed, view=None)
        else:
            await interaction.response.send_message(embed=success_embed, ephemeral=True)

        await self._refresh_page_data_only(interaction)

    # ==================== 數據管理方法 ====================

    async def _refresh_page_data_only(
        self, interaction: Optional[discord.Interaction] = None
    ):
        """刷新頁面數據並自動更新原始卡冊訊息"""
        await self._fetch_and_apply_page_data(self.current_page)

        if self.current_page > self.total_pages:
            self.current_page = max(1, self.total_pages)
            await self._fetch_and_apply_page_data(self.current_page)

        self._update_button_states()

        if self.message:
            embed = await self.get_current_page_embed()
            if interaction and hasattr(interaction, "followup"):
                await interaction.followup.edit_message(
                    self.message.id, embed=embed, view=self
                )
            else:
                await self.message.edit(embed=embed, view=self)

    # ==================== 排序模式回調方法 ====================

    async def _move_to_top_callback(self, interaction: discord.Interaction):
        """置頂按鈕回調"""
        await interaction.response.defer()
        await self._handle_card_move(interaction, "top")

    async def _move_up_callback(self, interaction: discord.Interaction):
        """上移按鈕回調"""
        await interaction.response.defer()
        await self._handle_card_move(interaction, "up")

    async def _move_down_callback(self, interaction: discord.Interaction):
        """下移按鈕回調"""
        await interaction.response.defer()
        await self._handle_card_move(interaction, "down")

    async def _move_to_bottom_callback(self, interaction: discord.Interaction):
        """置底按鈕回調"""
        await interaction.response.defer()
        await self._handle_card_move(interaction, "bottom")

    async def _move_to_position_callback(self, interaction: discord.Interaction):
        """移至指定位置按鈕回調"""
        if not self.current_card:
            raise CardNotFoundError("當前頁面沒有卡片可移動")

        favorite_card_count = await collection_service.get_favorite_card_count(
            self.target_user_id
        )

        move_with_filters: Callable[
            [int, int, str], Coroutine[Any, Any, Dict[str, Any]]
        ] = sorting_service.move_card_to_position

        async def update_view_func(
            result: dict,
            interaction: discord.Interaction,
            exception: Optional[Exception] = None,
        ):
            if exception:
                raise exception

            position = result.get("position", "未知")
            await self._send_ephemeral_message(
                interaction, f"移動成功！卡片現在位於第 {position} 位。"
            )
            target_page = result.get("page", self.current_page)
            await self._refresh_view_and_ui(target_page)

        if not self.user:
            raise BusinessError("User not found.")
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("沒有選定的卡片")

        # The types are now compatible with the SortPositionModal
        modal = SortPositionModal(
            bot=self.bot,
            user_id=self.user.id,
            card_id=self.current_card.card.card_id,
            callback_func=move_with_filters,
            update_view_func=update_view_func,
            max_position=favorite_card_count,
        )
        await interaction.response.send_modal(modal)

    async def _handle_card_move(self, interaction: discord.Interaction, direction: str):
        """處理卡片移動操作的通用方法"""
        if not self.current_card or not self.current_card.card:
            raise CardNotFoundError("無法獲取當前卡片信息")
        if not self.user:
            raise BusinessError("User not found.")

        card_id = self.current_card.card.card_id

        # 執行資料庫操作
        result = await self._execute_move_operation(self.user.id, card_id, direction)
        message = self._get_success_message(direction, result)

        # 先發送臨時的反饋訊息
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=True)

        # 然後刷新主視圖
        target_page = result.get("page", self.current_page)
        await self._refresh_view_and_ui(target_page)

    async def _execute_move_operation(self, user_id: int, card_id: int, direction: str):
        """執行卡片移動操作"""
        logger.debug(
            "[GACHA][SORT][EXECUTE] 移動操作: user_id=%s, card_id=%s, direction=%s",
            user_id,
            card_id,
            direction,
        )

        if direction == "top":
            return await sorting_service.move_card_to_top(user_id, card_id)
        elif direction == "up":
            return await sorting_service.move_card_up(user_id, card_id)
        elif direction == "down":
            return await sorting_service.move_card_down(user_id, card_id)
        elif direction == "bottom":
            return await sorting_service.move_card_to_bottom(user_id, card_id)
        else:
            raise InvalidOperationError(f"未知的移動方向：{direction}")

    def _get_success_message(self, direction: str, result: dict) -> str:
        """獲取成功訊息"""
        position = result.get("position", "未知")
        direction_map = {"top": "置頂", "up": "上移", "down": "下移", "bottom": "置底"}
        action = direction_map.get(direction, direction)
        return f"{action}成功！卡片現在位於第 {position} 位。"

    async def _refresh_view_and_ui(self, target_page: int):
        """獲取新頁面數據並編輯視圖的原始訊息"""
        self._clear_page_cache("卡片移動操作完成")
        await self._fetch_and_apply_page_data(target_page)
        self._update_button_states()
        embed = await self.get_current_page_embed()
        if self.message:
            await self.message.edit(embed=embed, view=self)

    async def _back_to_function_callback(self, interaction: discord.Interaction):
        """返回功能模式按鈕回調"""
        await interaction.response.defer()
        self.in_sort_mode = False
        self._clear_page_cache("退出排序功能模式")
        await self._fetch_and_apply_page_data(self.current_page)
        self.clear_items()
        self.add_pagination_buttons()
        self._add_function_mode_buttons()
        self._update_button_states()
        embed = await self.get_current_page_embed()
        await interaction.edit_original_response(embed=embed, view=self)


__all__ = ["CollectionView"]
