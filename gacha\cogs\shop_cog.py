import discord
from discord import app_commands
from discord.ext import commands

from gacha.views.shop.shop_category_view import ShopCategoryView  # 我們將修改這個 View
from utils.logger import logger


class ShopCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("ShopCog (Commerce Hub) loaded.")

    @app_commands.command(name="shop", description="開啟商業中心，進行買賣交易")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def shop(self, interaction: discord.Interaction):
        # 創建一個新的、更通用的主頁面 Embed
        embed = discord.Embed(
            title="🏪 商業中心",
            description="歡迎來到商業中心！\n請從下方的選單選擇一項服務。",
            color=discord.Color.dark_teal(),
        )
        embed.add_field(
            name="🎫 油票兌換",
            value="使用抽卡獲得的油票，兌換各種稀有卡片兌換券。",
            inline=False,
        )
        embed.add_field(
            name="📦 資源出售",
            value="將你採集和製作的資源出售給系統，換取油幣。",
            inline=False,
        )
        embed.add_field(
            name="🎫 油票來源",
            value="透過 `/w` 抽卡獲得（每花費100油幣獲得1油票）",
            inline=False,
        )
        embed.set_footer(text="選擇一個選項以繼續...")
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif?ex=682f9ce9&is=682e4b69&hm=4c0eb6b418eaed804fe07b7cba125dd1daeee0212508d71f598be3c19294b647&"
        )

        # 使用改造後的 ShopCategoryView
        view = ShopCategoryView(bot=self.bot, user=interaction.user)
        await interaction.response.send_message(embed=embed, view=view)


async def setup(bot: commands.Bot):
    await bot.add_cog(ShopCog(bot))
