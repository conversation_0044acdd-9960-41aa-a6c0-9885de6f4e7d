from typing import Any, Awaitable, Callable, Optional, Union

import discord


async def display_game_end(
    interaction: discord.Interaction,
    game_name: str,
    game_result: Any,
    original_bet: int,
    view_provider: Callable[..., Awaitable[discord.ui.View]],
    game_specific_embed_builder: Callable[
        [Any, int], Union[discord.Embed, Awaitable[discord.Embed]]
    ],
    send_new_message: bool = False,
    awarded_amount: Optional[float] = None,
) -> None:
    """處理遊戲結束消息的顯示，包括embeds和帶有重玩選項的view。

    參數:
        interaction: Discord 互動對象
        game_name: 遊戲名稱
        game_result: 遊戲結果數據
        original_bet: 原始下注金額
        view_provider: 提供視圖的異步函數
        game_specific_embed_builder: 構建特定遊戲嵌入的函數
        send_new_message: 是否發送新消息而不是編輯現有消息
        awarded_amount: 獎勵金額（可選）
    """
    # 檢查 embed builder 是否為異步函數
    embed_result = game_specific_embed_builder(game_result, original_bet)
    embed: discord.Embed
    if isinstance(embed_result, discord.Embed):
        embed = embed_result
    else:
        embed = await embed_result
    game_over_view = await view_provider()
    message_content = ""

    # 直接發送訊息，Discord 異常會拋到 bot.py 處理
    if send_new_message:
        await interaction.followup.send(
            content=message_content,
            embed=embed,
            view=game_over_view,
            ephemeral=False,
        )
    else:
        await interaction.edit_original_response(
            content=message_content, embed=embed, view=game_over_view
        )
