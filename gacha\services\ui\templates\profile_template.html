<!DOCTYPE html>
<!--
  PROFILE TEMPLATE - STATIC SCREENSHOT VERSION

  IMPORTANT:
  - This HTML template is designed for static image generation (screenshots).
  - It has a fixed size (1080x720px) defined in the CSS (body tag in reset.css).
  - All interactive elements have been removed for consistent screenshot results.
  - Data is intended to be injected dynamically before rendering the screenshot.
  - Tailwind CSS is used for general styling, supplemented by custom CSS in css/main.css.
-->
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="用戶收藏卡片檔案展示">
    <meta name="theme-color" content="#121212">
    <title>用戶檔案</title>
    
    <!-- 第三方框架與字體 -->
    <script src="https://cdn.tailwindcss.com/"></script>
    
    <!-- 自定義CSS -->
    <link rel="stylesheet" href="css/main.css">
    
    <!-- 更豐富的字體選項 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome 圖標 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 隱藏滾動條 */
        ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
            display: none;
        }
        
        html, body {
            overflow: hidden;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
        }
        
        /* 右上角浮動LOGO樣式 */
        .floating-logo {
            position: absolute;
            bottom: -10px;
            right: -10px;
            width: 120px;
            height: auto;
            z-index: 50;
            opacity: 0.6;
            filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
        }
        
        /* 用戶狀態卡片樣式 */
        .user-status-card {
            background: rgba(30, 30, 34, 0.6);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-radius: 14px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            margin-left: 20px;
            max-width: 320px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
        }
        
        .user-status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            opacity: 0.6;
        }
        
        /* 新增：簡化版用戶狀態樣式 */
        .user-status-simple {
            display: flex;
            align-items: center;
            margin-top: 4px;
            background: rgba(30, 30, 34, 0.4);
            border-radius: 10px;
            padding: 4px 10px;
            max-width: 400px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 卡片展示區標題樣式 */
        .cards-section-title {
            text-align: center;
            font-size: 1.15rem;
            font-weight: 700;
            color: var(--color-text-light);
            margin-bottom: 6px;
            padding: 2px 18px;
            background: linear-gradient(90deg, rgba(0,0,0,0.15), rgba(30,30,34,0.6), rgba(0,0,0,0.15));
            display: inline-block;
            border-radius: var(--radius-md);
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }
        
        .cards-section-title::before,
        .cards-section-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 35px;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 215, 0, 0.6), transparent);
        }
        
        .cards-section-title::before {
            left: -40px;
        }
        
        .cards-section-title::after {
            right: -40px;
            transform: rotate(180deg);
        }
        
        .cards-section-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-top: 0;
            margin-bottom: 0;
        }
        
        .star-icon {
            color: #FFD700;
            filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
            font-size: 0.85em;
            margin: 0 4px;
            vertical-align: middle;
        }
        
        /* 主卡片信息样式新增 - 这些样式已移至 cards.css */
        /* .card-info-wrapper, .star-rating-container, .card-name-container, .card-series-container 等相关样式 */
        /* 此处保留为空白或可删除，以表示样式已外部化 */
    </style>
</head>
<body class="font-sans antialiased text-white">
    <div class="profile-container" id="profile-background">
        <!-- 浮動LOGO元素 -->
        <img src="logo.png" alt="Logo" class="floating-logo">
        
        <div class="absolute inset-0 flex flex-col main-content-area z-20">
            <!-- 上部內容區域 (用戶資訊和主卡片) -->
            <div class="flex w-full upper-content-row">
                <!-- 左側容器 (包含用戶資訊和副卡片) -->
                <div class="flex flex-col space-y-3 left-column w-[620px] flex-shrink-0" style="width: 620px !important;">
                    <!-- 左側用戶資訊欄 -->
                    <div class="user-info-column w-full">
                        <div class="info-panel user-info-panel">
                            <div class="flex mb-2">
                                <!-- 左側頭像固定 -->
                                <div class="user-avatar-container" id="user-avatar" style="background-image: url('avatar.png');"><span id="username-initial" style="display: none;">S</span></div>
                                
                                <!-- 右側用戶信息（垂直排列）-->
                                <div class="ml-6 flex flex-col">
                                    <h2 class="font-bold" id="username">soup</h2>
                                    <p class="text-gray-400 text-sm mt-1">ID: #123456</p>
                                    <div class="user-status-simple">
                                        <i class="fas fa-comment-dots mr-2 text-blue-400"></i>
                                        <p id="user-status" class="text-sm italic">「保持熱愛，奔赴下一個SSR！」</p>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1 user-stats">
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-coins fa-sm"></i> 油幣:</span><span class="stat-value" id="oil-balance">3</span></div>
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-ticket-alt fa-sm"></i> 油票:</span><span class="stat-value" id="oil-ticket-balance">133</span></div>
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-trophy fa-sm"></i> 收集率:</span><span class="stat-value" id="completion-rate">80.37%</span></div>
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-medal fa-sm"></i> 抽卡次數:</span><span class="stat-value" id="total-draws">51/59</span></div>
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-heart fa-sm"></i> 讚數:</span><span class="stat-value flex items-center"><span id="like-count" class="mr-1">16</span><i class="fas fa-fire-alt text-red-500 fa-xs"></i></span></div>
                                <div class="flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0"><span class="stat-label"><i class="fas fa-images fa-sm"></i> 擁有卡片:</span><span class="stat-value" id="total-owned">N/A</span></div>
                                <div class="col-span-2 flex justify-between items-center px-2 py-[1px] border-b border-white border-opacity-[0.04] rounded-xl last:border-b-0">
                                    <div class="text-lg w-full flex items-center justify-center" id="rarity-counts">
                                        <i class="fas fa-chart-pie fa-sm mr-2 text-gray-400"></i>
                                        <span class="rarity-tag rarity-c">C: <span class="count" id="rarity-c-count">0</span></span>
                                        <span class="rarity-tag rarity-r">R: <span class="count" id="rarity-r-count">0</span></span>
                                        <span class="rarity-tag rarity-sr">SR: <span class="count" id="rarity-sr-count">0</span></span>
                                        <span class="rarity-tag rarity-ssr">SSR: <span class="count" id="rarity-ssr-count">0</span></span>
                                        <span class="rarity-tag rarity-ur">UR: <span class="count" id="rarity-ur-count">0</span></span>
                                        <span class="rarity-tag rarity-lr">LR: <span class="count" id="rarity-lr-count">0</span></span>
                                        <span class="rarity-tag rarity-ex">EX: <span class="count" id="rarity-ex-count">0</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 副卡片顯示區域 -->
                    <div class="cards-section-wrapper" id="sub-cards-section">
                        <div class="cards-section-title" style="margin-bottom: 3px;">
                            <span class="star-icon">★</span>我的最愛<span class="star-icon">★</span>
                        </div>
                        <div class="flex justify-between gap-3 w-full p-0" id="sub-cards-row">
                            <div class="sub-card-container" id="sub-card-container-1">
                                <img id="sub-card-img-1" src="2.gif" alt="副卡片1" class="object-cover w-full h-full bg-transparent border-0">
                                <div class="card-stars" id="sub-card-stars-1">
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                </div>
                                <div class="sub-card-name" id="sub-card-name-1">副卡片名稱1</div>
                            </div>
                            <div class="sub-card-container" id="sub-card-container-2">
                                <img id="sub-card-img-2" src="3.gif" alt="副卡片2" class="object-cover w-full h-full bg-transparent border-0">
                                <div class="card-stars" id="sub-card-stars-2">
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                    <span class="star-normal">★</span>
                                </div>
                                <div class="sub-card-name" id="sub-card-name-2">副卡片名稱2</div>
                            </div>
                            <div class="sub-card-container" id="sub-card-container-3">
                                <img id="sub-card-img-3" src="4.gif" alt="副卡片3" class="object-cover w-full h-full bg-transparent border-0">
                                <div class="card-stars" id="sub-card-stars-3">
                                    <span class="star-special">★</span>
                                    <span class="star-special">★</span>
                                </div>
                                <div class="sub-card-name" id="sub-card-name-3">副卡片名稱3</div>
                            </div>
                            <div class="sub-card-container" id="sub-card-container-4">
                                <img id="sub-card-img-4" src="5.gif" alt="副卡片4" class="object-cover w-full h-full bg-transparent border-0">
                                <div class="card-stars" id="sub-card-stars-4">
                                    <span class="star-rare">★</span>
                                    <span class="star-rare">★</span>
                                    <span class="star-rare">★</span>
                                </div>
                                <div class="sub-card-name" id="sub-card-name-4">副卡片名稱4</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右側主卡片欄 -->
                <div class="main-card-column ml-2 flex-grow flex justify-start pt-20" id="main-card-section">
                    <div class="main-card-container">
                        <img id="main-card-img" src="1.gif" alt="主展示卡片" class="object-cover w-full h-full rounded-2xl bg-transparent border-0">
                    </div>
                    <!-- 重构的卡片信息区域 -->
                    <div class="card-info-wrapper">
                        <div class="star-rating-container">
                            <div id="main-card-stars">
                                <!-- Stars will be injected here by Playwright -->
                            </div>
                        </div>
                        <div class="card-name-container">
                            <h3 id="main-card-name" class="text-xl font-bold text-white tracking-wide">Teto Kasane</h3>
                        </div>
                        <div class="card-series-container">
                            <p id="main-card-series" class="text-gray-400 text-sm italic">Vocaloid</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>