"""
Pioneer System 任務初始化服務
負責根據玩家狀態（如首次創建、時代晉升）為其解鎖和創建任務進度。
"""

from typing import Optional

import asyncpg

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from utils.logger import logger


class TaskInitializer:
    """處理任務初始化和解鎖的邏輯"""

    async def initialize_quests_for_new_user(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> None:
        """
        為新創建的用戶初始化所有第一時代的非週期性任務。
        """
        logger.info("為新用戶 %s 初始化第一時代的任務...", user_id)
        await self.check_and_unlock_new_quests(user_id, 1, connection)

    async def check_and_unlock_new_quests(
        self,
        user_id: int,
        new_era: int,
        connection: Optional[asyncpg.Connection] = None,
    ) -> None:
        """
        檢查並為玩家解鎖新時代的任務。

        Args:
            user_id (int): 玩家ID。
            new_era (int): 玩家當前或新達到的時代。
            connection (Optional[asyncpg.Connection]): 數據庫連接。
        """
        try:
            assert game_data is not None
            all_tasks = game_data.get_config("tasks")
            for quest_id, task_config in all_tasks.items():
                # 只處理成就和時代任務的自動解鎖
                if task_config.type not in [
                    "achievement",
                    "era_quest",
                    "daily",
                    "weekly",
                ]:
                    continue

                # 檢查任務是否屬於當前或更早的時代
                if task_config.unlock_era and task_config.unlock_era <= new_era:
                    # 獲取任務目標
                    # 注意：一個任務可能有多個目標，但進度模型是單一的。
                    # 此處簡化處理，取第一個目標的 target。複雜任務需要更精細的設計。
                    if task_config.objectives:
                        target = task_config.objectives[0].get("target", 1)
                        await repositories.create_quest_progress(
                            user_id, quest_id, target, connection
                        )

            logger.info("為用戶 %s 檢查並解鎖了時代 %s 的任務。", user_id, new_era)

        except Exception as e:
            logger.error("為用戶 %s 解鎖時代 %s 的任務失敗: %s", user_id, new_era, e)

    async def refresh_periodic_quests(
        self, user_id: int, connection: Optional[asyncpg.Connection] = None
    ) -> None:
        """
        刷新玩家的每日和每週任務。
        (此為簡化實現，完整的實現需要處理重置時間邏輯)
        """
        # TODO: 實現完整的每日/每週任務刷新邏輯
        # 1. 獲取所有 daily/weekly 任務配置
        # 2. 檢查玩家的 QuestProgress 記錄
        # 3. 如果記錄不存在，或 completed_at/updated_at 已過重置時間，則創建/重置進度
        pass


# 創建一個單例供其他模塊使用
task_initializer = TaskInitializer()
