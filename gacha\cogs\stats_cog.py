"""
抽卡統計 Cog - 統一儀表板
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import asyncpg
import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button

from config.app_config import (
    get_config,
    get_default_rarity_emojis,
    get_minigame_emoji,
    get_pool_type_names,
)
from database.postgresql.async_manager import get_pool
from gacha.exceptions import DatabaseOperationError, UserNotFoundError
from gacha.models.models import GachaUser
from gacha.services import collection_service, game_stats_service, user_service
from gacha.views.utils import get_pool_type_choices
from utils.base_view import BaseView
from utils.logger import logger


# 常量定義
class Thumbnails:
    PROFILE = "https://cdn.discordapp.com/attachments/1336020673730187334/1390336286148726804/user-profile.png?ex=686b4501&is=6869f381&hm=3b8a14ff6a925cef51743ff8f272f833720b865240b1c689621514a335a135f4&"
    DRAW_STATS = "https://cdn.discordapp.com/attachments/1336020673730187334/1384908990907027586/bar-chart.png?ex=68542481&is=6852d301&hm=93eed2f0dd7fb0e43bef9d52cbeacd5d97bb642055ac8b836e6ff058638c5106&"
    COLLECTION = "https://cdn.discordapp.com/attachments/1336020673730187334/1390336285695725638/cards.png?ex=686b4501&is=6869f381&hm=9146212440b64744767c23a8d3cf0a28e335b14a5a2a4f051e8e3446c8c11429&"
    MINIGAME = "https://cdn.discordapp.com/attachments/1336020673730187334/1390336285209194577/casino-chip.png?ex=686b4501&is=6869f381&hm=e3311414c83959755583312433032c024c687a6331f342f28a43d331b155dcb8&"
    GLOBAL_LUCK = "https://cdn.discordapp.com/attachments/1336020673730187334/1386057739150692352/four-leaf.png?ex=6858525c&is=685700dc&hm=d88f66d74369d307f3accf3352f00d10458c2d007fc7d2252c5e107df9271fc7&"


class ProgressBars:
    FILLED = "<:gb:1389977976849825872>"
    UNFILLED = "<:wb:1389977972282494986>"


class UnifiedStatsView(BaseView):
    """統一的統計儀表板視圖"""

    # 頁面配置映射
    PAGE_CONFIG = {
        "profile": {"label": "玩家資訊", "emoji": "📊"},
        "draw_stats": {"label": "抽卡統計", "emoji": "🃏"},
        "collection_stats": {"label": "收藏統計", "emoji": "📚"},
        "minigame_stats": {"label": "遊戲統計", "emoji": "🎲"},
        "global_luck": {"label": "全服歐洲人", "emoji": "🌟"},
    }

    def __init__(self, cog_instance: "StatsCog", user: discord.User, **data):
        super().__init__(bot=cog_instance.bot, user_id=user.id, timeout=300)
        self.cog = cog_instance
        self.user = user
        self.data = data
        self.current_page = "profile"
        self._update_button_styles()

    def _update_button_styles(self) -> None:
        """更新按鈕樣式"""
        for item in self.children:
            if isinstance(item, Button) and item.custom_id:
                is_current = item.custom_id == self.current_page
                item.style = (
                    discord.ButtonStyle.primary
                    if is_current
                    else discord.ButtonStyle.secondary
                )
                item.disabled = is_current

    async def _update_page(
        self, interaction: discord.Interaction, page_name: str
    ) -> None:
        """更新頁面內容"""
        if self.current_page == page_name:
            await interaction.response.defer()
            return

        await interaction.response.defer()
        self.current_page = page_name
        self._update_button_styles()

        # 動態獲取embed構建器
        embed_builder = getattr(self.cog, f"_build_{page_name}_embed", None)
        if not embed_builder:
            logger.error("找不到embed生成器: _build_%s_embed", page_name)
            return

        # 根據頁面類型傳遞相應數據
        embed_args = self._get_embed_args(page_name)
        embed = embed_builder(self.user, *embed_args)

        await interaction.edit_original_response(embed=embed, view=self)

    def _get_embed_args(self, page_name: str) -> tuple:
        """根據頁面名稱獲取embed構建參數"""
        if page_name == "profile":
            return (
                self.data.get("profile"),
                self.data.get("draw_stats"),
                self.data.get("minigame_stats"),
                self.data.get("collection_stats"),
                self.data.get("financial_summary"),
                self.data.get("recent_spending"),
            )
        # 其他頁面保持不變
        return (self.data.get(page_name),)

    @discord.ui.button(
        label="玩家資訊",
        style=discord.ButtonStyle.primary,
        custom_id="profile",
        emoji="📊",
    )
    async def profile_button(self, interaction: discord.Interaction, _: Button):
        await self._update_page(interaction, "profile")

    @discord.ui.button(
        label="抽卡統計",
        style=discord.ButtonStyle.secondary,
        custom_id="draw_stats",
        emoji="🃏",
    )
    async def draw_stats_button(self, interaction: discord.Interaction, _: Button):
        await self._update_page(interaction, "draw_stats")

    @discord.ui.button(
        label="收藏統計",
        style=discord.ButtonStyle.secondary,
        custom_id="collection_stats",
        emoji="📚",
    )
    async def collection_stats_button(
        self, interaction: discord.Interaction, _: Button
    ):
        await self._update_page(interaction, "collection_stats")

    @discord.ui.button(
        label="遊戲統計",
        style=discord.ButtonStyle.secondary,
        custom_id="minigame_stats",
        emoji="🎲",
    )
    async def minigame_stats_button(self, interaction: discord.Interaction, _: Button):
        await self._update_page(interaction, "minigame_stats")

    @discord.ui.button(
        label="全服歐洲人",
        style=discord.ButtonStyle.secondary,
        custom_id="global_luck",
        emoji="🌟",
    )
    async def global_luck_button(self, interaction: discord.Interaction, _: Button):
        await self._update_page(interaction, "global_luck")


class StatsCog(commands.Cog):
    """統計儀表板 Cog"""

    # 各卡池期望頂級卡率配置
    POOL_EXPECTED_TOP_TIER_RATES = {
        "main": 0.0006,
        "hololive": 0.0006,
        "ua": 0.0006,
        "ptcg": 0.0006,
        "wixoss": 0.0006,
        "summer": 0.0005,
        "vd": 0.0005,
        "special": 0.0005,
        "special_maid": 0.0005,
        "ongeki": 0.0005,
        "sve": 0.0006,  # Shadowverse Evolve 卡池 (R6+R7 = 0.05% + 0.01% = 0.06%)
    }
    DEFAULT_EXPECTED_RATE = 0.0006

    def __init__(self, bot: commands.Bot):
        self.bot = bot

    async def cog_unload(self) -> None:
        """Cog卸載時的清理工作"""
        logger.info("StatsCog 已卸載")

    @app_commands.command(
        name="stats", description="查看你的油幣、抽卡、收藏、遊戲等綜合統計資料"
    )
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(pool_type="卡池類型（不選則顯示全部）")
    @app_commands.choices(
        pool_type=get_pool_type_choices(show_individual_pools_only=True)
    )
    async def stats_command(
        self, interaction: discord.Interaction, pool_type: Optional[str] = None
    ) -> None:
        """主命令，獲取所有數據並顯示儀表板"""
        await interaction.response.defer(thinking=True)

        user_id = interaction.user.id
        pool_type = None if pool_type == "all" else pool_type

        # 並行獲取所有數據
        data = await self._fetch_all_stats_data(user_id, pool_type)

        # 檢查用戶是否存在
        if not data.get("profile"):
            raise UserNotFoundError(user_id=user_id)

        # 檢查是否有活動記錄
        if self._has_no_activity(data):
            embed = discord.Embed(
                title="查無活動記錄",
                description="你還沒有任何抽卡、收藏或遊戲記錄，快去探索機器人的功能吧！",
                color=discord.Color.light_gray(),
            )
            await interaction.followup.send(embed=embed)
            return

        # 建立並發送儀表板
        # Use display_name and display_avatar which work for both User and Member
        user = interaction.user
        embed = self._build_profile_embed(
            user,
            data["profile"],
            data["draw_stats"],
            data["minigame_stats"],
            data["collection_stats"],
            # --- 傳入新數據 ---
            financial_summary=data.get("financial_summary"),
            recent_spending=data.get("recent_spending"),
        )
        # 確保 user 是 discord.User 類型
        user = interaction.user
        if isinstance(user, discord.Member):
            user = user._user  # 獲取底層的 User 對象
        view = UnifiedStatsView(self, user, **data)
        await interaction.followup.send(embed=embed, view=view)

    async def _fetch_all_stats_data(
        self, user_id: int, pool_type: Optional[str]
    ) -> Dict[str, Any]:
        """循序獲取所有統計數據，以避免 InterfaceError"""
        data = {}
        pool = get_pool()
        async with pool.acquire() as conn:
            # 依賴順序：profile -> financial -> spending -> draw -> collection -> minigame -> global_luck
            data["profile"] = await user_service.get_user(
                user_id, create_if_missing=True, connection=conn
            )
            data["financial_summary"] = await self._get_user_financial_summary(
                user_id, pool_type, connection=conn
            )
            data["recent_spending"] = await self._get_recent_spending(
                user_id, pool_type, connection=conn
            )
            data["draw_stats"] = await self._get_user_draw_stats(
                user_id, pool_type, connection=conn
            )
            data[
                "collection_stats"
            ] = await collection_service.get_detailed_collection_stats(
                user_id, pool_type, connection=conn
            )
            data[
                "minigame_stats"
            ] = await game_stats_service.get_user_game_stats_with_details(
                user_id, connection=conn
            )
            data["global_luck"] = await self.get_recent_good_cards(
                pool_type, connection=conn
            )
        return data

    def _has_no_activity(self, data: Dict[str, Any]) -> bool:
        """檢查用戶是否沒有任何活動記錄"""
        draw_stats = data.get("draw_stats")
        return (
            (not draw_stats or draw_stats.get("total_draws_from_history", 0) == 0)
            and not data.get("collection_stats")
            and not data.get("minigame_stats")
        )

    # --- Helper Methods ---

    # --- Embed 構建器 ---

    def _build_profile_embed(
        self,
        user: Union[discord.User, discord.Member],
        profile: Optional[GachaUser],
        draw_stats: Dict,
        minigame_stats: Dict,
        collection_stats: Dict,
        # --- 新增接收額外數據的參數 ---
        financial_summary: Optional[Dict] = None,
        recent_spending: Optional[List] = None,
    ) -> discord.Embed:
        """構建玩家資訊embed"""
        # 檢查是否有卡池篩選
        pool_filter = None
        pool_names = get_pool_type_names()

        if draw_stats and draw_stats.get("pool_type_filter"):
            pool_filter = draw_stats.get("pool_type_filter")
        elif collection_stats and collection_stats.get("pool_type_filter"):
            pool_filter = collection_stats.get("pool_type_filter")

        # 構建標題
        title = "玩家資訊"
        if pool_filter:
            pool_display_name = pool_names.get(pool_filter, pool_filter)
            title = f"玩家資訊 ({pool_display_name})"

        embed = discord.Embed(
            title=title, color=discord.Color.blue(), timestamp=datetime.now()
        )
        embed.set_author(
            name=f"{user.display_name} 的個人儀表板", icon_url=user.display_avatar.url
        )
        embed.set_thumbnail(url=Thumbnails.PROFILE)

        if not profile:
            embed.description = "無法加載玩家資訊"
            return embed

        # 帳號資訊
        join_date = (
            f"<t:{int(profile.created_at.timestamp())}:D>"
            if profile.created_at
            else "未知"
        )
        embed.add_field(
            name="帳號資訊",
            value=f"**玩家名稱：** {user.mention}\n**加入時間：** {join_date}",
            inline=False,
        )

        # --- 修改資產資訊，並新增財務概覽 ---
        card_value = (
            financial_summary.get("total_card_value", 0) if financial_summary else 0
        )
        stock_value = (
            financial_summary.get("total_stock_value", 0) if financial_summary else 0
        )

        from config.app_config import get_oil_emoji

        oil_emoji = get_oil_emoji()

        asset_lines = [
            f"**油幣餘額：** `{profile.oil_balance:,}` {oil_emoji}",
            f"**油票餘額：** `{profile.oil_ticket_balance:,}`",
            f"**卡片總價值：** `{card_value:,.0f}` {oil_emoji}",
            f"**股票總價值：** `{stock_value:,.0f}` {oil_emoji}",
        ]

        embed.add_field(
            name="💰 資產與價值", value="\n".join(asset_lines), inline=False
        )

        # 最近花費
        if recent_spending:
            spending_lines = [
                f"`{s['spend_date'].strftime('%Y-%m-%d')}`: **`{s['total_daily_spending']:,}`** {oil_emoji}"
                for s in recent_spending
            ]
            if spending_lines:
                embed.add_field(
                    name="💸 最近花費 (UTC+8)",
                    value="\n".join(spending_lines),
                    inline=False,
                )

        # 核心統計 (保持不變)
        total_draws = draw_stats.get("total_draws_from_history", 0) if draw_stats else 0
        total_games = (
            minigame_stats.get("overall", {}).get("total_games", 0)
            if minigame_stats
            else 0
        )
        collection_rate = (
            collection_stats.get("overall", {}).get("completion_rate", 0)
            if collection_stats
            else 0
        )

        embed.add_field(
            name="📊 核心統計",
            value=f"**總抽卡次數：** `{total_draws:,}`\n**總遊戲場次：** `{total_games:,}`\n**卡冊收集率：** `{collection_rate:.2f}`%",
            inline=False,
        )

        embed.set_footer(text="這是你的個人活動總覽 | 價值為估算值")
        return embed

    def _build_draw_stats_embed(
        self, user: Union[discord.User, discord.Member], stats_data: Dict[str, Any]
    ) -> discord.Embed:
        """構建抽卡統計embed"""
        if not stats_data or stats_data.get("total_draws_from_history", 0) == 0:
            return discord.Embed(
                title="抽卡統計",
                description="沒有抽卡記錄",
                color=discord.Color.light_gray(),
            )

        # 構建標題
        title = ""
        if pool_filter := stats_data.get("pool_type_filter"):
            pool_names = get_pool_type_names()
            pool_display_name = pool_names.get(pool_filter, pool_filter)
            title = f"({pool_display_name})"

        embed = discord.Embed(
            title=title, color=discord.Color.blue(), timestamp=datetime.now()
        )
        embed.set_author(
            name=f"{user.display_name} 的抽卡統計", icon_url=user.display_avatar.url
        )
        embed.set_thumbnail(url=Thumbnails.DRAW_STATS)

        # 添加各種統計信息
        self._add_basic_stats(embed, stats_data)
        self._add_rarity_stats(embed, stats_data)
        self._add_pool_stats(embed, stats_data)
        self._add_luck_stats(embed, stats_data)

        embed.set_footer(text="🎯 頂級卡判定：各卡池高稀有度卡片")
        return embed

    def _build_collection_stats_embed(
        self, user: Union[discord.User, discord.Member], stats_data: Dict[str, Any]
    ) -> discord.Embed:
        """構建收藏統計embed"""
        if not stats_data:
            embed = discord.Embed(
                title="收藏統計", color=discord.Color.green(), timestamp=datetime.now()
            )
            embed.description = "無法加載收藏統計數據"
            return embed

        # 構建標題
        title = ""
        if pool_filter := stats_data.get("pool_type_filter"):
            pool_names = get_pool_type_names()
            pool_display_name = pool_names.get(pool_filter, pool_filter)
            title = f"({pool_display_name})"

        embed = discord.Embed(
            title=title, color=discord.Color.green(), timestamp=datetime.now()
        )
        embed.set_author(
            name=f"{user.display_name} 的收藏統計", icon_url=user.display_avatar.url
        )
        embed.set_thumbnail(url=Thumbnails.COLLECTION)

        # 總體進度
        self._add_overall_collection_progress(embed, stats_data)

        # 稀有度進度
        self._add_rarity_collection_progress(embed, stats_data)

        # 卡池進度
        self._add_pool_collection_progress(embed, stats_data)

        return embed

    def _add_overall_collection_progress(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加總體收藏進度"""
        overall = stats_data.get("overall", {})
        collected = overall.get("total_collected", 0)
        total = overall.get("total_cards", 0)
        rate = overall.get("completion_rate", 0)

        embed.add_field(
            name="總體進度",
            value=f"**已收集獨特卡片：** `{collected:,}`\n**遊戲中卡片總數：** `{total:,}`\n**總體收集率：** `{rate:.2f}`%",
            inline=False,
        )

    def _add_rarity_collection_progress(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加稀有度收藏進度"""
        by_rarity = stats_data.get("by_rarity", {})
        if not by_rarity:
            return

        rarity_emojis = get_default_rarity_emojis()
        rarity_lines = [
            f"{rarity_emojis.get(rarity, '⚪')} `{data['collected']}/{data['total']}` ({data['percentage']:.1f}%)"
            for rarity, data in by_rarity.items()
        ]

        if rarity_lines:
            embed.add_field(
                name="稀有度進度", value="\n".join(rarity_lines), inline=False
            )

    def _add_pool_collection_progress(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加卡池收藏進度"""
        by_pool = stats_data.get("by_pool", {})
        if not by_pool:
            return

        pool_names = get_pool_type_names()
        sorted_pools = sorted(
            by_pool.items(),
            key=lambda item: pool_names.get(item[0]) or item[0],
        )

        all_lines = []
        for pool_key, data in sorted_pools:
            name = pool_names.get(pool_key, pool_key)
            percentage = data["percentage"]
            collected = data["collected"]
            total = data["total"]
            filled_count = round(percentage / 20)  # 5 bars
            bar = ProgressBars.FILLED * filled_count + ProgressBars.UNFILLED * (
                5 - filled_count
            )
            all_lines.append(
                f"**{name}** `{percentage:.1f}% ({collected}/{total})`\n{bar}"
            )

        if all_lines:
            # 如果指定了pool_type篩選且只有一個卡池，使用單欄顯示
            if stats_data.get("pool_type_filter") and len(all_lines) == 1:
                embed.add_field(name="卡池進度", value=all_lines[0], inline=False)
            else:
                # 分成兩欄顯示
                midpoint = (len(all_lines) + 1) // 2
                col1_content = "\n\n".join(all_lines[:midpoint])
                col2_content = "\n\n".join(all_lines[midpoint:])

                # 檢查內容長度限制
                if len(col1_content) > 1024:
                    col1_content = "內容過長無法顯示"
                embed.add_field(name="卡池進度", value=col1_content, inline=True)

                if col2_content:
                    if len(col2_content) > 1024:
                        col2_content = "內容過長無法顯示"
                    embed.add_field(name="\u200b", value=col2_content, inline=True)

    def _build_minigame_stats_embed(
        self, user: Union[discord.User, discord.Member], stats_data: Dict[str, Any]
    ) -> discord.Embed:
        """構建遊戲統計embed"""
        embed = discord.Embed(
            title=None, color=discord.Color.orange(), timestamp=datetime.now()
        )
        embed.set_author(
            name=f"{user.display_name} 的遊戲統計", icon_url=user.display_avatar.url
        )
        embed.set_thumbnail(url=Thumbnails.MINIGAME)

        overall_stats = stats_data.get("overall")
        if not overall_stats:
            embed.description = "沒有遊戲記錄"
            return embed

        # 添加總體統計
        self._add_overall_game_stats(embed, overall_stats)

        # 添加各遊戲詳情
        self._add_per_game_stats(embed, stats_data)

        embed.set_footer(text="排名數據每小時更新一次")
        return embed

    def _add_overall_game_stats(
        self, embed: discord.Embed, overall_stats: Dict[str, Any]
    ) -> None:
        """添加總體遊戲統計"""
        overall_ranks = overall_stats.get("rankings", {})

        def format_rank(rank_key: str) -> str:
            rank_val = overall_ranks.get(rank_key)
            if rank_val is None:
                return ""
            return f"(#{rank_val})"

        total_games_rank = format_rank("total_games_rank")
        total_profit_rank = format_rank("total_profit_loss_rank")
        luck_index_rank = format_rank("win_rate_rank")

        game_names = get_config("gacha_core_settings.game_names", {}) or {}
        favorite_game_key = overall_stats.get("favorite_game")
        fav_game_name = "N/A"
        if favorite_game_key:
            if game_names and hasattr(game_names, favorite_game_key):
                fav_game_name = getattr(game_names, favorite_game_key)
            else:
                fav_game_name = favorite_game_key

        overall_lines = [
            f"**總遊玩場次:** `{overall_stats.get('total_games', 0):,}` {total_games_rank}",
            f"**總淨盈虧:** `{overall_stats.get('total_profit_loss', 0):+,}` {total_profit_rank}",
            f"**賭運指數:** `{overall_stats.get('luck_index', 0):.2f}` ({overall_stats.get('luck_title', 'N/A')}) {luck_index_rank}",
            f"**玩家風格:** {overall_stats.get('style_title', 'N/A')}",
            f"**最愛遊戲:** {fav_game_name}",
        ]
        embed.add_field(
            name="🎲 總體統計", value="\n".join(overall_lines), inline=False
        )

    def _format_game_rank(self, ranks: Dict, rank_key: str) -> str:
        rank_val = ranks.get(rank_key)
        if rank_val is None:
            return "(-)"
        return f"(#{rank_val})"

    def _add_per_game_stats(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加各遊戲詳細統計"""
        per_game_stats = {k: v for k, v in stats_data.items() if k != "overall"}
        if not per_game_stats:
            return

        columns = 3
        game_names = get_config("gacha_core_settings.game_names", {})
        sorted_games = sorted(
            per_game_stats.items(),
            key=lambda item: item[1].get("total_games", 0),
            reverse=True,
        )

        for game_type, data in sorted_games:
            name = (
                getattr(game_names, game_type, game_type.title())
                if game_names
                else game_type.title()
            )
            emoji = get_minigame_emoji(game_type)
            ranks = data.get("rankings", {})

            total_games = data.get("total_games", 0)
            win_rate = (
                (data.get("total_wins", 0) / total_games * 100)
                if total_games > 0
                else 0
            )

            lines = [
                f"**{emoji} {name}**",
                f"場次: `{data.get('total_games', 0):,}` {self._format_game_rank(ranks, 'total_games_rank')}",
                f"勝率: `{win_rate:.1f}%` {self._format_game_rank(ranks, 'win_rate_rank')}",
                f"淨盈虧: `{data.get('total_profit_loss', 0):+,}` {self._format_game_rank(ranks, 'total_profit_loss_rank')}",
            ]

            embed.add_field(name="\u200b", value="\n".join(lines), inline=True)

        # 填充空白欄位以對齊
        num_games = len(sorted_games)
        if num_games % columns != 0:
            placeholders_to_add = columns - (num_games % columns)
            for _ in range(placeholders_to_add):
                embed.add_field(name="\u200b", value="\u200b", inline=True)

    def _build_global_luck_embed(
        self,
        user: Union[discord.User, discord.Member],
        good_cards: List[Dict[str, Any]],
    ) -> discord.Embed:
        """構建全服歐洲人embed"""
        embed = discord.Embed(
            title="全服歐洲人", color=discord.Color.gold(), timestamp=datetime.now()
        )
        embed.set_author(
            name=f"{user.display_name} 的儀表板", icon_url=user.display_avatar.url
        )
        embed.set_thumbnail(url=Thumbnails.GLOBAL_LUCK)

        if not good_cards:
            embed.add_field(
                name="\u200b", value="最近沒有高稀有度抽卡記錄", inline=False
            )
            return embed

        # 構建好卡記錄列表
        field_lines = self._build_good_card_lines(good_cards)

        # 分割內容到多個field（避免超過1024字符限制）
        self._add_good_card_fields(embed, field_lines)

        embed.set_footer(text="歐洲人紀錄會轉發到支援伺服器 | 連結在 /機器人資訊")
        return embed

    def _build_good_card_lines(self, good_cards: List[Dict[str, Any]]) -> List[str]:
        """構建好卡記錄行"""
        rarity_emojis = get_default_rarity_emojis()
        pool_names = get_pool_type_names()

        field_lines = []
        for record in good_cards:
            unix_timestamp = int(record["created_at"].timestamp())
            timestamp_str = f"<t:{unix_timestamp}:t>"
            nickname = record.get("nickname") or f"用戶{record['user_id']}"
            rarity_emoji = (
                rarity_emojis.get(record["rarity"], "⚪") if rarity_emojis else "⚪"
            )
            pool_name = pool_names.get(
                record.get("pool_type", ""), record.get("pool_type", "")
            )

            line = f"{timestamp_str} `{nickname}`\n<a:a_:1382444741798658228> 在【{pool_name}】抽到了 {rarity_emoji} **{record['card_name']}**"
            field_lines.append(line)

        return field_lines

    def _add_good_card_fields(
        self, embed: discord.Embed, field_lines: List[str]
    ) -> None:
        """添加好卡記錄到embed fields"""
        current_content = ""
        for line in field_lines:
            if len(current_content) + len(line) + 2 > 1024:
                embed.add_field(
                    name="\u200b", value=current_content.strip(), inline=False
                )
                current_content = ""
            current_content += line + "\n\n"

        if current_content:
            embed.add_field(name="\u200b", value=current_content.strip(), inline=False)

    # --- 數據獲取和處理方法 ---

    async def _get_user_financial_summary(
        self,
        user_id: int,
        pool_type: Optional[str] = None,
        connection: Optional[asyncpg.Connection] = None,
    ) -> Dict[str, Any]:
        """獲取用戶的財務摘要，包括卡片和股票價值"""
        if connection is None:
            raise ValueError("Database connection is required")
        conn = connection
        try:
            # 構建卡池篩選條件
            card_pool_condition = ""
            params: List[Union[int, str]] = [user_id]

            if pool_type:
                card_pool_condition = "AND mc.pool_type = $2"
                params.append(pool_type)

            # 使用單一查詢合併計算，提高效率
            query = f"""
            SELECT
                -- 計算卡片總價值: SUM(單卡市價 * 數量)，支持卡池篩選
                (
                    SELECT COALESCE(SUM(mc.current_market_sell_price * uc.quantity), 0)
                    FROM gacha_user_collections uc
                    JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
                    WHERE uc.user_id = $1 {card_pool_condition}
                ) AS total_card_value,
                -- 計算股票總價值: SUM(現價 * 數量)（股票不受卡池篩選影響）
                (
                    SELECT COALESCE(SUM(va.current_price * pp.quantity), 0)
                    FROM player_portfolios pp
                    JOIN virtual_assets va ON pp.asset_id = va.asset_id
                    WHERE pp.user_id = $1 AND pp.quantity > 0
                ) AS total_stock_value
            """
            result = await conn.fetchrow(query, *params)

            if result:
                return {
                    "total_card_value": result.get("total_card_value", 0),
                    "total_stock_value": result.get("total_stock_value", 0),
                }
            return {"total_card_value": 0, "total_stock_value": 0}

        except Exception as e:
            logger.error(
                f"[COG][stats] 獲取用戶 {user_id} 財務摘要失敗 (pool_type={pool_type}): {e}",
                exc_info=True,
            )
            raise DatabaseOperationError(f"獲取財務摘要失敗: {e}") from e

    async def _get_recent_spending(
        self,
        user_id: int,
        pool_type: Optional[str] = None,
        connection: Optional[asyncpg.Connection] = None,
    ) -> List[Dict[str, Any]]:
        """獲取用戶最近的每日花費（抽卡 + 買股票），支持卡池篩選"""
        if connection is None:
            raise ValueError("Database connection is required")
        conn = connection
        try:
            # 從配置中讀取抽卡成本
            pool_costs = get_config("gacha_core_settings.pool_costs", {})
            single_costs = pool_costs  # 直接使用 pool_costs

            # 設置默認值
            default_single = getattr(single_costs, "all", 50) if single_costs else 50

            # 為單抽安全地構建成本表達式，這是計算花費的唯一依據
            cost_expr = str(default_single)
            if single_costs and isinstance(single_costs, dict):
                single_cost_case = " ".join(
                    [
                        f"WHEN user_selected_pool = '{p}' THEN {c}"
                        for p, c in single_costs.items()
                    ]
                )
                if single_cost_case:
                    cost_expr = f"CASE {single_cost_case} ELSE {default_single} END"

            # 構建卡池篩選條件
            pool_condition = ""
            params: List[Union[int, str]] = [user_id]

            if pool_type:
                pool_condition = "AND pool_type = $2"
                params.append(pool_type)

            query = f"""
            WITH daily_spending AS (
                -- 抽卡花費 (從 gacha_draw_history)，每條記錄都按單抽成本計算，支持卡池篩選
                SELECT
                    DATE(created_at AT TIME ZONE 'Asia/Taipei') AS spend_date,
                    SUM({cost_expr}) AS amount
                FROM gacha_draw_history
                WHERE user_id = $1 {pool_condition}
                GROUP BY spend_date

                UNION ALL

                -- 買入股票花費 (從 market_transactions)（股票不受卡池篩選影響）
                SELECT
                    DATE(timestamp AT TIME ZONE 'Asia/Taipei') AS spend_date,
                    SUM(total_amount) AS amount
                FROM market_transactions
                WHERE user_id = $1 AND transaction_type = 'BUY'
                GROUP BY spend_date
            )
            -- 合併並排序
            SELECT
                spend_date,
                SUM(amount)::bigint AS total_daily_spending
            FROM daily_spending
            GROUP BY spend_date
            ORDER BY spend_date DESC
            LIMIT 5;
            """
            records = await conn.fetch(query, *params)
            return [dict(record) for record in records]

        except Exception as e:
            logger.error(
                f"[COG][stats] 獲取用戶 {user_id} 最近花費失敗 (pool_type={pool_type}): {e}",
                exc_info=True,
            )
            raise DatabaseOperationError(f"獲取最近花費失敗: {e}") from e

    async def _get_user_draw_stats(
        self,
        user_id: int,
        pool_type: Optional[str] = None,
        connection: Optional[asyncpg.Connection] = None,
    ) -> Dict[str, Any]:
        """獲取用戶的抽卡統計數據"""
        if connection is None:
            raise ValueError("Database connection is required")
        conn = connection
        try:
            # 檢查用戶是否存在
            if not await self._user_exists(conn, user_id):
                raise UserNotFoundError(user_id=user_id)

            # 獲取主要統計數據
            stats_row = await self._fetch_main_draw_stats(conn, user_id, pool_type)
            if not stats_row or stats_row["total_draws"] == 0:
                return {"total_draws_from_history": 0}

            # 處理統計數據
            processed_data = self._process_draw_stats(stats_row)

            # 獲取運氣統計
            luck_data = await self._fetch_luck_stats(conn, user_id, pool_type)

            # 計算衍生統計
            derived_stats = self._calculate_derived_stats(
                stats_row, luck_data, processed_data["pool_breakdown"]
            )

            # 獲取服務器比較數據
            server_comparison = await self._get_server_comparison(
                conn,
                user_id,
                pool_type,
                stats_row["total_draws"],
                stats_row["top_tier_count"] or 0,
            )

            return {
                "user_id": user_id,
                "pool_type_filter": pool_type,
                "total_draws_from_history": stats_row["total_draws"],
                "unique_cards_drawn": stats_row["unique_cards_drawn"],
                "new_cards_count": stats_row["new_cards_count"],
                "wish_cards_count": stats_row["wish_cards_count"],
                "first_draw_date": stats_row["first_draw_date"],
                "last_draw_date": stats_row["last_draw_date"],
                **processed_data,
                "luck_stats": {
                    **derived_stats,
                    "most_drawn_cards": processed_data.get("most_drawn_cards", []),
                    "server_comparison": server_comparison,
                    "high_rarity_stats": {
                        "top_tier_count": stats_row["top_tier_count"] or 0,
                        "ssr_plus_count": stats_row["ssr_plus_count"] or 0,
                    },
                },
            }

        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(
                "獲取用戶抽卡統計失敗: user_id=%s, error=%s", user_id, e, exc_info=True
            )
            raise DatabaseOperationError(f"獲取抽卡統計失敗: {str(e)}") from e

    async def _user_exists(self, conn: asyncpg.Connection, user_id: int) -> bool:
        """檢查用戶是否存在"""
        result = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM gacha_users WHERE user_id = $1)", user_id
        )
        return bool(result) if result is not None else False

    async def _fetch_main_draw_stats(
        self, conn: asyncpg.Connection, user_id: int, pool_type: Optional[str]
    ) -> Optional[asyncpg.Record]:
        """獲取主要抽卡統計數據 (已優化)"""
        # --- 正確的單次掃描優化版本 ---
        query_optimized = """
        WITH user_draws AS (
            -- CTE定義，準備好所有需要的基礎數據
            SELECT
                dh.card_id, mc.rarity, mc.name as card_name, dh.pool_type,
                dh.is_new, dh.is_wish, dh.created_at,
                CASE
                    WHEN dh.pool_type IN ('main', 'hololive', 'ua', 'ptcg', 'wixoss', 'summer', 'vd') AND mc.rarity >= 6 THEN true
                    WHEN dh.pool_type IN ('special', 'special_maid', 'ongeki') AND mc.rarity >= 5 THEN true
                    WHEN dh.pool_type = 'sve' AND mc.rarity >= 6 THEN true
                    ELSE false
                END AS is_top_tier
            FROM gacha_draw_history dh
            JOIN gacha_master_cards mc ON dh.card_id = mc.card_id
            WHERE dh.user_id = $1 AND ($2::text IS NULL OR dh.pool_type = $2)
        ),
        aggregated_stats AS (
            -- CTE 1: 計算所有基礎聚合統計，只掃描一次 user_draws
            SELECT
                COUNT(*) AS total_draws,
                COUNT(DISTINCT card_id) AS unique_cards_drawn,
                COUNT(*) FILTER (WHERE is_new) AS new_cards_count,
                COUNT(*) FILTER (WHERE is_wish) AS wish_cards_count,
                MIN(created_at) AS first_draw_date,
                MAX(created_at) AS last_draw_date,
                COUNT(*) FILTER (WHERE is_top_tier) AS top_tier_count,
                COUNT(*) FILTER (WHERE rarity >= 4) AS ssr_plus_count
            FROM user_draws
        ),
        rarity_counts_cte AS (
            -- CTE 2: 計算稀有度分布
            SELECT jsonb_object_agg(rarity::text, cnt) as rarity_counts
            FROM (SELECT rarity, COUNT(*) as cnt FROM user_draws GROUP BY rarity) t
        ),
        pool_counts_cte AS (
            -- CTE 3: 計算卡池分布
            SELECT jsonb_object_agg(pool_type, cnt) as pool_counts
            FROM (SELECT pool_type, COUNT(*) as cnt FROM user_draws GROUP BY pool_type) t
        ),
        most_drawn_cards_cte AS (
            -- CTE 4: 計算最常抽到的卡片
            SELECT jsonb_agg(cards ORDER BY cards.draw_count DESC, cards.rarity DESC) as most_drawn_cards
            FROM (
                SELECT card_name, rarity, COUNT(*) as draw_count
                FROM user_draws GROUP BY card_id, card_name, rarity
                ORDER BY draw_count DESC, rarity DESC LIMIT 5
            ) cards
        )
        -- 最終查詢: 將所有預先計算好的 CTE 結果合併成一個記錄
        SELECT *
        FROM aggregated_stats, rarity_counts_cte, pool_counts_cte, most_drawn_cards_cte;
        """
        return await conn.fetchrow(query_optimized, user_id, pool_type)

    def _process_draw_stats(self, stats_row: asyncpg.Record) -> Dict[str, Any]:
        """處理抽卡統計數據"""
        # 處理稀有度分布
        rarity_breakdown = []
        if stats_row["rarity_counts"]:
            rarity_breakdown = [
                {"rarity": int(k), "draw_count": v}
                for k, v in stats_row["rarity_counts"].items()
            ]
            rarity_breakdown.sort(key=lambda x: x["rarity"], reverse=True)

        # 處理卡池分布
        pool_breakdown = []
        if stats_row["pool_counts"]:
            pool_breakdown = [
                {"pool_type": k, "draw_count": v}
                for k, v in stats_row["pool_counts"].items()
            ]
            pool_breakdown.sort(key=lambda x: x["draw_count"], reverse=True)

        # 處理最常抽到的卡片
        most_drawn_cards = stats_row["most_drawn_cards"] or []

        return {
            "rarity_breakdown": rarity_breakdown,
            "pool_breakdown": pool_breakdown,
            "most_drawn_cards": most_drawn_cards,
        }

    async def _fetch_luck_stats(
        self, conn: asyncpg.Connection, user_id: int, pool_type: Optional[str]
    ) -> Optional[asyncpg.Record]:
        """獲取運氣統計數據"""
        query = """
        WITH user_draws_sequence AS (
            SELECT
                dh.draw_session_id, dh.created_at,
                CASE
                    WHEN (dh.pool_type IN ('main', 'hololive', 'ua', 'ptcg', 'wixoss', 'summer', 'vd') AND mc.rarity >= 6) THEN 1
                    WHEN (dh.pool_type IN ('special', 'special_maid', 'ongeki') AND mc.rarity >= 5) THEN 1
                    WHEN (dh.pool_type = 'sve' AND mc.rarity >= 6) THEN 1
                    ELSE 0
                END AS is_top_tier,
                ROW_NUMBER() OVER (ORDER BY dh.created_at) as draw_order
            FROM gacha_draw_history dh
            JOIN gacha_master_cards mc ON dh.card_id = mc.card_id
            WHERE dh.user_id = $1 AND ($2::text IS NULL OR dh.pool_type = $2)
        ),
        top_tier_positions AS (
            SELECT draw_order FROM user_draws_sequence WHERE is_top_tier = 1 ORDER BY draw_order
        ),
        drought_periods AS (
            SELECT
                CASE
                    WHEN LAG(draw_order) OVER (ORDER BY draw_order) IS NULL
                    THEN draw_order - 1
                    ELSE draw_order - LAG(draw_order) OVER (ORDER BY draw_order) - 1
                END as drought_length
            FROM top_tier_positions
            UNION ALL
            SELECT
                (SELECT MAX(draw_order) FROM user_draws_sequence) -
                (SELECT MAX(draw_order) FROM top_tier_positions) as drought_length
            WHERE (SELECT MAX(draw_order) FROM user_draws_sequence) >
                  (SELECT MAX(draw_order) FROM top_tier_positions)
        )
        SELECT
            COALESCE((SELECT MAX(drought_length) FROM drought_periods WHERE drought_length > 0), 0) AS longest_drought,
            (SELECT COALESCE(MAX(combo_count), 0) FROM (
                SELECT COUNT(*) as combo_count FROM user_draws_sequence
                WHERE is_top_tier = 1 GROUP BY draw_session_id
            ) t) AS max_combo
        """
        return await conn.fetchrow(query, user_id, pool_type)

    def _calculate_derived_stats(
        self,
        stats_row: asyncpg.Record,
        luck_data: Optional[asyncpg.Record],
        pool_breakdown: List[Dict],
    ) -> Dict[str, Any]:
        """計算衍生統計數據"""
        total_draws = stats_row["total_draws"]
        top_tier_count = stats_row["top_tier_count"] or 0

        # 計算平均出頂級卡抽數
        avg_draws_per_top_tier = (
            (total_draws / top_tier_count) if top_tier_count > 0 else 0
        )

        # 計算抽卡頻率
        time_diff = (
            stats_row["last_draw_date"] - stats_row["first_draw_date"]
            if stats_row["first_draw_date"] and stats_row["last_draw_date"]
            else None
        )
        days = max(1, time_diff.days + 1) if time_diff else 1
        draw_frequency = total_draws / days

        # 計算運氣指數
        luck_index = self._calculate_luck_index(
            top_tier_count, pool_breakdown, total_draws
        )

        return {
            "avg_draws_per_top_tier": avg_draws_per_top_tier,
            "longest_drought": luck_data["longest_drought"] if luck_data else 0,
            "draw_frequency": draw_frequency,
            "luck_index": luck_index,
            "max_combo": luck_data["max_combo"] if luck_data else 0,
        }

    def _calculate_luck_index(
        self, top_tier_count: int, pool_breakdown: List[Dict], total_draws: int
    ) -> float:
        """計算運氣指數"""
        if total_draws == 0:
            return 1.0

        actual_rate = top_tier_count / total_draws
        expected_rate = self._calculate_expected_rate(pool_breakdown, total_draws)

        return actual_rate / expected_rate if expected_rate > 0 else 1.0

    def _calculate_expected_rate(
        self, pool_breakdown: List[Dict], total_draws: int
    ) -> float:
        """計算期望頂級卡率"""
        if not pool_breakdown:
            return self.DEFAULT_EXPECTED_RATE

        total_expected_rate = sum(
            self.POOL_EXPECTED_TOP_TIER_RATES.get(
                p["pool_type"], self.DEFAULT_EXPECTED_RATE
            )
            * (p["draw_count"] / total_draws)
            for p in pool_breakdown
        )
        return total_expected_rate

    async def _get_server_comparison(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        pool_type: Optional[str],
        user_total_draws: int,
        user_top_tier_count: int,
    ) -> Dict[str, Any]:
        """獲取與全服平均的比較數據"""
        try:
            if user_total_draws == 0:
                return {}

            pool_filter = pool_type or "ALL"

            # 改為循序執行以避免 InterfaceError
            server_stats = await conn.fetchrow(
                "SELECT * FROM gacha_server_stats_mv WHERE pool_type = $1", pool_filter
            )
            user_rank = await conn.fetchval(
                "SELECT draw_rank FROM gacha_user_rankings_mv WHERE user_id = $1 AND pool_type = $2",
                user_id,
                pool_filter,
            )

            if not server_stats:
                return {}

            # 計算比率
            server_top_tier_rate = self._calculate_rate(
                server_stats["top_tier_count"], server_stats["total_draws"]
            )
            user_top_tier_rate = self._calculate_rate(
                user_top_tier_count, user_total_draws
            )

            # 計算比較百分比
            comparison_rate = 0
            if server_top_tier_rate > 0:
                comparison_rate = (
                    (user_top_tier_rate - server_top_tier_rate)
                    / server_top_tier_rate
                    * 100
                )

            return {
                "server_top_tier_rate": server_top_tier_rate,
                "user_top_tier_rate": user_top_tier_rate,
                "top_tier_rate_comparison": comparison_rate,
                "draw_count_rank": user_rank or "N/A",
                "total_users": server_stats["total_users"],
            }
        except Exception as e:
            logger.error("獲取全服比較數據失敗: %s", e, exc_info=True)
            return {}

    def _calculate_rate(self, count: int, total: int) -> float:
        """計算百分比率"""
        return (count / total * 100) if total > 0 else 0

    def _add_basic_stats(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加基礎統計信息"""
        total = stats_data.get("total_draws_from_history", 0)
        unique = stats_data.get("unique_cards_drawn", 0)
        new = stats_data.get("new_cards_count", 0)
        wish = stats_data.get("wish_cards_count", 0)

        new_rate = (new / total * 100) if total > 0 else 0
        wish_rate = (wish / total * 100) if total > 0 else 0

        embed.add_field(
            name="📊 基礎統計",
            value=f"**抽卡次數：** `{total:,}`\n**不同卡片：** `{unique:,}`\n**新卡次數：** `{new:,}` (`{new_rate:.1f}%`)\n**許願命中：** `{wish:,}` (`{wish_rate:.1f}%)`",
            inline=False,
        )

    def _add_rarity_stats(
        self, embed: discord.Embed, stats_data: Dict[str, Any]
    ) -> None:
        """添加稀有度分布統計"""
        rarity_stats = stats_data.get("rarity_breakdown", [])
        if not rarity_stats:
            return

        rarity_emojis = get_default_rarity_emojis()
        total = stats_data.get("total_draws_from_history", 0)

        lines = [
            f"{rarity_emojis.get(r['rarity'], '⚪')} `{r['draw_count']:,}` 次 (`{(r['draw_count'] / total * 100):.2f}`%)"
            for r in rarity_stats
        ]
        embed.add_field(name="⭐ 稀有度分布", value="\n".join(lines), inline=False)

    def _add_pool_stats(self, embed: discord.Embed, stats_data: Dict[str, Any]) -> None:
        """添加卡池分布統計"""
        pool_stats = stats_data.get("pool_breakdown", [])
        if not pool_stats or len(pool_stats) <= 1:
            return

        pool_names = get_pool_type_names()
        total = stats_data.get("total_draws_from_history", 0)

        lines = [
            f"🎯 **{pool_names.get(p['pool_type'], p['pool_type'])}：** `{p['draw_count']:,}` 次 (`{(p['draw_count'] / total * 100):.1f}`%)"
            for p in pool_stats[:10]
        ]
        embed.add_field(name="🎪 卡池分布", value="\n".join(lines), inline=False)

    def _add_luck_stats(self, embed: discord.Embed, stats_data: Dict[str, Any]) -> None:
        """添加運氣分析統計"""
        luck_stats = stats_data.get("luck_stats", {})
        if not luck_stats:
            return

        # 運氣分析
        self._add_luck_analysis(embed, luck_stats)

        # 全服比較
        self._add_server_comparison_stats(embed, luck_stats)

        # 最常抽到的卡片
        self._add_most_drawn_cards(embed, luck_stats)

    def _add_luck_analysis(
        self, embed: discord.Embed, luck_stats: Dict[str, Any]
    ) -> None:
        """添加運氣分析"""
        luck_index = luck_stats.get("luck_index", 0.0)
        luck_rating = self._get_luck_rating(luck_index)

        luck_info = [
            f"**平均出頂級卡：** `{luck_stats.get('avg_draws_per_top_tier', 0):.1f}` 抽",
            f"**最長乾旱：** `{luck_stats.get('longest_drought', 0):,}` 抽",
            f"**運氣指數：** `{luck_index:.2f}` {luck_rating}",
        ]
        embed.add_field(name="🍀 運氣分析", value="\n".join(luck_info), inline=False)

    def _get_luck_rating(self, luck_index: float) -> str:
        """根據運氣指數獲取評級"""
        if luck_index == 0:
            return "🚫 從未出貨"
        elif luck_index >= 2.0:
            return "🌟 超級歐皇"
        elif luck_index >= 1.5:
            return "✨ 歐皇"
        elif luck_index >= 1.2:
            return "😊 小歐"
        elif luck_index >= 0.8:
            return "😐 平民"
        elif luck_index >= 0.6:
            return "😕 小非"
        elif luck_index >= 0.4:
            return "😢 非酋"
        else:
            return "💀 超級非酋"

    def _add_server_comparison_stats(
        self, embed: discord.Embed, luck_stats: Dict[str, Any]
    ) -> None:
        """添加全服比較統計"""
        server_comp = luck_stats.get("server_comparison", {})
        if not server_comp:
            return

        comp_rate = server_comp.get("top_tier_rate_comparison", 0)
        comp_text = (
            f"比全服平均高 `{comp_rate:.1f}`% 🔥"
            if comp_rate > 0
            else f"比全服平均低 `{abs(comp_rate):.1f}`% 😭"
        )

        comp_info = [
            f"**頂級卡出率：** {comp_text}",
            f"**你的頂級卡率：** `{server_comp.get('user_top_tier_rate', 0):.3f}`%",
            f"**抽卡次數排名：** 全服第 `{server_comp.get('draw_count_rank', 'N/A')}` 名",
        ]
        embed.add_field(
            name="📊 全服比較 (每小時更新)", value="\n".join(comp_info), inline=False
        )

    def _add_most_drawn_cards(
        self, embed: discord.Embed, luck_stats: Dict[str, Any]
    ) -> None:
        """添加最常抽到的卡片"""
        most_drawn = luck_stats.get("most_drawn_cards", [])
        if not most_drawn:
            return

        rarity_emojis = get_default_rarity_emojis()
        card_info = [
            f"{rarity_emojis.get(c['rarity'], '⚪')} **{c['card_name']}** - `{c['draw_count']:,}` 次"
            for c in most_drawn[:3]
        ]
        embed.add_field(name="🎯 最常抽到", value="\n".join(card_info), inline=False)

    async def get_recent_good_cards(
        self,
        pool_type: Optional[str] = None,
        limit: int = 20,
        connection: Optional[asyncpg.Connection] = None,
    ) -> List[Dict[str, Any]]:
        """獲取最近的好卡記錄（全服）"""
        if connection is None:
            raise ValueError("Database connection is required")
        conn = connection
        try:
            notification_config = get_config(
                "gacha_notification_settings.notify_rarities", {}
            )
            if not notification_config:
                return []

            # 構建查詢條件
            params: List[Union[str, int]] = []
            rarity_conditions_parts = []

            if isinstance(notification_config, dict):
                for pool_name, rarities in notification_config.items():
                    if not rarities or (pool_type and pool_name != pool_type):
                        continue

                    params.append(pool_name)
                    pool_param_idx = len(params)
                    rarity_placeholders = [
                        f"${len(params) + i + 1}" for i, _ in enumerate(rarities)
                    ]
                    params.extend(rarities)
                    rarity_conditions_parts.append(
                        f"(dh.pool_type = ${pool_param_idx} AND mc.rarity IN ({', '.join(rarity_placeholders)}))"
                    )

            if not rarity_conditions_parts:
                return []

            # 構建完整查詢
            where_clause = f"WHERE {' OR '.join(rarity_conditions_parts)}"
            limit_param_index = len(params) + 1
            params.append(limit)

            query = f"""
                SELECT dh.created_at, dh.user_id, mc.name as card_name, mc.rarity, dh.pool_type, gu.nickname
                FROM gacha_draw_history dh
                JOIN gacha_master_cards mc USING (card_id)
                LEFT JOIN gacha_users gu USING (user_id)
                {where_clause}
                ORDER BY dh.created_at DESC
                LIMIT ${limit_param_index}
            """

            records = await conn.fetch(query, *params)
            return [dict(record) for record in records]

        except Exception as e:
            logger.error("獲取好卡記錄失敗: %s", e, exc_info=True)
            raise DatabaseOperationError(f"獲取好卡記錄失敗: {e}") from e


async def setup(bot: commands.Bot) -> None:
    """將StatsCog添加到機器人中"""
    await bot.add_cog(StatsCog(bot))
    logger.info("StatsCog 已成功註冊到機器人")
