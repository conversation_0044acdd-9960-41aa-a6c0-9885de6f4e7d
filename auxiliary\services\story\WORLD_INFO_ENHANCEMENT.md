# Story系統 World Info 強化功能說明

## 概述

本次更新為Story系統的World Info功能增加了四個重要特性：

1. **直接注入到Prompt模板**：可以將World Info內容直接注入到beilu_library的prompt模板中
2. **自定義歷史紀錄Role**：可以指定World Info在歷史紀錄中注入時使用的角色（user/system/assistant）
3. **順序參數**：支持通過order參數控制同一位置多個world info的顯示順序
4. **關鍵字動態觸發**：可以根據歷史記錄中的關鍵字動態決定是否觸發world info

## 功能1：直接注入到Prompt模板

### 使用方法

在theme配置的`custom_world_info_rules`中添加以下配置：

```python
{
    "id": "world_info_prompt",
    "name": "Prompt World Info",
    "content": "你的World Info內容",
    "insertion_target": "prompt_library",     # 指定注入到prompt模板
    "insertion_id": "world_info_content",     # 對應模板中的變量名
    "trigger_probability": 1.0,
    "enabled": True,
}
```

### 工作原理

- 當`insertion_target`設為`"prompt_library"`且`insertion_id`設為`"world_info_content"`時
- 內容會被注入到beilu_library.py中的`persona_description`模板項目
- 該模板項目的內容為`{world_info_content}`，會被替換為你的World Info內容
- 最終以system角色出現在prompt中

### 模板位置

修改了beilu_library.py中現有的模板項目：

```python
{
    "id": "persona_description",
    "name": "World Info",
    "role": "system",
    "content": "{world_info_content}",  # 原本是空字符串，現在支持world_info_content變量
    "enabled": True,
}
```

## 功能2：自定義歷史紀錄Role

### 使用方法

在theme配置的`custom_world_info_rules`中添加`role`字段：

```python
{
    "id": "world_info_history",
    "name": "History World Info",
    "content": "你的World Info內容",
    "insertion_target": "history",           # 注入到歷史紀錄
    "insertion_depth": 0,                    # 注入深度
    "role": "user",                          # 指定角色：user/system/assistant
    "trigger_probability": 1.0,
    "enabled": True,
}
```

### 支持的角色

- **`"system"`**：系統級提示，適合規則和設定
- **`"user"`**：用戶角色，適合模擬用戶的問題或想法
- **`"assistant"`**：助手角色，適合背景描述或系統生成的內容

### 向後兼容性

- 如果不指定`role`字段，默認使用`"system"`角色
- 現有的World Info配置無需修改即可正常工作

## 功能3：順序參數

### 使用方法

在world info配置中添加`order`參數：

```python
{
    "id": "world_info_1",
    "content": "第一條信息",
    "order": 1,  # 數字越小越在前面
    # 其他配置...
},
{
    "id": "world_info_2", 
    "content": "第二條信息",
    "order": 5,  # 數字越大越在後面
    # 其他配置...
}
```

### 工作原理

- 同一個`insertion_target`和`insertion_id`（或同一個`insertion_depth`）的多個world info會按`order`值排序
- `order`值越小的越靠前，越大的越靠後
- 如果不指定`order`，默認為0
- 支持負數順序值

### 注入順序詳解

以歷史記錄注入為例，假設有以下配置：
- 深度1，ORDER88 的world info A
- 深度1，ORDER99 的world info B  
- 深度0，ORDER1 的world info C
- 深度0，ORDER5 的world info D

最終的消息順序將是：
1. world info A (深度1，ORDER88)
2. world info B (深度1，ORDER99)  
3. [最新用戶輸入消息]
4. world info C (深度0，ORDER1)
5. world info D (深度0，ORDER5)

即：**深度越大越在前面，同深度內ORDER越小越在前面**

## 功能4：關鍵字動態觸發

### 使用方法

在world info配置中添加`trigger_keywords`數組：

```python
{
    "id": "combat_info",
    "content": "戰鬥相關的world info",
    "trigger_keywords": ["戰鬥", "攻擊", "技能", "魔法"],  # 關鍵字列表
    "trigger_probability": 1.0,  # 找到關鍵字時的觸發機率
    # 其他配置...
}
```

### 工作原理

- 系統會搜索整個短期歷史記錄（包括content和status_block）
- 如果找到任何一個指定的關鍵字，則有機會觸發該world info
- 關鍵字匹配不區分大小寫
- 如果不指定`trigger_keywords`，則按原有邏輯觸發
- 仍然受`trigger_probability`機率控制

### 觸發條件組合

World Info的最終觸發需要同時滿足：
1. `enabled` = true
2. `trigger_probability`機率檢查通過
3. `trigger_keywords`關鍵字檢查通過（如果指定了的話）

### 連鎖觸發功能

**系統支持world info的連鎖觸發**，已觸發的world info內容可以觸發其他world info：

示例連鎖觸發：
- World Info A：關鍵字="魔法"，內容="開始戰鬥訓練" 
- World Info B：關鍵字="戰鬥"，內容="學習防禦技能"
- World Info C：關鍵字="防禦"，內容="提升體力值"

如果歷史記錄中出現"魔法"：
1. 觸發A（因為歷史記錄包含"魔法"）
2. 觸發B（因為A的內容包含"戰鬥"）  
3. 觸發C（因為B的內容包含"防禦"）

### 連鎖觸發控制

- **防重複觸發**：同一個world info在一次處理中只會觸發一次
- **防無限循環**：默認最多進行3輪連鎖檢查，可通過`world_info_chain_depth`設定調整
- **順序保證**：先觸發的world info會影響後觸發的，保持邏輯一致性

### 效能優化設定

可在主題配置中添加以下參數來控制效能：

```python
THEME_CONFIG = {
    # 其他配置...
    "world_info_chain_depth": 2,  # 連鎖觸發最大深度（默認3）
    # 建議值：
    # - 1: 無連鎖觸發，最佳效能
    # - 2-3: 適中的連鎖效果，良好效能
    # - 4-5: 複雜連鎖效果，較高運算成本
}
```

### 效能考慮

- **關鍵字數量**：每個world info建議不超過5個關鍵字
- **歷史記錄長度**：系統只檢查短期歷史記錄，長度通常在20-50條之間
- **連鎖深度**：建議設為2-3輪，既能實現豐富效果又保持良好效能
- **優化機制**：系統已實現早期返回和避免重複檢查等優化

## 完整示例

參考`auxiliary/services/story/themes/world_info_example.py`文件，其中包含了所有功能的完整示例。

## 使用建議

1. **Prompt模板注入**：適合重要的世界觀設定，會始終出現在prompt中
2. **歷史紀錄注入**：適合動態的背景信息，可以根據機率觸發
3. **Role選擇**：
   - 使用`system`角色提供規則和約束
   - 使用`user`角色模擬用戶的背景知識或疑問
   - 使用`assistant`角色提供環境描述或系統生成的背景
4. **順序控制**：
   - 重要信息使用較小的order值（如1、2、3）
   - 補充信息使用較大的order值（如10、20、30）
   - 可以預留空間便於後續插入新內容
5. **關鍵字觸發**：
   - 適合情境相關的world info（如戰鬥、對話、探索等）
   - 關鍵字應該包含同義詞和變體（如"戰鬥"和"戰斗"）
   - 避免過於常見的詞彙以免頻繁觸發

## 技術實現

- beilu_library.py：修改現有`persona_description`模板，將content從空字符串改為`{world_info_content}`變量
- prompts.py：已支持`role`字段的讀取和應用
- 完全向後兼容，不影響現有功能
