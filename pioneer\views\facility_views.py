"""
Pioneer System 設施管理 Discord 視圖
包含設施列表、詳情、升級等視圖
"""

from typing import TYPE_CHECKING, List

import discord

from pioneer import repositories
from pioneer.exceptions import PioneerError
from pioneer.models.pioneer_models import Facility
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.response_embeds import SuccessEmbed

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader
    from utils.base_view import BotType


class FacilityManagementView(BaseView):
    """設施管理主視圖"""

    def __init__(
        self,
        user_id: int,
        facilities: List[Facility],
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facilities = facilities
        self.current_page = 0
        self.facilities_per_page = 5

    async def create_facility_management_embed(self) -> discord.Embed:
        """創建設施管理 Embed"""
        embed = discord.Embed(
            title="🏭 設施管理",
            description="管理您的所有設施。使用 `/build` 來建造新設施。",  # 在描述中添加引導
            color=0x2B2D31,
        )

        if not self.facilities:
            embed.add_field(
                name="🈳️ 無設施",
                # 這裡的引導文本至關重要
                value="您還沒有建造任何設施。\n**請使用 `/build` 指令來建造您的第一個設施！**",
                inline=False,
            )
        else:
            # 分頁顯示設施
            start_idx = self.current_page * self.facilities_per_page
            end_idx = start_idx + self.facilities_per_page
            page_facilities = self.facilities[start_idx:end_idx]

            facility_text = []
            for i, facility in enumerate(page_facilities):
                facility_config = self.game_data.get_facility_config(
                    facility.facility_type
                )
                facility_name = facility.facility_name or (
                    facility_config.name if facility_config else facility.facility_type
                )

                status = "🟢 運行中" if facility.is_active else "🔴 已停止"
                # upgrades_count = len(facility.upgrades)  # Currently unused

                type_name = (
                    facility_config.name if facility_config else facility.facility_type
                )
                facility_text.append(
                    f"{start_idx + i + 1}. **{facility_name}**\n"
                    f"   類型: {type_name} | 等級: {facility.level} | {status}"
                )

            embed.add_field(
                name=f"設施列表 (第 {self.current_page + 1} 頁)",
                value="\n".join(facility_text),
                inline=False,
            )

            # 統計信息
            total_facilities = len(self.facilities)
            total_pages = (
                total_facilities + self.facilities_per_page - 1
            ) // self.facilities_per_page
            active_facilities = sum(1 for f in self.facilities if f.is_active)

            embed.add_field(
                name="📊 統計",
                value=f"總設施數: {total_facilities}\n"
                f"運行中: {active_facilities}\n"
                f"頁數: {self.current_page + 1}/{total_pages}",
                inline=True,
            )

        return embed

    @discord.ui.button(label=None, style=discord.ButtonStyle.secondary, emoji="⬅️")
    async def prev_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """上一頁按鈕"""
        if self.current_page > 0:
            self.current_page -= 1
            embed = await self.create_facility_management_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label=None, style=discord.ButtonStyle.secondary, emoji="➡️")
    async def next_page_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """下一頁按鈕"""
        total_pages = (
            len(self.facilities) + self.facilities_per_page - 1
        ) // self.facilities_per_page
        if self.current_page < total_pages - 1:
            self.current_page += 1
            embed = await self.create_facility_management_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="查看詳情", style=discord.ButtonStyle.primary, emoji="🔍")
    async def detail_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """查看詳情按鈕"""
        if not self.facilities:
            raise PioneerError("沒有設施可查看")

        # 創建設施選擇下拉選單
        view = FacilitySelectionView(
            self.user_id, self.facilities, self.game_data, self.bot
        )
        embed = await view.create_selection_embed()

        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(label="刷新", style=discord.ButtonStyle.secondary, emoji="🔄")
    async def refresh_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """刷新按鈕"""
        self.facilities = await repositories.get_user_facilities(self.user_id)
        self.current_page = 0  # 重置到第一頁
        embed = await self.create_facility_management_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(
        label="返回主頁", style=discord.ButtonStyle.primary, emoji="🏠", row=1
    )
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """返回主頁按鈕"""
        from pioneer.views.pioneer_views import PioneerMainView

        view = PioneerMainView(self.user_id, self.game_data, self.bot)
        embed = await view.create_main_embed(interaction)
        await interaction.response.edit_message(embed=embed, view=view)


class FacilitySelectionView(BaseView):
    """設施選擇視圖"""

    def __init__(
        self,
        user_id: int,
        facilities: List[Facility],
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facilities = facilities

        # 添加設施選擇下拉選單
        if facilities:
            self.add_item(FacilitySelectDropdown(facilities, game_data))

    async def create_selection_embed(self) -> discord.Embed:
        """創建選擇 Embed"""
        embed = discord.Embed(
            title="🔍 選擇設施",
            description="從下拉選單中選擇要查看或管理的設施",
            color=0x2B2D31,
        )

        embed.add_field(
            name="操作說明",
            value="使用下方的下拉選單選擇設施，將會進入該設施的專屬管理頁面。",
            inline=False,
        )

        return embed

    @discord.ui.button(
        label="返回設施管理", style=discord.ButtonStyle.secondary, emoji="🔙"
    )
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """返回設施管理按鈕"""
        view = FacilityManagementView(
            self.user_id, self.facilities, self.game_data, self.bot
        )
        embed = await view.create_facility_management_embed()
        await interaction.response.edit_message(embed=embed, view=view)


class FacilitySelectDropdown(discord.ui.Select):
    """設施選擇下拉選單"""

    def __init__(self, facilities: List[Facility], game_data: "GameDataLoader"):
        self.facilities = facilities
        self.game_data = game_data

        options = []
        for facility in facilities[:25]:
            facility_config = game_data.get_facility_config(facility.facility_type)
            facility_name = facility.facility_name or (
                facility_config.name if facility_config else facility.facility_type
            )

            status_emoji = "🟢" if facility.is_active else "🔴"
            type_emoji = "🏪" if facility.facility_type == "shop" else "🏭"
            description = f"等級 {facility.level} | {facility_config.name if facility_config else '未知類型'}"

            options.append(
                discord.SelectOption(
                    label=f"{status_emoji} {facility_name}",
                    description=description,
                    emoji=type_emoji,
                    value=str(facility.id),
                )
            )

        super().__init__(
            placeholder="選擇要管理的設施...",
            options=options,
            min_values=1,
            max_values=1,
        )

    async def callback(self, interaction: discord.Interaction):
        """下拉選單回調 (修改)"""
        assert self.view is not None
        facility_id = int(self.values[0])
        selected_facility = next(
            (f for f in self.facilities if f.id == facility_id), None
        )

        if not selected_facility:
            raise PioneerError("設施不存在")

        # --- 核心修改：根據設施類型選擇視圖 ---
        if selected_facility.facility_type == "shop":
            view = ShopDetailView(
                interaction.user.id, selected_facility, self.game_data, self.view.bot
            )
            embed = await view.create_shop_embed()
        else:  # 其他所有普通設施
            view = FacilityDetailView(
                interaction.user.id, selected_facility, self.game_data, self.view.bot
            )
            embed = await view.create_detail_embed()

        await interaction.response.edit_message(embed=embed, view=view)


class FacilityDetailView(BaseView):
    """設施詳情視圖"""

    def __init__(
        self,
        user_id: int,
        facility: Facility,
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facility = facility

    async def create_detail_embed(self) -> discord.Embed:
        """創建詳情 Embed"""
        facility_config = self.game_data.get_facility_config(
            self.facility.facility_type
        )
        facility_name = self.facility.facility_name or (
            facility_config.name if facility_config else self.facility.facility_type
        )

        embed = discord.Embed(
            title=f"🏭 {facility_name}", description="設施詳細信息", color=0x2B2D31
        )

        type_name = (
            facility_config.name if facility_config else self.facility.facility_type
        )
        status = "🟢 運行中" if self.facility.is_active else "🔴 已停止"
        embed.add_field(
            name="基本信息",
            value=f"類型: {type_name}\n等級: {self.facility.level}\n狀態: {status}",
            inline=True,
        )

        # 顯示卡片加成
        assignment = await repositories.get_facility_card_assignment(self.facility.id)
        card_bonus_text = "無"
        if assignment:
            # 這裡可以進一步獲取卡片名稱顯示，為簡化先顯示"已指派"
            card_bonus_text = "✅ 已指派"

        embed.add_field(name="卡片加成", value=card_bonus_text, inline=True)

        if self.facility.upgrades:
            upgrade_text = [
                f"• {upgrade_type}" for upgrade_type in self.facility.upgrades.keys()
            ]
            embed.add_field(
                name="已解鎖升級",
                value="\n".join(upgrade_text) if upgrade_text else "無",
                inline=True,
            )
        else:
            embed.add_field(name="已解鎖升級", value="無", inline=True)

        return embed

    @discord.ui.button(label="升級設施", style=discord.ButtonStyle.primary, emoji="⬆️")
    async def upgrade_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        view = FacilityUpgradeView(
            self.user_id, self.facility, self.game_data, self.bot
        )
        embed = await view.create_upgrade_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(label="指派卡片", style=discord.ButtonStyle.primary, emoji="🎴")
    async def assign_card_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        from pioneer.views.card_assignment_view import CardAssignmentView

        view = CardAssignmentView(
            self.user_id, self.facility.id, self.game_data, repositories, self.bot
        )
        embed = await view.create_assignment_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(label="刷新", style=discord.ButtonStyle.secondary, emoji="🔄")
    async def refresh_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        updated_facility = await repositories.get_facility(self.facility.id)
        if updated_facility:
            self.facility = updated_facility
            embed = await self.create_detail_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            raise PioneerError("設施不存在或已被刪除")

    @discord.ui.button(
        label="返回列表", style=discord.ButtonStyle.secondary, emoji="🔙"
    )
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        facilities = await repositories.get_user_facilities(self.user_id)
        view = FacilityManagementView(
            self.user_id, facilities, self.game_data, self.bot
        )
        embed = await view.create_facility_management_embed()
        await interaction.response.edit_message(embed=embed, view=view)


# --- Modal for stocking items ---
class StockItemModal(BaseModal, title="上架物品到商店"):
    def __init__(self, facility_id: int, game_data: "GameDataLoader", bot: "BotType"):
        super().__init__(bot=bot, title="上架物品到商店")
        self.facility_id = facility_id
        self.game_data = game_data

    item_name = discord.ui.TextInput(
        label="物品名稱",
        placeholder="輸入您倉庫中要上架的物品的完整名稱...",
        required=True,
    )

    quantity = discord.ui.TextInput(
        label="上架數量", placeholder="輸入要上架的數量...", required=True
    )

    def _find_item_id_by_name(self, name: str) -> str | None:
        """根據物品名稱查找物品ID"""
        all_items = self.game_data.get_all_items()
        for item_id, item_config in all_items.items():
            if item_config.name == name:
                return item_id
        return None

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        # 1. 驗證輸入
        item_name_str = self.item_name.value
        quantity_str = self.quantity.value

        try:
            quantity = int(quantity_str)
            if quantity <= 0:
                raise PioneerError("數量必須是正整數。")
        except ValueError as e:
            raise PioneerError("數量必須是有效的數字。") from e

        item_id = self._find_item_id_by_name(item_name_str)
        if not item_id:
            raise PioneerError(f"找不到名為「{item_name_str}」的物品。")

        # 2. 調用 action_module
        from pioneer.modules import action_module

        params = {
            "facility_id": self.facility_id,
            "item_id": item_id,
            "quantity": quantity,
        }
        result = await action_module.execute_action(
            interaction.user.id, "stock_shop_item", params
        )

        if result.success:
            embed = SuccessEmbed(description=result.message)
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            # action_module 返回的失敗通常是業務邏輯問題
            raise PioneerError(result.message)


# --- 新增：專門的商店詳情視圖 ---
class ShopDetailView(BaseView):
    """商店設施詳情視圖"""

    def __init__(
        self,
        user_id: int,
        facility: Facility,
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facility = facility

    async def create_shop_embed(self) -> discord.Embed:
        """創建商店詳情 Embed"""
        facility_config = self.game_data.get_facility_config(
            self.facility.facility_type
        )
        facility_name = self.facility.facility_name or (
            facility_config.name if facility_config else self.facility.facility_type
        )

        embed = discord.Embed(
            title=f"🏪 {facility_name} (Lv.{self.facility.level})",
            description="管理您的商店，將貨物上架銷售以賺取油幣。",
            color=0x50E3C2,
        )

        # 獲取貨架槽位資訊
        shelf_slots = await repositories.get_facility_slots(self.facility.id)
        shelf_slots = [s for s in shelf_slots if s.slot_type == "shelf"]

        shelf_text = []
        if shelf_slots:
            for i, slot in enumerate(shelf_slots):
                if slot.item_id and slot.quantity > 0:
                    item_config = self.game_data.get_item_config(slot.item_id)
                    item_name = item_config.name if item_config else "未知物品"
                    price = item_config.base_sell_price if item_config else 0
                    shelf_text.append(
                        f"**貨架 {i + 1}:** {item_name} x{slot.quantity} (單價: {price:,})"
                    )
                else:
                    shelf_text.append(f"**貨架 {i + 1}:** [空]")
        else:
            shelf_text.append("此商店沒有貨架槽位。")

        embed.add_field(name="📦 貨架商品", value="\n".join(shelf_text), inline=False)

        # 銷售效率
        base_speed = facility_config.process_time if facility_config else 999
        marketing_bonus = (
            1.3
            if self.facility.upgrades and "marketing" in self.facility.upgrades
            else 1.0
        )  # 假設營銷升級提供30%速度
        final_speed = base_speed / marketing_bonus

        embed.add_field(
            name="📈 銷售效率",
            value=f"基礎銷售速度: {base_speed} 秒/件\n"
            f"當前銷售速度: {final_speed:.2f} 秒/件",
            inline=True,
        )

        # 指派卡片
        assignment = await repositories.get_facility_card_assignment(self.facility.id)
        card_bonus_text = "無"
        if assignment:
            # 這裡可以進一步獲取卡片名稱顯示，為簡化先顯示"已指派"
            card_bonus_text = "✅ 已指派"

        embed.add_field(name="🎴 卡片加成", value=card_bonus_text, inline=True)

        return embed

    @discord.ui.button(label="上架物品", style=discord.ButtonStyle.success, emoji="📥")
    async def stock_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """彈出一個 Modal 讓玩家輸入要上架的物品和數量"""
        modal = StockItemModal(self.facility.id, self.game_data, self.bot)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="升級", style=discord.ButtonStyle.primary, emoji="⬆️")
    async def upgrade_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        view = FacilityUpgradeView(
            self.user_id, self.facility, self.game_data, self.bot
        )
        embed = await view.create_upgrade_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(label="指派卡片", style=discord.ButtonStyle.primary, emoji="🎴")
    async def assign_card_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        from pioneer.views.card_assignment_view import CardAssignmentView

        view = CardAssignmentView(
            self.user_id, self.facility.id, self.game_data, repositories, self.bot
        )
        embed = await view.create_assignment_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(
        label="返回列表", style=discord.ButtonStyle.secondary, emoji="🔙", row=2
    )
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        facilities = await repositories.get_user_facilities(self.user_id)
        view = FacilityManagementView(
            self.user_id, facilities, self.game_data, self.bot
        )
        embed = await view.create_facility_management_embed()
        await interaction.response.edit_message(embed=embed, view=view)


class FacilityUpgradeView(BaseView):
    """設施升級視圖"""

    def __init__(
        self,
        user_id: int,
        facility: Facility,
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.facility = facility

    async def create_upgrade_embed(self) -> discord.Embed:
        """創建升級 Embed"""
        facility_config = self.game_data.get_facility_config(
            self.facility.facility_type
        )
        facility_name = self.facility.facility_name or (
            facility_config.name if facility_config else self.facility.facility_type
        )

        embed = discord.Embed(
            title=f"⬆️ 升級 {facility_name}",
            description="選擇要解鎖的升級",
            color=0x2B2D31,
        )

        if facility_config and facility_config.upgrades:
            available_upgrades = []
            for upgrade_type, upgrade_config in facility_config.upgrades.items():
                if (
                    self.facility.upgrades
                    and upgrade_type not in self.facility.upgrades
                ):
                    available_upgrades.append((upgrade_type, upgrade_config))

            if available_upgrades:
                upgrade_text = []
                for upgrade_type, upgrade_config in available_upgrades[
                    :5
                ]:  # 只顯示前5個
                    cost_text = ""
                    # 統一使用油幣系統，移除信用點檢查
                    if upgrade_config.cost_oil:
                        cost_text = f"{upgrade_config.cost_oil:,} 油幣"
                    else:
                        cost_text = "免費"

                    upgrade_text.append(
                        f"• **{upgrade_type}**\n  {upgrade_config.description}\n  成本: {cost_text}"
                    )

                embed.add_field(
                    name="可用升級", value="\n\n".join(upgrade_text), inline=False
                )
            else:
                embed.add_field(
                    name="無可用升級", value="所有升級都已解鎖", inline=False
                )
        else:
            embed.add_field(
                name="無升級配置", value="此設施沒有可用的升級", inline=False
            )

        return embed

    @discord.ui.button(label="返回", style=discord.ButtonStyle.secondary, emoji="🔙")
    async def back_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """返回按鈕"""
        # 判斷是從哪個視圖過來的
        if self.facility.facility_type == "shop":
            view = ShopDetailView(self.user_id, self.facility, self.game_data, self.bot)
            embed = await view.create_shop_embed()
        else:
            view = FacilityDetailView(
                self.user_id, self.facility, self.game_data, self.bot
            )
            embed = await view.create_detail_embed()

        await interaction.response.edit_message(embed=embed, view=view)
