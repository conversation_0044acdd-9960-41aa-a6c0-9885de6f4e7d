"""
Pioneer System 製作處理器
處理製作類動作
"""

from typing import Any, Dict

from pioneer.exceptions import (
    PioneerActionError,
    PioneerInsufficientItemsError,
    PioneerNotFoundError,
    PioneerValidationError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

from .base_processor import BaseProcessor


class CraftProcessor(BaseProcessor):
    """製作動作處理器"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """執行製作動作

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 動作參數（可能包含製作數量等）

        Returns:
            ActionResult: 執行結果
        """
        try:
            # 獲取製作配方ID，優先從 params 獲取，否則從 action_config 的 name 推斷
            # 這樣可以支持兩種調用方式：
            # 1. 通用 "craft" 動作 + recipe_id 參數（向後兼容）
            # 2. 具體動作 ID（如 "craft_wooden_plank"）
            recipe_id = params.get("recipe_id") or action_config.name
            if not recipe_id:
                raise PioneerValidationError("未指定製作配方。")

            quantity_param = params.get("quantity", "1")

            recipe_config = self.game_data.get_recipe_config(recipe_id)
            if not recipe_config:
                raise PioneerNotFoundError(f"找不到配方「{recipe_id}」。")

            if isinstance(quantity_param, str) and quantity_param.lower() == "max":
                quantity = await self._calculate_max_craftable_quantity(
                    user_id, recipe_config
                )
                if quantity == 0:
                    raise PioneerInsufficientItemsError(
                        "您沒有足夠的材料來製作任何數量的「{recipe_config.name}」。"
                    )
            else:
                try:
                    quantity = int(quantity_param)
                    if quantity <= 0:
                        raise PioneerValidationError("製作數量必須是一個大於零的整數。")
                except (ValueError, TypeError) as e:
                    raise PioneerValidationError(
                        "無效的製作數量，請輸入一個有效的數字。"
                    ) from e

            if recipe_config.skill_required:
                skill_check = await self._check_skill_level_requirement(
                    user_id,
                    recipe_config.skill_required,
                    recipe_config.min_skill_level,
                )
                if not skill_check:
                    skill_name = self.game_data.get_skill_name(
                        recipe_config.skill_required
                    )
                    raise PioneerActionError(
                        f"技能「{skill_name}」等級不足，需要達到 {recipe_config.min_skill_level} 級才能製作。"
                    )

            if recipe_config.facility_required:
                facility_check = await self._check_facility_requirement(
                    user_id, recipe_config.facility_required
                )
                if not facility_check:
                    facility_config_data = self.game_data.get_facility_config(
                        recipe_config.facility_required
                    )
                    facility_name = (
                        facility_config_data.name
                        if facility_config_data
                        else recipe_config.facility_required
                    )
                    raise PioneerActionError(f"製作此物品需要「{facility_name}」設施。")

            missing_materials = []
            for input_item in recipe_config.inputs:
                item_id = input_item["item_id"]
                required_quantity = input_item["quantity"] * quantity
                warehouse_item = await self.repository.get_warehouse_item(
                    user_id, item_id
                )
                current_quantity = warehouse_item.quantity if warehouse_item else 0
                if int(current_quantity) < int(required_quantity):
                    item_name = self.game_data.get_item_name(str(item_id)) or item_id
                    missing_materials.append(
                        f"• {item_name}: 需要 {required_quantity}, 現有 {current_quantity}"
                    )
            if missing_materials:
                raise PioneerInsufficientItemsError(
                    "製作材料不足：\n" + "\n".join(missing_materials)
                )

            for input_item in recipe_config.inputs:
                item_id = input_item["item_id"]
                consume_quantity = input_item["quantity"] * quantity
                await self.repository.consume_warehouse_item(
                    user_id, item_id, consume_quantity
                )

            # 計算產出
            rewards = []
            total_xp = 0

            for output_item in recipe_config.outputs:
                item_id = output_item["item_id"]
                base_quantity = output_item["quantity"] * quantity
                xp = output_item.get("xp", 1) * quantity

                if recipe_config.skill_required:
                    final_quantity = await self._calculate_output_quantity(
                        int(base_quantity),
                        user_id,
                        recipe_config.skill_required,
                        facility_id=None,
                    )
                else:
                    final_quantity = int(base_quantity)

                await self.repository.add_warehouse_item(
                    user_id, item_id, final_quantity
                )
                rewards.append(
                    {"type": "item", "item_id": item_id, "quantity": final_quantity}
                )
                total_xp += int(xp)

            xp_gained = {}
            message = f"製作成功！您獲得了 {quantity} 個「{recipe_config.name}」。"
            if recipe_config.skill_required and total_xp > 0:
                skill_id = recipe_config.skill_required
                final_xp = await self._calculate_xp_gain(
                    int(total_xp), user_id, skill_id, facility_id=None
                )
                skill_before = await self.repository.get_user_skill(user_id, skill_id)
                if not skill_before:
                    raise PioneerNotFoundError(
                        f"找不到玩家 {user_id} 的技能 {skill_id}。"
                    )
                level_before = skill_before.level

                await self.repository.add_skill_xp(user_id, skill_id, final_xp)
                skill_after = await self.repository.get_user_skill(user_id, skill_id)
                if not skill_after:
                    raise PioneerNotFoundError(
                        f"更新經驗後找不到玩家 {user_id} 的技能 {skill_id}。"
                    )
                level_after = skill_after.level

                xp_gained[skill_id] = final_xp
                skill_name = self.game_data.get_skill_name(skill_id) or skill_id

                if level_after > level_before:
                    message += f"\n您的「{skill_name}」技能提升至 {level_after} 級！"
                else:
                    message += f"\n獲得了 {final_xp} 點「{skill_name}」經驗。"

            from pioneer.services.task_updater import task_updater

            await task_updater.check_and_update_tasks(
                user_id, "craft", recipe=recipe_id, quantity=quantity
            )

            return ActionResult.success_result(
                message=message, rewards=rewards, xp_gained=xp_gained
            )

        except (
            PioneerNotFoundError,
            PioneerActionError,
            PioneerInsufficientItemsError,
            PioneerValidationError,
        ) as e:
            raise e
        except Exception as e:
            logger.error("製作處理器執行時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError(
                f"執行製作動作 '{action_config.name}' 時發生系統錯誤。"
            ) from e

    async def _check_facility_requirement(
        self, user_id: int, facility_type: str
    ) -> bool:
        """檢查用戶是否擁有指定類型的設施"""
        facilities = await self.repository.get_user_facilities_by_type(
            user_id, facility_type
        )
        return len(facilities) > 0

    async def _calculate_max_craftable_quantity(
        self, user_id: int, recipe_config
    ) -> int:
        """計算最大可製作數量"""
        if not recipe_config.inputs:
            return 999  # 如果沒有輸入材料，可以無限製作（或返回一個較大的數）

        max_possible = float("inf")

        for input_item in recipe_config.inputs:
            item_id = input_item["item_id"]
            required_per_craft = input_item["quantity"]

            if required_per_craft <= 0:
                continue

            warehouse_item = await self.repository.get_warehouse_item(user_id, item_id)
            current_quantity = warehouse_item.quantity if warehouse_item else 0

            possible_crafts = current_quantity // required_per_craft
            max_possible = min(max_possible, possible_crafts)

        return int(max_possible) if max_possible != float("inf") else 0
