"""
Gacha系統視圖通用工具函數
"""

from typing import Any, Dict, List, Optional, Tuple, Union

import discord
from discord import app_commands

from config.app_config import get_config
from gacha.constants import RarityLevel
from gacha.models.models import Card, CardWithStatus
from utils.logger import logger


def format_oil(amount: int, label: str = "餘額") -> str:
    """格式化油幣數量顯示

    參數:
        amount: 油幣數量
        label: 顯示標籤 (例如 "餘額", "價格", "贏得", "輸掉")

    返回:
        str: 格式化後的油幣字符串
    """
    sign = ""
    if label in ["贏得", "淨收益"]:
        sign = "+"
    elif label in ["輸掉", "淨損失"]:
        sign = "-"
        amount = abs(amount)
    from config.app_config import get_oil_emoji

    return f"{sign}{label}:`{amount:,}` {get_oil_emoji()}"


def format_number(amount: int) -> str:
    """格式化數字顯示，添加千分位分隔符

    參數:
        amount: 數字

    返回:
        str: 格式化後的數字字符串
    """
    return f"{amount:,}"


def get_rarity_display_code(
    rarity_level: Optional[RarityLevel], pool_type: Optional[str] = None
) -> str:
    """
    獲取稀有度對應的顯示代碼 (通常是 emoji)。

    此函數會根據傳入的 RarityLevel Enum 和可選的 pool_type，
    從配置中查找對應的顯示代碼。
    優先級：特定卡池配置 (`get_pool_specific_rarity_emojis()`)
             -> 默認配置 (`get_default_rarity_emojis()`)。

    參數:
        rarity_level: RarityLevel Enum 的成員，或 None。
        pool_type: 可選的卡池類型字串。

    返回:
        str: 對應的顯示代碼 (emoji)。如果找不到或輸入為 None，則返回空字串。
    """
    if rarity_level is None:
        return ""
    rarity_value = rarity_level.value
    if pool_type:
        from config.app_config import (
            get_default_rarity_emojis,
            get_pool_specific_rarity_emojis,
        )

        pool_specific_emojis_map = get_pool_specific_rarity_emojis().get(pool_type)
        if isinstance(pool_specific_emojis_map, dict):
            emoji = pool_specific_emojis_map.get(rarity_value)
            if emoji:
                return emoji
    else:
        from config.app_config import get_default_rarity_emojis
    default_emoji = get_default_rarity_emojis().get(rarity_value)
    if default_emoji:
        return default_emoji
    return ""


def get_encyclopedia_rarity_emoji(
    rarity_level: Optional[RarityLevel], pool_type: Optional[str] = None
) -> str:
    """獲取全圖鑑專用的稀有度表情符號 (使用 RarityLevel Enum，考慮卡池)"""
    if rarity_level is None:
        return "❓"
    rarity_value = rarity_level.value
    from config.app_config import (
        get_default_encyclopedia_rarity_emojis,
        get_pool_specific_encyclopedia_rarity_emojis,
    )

    if pool_type and pool_type in get_pool_specific_encyclopedia_rarity_emojis():
        pool_specific_emojis = get_pool_specific_encyclopedia_rarity_emojis()[pool_type]
        emoji = pool_specific_emojis.get(rarity_value)
        if emoji and (pool_type == "main" or "ID" not in emoji):
            return emoji
    default_emoji = get_default_encyclopedia_rarity_emojis().get(rarity_value)
    if default_emoji:
        return default_emoji
    return get_rarity_display_code(rarity_level, pool_type)


def get_rarity_color(
    rarity_level: Optional[RarityLevel], pool_type: Optional[str] = None
) -> int:
    """根據稀有度返回對應的Discord Embed顏色值 (使用 RarityLevel Enum，考慮卡池)"""
    if rarity_level is None:
        return 8421504
    rarity_value = rarity_level.value
    from config.app_config import get_rarity_colors_int

    rarity_colors = get_rarity_colors_int()
    actual_pool_key = (
        pool_type if pool_type and pool_type in rarity_colors else "default"
    )
    pool_colors = rarity_colors.get(actual_pool_key)
    if pool_colors:
        color = pool_colors.get(rarity_value)
        if color is not None:
            return color
    if actual_pool_key != "default":
        default_pool_colors = rarity_colors.get("default")
        if default_pool_colors:
            color = default_pool_colors.get(rarity_value)
            if color is not None:
                return color
    return 8421504


def get_rarity_image(rarity_level: Optional[RarityLevel]) -> Optional[str]:
    """獲取對應稀有度的圖片URL (使用 RarityLevel Enum)"""
    if rarity_level is None:
        return None
    rarity_value = rarity_level.value
    from config.app_config import get_rarity_images_url

    all_pools_images = get_rarity_images_url().get("all_pools")
    if all_pools_images:
        return all_pools_images.get(rarity_value)
    return None


def get_user_friendly_rarity_name(rarity_level: Optional[RarityLevel]) -> str:
    """
    獲取用戶友好的稀有度顯示名稱。

    此函數會根據傳入的 RarityLevel Enum，從配置中查找對應的
    中文稀有度名稱。

    例如：
    - RarityLevel.COMMON (假設其 value 為 1, 對應的 display_code 為 "C")
      -> "普通 (C)"

    參數:
        rarity_level: RarityLevel Enum 的成員，或 None。

    返回:
        str: 對應的中文稀有度名稱。如果找不到或輸入為 None，則返回備用字串。
    """
    if rarity_level is None:
        return "未知稀有度"
    from config.app_config import (
        get_rarity_display_codes,
        get_user_friendly_rarity_display_names,
    )

    display_code = get_rarity_display_codes().get(rarity_level.value)
    if display_code:
        friendly_name = get_user_friendly_rarity_display_names().get(display_code)
        if friendly_name:
            return friendly_name
        return f"{rarity_level.name.replace('_', ' ').title()} ({display_code})"
    return f"{rarity_level.name.replace('_', ' ').title()} (代碼 {rarity_level.value})"


def get_star_emoji_string(star_level: int) -> str:
    """根據星級返回對應的表情符號字串

    參數:
        star_level: 星級數值

    返回:
        str: 表情符號字串
    """
    if star_level <= 0:
        return ""
    from config.app_config import get_star_emojis

    star_emojis = get_star_emojis()
    stars_per_tier = int(str(get_config("gacha_core_settings.stars_per_tier", 5)))
    tier = min((star_level - 1) // stars_per_tier + 1, len(star_emojis))
    stars_count = (star_level - 1) % stars_per_tier + 1
    emoji = star_emojis.get(tier, star_emojis.get(1, ""))
    return emoji * stars_count


def get_ui_emoji(name: str) -> str:
    """獲取通用的 UI 表情符號

    參數:
        name: 表情符號名稱 ('old_card', 'heart', 'new_card')

    返回:
        str: 表情符號字串，找不到時返回空字串
    """
    from config.app_config import get_ui_button_emojis

    ui_emojis = get_ui_button_emojis()
    if name == "old_card":
        return ui_emojis.old_card or ""
    elif name == "heart":
        return ui_emojis.favorite or ""
    elif name == "new_card":
        return ui_emojis.new_card or ""
    return ""


def get_pool_rarity_stats(
    cards_summary: List[Dict[str, Any]], unique_only: bool = False
) -> Dict[str, Dict[int, int]]:
    """將卡片摘要按卡池類型和稀有度進行統計 (使用數字稀有度)

    參數:
        cards_summary: 卡片摘要列表，每個字典應包含 'pool_type', 'rarity'(int), 'count'(唯一卡片數), 'total_quantity'(總卡片數)
        unique_only: 是否僅計算不同卡片（使用 'count' 欄位）還是計算總張數（使用 'total_quantity' 欄位）

    返回:
        Dict[str, Dict[int, int]]: 按卡池類型和數字稀有度分組的統計結果
    """
    pool_rarity_counts: Dict[str, Dict[int, int]] = {}
    for item in cards_summary:
        pool_type = item.get("pool_type")
        rarity_val = item.get("rarity")
        if pool_type is None or rarity_val is None:
            logger.warning(
                "[GACHA][VIEW_UTILS] Skipped item in get_pool_rarity_stats due to missing pool_type or rarity: %s",
                item,
            )
            continue
        try:
            rarity_int = int(rarity_val)
        except (ValueError, TypeError):
            logger.warning(
                "[GACHA][VIEW_UTILS] Skipped item in get_pool_rarity_stats due to invalid rarity value: %s in item %s",
                rarity_val,
                item,
            )
            continue
        pool_data = pool_rarity_counts.setdefault(pool_type, {})
        if unique_only:
            value_to_store = item.get("count", 0)
        else:
            value_to_store = item.get("quantity_sold", item.get("total_quantity", 0))
        pool_data[rarity_int] = pool_data.get(rarity_int, 0) + value_to_store
    return pool_rarity_counts


# ===== 新增：配置驅動的 Choices 生成函數 =====


def get_pool_type_choices(
    show_individual_pools_only: bool = False,
) -> List[app_commands.Choice[str]]:
    """
    從配置生成卡池類型的 choices 選項

    Args:
        show_individual_pools_only: 控制卡池列表的篩選邏輯
            - True: 包含所有獨立卡池 (包括 "main")，排除 "混合池" (key "all")
            - False: 包含所有卡池但排除 "主卡池" (key "main")，包含 "混合池" (key "all")

    Returns:
        List[app_commands.Choice[str]]: 卡池選項列表
    """
    choices = []
    try:
        from config.app_config import get_config

        pool_configs = get_config("gacha_core_settings.pool_configurations", {})
        if not isinstance(pool_configs, dict):
            return []

        if show_individual_pools_only:
            # 用於 /aw, /mw, /sw 等指令
            for key, config_detail in pool_configs.items():
                if key == "all":  # 排除混合池
                    continue
                display_name = (
                    config_detail.name if hasattr(config_detail, "name") else key
                )
                choices.append(app_commands.Choice(name=display_name, value=key))
        else:
            # 用於抽卡指令如 /w 等
            for key, config_detail in pool_configs.items():
                if key == "main":  # 排除主卡池
                    continue
                display_name = (
                    config_detail.name if hasattr(config_detail, "name") else key
                )
                choices.append(app_commands.Choice(name=display_name, value=key))

    except Exception as e:
        logger.error("生成卡池類型choices時發生錯誤: %s", e)
        return []

    return choices


def get_rarity_choices() -> List[app_commands.Choice[int]]:
    """
    從配置生成稀有度的 choices 選項

    Returns:
        List[app_commands.Choice[int]]: 稀有度選項列表
    """
    choices = []
    try:
        from config.app_config import (
            get_rarity_display_codes,
            get_user_friendly_rarity_display_names,
        )

        rarity_codes = get_rarity_display_codes()
        rarity_names = get_user_friendly_rarity_display_names()
        for rarity_level in RarityLevel:
            display_code = rarity_codes.get(rarity_level.value)
            display_name = rarity_names.get(
                str(display_code),
                f"{rarity_level.name.replace('_', ' ').title()} ({display_code or rarity_level.name[:1]})",
            )
            choices.append(
                app_commands.Choice(name=display_name, value=rarity_level.value)
            )
    except Exception as e:
        logger.error("生成稀有度choices時發生錯誤: %s", e)
        return []

    return choices


def get_gif_type_choices() -> List[app_commands.Choice[str]]:
    """
    從配置生成GIF類型的 choices 選項

    Returns:
        List[app_commands.Choice[str]]: GIF類型選項列表
    """
    choices = []
    try:
        # 導入GIF配置模塊
        from auxiliary.services.image_processing.gif_modifier.config import (
            list_available_gifs,
        )

        # 定義 GIF 名稱的顯示文字映射
        gif_display_names = {
            "kick_trash.gif": "踢進垃圾桶",
            "frog.gif": "青蛙(很噁請小心)",
            "uncle_abe.gif": "阿北(很噁請小心)",
            "punching.gif": "打拳",
            "誰想要約會.gif": "誰想要約會",
            "petpet.gif": "摸頭",
            "都不帶我玩.gif": "都不帶我玩",
            # 可以在這裡添加更多自定義顯示名稱
        }

        # 從配置系統獲取GIF列表
        gif_names = list_available_gifs()

        # 轉換為Choice對象
        for name in gif_names:
            display_name = gif_display_names.get(name, name.split(".")[0])
            choices.append(app_commands.Choice(name=display_name, value=name))

    except Exception as e:
        logger.error("生成GIF類型choices時發生錯誤: %s", e)
        return []

    return choices


def get_completion_indicator(completion_rate: float) -> Tuple[str, discord.Color]:
    """根據完成率獲取指示器圖標和顏色

    參數:
        completion_rate: 完成率百分比

    返回:
        包含指示器圖標和顏色的元組
    """
    from config.app_config import get_completion_indicator_emojis

    completion_emojis = get_completion_indicator_emojis()
    if completion_rate == 100:
        indicator = completion_emojis["100"]
        color = discord.Color.gold()
    elif completion_rate >= 75:
        indicator = completion_emojis["75+"]
        color = discord.Color.green()
    elif completion_rate >= 50:
        indicator = completion_emojis["50+"]
        color = discord.Color.blue()
    elif completion_rate > 0:
        indicator = completion_emojis["0+"]
        color = discord.Color.light_gray()
    else:
        indicator = completion_emojis["0"]
        color = discord.Color.light_gray()
    return (indicator, color)


def to_card_with_status(
    result: Union[Dict[str, Any], Card, CardWithStatus],
) -> CardWithStatus:
    """將結果統一轉換為 CardWithStatus 對象"""
    from gacha.models.models import CardStatus

    if isinstance(result, CardWithStatus):
        return result
    if isinstance(result, dict):
        try:
            return CardWithStatus.from_result_dict(result)
        except (ValueError, KeyError) as e:
            logger.error(
                "Failed to convert dict to CardWithStatus: %s. Dict: %s", e, result
            )
            raise
    if isinstance(result, Card):
        # 如果只有 Card，創建一個預設狀態的 CardWithStatus
        return CardWithStatus.create(card=result, status=CardStatus())
    raise TypeError(f"Unsupported result type: {type(result)}")
