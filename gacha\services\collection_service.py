import math
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import asyncpg

# --- 從中心化管理器導入連接 ---
from database.postgresql.async_manager import get_redis_client
from gacha.exceptions import (
    CardNotFoundError,
    DatabaseOperationError,
    InsufficientBalanceError,
    InvalidQuantityError,
    InvalidTransferError,
    UserNotFoundError,
)
from gacha.models.filters import CollectionFilters
from gacha.models.models import SeriesCollection

# --- 導入依賴的 Repository 和 Service 模組 ---
from gacha.repositories.collection import user_collection_repository
from gacha.services import highest_star_maintenance_service as highest_star_maintenance
from gacha.services import user_service
from utils.logger import logger

# --- 模組級常數 ---
CARDS_PER_PAGE = 1
CARDS_PER_PAGE_LIST_MODE = 10
SERIES_PER_PAGE = 10

# --- 模組級函式 ---


async def get_favorite_card_count(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """(Async) 獲取用戶標記為最愛的卡片數量"""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)
    return await user_collection_repository.get_user_favorite_card_count(
        user_id, connection=connection
    )


async def get_user_cards_paginated(
    user_id: int,
    page: int = 1,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    filters: Optional[CollectionFilters] = None,
    *,
    pre_fetched_unique_cards: Optional[int] = None,
    pre_fetched_total_cards: Optional[int] = None,
    favorite_priority: bool = True,
    list_mode: bool = False,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 獲取用戶卡冊的分頁數據. Can accept pre-fetched stats."""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)

    if filters is None:
        filters = CollectionFilters()

    page_validated, sort_by_validated, sort_order_validated = (
        _validate_pagination_params(page, sort_by, sort_order)
    )
    cards_per_page = CARDS_PER_PAGE_LIST_MODE if list_mode else CARDS_PER_PAGE
    redis_client = get_redis_client()
    if redis_client is None:
        raise DatabaseOperationError("Redis client is not initialized.")
    result = await user_collection_repository.get_user_cards_paginated(
        redis_client,
        user_id,
        page_validated,
        cards_per_page,
        sort_by_validated,
        sort_order_validated,
        filters,
        pre_fetched_unique_cards=pre_fetched_unique_cards,
        pre_fetched_total_cards=pre_fetched_total_cards,
        favorite_priority=favorite_priority,
        connection=connection,
    )

    return {
        "cards": result.get("cards", []),
        "total_pages": result.get("total_pages", 1),
        "current_page": result.get("current_page", 1),
        "total_cards": result.get("total_cards", 0),
        "unique_cards": result.get("unique_cards", 0),
        "filters": filters,
    }


def _validate_pagination_params(
    page: int, sort_by: str, sort_order: str
) -> Tuple[int, str, str]:
    """驗證分頁參數 (使用統一的驗證器)"""
    from gacha.config.sorting_config import SortingValidator

    return SortingValidator.validate_pagination_params(page, sort_by, sort_order)


async def get_all_series(
    pool_type: Optional[str] = None, connection: Optional[asyncpg.Connection] = None
) -> List[str]:
    """(Async) 獲取所有可用的系列列表"""
    try:
        return await user_collection_repository._get_all_series(
            pool_type, connection=connection
        )
    except AttributeError as e:
        logger.error("[GACHA] user_collection_repository 缺少 _get_all_series 方法")
        raise DatabaseOperationError("收藏存儲庫缺少獲取系列列表的方法") from e


async def get_all_series_paginated(
    page: int = 1,
    pool_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 獲取分頁的系列列表"""
    if page < 1:
        page = 1

    all_series = await get_all_series(pool_type, connection=connection)
    total_series = len(all_series)
    total_pages = math.ceil(total_series / SERIES_PER_PAGE) if total_series > 0 else 0
    page = max(1, min(page, total_pages)) if total_pages > 0 else 1
    start_idx = (page - 1) * SERIES_PER_PAGE
    end_idx = min(start_idx + SERIES_PER_PAGE, total_series)
    current_page_series = all_series[start_idx:end_idx]

    return {
        "series_list": current_page_series,
        "total_pages": total_pages,
        "current_page": page,
        "total_series": total_series,
        "pool_type": pool_type,
    }


async def get_user_all_series_collections(
    user_id: int,
    series_list: List[str],
    pool_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, SeriesCollection]:
    """(Async) 批量獲取用戶對多個系列的收集情況"""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)

    return await user_collection_repository.get_user_series_collections_batch(
        user_id, series_list, pool_type, connection=connection
    )


async def get_user_overall_collection_stats(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 獲取用戶所有系列的總體收集狀況"""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)

    stats = await user_collection_repository.get_overall_collection_stats(
        user_id, connection=connection
    )

    total_collected = stats.get("total_collected", 0)
    total_cards = stats.get("total_cards", 0)
    completion_rate = total_collected / total_cards * 100 if total_cards > 0 else 0

    return {
        "total_collected": total_collected,
        "total_cards": total_cards,
        "completion_rate": completion_rate,
    }


async def get_user_collection_stats(
    user_id: int,
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 獲取用戶收藏統計信息 (簡化版)"""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)

    if filters is None:
        filters = CollectionFilters()

    return await user_collection_repository.get_user_collection_stats(user_id, filters)


async def get_filtered_card_ids(
    user_id: int,
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[int]:
    """(Async) 獲取符合過濾條件的卡片ID列表"""
    await user_service.get_user(user_id, create_if_missing=True, connection=connection)

    if filters is None:
        filters = CollectionFilters()

    return await user_collection_repository.get_filtered_card_ids(
        user_id, filters, connection=connection
    )


async def get_pool_specific_collection_stats(
    user_id: int, pool_type: str, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 獲取用戶特定卡池的收集狀況"""
    user = await user_service.get_user(
        user_id, create_if_missing=True, connection=connection
    )
    if not user:
        logger.warning(
            (
                "[CollectionService] User %s could not be found or created for "
                "get_pool_specific_collection_stats."
            ),
            user_id,
        )
        raise UserNotFoundError(user_id=user_id)

    stats = await user_collection_repository.get_pool_specific_collection_stats(
        user_id, pool_type, connection=connection
    )

    total_collected = stats.get("total_collected", 0)
    total_cards = stats.get("total_cards", 0)
    completion_rate = total_collected / total_cards * 100 if total_cards > 0 else 0

    return {
        "total_collected": total_collected,
        "total_cards": total_cards,
        "completion_rate": completion_rate,
    }


async def find_card_page(
    user_id: int,
    card_id: int,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    filters: Optional[CollectionFilters] = None,
    favorite_priority: bool = True,
    list_mode: bool = False,
    connection: Optional[asyncpg.Connection] = None,
) -> Tuple[int, int]:
    """(Async) 根據卡片ID查找卡片所在的頁碼和該篩選條件下的唯一卡片總數. Returns (page, total_unique_cards_for_filter). Page 0 if not found."""
    user = await user_service.get_user(
        user_id, create_if_missing=True, connection=connection
    )
    if not user:
        raise UserNotFoundError(f"找不到用戶: {user_id}")

    if filters is None:
        filters = CollectionFilters()

    _, sort_by_validated, sort_order_validated = _validate_pagination_params(
        1, sort_by, sort_order
    )
    cards_per_page = CARDS_PER_PAGE_LIST_MODE if list_mode else CARDS_PER_PAGE

    try:
        card_info = await user_collection_repository.get_user_card_position(
            user_id,
            card_id,
            sort_by_validated,
            sort_order_validated,
            filters,
            favorite_priority=favorite_priority,
            connection=connection,
        )
        position = card_info["position"]
        total_unique_for_filter = card_info.get("total", 0)
        page = (position - 1) // cards_per_page + 1
        logger.debug(
            (
                "[GACHA_SERVICE] find_card_page: card_id=%s, position=%s, "
                "total_unique=%s, page=%s, cards_per_page=%s"
            ),
            card_id,
            position,
            total_unique_for_filter,
            page,
            cards_per_page,
        )
        return (page, total_unique_for_filter)
    except CardNotFoundError:
        logger.info(
            "[GACHA_SERVICE] find_card_page: Card %s (user: %s) not found in current filter. Sort: %s %s, Filters: %s.",
            card_id,
            user_id,
            sort_by_validated,
            sort_order_validated,
            filters,
        )
        # 拋出一個新的、本地化訊息的異常
        raise CardNotFoundError(
            f"您尚未擁有 ID 為 `{card_id}` 的卡片，或該卡片不存在。"
        ) from None


async def get_user_card_quantity(
    user_id: int, master_card_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """
    查詢指定用戶擁有特定種類卡片的數量。
    """
    user = await user_service.get_user(user_id, connection=connection)
    if not user:
        logger.warning(
            "[CollectionService] User %s not found for get_user_card_quantity.",
            user_id,
        )
        raise UserNotFoundError(user_id=user_id)

    # 根據規範，移除 try-except，讓錯誤自然冒泡
    # 如果卡片不存在，repository 層應該返回 0 或 None，這裡直接返回
    quantity = await user_collection_repository.get_card_quantity(
        user_id, master_card_id, connection=connection
    )
    return quantity


async def transfer_card_quantity(
    sender_id: int,
    receiver_id: int,
    master_card_id: int,
    quantity: int,
    connection: asyncpg.Connection,
) -> None:
    """
    在兩個用戶之間轉移指定數量特定種類的卡片。
    此方法必須在一個事務 (transaction) 中執行。
    """
    if quantity <= 0:
        logger.warning(
            "[SERVICE_TRANSFER_CARD] Invalid quantity for transfer: %s. Must be positive.",
            quantity,
        )
        raise InvalidTransferError(f"轉移數量必須為正數，當前值: {quantity}")

    if sender_id == receiver_id:
        logger.warning(
            "[SERVICE_TRANSFER_CARD] Sender and receiver are the same user_id: %s. No action taken.",
            sender_id,
        )
        raise InvalidTransferError(f"發送方和接收方不能是同一用戶: {sender_id}")

    await user_service.get_user(sender_id, connection=connection)
    await user_service.get_user(receiver_id, connection=connection)

    try:
        # 獲取發送方的卡片信息（包括星級和數量）
        sender_card_info = await user_collection_repository.get_user_card(
            user_id=sender_id, card_id=master_card_id, connection=connection
        )
        if sender_card_info.quantity < quantity:
            raise InsufficientBalanceError(
                message=f"卡片 {master_card_id} 數量不足，無法轉移。",
                required=quantity,
                current=sender_card_info.quantity,
            )

        sender_star_level = sender_card_info.star_level
        is_complete_transfer = sender_card_info.quantity == quantity

        redis_client = get_redis_client()
        if redis_client is None:
            raise DatabaseOperationError("Redis client is not initialized.")

        (
            sender_remaining_quantity,
            was_card_deleted,
            was_favorite,
        ) = await user_collection_repository.decrement_card_quantity(
            redis_client,
            user_id=sender_id,
            card_id=master_card_id,
            quantity_to_remove=quantity,
            connection=connection,
        )
        logger.debug(
            "[SERVICE_TRANSFER_CARD] Sender %s card %s quantity decremented. Remaining: %s",
            sender_id,
            master_card_id,
            sender_remaining_quantity,
        )

        # 處理接收方：如果是完全轉移，則轉移星級
        if is_complete_transfer:
            receiver_new_quantity = await _increment_card_quantity_with_star_transfer(
                user_id=receiver_id,
                card_id=master_card_id,
                quantity_to_add=quantity,
                transferred_star_level=sender_star_level,
                connection=connection,
            )
            logger.debug(
                "[SERVICE_TRANSFER_CARD] Receiver %s card %s quantity incremented with star transfer (%s stars). New total: %s",
                receiver_id,
                master_card_id,
                sender_star_level,
                receiver_new_quantity,
            )
        else:
            redis_client = get_redis_client()
            if redis_client is None:
                raise DatabaseOperationError("Redis client is not initialized.")
            receiver_new_quantity = (
                await user_collection_repository.increment_card_quantity(
                    redis_client,
                    user_id=receiver_id,
                    card_id=master_card_id,
                    quantity_to_add=quantity,
                    connection=connection,
                )
            )
            logger.debug(
                "[SERVICE_TRANSFER_CARD] Receiver %s card %s quantity incremented (partial transfer, no star transfer). New total: %s",
                receiver_id,
                master_card_id,
                receiver_new_quantity,
            )

        # 收集市場統計更新數據
        unique_owner_updates = []
        favorite_count_updates = []

        # 如果卡片被完全刪除，發送方的獨立擁有者數量減少
        if was_card_deleted:
            unique_owner_updates.append((master_card_id, -1))
            # 只有當卡片完全被刪除且是收藏狀態時，才觸發收藏統計-1
            if was_favorite:
                logger.info(
                    "[SERVICE_TRANSFER_CARD] Card %s was favorite and was completely deleted for user %s, updating favorite stats.",
                    master_card_id,
                    sender_id,
                )
                favorite_count_updates.append((master_card_id, -1))

        # 如果是新擁有者，接收方的獨立擁有者數量增加
        is_new_owner = receiver_new_quantity == quantity
        if is_new_owner:
            unique_owner_updates.append((master_card_id, 1))

        # 計算總擁有量的淨變化（轉移操作中總擁有量不變，發送方-quantity，接收方+quantity，淨變化為0）
        # 因此不需要更新 total_owned_quantity

        # 【新增】在同一事務中更新市場統計
        from gacha.services.direct_market_stats_updater import (
            update_market_stats_in_transaction,
        )

        # 準備統計更新數據
        owner_changes = []
        if unique_owner_updates:
            for card_id, change in unique_owner_updates:
                owner_changes.append({"card_id": card_id, "change": change})

        # 準備收藏更新數據
        favorite_changes = favorite_count_updates if favorite_count_updates else []

        # 執行統計更新（卡片轉移不影響總擁有量，只影響擁有者分佈）
        await update_market_stats_in_transaction(
            conn=connection,
            drawn_card_ids=[],  # 轉移操作不改變總擁有量
            owner_changes=owner_changes,
            favorite_changes=favorite_changes,
        )

        # 安排最高星級維護任務（後台處理）- 只在卡片被完全移除且用戶有星級時觸發
        if highest_star_maintenance and was_card_deleted and sender_star_level > 0:
            try:
                await highest_star_maintenance.schedule_card_traded_maintenance(
                    card_id=master_card_id,
                    sender_user_id=sender_id,
                    receiver_user_id=receiver_id,
                    quantity_traded=quantity,
                )
                logger.debug(
                    "[SERVICE_TRANSFER_CARD] Highest star maintenance scheduled for card %s (sender %s lost card with star level %s)",
                    master_card_id,
                    sender_id,
                    sender_star_level,
                )
            except Exception as e:
                logger.error(
                    "[SERVICE_TRANSFER_CARD] Failed to schedule highest star maintenance for card %s: %s",
                    master_card_id,
                    e,
                    exc_info=True,
                )
                # 不拋出異常，因為這是後台處理，不應影響主要的轉移流程

        return  # 成功時不返回任何內容 (隱式返回 None)

    except (
        ValueError,
        InsufficientBalanceError,
        UserNotFoundError,
        DatabaseOperationError,
    ):
        # 重新拋出已知的業務或資料庫錯誤
        raise
    except Exception as e:
        logger.error(
            (
                "[SERVICE_TRANSFER_CARD] Unexpected error during transfer between %s "
                "and %s for card %s, quantity %s: %s"
            ),
            sender_id,
            receiver_id,
            master_card_id,
            quantity,
            e,
            exc_info=True,
        )
        # 根據規範，讓未知錯誤自然冒泡
        raise


async def _increment_card_quantity_with_star_transfer(
    user_id: int,
    card_id: int,
    quantity_to_add: int,
    transferred_star_level: int,
    connection: asyncpg.Connection,
) -> int:
    """
    增加卡片數量並處理星級轉移
    如果用戶已有該卡片，取最高星級；如果沒有，設置為轉移的星級
    """
    if quantity_to_add <= 0:
        raise InvalidQuantityError(f"quantity_to_add 必須為正整數: {quantity_to_add}")

    try:
        # 查詢接收方當前信息
        current_card_info = await user_collection_repository.get_user_card(
            user_id=user_id, card_id=card_id, connection=connection
        )
        # 接收方已有該卡片：取最高星級
        final_star_level = max(current_card_info.star_level, transferred_star_level)
        new_quantity = current_card_info.quantity + quantity_to_add

        # 更新數量和星級
        query = """
            UPDATE gacha_user_collections
            SET quantity = $1, star_level = $2, last_acquired = $3
            WHERE user_id = $4 AND card_id = $5
            RETURNING quantity
        """
        result = await connection.fetchrow(
            query, new_quantity, final_star_level, datetime.now(), user_id, card_id
        )
        if not result:
            raise DatabaseOperationError(
                f"更新卡片數量和星級失敗 for user={user_id}, card={card_id}"
            )

        logger.debug(
            (
                "[SERVICE_STAR_TRANSFER] User %s card %s: updated quantity to %s, "
                "star level to %s (max of %s and %s)"
            ),
            user_id,
            card_id,
            new_quantity,
            final_star_level,
            current_card_info.star_level,
            transferred_star_level,
        )
        return result["quantity"]
    except CardNotFoundError:
        # 接收方沒有該卡片：直接設置轉移的星級
        now = datetime.now()
        query = """
            INSERT INTO gacha_user_collections (
                user_id, card_id, quantity, star_level, first_acquired, last_acquired, is_favorite
            )
            VALUES ($1, $2, $3, $4, $5, $5, FALSE)
            RETURNING quantity
        """
        result = await connection.fetchrow(
            query, user_id, card_id, quantity_to_add, transferred_star_level, now
        )
        if not result:
            raise DatabaseOperationError(
                f"插入新卡片記錄失敗 for user={user_id}, card={card_id}"
            ) from None

        logger.debug(
            (
                "[SERVICE_STAR_TRANSFER] User %s card %s: created new record with "
                "quantity %s, star level %s"
            ),
            user_id,
            card_id,
            quantity_to_add,
            transferred_star_level,
        )
        return result["quantity"]
    except Exception as e:
        logger.error(
            "[SERVICE_STAR_TRANSFER] Unexpected error during star transfer for user %s, card %s: %s",
            user_id,
            card_id,
            e,
            exc_info=True,
        )
        raise


async def get_detailed_collection_stats(
    user_id: int,
    pool_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """
    獲取詳細的收藏統計數據，包括總體、按稀有度和按卡池的進度。
    """
    conn = connection
    try:
        # 循序獲取所有需要的數據
        if pool_type:
            overall_stats = (
                await user_collection_repository.get_pool_specific_collection_stats(
                    user_id, pool_type, connection=conn
                )
            )
        else:
            overall_stats = (
                await user_collection_repository.get_overall_collection_stats(
                    user_id, connection=conn
                )
            )

        user_summary = await user_collection_repository.get_user_cards_summary(
            user_id, pool_type, connection=conn
        )
        total_counts = await user_collection_repository.get_rarities_total_count(
            pool_type, connection=conn
        )

        # 手動計算並補上 overall 的 completion_rate
        if overall_stats:
            collected = overall_stats.get("total_collected", 0)
            total = overall_stats.get("total_cards", 0)
            overall_stats["completion_rate"] = (
                (collected / total * 100) if total > 0 else 0
            )

        # 1. 處理稀有度進度
        rarity_progress = {}
        user_rarity_counts = {}
        for summary in user_summary:
            rarity = summary["rarity"]
            count = summary["count"]
            user_rarity_counts[rarity] = user_rarity_counts.get(rarity, 0) + count

        total_rarity_counts = {}
        for _, rarities in total_counts.items():
            for rarity, count in rarities.items():
                total_rarity_counts[rarity] = total_rarity_counts.get(rarity, 0) + count

        for rarity, total in sorted(total_rarity_counts.items(), reverse=True):
            collected = user_rarity_counts.get(rarity, 0)
            rarity_progress[rarity] = {
                "collected": collected,
                "total": total,
                "percentage": (collected / total * 100) if total > 0 else 0,
            }

        # 2. 處理卡池進度
        pool_progress = {}
        user_pool_counts = {}
        for summary in user_summary:
            pool = summary["pool_type"]
            count = summary["count"]
            user_pool_counts[pool] = user_pool_counts.get(pool, 0) + count

        total_pool_counts = {}
        for pool, rarities in total_counts.items():
            total_pool_counts[pool] = sum(rarities.values())

        for pool, total in sorted(total_pool_counts.items()):
            collected = user_pool_counts.get(pool, 0)
            pool_progress[pool] = {
                "collected": collected,
                "total": total,
                "percentage": (collected / total * 100) if total > 0 else 0,
            }

        return {
            "pool_type_filter": pool_type,
            "overall": overall_stats,
            "by_rarity": rarity_progress,
            "by_pool": pool_progress,
        }
    except Exception as e:
        logger.error("獲取詳細收藏統計失敗 for user %s: %s", user_id, e, exc_info=True)
        raise
