# ActivityService 設計文件

## 1. 檔案目的

本服務 (`gacha/services/activity_service.py`) 的主要目的是根據使用者的歷史指令紀錄，計算其「活躍度」，並回傳一個獎勵乘數 (multiplier)。此舉旨在鼓勵使用者參與多樣化的活動，而非僅僅領取每日/每小時的被動獎勵。

## 2. 設定參數

以下參數將會定義在 `config/gacha_settings.yaml` 中：

```yaml
activity_monitoring:
  # 追蹤最近多少筆指令紀錄
  command_history_limit: 50
  # 定義哪些指令是 "農夫指令"
  farming_commands:
    - "hourly"
    - "daily"
  # 活躍度指數最低下限，避免獎勵完全歸零
  min_multiplier: 0.1
  # 活躍度指數最高上限，避免獎勵無限膨脹
  max_multiplier: 1.0
```

## 3. 核心函式 `get_reward_multiplier`

### 函式簽名

```python
async def get_reward_multiplier(user_id: int, connection: Connection) -> float:
```

### 步驟說明

1.  從設定檔讀取 `command_history_limit`, `farming_commands`, `min_multiplier`, `max_multiplier`。
2.  使用提供的 `connection` 物件，執行 SQL 查詢，從 `command_usage_stats` 表中獲取指定 `user_id` 最新的 N 筆 (`command_history_limit`) 指令名稱。
3.  遍歷查詢結果，計算「農夫指令」和「活躍指令」的數量。
4.  如果總指令數為 0，直接回傳 `max_multiplier` (1.0)。
5.  計算活躍度指數：`active_count / total_count`。
6.  將活躍度指數應用到獎勵乘數上，並確保結果介於 `min_multiplier` 和 `max_multiplier` 之間。
7.  回傳最終的獎勵乘數。

### SQL 查詢範例

```sql
SELECT command_name
FROM command_usage_stats
WHERE user_id = $1
ORDER BY used_at DESC
LIMIT $2;
```

### 完整程式碼草稿

```python
# gacha/services/activity_service.py

from typing import List, Set

from asyncpg import Connection

from config.app_config import get_config
from database.postgresql.async_manager import get_pool


class ActivityService:
    def __init__(self):
        self.COMMAND_HISTORY_LIMIT = get_config("gacha_core_settings.activity_monitoring.command_history_limit", 50)
        self.FARMING_COMMANDS: Set[str] = set(get_config("gacha_core_settings.activity_monitoring.farming_commands", ["hourly", "daily"]))
        self.MIN_MULTIPLIER = get_config("gacha_core_settings.activity_monitoring.min_multiplier", 0.1)
        self.MAX_MULTIPLIER = get_config("gacha_core_settings.activity_monitoring.max_multiplier", 1.0)

    async def get_reward_multiplier(self, user_id: int, connection: Connection = None) -> float:
        """
        根據使用者最近的指令活動，計算獎勵乘數。
        """
        should_release = False
        if connection is None:
            pool = get_pool()
            connection = await pool.acquire()
            should_release = True

        try:
            query = """
                SELECT command_name
                FROM command_usage_stats
                WHERE user_id = $1
                ORDER BY used_at DESC
                LIMIT $2;
            """
            records = await connection.fetch(query, user_id, self.COMMAND_HISTORY_LIMIT)

            if not records:
                return self.MAX_MULTIPLIER

            total_commands = len(records)
            farming_commands_count = 0

            for record in records:
                if record["command_name"] in self.FARMING_COMMANDS:
                    farming_commands_count += 1
            
            active_commands_count = total_commands - farming_commands_count
            
            # 計算活躍度指數
            activity_index = active_commands_count / total_commands
            
            # 將活躍度指數映射到獎勵乘數，並限制在設定的範圍內
            multiplier = max(self.MIN_MULTIPLIER, min(activity_index, self.MAX_MULTIPLIER))
            
            return multiplier

        finally:
            if should_release and connection:
                await connection.close()

# 建立一個單例
activity_service = ActivityService()
```

## 4. 相依性

*   `config.app_config.get_config`
*   `database.postgresql.async_manager.get_pool`
*   `asyncpg.Connection`