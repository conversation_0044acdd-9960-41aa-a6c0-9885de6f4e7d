"""
抽卡通知服務
負責檢測稀有卡片並發送通知到指定頻道
"""

import os
from typing import Dict, Optional, Set, Tuple, Union, cast

import aiohttp
import discord

from gacha.models.models import Card, CardWithStatus
from utils.logger import logger

# Module-level state
_enabled: bool = False
_webhook_url: str = ""
_notification_config: Dict[str, Set[int]] = {}
_session: Optional[aiohttp.ClientSession] = None


def initialize_notifier():
    """初始化通知服務的配置和會話"""
    global _enabled, _webhook_url, _notification_config, _session

    from config.app_config import GachaNotificationSettings, get_config

    notification_settings_model = get_config("gacha_notification_settings")

    if notification_settings_model is None:
        logger.warning(
            "[GACHA_NOTIFIER] 'gacha_notification_settings' 未在配置中加載，將使用默認值。通知功能可能無法正常工作。"
        )
        notification_settings_model = GachaNotificationSettings()

    notification_settings = cast(GachaNotificationSettings, notification_settings_model)

    webhook_enabled = os.getenv("WEBHOOK_NOTIFICATIONS_ENABLED", "false").lower() in (
        "true",
        "1",
        "yes",
    )
    _enabled = webhook_enabled

    if webhook_enabled:
        logger.info(
            "[GACHA_NOTIFIER] 環境變數 WEBHOOK_NOTIFICATIONS_ENABLED=true，通知功能已啟用"
        )
    else:
        logger.info(
            "[GACHA_NOTIFIER] 環境變數 WEBHOOK_NOTIFICATIONS_ENABLED=false 或未設置，通知功能已禁用"
        )

    _webhook_url = (
        notification_settings.webhook_url
        if notification_settings.webhook_url is not None
        else ""
    )
    raw_notify_rarities = notification_settings.notify_rarities
    _notification_config = {k: set(v) for k, v in raw_notify_rarities.items()}

    if _session is None or _session.closed:
        _session = aiohttp.ClientSession()


async def close_notifier_session():
    """關閉 aiohttp ClientSession."""
    global _session
    if _session and not _session.closed:
        await _session.close()
        _session = None


def should_notify(
    card: Union[Card, CardWithStatus], is_new_card: bool = False, is_wish: bool = False
) -> bool:
    """根據卡片卡池類型和稀有度決定是否發送通知"""
    if not _enabled or not _webhook_url:
        return False

    pool_type, rarity_int = _extract_card_info(card)
    if pool_type is None or rarity_int is None:
        return False

    notify_rarities_for_pool = _notification_config.get(pool_type, set())
    return rarity_int in notify_rarities_for_pool


def _extract_card_info(
    card: Union[Card, CardWithStatus],
) -> Tuple[Optional[str], Optional[int]]:
    """從卡片對象中提取卡池類型和稀有度"""
    if isinstance(card, CardWithStatus):
        actual_card = card.card
    else:
        actual_card = card

    rarity = getattr(actual_card, "rarity", None)
    pool_type = getattr(actual_card, "pool_type", "main")

    if rarity is None:
        return (pool_type, None)
    try:
        rarity_int = int(rarity)
        return (pool_type, rarity_int)
    except (ValueError, TypeError):
        return (pool_type, None)


def should_notify_from_card_with_status(card_with_status: CardWithStatus) -> bool:
    """從帶狀態的卡片對象判斷是否需要發送通知"""
    return should_notify(card_with_status)


async def notify_draw(
    user: discord.User,
    embed: discord.Embed,
    card: Union[Card, CardWithStatus],
    view: Optional[discord.ui.View] = None,
    is_new_card: bool = False,
) -> bool:
    """透過 Webhook 發送抽卡通知"""
    global _session
    if not _enabled:
        logger.debug("[GACHA_NOTIFIER] 通知功能未啟用。")
        return False

    if not _webhook_url:
        logger.warning("[GACHA_NOTIFIER] 未配置 Webhook URL，無法發送通知")
        return False

    if _session is None or _session.closed:
        logger.warning(
            "[GACHA_NOTIFIER] aiohttp session 未初始化或已關閉，將嘗試重新初始化。"
        )
        initialize_notifier()
        if _session is None:
            logger.error("[GACHA_NOTIFIER] aiohttp session 初始化失敗，無法發送通知。")
            return False

    from config.app_config import get_config

    pool_type_prefixes_config = get_config("gacha_core_settings.pool_type_prefixes")
    pool_type_prefixes = (
        dict(pool_type_prefixes_config) if pool_type_prefixes_config else {}
    )

    if isinstance(card, CardWithStatus):
        actual_card = card.card
        is_new_card_status = card.status.is_new_card
        pool_type = card.card.pool_type if card.card.pool_type is not None else "main"
        pool_prefix = pool_type_prefixes.get(pool_type, "")
    else:
        actual_card = card
        is_new_card_status = is_new_card
        pool_type = getattr(actual_card, "pool_type", "main")
        if pool_type is None:
            pool_type = "main"
        pool_prefix = pool_type_prefixes.get(pool_type, "")

    card_status_text = "新卡" if is_new_card_status else "重複"

    avatar_url = user.default_avatar.url
    if hasattr(user, "display_avatar") and user.display_avatar:
        avatar_url = user.display_avatar.url
    elif hasattr(user, "avatar") and user.avatar:
        avatar_url = user.avatar.url

    notification_embed = embed.copy()
    notification_embed.set_author(name="【媽的歐狗通知】", icon_url=avatar_url)
    notification_embed.title = (
        f"{user.display_name} 抽到了{pool_prefix}{card_status_text}!"
    )

    try:
        webhook_payload = {"embeds": [notification_embed.to_dict()]}
        if view:
            logger.warning(
                "[GACHA_NOTIFIER] Webhook 通知不支持直接發送 View 組件。View 將被忽略。"
            )

        async with _session.post(_webhook_url, json=webhook_payload) as response:
            if 200 <= response.status < 300:
                logger.info(
                    "[GACHA_NOTIFIER] Webhook 通知已成功發送至 %s", _webhook_url
                )
                return True
            else:
                logger.error(
                    "[GACHA_NOTIFIER] Webhook 通知失敗，狀態碼: %s, 回應: %s",
                    response.status,
                    await response.text(),
                )
                return False
    except aiohttp.ClientError as e:
        logger.error(
            "[GACHA_NOTIFIER] Webhook 請求時發生 aiohttp 客戶端錯誤: %s",
            e,
            exc_info=True,
        )
        return False
    except Exception as e:
        logger.error(
            "[GACHA_NOTIFIER] Webhook 發送通知時發生未知錯誤: %s", e, exc_info=True
        )
        return False
