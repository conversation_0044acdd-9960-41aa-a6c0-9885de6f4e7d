"""
PlayerCardManagementService - 玩家卡牌管理服務

負責玩家卡牌的技能槽位管理、被動技能裝備/卸下等操作。
支持卡牌实例系统的分离式管理。
"""

import logging
from typing import Any, Dict, List, Optional

# 导入异常类 - 使用gacha系统的异常
from gacha.exceptions import (
    CardNotFoundError,
    DatabaseOperationError,
    InvalidSlotError,
    PermissionDeniedError,
    SkillNotFoundError,
)
from rpg_system.battle_system.services import attribute_calculator
from rpg_system.config.loader import get_config_loader
from rpg_system.repositories import (
    global_skill_repository,
    player_collection_repository,
)
from rpg_system.repositories.player_collection_repository import is_special_card
from rpg_system.services import card_instance_service

logger = logging.getLogger(__name__)


# RPG卡牌管理专用异常（繼承自gacha異常）
# InvalidSlotError 已從 gacha.exceptions 導入，無需重複定義


# 定义槽位类型常量
ACTIVE_SKILL_SLOTS = 3  # 主动技能槽位数量
PASSIVE_SKILL_SLOTS = 5  # 被动技能槽位数量


# ==================== 錯誤的裝備/卸下系統已移除 ====================
# 根據設計文檔，RPG系統只有技能轉移功能，沒有單獨的裝備/卸下系統
# 技能修改只能通過以下兩種方式觸發：
# 1. 戰鬥勝利後獲得經驗（自動分離）
# 2. 卡牌間技能轉移（手動操作）

# 移除錯誤的卸下主動技能方法

# 移除錯誤的被動技能裝備/卸下方法


async def get_card_details_by_card_id(
    user_id: int, card_id: int
) -> Optional[Dict[str, Any]]:
    """
    根據card_id獲取卡牌詳細信息（使用新的Repository方法）

    Args:
        user_id: 用戶ID
        card_id: 卡牌ID

    Returns:
        卡牌詳細信息字典，如果找不到則返回None

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 使用統一的CardInstanceService獲取卡牌詳細信息（包含配置）
        return await card_instance_service.get_card_details_with_config(
            user_id, card_id
        )

    except Exception as e:
        logger.error("獲取卡牌詳細信息失敗: %s", e, exc_info=True)
        raise DatabaseOperationError(
            f"獲取卡牌詳細信息失敗: {str(e)}", original_exception=e
        ) from e


# ==================== 新的技能修改方法（支持自動分離） ====================


async def get_card_info_unified(user_id: int, card_id: int) -> Optional[Dict[str, Any]]:
    """
    統一的卡牌信息獲取方法（自動判斷類型）

    Args:
        user_id: 用戶ID
        card_id: 卡牌ID

    Returns:
        卡牌信息字典
    """
    try:
        return await card_instance_service.get_card_info(user_id, card_id)
    except Exception as e:
        logger.error(
            "獲取卡牌信息失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"獲取卡牌信息失敗: {str(e)}") from e


async def get_all_user_cards_unified(user_id: int) -> List[Dict[str, Any]]:
    """
    獲取用戶所有卡牌（使用UNION ALL查詢）

    Args:
        user_id: 用戶ID

    Returns:
        所有卡牌列表
    """
    try:
        return await card_instance_service.get_user_all_cards(user_id)
    except Exception as e:
        logger.error("獲取用戶所有卡牌失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶所有卡牌失敗: {str(e)}") from e


async def get_card_details_for_display(
    user_id: int, collection_id: int
) -> Optional[Dict[str, Any]]:
    """
    獲取卡牌詳細信息用於展示

    Args:
        user_id: 玩家ID
        collection_id: 卡牌收藏ID

    Returns:
        卡牌詳細信息字典，如果失敗則返回None
    """
    try:
        # 1. 獲取玩家卡牌數據
        card_id = collection_id
        player_card = await card_instance_service.get_card_info(user_id, card_id)

        if not player_card:
            logger.warning("找不到卡牌: %s", collection_id)
            return None

        if player_card.get("user_id") != user_id:
            logger.warning("玩家 %s 無權限查看卡牌 %s", user_id, collection_id)
            return None

        # 2. 獲取卡牌配置
        card_id_val = player_card.get("card_id")
        if not card_id_val:
            logger.warning("卡牌數據中缺少 card_id: %s", player_card)
            return None
        config_loader = get_config_loader()
        card_config = await config_loader.get_card_config(card_id_val)
        if not card_config:
            logger.warning("找不到卡牌配置: %s", card_id_val)
            return None

        # 3. 計算卡牌屬性
        calculated_stats = {}
        try:
            calculated_stats = await attribute_calculator.calculate_attributes(
                combatant_definition_id=card_id_val,
                combatant_type="CARD",
                rpg_level=player_card.get("rpg_level", 1),
                star_level=player_card.get("star_level", 0),
            )
        except Exception as calc_error:
            logger.warning("計算卡牌屬性失敗: %s", calc_error)
            calculated_stats = {}

        # 4. 構建詳細信息
        card_details = {
            "collection_id": collection_id,
            "card_id": card_id_val,
            "name": card_config.get("name", "未知卡牌"),
            "rarity": card_config.get("rarity", "N"),
            "rpg_level": player_card.get("rpg_level", 1),
            "rpg_xp": player_card.get("rpg_xp", 0),
            "star_level": player_card.get("star_level", 0),
            "is_favorite": player_card.get("is_favorite", False),
            # 基礎屬性（從配置）
            "base_stats": card_config.get("base_stats", {}),
            "growth_per_rpg_level": card_config.get("growth_per_rpg_level", {}),
            # 計算後的屬性
            "calculated_stats": calculated_stats,
            # 技能信息
            "primary_attack_skill_id": card_config.get("primary_attack_skill_id"),
            "innate_passive_skill_id": card_config.get("innate_passive_skill_id"),
            "equipped_active_skills": await _format_active_skills(
                player_card.get("equipped_active_skill_ids"), user_id
            ),
            "equipped_passive_skills": _format_passive_skills(
                player_card.get("equipped_common_passives")
            ),
            # 槽位信息
            "max_active_skill_slots": card_config.get("max_active_skill_slots", 3),
            "passive_skill_slots": card_config.get("passive_skill_slots", 0),
            # 時間信息
            "first_acquired": player_card.get("first_acquired"),
            "last_acquired": player_card.get("last_acquired"),
        }

        return card_details

    except Exception as e:
        logger.error("獲取卡牌詳細信息時發生錯誤: %s", e, exc_info=True)
        return None


async def _format_active_skills(
    equipped_active_skill_ids: Optional[List[str]], user_id: int
) -> List[Dict[str, Any]]:
    """
    格式化主動技能信息

    Args:
        equipped_active_skill_ids: 裝備的主動技能ID列表

    Returns:
        格式化的技能信息列表
    """
    if not equipped_active_skill_ids:
        return []

    config_loader = get_config_loader()
    formatted_skills = []
    for i, skill_id in enumerate(equipped_active_skill_ids):
        if skill_id:
            skill_config = config_loader.get_active_skill_config(skill_id)
            formatted_skills.append(
                {
                    "slot_index": i,
                    "skill_id": skill_id,
                    "name": skill_config["name"] if skill_config else "未知技能",
                    "description": (
                        _get_skill_description(skill_config) if skill_config else ""
                    ),
                    "level": await _get_skill_level(user_id, skill_id, "ACTIVE"),
                }
            )
        else:
            formatted_skills.append(
                {
                    "slot_index": i,
                    "skill_id": None,
                    "name": "空槽位",
                    "description": "",
                }
            )

    return formatted_skills


def _format_passive_skills(
    equipped_common_passives: Optional[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """
    格式化被動技能信息

    Args:
        equipped_common_passives: 裝備的被動技能字典

    Returns:
        格式化的技能信息列表
    """
    if not equipped_common_passives:
        return []

    config_loader = get_config_loader()
    formatted_skills = []
    for slot_key, passive_data in equipped_common_passives.items():
        if passive_data and isinstance(passive_data, dict):
            skill_id = passive_data.get("skill_id")
            level = passive_data.get("level", 1)

            if skill_id:
                skill_config = config_loader.get_passive_skill_config(skill_id)
                formatted_skills.append(
                    {
                        "slot_key": slot_key,
                        "skill_id": skill_id,
                        "name": skill_config["name"] if skill_config else "未知技能",
                        "description": (
                            _get_skill_description(skill_config) if skill_config else ""
                        ),
                        "level": level,
                    }
                )

    return formatted_skills


def _get_skill_description(skill_config: Any) -> str:
    """
    獲取技能描述

    Args:
        skill_config: 技能配置對象

    Returns:
        技能描述字符串
    """
    if not skill_config:
        return ""

    # 主動技能和被動技能使用 description_template
    if hasattr(skill_config, "description_template"):
        return skill_config.description_template

    # 天賦技能使用 description_template_by_star_level
    if hasattr(skill_config, "description_template_by_star_level"):
        desc_by_star = skill_config.description_template_by_star_level
        if desc_by_star:
            # 返回最低星級的描述作為默認
            min_star_level = min(int(k) for k in desc_by_star.keys())
            return desc_by_star.get(str(min_star_level), "")

    return ""


async def _get_skill_level(user_id: int, skill_id: str, skill_type: str) -> int:
    """
    獲取玩家的技能等級

    Args:
        user_id: 玩家ID
        skill_id: 技能ID
        skill_type: 技能類型

    Returns:
        技能等級，如果未學習則返回1
    """
    try:
        skill_data = await global_skill_repository.get_skill_data(
            user_id, skill_id, skill_type
        )
        if skill_data:
            return skill_data.skill_level
        return 1  # 未學習的技能默認等級為1
    except Exception as e:
        logger.warning(
            "獲取技能等級失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
        )
        return 1


async def transfer_skill_between_cards(
    user_id: int,
    source_collection_id: int,
    source_slot: str,
    target_collection_id: int,
    target_slot: str,
) -> Dict[str, Any]:
    """
    卡牌間技能轉移（使用CardInstanceService的核心邏輯）

    Args:
        user_id: 玩家ID
        source_collection_id: 源卡牌收藏ID
        source_slot: 源槽位（主動技能：0,1,2 或 被動技能：slot_0,slot_1,slot_2）
        target_collection_id: 目標卡牌收藏ID
        target_slot: 目標槽位（必須與源槽位類型相同）

    Returns:
        Dict[str, Any]: 轉移結果

    Raises:
        CardNotFoundError: 卡牌不存在
        PermissionDeniedError: 權限不足
        InvalidSlotError: 無效槽位
        SkillNotFoundError: 源槽位沒有技能
        ValueError: 槽位類型不匹配
    """
    try:
        # 1. 解析槽位類型
        source_is_passive = source_slot.startswith("slot_")
        target_is_passive = target_slot.startswith("slot_")

        if source_is_passive != target_is_passive:
            raise ValueError(
                "源槽位和目標槽位類型必須相同（主動技能只能轉到主動槽，被動技能只能轉到被動槽）"
            )

        # 2. 獲取源卡牌信息
        source_card_info = await card_instance_service.get_card_info(
            user_id, source_collection_id
        )
        if not source_card_info:
            raise CardNotFoundError(f"找不到源卡牌: {source_collection_id}")

        # 3. 獲取目標卡牌信息
        target_card_info = await card_instance_service.get_card_info(
            user_id, target_collection_id
        )
        if not target_card_info:
            raise CardNotFoundError(f"找不到目標卡牌: {target_collection_id}")

        # 4. 提取要轉移的技能
        skill_to_transfer = _extract_skill_from_slot(source_card_info, source_slot)
        if not skill_to_transfer:
            raise SkillNotFoundError(f"源槽位 {source_slot} 沒有技能")

        # 5. 構建目標卡牌的新技能配置
        new_target_skills = {
            "active": target_card_info.get("equipped_active_skill_ids", []).copy(),
            "passive": target_card_info.get("equipped_common_passives", {}).copy(),
        }

        # 記錄被覆蓋的技能
        previous_target_skill = None
        if source_is_passive:
            previous_target_skill = new_target_skills["passive"].get(target_slot)
            new_target_skills["passive"][target_slot] = skill_to_transfer
        else:
            target_slot_index = int(target_slot)
            while len(new_target_skills["active"]) <= target_slot_index:
                new_target_skills["active"].append(None)
            previous_target_skill = new_target_skills["active"][target_slot_index]
            new_target_skills["active"][target_slot_index] = (
                skill_to_transfer.get("skill_id")
                if isinstance(skill_to_transfer, dict)
                else skill_to_transfer
            )

        # 6. 判斷目標卡牌類型並修改技能
        target_is_special = is_special_card(target_card_info["card_id"])
        if target_is_special:
            await player_collection_repository.update_special_card_skills(
                target_card_info["card_id"], new_target_skills
            )
            final_target_card_id = target_card_info["card_id"]
        else:
            final_target_card_id = (
                await player_collection_repository.separate_card_for_skills(
                    user_id, target_card_info["card_id"], new_target_skills
                )
            )

        # 7. 刪除源卡牌
        source_is_special = is_special_card(source_card_info["card_id"])
        if source_is_special:
            await player_collection_repository.sacrifice_special_card(
                source_card_info["card_id"]
            )
        else:
            await player_collection_repository.sacrifice_default_card(
                user_id, source_card_info["card_id"]
            )

        logger.info(
            "玩家 %s 將技能從卡牌 %s 轉移到卡牌 %s，源卡牌已刪除",
            user_id,
            source_collection_id,
            target_collection_id,
        )

        return {
            "source_collection_id": source_collection_id,
            "target_collection_id": final_target_card_id,  # 可能是新分離的特殊卡ID
            "transferred_skill": skill_to_transfer,
            "source_slot": source_slot,
            "target_slot": target_slot,
            "previous_target_skill": previous_target_skill,
            "source_card_deleted": True,
            "message": "成功將技能從源卡牌轉移到目標卡牌，源卡牌已刪除",
        }

    except (
        CardNotFoundError,
        PermissionDeniedError,
        InvalidSlotError,
        SkillNotFoundError,
        ValueError,
    ):
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error(
            "技能轉移失敗: user_id=%s, source=%s, target=%s, error=%s",
            user_id,
            source_collection_id,
            target_collection_id,
            e,
        )
        raise DatabaseOperationError(f"技能轉移失敗: {str(e)}") from e


def _extract_skill_from_slot(card_info: Dict[str, Any], slot: str) -> Optional[Any]:
    """從卡牌的指定槽位提取技能信息"""
    try:
        if slot.isdigit():
            # 主動技能槽位
            slot_index = int(slot)
            active_skills = card_info.get("equipped_active_skill_ids", [])
            if slot_index < len(active_skills) and active_skills[slot_index]:
                return {"skill_id": active_skills[slot_index]}
        else:
            # 被動技能槽位
            passive_skills = card_info.get("equipped_common_passives", {})
            if slot in passive_skills and passive_skills[slot]:
                return passive_skills[slot]
        return None
    except Exception:
        return None


# ==================== 移除所有錯誤的裝備/卸下方法 ====================
# 根據設計文檔，RPG系統只有技能轉移功能，沒有單獨的裝備/卸下系統

# ==================== 卡牌实例系统核心方法 ====================


async def modify_card_skills_with_separation(
    user_id: int,
    card_id: int,
    source_type: str,
    source_id: Optional[str],
    new_skills: Dict[str, Any],
) -> Dict[str, Any]:
    """
    修改卡牌技能 - 支持双表分离式实例系统

    Args:
        user_id: 用戶ID
        card_id: 卡牌ID
        source_type: "default" 或 "special"
        source_id: 如果是special，則為special_instance_name；如果是default，則為None
        new_skills: 新的技能配置 {'active': [...], 'passive': {...}}

    Returns:
        修改結果信息

    Raises:
        CardNotFoundError: 找不到源記錄
        PermissionDeniedError: 權限不足
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 驗證用戶權限 - 檢查用戶是否擁有該卡牌
        if source_type == "default":
            # 檢查默認記錄
            user_cards = await player_collection_repository.get_all_user_cards(user_id)
            default_cards = [
                card
                for card in user_cards
                if card.get("card_id") is not None
                and not is_special_card(card.get("card_id", 0))
                and card.get("card_id") == card_id
            ]
            if not default_cards or default_cards[0].get("quantity", 0) <= 0:
                raise CardNotFoundError(f"用戶 {user_id} 沒有卡牌 {card_id} 的默認記錄")
        else:
            # 檢查特殊記錄（双表方案）
            if not source_id:
                raise ValueError("特殊配置類型需要提供source_id")

            try:
                source_id_int = int(source_id)
            except (ValueError, TypeError):
                raise ValueError(f"無效的 source_id: {source_id}") from None

            special_card = await player_collection_repository.get_special_card(
                source_id_int
            )
            if not special_card or special_card.get("user_id") != user_id:
                raise CardNotFoundError(
                    f"找不到特殊記錄: user_id={user_id}, card_id={card_id}, instance={source_id}"
                )

        # 執行技能修改
        if source_type == "default":
            result_collection_id = (
                await player_collection_repository.separate_card_for_skills(
                    user_id, card_id, new_skills
                )
            )
        else:
            if not source_id:
                raise ValueError("特殊配置類型需要提供source_id")
            source_id_int = int(source_id)
            await player_collection_repository.update_special_card_skills(
                source_id_int, new_skills
            )
            result_collection_id = source_id_int

        # 獲取修改後的記錄信息
        modified_card = await player_collection_repository.get_special_card(
            result_collection_id
        )

        logger.info(
            "用戶 %s 成功修改卡牌 %s 的技能配置，類型: %s",
            user_id,
            card_id,
            source_type,
        )

        return {
            "card_id": card_id,
            "collection_id": result_collection_id,
            "source_type": source_type,
            "source_id": source_id,
            "new_skills": new_skills,
            "is_new_instance": source_type == "default",  # 從默認分離會創建新實例
            "display_type": "配置",  # Hardcoded for now
            "special_instance_name": modified_card.get("instance_name")
            if modified_card
            else None,
            "message": f"成功修改技能配置，{'創建了新的特殊實例' if source_type == 'default' else '更新了特殊配置'}",
        }

    except (CardNotFoundError, PermissionDeniedError, ValueError):
        raise
    except Exception as e:
        logger.error(
            "修改卡牌技能配置失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"修改卡牌技能配置失敗: {str(e)}") from e


async def get_card_instances(user_id: int, card_id: int) -> List[Dict[str, Any]]:
    """
    獲取指定卡牌的所有實例（默認+特殊）

    Args:
        user_id: 用戶ID
        card_id: 卡牌ID

    Returns:
        卡牌實例列表，包含詳細的技能信息
    """
    try:
        all_cards = await player_collection_repository.get_all_user_cards(user_id)
        instances = [card for card in all_cards if card.get("card_id") == card_id]

        # 為每個實例添加格式化的技能信息
        for instance in instances:
            instance["formatted_active_skills"] = await _format_active_skills(
                instance.get("equipped_active_skill_ids"), user_id
            )
            instance["formatted_passive_skills"] = _format_passive_skills(
                instance.get("equipped_common_passives")
            )

            # 添加實例描述
            card_id_val = instance.get("card_id")
            if card_id_val is not None and is_special_card(card_id_val):
                instance["instance_description"] = (
                    f"特殊配置 - {instance.get('instance_name', 'Unknown')}"
                )
            else:
                instance["instance_description"] = (
                    f"默認配置 ({instance.get('quantity', 1)} 張)"
                )

        return instances

    except Exception as e:
        logger.error(
            "獲取卡牌實例失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"獲取卡牌實例失敗: {str(e)}") from e


async def transfer_skill_between_instances(
    user_id: int,
    source_collection_id: int,
    source_slot: str,
    target_card_id: int,
    target_slot: str,
) -> Dict[str, Any]:
    """
    在卡牌實例間轉移技能（簡化版技能轉移）

    Args:
        user_id: 用戶ID
        source_collection_id: 源卡牌收藏ID (現在是 card_id)
        source_slot: 源槽位
        target_card_id: 目標卡牌ID
        target_slot: 目標槽位

    Returns:
        轉移結果

    Raises:
        CardNotFoundError: 找不到卡牌
        SkillNotFoundError: 源槽位沒有技能
        InvalidSlotError: 槽位類型不匹配
    """
    try:
        # 1. 獲取源卡牌的技能
        source_card_id = source_collection_id
        source_card = await card_instance_service.get_card_info(user_id, source_card_id)
        if not source_card:
            raise CardNotFoundError(f"找不到源卡牌或無權限: {source_card_id}")

        # 2. 解析槽位並獲取技能
        source_skill = _extract_skill_from_slot(source_card, source_slot)
        if not source_skill:
            raise SkillNotFoundError(f"源槽位 {source_slot} 沒有技能")

        # 3. 驗證槽位類型匹配
        source_slot_type = _get_slot_type(source_slot)
        target_slot_type = _get_slot_type(target_slot)
        if source_slot_type != target_slot_type:
            raise InvalidSlotError(
                f"槽位類型不匹配: {source_slot_type} vs {target_slot_type}"
            )

        # 4. 為目標卡牌創建新的技能配置
        new_skills = {"active": [], "passive": {}}

        if source_slot_type == "active":
            new_skills["active"] = [None] * 3  # 默認3個主動技能槽
            target_slot_index = int(target_slot)
            new_skills["active"][target_slot_index] = source_skill["skill_id"]
        else:
            new_skills["passive"][target_slot] = source_skill

        # 5. 為目標卡牌創建特殊配置（從默認分離）
        result_collection_id = (
            await player_collection_repository.separate_card_for_skills(
                user_id, target_card_id, new_skills
            )
        )

        # 6. 刪除源卡牌（需要判断是否为特殊卡）
        source_is_special = is_special_card(source_card_id)
        if source_is_special:
            await player_collection_repository.sacrifice_special_card(source_card_id)
        else:
            await player_collection_repository.sacrifice_default_card(
                user_id, source_card_id
            )

        logger.info(
            "用戶 %s 成功轉移技能 %s 從卡牌 %s 到卡牌 %s",
            user_id,
            source_skill.get("skill_id", "Unknown"),
            source_collection_id,
            target_card_id,
        )

        return {
            "source_collection_id": source_collection_id,
            "target_card_id": target_card_id,
            "target_collection_id": result_collection_id,
            "transferred_skill": source_skill,
            "source_slot": source_slot,
            "target_slot": target_slot,
            "message": f"成功轉移技能 {source_skill.get('skill_id', 'Unknown')} 到目標卡牌",
        }

    except (CardNotFoundError, SkillNotFoundError, InvalidSlotError):
        raise
    except Exception as e:
        logger.error("技能轉移失敗: %s", e)
        raise DatabaseOperationError(f"技能轉移失敗: {str(e)}") from e


def _get_slot_type(slot: str) -> str:
    """獲取槽位類型"""
    return "active" if slot.isdigit() else "passive"
