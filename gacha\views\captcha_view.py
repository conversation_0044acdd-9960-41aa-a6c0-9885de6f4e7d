"""
處理驗證碼輸入的 View 和 Modal
"""

from typing import Awaitable, Callable, Optional

import discord

from utils.base_modal import BaseModal
from utils.base_view import BaseView


class CaptchaModal(BaseModal):
    """
    一個 Modal，用於讓使用者輸入他們在圖片中看到的驗證碼。
    """

    captcha_input = discord.ui.TextInput(
        label="請輸入圖片中的驗證碼",
        placeholder="不區分大小寫",
        min_length=4,
        max_length=4,
        required=True,
    )

    def __init__(
        self,
        bot,
        *,
        title: str = "驗證碼驗證",
        correct_answer: str,
        on_correct: Callable[[discord.Interaction], Awaitable[bool]],
        on_incorrect: Callable[[discord.Interaction], Awaitable[bool]],
        view: "CaptchaView",
    ):
        super().__init__(bot=bot, title=title)
        self.correct_answer = correct_answer.lower()
        self.on_correct = on_correct
        self.on_incorrect = on_incorrect
        self.view = view

    async def on_submit(self, interaction: discord.Interaction):
        user_input = self.captcha_input.value.lower()
        if user_input == self.correct_answer:
            should_stop = await self.on_correct(interaction)
            if should_stop:
                self.view.stop()
        else:
            should_stop = await self.on_incorrect(interaction)
            if should_stop:
                self.view.stop()


class CaptchaView(BaseView):
    """
    一個 View，包含一個觸發 CaptchaModal 的按鈕和一個更換驗證碼的按鈕。
    """

    def __init__(
        self,
        bot,
        user_id: int,
        *,
        correct_answer: str,
        on_correct: Callable[[discord.Interaction], Awaitable[bool]],
        on_incorrect: Callable[[discord.Interaction], Awaitable[bool]],
        on_timeout: Optional[Callable[[], Awaitable[None]]] = None,
        on_refresh: Optional[
            Callable[
                [discord.Interaction, "CaptchaView"],
                Awaitable[Optional[discord.File]],
            ]
        ] = None,
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.correct_answer = correct_answer
        self.on_correct = on_correct
        self.on_incorrect = on_incorrect
        self.on_timeout_callback = on_timeout
        self.on_refresh_callback = on_refresh
        self.message: Optional[discord.InteractionMessage] = None

        # 如果沒有提供 on_refresh 回調，則不顯示更換按鈕
        if self.on_refresh_callback is None:
            self.remove_item(self.refresh_captcha)

    async def on_timeout(self) -> None:
        """當 View 超時後執行的動作。"""
        if self.on_timeout_callback:
            await self.on_timeout_callback()

        # 禁用所有按鈕並更新訊息
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True

        if self.message:
            try:
                await self.message.edit(view=self)
            except discord.NotFound:
                pass  # 訊息可能已被使用者刪除
        self.stop()

    @discord.ui.button(
        label="輸入驗證碼", style=discord.ButtonStyle.primary, custom_id="captcha:enter"
    )
    async def enter_captcha(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """當使用者點擊按鈕時，彈出驗證碼輸入框。"""
        modal = CaptchaModal(
            bot=self.bot,
            correct_answer=self.correct_answer,
            on_correct=self.on_correct,
            on_incorrect=self.on_incorrect,
            view=self,
        )
        await interaction.response.send_modal(modal)

    @discord.ui.button(
        label="換一張",
        style=discord.ButtonStyle.secondary,
        custom_id="captcha:refresh",
    )
    async def refresh_captcha(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """當使用者點擊按鈕時，更換一張新的驗證碼。"""
        if self.on_refresh_callback:
            # 禁用按鈕以防止連點，並立即回應以更新 UI
            button.disabled = True
            await interaction.response.edit_message(view=self)

            # 呼叫 service 進行後端處理並獲取新圖片
            new_file = await self.on_refresh_callback(interaction, self)

            # 重新啟用按鈕
            button.disabled = False

            # 由 View 全權負責更新訊息
            if new_file:
                await interaction.edit_original_response(
                    attachments=[new_file], view=self
                )
            else:
                # 如果 service 處理失敗，也要恢復按鈕狀態
                await interaction.edit_original_response(view=self)
