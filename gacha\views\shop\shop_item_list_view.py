import math
from decimal import Decimal
from typing import TYPE_CHECKING, Optional

import discord
from discord import ButtonStyle, SelectOption
from discord.ui import Button, Select

from gacha.exceptions import BusinessError
from gacha.models.shop_models import ShopItemDefinition
from gacha.services import user_service
from gacha.views.collection.collection_view.base_pagination import (
    BasePaginationView,
)
from gacha.views.embeds.shop.shop_item_list_embed_builder import (
    ShopItemListEmbedBuilder,
)
from gacha.views.modals.ticket_exchange_modal import TicketExchangeModal
from utils.logger import logger

if TYPE_CHECKING:
    pass


class ShopItemListView(BasePaginationView):
    EXCHANGE_ITEM_CUSTOM_ID = "exchange_item"
    SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID = "shop_item_select_dropdown"

    def __init__(
        self,
        original_interaction: discord.Interaction,
        items: list[ShopItemDefinition],
        oil_ticket_balance: Optional[Decimal] = None,
        category_name: str = "商店商品列表",
        items_per_page: int = 4,
        timeout: int = 180,
    ):
        self.original_interaction = original_interaction
        self.all_items = items
        self.oil_ticket_balance = oil_ticket_balance
        self.category_name = category_name
        self.items_per_page = items_per_page
        self.total_items = len(items)
        self.embed_builder = ShopItemListEmbedBuilder(items_per_page=items_per_page)
        self.selected_item_id_for_exchange: Optional[str] = None
        if self.items_per_page <= 0 or self.total_items == 0:
            total_pages = 1
        else:
            total_pages = math.ceil(self.total_items / self.items_per_page)
        super().__init__(
            bot=original_interaction.client,  # type: ignore
            user_id=original_interaction.user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=float(timeout),
        )
        self._add_custom_shop_list_items()

    def _add_custom_shop_list_items(self):
        """Adds custom items like the item selection dropdown and exchange button."""
        self._add_item_selection_dropdown()
        exchange_btn = Button(
            label="🛒 進行兌換",
            style=ButtonStyle.success,
            custom_id=self.EXCHANGE_ITEM_CUSTOM_ID,
            row=2,
        )
        exchange_btn.callback = self.exchange_button_callback
        self.add_item(exchange_btn)

    def _add_item_selection_dropdown(self):
        """新增一個下拉式選單供用戶選擇要兌換的商品。Placed on row 1."""
        self.remove_item_by_custom_id(self.SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID)
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        current_page_items = self.all_items[start_index:end_index]
        if not current_page_items:
            return
        options = [
            SelectOption(
                label=item.display_name[:100],
                value=item.id,
                description=(item.description or "選擇此商品")[:100],
            )
            for item in current_page_items
        ]
        if len(options) > 25:
            logger.warning(
                "Shop item list dropdown options truncated for user %s, page %s",
                self.user_id,
                self.current_page,
            )
            options = options[:25]
        if not options:
            return
        select = Select(
            placeholder="選擇要兌換的商品...",
            options=options,
            custom_id=self.SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID,
            min_values=1,
            max_values=1,
            row=1,
        )
        select.callback = self.select_item_callback
        self.add_item(select)

    def remove_item_by_custom_id(self, custom_id: str):
        """Removes an item from the view based on its custom_id."""
        item_to_remove = next(
            (
                child
                for child in self.children
                if isinstance(child, (Button, Select)) and child.custom_id == custom_id
            ),
            None,
        )
        if item_to_remove:
            self.remove_item(item_to_remove)

    async def select_item_callback(self, interaction: discord.Interaction):
        """商品選擇回調

        注意：權限檢查已移至 BasePaginationView.interaction_check() 方法中
        """
        await interaction.response.defer(ephemeral=True)
        # 使用 cast 來輔助類型檢查
        if not interaction.data or not isinstance(interaction.data, dict):
            logger.warning("Interaction data is not a valid dictionary.")
            return

        values = interaction.data.get("values")
        if not values or not isinstance(values, list):
            logger.warning(
                "Could not find 'values' in interaction data for select callback."
            )
            return

        self.selected_item_id_for_exchange = values[0]
        logger.info(
            "User %s selected shop item ID: %s for exchange.",
            self.user_id,
            self.selected_item_id_for_exchange,
        )

    async def exchange_button_callback(self, interaction: discord.Interaction):
        """兌換按鈕回調

        注意：權限檢查已移至 BasePaginationView.interaction_check() 方法中
        """
        if not self.selected_item_id_for_exchange:
            raise BusinessError("請先從下拉選單中選擇一個商品進行兌換。")

        selected_item_def = next(
            (
                item
                for item in self.all_items
                if item.id == self.selected_item_id_for_exchange
            ),
            None,
        )
        if not selected_item_def:
            raise BusinessError("選擇的商品無效或已不存在，請刷新商店或重新選擇。")

        # 在兌換前，即時獲取最新的油票餘額
        current_balance = await user_service.get_oil_ticket_balance(
            self.user_id, create_if_missing=True
        )
        if current_balance < selected_item_def.cost_oil_tickets:
            raise BusinessError(
                f"您的油票餘額不足以兌換此商品（需要 {selected_item_def.cost_oil_tickets} 油票，您目前有 {current_balance} 張）。"
            )

        if not self.message:
            raise BusinessError("無法找到原始訊息，無法開啟兌換視窗。")
        # 直接使用模組級的shop_service，不再依賴cog的shop_service實例
        exchange_modal = TicketExchangeModal(
            bot=self.bot,
            shop_item_id=selected_item_def.id,
            original_message_to_edit=self.message,
        )
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        await interaction.response.send_modal(exchange_modal)

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed。"""
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        current_page_items_data = self.all_items[start_index:end_index]
        return self.embed_builder.build_embed(
            items=current_page_items_data,
            current_page=self.current_page,
            total_items=self.total_items,
            oil_ticket_balance_value=self.oil_ticket_balance
            if self.oil_ticket_balance is not None
            else Decimal("0"),
            category_name=self.category_name,
        )

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新視圖到指定頁面，並編輯原始消息。"""
        self.current_page = page
        self._add_item_selection_dropdown()
        embed = await self.get_current_page_embed()
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        await interaction.response.edit_message(embed=embed, view=self)
