from typing import List, Optional, Union

import discord
from discord.ext import commands

from config.app_config import (
    get_default_rarity_emojis,
    get_pool_type_names,
    get_rarity_display_codes,
    get_settings,
)
from gacha.exceptions import BusinessError
from gacha.models.filters import CollectionFilters, FavoriteStatusConstants
from gacha.views.collection.collection_view.card_view import CollectionView


class SortOrderSelect(discord.ui.Select):
    """排序順序下拉選單"""

    def __init__(self, current_order: str):
        from gacha.config.sorting_config import SortingConfig

        options = []
        for sort_option in SortingConfig.SORT_ORDER_OPTIONS:
            select_option = sort_option.to_select_option()
            # 添加表情符號
            if sort_option.value == "asc":
                select_option.emoji = "🔼"
            elif sort_option.value == "desc":
                select_option.emoji = "🔽"
            options.append(select_option)
        super().__init__(
            placeholder="選擇排序順序...",
            min_values=1,
            max_values=1,
            options=options,
            row=0,
        )
        self.view: "FilterManagementView"
        for option in self.options:
            if option.value == current_order:
                option.default = True

    async def callback(self, interaction: discord.Interaction):
        if isinstance(self.view, FilterManagementView):
            self.view.filters.sort_order = self.values[0]
        await interaction.response.defer()


class SortBySelect(discord.ui.Select):
    """排序方式下拉選單"""

    def __init__(self, current_sort_by: str, use_encyclopedia: bool = False):
        from gacha.config.sorting_config import SortingConfig

        sort_options = (
            SortingConfig.ENCYCLOPEDIA_SORT_OPTIONS
            if use_encyclopedia
            else SortingConfig.SORT_OPTIONS
        )
        options = [sort_option.to_select_option() for sort_option in sort_options]
        super().__init__(
            placeholder="選擇排序方式...",
            min_values=1,
            max_values=1,
            options=options,
            row=1,
        )
        self.view: "FilterManagementView"
        for option in self.options:
            if option.value == current_sort_by:
                option.default = True

    async def callback(self, interaction: discord.Interaction):
        if isinstance(self.view, FilterManagementView):
            self.view.filters.sort_by = self.values[0]
        await interaction.response.defer()


class NameFilterModal(discord.ui.Modal, title="依名稱篩選"):
    """名稱篩選 Modal"""

    name_input = discord.ui.TextInput(
        label="卡片名稱",
        placeholder="輸入要篩選的卡片名稱...",
        required=False,
    )

    def __init__(self, view: "FilterManagementView"):
        super().__init__()
        self.view = view
        self.name_input.default = self.view.filters.card_name

    async def on_submit(self, interaction: discord.Interaction):
        card_name = self.name_input.value.strip() if self.name_input.value else None
        validation_config = get_settings().ui_settings.form_validation
        max_length = validation_config.get("max_card_name_length", 100)

        if card_name and len(card_name) > max_length:
            raise BusinessError(f"❌ 卡片名稱過長，請限制在{max_length}字符內")

        self.view.filters.card_name = card_name
        self.view.update_name_button_label()
        await interaction.response.edit_message(view=self.view)


class SeriesFilterModal(discord.ui.Modal, title="依系列篩選"):
    """系列篩選 Modal"""

    series_input = discord.ui.TextInput(
        label="系列名稱",
        placeholder="輸入要篩選的系列名稱...",
        required=False,
    )

    def __init__(self, view: "FilterManagementView"):
        super().__init__()
        self.view = view
        self.series_input.default = self.view.filters.series

    async def on_submit(self, interaction: discord.Interaction):
        series = self.series_input.value.strip() if self.series_input.value else None
        if series and len(series) > 100:
            raise BusinessError("❌ 系列名稱過長，請限制在100字符內")

        self.view.filters.series = series
        self.view.update_series_button_label()
        await interaction.response.edit_message(view=self.view)


class RaritySelect(discord.ui.Select):
    """稀有度複選下拉選單"""

    def __init__(self, current_rarities: List[int]):
        rarity_display_codes = get_rarity_display_codes()
        rarity_emojis = get_default_rarity_emojis()

        options = []
        for rarity_id, name in rarity_display_codes.items():
            # 獲取對應的稀有度 emoji
            emoji = rarity_emojis.get(rarity_id, "")
            options.append(
                discord.SelectOption(
                    label=name, value=str(rarity_id), emoji=emoji if emoji else None
                )
            )

        for option in options:
            if int(option.value) in current_rarities:
                option.default = True

        super().__init__(
            placeholder="選擇要篩選的稀有度...",
            min_values=0,
            max_values=len(options),
            options=options,
            row=0,
        )
        self.view: "FilterManagementView"

    async def callback(self, interaction: discord.Interaction):
        if isinstance(self.view, FilterManagementView):
            self.view.filters.rarity_in = [int(v) for v in self.values]
            await self.view.back_to_main_filter_view(interaction)


class PoolSelect(discord.ui.Select):
    """卡池複選下拉選單"""

    def __init__(self, pools: dict, current_pools: Optional[List[str]]):
        options = [
            discord.SelectOption(label=name, value=key) for key, name in pools.items()
        ]
        # 設置已選中的選項
        if current_pools:
            for option in options:
                if option.value in current_pools:
                    option.default = True

        super().__init__(
            placeholder="選擇要篩選的卡池...",
            min_values=0,
            max_values=len(options),  # 支持複選
            options=options,
            row=0,
        )
        self.view: "FilterManagementView"

    async def callback(self, interaction: discord.Interaction):
        if isinstance(self.view, FilterManagementView):
            # 使用 pool_type_in 字段支持複選
            self.view.filters.pool_type_in = self.values if self.values else []
            # 清空舊的 pool_type 字段以避免衝突
            self.view.filters.pool_type = None
            await self.view.back_to_main_filter_view(interaction)


class FilterManagementView(discord.ui.View):
    """篩選管理器視圖"""

    def __init__(
        self,
        bot: Union[commands.Bot, commands.AutoShardedBot],
        user: Union[discord.User, discord.Member],
        previous_view: "CollectionView",
        filters: CollectionFilters,
    ):
        """初始化篩選管理器視圖"""
        super().__init__(timeout=600)
        self.bot = bot
        self.user = user
        self.previous_view = previous_view
        self.filters = filters
        self.message = None

        self._add_initial_components()

    def _add_initial_components(self):
        """添加初始組件"""
        self.add_item(SortOrderSelect(self.filters.sort_order))
        self.add_item(SortBySelect(self.filters.sort_by))

        self.name_button = discord.ui.Button(
            label="名稱", style=discord.ButtonStyle.secondary, row=2
        )
        self.name_button.callback = self.show_name_modal
        self.add_item(self.name_button)
        self.update_name_button_label()

        self.series_button = discord.ui.Button(
            label="系列", style=discord.ButtonStyle.secondary, row=2
        )
        self.series_button.callback = self.show_series_modal
        self.add_item(self.series_button)
        self.update_series_button_label()

        self.rarity_button = discord.ui.Button(
            label="稀有度", style=discord.ButtonStyle.secondary, row=2
        )
        self.rarity_button.callback = self.show_rarity_select
        self.add_item(self.rarity_button)
        self.update_rarity_button_label()

        self.pool_button = discord.ui.Button(
            label="卡池", style=discord.ButtonStyle.secondary, row=2
        )
        self.pool_button.callback = self.show_pool_select
        self.add_item(self.pool_button)
        self.update_pool_button_label()

        self.favorite_button = discord.ui.Button(
            label="最愛", style=discord.ButtonStyle.secondary, row=2
        )
        self.favorite_button.callback = self.toggle_favorite_filter
        self.add_item(self.favorite_button)
        self.update_favorite_button_label()

        apply_button = discord.ui.Button(
            label="套用篩選",
            style=discord.ButtonStyle.primary,
            row=4,
            emoji="✅",
        )
        apply_button.callback = self.back_to_collection_view
        self.add_item(apply_button)

    async def show_name_modal(self, interaction: discord.Interaction):
        """顯示名稱篩選 Modal"""
        await interaction.response.send_modal(NameFilterModal(self))

    async def show_series_modal(self, interaction: discord.Interaction):
        """顯示系列篩選 Modal"""
        await interaction.response.send_modal(SeriesFilterModal(self))

    async def show_rarity_select(self, interaction: discord.Interaction):
        """顯示稀有度篩選下拉選單"""
        self.clear_items()
        current_rarities = (
            self.filters.rarity_in if self.filters.rarity_in is not None else []
        )
        self.add_item(RaritySelect(current_rarities))

        back_button = discord.ui.Button(
            label="返回", style=discord.ButtonStyle.secondary, row=1
        )
        back_button.callback = self.back_to_main_filter_view
        self.add_item(back_button)

        await interaction.response.edit_message(view=self)

    def update_rarity_button_label(self):
        """更新稀有度按鈕的標籤"""
        if self.filters.rarity_in:
            rarity_display_codes = get_rarity_display_codes()
            rarity_names = [
                rarity_display_codes.get(r_id, str(r_id))
                for r_id in self.filters.rarity_in
            ]
            self.rarity_button.label = f"稀有度: {', '.join(rarity_names)}"
            self.rarity_button.style = discord.ButtonStyle.success
        else:
            self.rarity_button.label = "稀有度"
            self.rarity_button.style = discord.ButtonStyle.secondary

    async def show_pool_select(self, interaction: discord.Interaction):
        """顯示卡池篩選下拉選單"""
        self.clear_items()
        pools = get_pool_type_names()
        # 傳遞當前選中的卡池列表
        current_pools = self.filters.pool_type_in if self.filters.pool_type_in else []
        self.add_item(PoolSelect(pools, current_pools))

        back_button = discord.ui.Button(
            label="返回", style=discord.ButtonStyle.secondary, row=1
        )
        back_button.callback = self.back_to_main_filter_view
        self.add_item(back_button)

        await interaction.response.edit_message(view=self)

    def update_pool_button_label(self):
        """更新卡池按鈕的標籤"""
        # 檢查是否有選中的卡池（支持新的複選和舊的單選）
        has_pool_filter = (
            self.filters.pool_type_in and len(self.filters.pool_type_in) > 0
        ) or self.filters.pool_type is not None

        if has_pool_filter:
            # 複選後按鈕只顯示綠色，不顯示具體名稱
            self.pool_button.label = "卡池"
            self.pool_button.style = discord.ButtonStyle.success
        else:
            self.pool_button.label = "卡池"
            self.pool_button.style = discord.ButtonStyle.secondary

    def update_name_button_label(self):
        """更新名稱按鈕的標籤"""
        if self.filters.card_name:
            self.name_button.style = discord.ButtonStyle.success
        else:
            self.name_button.style = discord.ButtonStyle.secondary

    def update_series_button_label(self):
        """更新系列按鈕的標籤"""
        if self.filters.series:
            self.series_button.style = discord.ButtonStyle.success
        else:
            self.series_button.style = discord.ButtonStyle.secondary

    async def toggle_favorite_filter(self, interaction: discord.Interaction):
        """切換最愛篩選狀態"""
        if self.filters.favorite_status == FavoriteStatusConstants.ALL:
            self.filters.favorite_status = FavoriteStatusConstants.FAVORITE_ONLY
        elif self.filters.favorite_status == FavoriteStatusConstants.FAVORITE_ONLY:
            self.filters.favorite_status = FavoriteStatusConstants.NON_FAVORITE_ONLY
        else:
            self.filters.favorite_status = FavoriteStatusConstants.ALL

        self.update_favorite_button_label()
        await interaction.response.edit_message(view=self)

    def update_favorite_button_label(self):
        """更新最愛按鈕的標籤"""
        if self.filters.favorite_status == FavoriteStatusConstants.FAVORITE_ONLY:
            self.favorite_button.label = "僅最愛"
            self.favorite_button.style = discord.ButtonStyle.success
        elif self.filters.favorite_status == FavoriteStatusConstants.NON_FAVORITE_ONLY:
            self.favorite_button.label = "非最愛"
            self.favorite_button.style = discord.ButtonStyle.danger
        else:
            self.favorite_button.label = "全部"
            self.favorite_button.style = discord.ButtonStyle.secondary

    async def back_to_main_filter_view(self, interaction: discord.Interaction):
        """返回到主篩選視圖"""
        self.clear_items()
        self._add_initial_components()
        await interaction.response.edit_message(view=self)

    async def back_to_collection_view(self, interaction: discord.Interaction):
        """返回到收藏視圖"""
        await interaction.response.defer()

        # 檢查排序條件是否有變更
        sort_changed = (
            self.previous_view.sort_by != self.filters.sort_by
            or self.previous_view.sort_order != self.filters.sort_order
        )

        # 更新篩選條件
        self.previous_view.filters = self.filters

        # 同步排序參數到視圖屬性（重要：確保排序信息顯示正確）
        self.previous_view.sort_by = self.filters.sort_by
        self.previous_view.sort_order = self.filters.sort_order

        # 如果排序條件有變更，自動切換到統一排序模式（與指令邏輯一致）
        if sort_changed:
            self.previous_view.favorite_priority = False

        await self.previous_view._refresh_page_data_only(interaction)


__all__ = ["FilterManagementView"]
