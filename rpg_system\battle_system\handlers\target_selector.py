"""
目標選擇器 (TargetSelector)
根據技能定義和戰場情況選擇合適的目標
"""

from typing import Any, Dict, List

from utils.logger import logger

from ...formula_engine.evaluator import evaluate_formula
from ..models.battle import Battle
from ..models.combatant import Combatant


async def select_targets(
    caster: "Combatant",
    target_logic_detail: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    選擇目標

    Args:
        caster: 施法者
        target_logic_detail: 目標邏輯詳情（來自技能配置）
        battle_context: 戰鬥上下文

    Returns:
        符合條件的目標列表
    """
    try:
        # 1. 確定基礎目標池
        potential_targets = _get_base_target_pool(
            caster, target_logic_detail, battle_context
        )

        if not potential_targets:
            return []

        # 2. 應用目標條件過濾
        filtered_targets = await _apply_target_conditions(
            potential_targets,
            caster,
            target_logic_detail,
            battle_context,
        )

        if not filtered_targets:
            return []

        # 3. 應用排序邏輯
        sorted_targets = _apply_sorting(filtered_targets, target_logic_detail)

        # 4. 選擇數量
        target_count = await _calculate_target_count(
            caster, target_logic_detail, len(sorted_targets)
        )

        # 5. 應用選擇策略
        final_targets = _apply_selection_strategy(
            sorted_targets, target_count, target_logic_detail, battle_context
        )

        return final_targets

    except Exception as e:
        logger.error("目標選擇錯誤: %s", e)
        return []


def _get_base_target_pool(
    caster: "Combatant",
    target_logic_detail: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    獲取基礎目標池
    """
    base_pool = target_logic_detail.get("base_pool", "ENEMIES")

    if base_pool == "ENEMIES":
        return battle_context.get_all_alive_enemies_of(caster)
    elif base_pool == "ALLIES":
        return battle_context.get_all_alive_allies_of(caster)
    elif base_pool == "SELF":
        return [caster] if caster.is_alive() else []
    elif base_pool == "ALL_ALIVE":
        return battle_context.get_all_alive_combatants()
    else:
        logger.warning("未知的基礎目標池類型: %s", base_pool)
        return []


async def _apply_target_conditions(
    potential_targets: List["Combatant"],
    caster: "Combatant",
    target_logic_detail: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    應用目標條件過濾
    """
    conditions = target_logic_detail.get("conditions", [])
    if not conditions:
        return potential_targets

    filtered_targets = []

    for target_candidate in potential_targets:
        passes_all_conditions = True

        for condition in conditions:
            context_vars = _prepare_condition_context(
                caster, target_candidate, battle_context
            )
            condition_formula = condition.get("formula", "1")
            condition_met = await evaluate_formula(condition_formula, context_vars)

            if not condition_met:
                passes_all_conditions = False
                break

        if passes_all_conditions:
            filtered_targets.append(target_candidate)

    return filtered_targets


def _prepare_condition_context(
    caster: "Combatant", target: "Combatant", battle_context: "Battle"
) -> Dict[str, Any]:
    """
    準備條件評估的上下文變量
    """
    return {
        "caster_stat_hp": caster.current_hp,
        "caster_stat_max_hp": caster.max_hp,
        "caster_stat_mp": caster.current_mp,
        "caster_stat_max_mp": caster.max_mp,
        "caster_stat_patk": caster.current_stats.get("patk", 0),
        "caster_stat_pdef": caster.current_stats.get("pdef", 0),
        "caster_stat_matk": caster.current_stats.get("matk", 0),
        "caster_stat_mdef": caster.current_stats.get("mdef", 0),
        "caster_stat_spd": caster.current_stats.get("spd", 0),
        "caster_current_hp_percent": caster.current_hp / max(caster.max_hp, 1),
        "caster_missing_hp_percent": 1 - (caster.current_hp / max(caster.max_hp, 1)),
        "target_stat_hp": target.current_hp,
        "target_stat_max_hp": target.max_hp,
        "target_stat_mp": target.current_mp,
        "target_stat_max_mp": target.max_mp,
        "target_stat_patk": target.current_stats.get("patk", 0),
        "target_stat_pdef": target.current_stats.get("pdef", 0),
        "target_stat_matk": target.current_stats.get("matk", 0),
        "target_stat_mdef": target.current_stats.get("mdef", 0),
        "target_stat_spd": target.current_stats.get("spd", 0),
        "target_current_hp_percent": target.current_hp / max(target.max_hp, 1),
        "target_missing_hp_percent": 1 - (target.current_hp / max(target.max_hp, 1)),
        "target_is_boss": 1 if getattr(target, "is_boss", False) else 0,
        "current_turn": battle_context.current_turn,
    }


def _apply_sorting(
    targets: List["Combatant"], target_logic_detail: Dict[str, Any]
) -> List["Combatant"]:
    """
    應用排序邏輯
    """
    sort_by = target_logic_detail.get("sort_by")
    if not sort_by or not targets:
        return targets

    sort_order = target_logic_detail.get("sort_order", "ASC")
    reverse_sort = sort_order == "DESC"

    try:

        def get_sort_key(combatant: "Combatant") -> float:
            if sort_by in ["current_hp", "max_hp", "current_mp", "max_mp"]:
                return getattr(combatant, sort_by, 0)
            else:
                return combatant.current_stats.get(sort_by, 0)

        return sorted(targets, key=get_sort_key, reverse=reverse_sort)

    except Exception as e:
        logger.warning("排序失敗: %s，返回原始順序", e)
        return targets


async def _calculate_target_count(
    caster: "Combatant",
    target_logic_detail: Dict[str, Any],
    available_count: int,
) -> int:
    """
    計算要選擇的目標數量
    """
    count_logic = target_logic_detail.get("count_logic", "SINGLE")

    if count_logic == "ALL":
        return available_count
    elif count_logic == "FORMULA":
        count_formula = target_logic_detail.get("count_formula", "1")
        context_vars = {
            "caster_stat_patk": caster.current_stats.get("patk", 0),
            "caster_stat_matk": caster.current_stats.get("matk", 0),
            "available_targets": available_count,
        }
        calculated_count = await evaluate_formula(count_formula, context_vars)
        return max(0, int(calculated_count))
    else:
        return 1


def _apply_selection_strategy(
    targets: List["Combatant"],
    target_count: int,
    target_logic_detail: Dict[str, Any],
    battle_context: "Battle",
) -> List["Combatant"]:
    """
    應用選擇策略
    """
    if not targets or target_count <= 0:
        return []

    target_count = min(target_count, len(targets))
    selection_strategy = target_logic_detail.get("selection_strategy", "FIRST_N")

    if selection_strategy == "FIRST_N":
        return targets[:target_count]
    elif selection_strategy == "LAST_N":
        return targets[-target_count:]
    elif selection_strategy == "RANDOM_N":
        if hasattr(battle_context, "_rng"):
            return battle_context._rng.sample(targets, target_count)
        else:
            import random

            return random.sample(targets, target_count)
    else:
        logger.warning("未知的選擇策略: %s，使用 FIRST_N", selection_strategy)
        return targets[:target_count]
