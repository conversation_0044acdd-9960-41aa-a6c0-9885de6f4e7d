from __future__ import annotations

from datetime import datetime, timezone
from decimal import ROUND_HALF_UP, Decimal, InvalidOperation
from typing import Any, Dict, List, NamedTuple, Optional

import asyncpg

from database.postgresql.async_manager import get_pool
from gacha.exceptions import InvalidOperationError
from gacha.repositories.card import master_card_repository
from utils.logger import logger

# 常量定義，避免重複創建
DECIMAL_ZERO = Decimal("0.0")
DECIMAL_ONE = Decimal("1.0")
MIN_MODIFIER = Decimal("0.1")
MAX_MODIFIER = Decimal("10.0")
STOCK_INFLUENCE_WEIGHT = Decimal("1.0")
MIN_PRICE = Decimal("1.0")
GLOBAL_CATEGORY_KEY = "GLOBAL:__all__"


class PriceComponents(NamedTuple):
    """價格組件結構化數據"""

    actual_price: Decimal
    trend_symbol: str
    base_sell_price: Decimal
    supply_demand_modifier: Decimal
    previous_sd_modifier: Decimal
    final_stock_influence: Decimal
    stock_influence_weight: Decimal
    weighted_stock_influence: Decimal
    final_modifier_raw: Decimal
    clamped_modifier: Decimal
    min_modifier_config: Decimal
    max_modifier_config: Decimal


def _calculate_price_components_for_single_card(
    base_sell_price: Decimal,
    market_stats: Dict[str, Decimal],
    category_stock_influences: List[Dict[str, Any]],
) -> PriceComponents:
    """計算單張卡片的價格組件 - 優化版本"""
    if not base_sell_price or base_sell_price <= DECIMAL_ZERO:
        raise InvalidOperationError(
            f"無效的基礎售價: {base_sell_price}。價格必須為正數。"
        )

    # 使用預定義常量，避免重複創建 Decimal 對象
    supply_demand_modifier = market_stats.get("supply_demand_modifier", DECIMAL_ONE)
    previous_sd_modifier = market_stats.get(
        "previous_sd_modifier", supply_demand_modifier
    )

    # 使用字典映射替代 if-elif 鏈
    trend_symbol = (
        "↑"
        if supply_demand_modifier > previous_sd_modifier
        else ("↓" if supply_demand_modifier < previous_sd_modifier else "─")
    )

    # 優化股票影響計算 - 使用生成器表達式和 sum()
    current_time_utc = datetime.now(timezone.utc)

    def calculate_influence_offset(influence: Dict[str, Any]) -> Decimal:
        """計算單個影響的偏移量"""
        base_offset = Decimal(influence["base_stock_modifier"]) - DECIMAL_ONE

        expiry_time = influence.get("news_effect_expiry")
        if not expiry_time:
            return base_offset

        # 優化時區處理
        expiry_utc = (
            expiry_time.replace(tzinfo=timezone.utc)
            if expiry_time.tzinfo is None
            else expiry_time.astimezone(timezone.utc)
        )

        if expiry_utc > current_time_utc:
            temp_modifier = Decimal(influence["temporary_news_modifier"]) - DECIMAL_ONE
            return base_offset + temp_modifier
        return base_offset

    # 使用生成器表達式計算總影響
    total_stock_influence_offset = sum(
        calculate_influence_offset(influence) for influence in category_stock_influences
    )

    # 計算最終價格
    final_stock_influence = DECIMAL_ONE + total_stock_influence_offset
    weighted_stock_influence = (
        DECIMAL_ONE + (final_stock_influence - DECIMAL_ONE) * STOCK_INFLUENCE_WEIGHT
    )
    final_modifier = supply_demand_modifier * weighted_stock_influence
    clamped_modifier = max(MIN_MODIFIER, min(MAX_MODIFIER, final_modifier))

    actual_sell_price = (base_sell_price * clamped_modifier).quantize(
        Decimal("0.0001"), rounding=ROUND_HALF_UP
    )
    actual_sell_price = max(MIN_PRICE, actual_sell_price)

    # 使用 NamedTuple 返回結構化數據
    return PriceComponents(
        actual_price=actual_sell_price,
        trend_symbol=trend_symbol,
        base_sell_price=base_sell_price,
        supply_demand_modifier=supply_demand_modifier,
        previous_sd_modifier=previous_sd_modifier,
        final_stock_influence=final_stock_influence,
        stock_influence_weight=STOCK_INFLUENCE_WEIGHT,
        weighted_stock_influence=weighted_stock_influence,
        final_modifier_raw=final_modifier,
        clamped_modifier=clamped_modifier,
        min_modifier_config=MIN_MODIFIER,
        max_modifier_config=MAX_MODIFIER,
    )


async def _fetch_pricing_data_from_db(
    card_ids_to_fetch: List[int], conn: asyncpg.Connection
) -> tuple[
    Dict[int, Dict[str, Any]],
    Dict[int, Dict[str, Decimal]],
    Dict[str, Dict[str, Any]],
    Dict[int, List[str]],
    List[int],
]:
    """從數據庫獲取定價數據 - 優化版本"""
    if not card_ids_to_fetch:
        return {}, {}, {}, {}, []

    # 獲取卡片主數據
    card_master_map = await master_card_repository.get_cards_details_for_pricing_batch(
        card_ids_to_fetch, connection=conn
    )
    valid_card_ids = [cid for cid in card_ids_to_fetch if cid in card_master_map]

    if not valid_card_ids:
        return card_master_map, {}, {}, {}, []

    # 並行獲取市場統計數據
    market_stats_query = """
        SELECT card_id, supply_demand_modifier, previous_sd_modifier
        FROM gacha_card_market_stats
        WHERE card_id = ANY($1::int[])
    """
    market_stats_rows = await conn.fetch(market_stats_query, valid_card_ids)

    # 優化市場統計數據處理 - 使用字典推導式
    market_stats_map = {
        row["card_id"]: {
            "supply_demand_modifier": (
                Decimal(row["supply_demand_modifier"])
                if row["supply_demand_modifier"] is not None
                else DECIMAL_ONE
            ),
            "previous_sd_modifier": (
                Decimal(row["previous_sd_modifier"])
                if row["previous_sd_modifier"] is not None
                else (
                    Decimal(row["supply_demand_modifier"])
                    if row["supply_demand_modifier"] is not None
                    else DECIMAL_ONE
                )
            ),
        }
        for row in market_stats_rows
    }

    # 優化類別鍵生成 - 預先分配集合大小
    all_category_keys = {GLOBAL_CATEGORY_KEY}
    card_to_category_keys_map = {}

    # 批量處理卡片類別鍵
    for card_id in valid_card_ids:
        master_info = card_master_map.get(card_id)
        if not master_info:
            continue

        # 每張卡片都包含全局影響
        keys_for_card = [GLOBAL_CATEGORY_KEY]

        pool_type = master_info.get("pool_type")
        rarity = master_info.get("rarity")

        if pool_type:
            pool_type_key = f"POOL_TYPE:{pool_type}"
            keys_for_card.append(pool_type_key)
            all_category_keys.add(pool_type_key)

            if rarity is not None:
                rarity_key = f"RARITY_IN_POOL:{pool_type}:{rarity}"
                keys_for_card.append(rarity_key)
                all_category_keys.add(rarity_key)

        card_to_category_keys_map[card_id] = keys_for_card

    # 獲取股票影響數據
    category_influence_data_map = {}
    if all_category_keys:
        stock_influences_query = """
            SELECT category_key, base_stock_modifier, temporary_news_modifier, news_effect_expiry
            FROM gacha_category_stock_influence
            WHERE category_key = ANY($1::varchar[])
        """
        influence_rows = await conn.fetch(
            stock_influences_query, list(all_category_keys)
        )

        # 使用字典推導式優化數據處理
        category_influence_data_map = {
            row["category_key"]: dict(row) for row in influence_rows
        }

    return (
        card_master_map,
        market_stats_map,
        category_influence_data_map,
        card_to_category_keys_map,
        valid_card_ids,
    )


def _calculate_prices_batch(
    card_ids_to_calculate: List[int],
    card_master_map: Dict[int, Dict[str, Any]],
    market_stats_map: Dict[int, Dict[str, Decimal]],
    category_influence_data_map: Dict[str, Dict[str, Any]],
    card_to_category_keys_map: Dict[int, List[str]],
) -> Dict[int, Dict[str, Any]]:
    """批量計算價格 - 優化版本（同步處理，避免不必要的 async）"""

    # 預設默認市場統計
    default_market_stats = {
        "supply_demand_modifier": DECIMAL_ONE,
        "previous_sd_modifier": DECIMAL_ONE,
    }

    results = {}
    for card_id in card_ids_to_calculate:
        try:
            master_info = card_master_map.get(card_id)
            if not master_info:
                raise InvalidOperationError(f"找不到卡片主數據 for card_id: {card_id}")

            raw_sell_price = master_info.get("sell_price")
            if raw_sell_price is None:
                raise InvalidOperationError(f"卡片缺少售價 for card_id: {card_id}")

            try:
                base_sell_price = Decimal(raw_sell_price)
            except (ValueError, TypeError, InvalidOperation) as e:
                raise InvalidOperationError(
                    f"無效的售價格式 for card_id: {card_id}, price: '{raw_sell_price}'"
                ) from e

            market_stats = market_stats_map.get(card_id, default_market_stats)
            category_keys = card_to_category_keys_map.get(card_id, [])
            stock_influences = [
                category_influence_data_map[key]
                for key in category_keys
                if key in category_influence_data_map
            ]

            price_components = _calculate_price_components_for_single_card(
                base_sell_price, market_stats, stock_influences
            )
            results[card_id] = price_components._asdict()
        except Exception as e:
            logger.error(
                "[BATCH_PRICE_CALC] Failed to calculate price for card_id %s: %s",
                card_id,
                e,
                exc_info=True,
            )
            # 根據規範，讓錯誤冒泡，而不是返回 None
            raise

    return results


async def _process_price_calculation(
    card_ids: List[int], conn: asyncpg.Connection
) -> Dict[int, Dict[str, Any]]:
    """處理價格計算的核心邏輯"""
    # 獲取定價數據
    (
        card_master_map,
        market_stats_map,
        category_influence_data_map,
        card_to_category_keys_map,
        valid_card_ids,
    ) = await _fetch_pricing_data_from_db(card_ids, conn)

    # 建立結果字典，對於無效的卡片ID，我們直接不包含在結果中
    results: Dict[int, Dict[str, Any]] = {}

    if valid_card_ids:
        # 計算有效卡片的價格
        # 錯誤將從 _calculate_prices_batch 冒泡
        calculated_results = _calculate_prices_batch(
            valid_card_ids,
            card_master_map,
            market_stats_map,
            category_influence_data_map,
            card_to_category_keys_map,
        )
        results.update(calculated_results)

    # 檢查是否有任何請求的 card_id 未被處理
    unprocessed_ids = set(card_ids) - set(results.keys())
    if unprocessed_ids:
        logger.warning(
            "[BATCH_PRICE] The following card_ids could not be processed and will be excluded from the result: %s",
            unprocessed_ids,
        )

    return results


async def get_actual_card_sell_prices_batch(
    card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Dict[int, Dict[str, Any]]:
    """計算批量卡片的實際市場售價 - 優化版本"""
    if not card_ids:
        return {}

    # 優化連接管理
    if connection:
        return await _process_price_calculation(card_ids, connection)

    async with get_pool().acquire() as conn:
        return await _process_price_calculation(card_ids, conn)


async def calculate_and_get_market_prices_for_ids(
    card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Dict[int, Decimal]:
    """計算並返回批量卡片的實際市場售價 - 優化版本"""
    if not card_ids:
        return {}

    price_details_batch = await get_actual_card_sell_prices_batch(card_ids, connection)

    # 使用字典推導式優化價格提取
    return {
        card_id: details["actual_price"]
        for card_id, details in price_details_batch.items()
        if details
        and "actual_price" in details
        and isinstance(details["actual_price"], Decimal)
    }
