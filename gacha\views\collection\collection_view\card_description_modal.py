"""
Gacha系統卡片自訂描述模態
用於設置卡片描述的模態視窗
"""

import discord
from discord import TextStyle

from gacha.services import encyclopedia_service
from utils.base_modal import BaseModal
from utils.error_handler import BotType
from utils.response_embeds import SuccessEmbed


class CardDescriptionModal(BaseModal):
    """卡片描述設置模態視窗"""

    def __init__(
        self,
        bot: "BotType",
        user_id: int,
        card_id: int,
        card_name: str,
        current_star: int,
        min_star: int = 10,
    ):
        """初始化卡片描述設置模態

        參數:
            bot: Bot實例
            user_id: 用戶ID
            card_id: 卡片ID
            card_name: 卡片名稱
            current_star: 當前星級
            min_star: 最低需要星級
        """
        super().__init__(bot=bot, title=f"設置「{card_name}」的描述", timeout=None)
        self.user_id = user_id
        self.card_id = card_id
        self.card_name = card_name
        self.current_star = current_star
        self.min_star = min_star
        self.description_input = discord.ui.TextInput(
            label=f"為這張卡片設置描述 (當前星級: {current_star})",
            placeholder="請輸入對這張卡片的描述...",
            style=TextStyle.paragraph,
            min_length=5,
            max_length=200,
            required=True,
        )
        self.add_item(self.description_input)

    async def on_submit(self, interaction: discord.Interaction):
        """提交處理"""
        # 根據規範，優先 defer
        await interaction.response.defer(ephemeral=True)

        description = self.description_input.value.strip()

        # 業務邏輯（星級檢查）已移至 service 層，此處直接呼叫。
        # service 會在星級不足或描述不符規則時拋出 BusinessError，由 BaseModal.on_error 自動處理。
        await encyclopedia_service.update_card_description(
            user_id=self.user_id,
            card_id=self.card_id,
            description=description,
            min_star=self.min_star,
        )

        # 使用標準化的成功訊息 Embed
        embed = SuccessEmbed(
            description=f"成功為「{self.card_name}」設置描述！\n\n**描述內容**\n```{description}```"
        )
        await interaction.followup.send(embed=embed)
