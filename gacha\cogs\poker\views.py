"""
德州撲克1v1 Discord UI組件
包含所有的Modal、View和Button類
"""

import asyncio
from typing import Dict, Optional

import discord
from discord.ui import Button, TextInput

from gacha.exceptions import InvalidBetAmountError, InvalidOperationError
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger

from . import logic as poker_logic
from .models import (
    STAKE_CONFIGS,
    GameStage,
    LeaveGameCommand,
    PlayerAction,
    PlayerActionCommand,
    PokerGameState,
    StakeTier,
)


class GameStateValidator:
    """遊戲狀態驗證器 - 統一的狀態檢查邏輯"""

    @staticmethod
    async def check_game_state(
        interaction: discord.Interaction,
        game_state: PokerGameState,
        player_index: Optional[int] = None,
    ) -> bool:
        """統一的遊戲狀態檢查，不滿足條件時拋出異常"""

        # 檢查遊戲是否已結束
        if game_state.game_over:
            raise InvalidOperationError("❌ 遊戲已經結束！")

        # 檢查是否已經有勝負結果或進入攤牌階段
        if (
            game_state.winner_id is not None
            or game_state.current_stage == GameStage.SHOWDOWN
        ):
            raise InvalidOperationError("❌ 這手牌已經結束！")

        # 檢查雙方是否都全押
        if game_state.player1.is_all_in and game_state.player2.is_all_in:
            raise InvalidOperationError("❌ 雙方都已全押，等待攤牌！")

        # 檢查是否有操作正在進行
        if getattr(game_state, "_leaving_in_progress", False):
            raise InvalidOperationError("❌ 遊戲正在結算中！")

        if getattr(game_state, "_settlement_in_progress", False):
            raise InvalidOperationError("❌ 遊戲正在結算中！")

        # 如果指定了玩家索引，檢查是否是該玩家的回合
        if player_index is not None and not poker_logic.can_player_act(
            game_state, player_index
        ):
            logger.warning(
                "玩家 %s 嘗試在非自己回合操作 - 遊戲 %s, 當前玩家索引: %s, 嘗試操作玩家索引: %s, 遊戲階段: %s, 勝者: %s",
                interaction.user.id,
                game_state.game_id,
                game_state.current_player_index,
                player_index,
                game_state.current_stage.value,
                game_state.winner_id,
            )
            raise InvalidOperationError("❌ 不是你的回合！")

        return True


class BuyinModal(BaseModal):
    """帶入金額輸入模態框"""

    def __init__(self, stake_tier: StakeTier, cog, original_message=None):
        self.stake_tier = stake_tier
        self.cog = cog
        self.original_message = original_message  # 保存原始的場次選擇消息
        self.expected_user_id = None  # 將在創建時設置
        config = STAKE_CONFIGS[stake_tier]

        # 根據場次設置不同的標題
        if stake_tier == StakeTier.NEWBIE:
            title = "🟢 新手場 - 輸入帶入金額"
        elif stake_tier == StakeTier.REGULAR:
            title = "🟡 普通場 - 輸入帶入金額"
        else:  # PRO
            title = "🔴 高手場 - 輸入帶入金額"

        super().__init__(bot=cog.bot, title=title)

        self.buyin_input = TextInput(
            label="💰 帶入金額",
            placeholder="💵 請輸入 %s ~ %s 之間的金額"
            % (f"{config['min_buyin']:,}", f"{config['max_buyin']:,}"),
            min_length=1,
            max_length=10,
        )
        self.add_item(self.buyin_input)

    async def on_submit(self, interaction: discord.Interaction):
        # 先defer，避免超時（不顯示思考狀態）
        await interaction.response.defer()

        buyin_amount_str = self.buyin_input.value.replace(",", "")
        buyin_amount = int(buyin_amount_str)

        config = STAKE_CONFIGS[self.stake_tier]

        # Convert config values to int if they are strings
        min_buyin = (
            int(config["min_buyin"])
            if isinstance(config["min_buyin"], str)
            else config["min_buyin"]
        )
        max_buyin = (
            int(config["max_buyin"])
            if isinstance(config["max_buyin"], str)
            else config["max_buyin"]
        )

        # 唯一保留的驗證：檢查金額是否在場次允許範圍內（UI層面的輸入驗證）
        if not (min_buyin <= buyin_amount <= max_buyin):
            raise InvalidBetAmountError(
                f"帶入金額必須在 {config['min_buyin']:,} ~ {config['max_buyin']:,} 之間！"
            )

        # --- 核心變化：將所有業務邏輯委派給 Cog ---
        # 標記玩家操作中，並將所有業務邏輯委派給 Cog
        await self.cog._mark_player_in_operation(interaction.user.id)

        # The checks for is_player_in_game and is_player_in_queue are now handled by the cog/service layer.

        # 🔧 修復：Modal 已經 defer，必須使用 followup
        await self.cog._join_matchmaking(
            interaction,
            self.stake_tier,
            buyin_amount,
            use_followup=True,
            original_message=self.original_message,
        )


class RaiseModal(BaseModal):
    """加注金額輸入模態框"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        self.game_state = game_state
        self.player_index = player_index
        self.cog = cog

        config = STAKE_CONFIGS[game_state.stake_tier]
        # 🔧 修復BUG：最小加注金額應該是大盲注，不是當前最高下注
        min_raise = config["big_blind"]

        super().__init__(bot=cog.bot, title="加注金額")

        self.raise_input = TextInput(
            label="加注金額",
            placeholder="最小加注：%s" % f"{min_raise:,}",
            min_length=1,
            max_length=10,
        )
        self.add_item(self.raise_input)

    async def on_submit(self, interaction: discord.Interaction):
        # 🔧 修復：添加更嚴格的狀態檢查，防止舊Modal被提交
        # 從遊戲管理器獲取最新的遊戲狀態
        from . import game_manager

        current_game_state = None
        if game_manager.matchmaking_queue:
            current_game_state = game_manager.matchmaking_queue.active_games.get(
                self.game_state.game_id
            )

        if not current_game_state:
            raise InvalidOperationError("❌ 遊戲已結束或不存在！")

        # 使用最新的遊戲狀態進行檢查，不符合會拋出異常
        await GameStateValidator.check_game_state(
            interaction, current_game_state, self.player_index
        )

        # 先defer，避免超時，並讓後續可以用 followup
        await interaction.response.defer(ephemeral=True)

        raise_amount = int(self.raise_input.value.replace(",", ""))

        # 創建 Future 以接收結果
        loop = asyncio.get_running_loop()
        future = loop.create_future()

        # 創建玩家動作命令
        command = PlayerActionCommand(
            player_index=self.player_index,
            action=PlayerAction.RAISE,
            amount=raise_amount,
            interaction=interaction,
            future=future,
        )

        # 發送命令到遊戲處理器
        await self._send_command_to_processor(command)

        # 等待處理器完成操作，如果發生業務錯誤，異常會在此處被重新拋出
        await future

    async def _send_command_to_processor(self, command):
        """發送命令到遊戲處理器"""
        # 從 Cog 獲取對應的處理器
        processor = await self.cog._get_game_processor(self.game_state.game_id)
        if processor:
            await processor.send_command(command)
        else:
            logger.error(
                "動作按鈕無法找到遊戲 %s 的處理器！遊戲可能已靜默終止。",
                self.game_state.game_id,
            )
            raise InvalidOperationError(
                f"找不到遊戲 {self.game_state.game_id} 的處理器"
            )


class PokerGameView(BaseView):
    """德州撲克遊戲視圖"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        player = game_state.player1 if player_index == 0 else game_state.player2
        super().__init__(bot=cog.bot, user_id=player.user_id, timeout=None)
        self.game_state = game_state
        self.player_index = player_index
        self.cog = cog

        # 根據遊戲狀態添加按鈕
        self._setup_buttons()

    def _setup_buttons(self):
        """設置按鈕"""
        self.clear_items()

        # 檢查是否一手牌剛結束（有勝者但遊戲還沒結束）
        hand_just_ended = (
            self.game_state.winner_id is not None and not self.game_state.game_over
        )

        if self.game_state.game_over:
            # 遊戲結束，只顯示再玩一局按鈕
            self.add_item(
                ContinueMatchingButton(self.game_state, self.player_index, self.cog)
            )
        elif hand_just_ended:
            # 一手牌剛結束，顯示等待下一手牌的狀態，不顯示任何動作按鈕
            # 只顯示離開遊戲按鈕
            self.add_item(LeaveGameButton(self.game_state, self.player_index, self.cog))
        else:
            # 遊戲進行中，顯示動作按鈕
            valid_actions = poker_logic.get_valid_actions(
                self.game_state, self.player_index
            )

            if PlayerAction.FOLD in valid_actions:
                self.add_item(FoldButton(self.game_state, self.player_index, self.cog))

            if PlayerAction.CHECK in valid_actions:
                self.add_item(CheckButton(self.game_state, self.player_index, self.cog))

            if PlayerAction.CALL in valid_actions:
                call_amount = self.game_state.current_bet - (
                    self.game_state.player1.current_bet
                    if self.player_index == 0
                    else self.game_state.player2.current_bet
                )
                self.add_item(
                    CallButton(
                        self.game_state, self.player_index, self.cog, call_amount
                    )
                )

            if PlayerAction.RAISE in valid_actions:
                self.add_item(RaiseButton(self.game_state, self.player_index, self.cog))

            if PlayerAction.ALL_IN in valid_actions:
                self.add_item(AllInButton(self.game_state, self.player_index, self.cog))

            # 離開遊戲按鈕 - 直接結算
            self.add_item(LeaveGameButton(self.game_state, self.player_index, self.cog))


class StakeTierSelectView(BaseView):
    """場次選擇視圖"""

    def __init__(self, cog, user_id: int):
        super().__init__(bot=cog.bot, user_id=user_id, timeout=300)
        self.cog = cog

        # 添加場次選擇按鈕
        for tier in StakeTier:
            config = STAKE_CONFIGS[tier]
            self.add_item(StakeTierButton(tier, config, cog))

    async def on_timeout(self):
        """處理超時"""
        # 場次選擇介面超時時不需要取消操作標記，因為此時用戶還沒有被標記為操作中
        # 只需要清理視圖組件
        self.clear_items()
        self.stop()


class MatchmakingView(BaseView):
    """匹配中視圖"""

    def __init__(self, stake_tier: StakeTier, cog, user_id: int):
        super().__init__(bot=cog.bot, user_id=user_id, timeout=180)  # 3分鐘超時
        self.stake_tier = stake_tier
        self.cog = cog
        self._interaction: Optional[discord.Interaction] = None  # 將在使用時設置
        self.message: Optional[discord.Message] = None  # 將在使用時設置

        self.add_item(CancelMatchmakingButton(stake_tier, cog))

    async def on_timeout(self):
        """處理超時"""
        # 從隊列中移除（使用原子性方法）
        if hasattr(self, "user_id"):
            from . import game_manager

            removed = False
            if game_manager.matchmaking_queue and self.user_id is not None:
                removed = await game_manager.matchmaking_queue.remove_from_queue_atomic(
                    self.user_id, self.stake_tier
                )

            # 無論是否成功移除，都要取消操作標記和更新UI
            # 因為條目可能已經被定期清理任務移除
            await self.cog._unmark_player_in_operation(self.user_id)

            # 禁用所有按鈕組件（遵循Discord.py最佳實踐）
            for item in self.children:
                if hasattr(item, "disabled"):
                    try:
                        item.disabled = True  # type: ignore
                    except AttributeError:
                        pass

            # 更新用戶界面，告知匹配超時
            await self._show_timeout_message()

            if removed:
                logger.info("用戶 %s 匹配超時，已從隊列移除", self.user_id)
            else:
                logger.info(
                    "用戶 %s 匹配超時，但條目已不在隊列中（可能被定期清理）",
                    self.user_id,
                )
        self.stop()

    async def _show_timeout_message(self):
        """顯示匹配超時消息"""
        # 嘗試從 View 的 message 屬性獲取消息
        waiting_message = getattr(self, "message", None)

        # 如果沒有，嘗試從當前 View 的交互中獲取
        if not waiting_message and hasattr(self, "_interaction") and self._interaction:
            try:
                waiting_message = await self._interaction.original_response()
            except Exception:
                pass

        if waiting_message:
            timeout_embed = discord.Embed(
                title="⏰ 匹配超時",
                description="**很抱歉，在3分鐘內沒有找到合適的對手。**\n\n"
                "🔄 您可以重新嘗試匹配\n"
                "💡 建議在玩家較多的時間段進行匹配",
                color=discord.Color.orange(),
            )

            timeout_embed.add_field(
                name="💭 建議",
                value="• 嘗試不同的場次等級\n• 在高峰時段進行匹配\n• 調整帶入金額範圍",
                inline=False,
            )

            timeout_embed.set_footer(text="可以重新使用 /poker1v1 命令開始匹配")

            # 關鍵修復：保留view但組件已被禁用，而不是設置view=None
            await waiting_message.edit(embed=timeout_embed, view=self)
            logger.info("匹配超時消息已更新")
        else:
            logger.warning("無法找到等待消息，無法顯示超時狀態")


# 按鈕組件
class StakeTierButton(Button):
    """場次選擇按鈕"""

    def __init__(self, tier: StakeTier, config: Dict, cog):
        self.tier = tier
        self.config = config
        self.cog = cog

        # 根據場次設置不同的樣式和 emoji
        if tier == StakeTier.NEWBIE:
            label = "新手場"
            emoji = "🟢"
            style = discord.ButtonStyle.success
        elif tier == StakeTier.REGULAR:
            label = "普通場"
            emoji = "🟡"
            style = discord.ButtonStyle.primary
        else:  # PRO
            label = "高手場"
            emoji = "🔴"
            style = discord.ButtonStyle.danger

        super().__init__(
            label=label,
            emoji=emoji,
            style=style,
            row=0,  # 所有按鈕都在同一行
        )

    async def callback(self, interaction: discord.Interaction):
        # The checks for is_player_in_game and is_player_in_queue are now handled by the cog/service layer.

        # 獲取原始消息
        original_message = interaction.message

        # 顯示帶入金額輸入框
        modal = BuyinModal(self.tier, self.cog, original_message)
        await interaction.response.send_modal(modal)


class CancelMatchmakingButton(Button):
    """取消匹配按鈕"""

    def __init__(self, stake_tier: StakeTier, cog):
        self.stake_tier = stake_tier
        self.cog = cog
        super().__init__(label="取消匹配", style=discord.ButtonStyle.danger, emoji="❌")

    async def callback(self, interaction: discord.Interaction):
        # 🔧 修復：使用 defer + followup 模式，避免 edit_message 的 webhook 問題
        await interaction.response.defer()

        # 從隊列中移除（使用原子性方法）
        from . import game_manager

        removed = False
        if game_manager.matchmaking_queue:
            removed = await game_manager.matchmaking_queue.remove_from_queue_atomic(
                interaction.user.id, self.stake_tier
            )

        # 🔧 修復：無論是否成功移除，都要取消操作標記
        # 因為用戶可能已經不在隊列中（被定期清理等），但操作標記仍然存在
        await self.cog._unmark_player_in_operation(interaction.user.id)

        if removed:
            embed = discord.Embed(
                title="❌ 已取消匹配",
                description="您已退出匹配隊列",
                color=discord.Color.red(),
            )
        else:
            embed = discord.Embed(
                title="⚠️ 取消失敗",
                description="您不在匹配隊列中",
                color=discord.Color.orange(),
            )

        # 禁用按鈕
        if self.view:
            for item in self.view.children:
                if hasattr(item, "disabled"):
                    item.disabled = True

        # 編輯原始消息，而不是發送新消息
        # 讓全域錯誤處理器處理 discord.NotFound 等異常
        if interaction.message:
            await interaction.message.edit(embed=embed, view=self.view)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)


class PokerActionButton(Button):
    """撲克動作按鈕基類 - 統一處理權限檢查和錯誤處理"""

    def __init__(
        self,
        game_state: PokerGameState,
        player_index: int,
        cog,
        action: PlayerAction,
        label: str,
        style: discord.ButtonStyle,
        emoji: Optional[str] = None,
        **kwargs,
    ):
        self.game_state = game_state
        self.player_index = player_index
        self.cog = cog
        self.action = action
        super().__init__(label=label, style=style, emoji=emoji, **kwargs)

    async def callback(self, interaction: discord.Interaction):
        # 從遊戲管理器獲取最新的遊戲狀態
        from . import game_manager

        current_game_state = None
        if game_manager.matchmaking_queue:
            current_game_state = game_manager.matchmaking_queue.active_games.get(
                self.game_state.game_id
            )

        if not current_game_state:
            # 讓 on_error 處理，這裡只記錄日誌
            logger.warning(
                "玩家 %s 嘗試在已結束的遊戲 %s 中操作",
                interaction.user.id,
                self.game_state.game_id,
            )
            # 拋出一個業務異常，由 BaseView 統一處理
            raise InvalidOperationError("❌ 遊戲已結束或不存在！")

        # 使用最新的遊戲狀態進行檢查，不符合會拋出異常
        await GameStateValidator.check_game_state(
            interaction, current_game_state, self.player_index
        )

        # 檢查是否需要特殊處理（如加注需要彈出模態框）
        if self.action == PlayerAction.RAISE:
            modal = RaiseModal(current_game_state, self.player_index, self.cog)
            await interaction.response.send_modal(modal)
            return

        # 先defer
        await interaction.response.defer()

        # 創建 Future 以接收結果
        loop = asyncio.get_running_loop()
        future = loop.create_future()

        # 創建玩家動作命令（使用最新的遊戲狀態）
        command = PlayerActionCommand(
            player_index=self.player_index,
            action=self.action,
            amount=0,  # 非加注動作不需要金額
            interaction=interaction,
            future=future,
        )

        # 發送命令到遊戲處理器
        await self._send_command_to_processor(command)

        # 等待處理器完成操作，如果發生業務錯誤，異常會在此處被重新拋出
        await future

    async def _send_command_to_processor(self, command):
        """發送命令到遊戲處理器"""
        # 從 Cog 獲取對應的處理器
        processor = await self.cog._get_game_processor(self.game_state.game_id)
        if processor:
            await processor.send_command(command)
        else:
            # <-- 添加日誌
            logger.error(
                "動作按鈕無法找到遊戲 %s 的處理器！遊戲可能已靜默終止。",
                self.game_state.game_id,
            )
            raise InvalidOperationError(
                f"找不到遊戲 {self.game_state.game_id} 的處理器"
            )


class FoldButton(PokerActionButton):
    """棄牌按鈕"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        super().__init__(
            game_state,
            player_index,
            cog,
            PlayerAction.FOLD,
            "棄牌",
            discord.ButtonStyle.danger,
            "🗑️",
        )


class CheckButton(PokerActionButton):
    """過牌按鈕"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        super().__init__(
            game_state,
            player_index,
            cog,
            PlayerAction.CHECK,
            "過牌",
            discord.ButtonStyle.secondary,
            "✋",
        )


class CallButton(PokerActionButton):
    """跟注按鈕"""

    def __init__(
        self, game_state: PokerGameState, player_index: int, cog, call_amount: int
    ):
        super().__init__(
            game_state,
            player_index,
            cog,
            PlayerAction.CALL,
            "跟注 %s" % f"{call_amount:,}",
            discord.ButtonStyle.primary,
            "💰",
        )
        self.call_amount = call_amount


class RaiseButton(PokerActionButton):
    """加注按鈕"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        super().__init__(
            game_state,
            player_index,
            cog,
            PlayerAction.RAISE,
            "加注",
            discord.ButtonStyle.success,
            "📈",
        )


class AllInButton(PokerActionButton):
    """全押按鈕"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        player = game_state.player1 if player_index == 0 else game_state.player2
        super().__init__(
            game_state,
            player_index,
            cog,
            PlayerAction.ALL_IN,
            "全押 %s" % f"{player.chips:,}",
            discord.ButtonStyle.danger,
            "🎯",
        )


class ContinueMatchingButton(Button):
    """繼續匹配按鈕"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        self.game_state = game_state
        self.player_index = player_index
        self.cog = cog
        super().__init__(
            label="繼續匹配", style=discord.ButtonStyle.success, emoji="🔍"
        )

    async def callback(self, interaction: discord.Interaction):
        # 🔧 修復：遵循 Discord 交互規則 - 先 defer，再 followup
        await interaction.response.defer()

        # 檢查是否在匹配隊列中
        if await self.cog._is_player_in_queue(interaction.user.id):
            raise InvalidOperationError("❌ 您已經在匹配隊列中！")

        # 🔧 修復：檢查是否已經在其他遊戲中
        if await self.cog._is_player_in_game(interaction.user.id):
            raise InvalidOperationError("❌ 您已經在其他遊戲中！")

        # 🔧 修復：使用玩家的原始帶入金額，而不是最大帶入金額
        # 根據玩家索引獲取原始帶入金額
        if self.player_index == 0:
            original_buyin = self.game_state.player1_original_buyin
        else:
            original_buyin = self.game_state.player2_original_buyin

        # 直接觸發匹配流程，使用相同的賭注等級和原始帶入金額
        # 讓錯誤自然冒泡
        await self.cog._join_matchmaking(
            interaction,
            self.game_state.stake_tier,
            original_buyin,
            use_followup=True,
        )


class LeaveGameButton(Button):
    """離開遊戲按鈕 - 安全結算版"""

    def __init__(self, game_state: PokerGameState, player_index: int, cog):
        self.game_state = game_state
        self.player_index = player_index
        self.cog = cog
        super().__init__(label="離開遊戲", style=discord.ButtonStyle.danger, emoji="🚪")

    async def callback(self, interaction: discord.Interaction):
        # 從遊戲管理器獲取最新的遊戲狀態
        from . import game_manager

        current_game_state = None
        if game_manager.matchmaking_queue:
            current_game_state = game_manager.matchmaking_queue.active_games.get(
                self.game_state.game_id
            )

        if not current_game_state:
            raise InvalidOperationError("❌ 遊戲已結束或不存在！")

        # 檢查遊戲狀態
        if current_game_state.game_over:
            raise InvalidOperationError("❌ 遊戲已經結束！")

        await interaction.response.defer()

        # 創建 Future 以接收結果
        loop = asyncio.get_running_loop()
        future = loop.create_future()

        # 創建離開遊戲命令
        command = LeaveGameCommand(
            player_index=self.player_index,
            interaction=interaction,
            future=future,
        )

        # 發送命令到遊戲處理器
        await self._send_command_to_processor(command)

        # 等待處理器完成操作，如果發生業務錯誤，異常會在此處被重新拋出
        await future

    async def _send_command_to_processor(self, command):
        """發送命令到遊戲處理器"""
        # 從 Cog 獲取對應的處理器
        processor = await self.cog._get_game_processor(self.game_state.game_id)
        if processor:
            await processor.send_command(command)
        else:
            # <-- 添加日誌
            logger.error(
                "動作按鈕無法找到遊戲 %s 的處理器！遊戲可能已靜默終止。",
                self.game_state.game_id,
            )
            raise InvalidOperationError(
                f"找不到遊戲 {self.game_state.game_id} 的處理器"
            )
