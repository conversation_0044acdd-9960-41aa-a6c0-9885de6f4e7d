import asyncio
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Tuple

from gacha.exceptions import BusinessError
from gacha.repositories.collection import highest_star_repository
from utils.logger import logger

# --- 模組級配置 ---
BATCH_SIZE = 50
BATCH_INTERVAL = 3.0
QUEUE_WARNING_THRESHOLD = 5000

# --- 模組級狀態 ---
_maintenance_queue: asyncio.Queue[Optional["HighestStarMaintenanceTask"]] = (
    asyncio.Queue()
)
_worker_task: Optional[asyncio.Task] = None
_active = False


class HighestStarOperationType(Enum):
    """最高星級操作類型"""

    CARD_SOLD = "card_sold"
    CARD_TRADED = "card_traded"


@dataclass
class HighestStarMaintenanceTask:
    """最高星級維護任務"""

    operation_type: HighestStarOperationType
    card_user_pairs: List[Tuple[int, int]]
    receiver_user_id: Optional[int] = None
    quantity_removed: int = 1


# --- 公共 API ---


async def start():
    """啟動最高星級維護工作任務"""
    global _worker_task, _active
    if _worker_task is None or _worker_task.done():
        _active = True
        _worker_task = asyncio.create_task(_maintenance_worker())
        logger.info("Highest Star Maintenance service started.")


async def stop():
    """停止最高星級維護工作任務"""
    global _active, _worker_task
    _active = False
    if _worker_task and not _worker_task.done():
        await _maintenance_queue.put(None)  # 停止信號
        try:
            await asyncio.wait_for(_worker_task, timeout=10.0)
            logger.info("Highest Star Maintenance service stopped gracefully.")
        except asyncio.TimeoutError:
            _worker_task.cancel()
            logger.warning(
                "Highest Star Maintenance service stopping timed out, task cancelled."
            )
    _worker_task = None


async def schedule_card_sold_maintenance_batch(card_user_pairs: List[Tuple[int, int]]):
    """
    批量安排卡片賣出後的最高星級維護。
    """
    if not card_user_pairs:
        return
    try:
        cards_needing_maintenance = (
            await highest_star_repository.filter_cards_needing_maintenance(
                card_user_pairs
            )
        )
        if cards_needing_maintenance:
            task = HighestStarMaintenanceTask(
                operation_type=HighestStarOperationType.CARD_SOLD,
                card_user_pairs=cards_needing_maintenance,
            )
            await _enqueue_task(task)
    except Exception as e:
        logger.error(
            f"Error scheduling card sold maintenance batch: {e}", exc_info=True
        )
        raise BusinessError(f"安排卡片賣出維護失敗: {e}") from e


async def schedule_card_traded_maintenance(
    card_id: int, sender_user_id: int, receiver_user_id: int, quantity_traded: int = 1
):
    """
    安排卡片交易後的最高星級維護。
    """
    task = HighestStarMaintenanceTask(
        operation_type=HighestStarOperationType.CARD_TRADED,
        card_user_pairs=[(card_id, sender_user_id)],
        receiver_user_id=receiver_user_id,
        quantity_removed=quantity_traded,
    )
    await _enqueue_task(task)


async def get_queue_size() -> int:
    """獲取當前隊列大小"""
    return _maintenance_queue.qsize()


async def is_active() -> bool:
    """檢查服務是否活躍"""
    return _active and _worker_task is not None and not _worker_task.done()


# --- 內部實現 ---


async def _enqueue_task(task: HighestStarMaintenanceTask):
    """將任務加入隊列並監控大小"""
    await _maintenance_queue.put(task)
    queue_size = _maintenance_queue.qsize()
    if queue_size > QUEUE_WARNING_THRESHOLD:
        logger.warning(
            f"Highest star maintenance queue size is large: {queue_size} (threshold: {QUEUE_WARNING_THRESHOLD})."
        )


async def _maintenance_worker():
    """
    維護工作任務，持續從隊列中獲取任務並批量處理。
    """
    global _active
    batch_tasks: List[HighestStarMaintenanceTask] = []
    last_processed_time = asyncio.get_event_loop().time()

    while _active or not _maintenance_queue.empty():
        try:
            timeout = None
            if batch_tasks:
                time_since_last_process = (
                    asyncio.get_event_loop().time() - last_processed_time
                )
                timeout = max(0, BATCH_INTERVAL - time_since_last_process)

            task = await asyncio.wait_for(_maintenance_queue.get(), timeout=timeout)

            if task is None:  # 停止信號
                _maintenance_queue.task_done()
                break

            batch_tasks.append(task)
            _maintenance_queue.task_done()

            if len(batch_tasks) >= BATCH_SIZE:
                await _process_batch(batch_tasks)
                batch_tasks.clear()
                last_processed_time = asyncio.get_event_loop().time()

        except asyncio.TimeoutError:
            if batch_tasks:
                await _process_batch(batch_tasks)
                batch_tasks.clear()
                last_processed_time = asyncio.get_event_loop().time()
        except asyncio.CancelledError:
            logger.info("Maintenance worker cancelled.")
            break
        except Exception as e:
            logger.error(
                f"Unexpected error in highest star maintenance worker: {e}",
                exc_info=True,
            )
            await asyncio.sleep(1)

    if batch_tasks:
        await _process_batch(batch_tasks)
    logger.info("Maintenance worker finished processing.")
    _active = False


async def _process_batch(tasks: List[HighestStarMaintenanceTask]):
    """
    批量處理維護任務。
    """
    if not tasks:
        return

    card_ids_to_process = set()
    for task in tasks:
        for card_id, _ in task.card_user_pairs:
            card_ids_to_process.add(card_id)

    for card_id in card_ids_to_process:
        try:
            await _process_card_maintenance(card_id)
        except Exception as e:
            logger.error(
                f"Error processing highest star maintenance for card {card_id}: {e}",
                exc_info=True,
            )


async def _process_card_maintenance(card_id: int):
    """
    處理單張卡片的維護任務。
    """
    await asyncio.sleep(1)  # 增加延遲以等待主交易完成
    await highest_star_repository.recalculate_highest_star_holder(card_id)
