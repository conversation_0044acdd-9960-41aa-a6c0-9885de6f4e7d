# -*- coding: utf-8 -*-
"""
戰力分析功能的輔助函數
"""

import datetime
import re

import aiohttp
import discord

from utils.logger import logger

from .config import PowerAnalysisConfig


async def send_power_image_to_webhook(
    config: PowerAnalysisConfig,
    cdn_url: str,
    user_name: str,
    level: str,
    request_id: str,
) -> None:
    """發送戰力分析圖片 CDN URL 到 webhook"""
    webhook_payload = {
        "content": f"⚔️ **{user_name}** 的戰力分析結果 ({level}級)",
        "embeds": [
            {
                "title": "戰力分析圖片",
                "image": {"url": cdn_url},
                "color": config.webhook_color,
                "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
            }
        ],
    }

    try:
        async with (
            aiohttp.ClientSession() as session,
            session.post(config.webhook_url, json=webhook_payload) as response,
        ):
            if response.ok:
                logger.info(
                    "[%s] 戰力分析圖片 webhook 已成功發送，狀態碼: %s",
                    request_id,
                    response.status,
                )
            else:
                error_text = await response.text()
                logger.error(
                    "[%s] 戰力分析圖片 webhook 發送失敗，狀態碼: %s, 回應: %s",
                    request_id,
                    response.status,
                    error_text,
                )
    except aiohttp.ClientError as e:
        logger.error("[%s] 發送戰力分析圖片 webhook 時發生網路錯誤: %s", request_id, e)
    except Exception as e:
        logger.error("[%s] 發送戰力分析圖片 webhook 時發生未知錯誤: %s", request_id, e)


def get_sanitized_user_display_name(interaction: discord.Interaction) -> str:
    """獲取清理後的用戶顯示名稱"""
    try:
        user = interaction.user
        display_name = user.display_name
        # 清理顯示名稱
        sanitized_name = re.sub(r"[^\w\s\u4e00-\u9fff]", "", display_name)
        return sanitized_name[:20] if sanitized_name else f"用戶{str(user.id)[-4:]}"
    except Exception:
        return f"用戶{str(interaction.user.id)[-4:]}"
