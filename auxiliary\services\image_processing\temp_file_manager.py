"""
臨時文件管理器
用於管理 GIF 處理過程中的臨時文件，防止文件鎖定和清理問題
"""

import asyncio
import logging
import os
import time
from pathlib import Path

logger = logging.getLogger(__name__)


class TempFileManager:
    """臨時文件管理器"""

    def __init__(self, temp_dir: str = "image_processing/temp"):
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)

    async def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """清理超過指定時間的舊文件"""
        return await asyncio.to_thread(self._cleanup_old_files_sync, max_age_hours)

    def _cleanup_old_files_sync(self, max_age_hours: int) -> int:
        """同步清理舊文件"""
        cleaned_count = 0
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        try:
            for file_path in self.temp_dir.glob("*.gif"):
                try:
                    # 檢查文件年齡
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(
                            "已清理舊文件: %s (年齡: %.1f 小時)",
                            file_path,
                            file_age / 3600,
                        )
                except Exception as e:
                    logger.warning("清理文件失敗 %s: %s", file_path, e)

        except Exception as e:
            logger.error("清理臨時文件目錄失敗: %s", e)

        if cleaned_count > 0:
            logger.info("已清理 %d 個舊的臨時文件", cleaned_count)

        return cleaned_count

    async def force_cleanup_locked_files(self) -> int:
        """強制清理可能被鎖定的文件"""
        return await asyncio.to_thread(self._force_cleanup_locked_files_sync)

    def _force_cleanup_locked_files_sync(self) -> int:
        """同步強制清理被鎖定的文件"""
        cleaned_count = 0

        try:
            for file_path in self.temp_dir.glob("*.gif"):
                try:
                    # 嘗試重命名文件來檢測是否被鎖定
                    temp_name = file_path.with_suffix(".tmp")
                    file_path.rename(temp_name)
                    temp_name.rename(file_path)

                except Exception:
                    # 如果重命名失敗，說明文件可能被鎖定
                    logger.warning("檢測到可能被鎖定的文件: %s", file_path)
                    try:
                        # 嘗試強制刪除
                        if os.name == "nt":  # Windows
                            os.system(f'del /f /q "{file_path}"')
                        else:  # Unix/Linux
                            file_path.unlink()
                        cleaned_count += 1
                        logger.info("已強制清理被鎖定的文件: %s", file_path)
                    except Exception as e:
                        logger.error("強制清理文件失敗 %s: %s", file_path, e)

        except Exception as e:
            logger.error("強制清理過程中發生錯誤: %s", e)

        return cleaned_count

    def create_unique_filename(
        self, prefix: str, user_id: int, extension: str = ".gif"
    ) -> str:
        """創建唯一的文件名"""
        timestamp = int(time.time() * 1000000)  # 微秒級時間戳
        filename = f"{prefix}_{user_id}_{timestamp}{extension}"
        return str(self.temp_dir / filename)

    async def safe_remove_file(self, file_path: str) -> bool:
        """安全地刪除文件"""
        return await asyncio.to_thread(self._safe_remove_file_sync, file_path)

    def _safe_remove_file_sync(self, file_path: str) -> bool:
        """同步安全刪除文件"""
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                logger.debug("已刪除文件: %s", file_path)
                return True
        except Exception as e:
            logger.warning("刪除文件失敗 %s: %s", file_path, e)
        return False

    async def get_temp_dir_size(self) -> int:
        """獲取臨時目錄大小（字節）"""
        return await asyncio.to_thread(self._get_temp_dir_size_sync)

    def _get_temp_dir_size_sync(self) -> int:
        """同步獲取臨時目錄大小"""
        total_size = 0
        try:
            for file_path in self.temp_dir.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.error("計算臨時目錄大小失敗: %s", e)
        return total_size


# 全局實例
temp_file_manager = TempFileManager()


# 便利函數
async def cleanup_temp_files(max_age_hours: int = 24) -> int:
    """清理臨時文件的便利函數"""
    return await temp_file_manager.cleanup_old_files(max_age_hours)


async def force_cleanup_locked_files() -> int:
    """強制清理被鎖定文件的便利函數"""
    return await temp_file_manager.force_cleanup_locked_files()


def create_temp_filename(prefix: str, user_id: int) -> str:
    """創建臨時文件名的便利函數"""
    return temp_file_manager.create_unique_filename(prefix, user_id)
