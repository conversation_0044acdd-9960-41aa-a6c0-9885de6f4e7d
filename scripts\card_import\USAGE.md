# 卡片匯入系統使用指南

## 快速開始

### 方法一：使用批次檔案（推薦）
1. 雙擊 `import_cards.bat` 檔案
2. 根據選單選擇要執行的操作
3. 按照提示完成操作

### 方法二：命令行使用

#### 匯入 Hololive 卡片
```bash
cd scripts/card_import
python scripts/import_cards.py hololive data/hololive_card.json hololive
```

#### 匯入 Pokemon TCG 卡片
```bash
cd scripts/card_import
python scripts/import_cards.py ptcg data/pokemon_cards_jp_updated.json ptcg
```

#### 匯入 Union Arena 卡片
```bash
cd scripts/card_import
python scripts/import_cards.py ua data/union_arena_data.json ua
```

#### 匯入 WIXOSS 卡片
```bash
cd scripts/card_import
python scripts/import_wixoss_cards.py data/wixoss_simplified.json
```

#### 導出所有卡片
```bash
cd scripts/card_import
python scripts/export_cards_by_pool.py
```

#### 預下載卡片圖片
```bash
cd scripts/card_import
python scripts/pre_download_master_card_images.py
```

## 環境需求

1. **Python 3.7+**
2. **必要的 Python 套件：**
   - psycopg2
   - python-dotenv
   - asyncpg (用於圖片下載腳本)
   - aiohttp (用於圖片下載腳本)

3. **環境變數設定：**
   確保專案根目錄的 `.env` 檔案包含以下設定：
   ```
   GACHA_DB_NAME=your_database_name
   PG_USER=your_username
   PG_PASSWORD=your_password
   PG_HOST=localhost
   PG_PORT=5432
   ```

## 故障排除

### 常見問題

1. **找不到 .env 檔案**
   - 確保 `.env` 檔案位於專案根目錄 (D:\DICKPK\.env)
   - 檢查檔案權限

2. **資料庫連接失敗**
   - 檢查 PostgreSQL 服務是否運行
   - 驗證 `.env` 檔案中的資料庫連接資訊
   - 確認資料庫使用者有適當權限

3. **JSON 檔案解析錯誤**
   - 檢查 JSON 檔案格式是否正確
   - 確認檔案編碼為 UTF-8

4. **卡片重複匯入**
   - 系統會自動跳過已存在的卡片 (基於 original_id)
   - 這是正常行為，不會造成重複資料

### 日誌和除錯

- 所有腳本都會輸出詳細的執行日誌
- 注意查看警告和錯誤訊息
- 如果遇到問題，請保存完整的錯誤訊息以便除錯

## 檔案格式說明

### 支援的卡片格式

1. **Hololive 格式** (hololive_card.json)
2. **Pokemon TCG 格式** (pokemon_cards_jp_updated.json)
3. **Union Arena 格式** (union_arena_data.json)
4. **WIXOSS 格式** (wixoss_simplified.json)

所有格式都包含以下基本欄位：
- `original_id`: 卡片唯一識別碼
- `cardName`: 卡片名稱
- `seriesName`: 系列名稱
- `mediaUrl`: 圖片 URL
- `rarityLevel`: 稀有度等級 (1-7)

## 注意事項

1. **備份資料庫**：在大量匯入前建議備份資料庫
2. **網路連接**：圖片下載功能需要穩定的網路連接
3. **磁碟空間**：確保有足夠空間存放下載的圖片
4. **執行順序**：建議先匯入卡片資料，再下載圖片
