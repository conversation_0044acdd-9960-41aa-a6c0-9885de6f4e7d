"""
狀態效果實例模型
"""

import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, Optional


@dataclass
class StatusEffectInstance:
    """狀態效果實例模型"""

    instance_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status_effect_id: str = ""  # 指向 status_effects.json 中的定義
    caster_id: Optional[str] = None  # 施法者的instance_id
    target_id: str = ""  # 目標的instance_id

    # 狀態效果屬性
    duration_turns: int = 0  # 剩餘持續回合數
    stack_count: int = 1  # 疊加層數

    # 效果強度相關（用於可疊加強度的效果）
    effect_values: Dict[str, float] = field(default_factory=dict)

    # 動態參數覆蓋（用於 APPLY 系列的參數覆蓋）
    value_overrides: Dict[str, str] = field(default_factory=dict)

    # 創建時的上下文信息
    creation_context: Dict[str, Any] = field(default_factory=dict)

    def get_definition(self, all_configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        從全局配置中獲取狀態效果定義，並應用動態參數覆蓋

        Args:
            all_configs: 全局配置數據

        Returns:
            狀態效果的詳細定義（已應用參數覆蓋）
        """
        status_effects = all_configs.get("status_effects", {})
        definition = status_effects.get(self.status_effect_id, {}).copy()

        # 應用動態參數覆蓋
        if self.value_overrides:
            definition = self._apply_value_overrides(definition)

        return definition

    def _apply_value_overrides(self, definition: Dict[str, Any]) -> Dict[str, Any]:
        """
        應用動態參數覆蓋到狀態效果定義

        Args:
            definition: 原始狀態效果定義

        Returns:
            應用覆蓋後的定義
        """
        # 深拷貝以避免修改原始定義
        import copy

        modified_definition = copy.deepcopy(definition)

        # 遍歷所有效果定義並應用覆蓋
        for effect_list_key in [
            "effect_definitions_on_apply",
            "effect_definitions_per_tick",
            "effect_definitions_on_expire",
            "effect_definitions_triggered",
        ]:
            effect_list = modified_definition.get(effect_list_key, [])
            for effect_def in effect_list:
                if effect_def.get("effect_type") == "STAT_MODIFICATION":
                    modifications = effect_def.get("modifications", [])
                    for mod in modifications:
                        stat_name = mod.get("stat_name")
                        if stat_name and stat_name in self.value_overrides:
                            mod["value_formula"] = self.value_overrides[stat_name]

        return modified_definition

    def is_expired(self) -> bool:
        """
        檢查狀態效果是否已過期

        Returns:
            是否已過期
        """
        return self.duration_turns <= 0

    def tick_duration(self) -> bool:
        """
        減少持續時間

        Returns:
            是否仍然有效（未過期）
        """
        if self.duration_turns > 0:
            self.duration_turns -= 1
        return not self.is_expired()

    def can_stack_with(
        self, other: "StatusEffectInstance", all_configs: Dict[str, Any]
    ) -> bool:
        """
        檢查是否可以與另一個狀態效果疊加

        Args:
            other: 另一個狀態效果實例
            all_configs: 全局配置數據

        Returns:
            是否可以疊加
        """
        if self.status_effect_id != other.status_effect_id:
            return False

        definition = self.get_definition(all_configs)
        special_flags = definition.get("special_flags", [])
        max_stacks = definition.get("max_stacks", 1)

        # 檢查是否允許疊加
        if (
            "STACKABLE_DURATION" in special_flags
            or "STACKABLE_INTENSITY" in special_flags
        ):
            return self.stack_count < max_stacks

        return False

    def stack_with(
        self, other: "StatusEffectInstance", all_configs: Dict[str, Any]
    ) -> None:
        """
        與另一個狀態效果疊加

        Args:
            other: 另一個狀態效果實例
            all_configs: 全局配置數據
        """
        if not self.can_stack_with(other, all_configs):
            return

        definition = self.get_definition(all_configs)
        special_flags = definition.get("special_flags", [])

        if "STACKABLE_DURATION" in special_flags:
            # 疊加持續時間類型：增加層數，刷新持續時間
            self.stack_count = min(
                self.stack_count + 1, definition.get("max_stacks", 1)
            )
            self.duration_turns = other.duration_turns

        elif "STACKABLE_INTENSITY" in special_flags:
            # 疊加強度類型：增加層數，疊加效果值，刷新持續時間
            self.stack_count = min(
                self.stack_count + 1, definition.get("max_stacks", 1)
            )
            self.duration_turns = other.duration_turns

            # 疊加效果值
            for key, value in other.effect_values.items():
                self.effect_values[key] = self.effect_values.get(key, 0) + value

    def refresh_duration(self, new_duration: int) -> None:
        """
        刷新持續時間

        Args:
            new_duration: 新的持續時間
        """
        self.duration_turns = new_duration

    def get_effective_value(self, value_key: str) -> float:
        """
        獲取考慮疊加層數的有效值

        Args:
            value_key: 值的鍵名

        Returns:
            有效值
        """
        base_value = self.effect_values.get(value_key, 0.0)

        # 對於疊加強度類型的效果，值已經在疊加時累加了
        # 對於疊加持續時間類型的效果，可能需要乘以層數
        # 這裡簡化處理，直接返回存儲的值
        return base_value

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "instance_id": self.instance_id,
            "status_effect_id": self.status_effect_id,
            "caster_id": self.caster_id,
            "target_id": self.target_id,
            "duration_turns": self.duration_turns,
            "stack_count": self.stack_count,
            "effect_values": self.effect_values,
            "value_overrides": self.value_overrides,
            "creation_context": self.creation_context,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "StatusEffectInstance":
        """從字典創建實例"""
        return cls(
            instance_id=data.get("instance_id", str(uuid.uuid4())),
            status_effect_id=data.get("status_effect_id", ""),
            caster_id=data.get("caster_id"),
            target_id=data.get("target_id", ""),
            duration_turns=data.get("duration_turns", 0),
            stack_count=data.get("stack_count", 1),
            effect_values=data.get("effect_values", {}),
            value_overrides=data.get("value_overrides", {}),
            creation_context=data.get("creation_context", {}),
        )
