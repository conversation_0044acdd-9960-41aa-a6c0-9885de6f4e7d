import asyncio
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
from typing import List, Tuple

from database.postgresql.async_manager import get_pool
from gacha.exceptions import BusinessError
from gacha.services import direct_market_stats_updater
from utils.logger import logger

# --- 強制停止舊worker的信號旗 ---
FORCE_STOP_OLD_WORKERS = False

# --- 模組配置 ---
BATCH_SIZE = 500
BATCH_INTERVAL = 1.0
QUEUE_WARNING_THRESHOLD = 10000

# --- 模組狀態 ---
_maintenance_queue: asyncio.Queue = asyncio.Queue()
_worker_tasks: List[asyncio.Task] = []
_active = False
NUM_WORKERS = 4  # 可根據伺服器核心數和負載進行調整
ADVISORY_LOCK_KEY = 42  # 用於市場統計更新的特定諮詢鎖ID


class MarketStatsOperationType(Enum):
    """市場統計操作類型"""

    TOTAL_OWNED_UPDATE = "total_owned_update"
    UNIQUE_OWNER_UPDATE = "unique_owner_update"
    FAVORITE_COUNT_UPDATE = "favorite_count_update"
    WISHLIST_COUNT_UPDATE = "wishlist_count_update"


@dataclass
class MarketStatsMaintenanceTask:
    """市場統計維護任務"""

    operation_type: MarketStatsOperationType
    updates: List[Tuple[int, int]]  # [(card_id, change_amount), ...]


# --- 內部輔助函數 ---


def _merge_duplicate_updates(
    updates: List[Tuple[int, int]], operation_type: MarketStatsOperationType
) -> List[Tuple[int, int]]:
    """
    合併重複的 card_id 更新，根據操作類型使用不同的合併策略
    """
    if not updates:
        return []

    if operation_type == MarketStatsOperationType.UNIQUE_OWNER_UPDATE:
        increment_cards = set()
        decrement_cards = set()
        for card_id, change_amount in updates:
            if change_amount == 1:
                increment_cards.add(card_id)
            elif change_amount == -1:
                decrement_cards.add(card_id)

        final_increments = increment_cards - decrement_cards
        final_decrements = decrement_cards - increment_cards

        result = []
        result.extend([(card_id, 1) for card_id in final_increments])
        result.extend([(card_id, -1) for card_id in final_decrements])
        return result
    else:
        merged_updates = defaultdict(int)
        for card_id, change_amount in updates:
            merged_updates[card_id] += change_amount
        return [
            (card_id, change)
            for card_id, change in merged_updates.items()
            if change != 0
        ]


def _prepare_update_records(
    tasks_batch: List[MarketStatsMaintenanceTask],
) -> Tuple[List[tuple], List[tuple]]:
    """
    【同步函數】準備用於資料庫更新的記錄。
    這是一個 CPU 密集型操作，應該在執行器中運行以避免阻塞事件循環。
    """
    # 1. 將不同類型的更新合併到一個以 card_id 為 key 的字典中
    combined_updates = defaultdict(lambda: defaultdict(int))
    unique_owner_updates = []

    for task in tasks_batch:
        op_type = task.operation_type
        if op_type == MarketStatsOperationType.TOTAL_OWNED_UPDATE:
            for card_id, delta in task.updates:
                combined_updates[card_id]["total_owned_quantity"] += delta
        elif op_type == MarketStatsOperationType.FAVORITE_COUNT_UPDATE:
            for card_id, delta in task.updates:
                combined_updates[card_id]["favorite_count"] += delta
        elif op_type == MarketStatsOperationType.WISHLIST_COUNT_UPDATE:
            for card_id, delta in task.updates:
                combined_updates[card_id]["wishlist_count"] += delta
        elif op_type == MarketStatsOperationType.UNIQUE_OWNER_UPDATE:
            unique_owner_updates.extend(task.updates)

    # 2. 處理合併更新
    update_records = []
    if combined_updates:
        for card_id in sorted(combined_updates.keys()):
            deltas = combined_updates[card_id]
            record = (
                card_id,
                deltas.get("total_owned_quantity", 0),
                deltas.get("favorite_count", 0),
                deltas.get("wishlist_count", 0),
            )
            update_records.append(record)

    # 3. 處理獨立擁有者更新
    merged_unique_updates = []
    if unique_owner_updates:
        merged_unique_updates = _merge_duplicate_updates(
            unique_owner_updates,
            MarketStatsOperationType.UNIQUE_OWNER_UPDATE,
        )

    return update_records, merged_unique_updates


async def _process_tasks_batch(tasks_batch: List[MarketStatsMaintenanceTask]):
    """
    處理一批市場統計維護任務。
    """
    if not tasks_batch:
        return

    task = asyncio.current_task()
    task_name = task.get_name() if task else "worker"
    logger.info(
        "[MARKET_STATS_WORKER] %s 開始處理批次任務，數量: %s",
        task_name,
        len(tasks_batch),
    )

    # 1. 【非阻塞執行】在執行器中運行 CPU 密集的合併操作
    loop = asyncio.get_running_loop()
    update_records, merged_unique_updates = await loop.run_in_executor(
        None, _prepare_update_records, tasks_batch
    )

    # 2. 【異步執行】執行資料庫事務
    pool = get_pool()
    if pool is None:
        raise BusinessError("資料庫連線池未初始化")

    async with pool.acquire() as conn:
        async with conn.transaction():
            # 使用諮詢鎖確保寫入互斥
            await conn.execute("SELECT pg_advisory_xact_lock($1)", ADVISORY_LOCK_KEY)

            if update_records:
                logger.debug(
                    "%s 持有鎖，準備合併更新 %s 張卡片...",
                    task_name,
                    len(update_records),
                )
                await direct_market_stats_updater.bulk_update_combined_stats(
                    update_records, connection=conn
                )

            if merged_unique_updates:
                logger.debug(
                    "%s 持有鎖，準備處理獨立擁有者更新: %s 項",
                    task_name,
                    len(merged_unique_updates),
                )
                await direct_market_stats_updater.bulk_update_unique_owners(
                    merged_unique_updates, connection=conn
                )


async def _maintenance_worker():
    """
    維護工作任務，持續從隊列中獲取任務並批量處理。
    【已修復】確保任務在失敗時會被重新排隊，而不是被丟棄。
    """
    task = asyncio.current_task()
    task_name = task.get_name() if task else "worker"
    logger.info("%s 開始運行", task_name)

    if "FORCE_STOP_OLD_WORKERS" in globals() and FORCE_STOP_OLD_WORKERS:
        logger.warning("偵測到強制停止信號，舊 worker (%s) 正在自我終止...", task_name)
        return

    tasks_batch: List[MarketStatsMaintenanceTask] = []
    last_process_time = asyncio.get_event_loop().time()

    while _active:
        try:
            current_time = asyncio.get_event_loop().time()
            time_since_last_process = current_time - last_process_time

            should_process = (len(tasks_batch) >= BATCH_SIZE) or (
                len(tasks_batch) > 0 and time_since_last_process >= BATCH_INTERVAL
            )

            if should_process:
                await _process_tasks_batch(tasks_batch)
                tasks_batch.clear()  # 成功處理後清空
                last_process_time = asyncio.get_event_loop().time()
                continue

            # 優先檢查佇列是否為空，避免在有任務時還去長時間等待
            if not _maintenance_queue.empty():
                task_item = _maintenance_queue.get_nowait()
            else:
                # 只有當佇列為空時，才去 await get()
                remaining_time = max(0.1, BATCH_INTERVAL - time_since_last_process)
                task_item = await asyncio.wait_for(
                    _maintenance_queue.get(), timeout=remaining_time
                )

            if task_item is None:
                logger.info("%s 收到停止信號", task_name)
                break

            tasks_batch.append(task_item)
            
            # 無論是從 nowait 還是 await 拿到任務，
            # 在下一次迴圈前都強制讓出控制權，避免緊迫循環
            await asyncio.sleep(0)

        except asyncio.TimeoutError:
            if tasks_batch:
                try:
                    await _process_tasks_batch(tasks_batch)
                    tasks_batch.clear()  # 成功處理後清空
                except Exception as e:
                    logger.error(
                        "%s 處理超時批次時發生錯誤: %s", task_name, e, exc_info=True
                    )
                    # 錯誤處理：將任務放回隊列以便重試
                    for task in reversed(tasks_batch):
                        _maintenance_queue.put_nowait(task)
                    tasks_batch.clear()
                    await asyncio.sleep(5)  # 發生錯誤時等待更長時間
            last_process_time = asyncio.get_event_loop().time()

        except Exception as e:
            logger.error("%s 在主循環中發生未知錯誤: %s", task_name, e, exc_info=True)
            if tasks_batch:
                # 錯誤處理：將任務放回隊列
                for task in reversed(tasks_batch):
                    _maintenance_queue.put_nowait(task)
                tasks_batch.clear()
            await asyncio.sleep(5)

    # 服務停止後，處理剩餘的所有任務
    if tasks_batch:
        try:
            logger.info("%s 正在處理關閉前的剩餘任務...", task_name)
            await _process_tasks_batch(tasks_batch)
            tasks_batch.clear()
        except Exception as e:
            logger.error("%s 處理最後一批任務時失敗: %s", task_name, e, exc_info=True)

    logger.info("%s 已停止", task_name)

    logger.info("%s 已停止", task_name)


# --- 公共 API ---


async def start():
    """啟動市場統計維護工作任務"""
    global _worker_tasks, _active
    if not _active:
        _active = True
        _worker_tasks = [
            asyncio.create_task(_maintenance_worker(), name=f"maint_worker_{i}")
            for i in range(NUM_WORKERS)
        ]
        logger.info("MarketStatsMaintenanceService 已啟動 %s 個 worker", NUM_WORKERS)


async def stop():
    """停止所有市場統計維護工作任務"""
    global _worker_tasks, _active
    if not _active:
        return
    _active = False

    # 為每個 worker 發送一個停止信號
    for _ in _worker_tasks:
        await _maintenance_queue.put(None)

    try:
        await asyncio.wait_for(
            asyncio.gather(*_worker_tasks, return_exceptions=True), timeout=15.0
        )
        logger.info("所有 MarketStatsMaintenanceService worker 已正常停止")
    except asyncio.TimeoutError:
        logger.warning(
            "MarketStatsMaintenanceService workers 停止超時，正在強制取消..."
        )
        for task in _worker_tasks:
            task.cancel()
    finally:
        _worker_tasks.clear()


async def _enqueue_task(task: MarketStatsMaintenanceTask):
    """將任務加入隊列 - 永不丟棄任務"""
    logger.debug("[MARKET_STATS_SERVICE] 將任務加入隊列: %s", task.operation_type.value)
    await _maintenance_queue.put(task)
    queue_size = _maintenance_queue.qsize()
    if queue_size > QUEUE_WARNING_THRESHOLD:
        logger.warning(
            "Market stats maintenance queue size is large: %s (threshold: %s). Consider checking system performance.",
            queue_size,
            QUEUE_WARNING_THRESHOLD,
        )


async def schedule_total_owned_update(updates: List[Tuple[int, int]]):
    """安排總擁有量更新任務"""
    if not updates:
        return
    await _enqueue_task(
        MarketStatsMaintenanceTask(MarketStatsOperationType.TOTAL_OWNED_UPDATE, updates)
    )


async def schedule_unique_owner_update(updates: List[Tuple[int, int]]):
    """安排獨立擁有者更新任務"""
    if not updates:
        return
    await _enqueue_task(
        MarketStatsMaintenanceTask(
            MarketStatsOperationType.UNIQUE_OWNER_UPDATE, updates
        )
    )


async def schedule_favorite_count_update(updates: List[Tuple[int, int]]):
    """安排收藏數量更新任務"""
    if not updates:
        return
    await _enqueue_task(
        MarketStatsMaintenanceTask(
            MarketStatsOperationType.FAVORITE_COUNT_UPDATE, updates
        )
    )


async def schedule_wishlist_count_update(updates: List[Tuple[int, int]]):
    """安排許願數量更新任務"""
    if not updates:
        return
    await _enqueue_task(
        MarketStatsMaintenanceTask(
            MarketStatsOperationType.WISHLIST_COUNT_UPDATE, updates
        )
    )


async def get_queue_size() -> int:
    """獲取當前隊列大小"""
    return _maintenance_queue.qsize()


async def is_active() -> bool:
    """檢查服務是否活躍"""
    global _worker_tasks
    return _active and any(not task.done() for task in _worker_tasks)
