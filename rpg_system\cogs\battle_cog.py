"""
RPG戰鬥系統的Discord Cog
處理戰鬥相關的Discord命令和交互
使用純異常模式進行錯誤處理
"""

import asyncio

import discord
from discord import app_commands
from discord.ext import commands

from bot import CustomAutoShardedBot
from rpg_system.services import battle_coordinator_service
from rpg_system.views.embeds.battle_embeds import BattleEmbedBuilder
from utils.logger import logger


class BattleCog(commands.Cog, name="RPG戰鬥"):
    """處理RPG戰鬥相關指令"""

    def __init__(self, bot: CustomAutoShardedBot):
        self.bot = bot
        logger.info("BattleCog initialized.")

    @app_commands.command(name="fight_floor", description="挑戰指定樓層")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(floor_id="要挑戰的樓層ID")
    async def fight_floor(self, interaction: discord.Interaction, floor_id: str):
        """
        挑戰指定樓層的PVE戰鬥
        """
        await interaction.response.defer()
        user_id = interaction.user.id

        battle = await battle_coordinator_service.prepare_pve_battle(user_id, floor_id)
        battle = await battle_coordinator_service.start_battle(battle)

        acting_combatant = await battle.get_acting_combatant()
        initial_embed = BattleEmbedBuilder.create_battle_progress_embed(
            battle_id=battle.battle_id,
            current_turn=1,
            current_actor=acting_combatant.name if acting_combatant else "Unknown",
            player_team_status=self._get_team_status(battle.player_team),
            monster_team_status=self._get_team_status(battle.monster_team),
        )

        message = await interaction.followup.send(embed=initial_embed)
        await self._run_live_battle(battle, message, user_id, floor_id)

    async def _run_live_battle(self, battle, message, user_id: int, floor_id: str):
        """
        運行實時戰鬥更新
        """
        turn_count = 0
        max_turns = 100

        while not battle.is_battle_over() and turn_count < max_turns:
            await asyncio.sleep(2.0)
            turn_result = await battle.auto_battle_step()

            if (
                turn_result.get("battle_ended", False)
                or turn_result.get("error")
                or battle.is_battle_over()
            ):
                break

            turn_count += 1
            current_actor = await battle.get_acting_combatant()
            progress_embed = BattleEmbedBuilder.create_battle_progress_embed(
                battle_id=battle.battle_id,
                current_turn=turn_count,
                current_actor=current_actor.name if current_actor else "Unknown",
                player_team_status=self._get_team_status(battle.player_team),
                monster_team_status=self._get_team_status(battle.monster_team),
            )

            recent_logs = battle.battle_log[-5:] if battle.battle_log else []
            if recent_logs:
                log_text = self._format_battle_logs(recent_logs)
                progress_embed.add_field(
                    name="📜 本回合戰報", value=log_text, inline=False
                )

            await message.edit(embed=progress_embed)

        completion_result = (
            await battle_coordinator_service.handle_pve_battle_completion(
                user_id, battle, floor_id
            )
        )

        final_embed = BattleEmbedBuilder.create_battle_result_embed(
            battle.to_dict(), completion_result
        )
        await message.edit(embed=final_embed, view=None)

    def _get_team_status(self, team):
        """
        獲取隊伍狀態信息
        """
        team_status = []
        for combatant in team:
            status_effects = []
            if hasattr(combatant, "status_effects") and combatant.status_effects:
                for effect in combatant.status_effects:
                    status_effects.append(
                        {
                            "effect_id": (effect.status_effect_id),
                            "duration": (effect.duration_turns),
                        }
                    )
            team_status.append(
                {
                    "instance_id": combatant.instance_id,
                    "name": combatant.name,
                    "is_alive": combatant.is_alive(),
                    "current_hp": combatant.current_hp,
                    "max_hp": combatant.max_hp,
                    "current_mp": getattr(combatant, "current_mp", 0),
                    "max_mp": getattr(combatant, "max_mp", 0),
                    "status_effects": status_effects,
                }
            )
        return team_status

    def _format_battle_logs(self, logs, max_length: int = 1000):
        """
        格式化戰鬥日誌
        """
        if not logs:
            return "本回合無特殊事件。"
        log_lines = [f"• {log.message}" for log in logs]
        full_text = "\n".join(log_lines)
        if len(full_text) > max_length:
            return full_text[: max_length - 3] + "..."
        return full_text


async def setup(bot: CustomAutoShardedBot):
    """設置Cog"""
    await bot.add_cog(BattleCog(bot))
    logger.info("BattleCog added successfully.")
