from __future__ import annotations

from discord import Interaction, app_commands
from discord.ext import commands

from gacha.services import leaderboard_service
from gacha.views.leaderboard.leaderboard_view import LeaderboardView
from utils.logger import logger


class LeaderboardCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("LeaderboardCog initialized.")

    @app_commands.command(name="lb", description="查看排行榜")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(category="選擇排行榜分類")
    @app_commands.choices(
        category=[
            app_commands.Choice(name="📊 綜合排行", value="general"),
            app_commands.Choice(name="🎴 卡牌相關排行", value="collection"),
            app_commands.Choice(name="📈 股票市場排行", value="stock_market"),
            app_commands.Choice(name="🎮 小遊戲排行", value="games"),
            app_commands.Choice(name="📋 其他排行", value="other"),
        ]
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    async def leaderboard(self, interaction: Interaction, category: str) -> None:
        """
        處理排行榜命令，協調數據加載和視圖顯示。
        現在完全由 leaderboard_service 驅動。
        """
        await interaction.response.defer(thinking=True)

        # 步驟 1: 呼叫 Service 層獲取所有初始數據
        # 錯誤將在此處被捕獲並由全局錯誤處理器處理
        leaderboard_data = await leaderboard_service.get_initial_leaderboard_data(
            category, interaction.user.id
        )

        # 步驟 2: 創建並發送視圖
        # 從返回的數據中提取初始參數
        initial_params = leaderboard_data.get("initial_params", {})
        initial_category = leaderboard_data.get("initial_category", category)

        view = LeaderboardView(
            user=interaction.user,
            initial_category=initial_category,
            initial_params=initial_params,
            leaderboard_data=leaderboard_data,
            bot=self.bot,
            original_interaction=interaction,
        )

        await view.update_view()
        embed = await view.get_current_page_embed()

        # 使用 followup.send，因為 defer() 之後只能用這個
        message = await interaction.followup.send(embed=embed, view=view)
        view.message = message


async def setup(bot: commands.Bot):
    await bot.add_cog(LeaderboardCog(bot))
    logger.info("LeaderboardCog added to bot.")
