#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匯出 gacha_master_cards 資料表的所有卡片資料為 JSON 格式
根據 pool_type 進行分類
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# 從 .env 檔案載入環境變數
load_dotenv()


class DateTimeEncoder(json.JSONEncoder):
    """自定義 JSON 編碼器，處理 datetime 和 Decimal 類型"""

    def default(self, o):
        if isinstance(o, datetime):
            return o.isoformat()
        elif isinstance(o, Decimal):
            return float(o)
        return super().default(o)


async def export_cards_by_pool():
    """匯出卡片資料並根據 pool_type 分類"""
    pool = get_pool()
    if not pool:
        print("數據庫連接池未初始化。")
        return

    async with pool.acquire() as conn:
        print("正在查詢資料庫...")

        # 查詢所有卡片資料
        query = """
        SELECT
            card_id,
            original_id,
            name,
            series,
            image_url,
            description,
            sell_price,
            creation_date,
            pool_type,
            rarity,
            current_market_sell_price,
            last_price_update_at
        FROM gacha_master_cards
        ORDER BY pool_type, rarity, card_id
        """

        rows = await conn.fetch(query)
        print(f"查詢到 {len(rows)} 張卡片")

        # 根據 pool_type 分類
        cards_by_pool: Dict[str, List[Dict[str, Any]]] = {}

        for row in rows:
            # 將 asyncpg.Record 轉換為字典
            card_data = dict(row)

            pool_type = card_data["pool_type"]

            if pool_type not in cards_by_pool:
                cards_by_pool[pool_type] = []

            cards_by_pool[pool_type].append(card_data)

        # 建立輸出目錄
        output_dir = "exported_cards"
        os.makedirs(output_dir, exist_ok=True)

        # 匯出統計資訊
        stats = {}
        for pool_type, cards in cards_by_pool.items():
            stats[pool_type] = {"total_cards": len(cards), "rarity_distribution": {}}

            # 統計稀有度分布
            for card in cards:
                rarity = card["rarity"]
                if rarity not in stats[pool_type]["rarity_distribution"]:
                    stats[pool_type]["rarity_distribution"][rarity] = 0
                stats[pool_type]["rarity_distribution"][rarity] += 1

        # 儲存統計資訊
        stats_file = os.path.join(output_dir, "export_statistics.json")
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(stats, f, ensure_ascii=False, indent=2, cls=DateTimeEncoder)
        print(f"統計資訊已儲存至: {stats_file}")

        # 分別儲存每個 pool_type 的資料
        for pool_type, cards in cards_by_pool.items():
            filename = f"cards_{pool_type}.json"
            filepath = os.path.join(output_dir, filename)

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(cards, f, ensure_ascii=False, indent=2, cls=DateTimeEncoder)

            print(f"已匯出 {pool_type} 池: {len(cards)} 張卡片 -> {filepath}")

        # 儲存完整資料（所有 pool 合併）
        all_cards_file = os.path.join(output_dir, "all_cards_complete.json")
        all_cards = []
        for cards in cards_by_pool.values():
            all_cards.extend(cards)

        with open(all_cards_file, "w", encoding="utf-8") as f:
            json.dump(all_cards, f, ensure_ascii=False, indent=2, cls=DateTimeEncoder)
        print(f"完整資料已儲存至: {all_cards_file}")

        # 儲存按 pool_type 分類的結構化資料
        structured_file = os.path.join(output_dir, "cards_by_pool_structured.json")
        with open(structured_file, "w", encoding="utf-8") as f:
            json.dump(
                cards_by_pool, f, ensure_ascii=False, indent=2, cls=DateTimeEncoder
            )
        print(f"結構化資料已儲存至: {structured_file}")

        print("\n=== 匯出完成 ===")
        print(f"總計匯出 {len(all_cards)} 張卡片")
        print(f"分為 {len(cards_by_pool)} 個池類型:")
        for pool_type, cards in cards_by_pool.items():
            print(f"  - {pool_type}: {len(cards)} 張卡片")


async def main():
    await setup_connections()
    try:
        await export_cards_by_pool()
    except Exception as e:
        print(f"匯出過程中發生錯誤: {e}")
        raise
    finally:
        await close_connections()


if __name__ == "__main__":
    asyncio.run(main())
