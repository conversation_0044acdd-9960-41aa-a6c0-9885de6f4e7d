from typing import Union

import discord
from discord.ext import commands

from gacha.exceptions import BusinessError
from utils.error_handler import handle_interaction_error

BotType = Union[commands.Bot, commands.AutoShardedBot]


class BaseView(discord.ui.View):
    def __init__(self, *, bot: BotType, user_id: int, timeout: float | None = 600):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.user_id = user_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此視圖互動的使用者是否為原始觸發者。
        這個檢查現在對所有繼承者都有效！
        """
        if interaction.user.id != self.user_id:
            raise BusinessError("您無法操作此介面！")
        return True

    async def on_error(
        self, interaction: discord.Interaction, error: Exception, item: discord.ui.Item
    ):  # type: ignore[override]
        """
        通用錯誤處理器。

        分層處理：
        1. BusinessError: 業務邏輯錯誤，直接向用戶顯示清晰訊息。
        2. 其他異常: 交給全域處理器處理。
        """
        # 將所有錯誤（包括 BusinessError）直接轉發給集中的錯誤處理器
        await handle_interaction_error(interaction, error, self.bot)
