# -*- coding: utf-8 -*-
"""
AI 回應清理策略配置系統

支援每個提示詞庫定義自己的清理規則，提供高度的擴展性。
分離歷史記錄清理和前端顯示清理兩種不同的需求。
"""

import re
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class CleanupStrategy:
    """清理策略配置"""

    # === 歷史記錄清理配置 (給AI看的內容) ===
    # 歷史記錄中要保留的標籤列表
    history_keep_tags: Optional[List[str]] = None

    # 歷史記錄中要移除的標籤列表
    history_remove_tags: Optional[List[str]] = None

    # 歷史記錄中要提取內容的標籤列表（移除標籤，保留內容）
    history_extract_content_tags: Optional[List[str]] = None

    # === 前端顯示清理配置 (給用戶看的內容) ===
    # 前端顯示要保留的標籤列表（保留標籤和內容）
    frontend_keep_tags: Optional[List[str]] = None

    # 前端顯示要移除的標籤列表（移除標籤和內容）
    frontend_remove_tags: Optional[List[str]] = None

    # 前端顯示要提取內容的標籤列表（移除標籤，保留內容）
    frontend_extract_content_tags: Optional[List[str]] = None

    # 前端顯示要移除的正規表達式模式列表
    frontend_remove_patterns: Optional[List[str]] = None

    # === 通用配置 ===
    # 是否移除重複標籤
    dedupe_tags: bool = True

    # 自定義清理函數名稱 (可選)
    custom_cleaner: Optional[str] = None

    def __post_init__(self):
        if self.history_keep_tags is None:
            self.history_keep_tags = []
        if self.history_remove_tags is None:
            self.history_remove_tags = []
        if self.history_extract_content_tags is None:
            self.history_extract_content_tags = []
        if self.frontend_keep_tags is None:
            self.frontend_keep_tags = []
        if self.frontend_remove_tags is None:
            self.frontend_remove_tags = []
        if self.frontend_extract_content_tags is None:
            self.frontend_extract_content_tags = []
        if self.frontend_remove_patterns is None:
            self.frontend_remove_patterns = []


# 預定義的清理策略
CLEANUP_STRATEGIES = {
    "default": CleanupStrategy(
        # 歷史記錄配置 - 保持現有行為不變
        history_keep_tags=["content", "status_block"],  # 歷史中只保留這些
        history_remove_tags=[
            "disclaimer",
            "guifan",
            "fin",
            "summary",
            "thinking",
            "context",
        ],
        # 對於歷史記錄，我們要提取摘要內容（支持新舊格式）
        history_extract_content_tags=["details", "summary"],
        # 前端顯示配置 - 修復狀態欄重複顯示問題
        frontend_extract_content_tags=["content"],  # 提取content內容給用戶看
        frontend_keep_tags=[],  # 不保留任何標籤，狀態欄由story_logic.py單獨處理
        frontend_remove_tags=[
            "disclaimer",
            "guifan",
            "fin",
            "summary",
            "thinking",
            "context",
            "options",
            "status_block",  # 移除狀態欄標籤，避免重複顯示
        ],
        frontend_remove_patterns=[
            # 修正：移除在備案機制下可能殘留的、未配對的 <content> 開頭標籤
            r"^\s*<content>\s*",
            r"```start|```end|<done>|`<done>`",
            r"<!--[\s\S]*?-->\s*",
        ],
        dedupe_tags=True,
    ),
    "beilu": CleanupStrategy(
        # beilu 策略完全使用自定義清理函數，標準配置僅供參考
        # 實際配置在 custom_beilu_cleaner 函數中硬編碼
        custom_cleaner="custom_beilu_cleaner",
        dedupe_tags=True,
    ),
    # 範例：嚴格清理策略
    "strict": CleanupStrategy(
        # 歷史記錄 - 嚴格模式，只保留核心內容
        history_keep_tags=["content", "status_block"],
        history_remove_tags=[
            "disclaimer",
            "guifan",
            "fin",
            "summary",
            "details",
            "thinking",
            "context",
        ],
        # 前端顯示 - 同樣嚴格
        frontend_extract_content_tags=["content"],
        frontend_keep_tags=["status_block"],
        frontend_remove_tags=[
            "disclaimer",
            "guifan",
            "fin",
            "summary",
            "details",
            "thinking",
            "context",
            "options",
        ],
        frontend_remove_patterns=[
            r"```start|```end|<done>|`<done>`",
            r"<!--[\s\S]*?-->\s*",
            r".*?</think(ing)?>\n?",
            r"<think(ing)?>[\s\S]*?</think(ing)?>\n?",
        ],
        dedupe_tags=True,
    ),
}


def get_cleanup_strategy(library_name: str) -> CleanupStrategy:
    """獲取指定庫的清理策略"""
    return CLEANUP_STRATEGIES.get(library_name, CLEANUP_STRATEGIES["default"])


def apply_cleanup_strategy(
    text: str, strategy: CleanupStrategy, cleanup_type: str = "frontend"
) -> str:
    """應用清理策略到文本

    Args:
        text: 要清理的文本
        strategy: 清理策略配置
        cleanup_type: 清理類型 'frontend' | 'history'
    """
    result = text

    # 1. 去重標籤 (如果啟用)
    if strategy.dedupe_tags:
        result = _remove_duplicate_tags(result)

    # 2. 如果有自定義清理函數，優先使用
    if strategy.custom_cleaner:
        custom_func = globals().get(strategy.custom_cleaner)
        if custom_func and callable(custom_func):
            return str(custom_func(result, strategy, cleanup_type))

    # 3. 根據清理類型選擇對應的規則
    if cleanup_type == "history":
        keep_tags = strategy.history_keep_tags or []
        remove_tags = strategy.history_remove_tags or []
        extract_content_tags = strategy.history_extract_content_tags or []
        remove_patterns = []  # 歷史記錄不使用正規表達式清理
    else:  # frontend
        keep_tags = strategy.frontend_keep_tags or []
        remove_tags = strategy.frontend_remove_tags or []
        extract_content_tags = strategy.frontend_extract_content_tags or []
        remove_patterns = strategy.frontend_remove_patterns or []

    # 4. 先提取指定標籤的內容（移除標籤，保留內容）
    if extract_content_tags:
        result = _extract_tag_content(result, extract_content_tags)

    # 5. 應用標籤清理規則
    if remove_tags or keep_tags:
        result = _apply_tag_rules(result, remove_tags, keep_tags)

    # 6. 應用正規表達式清理 (僅前端)
    if remove_patterns:
        result = _apply_pattern_rules(result, remove_patterns)

    return result.strip()


def _remove_duplicate_tags(text: str) -> str:
    """移除重複的標籤，只保留第一個出現的標籤內容。"""
    # 處理可能重複的標籤列表
    tags_to_dedupe = [
        "content",
        "status_block",
        "options",
        "summary",
        "details",  # 向下兼容舊格式
        "response",
        "story",
        "interactive_input",
        "thinking",
        "context",
        "disclaimer",
        "guifan",
        "fin",
    ]

    result = text
    for tag in tags_to_dedupe:
        # 匹配所有該標籤的出現
        pattern = rf"<{tag}>(.*?)</{tag}>"
        matches = list(re.finditer(pattern, result, re.DOTALL | re.IGNORECASE))

        if len(matches) > 1:
            # 如果有多個匹配，保留第一個，移除其他的
            # 從後往前移除，避免索引變化
            for match in reversed(matches[1:]):
                result = result[: match.start()] + result[match.end() :]

    # 處理選項標籤的去重 (option_1, option_2, etc.)
    option_pattern = r"<option_(\d+)>(.*?)</option_\d+>"
    option_matches = list(
        re.finditer(option_pattern, result, re.DOTALL | re.IGNORECASE)
    )

    if option_matches:
        # 收集所有選項內容，按編號去重
        options_found = {}
        for match in option_matches:
            option_num = match.group(1)
            if option_num not in options_found:
                options_found[option_num] = match

        # 從後往前移除重複的選項標籤
        for match in reversed(option_matches):
            option_num = match.group(1)
            if match != options_found[option_num]:  # 不是第一次出現的
                result = result[: match.start()] + result[match.end() :]

    return result


def _extract_tag_content(text: str, extract_tags: List[str]) -> str:
    """提取指定標籤的內容，移除標籤本身"""
    result = text

    for tag in extract_tags:
        if tag == "details":
            # 特殊處理：舊格式的details標籤包含<summary>，我們只要summary後的內容
            details_pattern = r"<details><summary>.*?</summary>([\s\S]*?)</details>"
            details_match = re.search(details_pattern, result, re.IGNORECASE)
            if details_match:
                content = details_match.group(1).strip()
                result = re.sub(details_pattern, content, result, flags=re.IGNORECASE)
        elif tag == "summary":
            # 處理簡化的summary標籤：直接提取內容
            summary_pattern = rf"<{re.escape(tag)}>(.*?)</{re.escape(tag)}>"
            matches = re.findall(summary_pattern, result, re.DOTALL | re.IGNORECASE)
            if matches:
                # 用第一個匹配的內容替換整個標籤
                content = matches[0].strip()
                result = re.sub(
                    summary_pattern,
                    content,
                    result,
                    count=1,
                    flags=re.DOTALL | re.IGNORECASE,
                )
        else:
            # 一般標籤處理
            pattern = rf"<{re.escape(tag)}>(.*?)</{re.escape(tag)}>"
            matches = re.findall(pattern, result, re.DOTALL | re.IGNORECASE)

            if matches:
                # 用第一個匹配的內容替換整個標籤
                content = matches[0].strip()
                result = re.sub(
                    pattern, content, result, count=1, flags=re.DOTALL | re.IGNORECASE
                )

    return result


def _apply_tag_rules(text: str, remove_tags: List[str], keep_tags: List[str]) -> str:
    """應用標籤清理規則"""
    result = text

    # keep_tags 優先級更高
    if keep_tags:
        # 如果定義了keep_tags，那麼只保留這些標籤，移除其他所有標籤
        # 但這可能太激進，暫時只處理remove_tags
        pass

    # 移除指定的標籤
    for tag in remove_tags:
        pattern = rf"<{re.escape(tag)}>[\s\S]*?</{re.escape(tag)}>\s*"
        result = re.sub(pattern, "", result, flags=re.IGNORECASE)

    return result


def _apply_pattern_rules(text: str, patterns: List[str]) -> str:
    """應用正規表達式清理規則"""
    result = text

    for pattern in patterns:
        try:
            result = re.sub(pattern, "", result, flags=re.DOTALL | re.IGNORECASE)
        except re.error as e:
            # 如果正規表達式有錯誤，記錄但不中斷處理
            import logging

            logger = logging.getLogger(__name__)
            logger.warning(f"Invalid regex pattern '{pattern}': {e}")

    return result


# 自定義清理函數範例
def custom_beilu_cleaner(
    text: str, strategy: CleanupStrategy, cleanup_type: str
) -> str:
    """beilu庫的自定義清理函數 - 硬編碼配置，不依賴 strategy 參數"""
    # 1. 去重標籤（thinking 標籤已在 story_logic.py 中清理）
    result = _remove_duplicate_tags(text)

    if cleanup_type == "history":
        # 2. 歷史記錄：先提取摘要內容（支持新舊格式）
        result = _extract_tag_content(result, ["details", "summary"])

        # 3. 然後移除指定標籤，保留 content 和 status_block
        result = _apply_tag_rules(
            result,
            remove_tags=[
                "disclaimer",
                "guifan",
                "fin",
                "summary",
                "thinking",
                "context",
            ],
            keep_tags=["content", "status_block"],
        )
    else:  # frontend
        # 2. 先提取 content 標籤內容
        result = _extract_tag_content(result, ["content"])

        # 3. 應用標籤清理規則，移除所有標籤包括 status_block
        # 因為 status_block 已在 story_logic.py 中單獨解析並處理
        result = _apply_tag_rules(
            result,
            remove_tags=[
                "disclaimer",
                "guifan",
                "fin",
                "summary",
                "thinking",
                "context",
                "options",
                "status_block",
            ],
            keep_tags=[],
        )

        # 4. 應用 beilu 專用的正規表達式清理
        patterns = [
            r"<math class>[\s\S]*?</math class>",  # 微積分去除
            r"\*\*",  # 星號清理
        ]
        result = _apply_pattern_rules(result, patterns)

        # 5. 不再需要處理論壇格式標籤，已改為純文字格式

        # 6. beilu庫特殊的文本處理
        result = _apply_beilu_text_cleanup(result)

    return result.strip()


def _apply_forum_format_cleanup(text: str) -> str:
    """處理論壇格式標籤，轉換為前端友好的格式"""
    result = text

    # 處理論壇結構 <wz>...</wz>
    wz_pattern = r"<wz>([\s\S]*?)</wz>"
    wz_match = re.search(wz_pattern, result, re.IGNORECASE)
    if wz_match:
        wz_content = wz_match.group(1)

        # 提取標題 <bt>...</bt>
        bt_pattern = r"<bt>(.*?)</bt>"
        bt_match = re.search(bt_pattern, wz_content, re.IGNORECASE)
        title = bt_match.group(1) if bt_match else ""

        # 提取論壇信息 <zj>...</zj>
        zj_pattern = r"<zj>(.*?)</zj>"
        zj_match = re.search(zj_pattern, wz_content, re.IGNORECASE)
        info = zj_match.group(1) if zj_match else ""

        # 提取評論列表 <pl>...</pl>
        pl_pattern = r"<pl>([\s\S]*?)</pl>"
        pl_match = re.search(pl_pattern, wz_content, re.IGNORECASE)
        comments = ""
        if pl_match:
            pl_content = pl_match.group(1)
            # 處理每條評論 <nr>...</nr>
            nr_pattern = r"<nr>(.*?)</nr>"
            nr_matches = re.findall(nr_pattern, pl_content, re.IGNORECASE)
            if nr_matches:
                comments = "\n".join(f"💬 {comment.strip()}" for comment in nr_matches)

        # 組合成清潔的論壇格式
        forum_text = (
            f"📋 {title}\n🏷️ {info}\n\n{comments}"
            if comments
            else f"📋 {title}\n🏷️ {info}"
        )

        # 替換原始標籤結構
        result = re.sub(wz_pattern, forum_text, result, flags=re.IGNORECASE)

    return result


def _apply_beilu_text_cleanup(text: str) -> str:
    """beilu庫專用的文本清理"""
    result = text

    # 專防省略號增殖 - 移除特定詞彙後的省略號
    ellipsis_pattern1 = r"(最終|然後|地|的|以及|有些|和|似乎|已經|是|去|或許|更加|那個|一[\u4e00-\u9fa5])(……|…|······)(?!\s)"
    result = re.sub(ellipsis_pattern1, r"\1", result)

    ellipsis_pattern2 = r"(最終|然後|地|的|以及|有些|和|似乎|已經|是|去|或許|更加|那個|一[\u4e00-\u9fa5])(……|…)"
    result = re.sub(ellipsis_pattern2, r"\1", result)

    # 省略號改逗號 - 將剩餘的省略號替換為逗號（但不處理字符串開頭的省略號）
    # ellipsis_to_comma = r"(?<!^)(……|…(?!…))(?=\S)"
    # result = re.sub(ellipsis_to_comma, "，", result)

    return result
