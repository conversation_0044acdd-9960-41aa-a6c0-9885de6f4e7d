from typing import Any, Dict, List, Optional, Tuple

from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    BusinessError,
    CardAlreadyAtBottomError,
    CardAlreadyAtTopError,
    CardNotMarkedAsFavoriteError,
    CardNotSortedError,
    InvalidPositionError,
    NextCardNotFoundError,
    NoCardIdsProvidedError,
    NoCardUpdatesProvidedError,
    PreviousCardNotFoundError,
)
from gacha.repositories.collection import user_collection_repository
from gacha.services import collection_service, sorting_index_manager
from utils.logger import logger

"""
排序服務模組，處理卡片自定義排序邏輯。
"""

PAGE_SIZE = collection_service.CARDS_PER_PAGE


def _calculate_page_from_position(position: int) -> int:
    """根據位置計算頁面"""
    if position <= 0:
        return 1
    return (position - 1) // PAGE_SIZE + 1


async def move_card_to_position(
    user_id: int, card_id: int, target_position_str: str
) -> Dict[str, Any]:
    """
    (Async) 卡片排序的核心方法 - 移動卡片到指定位置
    """
    try:
        target_position = int(target_position_str)
    except (ValueError, TypeError) as e:
        raise InvalidPositionError(message="無效的位置輸入，請輸入一個數字。") from e

    try:
        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                card_info = await user_collection_repository.get_card_sort_info(
                    user_id, card_id, connection=conn
                )
                if not card_info or card_info["sort_index"] is None:
                    raise CardNotMarkedAsFavoriteError(card_id=card_id)

                total_sorted_cards = (
                    await user_collection_repository.count_sorted_cards(
                        user_id, connection=conn
                    )
                )
                if not (1 <= target_position <= total_sorted_cards):
                    logger.warning(
                        "[GACHA][SORT] 目標位置無效: %s, 卡片總數: %s",
                        target_position,
                        total_sorted_cards,
                    )
                    raise InvalidPositionError(
                        target_position=target_position, total_cards=total_sorted_cards
                    )

                current_position = card_info["rank"]
                if current_position == target_position:
                    if target_position == 1:
                        raise CardAlreadyAtTopError()
                    elif target_position == total_sorted_cards:
                        raise CardAlreadyAtBottomError()
                    else:
                        return {
                            "position": target_position,
                            "page": _calculate_page_from_position(target_position),
                        }

                new_index = await sorting_index_manager.calculate_index_for_position(
                    user_id, card_id, current_position, target_position, connection=conn
                )
                await user_collection_repository.set_custom_sort_index(
                    user_id, card_id, new_index, connection=conn
                )

                updated_card_info = await user_collection_repository.get_card_sort_info(
                    user_id, card_id, connection=conn
                )
                actual_position = (
                    updated_card_info["rank"] if updated_card_info else target_position
                )

                if actual_position != target_position:
                    logger.warning(
                        "[GACHA][SORT] 實際位置(%s)與目標位置(%s)不一致",
                        actual_position,
                        target_position,
                    )

        new_page = _calculate_page_from_position(actual_position)
        return {
            "position": actual_position,
            "page": new_page,
            "debug_info": {
                "target_position": target_position,
                "actual_position": actual_position,
                "target_page": _calculate_page_from_position(target_position),
                "actual_page": new_page,
                "new_index": new_index,
                "page_size": PAGE_SIZE,
            },
        }
    except (
        CardNotMarkedAsFavoriteError,
        InvalidPositionError,
        CardAlreadyAtTopError,
        CardAlreadyAtBottomError,
        ValueError,
    ):
        raise
    except Exception as e:
        logger.error("[GACHA][SORT] 移動卡片到指定位置失敗: %s", str(e), exc_info=True)
        # 根據規範，讓未知錯誤自然冒泡，而不是包裝成 BusinessError
        raise


async def move_card_to_top(user_id: int, card_id: int) -> Dict[str, Any]:
    """(Async) 將卡片移至自定義排序的最前面（位置1）"""
    # 根據規範，移除 try-except，讓錯誤自然冒泡
    return await move_card_to_position(user_id, card_id, "1")


async def move_card_to_bottom(user_id: int, card_id: int) -> Dict[str, Any]:
    """(Async) 將卡片移至自定義排序的最後面"""
    # 根據規範，移除 try-except，讓錯誤自然冒泡
    total_cards = await user_collection_repository.count_sorted_cards(user_id)
    target_position = total_cards if total_cards > 0 else 1
    return await move_card_to_position(user_id, card_id, str(target_position))


async def move_card_up(user_id: int, card_id: int) -> Dict[str, Any]:
    """(Async) 將卡片在排序中向上移動一位 (與前一張卡交換索引)"""
    try:
        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                sort_info = await user_collection_repository.get_card_sort_info(
                    user_id, card_id, connection=conn
                )
                if not sort_info or sort_info.get("sort_index") is None:
                    raise CardNotSortedError(card_id=card_id)

                current_index = sort_info["sort_index"]
                current_rank = sort_info["rank"]
                if current_rank == 1:
                    raise CardAlreadyAtTopError()

                prev_card_info = await user_collection_repository.get_prev_sorted_card(
                    user_id, current_index, connection=conn
                )
                if not prev_card_info or prev_card_info.get("sort_index") is None:
                    logger.error(
                        "[GACHA][SORT] move_card_up: 找不到有效的前一張卡片，但 rank 為 %s。 User=%s, Card=%s",
                        current_rank,
                        user_id,
                        card_id,
                    )
                    raise PreviousCardNotFoundError()

                prev_card_id = prev_card_info["card_id"]
                prev_index = prev_card_info["sort_index"]
                await user_collection_repository.set_custom_sort_index(
                    user_id, card_id, prev_index, connection=conn
                )
                await user_collection_repository.set_custom_sort_index(
                    user_id, prev_card_id, current_index, connection=conn
                )

                new_rank = current_rank - 1
                return {
                    "position": new_rank,
                    "page": _calculate_page_from_position(new_rank),
                }
    except (
        CardNotSortedError,
        PreviousCardNotFoundError,
        CardAlreadyAtTopError,
        ValueError,
    ):
        raise
    except Exception as e:
        logger.error("[GACHA][SORT] 上移卡片失敗: %s", str(e), exc_info=True)
        raise


async def move_card_down(user_id: int, card_id: int) -> Dict[str, Any]:
    """(Async) 將卡片在排序中向下移動一位 (與後一張卡交換索引)"""
    try:
        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                sort_info = await user_collection_repository.get_card_sort_info(
                    user_id, card_id, connection=conn
                )
                if not sort_info or sort_info.get("sort_index") is None:
                    raise CardNotSortedError(card_id=card_id)

                current_index = sort_info["sort_index"]
                current_rank = sort_info["rank"]
                total_sorted_cards = (
                    await user_collection_repository.count_sorted_cards(
                        user_id, connection=conn
                    )
                )

                if current_rank >= total_sorted_cards:
                    raise CardAlreadyAtBottomError()

                next_card_info = await user_collection_repository.get_next_sorted_card(
                    user_id, current_index, connection=conn
                )
                if not next_card_info or next_card_info.get("sort_index") is None:
                    logger.error(
                        "[GACHA][SORT] move_card_down: 找不到有效的後一張卡片，但 rank 為 %s/%s。 User=%s, Card=%s",
                        current_rank,
                        total_sorted_cards,
                        user_id,
                        card_id,
                    )
                    raise NextCardNotFoundError()

                next_card_id = next_card_info["card_id"]
                next_index = next_card_info["sort_index"]

                await user_collection_repository.set_custom_sort_index(
                    user_id, card_id, next_index, connection=conn
                )
                await user_collection_repository.set_custom_sort_index(
                    user_id, next_card_id, current_index, connection=conn
                )

                new_rank = current_rank + 1
                return {
                    "position": new_rank,
                    "page": _calculate_page_from_position(new_rank),
                }
    except (
        CardNotSortedError,
        NextCardNotFoundError,
        CardAlreadyAtBottomError,
        ValueError,
    ):
        raise
    except Exception as e:
        logger.error("[GACHA][SORT] 下移卡片失敗: %s", str(e), exc_info=True)
        raise


async def update_card_custom_sort_index(
    user_id: int, card_id: int, custom_sort_index: int
) -> Dict[str, Any]:
    """(Async) 更新指定卡片的自定義排序索引"""
    try:
        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                await user_collection_repository.get_user_card(
                    user_id, card_id, connection=conn
                )
                await user_collection_repository.set_custom_sort_index(
                    user_id, card_id, custom_sort_index, connection=conn
                )
                updated_card_info = await user_collection_repository.get_card_sort_info(
                    user_id, card_id, connection=conn
                )
                new_position = (
                    updated_card_info["rank"]
                    if updated_card_info and updated_card_info["sort_index"] is not None
                    else 0
                )

        return {
            "position": new_position,
            "page": _calculate_page_from_position(new_position),
            "custom_sort_index": custom_sort_index,
        }
    except Exception as e:
        logger.error(
            "[GACHA][SORT] 更新卡片排序索引時發生未知錯誤: %s", str(e), exc_info=True
        )
        raise


async def bulk_update_sort_indexes(
    user_id: int, card_updates: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """(Async) 批量更新多張卡片的排序索引"""
    try:
        if not card_updates:
            raise NoCardUpdatesProvidedError()

        updates_for_repo: List[Tuple[int, Optional[int]]] = []
        for update_item in card_updates:
            card_id = update_item.get("card_id")
            sort_index = update_item.get("sort_index")
            if card_id is None:
                raise ValueError("批量更新中缺少 card_id")
            updates_for_repo.append((card_id, sort_index))

        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                await user_collection_repository.bulk_update_sort_indexes(
                    user_id, updates_for_repo, connection=conn
                )

        return {"updated_count": len(card_updates)}
    except NoCardUpdatesProvidedError:
        raise
    except ValueError as ve:
        logger.error(
            "[GACHA][SORT] 批量更新排序索引時參數錯誤: %s", str(ve), exc_info=True
        )
        raise BusinessError(f"參數錯誤: {str(ve)}") from ve
    except Exception as e:
        logger.error("[GACHA][SORT] 批量更新排序索引失敗: %s", str(e), exc_info=True)
        raise


async def reorder_cards(user_id: int, card_ids: List[int]) -> Dict[str, Any]:
    """(Async) 根據提供的卡片ID列表重新排序所有卡片"""
    try:
        if not card_ids:
            raise NoCardIdsProvidedError()

        base_index = 1000
        step = 1000
        card_updates = [
            {"card_id": card_id, "sort_index": base_index + pos * step}
            for pos, card_id in enumerate(card_ids, start=1)
        ]
        await bulk_update_sort_indexes(user_id, card_updates)

        return {"reordered_count": len(card_ids)}
    except NoCardIdsProvidedError:
        raise
    except Exception as e:
        logger.error("[GACHA][SORT] 重新排序卡片失敗: %s", str(e), exc_info=True)
        raise


async def reset_card_sort_index(user_id: int, card_id: int) -> Dict[str, Any]:
    """(Async) 重置卡片的排序索引 (設為 NULL，相當於從排序列表中移除)"""
    try:
        pool = get_pool()
        if pool is None:
            raise BusinessError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                await user_collection_repository.set_custom_sort_index(
                    user_id, card_id, None, connection=conn
                )

        return {"card_id": card_id, "position": 0, "page": 1}
    except Exception as e:
        logger.error("[GACHA][SORT] 重置卡片排序索引失敗: %s", str(e), exc_info=True)
        raise


async def get_current_card_position(user_id: int, card_id: int) -> int:
    """(Async) 獲取卡片當前的位置"""
    # 根據規範，移除 try-except，讓錯誤自然冒泡
    card_info = await user_collection_repository.get_card_sort_info(user_id, card_id)
    return card_info["rank"] if card_info and card_info["sort_index"] is not None else 0
