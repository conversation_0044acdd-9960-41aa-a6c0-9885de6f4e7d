"""
Gacha系統許願命令 - COG 版本
實現/wish命令，整合用戶查看、添加、移除許願卡片及擴充槽位、提升力度等功能
"""

from typing import TYPE_CHECKING, Any, Dict, Tuple, Union, cast

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    BotType = Union[commands.Bot, commands.AutoShardedBot]
else:
    BotType = commands.Bot

from gacha.exceptions import BusinessError
from gacha.repositories.card import master_card_repository
from gacha.services import (
    encyclopedia_service,
    wish_service,
)
from gacha.views import utils as view_utils
from gacha.views.embeds.gacha.wish_embed_builder import WishEmbedBuilder
from gacha.views.ui_components.confirmation import ConfirmationView
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class WishView(BaseView):
    """
    整合版許願系統視圖
    提供添加、移除許願卡片，擴充槽位和提升力度等功能
    """

    def __init__(self, bot, user_id: int, wish_data: Dict[str, Any], timeout=180):
        """初始化許願視圖

        參數:
            bot: Bot 實例
            user_id: 用戶ID
            wish_data: 許願數據
            timeout: 超時時間（秒）
        """
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.wish_data = wish_data
        self.wish_slots = wish_data.get("wish_slots", 1)
        self.wish_power_level = wish_data.get("wish_power_level", 1)
        self.update_buttons()

    async def _refresh_view_data(self):
        """刷新視圖數據並更新按鈕狀態"""
        self.wish_data = await wish_service.get_wish_list(self.user_id)
        self.wish_slots = self.wish_data.get("wish_slots", 1)
        self.wish_power_level = self.wish_data.get("wish_power_level", 1)
        self.update_buttons()

    async def _update_original_view(self, interaction: discord.Interaction):
        """更新原始視圖的通用方法"""
        builder = WishEmbedBuilder(
            user=interaction.user,
            wishes=self.wish_data.get("wishes", []),
            wish_slots=self.wish_slots,
            wish_power_level=self.wish_power_level,
            used_slots=self.wish_data.get("used_slots", 0),
            nickname=self.wish_data.get("user_nickname"),
        )
        embed = builder.build_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    def update_buttons(self):
        """根據當前狀態更新按鈕"""
        current_slots = self.wish_data.get("wish_slots", 1)
        current_level = self.wish_data.get("wish_power_level", 1)
        used_slots = self.wish_data.get("used_slots", 0)

        # 使用字典映射按鈕狀態，更 Pythonic
        from config.app_config import get_wish_max_power_level, get_wish_max_slots

        max_wish_slots = get_wish_max_slots(default_value=10)
        max_wish_power_level = get_wish_max_power_level(default_value=10)

        button_states = {
            "expand": current_slots >= max_wish_slots,
            "powerup": current_level >= max_wish_power_level,
            "add": used_slots >= current_slots,
            "remove": used_slots <= 0,
        }

        # 批量更新按鈕狀態
        for item in self.children:
            if isinstance(item, discord.ui.Button) and item.custom_id in button_states:
                item.disabled = button_states[item.custom_id]

    @discord.ui.button(
        label="添加許願",
        style=discord.ButtonStyle.primary,
        emoji="✨",
        custom_id="add",
        row=0,
    )
    async def add_wish_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        select_modal = SelectWishMethodModal(
            title="選擇許願方式", parent_view=self, original_interaction=interaction
        )
        await interaction.response.send_modal(select_modal)
        await select_modal.wait()
        # Modal 會自行處理更新，這裡不需要額外操作

    @discord.ui.button(
        label="移除許願",
        style=discord.ButtonStyle.danger,
        emoji="🗑️",
        custom_id="remove",
        row=0,
    )
    async def remove_wish_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        modal = WishCardIdModal(
            title="移除許願卡片",
            action="remove",
            parent_view=self,
            original_interaction=interaction,
        )
        await interaction.response.send_modal(modal)
        await modal.wait()
        # Modal 會自行處理更新，這裡不需要額外操作

    @discord.ui.button(
        label="擴充槽位",
        style=discord.ButtonStyle.success,
        emoji="🔓",
        custom_id="expand",
        row=1,
    )
    async def expand_slot_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        # 先 defer 原始 interaction
        await interaction.response.defer()

        current_slots = self.wish_data.get("wish_slots", 1)
        cost = wish_service.calculate_next_slot_cost(current_slots)
        from config.app_config import get_wish_max_slots

        max_wish_slots = get_wish_max_slots(default_value=10)
        if current_slots >= max_wish_slots:
            embed = discord.Embed(
                title="無法擴充許願槽位",
                description=f"你已達到許願槽位上限 ({max_wish_slots}/{max_wish_slots})",
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        async def expand_slot_confirmed(
            confirm_interaction: discord.Interaction, confirmed: bool
        ):
            if not confirmed:
                cancel_embed = discord.Embed(
                    title="已取消擴充槽位",
                    description="操作已取消。",
                    color=discord.Color.orange(),
                )
                await confirm_interaction.response.edit_message(
                    embed=cancel_embed, view=None
                )
                return

            # 移除 try-except，讓錯誤冒泡到 BaseView.on_error
            result = await wish_service.expand_slot(self.user_id)
            await self._refresh_view_data()

            success_embed = SuccessEmbed(
                title="擴充許願槽位成功",
                description=f"槽位已擴充\n當前許願槽位: {result.get('new_slots')}",
            )
            await confirm_interaction.response.edit_message(
                embed=success_embed, view=None
            )
            await self._update_original_view(interaction)

        confirm_text = f"你確定要花費 **{cost}** 油幣擴充許願槽位嗎？\n當前: {current_slots}/{max_wish_slots}"

        # 創建確認嵌入
        embed = discord.Embed(
            title="確認擴充許願槽位",
            description=confirm_text,
            color=discord.Color.blue(),
        )
        embed.add_field(
            name="操作提示",
            value="點擊確認以花費油幣擴充許願槽位，或點擊取消放棄操作。",
            inline=False,
        )
        embed.set_footer(text="擴充許願槽位將增加可同時許願的卡片數量")

        # 創建確認視圖
        view = ConfirmationView(
            bot=cast(BotType, interaction.client),
            user_id=self.user_id,
            on_confirm=lambda confirm_interaction: expand_slot_confirmed(
                confirm_interaction, True
            ),
            on_cancel=lambda cancel_interaction: expand_slot_confirmed(
                cancel_interaction, False
            ),
        )

        await interaction.followup.send(embed=embed, view=view, ephemeral=True)

    @discord.ui.button(
        label="提升力度",
        style=discord.ButtonStyle.success,
        emoji="💪",
        custom_id="powerup",
        row=1,
    )
    async def power_up_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        # 先 defer 原始 interaction
        await interaction.response.defer()

        current_level = self.wish_data.get("wish_power_level", 1)
        cost = wish_service.calculate_next_power_cost(current_level)
        from config.app_config import get_wish_max_power_level

        max_wish_power_level = get_wish_max_power_level(default_value=10)
        if current_level >= max_wish_power_level:
            embed = discord.Embed(
                title="無法提升許願力度",
                description=f"你已達到許願力度上限 (Lv.{max_wish_power_level}/{max_wish_power_level})",
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        async def power_up_confirmed(
            confirm_interaction: discord.Interaction, confirmed: bool
        ):
            if not confirmed:
                cancel_embed = discord.Embed(
                    title="已取消提升力度",
                    description="操作已取消。",
                    color=discord.Color.orange(),
                )
                await confirm_interaction.response.edit_message(
                    embed=cancel_embed, view=None
                )
                return

            # 移除 try-except，讓錯誤冒泡到 BaseView.on_error
            result = await wish_service.power_up(self.user_id)
            await self._refresh_view_data()

            new_level = result.get("new_level", 1)
            multiplier = wish_service.get_wish_chance_multiplier(new_level)
            success_embed = SuccessEmbed(
                title="提升許願力度成功",
                description=f"力度已提升\n當前許願力度: {new_level}\n權重倍率: {multiplier}倍",
            )
            await confirm_interaction.response.edit_message(
                embed=success_embed, view=None
            )
            await self._update_original_view(interaction)

        confirm_text = f"你確定要花費 **{cost}** 油幣提升許願力度嗎？\n當前: Lv.{current_level}/{max_wish_power_level}\n提升後權重將從 {wish_service.get_wish_chance_multiplier(current_level)}倍 → {wish_service.get_wish_chance_multiplier(current_level + 1)}倍"

        # 創建確認嵌入
        embed = discord.Embed(
            title="確認提升許願力度",
            description=confirm_text,
            color=discord.Color.blue(),
        )
        embed.add_field(
            name="操作提示",
            value="點擊確認以花費油幣提升許願力度，或點擊取消放棄操作。",
            inline=False,
        )
        embed.set_footer(text="提升許願力度將增加許願卡片的抽取機率")

        # 創建確認視圖
        view = ConfirmationView(
            bot=cast(BotType, interaction.client),
            user_id=self.user_id,
            on_confirm=lambda confirm_interaction: power_up_confirmed(
                confirm_interaction, True
            ),
            on_cancel=lambda cancel_interaction: power_up_confirmed(
                cancel_interaction, False
            ),
        )

        await interaction.followup.send(embed=embed, view=view, ephemeral=True)


class SelectWishMethodModal(BaseModal):
    """選擇許願方式的模態框"""

    def __init__(
        self,
        title: str,
        parent_view: "WishView",
        original_interaction: discord.Interaction,
    ):
        super().__init__(bot=parent_view.bot, title=title)
        self.parent_view = parent_view
        self.original_interaction = original_interaction
        self.submitted = False
        self.wish_added = False
        self.select_method = discord.ui.TextInput(
            label="輸入卡片ID",
            placeholder="請直接輸入要許願的卡片ID",
            min_length=1,
            max_length=15,
            required=True,
            style=discord.TextStyle.short,
        )
        self.add_item(self.select_method)

    async def on_submit(self, interaction: discord.Interaction):
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # on_error 會處理 BusinessError 和其他 Exception
        await interaction.response.defer(ephemeral=True, thinking=True)
        card_id_input = self.select_method.value.strip()

        # 這裡的 ValueError 會被 on_error 捕捉並記錄，但不會顯示給用戶，這是預期行為
        # 因為用戶輸入錯誤屬於開發者需要知道的非預期行為（前端應已驗證）
        # 如果要給用戶提示，應在 service 層拋出 BusinessError
        card_id = int(card_id_input)

        await wish_service.add_wish(interaction.user.id, card_id)

        embed = SuccessEmbed(
            title="添加許願成功",
            description="卡片已添加到許願列表",
        )
        self.wish_added = True
        await interaction.followup.send(embed=embed, ephemeral=True)

        # 更新父視圖
        if self.parent_view and self.original_interaction:
            await self._update_parent_view()

        self.submitted = True

    async def _update_parent_view(self):
        """更新父視圖"""
        if self.wish_added and self.parent_view and self.original_interaction:
            await self.parent_view._refresh_view_data()
            await self.parent_view._update_original_view(self.original_interaction)


class CardBrowseView(BaseView):
    """卡片瀏覽視圖，用於瀏覽圖鑑並許願"""

    def __init__(
        self,
        bot: commands.Bot,
        user_id: int,
        current_page: int,
        total_pages: int,
        current_card_id: int,
        timeout=180,
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.current_page = current_page
        self.total_pages = total_pages
        self.current_card_id = current_card_id
        self.wish_added = False

    # interaction_check 已由 BaseView 提供

    @discord.ui.button(
        label="上一張",
        style=discord.ButtonStyle.secondary,
        emoji="⬅️",
        custom_id="prev_card",
        row=0,
    )
    async def prev_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        self.current_page = max(1, self.current_page - 1)
        await self._update_card_view(interaction)

    @discord.ui.button(
        label="下一張",
        style=discord.ButtonStyle.secondary,
        emoji="➡️",
        custom_id="next_card",
        row=0,
    )
    async def next_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        self.current_page = min(self.total_pages, self.current_page + 1)
        await self._update_card_view(interaction)

    @discord.ui.button(
        label="許願此卡",
        style=discord.ButtonStyle.primary,
        emoji="✨",
        custom_id="wish_this_card",
        row=1,
    )
    async def wish_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        # 移除 try-except，讓錯誤冒泡到 BaseView.on_error
        await interaction.response.defer(ephemeral=True)
        await wish_service.add_wish(self.user_id, self.current_card_id)
        embed = SuccessEmbed(
            title="添加許願成功",
            description="卡片已添加到許願列表",
        )
        self.wish_added = True
        await interaction.followup.send(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="關閉",
        style=discord.ButtonStyle.danger,
        emoji="❌",
        custom_id="close_browse",
        row=1,
    )
    async def close_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        for child in self.children:
            if isinstance(child, discord.ui.Button):
                child.disabled = True
        await interaction.response.defer()
        if interaction.message:
            await interaction.edit_original_response(view=self)
        self.stop()

    async def _update_card_view(self, interaction: discord.Interaction):
        page_data = await encyclopedia_service.get_card_for_page(page=self.current_page)
        card_id = page_data.get("card_id")
        if not card_id:
            raise BusinessError("無法載入卡片圖鑑，請稍後再試。")
        self.current_card_id = card_id
        card_object = await master_card_repository.get_card(card_id)
        if not card_object:
            raise BusinessError(f"無法獲取卡片ID {card_id} 的信息，請稍後再試。")
        rarity_level_enum = card_object.rarity
        rarity_display = (
            view_utils.get_user_friendly_rarity_name(rarity_level_enum)
            if rarity_level_enum is not None
            else "未知"
        )
        embed = discord.Embed(
            title=f"卡片圖鑑 - {card_object.name or '未知'}",
            description=f"ID: {card_id} | 系列: {card_object.series or '未知'} | 稀有度: {rarity_display}",
            color=discord.Color.blue(),
        )
        if card_object.image_url:
            embed.set_image(url=card_object.image_url)
        embed.set_footer(text=f"頁碼: {self.current_page}/{self.total_pages}")
        await interaction.response.edit_message(embed=embed, view=self)


class WishCardIdModal(BaseModal):
    """許願卡片ID輸入模態框"""

    def __init__(
        self,
        title: str,
        action: str,
        parent_view: "WishView",
        original_interaction: discord.Interaction,
    ):
        super().__init__(bot=parent_view.bot, title=title)
        self.action = action
        self.parent_view = parent_view
        self.original_interaction = original_interaction
        self.submitted = False
        self.operation_success = False
        self.card_id_input = discord.ui.TextInput(
            label="卡片ID",
            placeholder="請輸入卡片ID（數字）",
            min_length=1,
            max_length=10,
            required=True,
            style=discord.TextStyle.short,
        )
        self.add_item(self.card_id_input)

    def _validate_card_id(self, card_id_input: str) -> int:
        """驗證並轉換卡片ID"""
        try:
            return int(card_id_input.strip())
        except ValueError as e:
            raise BusinessError("請輸入有效的卡片ID（數字）。") from e

    async def _execute_wish_action(
        self, user_id: int, card_id: int
    ) -> Tuple[str, discord.Embed]:
        """執行許願操作並返回結果"""
        if self.action == "add":
            await wish_service.add_wish(user_id, card_id)
            action_text = "添加許願"
            embed = SuccessEmbed(
                title=f"{action_text}成功",
                description="卡片已添加到許願列表",
            )
            self.operation_success = True
        elif self.action == "remove":
            await wish_service.remove_wish(user_id, card_id)
            action_text = "移除許願"
            embed = SuccessEmbed(
                title=f"{action_text}成功",
                description="卡片已從許願列表中移除",
            )
            self.operation_success = True
        else:
            raise ValueError("未知的操作類型。")

        return action_text, embed

    def _create_error_embed(
        self, action_text: str, error_message: str
    ) -> discord.Embed:
        """創建錯誤嵌入"""
        return discord.Embed(
            title=f"{action_text}失敗",
            description=error_message,
            color=discord.Color.red(),
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)

        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # on_error 會處理 BusinessError 和其他 Exception
        card_id = self._validate_card_id(self.card_id_input.value)

        # 執行許願操作
        _, embed = await self._execute_wish_action(interaction.user.id, card_id)
        await interaction.followup.send(embed=embed, ephemeral=True)

        # 更新父視圖
        if self.operation_success and self.parent_view and self.original_interaction:
            await self._update_parent_view()

        self.submitted = True

    async def _update_parent_view(self):
        """更新父視圖"""
        if self.operation_success and self.parent_view and self.original_interaction:
            await self.parent_view._refresh_view_data()
            await self.parent_view._update_original_view(self.original_interaction)


class WishCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("WishCog initialized.")

    @app_commands.command(name="wish", description="查看你的許願列表")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def wish_command(self, interaction: discord.Interaction):
        """顯示用戶的許願列表"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        await interaction.response.defer(ephemeral=True)
        user_id = interaction.user.id

        wish_data = await wish_service.get_wish_list(user_id)
        nickname = interaction.user.display_name
        builder = WishEmbedBuilder(
            user=interaction.user,
            wishes=wish_data.get("wishes", []),
            wish_slots=wish_data.get("wish_slots", 1),
            wish_power_level=wish_data.get("wish_power_level", 1),
            used_slots=wish_data.get("used_slots", 0),
            nickname=nickname,
        )
        embed = builder.build_embed()
        view = WishView(bot=self.bot, user_id=user_id, wish_data=wish_data)
        await interaction.followup.send(embed=embed, view=view)


async def setup(bot: commands.Bot):
    await bot.add_cog(WishCog(bot))
    logger.info("WishCog 已成功加載。")
