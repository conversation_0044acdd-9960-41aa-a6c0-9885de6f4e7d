"""
Gacha系統轉盤賭博遊戲 COG
處理轉盤遊戲的Discord命令和交互
"""

import asyncio
import json
import random
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button

import gacha.services.economy_service as economy_service
import gacha.services.game_stats_service as game_stats_service
from gacha.exceptions import BusinessError, GameError, InsufficientBalanceError
from utils.base_view import BaseView
from utils.logger import logger

# 從設計文檔中獲取的配置
RISK_CONFIGS = {
    "低": {
        "name": "低風險",
        "win_sectors": 6,
        "total_sectors": 8,
        "win_rate": 0.75,
        "payout_multipliers": {"small_win": 1.1, "medium_win": 1.4, "big_win": 1.8},
    },
    "中": {
        "name": "中風險",
        "win_sectors": 4,
        "total_sectors": 8,
        "win_rate": 0.50,
        "payout_multipliers": {"small_win": 1.2, "medium_win": 2.1, "big_win": 3.05},
    },
    "高": {
        "name": "高風險",
        "win_sectors": 2,
        "total_sectors": 8,
        "win_rate": 0.25,
        "payout_multipliers": {"small_win": 2.0, "big_win": 5.4},
    },
}

SECTOR_DISTRIBUTION = {
    "低": {"small_win": 4, "medium_win": 1, "big_win": 1, "lose": 2},
    "中": {"small_win": 2, "medium_win": 1, "big_win": 1, "lose": 4},
    "高": {"small_win": 1, "big_win": 1, "lose": 6},
}

# 新增：每個扇區精確的動畫時間點
SECTOR_TIMINGS = {
    "low": {
        "loop_duration": 1.017,
        "timings": {
            "spin-wheel_low_big_win_7_still.png": 1.016,
            "spin-wheel_low_lose_1_still.png": 0.078,
            "spin-wheel_low_lose_3_still.png": 0.055,
            "spin-wheel_low_medium_win_4_still.png": 0.036,
            "spin-wheel_low_small_win_2_still.png": 0.064,
            "spin-wheel_low_small_win_5_still.png": 0.025,
            "spin-wheel_low_small_win_6_still.png": 0.005,
            "spin-wheel_low_small_win_8_still.png": 0.095,
        },
    },
    "medium": {
        "loop_duration": 1.018,
        "timings": {
            "spin-wheel_medium_big_win_8_still.png": 0.097,
            "spin-wheel_medium_lose_2_still.png": 0.069,
            "spin-wheel_medium_lose_3_still.png": 0.049,
            "spin-wheel_medium_lose_5_still.png": 0.026,
            "spin-wheel_medium_lose_6_still.png": 0.006,
            "spin-wheel_medium_medium_win_4_still.png": 0.043,
            "spin-wheel_medium_small_win_1_still.png": 0.086,
            "spin-wheel_medium_small_win_7_still.png": 1.018,
        },
    },
    "high": {
        "loop_duration": 1.018,
        "timings": {
            "spin-wheel_high_big_win_7_still.png": 1.010,
            "spin-wheel_high_lose_1_still.png": 0.083,
            "spin-wheel_high_lose_2_still.png": 0.070,
            "spin-wheel_high_lose_3_still.png": 0.049,
            "spin-wheel_high_lose_5_still.png": 0.026,
            "spin-wheel_high_lose_6_still.png": 0.004,
            "spin-wheel_high_lose_8_still.png": 1.001,
            "spin-wheel_high_small_win_4_still.png": 0.041,
        },
    },
}

# 常量定義
RISK_MAPPING = {"低": "low", "中": "medium", "高": "high"}
DEFAULT_SPIN_DELAY = 3.5
BASE_LOOPS = 2
DISCORD_LATENCY_COMPENSATION = 0.2
TIMING_ADJUSTMENT_OFFSET = 0.05

# CDN 連結將從 JSON 文件中加載
CDN_LINKS = {}


def load_cdn_links():
    global CDN_LINKS
    cdn_file_path = "gacha/cogs/spin/spin_cdn_link.json"
    try:
        with open(cdn_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            CDN_LINKS = data.get("cdn_links", {})
        logger.info("成功加載 %s 個轉盤遊戲 CDN 連結。", len(CDN_LINKS))
    except FileNotFoundError:
        logger.error("錯誤：未找到 %s。轉盤圖片將無法顯示。", cdn_file_path)
    except json.JSONDecodeError:
        logger.error("錯誤：解析 %s 失敗。", cdn_file_path)
    except Exception as e:
        logger.error("加載 CDN 連結時發生未知錯誤: %s", e)


class GameResult(Enum):
    """遊戲結果枚舉"""

    SMALL_WIN = "small_win"
    MEDIUM_WIN = "medium_win"
    BIG_WIN = "big_win"
    LOSE = "lose"


@dataclass
class SpinResult:
    """轉盤遊戲結果的數據類"""

    result_type: GameResult
    payout_multiplier: float
    payout: int
    profit: int
    new_balance: int
    risk: str
    bet: int
    still_image_url: str


class SpinWheelView(BaseView):
    """轉盤遊戲交互視圖，實現一次性交互原則"""

    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        cog,
        original_bet: int,
        original_risk: str,
        current_balance: Optional[int] = None,
    ):
        super().__init__(bot=cog.bot, user_id=user.id, timeout=180)
        self.user = user
        self.cog = cog
        self.original_bet = original_bet
        self.original_risk = original_risk
        self.current_balance = current_balance
        self.triggered = False  # 核心狀態標誌，防止競爭條件

        # 按鈕配置: (標籤, 下注乘數, 風險, 樣式)
        button_configs = [
            ("🎰 再來一局", 1, self.original_risk, discord.ButtonStyle.green),
            ("💰 雙倍下注", 2, self.original_risk, discord.ButtonStyle.blurple),
        ]

        for label, bet_multiplier, risk, style in button_configs:
            new_bet = self.original_bet * bet_multiplier

            # 檢查餘額是否足夠
            has_balance = (
                self.current_balance is None or self.current_balance >= new_bet
            )

            button = Button(
                label=f"{label} ({new_bet:,} 油幣)",
                style=style if has_balance else discord.ButtonStyle.gray,
                custom_id=f"spin_wheel:replay:{bet_multiplier}:{risk}",
                disabled=not has_balance,
            )
            # 使用 lambda 捕獲正確的 new_bet 和 risk
            # Type ignore needed for dynamic callback assignment
            button.callback = lambda i, b=new_bet: self._replay_callback(i, b)  # type: ignore
            self.add_item(button)

    async def _replay_callback(self, interaction: discord.Interaction, new_bet: int):
        """重玩按鈕回調"""
        # 關鍵檢查：如果視圖已被觸發，則拒絕新的交互
        if self.triggered:
            raise BusinessError("⚙️ 你已經開始了新的遊戲，這個按鈕已失效。")

        # 新增：在回調中直接檢查餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < new_bet:
            raise InsufficientBalanceError(required=new_bet, current=current_balance)

        # 1. 立即鎖定，此操作必須在任何 await 之前同步完成
        self.triggered = True

        # 2. 立即停止視圖，讓 discord.py 停止監聽，作為第二層保險
        self.stop()

        # 3. 響應交互，確認收到點擊並防止交互超時
        await interaction.response.defer()

        # 4. 委派任務給 Cog 的核心邏輯
        await self.cog._start_game_logic(
            interaction, new_bet, self.original_risk, is_replay=True
        )

    async def on_timeout(self):
        """超時後，簡單地停止視圖以清理監聽器"""
        self.stop()


class SpinWheelCog(commands.Cog):
    """處理轉盤賭博遊戲命令的COG"""

    MIN_BET = 100
    SPIN_ICON_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1389302266284085280/roulette.png?ex=68642010&is=6862ce90&hm=4e8f22235dde20f29d26e7eceeb225fcc1a9ccaa0b569fd8b21efcc7074e9e68&"

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        load_cdn_links()

    def _determine_result(self, risk: str) -> tuple[GameResult, float]:
        """根據風險決定遊戲結果和倍率 (優化版)"""
        distribution = SECTOR_DISTRIBUTION[risk]
        multipliers_raw = RISK_CONFIGS[risk]["payout_multipliers"]
        multipliers: dict[str, float] = (
            multipliers_raw if isinstance(multipliers_raw, dict) else {}
        )

        # 使用 random.choices 搭配權重，更高效
        outcomes = list(distribution.keys())
        weights = list(distribution.values())

        # 隨機選擇一個結果
        chosen_outcome_str = random.choices(outcomes, weights=weights, k=1)[0]

        if chosen_outcome_str == "lose":
            return GameResult.LOSE, 0.0

        result_type = GameResult(chosen_outcome_str)
        multiplier_value = multipliers[chosen_outcome_str]
        if isinstance(multiplier_value, (str, int, float)):
            payout_multiplier = float(multiplier_value)
        else:
            payout_multiplier = multiplier_value
        return result_type, payout_multiplier

    def _get_image_urls(
        self, risk: str, result: GameResult
    ) -> tuple[Optional[str], Optional[str], Optional[str]]:
        """根據結果獲取對應的循環、靜態圖片URL和靜態圖片的鍵名"""
        if not CDN_LINKS:
            return None, None, None

        risk_lower = RISK_MAPPING.get(risk)
        if not risk_lower:
            logger.error("未知的風險等級: %s", risk)
            return None, None, None

        result_str = result.value

        # 1. 獲取循環動畫 URL
        loop_key = (
            f"spin-wheel-assets_{risk_lower}\\"
            f"spin-wheel_spinning_loop_{risk_lower}.webp"
        )
        loop_url = CDN_LINKS.get(loop_key)

        # 2. 獲取靜態結果圖片 URL
        matching_still_files = [
            k
            for k in CDN_LINKS.keys()
            if f"spin-wheel_{risk_lower}_{result_str}_" in k and "_still.png" in k
        ]

        if not matching_still_files:
            logger.warning(
                f"找不到對應的靜態圖片資源: risk={risk_lower}, result={result_str}"
            )
            return None, None, None

        still_key = random.choice(matching_still_files)
        still_url = CDN_LINKS.get(still_key)

        if not loop_url or not still_url:
            logger.warning("CDN連結缺失: loop='%s', still='%s'", loop_key, still_key)
            return None, None, None

        return loop_url, still_url, still_key

    async def _update_game_stats(self, user_id: int, result_data: SpinResult):
        """更新遊戲統計數據"""
        # 統一參數名稱，與 SpinResult 和其他遊戲保持一致
        game_data = {
            "bet": result_data.bet,
            "payout": result_data.payout,
            "profit": result_data.profit,
            "result": (
                "win"
                if result_data.profit > 0
                else ("push" if result_data.profit == 0 else "lose")
            ),
            "risk": result_data.risk,
            "payout_multiplier": result_data.payout_multiplier,
            "result_type": result_data.result_type.value,
        }
        await game_stats_service.record_game_result(user_id, "spin_wheel", game_data)

    async def _get_user_stats(self, user_id: int) -> Optional[Dict[str, Any]]:
        """獲取用戶統計數據"""
        stats = await game_stats_service.get_user_game_stats(user_id, "spin_wheel")
        return stats

    def _calculate_spin_delay(self, risk: str, still_key: str) -> float:
        """計算轉盤動畫延遲時間"""
        try:
            risk_lower = RISK_MAPPING.get(risk)
            if not risk_lower:
                return DEFAULT_SPIN_DELAY

            still_filename = still_key.split("\\")[-1]
            risk_timing_data = SECTOR_TIMINGS[risk_lower]
            loop_duration_value = risk_timing_data["loop_duration"]
            if isinstance(loop_duration_value, (str, int, float)):
                loop_duration = float(loop_duration_value)
            else:
                # Fallback to default if not a numeric type
                loop_duration = DEFAULT_SPIN_DELAY
            timings = risk_timing_data.get("timings", {})
            if isinstance(timings, dict) and still_filename in timings:
                timing_value = timings[still_filename]
                if isinstance(timing_value, (str, int, float)):
                    target_time = float(timing_value)
                else:
                    # Fallback to default if not a numeric type
                    target_time = DEFAULT_SPIN_DELAY
            else:
                target_time = DEFAULT_SPIN_DELAY

            total_delay = (
                (BASE_LOOPS * loop_duration)
                + target_time
                - DISCORD_LATENCY_COMPENSATION
                - TIMING_ADJUSTMENT_OFFSET
            )

            return max(total_delay, target_time)
        except (KeyError, IndexError) as e:
            logger.error(
                (
                    f"計算轉盤精準延遲時出錯: risk={risk}, "
                    f"still_key='{still_key}', error={e}. 使用預設延遲。"
                ),
                exc_info=True,
            )
            return DEFAULT_SPIN_DELAY

    def _create_result_description_and_color(
        self, profit: int, bet: int, win_rate: float, total_games: int
    ) -> tuple[list[str], str, discord.Color]:
        """創建結果描述和顏色"""
        description_parts = []

        if profit > 0:
            result_title = "恭喜獲勝！"
            result_color = discord.Color.green()
            description_parts.append(f"✅ 獲得{profit:,} <:oi:1382174314811363470>！")
        elif profit < 0:
            result_title = "很遺憾，再接再厲！"
            result_color = discord.Color.red()
            description_parts.append(
                f"❌ 損失{abs(profit):,} <:oi:1382174314811363470>！"
            )
        else:
            result_title = "平局！"
            result_color = discord.Color.blue()
            description_parts.append(f"🔄 平局，返還{bet:,} <:oi:1382174314811363470>")

        description_parts.append(
            f"<a:a_:1382444741798658228> 勝率`{win_rate}`% 總場次`{total_games}`"
        )

        return description_parts, result_title, result_color

    async def _start_game_logic(
        self,
        interaction: discord.Interaction,
        bet: int,
        risk: str,
        is_replay: bool = False,
    ):
        """遊戲核心邏輯"""
        user = interaction.user

        if not is_replay:
            await interaction.response.defer()

        # 1. 檢查餘額並扣款
        balance_info = await economy_service.get_balance(user.id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < bet:
            raise InsufficientBalanceError(required=bet, current=current_balance)
        await economy_service.award_oil(
            user_id=user.id,
            amount=-bet,
            transaction_type="game:spin_bet",
            reason="Spin bet",
        )

        # 2. 決定遊戲結果
        result_type, payout_multiplier = self._determine_result(risk)
        payout = int(bet * payout_multiplier)
        profit = payout - bet

        # 3. 獲取圖片資源
        loop_url, still_image_url, still_key = self._get_image_urls(risk, result_type)
        if not loop_url or not still_image_url or not still_key:
            raise GameError("找不到遊戲動畫資源，請聯繫管理員。")

        # 4. 階段1：顯示轉動動畫
        initial_stats = await self._get_user_stats(user.id)
        initial_total_profit_loss = (
            initial_stats.get("total_profit_loss", 0) if initial_stats else 0
        )
        balance_after_bet = balance_info["balance"] - bet

        prep_embed = discord.Embed(color=0x5865F2)
        prep_embed.set_author(name="轉盤轉動中...", icon_url=self.SPIN_ICON_URL)
        prep_embed.set_image(url=loop_url)
        footer_text = (
            f"下注: {bet:,} | 餘額: {balance_after_bet:,} | "
            f"總盈虧: {initial_total_profit_loss:,}"
        )
        prep_embed.set_footer(text=footer_text, icon_url=user.display_avatar.url)

        game_message: Union[discord.WebhookMessage, discord.InteractionMessage]
        game_message: Union[discord.WebhookMessage, discord.InteractionMessage]
        if is_replay:
            # 重玩時，交互已在視圖中響應 (defer)，因此我們使用 followup 發送新消息
            game_message = await interaction.followup.send(embed=prep_embed, wait=True)
        else:
            # 首次遊戲，我們編輯原始的延遲響應
            await interaction.edit_original_response(embed=prep_embed)
            game_message = await interaction.original_response()

        # 5. 計算動態延遲並等待
        delay = self._calculate_spin_delay(risk, still_key)
        await asyncio.sleep(delay)

        # 6. 處理獎勵和最終餘額
        if payout > 0:
            await economy_service.award_oil(
                user_id=user.id,
                amount=payout,
                transaction_type="game:spin_win",
                reason="Spin win",
            )
        final_balance_info = await economy_service.get_balance(user.id)
        new_balance = final_balance_info.get("balance", 0)

        # 7. 更新統計
        result_data = SpinResult(
            result_type=result_type,
            payout_multiplier=payout_multiplier,
            payout=payout,
            profit=profit,
            new_balance=new_balance,
            risk=risk,
            bet=bet,
            still_image_url=still_image_url,
        )
        await self._update_game_stats(user.id, result_data)

        # 8. 獲取更新後的統計數據
        final_stats = await self._get_user_stats(user.id)
        total_games = final_stats.get("total_games", 0) if final_stats else 0
        total_wins = final_stats.get("total_wins", 0) if final_stats else 0
        total_profit_loss = (
            final_stats.get("total_profit_loss", 0) if final_stats else 0
        )
        win_rate = round((total_wins / total_games) * 100, 1) if total_games > 0 else 0

        # 9. 階段3：顯示最終結果
        description_parts, result_title, result_color = (
            self._create_result_description_and_color(
                profit, bet, win_rate, total_games
            )
        )

        final_embed = discord.Embed(
            description="\n".join(description_parts), color=result_color
        )
        final_embed.set_author(name=result_title, icon_url=self.SPIN_ICON_URL)
        final_embed.set_image(url=still_image_url)

        footer_text = (
            f"下注: {bet:,} | 餘額: {new_balance:,} | 總盈虧: {total_profit_loss:,}"
        )
        final_embed.set_footer(text=footer_text, icon_url=user.display_avatar.url)

        # 10. 顯示交互按鈕
        view = SpinWheelView(user, self, bet, risk, new_balance)
        await game_message.edit(embed=final_embed, view=view)

    @app_commands.command(name="spin", description="開始轉盤賭博")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        bet=f"下注金額（最低{MIN_BET}油幣）", risk="風險等級：低/中/高"
    )
    @app_commands.choices(
        risk=[
            app_commands.Choice(name="低風險 (75%勝率)", value="低"),
            app_commands.Choice(name="中風險 (50%勝率)", value="中"),
            app_commands.Choice(name="高風險 (25%勝率)", value="高"),
        ]
    )
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def spin(
        self,
        interaction: discord.Interaction,
        bet: app_commands.Range[int, MIN_BET],
        risk: str,
    ):
        """處理 /spin 指令"""
        await self._start_game_logic(interaction, bet, risk, is_replay=False)


async def setup(bot: commands.Bot):
    """將COG添加到Bot中"""
    await bot.add_cog(SpinWheelCog(bot))
    logger.info("SpinWheelCog 已成功加載並註冊。")
