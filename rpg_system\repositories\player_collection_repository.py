"""
玩家卡牌收藏RPG相關數據訪問層 - 重構版
實現文檔中的簡單設計：只提供基礎的單表查詢方法
"""

import json
from typing import Any, Dict, List

from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    CardNotFoundError,
    DatabaseOperationError,
    EntityNotFoundError,
)
from gacha.repositories import _base_repo
from utils.logger import logger

# 卡牌ID範圍常量
DEFAULT_CARD_MIN = 1
DEFAULT_CARD_MAX = 999999
SPECIAL_CARD_MIN = 1000000
SPECIAL_CARD_MAX = 9999999999


def is_special_card(card_id: int) -> bool:
    """判斷是否為特殊卡"""
    return card_id >= SPECIAL_CARD_MIN


async def generate_next_special_card_id() -> int:
    """生成下一個特殊卡ID"""
    async with get_pool().acquire() as conn:
        max_id = await conn.fetchval(
            "SELECT MAX(card_id) FROM rpg_card_special_configs"
        )
        return max(SPECIAL_CARD_MIN, (max_id or SPECIAL_CARD_MIN - 1) + 1)


async def get_default_cards(user_id: int) -> List[Dict[str, Any]]:
    """獲取用戶的默認卡"""
    query = """
        SELECT
            guc.id,
            guc.user_id,
            guc.card_id,
            guc.quantity,
            guc.is_favorite,
            guc.star_level,
            guc.equipped_active_skill_ids,
            guc.equipped_common_passives,
            gmc.name,
            gmc.series,
            gmc.rarity,
            gmc.image_url,
            gmc.pool_type
        FROM gacha_user_collections guc
        JOIN gacha_master_cards gmc ON guc.card_id = gmc.card_id
        WHERE guc.user_id = $1 AND guc.quantity > 0
        ORDER BY gmc.rarity DESC, guc.card_id
    """
    try:
        rows = await _base_repo.fetch_all(query, (user_id,))
        return [dict(row) for row in rows] if rows else []
    except Exception as e:
        logger.error("獲取默認卡失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取默認卡失敗: {str(e)}") from e


async def get_special_cards(user_id: int) -> List[Dict[str, Any]]:
    """獲取用戶的特殊卡"""
    query = """
        SELECT
            rsc.id,
            rsc.user_id,
            rsc.card_id,
            rsc.rpg_level,
            rsc.rpg_xp,
            rsc.equipped_active_skill_ids,
            rsc.equipped_common_passives,
            rsc.star_level,
            rsc.is_favorite,
            rsc.source_collection_id,
            rsc.instance_name,
            rsc.name,
            rsc.series,
            rsc.rarity,
            rsc.image_url,
            rsc.pool_type
        FROM rpg_card_special_configs rsc
        WHERE rsc.user_id = $1
        ORDER BY rsc.rarity DESC, rsc.card_id
    """
    try:
        rows = await _base_repo.fetch_all(query, (user_id,))
        return [dict(row) for row in rows] if rows else []
    except Exception as e:
        logger.error("獲取特殊卡失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取特殊卡失敗: {str(e)}") from e


async def get_default_card(user_id: int, card_id: int) -> Dict[str, Any]:
    """獲取指定的默認卡"""
    query = """
        SELECT * FROM gacha_user_collections
        WHERE user_id = $1 AND card_id = $2 AND quantity > 0
    """
    try:
        row = await _base_repo.fetch_one(query, (user_id, card_id))
        if not row:
            raise CardNotFoundError(f"找不到用戶 {user_id} 的卡牌 {card_id}")
        return dict(row)
    except CardNotFoundError:
        raise
    except Exception as e:
        logger.error(
            "獲取默認卡失敗: user_id=%s, card_id=%s, 錯誤: %s", user_id, card_id, e
        )
        raise DatabaseOperationError(f"獲取默認卡失敗: {str(e)}") from e


async def get_special_card(card_id: int) -> Dict[str, Any]:
    """獲取指定的特殊卡，如果找不到則拋出CardNotFoundError"""
    query = "SELECT * FROM rpg_card_special_configs WHERE card_id = $1"
    try:
        row = await _base_repo.fetch_one(query, (card_id,))
        if not row:
            raise CardNotFoundError(f"找不到ID為 {card_id} 的特殊卡")
        return dict(row)
    except CardNotFoundError:
        raise
    except Exception as e:
        logger.error("獲取特殊卡失敗: card_id=%s, 錯誤: %s", card_id, e)
        raise DatabaseOperationError(f"獲取特殊卡失敗: {str(e)}") from e


async def get_cards_with_skill(user_id: int, skill_id: str) -> List[Dict[str, Any]]:
    """獲取擁有指定技能的所有卡牌（用於批量獻祭）"""
    query = """
        -- 默認卡
        SELECT
            guc.id,
            guc.card_id,
            'default' as card_type,
            guc.quantity,
            guc.is_favorite,
            guc.star_level,
            guc.equipped_active_skill_ids,
            guc.equipped_common_passives,
            gmc.name,
            gmc.series,
            gmc.rarity
        FROM gacha_user_collections guc
        JOIN gacha_master_cards gmc ON guc.card_id = gmc.card_id
        WHERE guc.user_id = $1 AND guc.quantity > 0
        AND (guc.equipped_active_skill_ids::jsonb ? $2 OR guc.equipped_common_passives::jsonb ? $2)

        UNION ALL

        -- 特殊卡
        SELECT
            rsc.id,
            rsc.card_id,
            'special' as card_type,
            1 as quantity,
            rsc.is_favorite,
            rsc.star_level,
            rsc.equipped_active_skill_ids,
            rsc.equipped_common_passives,
            rsc.name,
            rsc.series,
            rsc.rarity
        FROM rpg_card_special_configs rsc
        WHERE rsc.user_id = $1
        AND (rsc.equipped_active_skill_ids::jsonb ? $2 OR rsc.equipped_common_passives::jsonb ? $2)

        ORDER BY rarity DESC, card_id
    """

    try:
        rows = await _base_repo.fetch_all(query, (user_id, skill_id))
        return [dict(row) for row in rows] if rows else []
    except Exception as e:
        logger.error(
            "按技能查詢卡牌失敗: user_id=%s, skill_id=%s, 錯誤: %s",
            user_id,
            skill_id,
            e,
        )
        raise DatabaseOperationError(f"按技能查詢卡牌失敗: {str(e)}") from e


async def get_all_user_cards(user_id: int) -> List[Dict[str, Any]]:
    """獲取用戶所有卡牌（默認卡+特殊卡）- 使用UNION ALL"""
    query = """
        -- 默認卡
        SELECT
            guc.id,
            guc.user_id,
            guc.card_id,
            guc.quantity,
            guc.is_favorite,
            guc.star_level,
            1 as rpg_level,  -- 默認卡固定等級1
            0 as rpg_xp,     -- 默認卡固定經驗0
            guc.equipped_active_skill_ids,
            guc.equipped_common_passives,
            'default' as card_type,
            gmc.name,
            gmc.series,
            gmc.rarity,
            gmc.image_url,
            gmc.pool_type
        FROM gacha_user_collections guc
        JOIN gacha_master_cards gmc ON guc.card_id = gmc.card_id
        WHERE guc.user_id = $1 AND guc.quantity > 0

        UNION ALL

        -- 特殊卡
        SELECT
            rsc.id,
            rsc.user_id,
            rsc.card_id,
            1 as quantity,  -- 特殊卡固定數量1
            rsc.is_favorite,
            rsc.star_level,
            rsc.rpg_level,
            rsc.rpg_xp,
            rsc.equipped_active_skill_ids,
            rsc.equipped_common_passives,
            'special' as card_type,
            rsc.name,
            rsc.series,
            rsc.rarity,
            rsc.image_url,
            rsc.pool_type
        FROM rpg_card_special_configs rsc
        WHERE rsc.user_id = $1

        ORDER BY rarity DESC, card_id, card_type DESC  -- 特殊卡優先顯示
    """
    try:
        rows = await _base_repo.fetch_all(query, (user_id,))
        return [dict(row) for row in rows] if rows else []
    except Exception as e:
        logger.error("獲取用戶所有卡牌失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶所有卡牌失敗: {str(e)}") from e


# ==================== 戰鬥和技能修改的核心方法 ====================


async def separate_card_for_battle(
    user_id: int, source_card_id: int, exp_gained: int
) -> int:
    """從默認卡分離出特殊卡並添加經驗"""
    pool = get_pool()
    if not pool:
        raise DatabaseOperationError("Database pool is not initialized.")
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 1. 檢查是否有默認卡
            default_card = await conn.fetchrow(
                "SELECT * FROM gacha_user_collections WHERE user_id = $1 AND card_id = $2 AND quantity > 0",
                user_id,
                source_card_id,
            )
            if not default_card:
                raise CardNotFoundError("沒有可用的默認卡")

            # 2. 減少默認卡數量
            new_quantity = default_card["quantity"] - 1
            if new_quantity > 0:
                await conn.execute(
                    "UPDATE gacha_user_collections SET quantity = $1 WHERE id = $2",
                    new_quantity,
                    default_card["id"],
                )
            else:
                await conn.execute(
                    "DELETE FROM gacha_user_collections WHERE id = $1",
                    default_card["id"],
                )

            # 3. 獲取卡牌基本信息
            master_card = await conn.fetchrow(
                "SELECT * FROM gacha_master_cards WHERE card_id = $1",
                source_card_id,
            )

            # 4. 生成新的特殊卡ID
            special_card_id = await generate_next_special_card_id()

            # 5. 創建獨立的特殊卡
            await conn.execute(
                """
                INSERT INTO rpg_card_special_configs
                (card_id, user_id, name, series, rarity, image_url, pool_type,
                 rpg_level, rpg_xp, equipped_active_skill_ids, equipped_common_passives,
                 star_level, is_favorite, quantity)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """,
                special_card_id,
                user_id,
                master_card["name"],
                master_card["series"],
                master_card["rarity"],
                master_card["image_url"],
                master_card["pool_type"],
                1,
                exp_gained,  # 從等級1開始，帶有戰鬥經驗
                default_card["equipped_active_skill_ids"],
                default_card["equipped_common_passives"],
                default_card["star_level"],
                default_card["is_favorite"],
                1,
            )

            return special_card_id


async def separate_card_for_skills(
    user_id: int, source_card_id: int, new_skills: Dict
) -> int:
    """從默認卡分離出特殊卡並設置技能"""
    pool = get_pool()
    if not pool:
        raise DatabaseOperationError("Database pool is not initialized.")
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 1. 減少默認卡數量
            default_card = await conn.fetchrow(
                "SELECT * FROM gacha_user_collections WHERE user_id = $1 AND card_id = $2 AND quantity > 0",
                user_id,
                source_card_id,
            )
            if not default_card:
                raise CardNotFoundError("沒有可用的默認卡")

            new_quantity = default_card["quantity"] - 1
            if new_quantity > 0:
                await conn.execute(
                    "UPDATE gacha_user_collections SET quantity = $1 WHERE id = $2",
                    new_quantity,
                    default_card["id"],
                )
            else:
                await conn.execute(
                    "DELETE FROM gacha_user_collections WHERE id = $1",
                    default_card["id"],
                )

            # 2. 獲取卡牌基本信息
            master_card = await conn.fetchrow(
                "SELECT * FROM gacha_master_cards WHERE card_id = $1",
                source_card_id,
            )

            # 3. 生成新的特殊卡ID
            special_card_id = await generate_next_special_card_id()

            # 4. 創建特殊卡並設置技能
            await conn.execute(
                """
                INSERT INTO rpg_card_special_configs
                (card_id, user_id, name, series, rarity, image_url, pool_type,
                 rpg_level, rpg_xp, equipped_active_skill_ids, equipped_common_passives,
                 star_level, is_favorite, quantity)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """,
                special_card_id,
                user_id,
                master_card["name"],
                master_card["series"],
                master_card["rarity"],
                master_card["image_url"],
                master_card["pool_type"],
                1,
                0,  # 從等級1、經驗0開始
                json.dumps(new_skills["active"]),
                json.dumps(new_skills["passive"]),
                default_card["star_level"],
                default_card["is_favorite"],
                1,
            )

            return special_card_id


async def update_special_card_skills(card_id: int, new_skills: Dict) -> None:
    """更新特殊卡技能"""
    query = """
        UPDATE rpg_card_special_configs
        SET equipped_active_skill_ids = $2,
            equipped_common_passives = $3,
            updated_at = CURRENT_TIMESTAMP
        WHERE card_id = $1
    """
    try:
        result = await _base_repo.execute_query(
            query,
            [
                card_id,
                json.dumps(new_skills["active"]),
                json.dumps(new_skills["passive"]),
            ],
        )
        if result != 1:
            raise EntityNotFoundError(f"找不到特殊卡: {card_id}")
    except EntityNotFoundError:
        raise
    except Exception as e:
        logger.error("更新特殊卡技能失敗: card_id=%s, 錯誤: %s", card_id, e)
        raise DatabaseOperationError(f"更新特殊卡技能失敗: {str(e)}") from e


async def update_special_card_experience(card_id: int, exp_gained: int) -> None:
    """更新特殊卡經驗"""
    query = """
        UPDATE rpg_card_special_configs
        SET rpg_xp = rpg_xp + $2,
            updated_at = CURRENT_TIMESTAMP
        WHERE card_id = $1
    """
    try:
        result = await _base_repo.execute_query(query, [card_id, exp_gained])
        if result != 1:
            raise EntityNotFoundError(f"找不到特殊卡: {card_id}")
    except EntityNotFoundError:
        raise
    except Exception as e:
        logger.error("更新特殊卡經驗失敗: card_id=%s, 錯誤: %s", card_id, e)
        raise DatabaseOperationError(f"更新特殊卡經驗失敗: {str(e)}") from e


async def update_rpg_level_and_xp(
    special_card_id: int, rpg_level: int, rpg_xp: int
) -> None:
    """更新特殊卡的RPG等級和經驗"""
    query = """
        UPDATE rpg_card_special_configs
        SET rpg_level = $2, rpg_xp = $3, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
    """
    try:
        status = await _base_repo.execute_query(
            query, (special_card_id, rpg_level, rpg_xp)
        )
        if status != 1:
            raise EntityNotFoundError(f"找不到特殊卡記錄: {special_card_id}")
    except EntityNotFoundError:
        raise
    except Exception as e:
        logger.error(
            "更新特殊卡RPG等級和經驗失敗: special_card_id=%s, 錯誤: %s",
            special_card_id,
            e,
        )
        raise DatabaseOperationError(f"更新特殊卡RPG等級和經驗失敗: {str(e)}") from e


# ==================== 獻祭相關方法 ====================


async def sacrifice_default_card(user_id: int, card_id: int, quantity: int = 1) -> bool:
    """獻祭默認卡"""
    from database.postgresql.async_manager import get_pool

    pool = get_pool()
    if pool is None:
        raise DatabaseOperationError("資料庫連線池未初始化")

    try:
        async with pool.acquire() as conn:
            async with conn.transaction():
                # 1. 檢查並減少卡片數量
                check_query = """
                    SELECT quantity FROM gacha_user_collections
                    WHERE user_id = $1 AND card_id = $2 AND quantity >= $3
                """
                current_card = await conn.fetchrow(
                    check_query, user_id, card_id, quantity
                )
                if not current_card:
                    return False

                # 2. 更新卡片數量
                update_query = """
                    UPDATE gacha_user_collections
                    SET quantity = quantity - $3
                    WHERE user_id = $1 AND card_id = $2 AND quantity >= $3
                """
                result = await conn.execute(update_query, user_id, card_id, quantity)
                rows_affected = int(result.split()[-1]) if result else 0

                if rows_affected != 1:
                    return False

                # 3. 檢查是否需要更新擁有者統計（如果卡片完全被移除）
                remaining_quantity = current_card["quantity"] - quantity
                if remaining_quantity == 0:
                    # 【新增】在同一事務中更新市場統計
                    from gacha.services.direct_market_stats_updater import (
                        update_market_stats_for_sell_in_transaction,
                    )

                    # 模擬賣卡操作的統計更新
                    sold_cards_details = [
                        {"card_id": card_id, "quantity_sold": quantity}
                    ]
                    deleted_cards = [
                        {"card_id": card_id, "was_favorite": False}
                    ]  # 假設不是收藏

                    await update_market_stats_for_sell_in_transaction(
                        conn=conn,
                        sold_cards_details=sold_cards_details,
                        deleted_cards=deleted_cards,
                    )
                else:
                    # 只更新總擁有量統計
                    from gacha.services.direct_market_stats_updater import (
                        update_market_stats_in_transaction,
                    )

                    await update_market_stats_in_transaction(
                        conn=conn,
                        drawn_card_ids=[],
                        owner_changes=[],
                        favorite_changes=[],
                        wishlist_changes=[],
                    )

                    # 手動更新總擁有量
                    from gacha.services.direct_market_stats_updater import (
                        bulk_update_total_owned,
                    )

                    await bulk_update_total_owned(
                        [(card_id, -quantity)], connection=conn
                    )

                return True

    except Exception as e:
        logger.error(
            "獻祭默認卡失敗: user_id=%s, card_id=%s, 錯誤: %s", user_id, card_id, e
        )
        raise DatabaseOperationError(f"獻祭默認卡失敗: {str(e)}") from e


async def sacrifice_special_card(card_id: int) -> bool:
    """獻祭特殊卡"""
    query = "DELETE FROM rpg_card_special_configs WHERE card_id = $1"
    try:
        result = await _base_repo.execute_query(query, [card_id])
        return result == 1
    except Exception as e:
        logger.error("獻祭特殊卡失敗: card_id=%s, 錯誤: %s", card_id, e)
        raise DatabaseOperationError(f"獻祭特殊卡失敗: {str(e)}") from e


# ==================== 抽卡時設置默認技能 ====================


async def add_card_from_gacha(
    user_id: int, card_id: int, default_skills: Dict[str, Any]
) -> None:
    """抽卡時設置默認技能"""
    query = """
        INSERT INTO gacha_user_collections
        (user_id, card_id, quantity, equipped_active_skill_ids, equipped_common_passives,
         star_level, is_favorite, first_acquired, last_acquired)
        VALUES ($1, $2, 1, $3, $4, 0, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, card_id) DO UPDATE
        SET quantity = gacha_user_collections.quantity + 1,
            last_acquired = CURRENT_TIMESTAMP
    """
    try:
        await _base_repo.execute_query(
            query,
            [
                user_id,
                card_id,
                json.dumps(default_skills.get("active", [])),
                json.dumps(default_skills.get("passive", {})),
            ],
        )
    except Exception as e:
        logger.error(
            "抽卡添加卡牌失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"抽卡添加卡牌失敗: {str(e)}") from e
