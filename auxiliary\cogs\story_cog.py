import uuid
from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from auxiliary.exceptions import AuxiliaryError
from auxiliary.services import story_logic
from auxiliary.services.story.themes import STORY_THEMES
from auxiliary.views.story_management_views import Story<PERSON>istView
from auxiliary.views.story_master_view import <PERSON>MasterView
from auxiliary.views.story_readonly_view import <PERSON><PERSON><PERSON>OnlyView
from auxiliary.views.story_selection_view import StorySelectionView
from utils.response_embeds import InfoEmbed


class StoryCog(commands.Cog, name="AI 劇情"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # self.story_logic is no longer needed, call functions from the module directly.
        self.active_story_users = set()

    # 定義 story group
    story_group = app_commands.Group(name="story", description="AI 劇情冒險系統")

    @story_group.command(name="start", description="開始或繼續你的文字冒險故事")
    @app_commands.describe(
        new_game="選擇 True 以強制開始一個全新的冒險，這將會存檔你目前的故事。"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def story_start(
        self, interaction: discord.Interaction, new_game: Optional[bool] = False
    ):
        user_id = interaction.user.id

        if user_id in self.active_story_users:
            raise AuxiliaryError("你的故事正在生成中，請稍候再試。")

        await interaction.response.defer()

        history_data = await story_logic.get_user_history(user_id)

        if new_game:
            if history_data:
                await story_logic.clear_history(user_id)  # This pauses the story
            history_data = None  # Force new story selection

        if history_data:
            # The new history_data is nested.
            view = StoryMasterView(self.bot, interaction.user, self, history_data)
            initial_embed = await view.start()
            await interaction.edit_original_response(embed=initial_embed, view=view)
        else:
            view = StorySelectionView(
                bot=self.bot,
                user=interaction.user,
                story_cog=self,
            )
            theme = STORY_THEMES[0]
            embed = discord.Embed(
                title=f"📜 故事主題：{theme['title']}",
                description=theme.get("description", "暫無詳細描述。"),
                color=discord.Color.gold(),
            )
            if theme.get("image_url"):
                embed.set_image(url=theme["image_url"])
            embed.set_footer(text=f"主題 1/{len(STORY_THEMES)}")
            await interaction.edit_original_response(embed=embed, view=view)

    @story_group.command(name="list", description="查看你的所有故事")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def list_stories(self, interaction: discord.Interaction):
        stories = await story_logic.get_user_stories(interaction.user.id)

        if not stories:
            embed = InfoEmbed(
                title="📚 故事列表",
                description="你還沒有任何故事。使用 `/story start` 開始你的第一個冒險！",
            )
            await interaction.response.send_message(embed=embed)
            return

        view = StoryListView(stories, interaction.user, self)
        embed = await view.get_current_page_embed()
        await interaction.response.send_message(embed=embed, view=view)

        # 保存訊息供後續更新使用
        view.message = await interaction.original_response()

    @story_group.command(name="view", description="查看分享的故事")
    @app_commands.describe(story_id="要查看的故事ID")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def view_shared_story(self, interaction: discord.Interaction, story_id: str):
        try:
            story_uuid = uuid.UUID(story_id)
        except ValueError as e:
            raise AuxiliaryError("無效的故事ID格式。") from e

        story_data = await story_logic.get_story_by_id(story_uuid)
        if not story_data:
            raise AuxiliaryError("故事不存在或未公開分享。")

        view = StoryReadOnlyView(self.bot, interaction.user, story_data)

        # 交由 view 的 start 方法來生成初始畫面
        initial_embed = await view.start()  # type: ignore

        await interaction.response.send_message(embed=initial_embed, view=view)


async def setup(bot: commands.Bot):
    await bot.add_cog(StoryCog(bot))
