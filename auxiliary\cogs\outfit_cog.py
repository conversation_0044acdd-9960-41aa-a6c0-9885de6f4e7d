# auxiliary/cogs/outfit_cog.py
import io
import logging
from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from gacha.exceptions import BusinessError

# 導入新的 service 層模組
from ..services.ai_core import message_handler
from ..services.outfit_rater import outfit_rater

logger = logging.getLogger(__name__)


# 在類外部定義上下文菜單命令
@app_commands.context_menu(name="穿搭評分")
@app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
@app_commands.allowed_installs(guilds=True, users=True)
@app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
async def rate_outfit_context(
    interaction: discord.Interaction, message: discord.Message
):
    """消息上下文菜單 - 穿搭評分功能"""
    # 類型斷言，確保 client 是 Bot 類型
    from discord.ext import commands

    if isinstance(interaction.client, (commands.Bot, commands.AutoShardedBot)):
        cog = interaction.client.get_cog("OutfitCog")
    else:
        cog = None
    if not cog:
        await interaction.response.send_message(
            "❌ 穿搭評分服務暫時不可用。", ephemeral=True
        )
        return

    image_url = None
    attachment_to_use = None

    if message.attachments:
        for attachment in message.attachments:
            if attachment.content_type and attachment.content_type.startswith("image/"):
                attachment_to_use = attachment
                break

    if not attachment_to_use and message.embeds:
        for embed in message.embeds:
            if embed.image and embed.image.url:
                image_url = embed.image.url
                break

    if not attachment_to_use and not image_url:
        raise BusinessError("❌ 所選消息不包含任何圖片。")

    # 直接調用 outfit_rater.py 中的 rate_outfit_from_binary 或 rate_outfit_from_url
    # 這裡我們需要一個統一的入口點，類似於之前的 slash command
    # 我們將在 Cog 中創建一個內部處理方法
    await cog._rate_outfit_command(  # type: ignore
        interaction, image=attachment_to_use, url=image_url
    )


class OutfitCog(commands.Cog, name="OutfitCog"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.bot.tree.add_command(rate_outfit_context)
        logger.info("OutfitCog 初始化並註冊上下文菜單完成")

    async def cog_unload(self):
        """COG 卸載時清理資源"""
        self.bot.tree.remove_command(
            rate_outfit_context.name, type=rate_outfit_context.type
        )
        logger.info("✅ 穿搭評分 Context Menu 已移除")

    async def _rate_outfit_command(
        self,
        interaction: discord.Interaction,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
    ):
        """評分穿搭的內部命令處理邏輯"""
        if not (image or url):
            await interaction.response.send_message(
                "⚠️ 請上傳一張穿搭照片或提供一個有效的圖片URL進行評分", ephemeral=True
            )
            return

        processing_message = (
            "🔗 正在從URL下載並分析您的穿搭照片..."
            if url and not image
            else "📸 正在分析您的穿搭照片..."
        )

        # 注意：這裡的 processor_func 直接來自 outfit_rater service
        # 我們需要一個適配器函數，因為 outfit_rater.rate_outfit_from_binary
        # 的簽名與 message_handler 期望的不同
        async def processor_adapter(
            image_data, user_id, response_message, request_id, user_prompt=None
        ):
            # outfit_rater.py 中的函數不處理 discord.WebhookMessage，它只返回結果
            # 這意味著我們需要在這裡處理後續的消息編輯
            from ..services.outfit_rater import rating_image_generator  # 延遲導入

            rating_result = await outfit_rater.rate_outfit_from_binary(
                image_data=image_data, request_id=request_id, user_id=user_id
            )
            if not rating_result or not rating_result.get("score"):
                await response_message.edit(
                    content="😕 AI 未能完成評分，請稍後再試或換張圖片試試"
                )
                return

            await response_message.edit(content="👗 評分完成，正在生成評分圖片...")

            image_bytes = await rating_image_generator.generate_rating_image(
                rating_result=rating_result,
                user_image_data=image_data,
                user_name=interaction.user.display_name,
            )

            discord_file = discord.File(
                io.BytesIO(image_bytes), filename=f"rating_{request_id}.webp"
            )
            edited_message = await response_message.edit(
                content=f"✨ {interaction.user.display_name}的穿搭評分準備好啦！",
                attachments=[discord_file],
            )

            # 新增 Webhook 轉發邏輯
            if edited_message.attachments:
                from ..services.outfit_rater import config, helpers

                cdn_url = edited_message.attachments[0].url
                score = rating_result.get("score", "N/A")
                user_display_name = helpers.get_sanitized_user_display_name(interaction)
                await helpers.send_rating_image_to_webhook(
                    config=config.default_config,
                    cdn_url=cdn_url,
                    user_name=user_display_name,
                    score=score,
                    request_id=request_id,
                )

        await message_handler.handle_interaction(
            interaction=interaction,
            processing_message=processing_message,
            processor_func=processor_adapter,
            image=image,
            url=url,
        )

    @app_commands.command(name="rate", description="評分你的穿搭")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(image="上傳一張你的穿搭照片", url="或者提供一個圖片的URL")
    async def rate_slash_command(
        self,
        interaction: discord.Interaction,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
    ):
        """斜線命令的入口點"""
        await self._rate_outfit_command(interaction, image, url)


async def setup(bot: commands.Bot):
    await bot.add_cog(OutfitCog(bot))
