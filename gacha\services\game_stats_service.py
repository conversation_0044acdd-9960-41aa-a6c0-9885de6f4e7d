"""
遊戲統計服務
處理小遊戲統計數據的記錄和查詢功能
"""

import logging
from typing import Any, Dict, List, Optional

import asyncpg

from gacha.core.game_registry import GameRegistry
from gacha.repositories.games import game_stats_repository

logger = logging.getLogger(__name__)

# --- 模組級函數 ---


async def record_game_result(
    user_id: int, game_type: str, game_data: Dict[str, Any]
) -> None:
    """記錄遊戲結果並更新統計數據

    Args:
        user_id: 用戶ID
        game_type: 遊戲類型 ('blackjack', 'dice', 'mines', 'tower', 'slot', 'poker1v1', 'spin_wheel')
        game_data: 遊戲數據字典，包含 bet, payout, profit, result 等
    """
    try:
        await game_stats_repository.record_game_result(user_id, game_type, game_data)
    except Exception as e:
        logger.error(
            "記錄遊戲結果失敗: user_id=%s, game_type=%s, error=%s",
            user_id,
            game_type,
            e,
        )
        raise


async def get_user_game_stats(
    user_id: int,
    game_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """獲取用戶遊戲統計數據

    Args:
        user_id: 用戶ID
        game_type: 遊戲類型，如果為None則返回所有遊戲的統計
        connection: 可選的資料庫連接

    Returns:
        Dict[str, Any]: 統計數據字典
    """
    try:
        if game_type:
            return await _get_single_game_stats(user_id, game_type, connection)
        else:
            return await _get_all_game_stats(user_id, connection)
    except Exception as e:
        logger.error(
            "獲取用戶遊戲統計失敗: user_id=%s, game_type=%s, error=%s",
            user_id,
            game_type,
            e,
        )
        raise


def _get_default_stats(user_id: int, game_type: str) -> Dict[str, Any]:
    """返回指定遊戲的默認統計數據結構"""
    return {
        "user_id": user_id,
        "game_type": game_type,
        "total_games": 0,
        "total_wins": 0,
        "total_losses": 0,
        "total_pushes": 0,
        "total_bet_amount": 0,
        "total_payout_amount": 0,
        "total_profit_loss": 0,
        "max_win": 0,
        "max_loss": 0,
        "game_specific_stats": {},
        "last_played_at": None,
    }


async def _get_single_game_stats(
    user_id: int, game_type: str, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """獲取單一遊戲的統計數據，如果不存在則返回默認值"""
    result = await game_stats_repository.get_user_stats_for_single_game(
        user_id, game_type, connection=connection
    )
    if not result:
        return _get_default_stats(user_id, game_type)
    return result


async def _get_all_game_stats(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """獲取所有遊戲的統計數據"""
    results = await game_stats_repository.get_user_stats_for_all_games(
        user_id, connection=connection
    )
    stats = {}
    for result in results:
        game_type = result["game_type"]
        stats[game_type] = result
    return stats


def get_available_games() -> List[Dict[str, str]]:
    """獲取可用的遊戲列表"""
    return GameRegistry.get_games_list()


def get_available_stats(game_type: str) -> List[Dict[str, str]]:
    """獲取指定遊戲的可用統計類型"""
    return GameRegistry.get_game_stats(game_type)


# --- 新增的統計強化功能 ---


async def get_user_game_stats_with_details(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """
    獲取用戶遊戲統計，並附加上排名和趣味分析數據。
    """
    try:
        # 1. 循序獲取基礎統計和排名數據
        base_stats = await get_user_game_stats(user_id, connection=connection)
        rankings_list = await game_stats_repository.get_user_game_rankings(
            user_id, connection=connection
        )

        # 2. 如果有任何遊戲統計，就先計算並創建 'overall' 統計
        if base_stats:
            overall_stats = _calculate_overall_and_fun_stats(base_stats)
            base_stats["overall"] = overall_stats

        # 3. 將排名列表轉換為字典以便查找
        rankings_map = {item["game_type"]: item for item in rankings_list}

        # 4. 將排名數據合併到基礎統計中
        for game_type, game_data in base_stats.items():
            if game_type in rankings_map:
                game_data["rankings"] = rankings_map[game_type]
            else:
                game_data["rankings"] = {}

        return base_stats
    except Exception as e:
        logger.error(
            "獲取詳細遊戲統計失敗: user_id=%s, error=%s", user_id, e, exc_info=True
        )
        # 在 stats cog 中，我們希望即使部分失敗也能繼續，所以返回空字典
        return {}


def _calculate_overall_and_fun_stats(all_game_stats: Dict[str, Any]) -> Dict[str, Any]:
    """根據所有遊戲的統計數據，計算總體數據和趣味統計"""
    total_games = 0
    total_bet = 0
    total_payout = 0
    total_profit = 0

    # 找到最愛的遊戲
    favorite_game_type = "N/A"
    max_games = -1

    for game_type, data in all_game_stats.items():
        total_games += data.get("total_games", 0)
        total_bet += data.get("total_bet_amount", 0)
        total_payout += data.get("total_payout_amount", 0)
        total_profit += data.get("total_profit_loss", 0)

        if data.get("total_games", 0) > max_games:
            max_games = data.get("total_games", 0)
            favorite_game_type = game_type

    # 計算賭運指數
    # 基準期望回報率，可寫入設定檔
    expected_return_rate = 0.975
    actual_return_rate = total_payout / total_bet if total_bet > 0 else 0
    luck_index = (
        actual_return_rate / expected_return_rate if expected_return_rate > 0 else 1.0
    )

    # 判斷賭運稱號
    if luck_index > 1.20:
        luck_title = "賭神 🎲✨"
    elif luck_index > 1.05:
        luck_title = "賭聖 😎"
    elif luck_index > 0.98:
        luck_title = "平民 🙏"
    elif luck_index > 0.90:
        luck_title = "小韭菜 🥲"
    else:
        luck_title = "大冤種 💀"

    # 判斷玩家風格
    avg_bet = total_bet / total_games if total_games > 0 else 0
    style_title = "新手上路"
    if total_games > 50:  # 基礎判斷
        if avg_bet > 100000:  # 平均下注超過 10 萬
            style_title = "豪賭客 💰" if total_profit > 0 else "慈善賭王 💸"
        elif avg_bet > 20000:
            style_title = "穩健投資者 📈" if total_profit > 0 else "風險愛好者 🎢"
        else:
            style_title = "娛樂玩家 😊"
    if total_games < 30 and total_profit > 500000:
        style_title = "天選之人 ✨"

    return {
        "total_games": total_games,
        "total_bet_amount": total_bet,
        "total_payout_amount": total_payout,
        "total_profit_loss": total_profit,
        "luck_index": luck_index,
        "luck_title": luck_title,
        "style_title": style_title,
        "favorite_game": favorite_game_type,
    }
