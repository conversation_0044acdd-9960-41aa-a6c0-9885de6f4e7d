from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

import asyncpg

from gacha.exceptions import DatabaseOperationError
from gacha.models.market_models import StockAsset, StockLifecycleStatus
from gacha.repositories import _base_repo
from utils.logger import logger

# from gacha.exceptions import DataMappingError # Potential future use for
# Pydantic errors


async def _get_asset_by_condition(
    condition: str, param: Any, conn: Optional[asyncpg.Connection] = None
) -> Optional[asyncpg.Record]:
    """
    通用的資產查詢方法，減少重複的 SQL 查詢代碼。

    Args:
        condition: WHERE 條件（如 "asset_id = $1" 或 "asset_symbol = $1"）
        param: 查詢參數
        conn: 可選的資料庫連接

    Returns:
        Optional[asyncpg.Record]: 查詢結果記錄

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = f"""
            SELECT
                asset_id, asset_symbol, asset_name, description,
                current_price, linked_criteria_type, linked_criteria_value,
                linked_pool_context, base_volatility, volatility_factor,
                influence_weight, total_shares, created_at, last_updated,
                initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                lifecycle_status
            FROM virtual_assets
            WHERE {condition}
        """
        return await _base_repo.fetch_one(query, (param,), connection=conn)
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"查詢資產失敗: {e}") from e


async def get_asset_by_id(
    asset_id: int, conn: Optional[asyncpg.Connection] = None
) -> Optional[StockAsset]:
    """
    根據 asset_id 從 virtual_assets 表查詢股票數據，並將結果映射到 StockAsset Pydantic 模型返回。

    Args:
        asset_id: 資產ID
        conn: 可選的資料庫連接

    Returns:
        Optional[StockAsset]: 找到的股票資產，如果不存在則返回 None

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    record = await _get_asset_by_condition("asset_id = $1", asset_id, conn)
    return StockAsset(**record) if record else None


async def update_lifecycle_status(
    asset_id: int,
    new_status: StockLifecycleStatus,
    conn: Optional[asyncpg.Connection] = None,
) -> bool:
    """
    更新指定 asset_id 的股票的 lifecycle_status 欄位和 last_updated 時間戳。

    Args:
        asset_id: 資產ID
        new_status: 新的生命週期狀態
        conn: 可選的資料庫連接

    Returns:
        bool: 成功更新返回 True，記錄不存在返回 False

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = """
            UPDATE virtual_assets
            SET lifecycle_status = $1, last_updated = $2
            WHERE asset_id = $3
        """
        status = await _base_repo.execute_query(
            query, (new_status.value, datetime.utcnow(), asset_id), connection=conn
        )
        return status == 1
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"更新資產 {asset_id} 生命週期狀態失敗: {e}"
        ) from e


async def get_active_or_st_stocks(
    conn: Optional[asyncpg.Connection] = None,
) -> List[StockAsset]:
    """
    查詢所有 lifecycle_status 為 ACTIVE 或 ST 的股票，並將結果映射到 StockAsset 模型列表返回。
    查詢應包含計算市值所需的 current_price 和 total_shares。

    Args:
        conn: 可選的資料庫連接

    Returns:
        List[StockAsset]: 活躍或ST狀態的股票列表

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = """
            SELECT
                asset_id, asset_symbol, asset_name, description,
                current_price, linked_criteria_type, linked_criteria_value,
                linked_pool_context, base_volatility, volatility_factor,
                influence_weight, total_shares, created_at, last_updated,
                initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                lifecycle_status
            FROM virtual_assets
            WHERE lifecycle_status = ANY($1)
        """
        statuses = [StockLifecycleStatus.ACTIVE.value, StockLifecycleStatus.ST.value]
        records = await _base_repo.fetch_all(query, (statuses,), connection=conn)
        return [StockAsset(**record) for record in records]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"查詢活躍股票失敗: {e}") from e


async def insert_stock(
    stock_data: Dict[str, Any], conn: Optional[asyncpg.Connection] = None
) -> Optional[StockAsset]:
    """
    向 virtual_assets 表插入一條新的股票記錄。
    stock_data 是一個包含所有必要欄位的字典。
    使用 RETURNING * 或插入後再查詢，以返回完整的 StockAsset 對象。

    Args:
        stock_data: 包含股票數據的字典
        conn: 可選的資料庫連接

    Returns:
        Optional[StockAsset]: 插入的股票資產，如果插入失敗則返回 None

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        if "lifecycle_status" in stock_data and isinstance(
            stock_data["lifecycle_status"], StockLifecycleStatus
        ):
            stock_data["lifecycle_status"] = stock_data["lifecycle_status"].value

        if "initial_anchor_price" in stock_data:
            stock_data["current_anchor_price"] = stock_data["initial_anchor_price"]

        stock_data.setdefault("created_at", datetime.utcnow())
        stock_data.setdefault("last_updated", datetime.utcnow())
        if "current_anchor_price" in stock_data:
            stock_data.setdefault("anchor_price_updated_at", datetime.utcnow())

        columns = ", ".join(stock_data.keys())
        placeholders = ", ".join([f"${i + 1}" for i in range(len(stock_data))])

        query = f"""
            INSERT INTO virtual_assets ({columns})
            VALUES ({placeholders})
            RETURNING asset_id, asset_symbol, asset_name, description,
                      current_price, linked_criteria_type, linked_criteria_value,
                      linked_pool_context, base_volatility, volatility_factor,
                      influence_weight, total_shares, created_at, last_updated,
                      initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                      lifecycle_status
        """

        values = list(stock_data.values())
        record = await _base_repo.fetch_one(query, values, connection=conn)
        return StockAsset(**record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"插入股票數據失敗: {e}") from e


async def get_active_companies_count(conn: Optional[asyncpg.Connection] = None) -> int:
    """
    獲取狀態為 ACTIVE 的公司數量。

    Args:
        conn: 可選的資料庫連接

    Returns:
        int: 活躍公司數量

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = "SELECT COUNT(*) FROM virtual_assets WHERE lifecycle_status = $1"
        count = await _base_repo.fetch_value(
            query, (StockLifecycleStatus.ACTIVE.value,), connection=conn
        )
        return count if count is not None else 0
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"查詢活躍公司數量失敗: {e}") from e


async def get_linked_assets_with_history(
    conn: Optional[asyncpg.Connection] = None,
) -> List[asyncpg.Record]:
    """獲取所有設定了 linked_criteria_type (非 GLOBAL) 的虛擬資產及其最近一次的價格歷史。"""
    query = """
        SELECT va.asset_id, va.current_price, va.influence_weight,
               va.linked_criteria_type, va.linked_criteria_value, va.linked_pool_context,
               (SELECT aph.price FROM asset_price_history aph
                WHERE aph.asset_id = va.asset_id ORDER BY aph.timestamp DESC OFFSET 1 LIMIT 1) AS previous_price
        FROM virtual_assets va
        WHERE va.linked_criteria_type IS NOT NULL AND va.linked_criteria_type != 'GLOBAL';
    """
    try:
        return await _base_repo.fetch_all(query, connection=conn)
    except asyncpg.PostgresError as e:
        logger.error("查詢連結資產時出錯: %s", e, exc_info=True)
        raise DatabaseOperationError(f"查詢連結資產失敗: {e}") from e


async def get_global_assets_with_history(
    conn: Optional[asyncpg.Connection] = None,
) -> List[asyncpg.Record]:
    """獲取所有設定了 linked_criteria_type 為 GLOBAL 的虛擬資產及其最近一次的價格歷史。"""
    query = """
        SELECT asset_id, current_price, influence_weight,
               (SELECT aph.price FROM asset_price_history aph
                WHERE aph.asset_id = va.asset_id ORDER BY aph.timestamp DESC OFFSET 1 LIMIT 1) AS previous_price
        FROM virtual_assets va
        WHERE va.linked_criteria_type = 'GLOBAL';
    """
    try:
        return await _base_repo.fetch_all(query, connection=conn)
    except asyncpg.PostgresError as e:
        logger.error("查詢全局資產時出錯: %s", e, exc_info=True)
        raise DatabaseOperationError(f"查詢全局資產失敗: {e}") from e


async def get_asset_by_symbol(
    asset_symbol: str, conn: Optional[asyncpg.Connection] = None
) -> Optional[StockAsset]:
    """
    根據 asset_symbol 查詢股票數據。

    Args:
        asset_symbol: 資產代碼
        conn: 可選的資料庫連接

    Returns:
        Optional[StockAsset]: 找到的股票資產，如果不存在則返回 None

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    record = await _get_asset_by_condition(
        "asset_symbol = $1", asset_symbol.upper(), conn
    )
    return StockAsset(**record) if record else None


async def get_asset_info_for_transaction(
    asset_symbol: str, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    獲取交易所需的資產信息（帶鎖定）。

    Args:
        asset_symbol: 資產代碼
        conn: 可選的資料庫連接

    Returns:
        Optional[Dict]: 包含 asset_id, current_price, asset_symbol 的字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = """
            SELECT asset_id, current_price, asset_symbol
            FROM virtual_assets
            WHERE asset_symbol = $1
            FOR UPDATE
        """
        record = await _base_repo.fetch_one(
            query, (asset_symbol.upper(),), connection=conn
        )
        return dict(record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"查詢交易資產信息 {asset_symbol} 失敗: {e}"
        ) from e


async def get_stock_and_user_data_for_buy(
    asset_symbol: str, user_id: int, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    Repository層：一次性獲取購買股票所需的所有數據（股票信息+用戶餘額）
    使用 CROSS JOIN 避免 CTE 複雜性

    Args:
        asset_symbol: 股票代碼
        user_id: 用戶ID
        conn: 數據庫連接

    Returns:
        Dict包含: asset_id, current_price, asset_symbol, oil_balance

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # 簡化查詢，使用 CROSS JOIN 和明確的 FOR UPDATE
        # 統一鎖順序: 先鎖用戶，再鎖資產，避免死鎖
        query = """
            SELECT
                va.asset_id, va.current_price, va.asset_symbol, va.lifecycle_status,
                gu.oil_balance
            FROM gacha_users gu
            CROSS JOIN virtual_assets va
            WHERE gu.user_id = $2 AND va.asset_symbol = $1 AND va.current_price > 0
            FOR UPDATE OF gu, va
        """
        record = await _base_repo.fetch_one(
            query, (asset_symbol.upper(), user_id), connection=conn
        )
        return dict(record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"查詢購買交易數據 {asset_symbol} 失敗: {e}"
        ) from e


async def get_stock_and_user_data_for_sell(
    asset_symbol: str, user_id: int, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    Repository層：一次性獲取出售股票所需的所有數據（股票信息+用戶餘額+持股數量）
    修復 FOR UPDATE 在 LEFT JOIN 中的問題

    Args:
        asset_symbol: 股票代碼
        user_id: 用戶ID
        conn: 數據庫連接

    Returns:
        Dict包含: asset_id, current_price, asset_symbol, user_balance, stock_quantity, portfolio_id

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # 修復：分別鎖定主表，避免在 LEFT JOIN 的可空側使用 FOR UPDATE
        # 統一鎖順序: 先鎖用戶，再鎖資產，避免死鎖
        query = """
            SELECT
                va.asset_id, va.current_price, va.asset_symbol, va.lifecycle_status,
                gu.oil_balance,
                COALESCE(pp.quantity, 0) as stock_quantity,
                pp.id as portfolio_id,
                COALESCE(pp.average_buy_price, 0) as average_buy_price
            FROM gacha_users gu
            CROSS JOIN virtual_assets va
            LEFT JOIN player_portfolios pp ON gu.user_id = pp.user_id AND pp.asset_id = va.asset_id
            WHERE gu.user_id = $2 AND va.asset_symbol = $1 AND va.current_price > 0
            FOR UPDATE OF gu, va
        """
        record = await _base_repo.fetch_one(
            query, (asset_symbol.upper(), user_id), connection=conn
        )
        return dict(record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"查詢出售交易數據 {asset_symbol} 失敗: {e}"
        ) from e


async def execute_buy_transaction_batch(
    user_id: int,
    asset_id: int,
    quantity: int,
    price_per_unit: Decimal,
    fee: Decimal,
    conn: Optional[asyncpg.Connection] = None,
) -> None:
    """
    Repository層：批量執行購買交易的所有數據庫操作

    Args:
        user_id: 用戶ID
        asset_id: 資產ID
        quantity: 購買數量
        price_per_unit: 單價
        fee: 手續費
        new_balance: 新餘額
        conn: 數據庫連接

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # UPSERT 投資組合記錄 - 防禦性 SQL：強制使用 price_per_unit * quantity 計算成本
        portfolio_upsert = """
            INSERT INTO player_portfolios (user_id, asset_id, quantity, average_buy_price, last_transaction_at)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
            ON CONFLICT (user_id, asset_id)
            DO UPDATE SET
                quantity = player_portfolios.quantity + $3,
                average_buy_price = (
                    (player_portfolios.average_buy_price * player_portfolios.quantity + ($4 * $3)) /
                    (player_portfolios.quantity + $3)
                ),
                last_transaction_at = CURRENT_TIMESTAMP
        """

        # 批量執行所有操作
        await _base_repo.execute_query(
            portfolio_upsert,
            (user_id, asset_id, quantity, price_per_unit),
            connection=conn,
        )

        await _base_repo.execute_query(
            "INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee) VALUES ($1, $2, $3, $4, $5, $6, $7)",
            (
                user_id,
                asset_id,
                "BUY",
                quantity,
                price_per_unit,
                price_per_unit * quantity,
                fee,
            ),
            connection=conn,
        )

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"執行購買交易批量操作失敗: {e}") from e


async def execute_sell_transaction_batch(
    user_id: int,
    asset_id: int,
    quantity: int,
    price_per_unit: Decimal,
    total_proceeds_before_fee: Decimal,
    fee: Decimal,
    current_stock_quantity: int,
    portfolio_id: Optional[int],
    context: Optional[Dict[str, Any]] = None,
    conn: Optional[asyncpg.Connection] = None,
) -> None:
    """
    Repository層：批量執行出售交易的所有數據庫操作

    Args:
        user_id: 用戶ID
        asset_id: 資產ID
        quantity: 出售數量
        price_per_unit: 單價
        total_proceeds_before_fee: 手續費前總收益
        fee: 手續費
        new_balance: 新餘額
        current_stock_quantity: 當前持股數量
        portfolio_id: 投資組合記錄ID
        conn: 數據庫連接

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        new_quantity = current_stock_quantity - quantity

        # 更新或刪除投資組合記錄
        if new_quantity == 0:
            # 檢查是否還有做空倉位，如果沒有則刪除整個記錄
            check_query = """
                SELECT short_quantity FROM player_portfolios
                WHERE id = $1
            """
            current_short_quantity = await _base_repo.fetch_value(
                check_query, (portfolio_id,), connection=conn
            )

            if current_short_quantity == 0:
                # 沒有做空倉位，刪除整個記錄
                await _base_repo.execute_query(
                    "DELETE FROM player_portfolios WHERE id = $1",
                    (portfolio_id,),
                    connection=conn,
                )
            else:
                # 有做空倉位，只重置持股相關字段
                await _base_repo.execute_query(
                    "UPDATE player_portfolios SET quantity = 0, average_buy_price = 0, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $1",
                    (portfolio_id,),
                    connection=conn,
                )
        else:
            await _base_repo.execute_query(
                "UPDATE player_portfolios SET quantity = $1, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $2",
                (new_quantity, portfolio_id),
                connection=conn,
            )

        # 記錄交易
        await _base_repo.execute_query(
            "INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee, context) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)",
            (
                user_id,
                asset_id,
                "SELL",
                quantity,
                price_per_unit,
                total_proceeds_before_fee,
                fee,
                context,
            ),
            connection=conn,
        )

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"執行出售交易批量操作失敗: {e}") from e


async def get_stocks_paginated(
    page: int = 1, per_page: int = 10, conn: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """
    分頁獲取股票列表，包含趨勢計算。

    Args:
        page: 頁碼
        per_page: 每頁數量
        conn: 可選的資料庫連接

    Returns:
        Dict: 包含股票列表和分頁信息的字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # 防禦性檢查：確保頁碼和每頁數量都是正數
        page = max(1, page)
        per_page = max(1, per_page)
        offset = (page - 1) * per_page

        # 獲取總數
        total_query = "SELECT COUNT(*) FROM virtual_assets WHERE current_price > 0 AND lifecycle_status != $1"
        total_stocks = await _base_repo.fetch_value(
            total_query, ("delisted",), connection=conn
        )

        # 獲取股票列表（使用高性能的 LATERAL JOIN + OFFSET 方法）
        stocks_query = """
            SELECT
                va.asset_id, va.asset_symbol, va.asset_name, va.current_price,
                va.description, va.lifecycle_status,
                COALESCE(vd.volume_24h, 0) as volume_24h,
                prev.previous_price
            FROM virtual_assets va
            LEFT JOIN (
                SELECT asset_id, SUM(quantity) as volume_24h
                FROM market_transactions
                WHERE timestamp >= NOW() - INTERVAL '24 hours'
                GROUP BY asset_id
            ) vd ON va.asset_id = vd.asset_id
            LEFT JOIN LATERAL (
                SELECT price as previous_price
                FROM asset_price_history aph
                WHERE aph.asset_id = va.asset_id
                ORDER BY timestamp DESC
                OFFSET 1 LIMIT 1
            ) prev ON true
            WHERE va.current_price > 0 AND va.lifecycle_status != $1
            ORDER BY va.asset_symbol
            LIMIT $2 OFFSET $3
        """

        stocks_records = await _base_repo.fetch_all(
            stocks_query, ("delisted", per_page, offset), connection=conn
        )

        return {
            "stocks": [dict(record) for record in stocks_records],
            "total_stocks": total_stocks,
            "current_page": page,
            "per_page": per_page,
            "total_pages": (total_stocks + per_page - 1) // per_page,
        }
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"分頁查詢股票失敗: {e}") from e


async def get_asset_details_by_symbol_or_id(
    asset_symbol_or_id, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    根據代碼或ID獲取資產詳細信息。

    Args:
        asset_symbol_or_id: 資產代碼或ID
        conn: 可選的資料庫連接

    Returns:
        Optional[Dict]: 資產詳細信息字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    if isinstance(asset_symbol_or_id, str):
        condition = "asset_symbol = $1"
        param = asset_symbol_or_id.upper()
    else:
        condition = "asset_id = $1"
        param = asset_symbol_or_id

    record = await _get_asset_by_condition(condition, param, conn)
    return dict(record) if record else None


async def search_asset_symbols_with_details(
    search_term: str, limit: int = 25, conn: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """
    搜索資產代碼並返回詳細資訊（用於更詳細的自動完成）。
    只返回活躍和ST狀態的股票，過濾掉已退市的股票。

    Args:
        search_term: 搜索詞
        limit: 結果限制數量
        conn: 可選的資料庫連接

    Returns:
        List[Dict[str, Any]]: 包含資產代碼、名稱、狀態等詳細資訊的列表

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        if search_term:
            query = """
                SELECT asset_symbol, asset_name, current_price, lifecycle_status
                FROM virtual_assets
                WHERE (asset_symbol ILIKE $1 OR asset_name ILIKE $1)
                AND lifecycle_status != $2
                ORDER BY
                    CASE WHEN asset_symbol ILIKE $1 THEN 1 ELSE 2 END,
                    asset_symbol
                LIMIT $3
            """
            records = await _base_repo.fetch_all(
                query,
                (f"{search_term}%", StockLifecycleStatus.DELISTED.value, limit),
                connection=conn,
            )
        else:
            query = """
                SELECT asset_symbol, asset_name, current_price, lifecycle_status
                FROM virtual_assets
                WHERE lifecycle_status != $1
                ORDER BY asset_symbol
                LIMIT $2
            """
            records = await _base_repo.fetch_all(
                query, (StockLifecycleStatus.DELISTED.value, limit), connection=conn
            )

        return [dict(record) for record in records]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"搜索資產詳細資訊失敗: {e}") from e


# ==================== 做空功能相關方法 ====================


async def get_stock_and_user_data_for_short(
    asset_symbol: str, user_id: int, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    獲取做空交易所需的股票和用戶數據（類似買入交易）

    Args:
        asset_symbol: 股票代碼
        user_id: 用戶ID
        conn: 數據庫連接

    Returns:
        Optional[Dict]: 包含股票和用戶信息的字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # 檢查股票是否允許做空（不能是ST狀態）
        query = """
            SELECT
                va.asset_id, va.current_price, va.asset_symbol, va.lifecycle_status,
                gu.oil_balance
            FROM gacha_users gu
            CROSS JOIN virtual_assets va
            WHERE gu.user_id = $2 AND va.asset_symbol = $1 AND va.current_price > 0
            FOR UPDATE OF gu, va
        """
        record = await _base_repo.fetch_one(
            query, (asset_symbol.upper(), user_id), connection=conn
        )
        return dict(record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"查詢做空交易數據 {asset_symbol} 失敗: {e}"
        ) from e


async def get_stock_and_user_data_for_cover(
    asset_symbol: str, user_id: int, conn: Optional[asyncpg.Connection] = None
) -> Optional[Dict[str, Any]]:
    """
    獲取回補交易所需的股票和用戶數據

    Args:
        asset_symbol: 股票代碼
        user_id: 用戶ID
        conn: 數據庫連接

    Returns:
        Optional[Dict]: 包含股票、用戶和做空持倉信息的字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = """
            SELECT
                va.asset_id, va.current_price, va.asset_symbol, va.lifecycle_status,
                gu.oil_balance,
                COALESCE(pp.short_quantity, 0) as short_quantity,
                COALESCE(pp.short_average_price, 0) as short_average_price,
                pp.id as portfolio_id
            FROM gacha_users gu
            CROSS JOIN virtual_assets va
            LEFT JOIN player_portfolios pp ON gu.user_id = pp.user_id AND pp.asset_id = va.asset_id
            WHERE gu.user_id = $2 AND va.asset_symbol = $1 AND va.current_price > 0
            FOR UPDATE OF gu, va
        """
        record = await _base_repo.fetch_one(
            query, (asset_symbol.upper(), user_id), connection=conn
        )
        return dict(record) if record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"查詢回補交易數據 {asset_symbol} 失敗: {e}"
        ) from e


async def execute_short_transaction_batch(
    user_id: int,
    asset_id: int,
    quantity: int,
    price_per_unit: Decimal,
    fee: Decimal,
    conn: Optional[asyncpg.Connection] = None,
) -> None:
    """
    Repository層：批量執行做空交易的所有數據庫操作

    Args:
        user_id: 用戶ID
        asset_id: 資產ID
        quantity: 做空數量
        price_per_unit: 單價
        fee: 手續費
        new_balance: 新餘額
        conn: 數據庫連接

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # UPSERT 做空持倉記錄 - 防禦性 SQL：強制使用 price_per_unit * quantity 計算成本
        portfolio_upsert = """
            INSERT INTO player_portfolios (user_id, asset_id, quantity, average_buy_price, short_quantity, short_average_price, last_transaction_at)
            VALUES ($1, $2, 0, 0, $3, $4, CURRENT_TIMESTAMP)
            ON CONFLICT (user_id, asset_id)
            DO UPDATE SET
                short_quantity = player_portfolios.short_quantity + $3,
                short_average_price = CASE
                    WHEN player_portfolios.short_quantity = 0 THEN $4
                    ELSE (
                        (player_portfolios.short_average_price * player_portfolios.short_quantity + ($4 * $3)) /
                        (player_portfolios.short_quantity + $3)
                    )
                END,
                last_transaction_at = CURRENT_TIMESTAMP
        """

        # 批量執行所有操作
        await _base_repo.execute_query(
            portfolio_upsert,
            (user_id, asset_id, quantity, price_per_unit),
            connection=conn,
        )

        await _base_repo.execute_query(
            "INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee) VALUES ($1, $2, $3, $4, $5, $6, $7)",
            (
                user_id,
                asset_id,
                "SHORT",
                quantity,
                price_per_unit,
                price_per_unit * quantity,
                fee,
            ),
            connection=conn,
        )

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"執行做空交易批量操作失敗: {e}") from e


async def execute_cover_transaction_batch(
    user_id: int,
    asset_id: int,
    quantity: int,
    price_per_unit: Decimal,
    settlement_amount: Decimal,
    fee: Decimal,
    current_short_quantity: int,
    portfolio_id: Optional[int],
    context: Optional[Dict[str, Any]] = None,
    conn: Optional[asyncpg.Connection] = None,
) -> None:
    """
    Repository層：批量執行回補交易的所有數據庫操作

    Args:
        user_id: 用戶ID
        asset_id: 資產ID
        quantity: 回補數量
        price_per_unit: 單價
        settlement_amount: 結算金額（原押注金額 + 盈虧）
        fee: 手續費
        new_balance: 新餘額
        current_short_quantity: 當前做空數量
        portfolio_id: 投資組合記錄ID
        conn: 數據庫連接

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        new_short_quantity = current_short_quantity - quantity

        # 更新或刪除做空持倉記錄
        if new_short_quantity == 0:
            # 檢查是否還有持股，如果沒有則刪除整個記錄
            check_query = """
                SELECT quantity FROM player_portfolios
                WHERE id = $1
            """
            current_quantity = await _base_repo.fetch_value(
                check_query, (portfolio_id,), connection=conn
            )

            if current_quantity == 0:
                # 沒有持股，刪除整個記錄
                await _base_repo.execute_query(
                    "DELETE FROM player_portfolios WHERE id = $1",
                    (portfolio_id,),
                    connection=conn,
                )
            else:
                # 有持股，只重置做空相關字段
                await _base_repo.execute_query(
                    "UPDATE player_portfolios SET short_quantity = 0, short_average_price = 0, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $1",
                    (portfolio_id,),
                    connection=conn,
                )
        else:
            # 部分回補，更新做空數量
            await _base_repo.execute_query(
                "UPDATE player_portfolios SET short_quantity = $1, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $2",
                (new_short_quantity, portfolio_id),
                connection=conn,
            )

        # 記錄交易
        await _base_repo.execute_query(
            "INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee, context) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)",
            (
                user_id,
                asset_id,
                "COVER",
                quantity,
                price_per_unit,
                settlement_amount,
                fee,
                context,
            ),
            connection=conn,
        )

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"執行回補交易批量操作失敗: {e}") from e


async def get_all_short_positions_by_asset(
    asset_id: int, conn: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """
    獲取特定股票的所有做空倉位（用於強制結算）

    Args:
        asset_id: 資產ID
        conn: 數據庫連接

    Returns:
        List[Dict]: 包含用戶ID、做空數量、平均價格等信息的列表

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = """
            SELECT user_id, short_quantity, short_average_price, id as portfolio_id
            FROM player_portfolios
            WHERE asset_id = $1 AND short_quantity > 0
        """
        records = await _base_repo.fetch_all(query, (asset_id,), connection=conn)
        return [dict(record) for record in records]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"查詢資產 {asset_id} 的做空倉位失敗: {e}") from e


async def batch_force_cover_short_positions(
    asset_id: int,
    settlement_price: Decimal,
    fee_rate: Decimal,
    min_fee: Decimal,
    per_share_fee: Decimal,
    conn: Optional[asyncpg.Connection] = None,
) -> int:
    """
    批量強制結算特定股票的所有做空倉位（ST狀態觸發）
    使用 CTE 優化版本，一次性處理所有用戶的強制結算

    手續費計算邏輯：
    - 基於回補時的市場價值計算：(結算價格 × 股數) × 手續費率
    - 而不是基於總返還金額計算，確保與設計文檔一致
    - 計算方式：max(市場價值 × 3%, 股數 × 0.05, 最低手續費)

    Args:
        asset_id: 資產ID
        settlement_price: 結算價格
        fee_rate: 手續費率 (通常為0.03，即3%)
        min_fee: 最低手續費 (通常為5油幣)
        per_share_fee: 每股手續費 (通常為0.05油幣)
        conn: 數據庫連接

    Returns:
        int: 被強制結算的倉位數量

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        # 使用 CTE 一次性計算所有用戶的結算數據並更新
        batch_settlement_query = """
        WITH settlement_calculations AS (
            SELECT
                pp.user_id,
                pp.id as portfolio_id,
                pp.short_quantity,
                pp.short_average_price,
                pp.quantity as current_quantity,
                gu.oil_balance as current_balance,

                -- 計算盈虧
                (pp.short_quantity * pp.short_average_price) as original_bet,
                (pp.short_average_price - $2) * pp.short_quantity as total_profit,
                (pp.short_quantity * pp.short_average_price) + ((pp.short_average_price - $2) * pp.short_quantity) as settlement_amount
            FROM player_portfolios pp
            JOIN gacha_users gu ON pp.user_id = gu.user_id
            WHERE pp.asset_id = $1 AND pp.short_quantity > 0
            FOR UPDATE OF pp, gu
        ),
        fee_calculations AS (
            SELECT *,
                -- 手續費應該基於回補時的市場價值，而不是總返還金額
                (short_quantity * $2) * $3 as percentage_fee,
                short_quantity * $5 as per_share_fee_total,
                GREATEST(
                    GREATEST((short_quantity * $2) * $3, short_quantity * $5),
                    $4
                ) as final_fee
            FROM settlement_calculations
        ),
        net_calculations AS (
            SELECT *,
                settlement_amount - final_fee as net_return
            FROM fee_calculations
        ),
        balance_updates AS (
            UPDATE gacha_users
            SET oil_balance = oil_balance + nc.net_return::integer,
                updated_at = CURRENT_TIMESTAMP
            FROM net_calculations nc
            WHERE gacha_users.user_id = nc.user_id
            RETURNING nc.user_id, nc.portfolio_id, nc.current_quantity,
                     nc.short_quantity, nc.settlement_amount, nc.final_fee,
                     (SELECT asset_symbol FROM virtual_assets WHERE asset_id = $1) as asset_symbol,
                     nc.net_return,
                     nc.current_balance
        ),
        balance_history_inserts AS (
            INSERT INTO balance_history (user_id, change_amount, balance_before, balance_after, transaction_type, reason)
            SELECT
                user_id,
                net_return::integer,
                current_balance,
                (current_balance + net_return)::integer,
                'FORCED_COVER',
                '強制平倉 ' || short_quantity || ' 股 ' || asset_symbol
            FROM balance_updates
            RETURNING user_id
        ),
        portfolio_updates AS (
            UPDATE player_portfolios
            SET short_quantity = 0,
                short_average_price = 0,
                last_transaction_at = CURRENT_TIMESTAMP
            FROM balance_updates bu
            WHERE player_portfolios.id = bu.portfolio_id AND bu.current_quantity > 0
            RETURNING bu.user_id, bu.short_quantity, bu.settlement_amount, bu.final_fee
        ),
        portfolio_deletions AS (
            DELETE FROM player_portfolios
            USING balance_updates bu
            WHERE player_portfolios.id = bu.portfolio_id AND bu.current_quantity = 0
            RETURNING bu.user_id, bu.short_quantity, bu.settlement_amount, bu.final_fee
        ),
        transaction_inserts AS (
            INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee)
            SELECT user_id, $1, 'FORCED_COVER', short_quantity, $2, settlement_amount, final_fee
            FROM (
                SELECT * FROM portfolio_updates
                UNION ALL
                SELECT * FROM portfolio_deletions
            ) combined_results
            RETURNING user_id
        )
        SELECT COUNT(DISTINCT user_id) as forced_cover_count FROM transaction_inserts;
        """

        result = await _base_repo.fetch_value(
            batch_settlement_query,
            (asset_id, settlement_price, fee_rate, min_fee, per_share_fee),
            connection=conn,
        )

        return result or 0

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"批量強制結算資產 {asset_id} 的做空倉位失敗: {e}"
        ) from e


async def get_trade_history_by_user_paginated(
    user_id: int,
    page: int = 1,
    per_page: int = 10,
    conn: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """
    分頁獲取指定用戶的交易歷史記錄。

    Args:
        user_id: 用戶ID
        page: 頁碼
        per_page: 每頁數量
        conn: 可選的資料庫連接

    Returns:
        Dict: 包含交易歷史列表和分頁信息的字典

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        page = max(1, page)
        per_page = max(1, per_page)
        offset = (page - 1) * per_page

        # 獲取總數
        total_query = "SELECT COUNT(*) FROM market_transactions WHERE user_id = $1"
        total_transactions = await _base_repo.fetch_value(
            total_query, (user_id,), connection=conn
        )

        # 獲取交易歷史列表
        history_query = """
            SELECT
                mt.id as transaction_id,
                mt.user_id,
                mt.asset_id,
                va.asset_symbol,
                va.asset_name,
                mt.transaction_type,
                mt.quantity,
                mt.price_per_unit,
                mt.total_amount,
                mt.fee,
                mt.timestamp,
                mt.context
            FROM market_transactions mt
            JOIN virtual_assets va ON mt.asset_id = va.asset_id
            WHERE mt.user_id = $1
            ORDER BY mt.timestamp DESC
            LIMIT $2 OFFSET $3
        """
        records = await _base_repo.fetch_all(
            history_query, (user_id, per_page, offset), connection=conn
        )

        return {
            "trades": [dict(record) for record in records],
            "total_trades": total_transactions,
            "current_page": page,
            "per_page": per_page,
            "total_pages": (
                (total_transactions + per_page - 1) // per_page
                if total_transactions
                else 1
            ),
        }
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"分頁查詢用戶 {user_id} 的交易歷史失敗: {e}"
        ) from e
