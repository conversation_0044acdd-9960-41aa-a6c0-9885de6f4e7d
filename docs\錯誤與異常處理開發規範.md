# 機器人錯誤與異常處理開發規範 (v3.0)

---

### 🎯 黃金規則 (Golden Rule)

**作為開發者，你只需要記住一件事：**

> **在「業務邏輯層」(`services`) 中，當業務規則不滿足時，直接 `raise` 一個對應的 `BusinessError`。**

**絕對不要** 在 UI 層（`cogs` 中的指令或 `views` 中的按鈕）自己用 `try...except` 來捕捉錯誤或手動回覆錯誤訊息。框架會自動為你處理好一切。

---

### 一、核心設計哲學

本專案的錯誤處理遵循三大核心原則：

1.  **使用者友好 (User-Friendly)**：對於使用者操作不當或業務規則導致的 **預期內錯誤**，必須向使用者顯示 **清晰、具體、可理解** 的提示訊息。
2.  **開發者友好 (Developer-Friendly)**：對於系統內部發生的 **預期外錯誤**（Bugs、服務中斷等），必須記錄 **詳盡的日誌** 以供除錯，同時給予使用者一個 **統一、簡潔的通用錯誤回覆**，避免洩漏內部技術細節。
3.  **程式碼簡潔 (DRY - Don't Repeat Yourself)**：錯誤處理邏輯應高度集中，避免在各個指令和 UI 元件中重複編寫相同的 `try...except` 程式碼塊。

### 二、開發者職責 vs. 框架職責

為了讓開發流程更簡單，我們明確劃分了你和框架的責任：

| 項目 | ✅ **你的職責 (You Do This)** | 🤖 **框架的職責 (Framework Handles This)** |
| :--- | :--- | :--- |
| **定義錯誤** | 在 `gacha/exceptions.py` 中，為新的業務場景定義一個繼承自 `BusinessError` 的異常類別。 | - |
| **拋出錯誤** | 在 `services` 中，當檢查到業務條件不滿足時，`raise` 你定義的 `BusinessError`。 | - |
| **捕捉錯誤** | **完全不用！** | `BaseView`/`BaseModal` 和全域處理器會自動捕捉所有冒泡的錯誤。 |
| **回覆使用者** | **完全不用！** | 框架會根據錯誤類型，自動產生標準格式的錯誤 Embed 回覆給使用者。 |
| **記錄日誌** | **完全不用！** | 框架會自動為未知的 `Exception` 記錄詳細的錯誤日誌。 |
| **UI 層編碼** | 保持指令和按鈕回呼函式乾淨，專注於呼叫 `services` 中的業務邏輯。 | - |

### 三、開發實踐指南 (How-To)

#### 第 1 步：定義你的「業務錯誤」

所有 **預期內的、可對使用者解釋的業務邏輯錯誤**，都必須定義為繼承自 `gacha.exceptions.BusinessError` 的自訂異常。

**位置**: `gacha/exceptions.py`

**範例**:
```python
# gacha/exceptions.py

# 這是所有業務錯誤的基底，你不需要動它
class BusinessError(Exception):
    """所有業務邏輯異常的基類。"""
    def __init__(self, message: str):
        super().__init__(message)
        self.message = message

# 【你的工作】為「餘額不足」場景，定義一個新錯誤
class InsufficientBalanceError(BusinessError):
    """餘額不足"""
    def __init__(self, required: int, current: int):
        message = f"餘額不足！您需要 {required:,}，但只有 {current:,}。"
        super().__init__(message)
```

#### 第 2 步：在服務 (Service) 中「拋出」錯誤

在你的業務邏輯程式碼中（通常是在 `gacha/services/` 目錄下的檔案），當檢查到不滿足業務規則時，**直接 `raise` 這些自訂異常**。

**範例**:
```python
# gacha/services/trading_service.py
from gacha.exceptions import InsufficientBalanceError

async def execute_trade(user_id: int, required_amount: int):
    current_balance = await get_user_balance(user_id) # 假設這是一個查詢餘額的函式

    # 【你的工作】檢查業務規則，並在不滿足時拋出錯誤
    if current_balance < required_amount:
        raise InsufficientBalanceError(required=required_amount, current=current_balance)

    # ... 執行交易成功的邏輯 ...
```

#### 第 3 步：保持 UI 層（Cog/View）的「乾淨」

所有指令 (`@app_commands.command`) 和視圖中的互動元件 (`@discord.ui.button`) 的回呼函式，都應 **保持乾淨**，專注於呼叫業務邏輯，並讓錯誤自然地「冒泡」出去。

**核心規範**:
*   **不要** 在指令或按鈕回呼函式中使用 `try...except`。
*   所有視圖 (`View`) **必須** 繼承自 `utils.base_view.BaseView`。
*   所有模態框 (`Modal`) **必須** 繼承自 `utils.base_modal.BaseModal`。
*   指令的第一行 **強烈建議** 是 `await interaction.response.defer()`。

**正確的 Cog/View 範例**:
```python
# gacha/cogs/trading_cog.py
from utils.base_view import BaseView
from gacha.services import trading_service

class TradeView(BaseView):
    def __init__(self, bot, user_id):
        super().__init__(bot=bot, user_id=user_id)

    @discord.ui.button(label="接受交易", style=discord.ButtonStyle.success)
    async def accept_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer() # 先回應 Discord，避免逾時

        # 【你的工作】直接呼叫業務邏輯，讓錯誤自然拋出
        # 如果 trading_service.execute_trade 拋出 InsufficientBalanceError，
        # BaseView.on_error 會自動捕捉並回覆給使用者。你什麼都不用做。
        await trading_service.execute_trade(user_id=interaction.user.id, required_amount=1000)

        # 成功後更新 UI
        await interaction.followup.send("交易成功！")
```

### 四、反面教材 (Anti-Patterns)

**絕對禁止** 以下寫法，它們會破壞集中式錯誤處理機制，並導致除錯困難：

**錯誤 1：在 UI 層捕捉並處理 `BusinessError`**
```python
# 【絕對錯誤的寫法】
@discord.ui.button(...)
async def some_button(self, interaction: discord.Interaction, ...):
    try:
        await some_service.do_stuff()
    except BusinessError as e:
        # 這會繞過框架的統一格式化處理，導致介面不一致，且程式碼冗餘。
        await interaction.response.send_message(f"發生錯誤: {e}")
```

**錯誤 2：在 UI 層捕捉通用 `Exception`**
```python
# 【極其錯誤的寫法】
@app_commands.command(...)
async def my_command(self, interaction: discord.Interaction, ...):
    try:
        await some_service.do_stuff()
    except Exception as e:
        # 這會將真正的 Bug (預期外錯誤) 隱藏起來，
        # 框架無法記錄詳細日誌，導致除錯極其困難！
        logger.error("發生未知錯誤: %s", e)
        await interaction.response.send_message("發生了一個未知錯誤。")
```

### 五、成功訊息處理規範

與錯誤處理對應，我們也需要統一成功操作的回應，以確保 UI 的一致性。

**核心原則**: 所有向使用者顯示的「操作成功」類型的訊息，都應使用標準化的 `SuccessEmbed` 類別來建立。

**位置**: `utils/response_embeds.py`

**範例**:
```python
# gacha/cogs/some_cog.py
from utils.response_embeds import SuccessEmbed
from gacha.services import some_service

@app_commands.command(...)
async def my_command(self, interaction: discord.Interaction):
    await interaction.response.defer()

    # 呼叫業務邏輯
    result_message = await some_service.do_something()

    # 使用標準化的 SuccessEmbed
    embed = SuccessEmbed(description=result_message)
    await interaction.followup.send(embed=embed)
