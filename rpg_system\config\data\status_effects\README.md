# Status Effects 分類說明

本資料夾包含了 RPG 系統中所有狀態效果的分類配置文件，參考了 `effect_templates` 資料夾的組織方式。

## 文件結構

### 1. `buff_effects.json` - 正面效果（增益狀態）
包含所有對角色有利的狀態效果：
- 屬性提升效果（攻擊力、防禦力、速度等）
- 回復效果
- 特殊增益效果（無敵、反射等）

**包含的效果：**
- `REGEN_WEAK` - 微弱回復
- `INVINCIBLE_SHORT` - 短暫無敵
- `ATK_BOOST` - 攻擊力提升
- `DEF_BOOST` - 防禦力提升
- `HP_BOOST` - 生命力提升
- `SPD_BOOST` - 速度提升
- `CRIT_RATE_BOOST` - 爆擊率提升
- `CRIT_DMG_BOOST` - 爆擊傷害提升
- `ACCURACY_BOOST` - 命中率提升
- `EVASION_BOOST` - 閃避率提升
- `MP_BOOST` - 法力值提升
- `MP_REGEN_BOOST` - 法力回復提升
- `REFLECT_STATUS` - 傷害反射
- `CRIT_BOOST` - 暴擊強化
- `ALL_STATS_BOOST` - 全能力強化

### 2. `debuff_effects.json` - 負面效果（減益狀態）
包含所有對角色不利的屬性削弱效果：
- 屬性降低效果
- 易傷效果
- 其他負面狀態

**包含的效果：**
- `SLOW_STATUS` - 緩速
- `ARMOR_BROKEN_STATUS` - 破甲
- `PHYSICAL_VULNERABILITY` - 物理易傷
- `COOLDOWN_INCREASED_DEBUFF` - 技能冷却延長
- `PRESSURE_DEBUFF` - 壓力
- `ATK_DEBUFF` - 攻擊力削弱
- `DEF_DEBUFF` - 防禦力削弱
- `SPD_DEBUFF` - 速度削弱
- `ACCURACY_DEBUFF` - 命中率削弱
- `EVASION_DEBUFF` - 閃避率削弱
- `HP_DEBUFF` - 生命力削弱
- `MP_DEBUFF` - 法力值削弱
- `CRIT_DEBUFF` - 暴擊削弱
- `ALL_STATS_DEBUFF` - 全能力削弱

### 3. `control_effects.json` - 控制效果
包含限制角色行動的控制類狀態：
- 眩暈、冰凍等無法行動狀態
- 沉默等限制技能使用狀態

**包含的效果：**
- `FREEZE_STATUS` - 冰凍
- `STUN_STATUS` - 眩暈
- `SILENCE_STATUS` - 沉默

### 4. `damage_over_time_effects.json` - 持續傷害效果
包含每回合造成傷害的狀態效果：
- 中毒、灼燒等持續傷害

**包含的效果：**
- `POISON_STATUS_ID` - 中毒
- `BURN_STATUS` - 灼燒

### 5. `shield_effects.json` - 護盾效果
包含提供傷害吸收的護盾類狀態：
- 各種類型的護盾效果

**包含的效果：**
- `BASIC_SHIELD` - 基礎護盾
- `MAGIC_SHIELD` - 魔法護盾
- `PHYSICAL_BARRIER` - 物理屏障

### 6. `special_effects.json` - 特殊效果
包含具有特殊觸發機制或複雜邏輯的狀態效果：
- 條件觸發效果
- 複合效果

**包含的效果：**
- `PREDICTIVE_DEFENSE_ACTIVE` - 預判防禦激活
- `WEAK_LIFESTEAL_STATUS` - 微弱吸血

## 設計原則

1. **功能導向分類**：按照效果的主要功能進行分類，便於管理和查找
2. **參考現有結構**：遵循 `effect_templates` 資料夾的組織方式
3. **清晰命名**：文件名稱直觀反映其包含的效果類型
4. **完整性**：確保原有 `status_effects.json` 中的所有效果都被正確分類

## 使用說明

- 原有的 `status_effects.json` 文件仍然保留作為備份
- 新的分類結構便於：
  - 按類型查找和管理狀態效果
  - 添加新的狀態效果到對應分類
  - 維護和更新特定類型的效果
  - 提高代碼的可讀性和維護性

## 注意事項

- 所有效果的 ID 和結構保持與原文件完全一致
- 分類僅為組織目的，不影響遊戲邏輯
- 如需添加新效果，請選擇合適的分類文件或創建新的分類
