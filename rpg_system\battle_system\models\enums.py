"""
戰鬥系統枚舉定義
"""

from enum import Enum


class BattleStatus(Enum):
    """戰鬥狀態枚舉"""

    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    PLAYER_WIN = "PLAYER_WIN"
    MONSTER_WIN = "MONSTER_WIN"
    DRAW = "DRAW"


class SkillType(Enum):
    """技能類型枚舉"""

    ACTIVE = "ACTIVE"
    PASSIVE = "PASSIVE"
    INNATE_PASSIVE = "INNATE_PASSIVE"
    PRIMARY_ATTACK = "PRIMARY_ATTACK"


class EventType(Enum):
    """戰鬥事件類型枚舉"""

    # 戰鬥流程事件
    ON_BATTLE_START = "ON_BATTLE_START"
    ON_BATTLE_END = "ON_BATTLE_END"

    # 回合流程事件
    ON_TURN_START = "ON_TURN_START"
    ON_TURN_END = "ON_TURN_END"

    # 行動與技能事件
    ON_PRIMARY_ATTACK_EXECUTED = "ON_PRIMARY_ATTACK_EXECUTED"
    ON_ACTIVE_SKILL_EXECUTED = "ON_ACTIVE_SKILL_EXECUTED"
    ON_SKILL_TARGETED = "ON_SKILL_TARGETED"

    # 傷害與治療事件
    ON_DAMAGE_DEALT = "ON_DAMAGE_DEALT"
    ON_DAMAGE_TAKEN = "ON_DAMAGE_TAKEN"
    ON_HEAL_DEALT = "ON_HEAL_DEALT"
    ON_HEAL_RECEIVED = "ON_HEAL_RECEIVED"
    ON_DODGE = "ON_DODGE"
    ON_MISS = "ON_MISS"

    # 狀態效果事件
    ON_STATUS_EFFECT_APPLIED = "ON_STATUS_EFFECT_APPLIED"
    ON_STATUS_EFFECT_REMOVED = "ON_STATUS_EFFECT_REMOVED"
    ON_STATUS_EFFECT_EXPIRED = "ON_STATUS_EFFECT_EXPIRED"
    ON_STATUS_EFFECT_DISPELLED = "ON_STATUS_EFFECT_DISPELLED"
    ON_STATUS_EFFECT_RESISTED = "ON_STATUS_EFFECT_RESISTED"
    ON_STATUS_EFFECT_TICK = "ON_STATUS_EFFECT_TICK"

    # 生存狀態事件
    ON_ALLY_DEATH = "ON_ALLY_DEATH"
    ON_ENEMY_DEATH = "ON_ENEMY_DEATH"
    ON_COMBATANT_DEATH = "ON_COMBATANT_DEATH"
    ON_HP_THRESHOLD_REACHED = "ON_HP_THRESHOLD_REACHED"


class DamageType(Enum):
    """傷害類型枚舉"""

    DAMAGE = "DAMAGE"  # 統一的傷害類型
    TRUE_DAMAGE = "TRUE_DAMAGE"


class TargetType(Enum):
    """目標類型枚舉"""

    ENEMY_SINGLE = "ENEMY_SINGLE"
    ENEMY_ALL = "ENEMY_ALL"
    ALLY_SINGLE = "ALLY_SINGLE"
    ALLY_ALL = "ALLY_ALL"
    SELF = "SELF"
    ALL = "ALL"


class StatusEffectType(Enum):
    """狀態效果類型枚舉"""

    BUFF = "BUFF"
    DEBUFF = "DEBUFF"
    NEUTRAL = "NEUTRAL"


class ModificationTy(Enum):
    """屬性修改類型枚舉"""

    FLAT_ADD = "FLAT_ADD"
    PERCENTAGE_ADD = "PERCENTAGE_ADD"
    PERCENTAGE_ADD_BASE = "PERCENTAGE_ADD_BASE"
    MULTIPLIER_ADD = "MULTIPLIER_ADD"


class TriggerConditionType(Enum):
    """觸發條件類型枚舉"""

    ON_BATTLE_START = "ON_BATTLE_START"
    ON_TURN_START = "ON_TURN_START"
    ON_TURN_END = "ON_TURN_END"
    ON_ACTION_EXECUTED = "ON_ACTION_EXECUTED"
    ON_DAMAGE_DEALT = "ON_DAMAGE_DEALT"
    ON_DAMAGE_TAKEN = "ON_DAMAGE_TAKEN"
    ON_HEAL_RECEIVED = "ON_HEAL_RECEIVED"
    ON_STATUS_EFFECT_APPLIED = "ON_STATUS_EFFECT_APPLIED"
    ON_ALLY_DEATH = "ON_ALLY_DEATH"
    ON_ENEMY_DEATH = "ON_ENEMY_DEATH"
    ON_HP_THRESHOLD_REACHED = "ON_HP_THRESHOLD_REACHED"
