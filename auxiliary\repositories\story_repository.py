"""
Story Repository
此模組負責與故事相關的資料庫表（stories 和 story_turns）進行互動。
它遵循倉庫模式，提供模組級的異步函數來操作資料庫。
"""

import uuid
from typing import Any, Dict, List, Optional

import asyncpg
from pydantic import BaseModel

from auxiliary.services.story.constants import StoryStatus
from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)

# --- Data Models ---


class Story(BaseModel):
    """代表 stories 表中的一筆記錄"""

    id: uuid.UUID
    user_id: int
    title: Optional[str] = None
    theme_title: Optional[str] = None
    status: str = StoryStatus.ACTIVE.value
    is_public: bool = False


class StoryTurn(BaseModel):
    """代表 story_turns 表中的一筆記錄"""

    story_id: uuid.UUID
    turn_number: int
    role: str
    content: str
    options: Optional[List[str]] = None
    summary: Optional[str] = None
    status_block: Optional[str] = None
    # user_id 不再需要，因為它可以從 story_id 關聯的 stories 表中獲得


class StoryChunkSummary(BaseModel):
    """代表 story_chunk_summaries 表中的一筆記錄"""

    id: uuid.UUID
    story_id: uuid.UUID
    start_turn_number: int
    end_turn_number: int
    content: str


# --- Story (Metadata) Functions ---


async def create_story(
    story_data: Story, conn: Optional[asyncpg.Connection] = None
) -> uuid.UUID:
    """在 stories 表中創建一個新的故事記錄。"""
    query = """
        INSERT INTO stories (id, user_id, title, theme_title, status, is_public)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id;
    """
    story_id = await fetch_value(
        query,
        (
            story_data.id,
            story_data.user_id,
            story_data.title,
            story_data.theme_title,
            story_data.status,
            story_data.is_public,
        ),
        connection=conn,
    )
    return story_id


async def update_story_title(user_id: int, story_id: uuid.UUID, new_title: str) -> bool:
    """更新指定故事的標題。"""
    affected_rows = await execute_query(
        "UPDATE stories SET title = $1 WHERE id = $2 AND user_id = $3",
        (new_title, story_id, user_id),
    )
    return affected_rows > 0


async def update_story_status(
    user_id: int, story_id: uuid.UUID, new_status: str
) -> bool:
    """更新指定故事的狀態 (e.g., 'active', 'paused')。"""
    affected_rows = await execute_query(
        "UPDATE stories SET status = $1 WHERE id = $2 AND user_id = $3",
        (new_status, story_id, user_id),
    )
    return affected_rows > 0


async def toggle_story_public(user_id: int, story_id: uuid.UUID) -> Optional[bool]:
    """切換故事的公開/私有狀態。"""
    query = "UPDATE stories SET is_public = NOT is_public WHERE id = $1 AND user_id = $2 RETURNING is_public;"
    new_status = await fetch_value(query, (story_id, user_id))
    return new_status


async def delete_story(user_id: int, story_id: uuid.UUID) -> bool:
    """
    從 stories 表中刪除一個故事。
    由於設定了 ON DELETE CASCADE，相關的 story_turns 記錄將被自動刪除。
    """
    affected_rows = await execute_query(
        "DELETE FROM stories WHERE id = $1 AND user_id = $2",
        (story_id, user_id),
    )
    return affected_rows > 0


async def pause_user_active_stories(
    user_id: int, conn: Optional[asyncpg.Connection] = None
):
    """暫停用戶所有活躍的故事。"""
    await execute_query(
        "UPDATE stories SET status = $1 WHERE user_id = $2 AND status = $3",
        (StoryStatus.PAUSED.value, user_id, StoryStatus.ACTIVE.value),
        connection=conn,
    )


async def resume_story(
    user_id: int, story_id: uuid.UUID, conn: asyncpg.Connection
) -> bool:
    """恢復一個暫停的故事。應在一個事務中與 pause_user_active_stories 一起被呼叫。"""
    # 這裡的邏輯是先暫停所有，再恢復指定的一個
    await pause_user_active_stories(user_id, conn=conn)
    affected_rows = await execute_query(
        "UPDATE stories SET status = $1 WHERE user_id = $2 AND id = $3",
        (StoryStatus.ACTIVE.value, user_id, story_id),
        connection=conn,
    )
    return affected_rows > 0


# --- StoryTurn (Content) Functions ---


async def add_story_turn(
    turn_data: StoryTurn, conn: Optional[asyncpg.Connection] = None
):
    """在 story_turns 表中新增一個故事回合。"""
    query = """
        INSERT INTO story_turns (story_id, turn_number, role, content, options, summary, status_block)
        VALUES ($1, $2, $3, $4, $5, $6, $7);
    """
    await execute_query(
        query,
        (
            turn_data.story_id,
            turn_data.turn_number,
            turn_data.role,
            turn_data.content,
            turn_data.options,
            turn_data.summary,
            turn_data.status_block,
        ),
        connection=conn,
    )


async def get_story_turns(
    story_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取指定故事的所有回合，按回合數升序排列。"""
    query = "SELECT * FROM story_turns WHERE story_id = $1 ORDER BY turn_number ASC;"
    records = await fetch_all(query, (story_id,), connection=conn)
    return [dict(record) for record in records] if records else []


async def get_story_turns_by_page(
    story_id: uuid.UUID, page: int, page_size: int = 2
) -> List[Dict[str, Any]]:
    """根據頁碼獲取特定範圍的回合。"""
    offset = (page - 1) * page_size
    query = """
        SELECT * FROM story_turns
        WHERE story_id = $1
        ORDER BY turn_number ASC
        LIMIT $2 OFFSET $3;
    """
    records = await fetch_all(query, (story_id, page_size, offset))
    return [dict(record) for record in records] if records else []


async def get_next_turn_number(
    story_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
) -> int:
    """獲取指定故事的下一個回合編號。"""
    max_turn = await fetch_value(
        "SELECT MAX(turn_number) FROM story_turns WHERE story_id = $1",
        (story_id,),
        connection=conn,
    )
    return (max_turn or 0) + 1


async def revert_story_to_turn(
    story_id: uuid.UUID, turn_number: int, conn: Optional[asyncpg.Connection] = None
):
    """將故事回溯到指定的回合數，刪除之後的所有回合。"""
    # 注意：user_id 不再是必要條件，因為 story_id 是唯一的
    query = "DELETE FROM story_turns WHERE story_id = $1 AND turn_number > $2"
    await execute_query(query, (story_id, turn_number), connection=conn)


async def remove_last_ai_response(
    story_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
):
    """刪除最新的 AI 回應。"""
    query = """
        DELETE FROM story_turns
        WHERE id = (
            SELECT id FROM story_turns
            WHERE story_id = $1 AND role = 'assistant'
            ORDER BY turn_number DESC
            LIMIT 1
        )
    """
    await execute_query(query, (story_id,), connection=conn)


# --- Combined Query Functions ---


async def get_user_stories(user_id: int) -> List[Dict[str, Any]]:
    """
    獲取用戶所有故事的摘要列表，並包含每個故事的回合數。
    """
    query = """
        SELECT 
            s.id as story_id,
            s.title as story_title,
            s.theme_title,
            s.status as story_status,
            s.is_public,
            s.created_at,
            s.updated_at,
            COUNT(t.id) FILTER (WHERE t.role = 'assistant') as turn_count
        FROM stories s
        LEFT JOIN story_turns t ON s.id = t.story_id
        WHERE s.user_id = $1
        GROUP BY s.id
        ORDER BY s.updated_at DESC;
    """
    records = await fetch_all(query, (user_id,))
    return [dict(record) for record in records] if records else []


async def get_active_story_summary(user_id: int) -> Optional[Dict[str, Any]]:
    """獲取用戶最新活躍故事的元數據和總回合數，不包含回合內容。"""
    query = """
        SELECT
            s.id,
            s.title,
            s.theme_title,
            s.status,
            s.is_public,
            COUNT(t.id) as total_turn_count
        FROM stories s
        LEFT JOIN story_turns t ON s.id = t.story_id
        WHERE s.user_id = $1 AND s.status = 'active'
        GROUP BY s.id
        ORDER BY s.updated_at DESC
        LIMIT 1;
    """
    record = await fetch_one(query, (user_id,))
    return dict(record) if record else None


async def get_active_story_for_user(user_id: int) -> Optional[Dict[str, Any]]:
    """獲取用戶最新的活躍故事及其所有回合。"""
    # 1. 找到最新的活躍故事
    story_query = f"""
        SELECT id, title, theme_title, status, is_public
        FROM stories
        WHERE user_id = $1 AND status = '{StoryStatus.ACTIVE.value}'
        ORDER BY updated_at DESC
        LIMIT 1;
    """
    story_record = await fetch_one(story_query, (user_id,))

    if not story_record:
        return None

    # 2. 獲取該故事的所有回合
    story_id = story_record["id"]
    turns = await get_story_turns(story_id)

    return {
        "story": dict(story_record),
        "turns": turns,
    }


async def get_story_by_id_if_public(story_id: uuid.UUID) -> Optional[Dict[str, Any]]:
    """如果故事是公開的，則獲取其元數據和所有回合。"""
    # 1. 獲取故事元數據，並檢查是否公開
    story_query = "SELECT * FROM stories WHERE id = $1 AND is_public = TRUE;"
    story_record = await fetch_one(story_query, (story_id,))

    if not story_record:
        return None

    # 2. 獲取該故事的所有回合
    turns = await get_story_turns(story_id)

    return {
        "story": dict(story_record),
        "turns": turns,
        "is_shared": True,  # 保持與舊 API 的兼容性
    }


async def get_story_by_id_if_public_with_summaries(story_id: uuid.UUID) -> Optional[Dict[str, Any]]:
    """如果故事是公開的，則獲取其元數據、所有回合和總結。"""
    # 1. 獲取基本故事數據
    story_data = await get_story_by_id_if_public(story_id)
    if not story_data:
        return None

    # 2. 獲取該故事的所有總結
    chunk_summaries = await get_chunk_summaries_for_story(story_id)

    # 3. 添加總結數據
    story_data["chunk_summaries"] = chunk_summaries

    return story_data


async def get_story_with_limited_turns(
    user_id: int, story_id: uuid.UUID, max_turns: int = 120
) -> Optional[Dict[str, Any]]:
    """獲取用戶的特定故事及其最近的回合（高效版本）。

    Args:
        user_id: 用戶ID
        story_id: 故事ID
        max_turns: 最大回合數，預設120（足夠支援20回合短期記憶+100回合長期摘要）
    """
    story_query = "SELECT * FROM stories WHERE id = $1 AND user_id = $2;"
    story_record = await fetch_one(story_query, (story_id, user_id))

    if not story_record:
        return None

    # 獲取最近的 max_turns 個回合
    turns_query = """
        SELECT * FROM story_turns 
        WHERE story_id = $1 
        ORDER BY turn_number DESC 
        LIMIT $2
    """
    recent_turns_records = await fetch_all(turns_query, (story_id, max_turns))

    # 重新排序為升序（從最早到最新）
    turns = (
        [dict(record) for record in reversed(recent_turns_records)]
        if recent_turns_records
        else []
    )

    return {
        "story": dict(story_record),
        "turns": turns,
    }


async def verify_story_ownership(user_id: int, story_id: uuid.UUID) -> bool:
    """驗證用戶是否擁有指定的故事。"""
    story_query = "SELECT 1 FROM stories WHERE id = $1 AND user_id = $2;"
    result = await fetch_value(story_query, (story_id, user_id))
    return result is not None


async def get_story_status(
    user_id: int, story_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
) -> Optional[str]:
    """獲取用戶故事的狀態（高效方法）。"""
    story_query = "SELECT status FROM stories WHERE id = $1 AND user_id = $2;"
    return await fetch_value(story_query, (story_id, user_id), connection=conn)


async def get_full_story_for_user(
    user_id: int, story_id: uuid.UUID
) -> Optional[Dict[str, Any]]:
    """獲取用戶的特定故事及其所有回合。"""
    story_query = "SELECT * FROM stories WHERE id = $1 AND user_id = $2;"
    story_record = await fetch_one(story_query, (story_id, user_id))

    if not story_record:
        return None

    turns = await get_story_turns(story_id)

    return {
        "story": dict(story_record),
        "turns": turns,
    }


# --- Story Chunk Summary Functions ---


async def create_or_update_chunk_summary(
    story_id: uuid.UUID,
    start_turn_number: int,
    end_turn_number: int,
    content: str,
    conn: Optional[asyncpg.Connection] = None,
) -> uuid.UUID:
    """創建或更新一條「大總結」記錄。"""
    query = """
        INSERT INTO story_chunk_summaries (story_id, start_turn_number, end_turn_number, content)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (story_id, start_turn_number, end_turn_number)
        DO UPDATE SET 
            content = EXCLUDED.content,
            updated_at = CURRENT_TIMESTAMP
        RETURNING id;
    """
    summary_id = await fetch_value(
        query,
        (story_id, start_turn_number, end_turn_number, content),
        connection=conn,
    )
    return summary_id


async def get_chunk_summaries_for_story(
    story_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """獲取指定故事的所有「大總結」。"""
    query = """
        SELECT * FROM story_chunk_summaries 
        WHERE story_id = $1 
        ORDER BY start_turn_number ASC;
    """
    records = await fetch_all(query, (story_id,), connection=conn)
    return [dict(record) for record in records] if records else []


async def get_story_turns_by_range(
    story_id: uuid.UUID,
    start_turn_number: int,
    end_turn_number: int,
    conn: Optional[asyncpg.Connection] = None,
) -> List[Dict[str, Any]]:
    """獲取指定範圍內的所有回合數據。"""
    query = """
        SELECT * FROM story_turns 
        WHERE story_id = $1 AND turn_number >= $2 AND turn_number <= $3
        ORDER BY turn_number ASC;
    """
    records = await fetch_all(
        query, (story_id, start_turn_number, end_turn_number), connection=conn
    )
    return [dict(record) for record in records] if records else []


async def delete_chunk_summary(
    summary_id: uuid.UUID, conn: Optional[asyncpg.Connection] = None
) -> bool:
    """刪除一條「大總結」（用於重新生成功能）。"""
    affected_rows = await execute_query(
        "DELETE FROM story_chunk_summaries WHERE id = $1",
        (summary_id,),
        connection=conn,
    )
    return affected_rows > 0


async def delete_chunk_summary_by_range(
    story_id: uuid.UUID,
    start_turn_number: int,
    end_turn_number: int,
    conn: Optional[asyncpg.Connection] = None,
) -> bool:
    """根據範圍刪除「大總結」（用於重新生成功能）。"""
    affected_rows = await execute_query(
        "DELETE FROM story_chunk_summaries WHERE story_id = $1 AND start_turn_number = $2 AND end_turn_number = $3",
        (story_id, start_turn_number, end_turn_number),
        connection=conn,
    )
    return affected_rows > 0
