"""
Gacha系統排行榜視圖
提供排行榜展示的Discord嵌入消息生成方法
"""

from __future__ import annotations

import asyncio
import time
from datetime import datetime
from typing import Any, Callable, Optional, Union

import discord
from discord.ext.commands import Bot

from config.app_config import LeaderboardConfigDetail, get_config
from gacha.constants import RarityLevel
from gacha.services import leaderboard_service, profile_service
from gacha.views import utils as view_utils
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views.profile.profile_view import ProfileLikeButton
from gacha.views.utils import format_oil
from utils.base_modal import BaseModal
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


def _is_pool_filtered(pool_type: Optional[str]) -> bool:
    """檢查是否有特定卡池篩選（排除 'all_pools' 這種全選項）"""
    return bool(pool_type and pool_type not in ("all", "all_pools"))


def _is_all_pools_selected(pool_type: Optional[str]) -> bool:
    """檢查是否選擇了 '所有卡池' 選項"""
    return not pool_type or pool_type == "all_pools"


class LeaderboardEmbedBuilder(BaseEmbedBuilder):
    """構建排行榜嵌入消息的類"""

    MEDAL_EMOJIS = {1: "🥇", 2: "🥈", 3: "🥉"}
    REPLY_EMOJI = "<:Reply:1357534074830590143>"
    REPLY_CONT_EMOJI = "<:ReplyCont:1383146319425699931>"
    GREEN_BAR_EMOJI = "<:greenbar:1366671921902784583>"
    EMPTY_BAR_EMOJI = "<:emptybar:1366671928449957898>"
    THUMBNAIL_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1382170293555957841/ranking.png?ex=684a2de5&is=6848dc65&hm=684c9fc44a3408efc60191523eb50838606d57bd0c6d9ea52f2c8e7809d0f3f2&"
    FOOTER_ICON_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1382170894813888532/magnifying-glass.png?ex=684a2e74&is=6848dcf4&hm=2c40d1b8576bc0e89790cc1dbf6eaae42c87992bf40cb85aca07049b16d25a91&"

    def __init__(
        self,
        leaderboard_data: dict[str, Any],
        leaderboard_type: str,
        items_per_page: int,
        asset_symbol: Optional[str] = None,
        current_user_rank: Optional[dict[str, Any]] = None,
        pool_type: Optional[str] = None,
    ):
        """初始化排行榜Embed構建器

        參數:
            leaderboard_data: 排行榜數據
            leaderboard_type: 排行榜類型
            items_per_page: 每頁顯示數量
            asset_symbol: 股票代碼 (可選, 用於特定股票排行)
            current_user_rank: 當前用戶的排名信息 (可選)
            pool_type: 卡池類型 (可選)
        """
        super().__init__(
            data={
                "leaderboard_data": leaderboard_data,
                "leaderboard_type": leaderboard_type,
                "items_per_page": items_per_page,
                "asset_symbol": asset_symbol,
                "current_user_rank": current_user_rank,
                "pool_type": pool_type,
            }
        )
        self.leaderboard_data = self.data["leaderboard_data"]
        self.leaderboard_type = self.data["leaderboard_type"]
        self.items_per_page = self.data["items_per_page"]
        self.asset_symbol = self.data["asset_symbol"]
        self.current_user_rank = self.data["current_user_rank"]
        self.pool_type = self.data["pool_type"]

    def build_embed(self) -> discord.Embed:
        """構建排行榜嵌入消息

        返回:
            discord.Embed: 排行榜嵌入消息
        """
        if not self.leaderboard_data or not self.leaderboard_data.get("results"):
            return self._create_base_embed(
                title="排行榜", description="暫無數據", color=discord.Color.light_gray()
            )

        from config.app_config import get_config

        leaderboard_config = get_config("gacha_core_settings.leaderboard_config")
        config: Optional[LeaderboardConfigDetail] = None
        if isinstance(leaderboard_config, dict) and self.leaderboard_type:
            config = leaderboard_config.get(self.leaderboard_type)

        if config:
            base_title = config.title
            base_color = config.color
            base_description = config.description
        else:
            # Fallback if config is not found
            config = LeaderboardConfigDetail(
                title=f"{self.leaderboard_type} 排行榜",
                color=7506394,
                description="",
                category="default",
                label=None,
                pool_filterable=False,
            )
            base_title = config.title
            base_color = config.color
            base_description = config.description
        current_page = self.leaderboard_data.get("current_page", 1)
        total_pages = self.leaderboard_data.get("total_pages", 1)
        total_users = self.leaderboard_data.get("total_users", 0)
        results = self.leaderboard_data.get("results", [])
        embed_title = base_title
        if self.leaderboard_type == "stock_holding" and self.asset_symbol:
            asset_name = results[0].get("asset_name") if results else None
            embed_title = (
                f"{asset_name} ({self.asset_symbol}) 持有排行"
                if asset_name
                else f"{base_title} ({self.asset_symbol})"
            )
        elif _is_pool_filtered(self.pool_type):
            pool_name = self.pool_type
            # 對於抽卡次數排行榜，使用 pool_configurations 來獲取名稱
            if self.leaderboard_type == "draws":
                pool_configs = get_config("gacha_core_settings.pool_configurations")
                if isinstance(pool_configs, dict) and self.pool_type:
                    pool_config = pool_configs.get(self.pool_type)
                    if pool_config:
                        pool_name = pool_config.name
            else:
                pool_names = get_config("gacha_core_settings.pool_type_names")
                if isinstance(pool_names, dict) and self.pool_type:
                    pool_name = pool_names.get(self.pool_type, self.pool_type)
            embed_title = f"{base_title} - {pool_name}"
        embed = self._create_base_embed(
            title=None, color=discord.Color(base_color) if base_color else None
        )

        # 設置作者欄（將標題移動到作者欄）
        self._set_author(embed, name=embed_title)

        # 設置縮圖
        self._set_thumbnail(embed, url=self.THUMBNAIL_URL)

        # 構建描述內容
        description_parts = []

        # 添加基本描述
        if base_description:
            description_parts.append(base_description)

        # 添加排行榜數據
        if results:
            leaderboard_lines = []

            for entry in results:
                # 使用已經在 LeaderboardCog._enrich_results_with_rank() 中計算好的排名
                rank = entry.get("rank", 1)
                entry_text = self._format_entry(entry)
                rank_prefix = self.MEDAL_EMOJIS.get(rank, f"`{rank}.`")
                user_name = entry.get("user_name", "未知用戶")
                leaderboard_lines.append(f"{rank_prefix} **{user_name}**\n{entry_text}")

            description_parts.append("\n\n".join(leaderboard_lines))
        else:
            description_parts.append("目前此排行暫無數據。")

            # 添加當前用戶排名信息（美化顯示）
        if self.current_user_rank and "rank" in self.current_user_rank:
            user_rank = self.current_user_rank["rank"]

            # 根據排名選擇不同的顯示樣式
            if user_rank <= 3:
                rank_display = (
                    f"{self.MEDAL_EMOJIS.get(user_rank, '🏆')} **第 {user_rank} 名**"
                )
            elif user_rank <= 10:
                rank_display = f"🔥 **第 {user_rank} 名**"
            elif user_rank <= 100:
                rank_display = f"⭐ **第 {user_rank} 名**"
            else:
                rank_display = f"📊 **第 {user_rank} 名**"

            description_parts.append(f"-# 你的排名：{rank_display}")

        # 組合所有描述內容
        embed.description = "\n\n".join(description_parts)

        # 構建 footer
        footer_parts = []

        if (query_time := self.leaderboard_data.get("query_time")) is not None:
            footer_parts.append(f"查詢: {query_time:.2f}s")
        if self.leaderboard_data.get("updated_at"):
            footer_parts.append("每小時更新")
        footer_parts.append(f"頁 {current_page:,}/{total_pages:,}")
        if total_users > 0:
            footer_parts.append(f"共 {total_users:,} 名玩家")

        embed.set_footer(text=" • ".join(footer_parts), icon_url=self.FOOTER_ICON_URL)
        return embed

    def _get_formatter(self) -> Callable[[dict[str, Any]], str]:
        """根據排行榜類型返回對應的格式化函數"""
        formatters = {
            "rarity": self._format_rarity_entry,
            "completion": self._format_completion_entry,
            "collection_unique": self._format_collection_unique_entry,
            "luck_index": self._format_luck_index_entry,
            "avg_draws": self._format_avg_draws_entry,
            "longest_drought": self._format_longest_drought_entry,
            "draws": self._format_draws_entry,
            "oil": self._format_oil_entry,
            "portfolio_value": lambda e: f"總資產價值: {format_oil(e.get('total_portfolio_value', 0))}",
            "stock_holding": lambda e: f"持有數量: `{e.get('stock_quantity', 0):,}` 股",
            "trade_volume": lambda e: f"總交易額: {format_oil(e.get('total_trade_volume', 0))}",
            "trade_count": lambda e: f"總交易次數: `{e.get('total_trade_count', 0):,}` 次",
            "stock_profit_loss": self._format_stock_profit_loss_entry,
        }

        from gacha.core.game_registry import GameRegistry

        if GameRegistry.is_valid_game_type(self.leaderboard_type):
            return self._format_game_stats_entry

        return formatters.get(self.leaderboard_type, lambda e: "未知排行榜類型")

    def _format_entry(self, entry: dict[str, Any]) -> str:
        """格式化排行榜條目"""
        formatter = self._get_formatter()
        return formatter(entry)

    def _format_rarity_entry(self, entry: dict[str, Any]) -> str:
        rarity_parts = []
        # 直接迭代 IntEnum，並按值降序排序
        for rarity_level in sorted(RarityLevel, reverse=True):
            rarity_num = rarity_level.value
            if (count := entry.get(f"rarity_{rarity_num}_count", 0)) > 0:
                try:
                    emoji = view_utils.get_rarity_display_code(rarity_level)
                except ValueError:
                    emoji = "❓"
                rarity_parts.append(f"{emoji} x`{count:,}`")
        return " | ".join(rarity_parts) if rarity_parts else "尚未收集任何卡片"

    def _format_completion_entry(self, entry: dict[str, Any]) -> str:
        percentage = entry.get("completion_percentage", 0)
        try:
            percentage = (
                float(percentage) if isinstance(percentage, str) else percentage
            )
        except (ValueError, TypeError):
            percentage = 0

        progress = int(percentage / 10)
        progress_bar = self.GREEN_BAR_EMOJI * progress + self.EMPTY_BAR_EMOJI * (
            10 - progress
        )
        collected = entry.get("collected_cards", 0)
        total = entry.get("total_cards", 0)
        return f"完成度: `{collected:,}/{total:,} ({percentage:.1f}%)`\n進度: {progress_bar}"

    def _format_collection_unique_entry(self, entry: dict[str, Any]) -> str:
        rarity_strings = []
        # 直接迭代 IntEnum，並按值降序排序
        for rarity_level in sorted(RarityLevel, reverse=True):
            rarity_num = rarity_level.value
            collected = entry.get(f"rarity_{rarity_num}_unique_cards", 0)
            total = entry.get(f"rarity_{rarity_num}_total_cards", 0)

            if total > 0:
                try:
                    emoji = view_utils.get_rarity_display_code(rarity_level)
                except ValueError:
                    emoji = "❓"
                rarity_strings.append(f"{emoji} {collected:,}/{total:,}")

        if not rarity_strings:
            return f"{self.REPLY_CONT_EMOJI}尚未收集任何卡片"

        rarity_lines = [
            f"{self.REPLY_CONT_EMOJI}{' | '.join(chunk)}"
            for i in range(0, len(rarity_strings), 4)
            if (chunk := rarity_strings[i : i + 4])
        ]
        # Make the last line use the end emoji
        if rarity_lines:
            last_line = rarity_lines[-1]
            rarity_lines[-1] = last_line.replace(
                self.REPLY_CONT_EMOJI, self.REPLY_EMOJI, 1
            )

        return "\n".join(rarity_lines)

    def _format_draws_entry(self, entry: dict[str, Any]) -> str:
        # 根據卡池類型決定顯示格式
        pool_draws = entry.get("pool_draws", 0)
        total_draws = entry.get("total_draws", 0)

        # 獲取卡池的顯示名稱
        pool_name = self.pool_type
        pool_names = get_config("gacha_core_settings.pool_type_names")
        if isinstance(pool_names, dict) and self.pool_type:
            pool_name = pool_names.get(self.pool_type, self.pool_type)

        if _is_all_pools_selected(self.pool_type):
            # 所有卡池：顯示總抽卡次數
            return f"總抽卡: `{total_draws:,}` | {format_oil(entry.get('oil_balance', 0), label='餘額')}"
        else:
            # 特定卡池：顯示該卡池的抽卡次數和總次數
            return f"**{pool_name}**: `{pool_draws:,}` | 總抽卡: `{total_draws:,}` | {format_oil(entry.get('oil_balance', 0), label='餘額')}"

    def _get_luck_rating(self, luck_index: float) -> str:
        """根據運氣指數獲取評級"""
        if luck_index == 0:
            return "🚫"
        elif luck_index >= 2.0:
            return "🌟"
        elif luck_index >= 1.5:
            return "✨"
        elif luck_index >= 1.2:
            return "😊"
        elif luck_index >= 0.8:
            return "😐"
        elif luck_index >= 0.6:
            return "😕"
        elif luck_index >= 0.4:
            return "😢"
        else:
            return "💀"

    def _format_luck_index_entry(self, entry: dict[str, Any]) -> str:
        luck_index = entry.get("luck_index", 1.0)
        luck_rating = self._get_luck_rating(luck_index)
        return f"運氣指數: `{luck_index:.2f}` {luck_rating}"

    def _format_avg_draws_entry(self, entry: dict[str, Any]) -> str:
        avg_draws = entry.get("avg_draws_per_top_tier", 0)
        return f"平均出貨: `{avg_draws:.1f}` 抽"

    def _format_longest_drought_entry(self, entry: dict[str, Any]) -> str:
        longest_drought = entry.get("longest_drought", 0)
        return f"最長乾旱: `{longest_drought:,}` 抽"

    def _format_oil_entry(self, entry: dict[str, Any]) -> str:
        return f"{format_oil(entry.get('oil_balance', 0), label='餘額')} | 抽卡次數: {entry.get('total_draws', 0):,}"

    def _format_stock_profit_loss_entry(self, entry: dict[str, Any]) -> str:
        """格式化股票已實現盈虧排行榜條目"""
        total_pnl = entry.get("total_profit_loss", 0)
        try:
            total_pnl = float(total_pnl) if isinstance(total_pnl, str) else total_pnl
        except (ValueError, TypeError):
            total_pnl = 0

        # 根據盈虧情況選擇顏色指示器
        if total_pnl > 0:
            pnl_indicator = "🟢"
            pnl_text = f"獲利 {total_pnl:+.2f}"
        elif total_pnl < 0:
            pnl_indicator = "🔴"
            pnl_text = f"虧損 {abs(total_pnl):.2f}"
        else:
            pnl_indicator = "⚪"
            pnl_text = "損益平衡 0.00"

        return f"總盈虧: {pnl_indicator} `{pnl_text}`"

    def _format_game_stats_entry(self, entry: dict[str, Any]) -> str:
        """格式化遊戲統計排行榜條目（優化版本 - 學習/STOCK指令排版風格）"""
        try:
            stats = {
                "total_games": entry.get("total_games", 0) or 0,
                "total_profit_loss": entry.get("total_profit_loss", 0) or 0,
                "win_rate": entry.get("win_rate", 0) or 0,
                "max_win": entry.get("max_win", 0) or 0,
                "max_loss": entry.get("max_loss", 0) or 0,
            }
            profit_display = format_oil(stats["total_profit_loss"], "盈虧")
            game_specific_stats = entry.get("game_specific_stats", {})
            consecutive_wins = (
                game_specific_stats.get("consecutive_wins", 0)
                if isinstance(game_specific_stats, dict)
                else 0
            )
            main_stats = f"{self.REPLY_CONT_EMOJI} {profit_display}\n"
            main_stats += f"{self.REPLY_CONT_EMOJI} 勝率 : `{stats['win_rate']:.1f}%` | 場次 : `{stats['total_games']:,}`"
            game_type = entry.get("game_type", "")
            specific_stats = ""
            logger.debug(
                "格式化遊戲統計: game_type=%s, game_specific_stats=%s",
                game_type,
                game_specific_stats,
            )

            if game_type == "blackjack" and isinstance(game_specific_stats, dict):
                blackjack_count = game_specific_stats.get("blackjack_count", 0)
                five_card_trick_count = game_specific_stats.get(
                    "five_card_trick_count", 0
                )
                specific_stats = f"{self.REPLY_EMOJI} BJ次數 : `{blackjack_count:,}` | 過五關 : `{five_card_trick_count:,}`"
            elif game_type == "dice" and isinstance(game_specific_stats, dict):
                big_rate = game_specific_stats.get("big_choice_win_rate", 0)
                small_rate = game_specific_stats.get("small_choice_win_rate", 0)
                triple_rate = game_specific_stats.get("triple_win_rate", 0)
                triple_count = game_specific_stats.get("triple_count", 0)
                specific_stats = f"{self.REPLY_CONT_EMOJI} 選大勝率 : `{big_rate:.1f}%` | 選小勝率 : `{small_rate:.1f}%`\n"
                specific_stats += f"{self.REPLY_EMOJI} 押圍骰勝率 : `{triple_rate:.1f}%` | 開出圍骰 : `{triple_count:,}` 次"
            elif game_type == "mines" and isinstance(game_specific_stats, dict):
                avg_tiles = game_specific_stats.get("avg_tiles_revealed", 0)
                coin_found = game_specific_stats.get("special_coin_found", 0)
                star_found = game_specific_stats.get("special_star_found", 0)
                specific_stats = f"{self.REPLY_EMOJI} 平均挖掘 : `{avg_tiles:.1f}`格 | 金幣 : `{coin_found}` | 星星 : `{star_found}`"
            elif game_type == "tower" and isinstance(game_specific_stats, dict):
                max_level = game_specific_stats.get("max_level_reached", 0)
                cashout_count = game_specific_stats.get("cashout_count", 0)
                avg_levels = game_specific_stats.get("avg_levels_completed", 0)
                specific_stats = f"{self.REPLY_EMOJI} 最高層級 : `{max_level}` | 提現次數 : `{cashout_count}` | 平均層級 : `{avg_levels:.1f}`"
            elif game_type == "slot" and isinstance(game_specific_stats, dict):
                jackpot_count = game_specific_stats.get("jackpot_count", 0)
                max_consecutive = game_specific_stats.get("max_consecutive_symbols", 0)
                rtp_percentage = game_specific_stats.get("rtp_percentage", 0)
                specific_stats = f"{self.REPLY_EMOJI} 頭獎次數 : `{jackpot_count:,}` | 最大連線 : `{max_consecutive:,}` | RTP : `{rtp_percentage:.1f}%`"
            elif game_type == "poker1v1" and isinstance(game_specific_stats, dict):
                total_hands = game_specific_stats.get("total_hands_played", 0)
                fold_rate = game_specific_stats.get("fold_rate", 0)
                all_in_count = game_specific_stats.get("total_all_in_count", 0)
                specific_stats = f"{self.REPLY_EMOJI} 總手牌 : `{total_hands:,}` | 棄牌率 : `{fold_rate:.1f}%` | 全下次數 : `{all_in_count:,}`"
            elif game_type == "spin_wheel" and isinstance(game_specific_stats, dict):
                result_counts = game_specific_stats.get("result_type_counts", {})
                big_wins = result_counts.get("big_win", 0)
                medium_wins = result_counts.get("medium_win", 0)
                small_wins = result_counts.get("small_win", 0)
                total_wins = big_wins + medium_wins + small_wins

                wins_display = f"大獎:`{big_wins:,}` 中獎:`{medium_wins:,}` 小獎:`{small_wins:,}` (總勝場: `{total_wins:,}`)"

                risk_stats = game_specific_stats.get("risk_stats", {})
                risk_display_parts = []
                for risk_key in ["高", "中", "低"]:
                    if risk_key in risk_stats:
                        stats_data = risk_stats[risk_key]
                        win_rate = (
                            (stats_data["wins"] / stats_data["games"] * 100)
                            if stats_data["games"] > 0
                            else 0
                        )
                        risk_display_parts.append(f"{risk_key}風險:`{win_rate:.1f}%`")
                risk_display = (
                    " | ".join(risk_display_parts)
                    if risk_display_parts
                    else "無風險數據"
                )

                specific_stats = f"{self.REPLY_CONT_EMOJI} {wins_display}\n{self.REPLY_EMOJI} {risk_display}"
            elif game_type == "baccarat" and isinstance(game_specific_stats, dict):
                banker_rate = game_specific_stats.get("banker_win_rate", 0)
                player_rate = game_specific_stats.get("player_win_rate", 0)
                tie_rate = game_specific_stats.get("tie_win_rate", 0)
                specific_stats = f"{self.REPLY_CONT_EMOJI} 莊勝率 : `{banker_rate:.1f}%` | 閒勝率 : `{player_rate:.1f}%`\n"
                specific_stats += f"{self.REPLY_EMOJI} 和勝率 : `{tie_rate:.1f}%`"

            max_win_display = format_oil(stats["max_win"], "最大贏")
            max_loss_display = format_oil(abs(stats["max_loss"]), "最大輸")
            final_stats = f"🔥 連勝 : `{consecutive_wins:,}` | {max_win_display} | {max_loss_display}"
            if specific_stats:
                return f"{main_stats}\n{specific_stats}\n{final_stats}"
            else:
                return f"{main_stats}\n{final_stats}"
        except Exception as e:
            logger.error("格式化遊戲統計條目時出錯: %s", e, exc_info=True)
            return f"格式化錯誤: {e}"

    def _calculate_percentage(self, value: int, total: int) -> float:
        """計算百分比，避免除以零錯誤"""
        return round(value * 100.0 / total, 1) if total else 0.0


class PlayerSearchModal(BaseModal):
    player_input = discord.ui.TextInput(
        label="玩家名稱或ID",
        placeholder="輸入玩家名稱或玩家ID（如：378830537630023683）",
        required=True,
        min_length=1,
        max_length=32,
    )

    def __init__(self, view: "LeaderboardView"):
        super().__init__(title="查詢玩家", bot=view.bot)
        self.leaderboard_view = view
        self.leaderboard_type = view.current_leaderboard_type
        self.asset_symbol = view.current_asset_symbol
        self.game_type = view.current_game_type
        self.game_stat_type = view.current_game_stat_type
        self.pool_type = view.current_pool_type
        self.items_per_page = (
            1
            if self.leaderboard_type == "profile_likes"
            else leaderboard_service.items_per_page
        )

    async def on_submit(self, interaction: discord.Interaction):
        from utils.logger import logger

        search_input = self.player_input.value.strip()
        logger.info(
            f"[LEADERBOARD_SEARCH] 用戶 {interaction.user.id} 搜索: '{search_input}'"
        )

        await interaction.response.defer(ephemeral=True, thinking=True)
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        is_id_search = search_input.isdigit()
        search_params = (
            {"user_id": int(search_input)}
            if is_id_search
            else {"player_name": search_input}
        )

        logger.info(
            f"[LEADERBOARD_SEARCH] 搜索參數: {search_params}, 排行榜類型: {self.leaderboard_type}"
        )

        player_data = await leaderboard_service.search_player_in_leaderboard(
            leaderboard_type=self.leaderboard_type,
            asset_symbol=self.asset_symbol,
            game_type=self.game_type,
            game_stat_type=self.game_stat_type,
            pool_type=self.pool_type,
            **search_params,
        )

        logger.info(f"[LEADERBOARD_SEARCH] 搜索結果: {player_data}")

        if player_data:
            player_name = player_data.get("user_name", search_input)
            player_rank = player_data["rank"]

            logger.info(
                f"[LEADERBOARD_SEARCH] 找到玩家: {player_name}, 排名: {player_rank}"
            )

            is_profile_likes = self.leaderboard_type == "profile_likes"
            target_page = (
                player_rank
                if is_profile_likes
                else (player_rank - 1) // self.items_per_page + 1
            )

            logger.info(
                f"[LEADERBOARD_SEARCH] 計算目標頁面: {target_page} (is_profile_likes: {is_profile_likes})"
            )

            if is_profile_likes:
                message = f"已找到玩家「{player_name}」，排名第 {player_rank} 位。正在為您顯示該玩家的個人檔案..."
            else:
                message = f"已找到玩家「{player_name}」，排名第 {player_rank} 位，在第 {target_page} 頁。排行榜將刷新至該頁面。"

            embed = SuccessEmbed(description=message)
            await interaction.followup.send(embed=embed, ephemeral=True)

            logger.info(
                f"[LEADERBOARD_SEARCH] 開始重新加載排行榜數據，目標頁面: {target_page}"
            )
            await self.leaderboard_view._reload_leaderboard_data(
                interaction, new_page=target_page
            )
            logger.info("[LEADERBOARD_SEARCH] 排行榜數據重新加載完成")


# --- Component Subclasses ---


class TypeSelect(discord.ui.Select):
    def __init__(self, parent_view: "LeaderboardView", row: int):
        self.parent_view = parent_view
        options = self._get_options()
        super().__init__(
            placeholder=f"切換 {parent_view.current_category.capitalize()} 分類排行...",
            options=options
            or [
                discord.SelectOption(
                    label="無可用類型", value="_no_types_", default=True
                )
            ],
            disabled=not options,
            row=row,
        )

    def _get_options(self) -> list[discord.SelectOption]:
        options = self.parent_view._get_leaderboard_types_for_category(
            self.parent_view.current_category
        )
        for option in options:
            option.default = option.value == self.parent_view.current_leaderboard_type
        if not any(o.default for o in options) and options:
            options[0].default = True
        return options

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        selected_type = self.values[0]

        # Reset state for the new type
        self.parent_view.current_leaderboard_type = selected_type
        self.parent_view.current_page = 1
        self.parent_view.current_user_rank = None
        self.parent_view.current_asset_symbol = None
        self.parent_view.current_game_type = None
        self.parent_view.current_game_stat_type = None

        leaderboard_config = get_config("gacha_core_settings.leaderboard_config")
        if isinstance(leaderboard_config, dict):
            config = leaderboard_config.get(selected_type)
            if config and getattr(config, "pool_filterable", False):
                # 所有支援卡池篩選的排行榜都默認選擇 "所有卡池"
                self.parent_view.current_pool_type = "all_pools"
            else:
                self.parent_view.current_pool_type = None
        else:
            self.parent_view.current_pool_type = None

        await self.parent_view._reload_leaderboard_data(
            interaction, new_page=1, refresh_user_rank=True
        )


class AssetSelect(discord.ui.Select):
    def __init__(self, parent_view: "LeaderboardView", row: int):
        self.parent_view = parent_view
        super().__init__(placeholder="選擇股票...", row=row, disabled=True)

    async def populate_options(self):
        assets = await leaderboard_service.get_all_market_assets()
        target_symbol = self.parent_view.current_asset_symbol
        if not assets:
            self.disabled = True
            self.placeholder = "市場上暫無股票"
            self.options = [
                discord.SelectOption(
                    label="無可用股票", value="_NO_ASSETS_", default=True
                )
            ]
        else:
            self.disabled = False
            self.placeholder = "選擇要查詢的股票..."
            self.options = [
                discord.SelectOption(
                    label=f"{asset['name']} ({asset['symbol']})",
                    value=asset["symbol"],
                    default=asset["symbol"] == target_symbol,
                    emoji="💰",
                )
                for asset in assets[:25]
            ]
            if not any(o.default for o in self.options) and self.options:
                self.options[0].default = True

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.parent_view.current_asset_symbol = self.values[0]
        self.parent_view.current_page = 1
        self.parent_view.current_user_rank = None
        await self.parent_view._reload_leaderboard_data(
            interaction, new_page=1, refresh_user_rank=True
        )


class GameStatSelect(discord.ui.Select):
    def __init__(self, parent_view: "LeaderboardView", row: int):
        self.parent_view = parent_view
        from gacha.services.game_stats_service import get_available_stats

        stats = []
        if parent_view.current_leaderboard_type:
            stats = get_available_stats(parent_view.current_leaderboard_type)
        options = [
            discord.SelectOption(
                label=stat["name"],
                value=stat["value"],
                emoji=stat["emoji"],
                default=stat["value"] == parent_view.current_game_stat_type,
            )
            for stat in stats[:25]
        ]
        super().__init__(placeholder="選擇統計類型...", options=options, row=row)

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.parent_view.current_game_stat_type = self.values[0]
        self.parent_view.current_page = 1
        self.parent_view.current_user_rank = None
        await self.parent_view._reload_leaderboard_data(
            interaction, new_page=1, refresh_user_rank=True
        )


class PoolSelect(discord.ui.Select):
    def __init__(self, parent_view: "LeaderboardView", row: int):
        self.parent_view = parent_view
        options = self._build_pool_options()
        super().__init__(placeholder="篩選卡池...", options=options, row=row)

    def _build_pool_options(self) -> list[discord.SelectOption]:
        """構建卡池選項列表"""
        # 添加 "所有卡池" 選項
        options = [
            discord.SelectOption(
                label="所有卡池",
                value="all_pools",
                emoji="🌐",
                default=self.parent_view.current_pool_type == "all_pools",
            )
        ]

        # 根據排行榜類型決定顯示哪些卡池
        if self.parent_view.current_leaderboard_type == "draws":
            # 抽卡次數排行榜：只顯示用戶實際可以選擇的卡池
            self._add_user_selectable_pools(options)
        else:
            # 其他排行榜：顯示所有實際存在的卡池
            self._add_all_existing_pools(options)

        return options

    def _add_user_selectable_pools(self, options: list[discord.SelectOption]) -> None:
        """添加用戶實際可以選擇的卡池選項"""
        pool_configs = get_config("gacha_core_settings.pool_configurations")
        if isinstance(pool_configs, dict):
            for pool_key, pool_config in pool_configs.items():
                if pool_key != "main":  # 排除主卡池，因為無法直接選擇
                    options.append(
                        discord.SelectOption(
                            label=pool_config.name,
                            value=pool_key,
                            emoji="🃏",
                            default=pool_key == self.parent_view.current_pool_type,
                        )
                    )

    def _add_all_existing_pools(self, options: list[discord.SelectOption]) -> None:
        """添加所有實際存在的卡池選項"""
        pool_names = get_config("gacha_core_settings.pool_type_names")
        if isinstance(pool_names, dict):
            for pool_key, pool_name in pool_names.items():
                options.append(
                    discord.SelectOption(
                        label=pool_name,
                        value=pool_key,
                        emoji="🃏",
                        default=pool_key == self.parent_view.current_pool_type,
                    )
                )

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        self.parent_view.current_pool_type = self.values[0]
        self.parent_view.current_page = 1
        self.parent_view.current_user_rank = None
        await self.parent_view._reload_leaderboard_data(
            interaction, new_page=1, refresh_user_rank=True
        )


class SearchPlayerButton(discord.ui.Button):
    def __init__(self, parent_view: "LeaderboardView", row: int):
        super().__init__(
            label="查詢玩家", style=discord.ButtonStyle.success, emoji="🔍", row=row
        )
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.send_modal(PlayerSearchModal(self.parent_view))


class LeaderboardView(BasePaginationView):
    # 將 emoji 映射表定義為類別屬性以便集中管理
    _TYPE_EMOJI_MAP = {
        "oil": "💰",
        "draws": "🎲",
        "rarity": "⭐",
        "completion": "📋",
        "collection_unique": "🎴",
        "luck_index": "🍀",
        "avg_draws": "🎯",
        "longest_drought": "🏜️",
        "portfolio_value": "💎",
        "stock_holding": "📊",
        "trade_volume": "📈",
        "trade_count": "🔄",
        "stock_profit_loss": "💹",
        "game_stats": "🎮",
        "profile_likes": "❤️",
    }

    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        initial_category: str,
        initial_params: dict[str, Any],
        leaderboard_data: dict[str, Any],
        bot: Bot,
        original_interaction: Optional[discord.Interaction] = None,
    ):
        self.bot: Bot = bot
        self._initialize_view_data(
            user,
            initial_category,
            initial_params,
            leaderboard_data,
            original_interaction,
        )
        super().__init__(
            bot=self.bot,
            user_id=user.id,
            current_page=self.current_page,
            total_pages=self.total_pages or 1,
            timeout=300,
        )

    def _initialize_view_data(
        self,
        user: Union[discord.User, discord.Member],
        initial_category: str,
        initial_params: dict[str, Any],
        leaderboard_data: dict[str, Any],
        original_interaction: Optional[discord.Interaction] = None,
    ) -> None:
        """初始化視圖數據"""
        self.current_user = user
        self.current_user_rank: Optional[dict[str, Any]] = leaderboard_data.get(
            "user_rank"
        )
        self.original_interaction = original_interaction

        self.current_category = initial_category
        self.current_leaderboard_type = initial_params.get("leaderboard_type")
        self.current_asset_symbol = initial_params.get("asset_symbol")
        self.current_game_type = initial_params.get("game_type")
        self.current_game_stat_type = initial_params.get("game_stat_type")
        self.current_pool_type = initial_params.get("pool_type")
        self.leaderboard_data = leaderboard_data or {}

        self.items_per_page = (
            1
            if self.current_leaderboard_type == "profile_likes"
            else leaderboard_service.items_per_page
        )
        if self.current_leaderboard_type == "profile_likes":
            total_users = self.leaderboard_data.get("total_users")
            self.total_pages = total_users if total_users is not None else 1
        else:
            self.total_pages = self.leaderboard_data.get("total_pages", 1)

        self.current_page = self.leaderboard_data.get("current_page", 1)

    async def update_view(self):
        """異步更新視圖的UI組件"""
        self.clear_items()

        # Re-add pagination buttons at the top (row 0)
        self.add_pagination_buttons(row=0)
        self._refresh_button_states()

        # Special layout for profile_likes
        if self.current_leaderboard_type == "profile_likes":
            self.add_item(SearchPlayerButton(self, row=1))
            self._update_like_button()
        else:
            # Standard layout for other leaderboards
            current_row = 1

            self.add_item(TypeSelect(self, row=current_row))
            current_row += 1

            if self.current_leaderboard_type and self._needs_asset_selection(
                self.current_leaderboard_type
            ):
                asset_select = AssetSelect(self, row=current_row)
                await asset_select.populate_options()
                self.add_item(asset_select)
                current_row += 1

            if self.current_leaderboard_type and self._needs_game_stat_selection(
                self.current_leaderboard_type
            ):
                self.current_game_type = self.current_leaderboard_type
                self.add_item(GameStatSelect(self, row=current_row))
                current_row += 1

            if self._needs_pool_selection():
                self.add_item(PoolSelect(self, row=current_row))
                current_row += 1

            self.add_item(SearchPlayerButton(self, row=current_row))

    async def _auto_select_first_asset(
        self, interaction: Optional[discord.Interaction], update_message: bool = True
    ):
        """自動選擇第一個可用的股票資產"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        assets = await leaderboard_service.get_all_market_assets()
        if assets:
            self.current_asset_symbol = assets[0]["symbol"]
            if update_message:
                await self._reload_leaderboard_data(
                    interaction, new_page=1, refresh_user_rank=True
                )
        # 如果沒有資產，此處不執行任何操作。
        # 錯誤將在 `_reload_leaderboard_data` -> `_fetch_data_for_reload`
        # -> `get_initial_leaderboard_data` 中被捕獲並引發 InitialLeaderboardError。

    async def _update_message(
        self,
        interaction: Optional[discord.Interaction],
        embed: Optional[discord.Embed] = None,
        content: Optional[str] = None,
        view: Optional[discord.ui.View] = None,
        attachments: Optional[list] = None,
    ) -> None:
        """統一的消息更新方法"""
        if attachments is None:
            attachments = []
        if view is None:
            view = self

        if interaction and not interaction.response.is_done():
            await interaction.response.edit_message(
                content=content, embed=embed, view=view, attachments=attachments
            )
        elif interaction and self.message:
            await interaction.followup.edit_message(
                self.message.id,
                content=content,
                embed=embed,
                view=view,
                attachments=attachments,
            )

    def _needs_asset_selection(self, leaderboard_type: str) -> bool:
        """判斷指定的排行榜類型是否需要資產選擇菜單"""
        return leaderboard_type in ["stock_holding"]

    def _needs_game_stat_selection(self, leaderboard_type: str) -> bool:
        """判斷指定的排行榜類型是否需要遊戲統計選擇菜單"""
        from gacha.core.game_registry import GameRegistry

        return GameRegistry.is_valid_game_type(leaderboard_type)

    def _needs_pool_selection(self) -> bool:
        """判斷當前排行榜是否需要卡池選擇菜單"""
        leaderboard_config = get_config("gacha_core_settings.leaderboard_config")
        if (
            not isinstance(leaderboard_config, dict)
            or not self.current_leaderboard_type
        ):
            return False
        config = leaderboard_config.get(self.current_leaderboard_type)
        return bool(config and getattr(config, "pool_filterable", False))

    def _get_leaderboard_types_for_category(
        self, category: str
    ) -> list[discord.SelectOption]:
        """獲取指定分類下的排行榜類型選項（優化版本）"""
        leaderboard_config = get_config("gacha_core_settings.leaderboard_config")
        if not isinstance(leaderboard_config, dict):
            return []
        return [
            discord.SelectOption(
                label=config_data.label or type_key,
                value=type_key,
                description=config_data.description[:100]
                if config_data.description
                else "",
                emoji=self._get_leaderboard_type_emoji(type_key),
            )
            for type_key, config_data in leaderboard_config.items()
            if config_data.category == category
        ]

    def _get_leaderboard_type_emoji(self, leaderboard_type: str) -> str:
        """獲取排行榜類型對應的 emoji"""
        from gacha.core.game_registry import GameRegistry

        if game_config := GameRegistry.get_game(leaderboard_type):
            return game_config.emoji
        return self._TYPE_EMOJI_MAP.get(leaderboard_type, "📊")

    async def _reload_leaderboard_data(
        self,
        interaction: Optional[discord.Interaction],
        new_page: int,
        refresh_user_rank: bool = False,
    ):
        """協調排行榜數據的重新加載和視圖的更新。"""
        start_time = time.time()

        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        params_prepared = await self._prepare_reload_params(interaction)
        if not params_prepared:
            return

        leaderboard_data_result = await self._fetch_data_for_reload(
            new_page, refresh_user_rank
        )
        self._update_state_after_reload(
            leaderboard_data_result, new_page, refresh_user_rank, start_time
        )
        await self._update_ui_after_reload(interaction)

    async def _prepare_reload_params(
        self, interaction: Optional[discord.Interaction]
    ) -> bool:
        """準備加載排行榜數據所需的參數，處理特殊情況。"""
        if (
            self.current_leaderboard_type == "stock_holding"
            and not self.current_asset_symbol
        ):
            await self._auto_select_first_asset(interaction, update_message=False)
            if not self.current_asset_symbol:
                return False
        elif self.current_leaderboard_type == "profile_likes":
            self.items_per_page = 1
        elif self.current_leaderboard_type and self._needs_game_stat_selection(
            self.current_leaderboard_type
        ):
            from gacha.core.game_registry import GameRegistry

            if (
                self.current_leaderboard_type
                and GameRegistry.is_valid_game_type(self.current_leaderboard_type)
                and not self.current_game_stat_type
            ):
                self.current_game_stat_type = "total_profit_loss"
        return True

    async def _fetch_data_for_reload(self, page: int, refresh_user_rank: bool) -> dict:
        """從服務層獲取排行榜數據。"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        return await leaderboard_service.get_leaderboard_with_user_rank(
            leaderboard_type=self.current_leaderboard_type,
            page=page,
            user_id=self.current_user.id if refresh_user_rank else None,
            asset_symbol=self.current_asset_symbol,
            game_type=self.current_game_type,
            game_stat_type=self.current_game_stat_type,
            pool_type=self.current_pool_type,
        )

    def _update_state_after_reload(
        self, data: dict, target_page: int, refresh_user_rank: bool, start_time: float
    ):
        """使用新數據更新視圖的內部狀態。"""
        self.leaderboard_data = data
        self.leaderboard_data["query_time"] = time.time() - start_time
        self.leaderboard_data["updated_at"] = datetime.now()

        if self.current_leaderboard_type == "profile_likes":
            self.total_pages = self.leaderboard_data.get("total_users") or 1
        else:
            self.total_pages = self.leaderboard_data.get("total_pages", 1)

        self.current_page = (
            min(target_page, self.total_pages)
            if self.total_pages and self.total_pages > 0
            else 1
        )

        if refresh_user_rank:
            self.current_user_rank = self.leaderboard_data.get("user_rank")

    async def _update_ui_after_reload(self, interaction: Optional[discord.Interaction]):
        """更新 Discord 上的 UI 元件。"""
        await self.update_view()
        embed = await self.get_current_page_embed()
        await self._update_message(interaction, embed=embed, attachments=[])

    async def get_current_page_embed(self) -> discord.Embed:
        """異步獲取當前頁面的 embed"""
        if self.current_leaderboard_type == "profile_likes":
            return await self._get_profile_likes_embed()
        elif self.current_leaderboard_type:
            items_per_page = leaderboard_service.items_per_page
            builder = LeaderboardEmbedBuilder(
                leaderboard_data=self.leaderboard_data,
                leaderboard_type=self.current_leaderboard_type,
                items_per_page=items_per_page,
                asset_symbol=self.current_asset_symbol,
                current_user_rank=self.current_user_rank,
                pool_type=self.current_pool_type,
            )
            return builder.build_embed()
        return discord.Embed(title="錯誤", description="未知的排行榜類型。")

    async def _get_profile_likes_embed(self) -> discord.Embed:
        """獲取個人檔案讚數排行榜的嵌入消息"""
        results = self.leaderboard_data.get("results", [])
        if not results:
            return discord.Embed(
                title="❤️ 個人檔案讚數排行榜",
                description="暫無數據或頁碼超出範圍。",
                color=discord.Color.light_gray(),
            )

        player_data = results[0]
        user_id = player_data.get("user_id")
        user_name = player_data.get("user_name", "未知用戶")
        like_count = player_data.get("like_count", 0)
        rank = self.current_page

        embed = self._create_base_profile_embed(user_name, like_count, rank)

        if not user_id:
            embed.add_field(name="錯誤", value="無法獲取玩家ID。", inline=False)
            return embed

        # 根據規範，移除 try...except，讓錯誤向上冒泡
        cdn_url = await profile_service.get_profile_image_url(
            bot=self.bot,
            user_id=user_id,
            user_name=user_name,
        )
        if cdn_url:
            embed.set_image(url=cdn_url)
        else:
            embed.add_field(name="錯誤", value="無法載入個人檔案圖片。", inline=False)

        return embed

    def _create_base_profile_embed(
        self, user_name: str, like_count: int, rank: int
    ) -> discord.Embed:
        """創建個人檔案讚數排行榜的基本嵌入消息"""
        embed = discord.Embed(
            title="❤️ 個人檔案讚數排行榜", color=discord.Color.from_rgb(255, 0, 255)
        )
        medal_emoji = {1: "🥇", 2: "🥈", 3: "🥉"}.get(rank, f"#{rank}")
        embed.add_field(
            name="排名", value=f"{medal_emoji} **{user_name}**", inline=True
        )
        embed.add_field(name="讚數", value=f"❤️ **{like_count}** 讚", inline=True)
        embed.add_field(
            name="頁碼", value=f"{self.current_page}/{self.total_pages}", inline=True
        )
        return embed

    def _update_like_button(self):
        """更新按讚按鈕"""
        for item in self.children:
            if isinstance(item, ProfileLikeButton):
                self.remove_item(item)

        if self.current_leaderboard_type == "profile_likes":
            results = self.leaderboard_data.get("results", [])
            if results:
                player_data = results[0]
                user_id = player_data.get("user_id")
                if user_id:
                    like_button = ProfileLikeButton(
                        owner_id=user_id,
                        row=1,
                        on_like_success_callback=self._on_like_success,
                    )
                    self.add_item(like_button)

    async def _on_like_success(self, interaction: discord.Interaction):
        """按讚成功後的回調函數"""
        await self._reload_leaderboard_data(interaction, new_page=self.current_page)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        await interaction.response.defer()
        await self._reload_leaderboard_data(interaction, new_page=page)
        if self.current_leaderboard_type == "profile_likes":
            asyncio.create_task(self._preload_profile_images())

    async def _preload_profile_images(self, preload_count: int = 3):
        """在背景中一個接一個地預載接下來幾頁的個人檔案圖片，以避免資源競爭。"""
        current_rank = self.current_page
        total_users = self.leaderboard_data.get("total_users", 0)

        for i in range(1, preload_count + 1):
            next_rank = current_rank + i
            if next_rank > total_users:
                break

            asyncio.create_task(self._check_and_schedule_preload_for_rank(next_rank))

    async def _check_and_schedule_preload_for_rank(self, rank: int):
        """獲取指定排名的玩家資料，並在需要時調度圖片生成。"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        player_data = await leaderboard_service.get_leaderboard_with_user_rank(
            leaderboard_type="profile_likes",
            page=rank,
            user_id=None,
            asset_symbol=None,
            game_type=None,
            game_stat_type=None,
            pool_type=None,
        )

        if not player_data or not player_data.get("results"):
            return

        player_info = player_data["results"][0]
        user_id = player_info.get("user_id")
        user_name = player_info.get("user_name")

        if not (user_id and user_name):
            return

        if not await profile_service.get_cached_profile_image_url(user_id):
            logger.info(
                f"Preloading profile image for Rank #{rank}, User ID: {user_id}"
            )
            await profile_service.get_profile_image_url(self.bot, user_id, user_name)
        else:
            logger.debug(
                f"Image already cached for Rank #{rank}, User ID: {user_id}. Skipping preload."
            )
