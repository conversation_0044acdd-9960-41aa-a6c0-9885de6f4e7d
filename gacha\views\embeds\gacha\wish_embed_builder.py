"""
Gacha系統許願Embed構建器
"""

from typing import Any, Dict, List, Optional, Union

import discord

from config.app_config import get_config, get_pool_type_emojis
from gacha.constants import RarityLevel
from gacha.services import wish_service
from gacha.views import utils as view_utils
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder


class WishEmbedBuilder(BaseEmbedBuilder):
    """構建許願列表嵌入消息的類"""

    def __init__(
        self,
        user: Union[discord.User, discord.Member],
        wishes: List[Dict[str, Any]],
        wish_slots: int,
        wish_power_level: int,
        used_slots: int,
        nickname: Optional[str] = None,
    ):
        """初始化許願Embed構建器

        參數:
            user: Discord用戶對象
            wishes: 許願列表，每項包含card_id、slot_index和card_info
            wish_slots: 已解鎖槽位數量
            wish_power_level: 許願力度等級
            used_slots: 已使用槽位數量
            nickname: 用戶暱稱，如果有的話優先使用
        """
        wish_data = {
            "user": user,
            "wishes": wishes,
            "wish_slots": wish_slots,
            "wish_power_level": wish_power_level,
            "used_slots": used_slots,
            "nickname": nickname,
        }
        super().__init__(data=wish_data)
        self.user = user
        self.wishes = wishes
        self.wish_slots = wish_slots
        self.wish_power_level = wish_power_level
        self.used_slots = used_slots
        self.nickname = nickname

    def build_embed(self) -> discord.Embed:
        """創建許願列表的Embed顯示

        返回:
            格式化後的Discord Embed對象
        """
        display_name = self.nickname or self.user.display_name
        multiplier = wish_service.get_wish_chance_multiplier(self.wish_power_level)
        next_level_cost = wish_service.calculate_next_power_cost(self.wish_power_level)
        next_slot_cost = wish_service.calculate_next_slot_cost(self.wish_slots)
        embed = self._create_base_embed(
            title="✨ 許願系統 ✨",
            description=f"**{display_name}** 的許願列表\n\n**`📋 槽位狀態`**：{self.used_slots:,}/{self.wish_slots:,} 格\n**`💪 力度等級`**：Lv.{self.wish_power_level:,} ({multiplier}倍權重)\n\n下一級力度費用：{self._format_cost(next_level_cost)} 油幣\n下一槽位費用：{self._format_cost(next_slot_cost)} 油幣\n",
            color=discord.Color(16753920),
        )
        embed.set_author(
            name=f"{display_name} 的許願列表", icon_url=self.user.display_avatar.url
        )
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif?ex=68129be9&is=68114a69&hm=343815c13076c3d7a1f4b63fc5a7c698335910b67a6aa970c8db324b347962b9&"
        )
        if not self.wishes:
            embed.add_field(
                name="📭 許願列表為空",
                value="使用 **添加許願** 按鈕或參數添加卡片到許願列表\n當你抽到許願中的卡片時，將獲得 ✨ 標記",
                inline=False,
            )
        else:
            wish_items = []
            sorted_wishes = sorted(self.wishes, key=lambda w: w["slot_index"])
            for wish in sorted_wishes:
                card_info = wish["card_info"]
                rarity = card_info.get("rarity")
                rarity_int = int(rarity) if rarity is not None else 0
                rarity_level_enum = None
                if rarity_int > 0:
                    try:
                        rarity_level_enum = RarityLevel(rarity_int)
                    except ValueError:
                        pass
                rarity_icon = view_utils.get_rarity_display_code(rarity_level_enum)
                pool_type = card_info.get("pool_type", "")
                pool_prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
                pool_prefix = (
                    pool_prefixes.get(pool_type, "")
                    if isinstance(pool_prefixes, dict)
                    else ""
                )
                pool_emoji = get_pool_type_emojis().get(pool_type, "")
                slot_index = wish["slot_index"]
                card_id = card_info["card_id"]
                card_name = card_info["name"]
                series = card_info["series"]
                wish_item = f"`[槽位 {slot_index + 1}]` {rarity_icon} {pool_emoji}{pool_prefix}**{card_name}** *(#{card_id})*\n┗ *{series}*"
                wish_items.append(wish_item)
            embed.add_field(
                name=f"📋 許願卡片列表 ({self.used_slots}/{self.wish_slots})",
                value="\n".join(wish_items) if wish_items else "無",
                inline=False,
            )
        embed.add_field(
            name="ℹ️ 許願系統說明",
            value=f"• 當你抽卡時，許願卡片有更高機率被抽中\n• 當前力度提供 **{multiplier}倍** 抽中權重\n• 使用按鈕或 `/wish [卡片ID] [操作]` 管理許願\n",
            inline=False,
        )
        embed.set_footer(
            text="許願系統 • 提高特定卡片抽取機率 • 抽中許願卡時將有特殊標記"
        )
        return embed

    def _get_multiplier_text(self, power_level: int) -> str:
        """根據力度等級獲取權重倍數文本 (私有方法)

        參數:
            power_level: 許願力度等級

        返回:
            str: 權重倍數文本
        """
        multiplier = wish_service.get_wish_chance_multiplier(power_level)
        return f"{int(multiplier)}x"

    def _format_cost(self, cost: int) -> str:
        """將成本格式化為易讀形式 (私有方法)

        參數:
            cost: 成本數值

        返回:
            str: 格式化後的成本文本
        """
        if cost < 0:
            return "已達上限"
        if cost >= 1000000:
            return f"{cost / 1000000:.1f}M"
        elif cost >= 1000:
            return f"{cost / 1000:.1f}K"
        else:
            return f"{cost:,}"
