{"BERSERKER_TRADE": {"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD_BASE", "value": 0.5}, {"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD_BASE", "value": -0.3}]}, "DUELIST_FOCUS": {"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.20"}, {"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.20"}, {"stat_name": "accuracy", "modification_type": "FLAT_ADD", "value_formula": "0.15"}]}, "MOMENTUM_SHIFT": {"effect_type": "STAT_MODIFICATION", "modifications": [{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "(custom:caster_consecutive_hits >= 3 ? 0.3 : 0)"}, {"stat_name": "pdef", "modification_type": "PERCENTAGE_ADD", "value_formula": "(custom:caster_dodged_last_turn ? 0.4 : 0)"}, {"stat_name": "spd", "modification_type": "PERCENTAGE_ADD", "value_formula": "(custom:caster_combo_breaker_active ? 0.25 : 0)"}]}}