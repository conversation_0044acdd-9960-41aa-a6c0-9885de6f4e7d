"""
德州撲克1v1遊戲數據模型
包含所有遊戲相關的數據結構、枚舉和常量

德州撲克1v1行動順序規則（複雜但重要）：
1. 位置關係：
   - 莊家（Dealer Button）= 小盲注（Small Blind）
   - 非莊家 = 大盲注（Big Blind）

2. 行動順序：
   - 翻牌前（Preflop）：小盲注先行動（因為大盲注已經下過盲注）
   - 翻牌後（Flop/Turn/River）：小盲注先行動

3. 每手牌結束後：莊家位置交換
"""

import asyncio
import random
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

# 復用blackjack_cog.py中的撲克牌emoji常量
from gacha.cogs.blackjack_cog import CARD_EMOJIS

# 隱藏牌 emoji
HIDDEN_CARD_EMOJI = "<:hd:1367815959645130762>"


class PokerHandRank(Enum):
    """德州撲克手牌等級"""

    HIGH_CARD = 1
    PAIR = 2
    TWO_PAIR = 3
    THREE_OF_A_KIND = 4
    STRAIGHT = 5
    FLUSH = 6
    FULL_HOUSE = 7
    FOUR_OF_A_KIND = 8
    STRAIGHT_FLUSH = 9
    ROYAL_FLUSH = 10


class GameStage(Enum):
    """遊戲階段"""

    PREFLOP = "preflop"
    FLOP = "flop"
    TURN = "turn"
    RIVER = "river"
    SHOWDOWN = "showdown"


class PlayerAction(Enum):
    """玩家動作"""

    FOLD = "fold"
    CALL = "call"
    RAISE = "raise"
    CHECK = "check"
    ALL_IN = "all_in"


class StakeTier(Enum):
    """場次等級"""

    NEWBIE = "newbie"
    REGULAR = "regular"
    PRO = "pro"


@dataclass
class PokerCard:
    """撲克牌類"""

    suit: str  # 'spades', 'hearts', 'diamonds', 'clubs'
    value: str  # 'A', '2'-'10', 'J', 'Q', 'K'

    @property
    def numeric_value(self) -> int:
        """獲取牌的數值（用於比較）"""
        if self.value == "A":
            return 14
        elif self.value in ["J", "Q", "K"]:
            return {"J": 11, "Q": 12, "K": 13}[self.value]
        else:
            return int(self.value)

    def __str__(self) -> str:
        """返回牌的 Emoji 字符串表示"""
        return CARD_EMOJIS.get((self.suit, self.value), CARD_EMOJIS[("unknown", "?")])


@dataclass
class PokerDeck:
    """撲克牌組"""

    cards: List[PokerCard] = field(default_factory=list)

    def __post_init__(self):
        """初始化完整牌組"""
        if not self.cards:
            self.reset()

    def reset(self):
        """重置為完整牌組"""
        suits = ["spades", "hearts", "diamonds", "clubs"]
        values = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        self.cards = [PokerCard(suit, value) for suit in suits for value in values]
        self.shuffle()

    def shuffle(self):
        """洗牌"""
        random.shuffle(self.cards)

    def deal(self) -> PokerCard:
        """發一張牌"""
        if not self.cards:
            raise ValueError("牌組已空")
        return self.cards.pop()


@dataclass
class HandEvaluation:
    """手牌評估結果"""

    rank: PokerHandRank
    high_cards: List[int]  # 用於比較的關鍵牌值
    description: str

    def __lt__(self, other):
        """比較手牌大小"""
        if self.rank.value != other.rank.value:
            return self.rank.value < other.rank.value
        return self.high_cards < other.high_cards


# 場次配置 - 2025年6月更新：符合德州撲克標準的盲注和帶入配置
STAKE_CONFIGS = {
    StakeTier.NEWBIE: {
        "name": "新手場",
        "small_blind": 10,
        "big_blind": 20,
        "min_buyin": 1000,
        "max_buyin": 5000,
        "rake_cap": 100,
    },
    StakeTier.REGULAR: {
        "name": "普通場",
        "small_blind": 100,
        "big_blind": 200,
        "min_buyin": 10000,
        "max_buyin": 50000,
        "rake_cap": 1000,
    },
    StakeTier.PRO: {
        "name": "高手場",
        "small_blind": 1000,
        "big_blind": 2000,
        "min_buyin": 100000,
        "max_buyin": 500000,
        "rake_cap": 10000,
    },
}

RAKE_PERCENTAGE = 0.05  # 5%抽水


@dataclass
class PokerPlayer:
    """德州撲克玩家"""

    user_id: int
    username: str
    chips: int
    hole_cards: List[PokerCard] = field(default_factory=list)
    current_bet: int = 0
    total_bet_this_hand: int = 0
    is_all_in: bool = False
    has_folded: bool = False
    last_action: Optional[PlayerAction] = None
    is_small_blind: bool = False
    is_big_blind: bool = False

    def can_act(self) -> bool:
        """檢查玩家是否可以行動"""
        return not self.has_folded and not self.is_all_in

    def reset_for_new_hand(self):
        """為新手牌重置玩家狀態"""
        self.hole_cards = []
        self.current_bet = 0
        self.total_bet_this_hand = 0
        self.is_all_in = False
        self.has_folded = False
        self.last_action = None


@dataclass
class MatchmakingEntry:
    """匹配隊列條目"""

    user_id: int
    username: str
    stake_tier: StakeTier
    buyin_amount: int
    timestamp: float
    interaction: Optional[Any] = None  # 保存 Discord Interaction 對象
    waiting_message: Optional[Any] = None  # 保存等待中的消息對象
    matchmaking_view: Optional[Any] = None  # 保存 MatchmakingView 對象，用於停止超時


@dataclass
class PokerGameState:
    """德州撲克遊戲狀態"""

    game_id: str
    player1: PokerPlayer
    player2: PokerPlayer
    deck: PokerDeck
    community_cards: List[PokerCard] = field(default_factory=list)
    pot: int = 0
    current_stage: GameStage = GameStage.PREFLOP
    current_player_index: int = 0  # 0 = player1, 1 = player2
    current_bet: int = 0  # 當前輪最高下注
    stake_tier: StakeTier = StakeTier.NEWBIE
    effective_stack: int = 0
    hand_number: int = 1
    dealer_button: int = 0  # 0 = player1是莊家, 1 = player2是莊家
    last_aggressor: Optional[int] = None  # 最後加注的玩家
    flop_reached: bool = False  # 是否到達翻牌圈（用於抽水計算）
    game_over: bool = False
    winner_id: Optional[int] = None
    win_reason: str = ""  # 最後一手牌的結束方式（用於統計）
    game_end_reason: str = ""  # 整個遊戲的結束原因
    final_pot: int = 0
    # 保存玩家原始帶入金額，用於繼續匹配
    player1_original_buyin: int = 0
    player2_original_buyin: int = 0
    rake_amount: int = 0
    created_at: float = field(default_factory=time.time)

    # 統計記錄標記
    _hand_stats_pending: bool = False  # 是否有待記錄的手牌統計

    # 結算狀態標記
    _settlement_in_progress: bool = False  # 是否正在進行結算
    _settlement_completed: bool = False  # 是否已完成結算

    # 遊戲消息對象（用於更新遊戲畫面）
    player1_message: Optional[Any] = None
    player2_message: Optional[Any] = None

    def get_current_player(self) -> PokerPlayer:
        """獲取當前行動玩家"""
        return self.player1 if self.current_player_index == 0 else self.player2

    def get_opponent(self, player_index: int) -> PokerPlayer:
        """獲取對手"""
        return self.player2 if player_index == 0 else self.player1

    def switch_current_player(self):
        """切換當前玩家"""
        self.current_player_index = 1 - self.current_player_index

    def get_small_blind_player(self) -> PokerPlayer:
        """獲取小盲注玩家"""
        # 🔧 修復BUG：在1v1德州撲克中，莊家是小盲注
        return self.player1 if self.dealer_button == 0 else self.player2

    def get_big_blind_player(self) -> PokerPlayer:
        """獲取大盲注玩家"""
        # 🔧 修復BUG：在1v1德州撲克中，非莊家是大盲注
        return self.player2 if self.dealer_button == 0 else self.player1

    def get_action_order_info(self) -> str:
        """獲取當前行動順序信息（用於調試）"""
        current_player = self.get_current_player()
        small_blind = self.get_small_blind_player()
        big_blind = self.get_big_blind_player()

        return (
            f"階段: {self.current_stage.value} | "
            f"莊家: Player{self.dealer_button + 1} | "
            f"小盲注: {small_blind.username} | "
            f"大盲注: {big_blind.username} | "
            f"當前行動: {current_player.username}"
        )

    def debug_positions(self) -> Dict[str, Any]:
        """調試位置信息"""
        return {
            "dealer_button": self.dealer_button,
            "current_player_index": self.current_player_index,
            "current_stage": self.current_stage.value,
            "small_blind_player": self.get_small_blind_player().username,
            "big_blind_player": self.get_big_blind_player().username,
            "current_player": self.get_current_player().username,
        }

    def calculate_effective_stack(self):
        """計算有效籌碼"""
        self.effective_stack = min(self.player1.chips, self.player2.chips)

    def advance_stage(self):
        """推進遊戲階段"""
        if self.current_stage == GameStage.PREFLOP:
            self.current_stage = GameStage.FLOP
            self.flop_reached = True
            # 發3張翻牌
            for _ in range(3):
                self.community_cards.append(self.deck.deal())
        elif self.current_stage == GameStage.FLOP:
            self.current_stage = GameStage.TURN
            # 發1張轉牌
            self.community_cards.append(self.deck.deal())
        elif self.current_stage == GameStage.TURN:
            self.current_stage = GameStage.RIVER
            # 發1張河牌
            self.community_cards.append(self.deck.deal())
        elif self.current_stage == GameStage.RIVER:
            self.current_stage = GameStage.SHOWDOWN

    def reset_betting_round(self):
        """重置下注輪"""
        from utils.logger import logger

        self.player1.current_bet = 0
        self.player2.current_bet = 0
        self.current_bet = 0
        self.last_aggressor = None

        # 重置本輪的行動記錄，但保留全押方的行動狀態
        # 🔧 修復BUG：一方全押後，不應該重置全押方的行動記錄
        if not self.player1.is_all_in:
            self.player1.last_action = None
        if not self.player2.is_all_in:
            self.player2.last_action = None

        # ✅ 德州撲克1v1規則：翻牌後小盲注先行動（在1v1中，莊家是小盲注）
        # 注意：翻牌前的行動順序在 start_new_hand() 中設置
        if self.current_stage != GameStage.PREFLOP:
            # 🔧 修復BUG：如果有玩家全押，需要特殊處理行動順序
            if self.player1.is_all_in and not self.player2.is_all_in:
                # 玩家1全押，玩家2先行動
                self.current_player_index = 1
                logger.debug(
                    f"遊戲 {self.game_id}: 玩家1全押，設置玩家2（索引1）先行動"
                )
            elif self.player2.is_all_in and not self.player1.is_all_in:
                # 玩家2全押，玩家1先行動
                self.current_player_index = 0
                logger.debug(
                    f"遊戲 {self.game_id}: 玩家2全押，設置玩家1（索引0）先行動"
                )
            elif self.player1.is_all_in and self.player2.is_all_in:
                # 雙方都全押，不需要行動（這種情況應該不會到這裡）
                logger.warning("遊戲 %s: 雙方都全押但仍在重置下注輪", self.game_id)
                self.current_player_index = self.dealer_button
            else:
                # 正常情況：按照小盲注先行動的規則
                self.current_player_index = self.dealer_button
                logger.debug(
                    f"遊戲 {self.game_id}: 正常情況，設置小盲注（dealer_button={self.dealer_button}）先行動"
                )

    def is_betting_round_complete(self) -> bool:
        """檢查下注輪是否完成"""
        from utils.logger import logger

        # 如果有玩家棄牌，下注輪結束
        if self.player1.has_folded or self.player2.has_folded:
            logger.debug("遊戲 %s: 下注輪完成 - 有玩家棄牌", self.game_id)
            return True

        # 如果雙方都全押，下注輪結束
        if self.player1.is_all_in and self.player2.is_all_in:
            logger.debug("遊戲 %s: 下注輪完成 - 雙方都全押", self.game_id)
            return True

        # 🛑 BUG修復：移除有問題的單方全押檢查邏輯。
        # 原本的邏輯（只要非全押方行動過就結束）是錯誤的，會導致玩家在對手全押後可以過牌。
        # 現在我們讓它走下面更通用的邏輯。

        # ✅ 修正：新下注輪開始時的特殊情況
        # 如果雙方下注都是0且都沒有行動，這是新下注輪的開始，不算完成
        if (
            self.player1.current_bet == 0
            and self.player2.current_bet == 0
            and self.player1.last_action is None
            and self.player2.last_action is None
        ):
            logger.debug("遊戲 %s: 下注輪未完成 - 新下注輪開始", self.game_id)
            return False

        # 檢查是否有一方全押，且另一方已跟注（即另一方也是全押，或者籌碼更多且已行動）
        if (self.player1.is_all_in and self.player2.last_action is not None) or (
            self.player2.is_all_in and self.player1.last_action is not None
        ):
            # 如果一方全押，只要另一方行動過（call或fold），下注就結束了。
            # fold的情況在最上面已經處理。所以這裡只考慮call。
            # 並且，如果call了，下注金額應該相等（除非call方籌碼不夠）
            # 所以，下方的通用邏輯可以更好地處理這種情況。
            pass

        # 正常情況：雙方下注相等且都已行動
        # 這個邏輯現在可以正確處理全押後跟注的情況，因為跟注會使雙方下注額相等。
        result = (
            self.player1.current_bet == self.player2.current_bet
            and self.player1.last_action is not None
            and self.player2.last_action is not None
        )
        logger.debug(
            f"遊戲 {self.game_id}: 正常情況檢查 - "
            f"下注相等: {self.player1.current_bet == self.player2.current_bet}, "
            f"player1已行動: {self.player1.last_action is not None}, "
            f"player2已行動: {self.player2.last_action is not None}, "
            f"結果: {result}"
        )
        return result

    def collect_bets_to_pot(self):
        """收集下注到底池"""
        self.pot += self.player1.current_bet + self.player2.current_bet
        self.player1.total_bet_this_hand += self.player1.current_bet
        self.player2.total_bet_this_hand += self.player2.current_bet
        self.player1.current_bet = 0
        self.player2.current_bet = 0

    def start_new_hand(self):
        """開始新手牌"""
        self.hand_number += 1
        self.dealer_button = 1 - self.dealer_button  # 交換莊家位置

        # 重置玩家狀態
        self.player1.reset_for_new_hand()
        self.player2.reset_for_new_hand()

        # 重置遊戲狀態
        self.community_cards = []
        self.pot = 0
        self.current_stage = GameStage.PREFLOP
        self.current_bet = 0
        self.last_aggressor = None
        self.flop_reached = False

        # 清空上一手牌的結果
        self.winner_id = None
        self.win_reason = ""
        self.final_pot = 0
        self.rake_amount = 0
        self._hand_stats_pending = False  # 重置統計記錄標記
        # 注意：不重置 game_end_reason，保留整個遊戲的結束原因
        # 🔧 修復BUG：重賽時需要重置遊戲結束標誌
        # 注意：這個方法只在重賽時被調用，所以可以安全重置 game_over
        # 如果是正常的手牌間隔，game_over 應該保持 False

        # 重新洗牌
        self.deck.reset()

        # 發手牌
        for _ in range(2):
            self.player1.hole_cards.append(self.deck.deal())
            self.player2.hole_cards.append(self.deck.deal())

        # 設置盲注
        self._post_blinds()

        # ✅ 修正：翻牌前小盲注先行動（在1v1中，莊家是小盲注）
        # 德州撲克1v1規則：翻牌前和翻牌後都是小盲注先行動
        self.current_player_index = self.dealer_button

    def _post_blinds(self):
        """下盲注"""
        config = STAKE_CONFIGS[self.stake_tier]
        small_blind = (
            int(config["small_blind"])
            if isinstance(config["small_blind"], str)
            else config["small_blind"]
        )
        big_blind = (
            int(config["big_blind"])
            if isinstance(config["big_blind"], str)
            else config["big_blind"]
        )

        small_blind_player = self.get_small_blind_player()
        big_blind_player = self.get_big_blind_player()

        # 下小盲注
        small_blind_amount = min(small_blind, small_blind_player.chips)
        small_blind_player.chips -= small_blind_amount
        small_blind_player.current_bet = (
            int(small_blind_amount)
            if isinstance(small_blind_amount, str)
            else small_blind_amount
        )
        small_blind_player.is_small_blind = True
        # 🔧 修復BUG：盲注不算作行動，保持None讓玩家可以正常行動
        # small_blind_player.last_action = None  # 盲注不算行動
        if small_blind_amount == small_blind_player.chips:
            small_blind_player.is_all_in = True

        # 下大盲注
        big_blind_amount = min(big_blind, big_blind_player.chips)
        big_blind_player.chips -= big_blind_amount
        big_blind_player.current_bet = (
            int(big_blind_amount)
            if isinstance(big_blind_amount, str)
            else big_blind_amount
        )
        big_blind_player.is_big_blind = True
        # 🔧 修復BUG：盲注不算作行動，保持None讓玩家可以正常行動
        # big_blind_player.last_action = None  # 盲注不算行動
        if big_blind_amount == big_blind_player.chips:
            big_blind_player.is_all_in = True

        self.current_bet = (
            int(big_blind_amount)
            if isinstance(big_blind_amount, str)
            else big_blind_amount
        )


# ==================== 命令對象 (Commands) ====================
# 這些是純數據載體，用於描述玩家操作和系統事件的意圖


@dataclass
class PlayerActionCommand:
    """玩家動作命令"""

    player_index: int  # 0 或 1
    action: PlayerAction
    amount: int = 0  # 用於加注金額
    timestamp: float = field(default_factory=time.time)
    interaction: Optional[Any] = None  # 用於回覆操作結果，設為 Any 避免循環導入
    future: Optional[asyncio.Future] = None  # 用於傳遞操作結果或異常


@dataclass
class LeaveGameCommand:
    """離開遊戲命令"""

    player_index: int  # 0 或 1
    timestamp: float = field(default_factory=time.time)
    interaction: Optional[Any] = None  # 用於回覆操作結果，設為 Any 避免循環導入
    future: Optional[asyncio.Future] = None  # 用於傳遞操作結果或異常


@dataclass
class TimeoutEvent:
    """超時事件"""

    timestamp: float = field(default_factory=time.time)
