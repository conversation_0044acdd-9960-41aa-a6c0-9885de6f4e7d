from decimal import Decimal
from typing import Any, Dict, List, Optional

import discord
from discord import app_commands
from discord.ext import commands

from gacha.constants import NewsFilterType, NewsTypeFilterEnum
from gacha.models.market_models import StockLifecycleStatus

# 服務與資料模型導入
from gacha.services import news_service, stock_trading_service
from gacha.services.news_service import NewsFilterOptions, NewsItemData, NewsPageData
from utils.base_view import BaseView
from utils.logger import logger

# ==================== Embed Builder ====================


class NewsEmbedBuilder:
    """新聞嵌入構建器"""

    DEFAULT_NO_DATA_TEXT = "N/A"
    ST_ERROR_EMOJI = "<a:Error:1371096622053724292>"
    TIME_EMOJI = "🗓️"

    def __init__(
        self,
        news_data: Optional[NewsItemData],
        current_item_index: int,
        total_items: int,
    ):
        self.data = news_data
        self.current_item_index = current_item_index
        self.total_items = total_items

    def build(self) -> discord.Embed:
        if not self.data:
            return self._build_empty_embed()

        return self._build_news_embed()

    def _build_empty_embed(self) -> discord.Embed:
        """構建空新聞嵌入"""
        embed = discord.Embed(
            title="📰 市場快訊",
            description="沒有找到符合條件的新聞。",
            color=discord.Color.orange(),
        )
        embed.set_footer(text="第 0/0 條")
        return embed

    def _build_news_embed(self) -> discord.Embed:
        """構建新聞嵌入"""
        news_item = self.data

        # This should not happen as build() checks for None, but add safety check
        if news_item is None:
            return self._build_empty_embed()

        # 獲取顏色配置
        from config.app_config import get_news_type_colors_int

        colors = get_news_type_colors_int()
        # Handle None news_type
        news_type_key = news_item.news_type or "default_news_color"
        embed_color = discord.Color(
            colors.get(
                news_type_key,
                colors.get("default_news_color", discord.Color.dark_grey().value),
            )
        )

        # 構建標題（包含ST狀態）
        title = f"📰 {news_item.headline}"
        if (
            news_item.asset_symbol
            and news_item.asset_lifecycle_status == StockLifecycleStatus.ST
        ):
            title += f" ({news_item.asset_symbol} {self.ST_ERROR_EMOJI})"

        embed = discord.Embed(
            title=title, description=news_item.content, color=embed_color
        )

        # 添加字段
        self._add_embed_fields(embed, news_item)

        # 設置頁腳
        page_info = (
            f"第 {self.current_item_index + 1}/{self.total_items} 條"
            if self.total_items > 0
            else "無符合條件的新聞"
        )
        embed.set_footer(text=page_info)

        return embed

    def _add_embed_fields(self, embed: discord.Embed, news_item: NewsItemData) -> None:
        """添加嵌入字段"""
        # 發布者
        author_display = news_item.character_name or news_item.source or "未知來源"
        embed.add_field(name="發布者", value=author_display, inline=True)

        # 新聞類型
        from config.app_config import get_ui_settings

        news_type_names = get_ui_settings().news_type_readable_names
        # Handle None news_type
        news_type_key = news_item.news_type or "未分類"
        news_type_display = news_type_names.get(news_type_key, news_type_key)
        embed.add_field(name="新聞類型", value=news_type_display, inline=True)

        # 發布時間
        published_at_str = (
            discord.utils.format_dt(news_item.published_at, style="F")
            if news_item.published_at
            else self.DEFAULT_NO_DATA_TEXT
        )
        embed.add_field(
            name=f"{self.TIME_EMOJI} 發布時間", value=published_at_str, inline=False
        )


# ==================== Base News View ====================


class BaseNewsView(BaseView):
    """新聞視圖基類"""

    def __init__(
        self,
        initiating_interaction: discord.Interaction,
        bot_instance: commands.Bot,
        message: Optional[discord.Message] = None,
        timeout: Optional[float] = 180.0,
    ):
        super().__init__(
            bot=bot_instance, user_id=initiating_interaction.user.id, timeout=timeout
        )
        self.initiating_interaction = initiating_interaction
        self.message: Optional[discord.Message] = message
        self.current_page: int = 1

    async def on_timeout(self):
        """超時處理"""
        logger.info("%s for user %s timed out.", self.__class__.__name__, self.user_id)
        self.stop()

    async def _get_data_for_display(self, **kwargs) -> Any:
        raise NotImplementedError("Subclasses must implement _get_data_for_display")

    def _build_embed(self, data: Any) -> discord.Embed:  # noqa: ARG002
        raise NotImplementedError("Subclasses must implement _build_embed")

    def _build_components(self, data: Any) -> List[discord.ui.Item]:  # noqa: ARG002
        raise NotImplementedError("Subclasses must implement _build_components")

    async def update_display(
        self, interaction_for_update: discord.Interaction, **kwargs
    ):
        if not self.message:
            logger.error("%s: No message to update.", self.__class__.__name__)
            return

        data = await self._get_data_for_display(**kwargs)
        if data is None:
            logger.warning(
                "%s: Failed to get data for display.", self.__class__.__name__
            )
            return

        new_embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)

        await interaction_for_update.followup.edit_message(
            self.message.id, embed=new_embed, view=self
        )

    async def send_initial_message(self, ephemeral: bool = False, **kwargs):
        """發送初始消息"""
        data = await self._get_data_for_display(**kwargs)
        if data is None:
            await self.initiating_interaction.followup.send(
                "無法加載初始數據。", ephemeral=True
            )
            return

        embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)

        self.message = await self.initiating_interaction.followup.send(
            embed=embed, view=self, ephemeral=ephemeral, wait=True
        )


# ==================== Single News View ====================


class SingleNewsView(BaseNewsView):
    """單條新聞視圖"""

    def __init__(
        self,
        initiating_interaction: discord.Interaction,
        bot_instance: commands.Bot,
        message: Optional[discord.Message] = None,
        initial_stock_symbol: Optional[str] = None,
    ):
        super().__init__(initiating_interaction, bot_instance, message, timeout=300.0)
        self.current_filter_scope: NewsFilterType = NewsFilterType.ALL
        self.current_news_type_filter: NewsTypeFilterEnum = NewsTypeFilterEnum.ALL
        self.current_item_index: int = 0
        self.total_items: int = 0
        self.current_news_item_data: Optional[NewsItemData] = None
        self.current_stock_symbol: Optional[str] = initial_stock_symbol

    async def _get_data_for_display(
        self, item_index: Optional[int] = None, **kwargs
    ) -> Optional[NewsPageData]:  # noqa: ARG002
        idx_to_fetch = item_index if item_index is not None else self.current_item_index
        filter_options = NewsFilterOptions(
            filter_type=self.current_filter_scope,
            news_type_filter=self.current_news_type_filter,
            specific_stock_symbol=self.current_stock_symbol,
        )

        page_data = await news_service.get_news_page_data(
            user_id=self.user_id,
            filters=filter_options,
            current_page_idx=idx_to_fetch,
            items_per_page=1,
        )

        if page_data:
            self.total_items = page_data.total_items
            if page_data.news_items:
                self.current_news_item_data = page_data.news_items[0]
                self.current_item_index = idx_to_fetch
            else:
                self.current_news_item_data = None
                if self.total_items == 0:
                    self.current_item_index = 0
        else:
            self.current_news_item_data = None
            self.total_items = 0
            self.current_item_index = 0

        return page_data

    def _build_embed(self, data: NewsPageData) -> discord.Embed:  # noqa: ARG002
        return NewsEmbedBuilder(
            news_data=self.current_news_item_data,
            current_item_index=(
                self.current_item_index if self.current_news_item_data else 0
            ),
            total_items=self.total_items,
        ).build()

    def _build_components(self, data: NewsPageData) -> List[discord.ui.Item]:  # noqa: ARG002
        self._add_scope_filter_select()
        self._add_news_type_filter_select()
        self._add_navigation_buttons()
        return self.children

    def _add_scope_filter_select(self) -> None:
        """添加範圍過濾選單"""
        scope_options = [
            discord.SelectOption(
                label="所有新聞 (範圍)",
                value=NewsFilterType.ALL.value,
                default=self.current_filter_scope == NewsFilterType.ALL,
            ),
            discord.SelectOption(
                label="我的持倉相關",
                value=NewsFilterType.PORTFOLIO.value,
                default=self.current_filter_scope == NewsFilterType.PORTFOLIO,
            ),
            discord.SelectOption(
                label="我的做空相關",
                value=NewsFilterType.SHORT_POSITIONS.value,
                default=self.current_filter_scope == NewsFilterType.SHORT_POSITIONS,
            ),
            discord.SelectOption(
                label="全局新聞",
                value=NewsFilterType.GLOBAL.value,
                default=self.current_filter_scope == NewsFilterType.GLOBAL,
            ),
        ]
        scope_select = discord.ui.Select(
            placeholder="篩選新聞範圍...",
            options=scope_options,
            custom_id="news_filter_scope_select",
            row=0,
        )
        scope_select.callback = self.on_filter_scope_select
        self.add_item(scope_select)

    def _add_news_type_filter_select(self) -> None:
        """添加新聞類型過濾選單"""
        from config.app_config import get_ui_settings

        news_type_names = get_ui_settings().news_type_readable_names

        news_type_opts = [
            discord.SelectOption(
                label="所有類型",
                value=NewsTypeFilterEnum.ALL.value,
                default=self.current_news_type_filter == NewsTypeFilterEnum.ALL,
            )
        ]

        # 添加其他新聞類型選項
        for nt in NewsTypeFilterEnum:
            if nt != NewsTypeFilterEnum.ALL:
                news_type_opts.append(
                    discord.SelectOption(
                        label=news_type_names.get(nt.value, nt.value),
                        value=nt.value,
                        default=self.current_news_type_filter == nt,
                    )
                )

        news_type_select = discord.ui.Select(
            placeholder="篩選新聞種類...",
            options=news_type_opts,
            custom_id="news_type_filter_select",
            row=1,
        )
        news_type_select.callback = self.on_news_type_filter_select
        self.add_item(news_type_select)

    def _add_navigation_buttons(self) -> None:
        """添加導航按鈕"""
        # 上一條按鈕
        prev_button = discord.ui.Button(
            label="⬅️ 上一條",
            style=discord.ButtonStyle.secondary,
            custom_id="news_prev",
            disabled=self.current_item_index <= 0,
            row=2,
        )
        prev_button.callback = self.go_to_previous_item
        self.add_item(prev_button)

        # 頁面指示器
        page_label = (
            f"{self.current_item_index + 1}/{self.total_items}"
            if self.total_items > 0
            else "0/0"
        )
        page_indicator = discord.ui.Button(
            label=page_label,
            style=discord.ButtonStyle.grey,
            disabled=True,
            custom_id="news_page_indicator",
            row=2,
        )
        self.add_item(page_indicator)

        # 下一條按鈕
        next_button = discord.ui.Button(
            label="下一條 ➡️",
            style=discord.ButtonStyle.secondary,
            custom_id="news_next",
            disabled=self.current_item_index >= self.total_items - 1
            or self.total_items == 0,
            row=2,
        )
        next_button.callback = self.go_to_next_item
        self.add_item(next_button)

    async def _handle_filter_change(self, interaction: discord.Interaction):
        """處理過濾器變更"""
        self.current_item_index = 0
        await self.update_display(interaction_for_update=interaction)

    async def on_filter_scope_select(self, interaction: discord.Interaction):
        """範圍過濾器選擇回調"""
        await interaction.response.defer()
        if (
            interaction.data
            and "values" in interaction.data
            and interaction.data["values"]
        ):
            self.current_filter_scope = NewsFilterType(interaction.data["values"][0])
            await self._handle_filter_change(interaction)

    async def on_news_type_filter_select(self, interaction: discord.Interaction):
        """新聞類型過濾器選擇回調"""
        await interaction.response.defer()
        if (
            interaction.data
            and "values" in interaction.data
            and interaction.data["values"]
        ):
            self.current_news_type_filter = NewsTypeFilterEnum(
                interaction.data["values"][0]
            )
            await self._handle_filter_change(interaction)

    async def go_to_previous_item(self, interaction: discord.Interaction):
        """前往上一條新聞"""
        await interaction.response.defer()
        if self.current_item_index > 0:
            new_idx = self.current_item_index - 1
            await self.update_display(
                interaction_for_update=interaction, item_index=new_idx
            )

    async def go_to_next_item(self, interaction: discord.Interaction):
        """前往下一條新聞"""
        await interaction.response.defer()
        if self.current_item_index < self.total_items - 1:
            new_idx = self.current_item_index + 1
            await self.update_display(
                interaction_for_update=interaction, item_index=new_idx
            )


# ==================== News Cog ====================


class NewsCog(commands.Cog, name="新聞"):
    """市場新聞 Cog - 獨立處理所有新聞相關功能"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # 服務現在是無狀態導入，不再需要初始化
        logger.info("NewsCog initialized.")

    async def stock_symbol_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:  # noqa: ARG002
        """股票代碼自動完成"""
        if not stock_trading_service:
            logger.error(
                "stock_symbol_autocomplete: StockTradingService not available."
            )
            return []

        asset_details = await stock_trading_service.search_asset_symbols_with_details(
            current
        )
        return self._format_autocomplete_choices(asset_details)

    def _format_autocomplete_choices(
        self, asset_details: List[Dict[str, Any]]
    ) -> List[app_commands.Choice[str]]:
        """格式化自動完成選項"""
        choices = []
        for asset_info in asset_details:
            symbol = asset_info["asset_symbol"]
            name = asset_info["asset_name"]
            price = Decimal(str(asset_info["current_price"]))
            status = asset_info["lifecycle_status"]

            # 添加狀態標記
            status_suffix = " (ST)" if status == "st" else ""

            # 格式化顯示名稱
            display_name = f"{symbol} - {name} ({price:.2f} 油幣){status_suffix}"

            # 限制長度
            if len(display_name) > 100:
                display_name = display_name[:97] + "..."

            choices.append(app_commands.Choice(name=display_name, value=symbol))

        return choices

    @app_commands.command(name="news", description="查看最新的市場新聞。")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(symbol="要查詢新聞的股票代碼 (可選，留空則顯示所有新聞)")
    @app_commands.autocomplete(symbol=stock_symbol_autocomplete)
    async def news(
        self, interaction: discord.Interaction, symbol: Optional[str] = None
    ):
        """查看市場新聞指令"""
        await interaction.response.defer(ephemeral=False)

        # 檢查服務可用性
        if not news_service:
            logger.error("News service is not available.")
            raise RuntimeError("新聞服務目前不可用，請稍後再試")

        view = SingleNewsView(
            initiating_interaction=interaction,
            bot_instance=self.bot,
            initial_stock_symbol=symbol,
        )
        await view.send_initial_message(ephemeral=False)


async def setup(bot: commands.Bot):
    await bot.add_cog(NewsCog(bot))
    logger.info("NewsCog added successfully.")
