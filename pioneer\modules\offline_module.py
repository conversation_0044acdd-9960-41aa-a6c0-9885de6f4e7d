"""
Pioneer System Offline Module
負責處理玩家的離線產出計算。
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, cast

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.exceptions import Config<PERSON>otFoundError, PioneerCalculationError
from pioneer.models.pioneer_models import Facility
from utils.logger import logger


async def process_user_offline_production(user_id: int) -> Dict[str, Any]:
    """
    處理用戶的所有離線產出。
    """
    try:
        facilities = await repositories.get_user_facilities(user_id, active_only=True)

        total_results: Dict[str, Any] = {
            "facilities_processed": 0,
            "total_cycles": 0,
            "items_produced": {},
            "earnings_generated": 0,
            "errors": [],
        }

        for facility in facilities:
            try:
                result = await _process_facility_offline(facility)
                if not result:
                    continue

                total_results["facilities_processed"] += 1
                total_results["total_cycles"] += result.get("cycles_executed", 0)
                total_results["earnings_generated"] += result.get("earnings", 0)

                items_produced = total_results["items_produced"]
                for item_id, quantity in result.get("items_produced", {}).items():
                    items_produced[item_id] = items_produced.get(item_id, 0) + quantity

            except Exception as e:
                logger.error("處理設施 %s 離線產出失敗: %s", facility.id, e)
                errors_list = cast(List[Dict[str, Any]], total_results["errors"])
                errors_list.append({"facility_id": facility.id, "error": str(e)})

        return total_results

    except Exception as e:
        logger.error("處理用戶 %s 離線產出失敗: %s", user_id, e)
        raise PioneerCalculationError(f"離線產出計算失敗: {str(e)}") from e


async def _process_facility_offline(facility: Facility) -> Dict[str, Any]:
    """處理單個設施的離線產出"""
    try:
        if game_data is None:
            raise PioneerCalculationError("GameDataLoader 尚未初始化")

        facility_config = game_data.get_facility_config(facility.facility_type)
        if not facility_config:
            raise ConfigNotFoundError("facility", facility.facility_type)

        # 1. 處理自動輸入
        await _handle_auto_input(facility, facility_config)

        current_time = datetime.now()
        if not facility.last_production_time:
            facility.last_production_time = current_time
        time_passed = current_time - facility.last_production_time

        if time_passed.total_seconds() < facility_config.process_time:
            return {"cycles_executed": 0, "items_produced": {}, "earnings": 0}

        economy_config = game_data.economy
        if not economy_config:
            raise ConfigNotFoundError("economy", "economy")

        offline_limits = economy_config.offline_limits
        max_offline_hours = int(offline_limits.get("max_offline_hours", 168))
        offline_efficiency = float(offline_limits.get("offline_efficiency", 0.8))
        max_cycles = int(offline_limits.get("max_offline_cycles", 1000))

        max_offline_seconds = max_offline_hours * 3600
        effective_time = min(time_passed.total_seconds(), max_offline_seconds)

        time_cycles = int(effective_time / facility_config.process_time)
        time_cycles = min(time_cycles, max_cycles)

        if facility_config.process_type == "generate":
            actual_cycles = await _calculate_generate_cycles(
                facility, facility_config, time_cycles
            )
        elif facility_config.process_type == "transform":
            actual_cycles = await _calculate_craft_cycles(
                facility, facility_config, time_cycles
            )
        elif facility_config.process_type == "sell":
            actual_cycles = await _calculate_sell_cycles(
                facility, facility_config, time_cycles
            )
        elif facility_config.process_type == "research":
            actual_cycles = await _calculate_research_cycles(
                facility, facility_config, time_cycles
            )
        else:
            actual_cycles = 0

        final_cycles = int(actual_cycles * offline_efficiency)

        result = await _execute_production_cycles(
            facility, facility_config, final_cycles
        )

        new_production_time = facility.last_production_time + timedelta(
            seconds=final_cycles * facility_config.process_time
        )
        await repositories.update_facility_production_time(
            facility.id, new_production_time
        )

        # 3. 處理自動輸出
        await _handle_auto_output(facility, facility_config)

        return result

    except Exception as e:
        logger.error("設施 %s 離線計算失敗: %s", facility.id, e)
        raise PioneerCalculationError(f"設施離線計算失敗: {str(e)}") from e


async def _calculate_generate_cycles(
    facility: Facility, facility_config, time_cycles: int
) -> int:
    output_slots = await repositories.get_facility_slots(
        facility.id, slot_type="output"
    )
    if not output_slots:
        return 0

    min_cycles = time_cycles

    for output_item in facility_config.outputs:
        item_id = output_item["item_id"]
        quantity_per_cycle = output_item["quantity"]

        slot = next(
            (s for s in output_slots if s.item_id == item_id or s.item_id is None), None
        )
        if slot:
            available_space = slot.max_capacity - slot.quantity
            max_cycles_for_this_item = available_space // quantity_per_cycle
            min_cycles = min(min_cycles, max_cycles_for_this_item)

    return max(0, min_cycles)


async def _calculate_craft_cycles(
    facility: Facility, facility_config, time_cycles: int
) -> int:
    input_slots = await repositories.get_facility_slots(facility.id, slot_type="input")
    output_slots = await repositories.get_facility_slots(
        facility.id, slot_type="output"
    )

    min_cycles = time_cycles

    for input_item in facility_config.inputs:
        item_id = input_item["item_id"]
        quantity_per_cycle = input_item["quantity"]

        slot = next((s for s in input_slots if s.item_id == item_id), None)
        if slot:
            max_cycles_for_this_input = slot.quantity // quantity_per_cycle
            min_cycles = min(min_cycles, max_cycles_for_this_input)
        else:
            return 0

    for output_item in facility_config.outputs:
        item_id = output_item["item_id"]
        quantity_per_cycle = output_item["quantity"]

        slot = next(
            (s for s in output_slots if s.item_id == item_id or s.item_id is None), None
        )
        if slot:
            available_space = slot.max_capacity - slot.quantity
            max_cycles_for_this_output = available_space // quantity_per_cycle
            min_cycles = min(min_cycles, max_cycles_for_this_output)

    return max(0, min_cycles)


async def _calculate_sell_cycles(
    facility: Facility, facility_config, time_cycles: int
) -> int:
    return time_cycles


async def _calculate_research_cycles(
    facility: Facility, facility_config, time_cycles: int
) -> int:
    """計算研究設施的產出輪次，研究點無上限，直接返回時間輪次"""
    return time_cycles


async def _execute_production_cycles(
    facility: Facility, facility_config, cycles: int
) -> Dict[str, Any]:
    if cycles <= 0:
        return {"cycles_executed": 0, "items_produced": {}, "earnings": 0}

    result = {"cycles_executed": cycles, "items_produced": {}, "earnings": 0}

    try:
        if facility_config.process_type == "generate":
            for output_item in facility_config.outputs:
                item_id = output_item["item_id"]
                total_quantity = output_item["quantity"] * cycles
                await repositories.add_facility_slot_item(
                    facility.id, "output", 0, item_id, total_quantity
                )
                result["items_produced"][item_id] = total_quantity

        elif facility_config.process_type == "transform":
            for input_item in facility_config.inputs:
                item_id = input_item["item_id"]
                total_quantity = input_item["quantity"] * cycles
                await repositories.consume_facility_slot_item(
                    facility.id, "input", item_id, total_quantity
                )

            for output_item in facility_config.outputs:
                item_id = output_item["item_id"]
                total_quantity = output_item["quantity"] * cycles
                await repositories.add_facility_slot_item(
                    facility.id, "output", 0, item_id, total_quantity
                )
                result["items_produced"][item_id] = total_quantity

        elif facility_config.process_type == "sell":
            shelf_slots = await repositories.get_facility_slots(
                facility.id, slot_type="shelf"
            )

            for slot in shelf_slots:
                if slot.item_id and slot.quantity > 0:
                    if not game_data:
                        continue
                    item_config = game_data.get_item_config(slot.item_id)
                    if item_config:
                        sell_quantity = min(slot.quantity, cycles)
                        item_price = item_config.base_sell_price
                        earnings = sell_quantity * item_price

                        await repositories.consume_facility_slot_item(
                            facility.id, "shelf", slot.item_id, sell_quantity
                        )
                        await repositories.add_pending_oil_earnings(
                            facility.user_id, earnings
                        )
                        result["earnings"] += earnings

        elif facility_config.process_type == "research":
            for output_item in facility_config.outputs:
                if output_item.get("item_id") == "research_points":
                    total_points = output_item["quantity"] * cycles
                    if total_points > 0:
                        # 將研究點作為一種特殊物品添加到倉庫
                        await repositories.add_warehouse_item(
                            facility.user_id, "research_points", total_points
                        )
                        result["items_produced"]["research_points"] = total_points

        return result

    except Exception as e:
        logger.error("執行生產輪次失敗: %s", e)
        raise PioneerCalculationError(f"執行生產輪次失敗: {str(e)}") from e


async def _handle_auto_input(facility: Facility, facility_config) -> None:
    """處理設施的自動輸入"""
    if not facility.upgrades:
        return
    auto_input_upgrades = [
        up for up in facility.upgrades if up.startswith("auto_input")
    ]
    if not auto_input_upgrades:
        return

    # 重新獲取槽位信息，因為它們可能在其他操作中被改變
    input_slots = await repositories.get_facility_slots(facility.id, slot_type="input")
    if not input_slots:
        return

    for slot in input_slots:
        input_conf = next(
            (
                conf
                for conf in facility_config.inputs
                if conf.get("slot_type") == slot.slot_type
            ),
            None,
        )
        if not input_conf:
            continue

        required_upgrade = f"auto_input_{slot.slot_type}"
        if (
            required_upgrade not in facility.upgrades
            and "auto_input" not in facility.upgrades
        ):
            continue

        needed_quantity = slot.max_capacity - slot.quantity
        if needed_quantity <= 0:
            continue

        item_id_to_pull = input_conf.get("item_id")
        if not item_id_to_pull:
            continue

        warehouse_stock = await repositories.get_item_quantity(
            facility.user_id, item_id_to_pull
        )
        transfer_quantity = min(needed_quantity, warehouse_stock)

        if transfer_quantity > 0:
            try:
                success = await repositories.move_item_to_facility_slot(
                    facility.user_id,
                    item_id_to_pull,
                    transfer_quantity,
                    facility.id,
                    slot.slot_type,
                    slot.slot_index,
                )
                if success:
                    logger.info(
                        f"設施 {facility.id}: 自動輸入 {transfer_quantity} 個 {item_id_to_pull} 到槽位 {slot.slot_index}"
                    )
            except Exception as e:
                logger.error("自動輸入失敗 for facility %s: %s", facility.id, e)


async def _handle_auto_output(facility: Facility, facility_config) -> None:
    """處理設施的自動輸出"""
    if not facility.upgrades or "auto_output" not in facility.upgrades:
        return

    # 重新獲取槽位信息
    output_slots = await repositories.get_facility_slots(
        facility.id, slot_type="output"
    )
    if not output_slots:
        return

    for slot in output_slots:
        if slot.quantity > 0 and slot.item_id:
            try:
                success = await repositories.transfer_item_from_slot_to_warehouse(
                    facility.user_id, facility.id, slot.slot_type, slot.slot_index
                )
                if success:
                    logger.info(
                        f"設施 {facility.id}: 自動輸出 {slot.quantity} 個 {slot.item_id} 從槽位 {slot.slot_index}"
                    )
            except Exception as e:
                logger.error("自動輸出失敗 for facility %s: %s", facility.id, e)
