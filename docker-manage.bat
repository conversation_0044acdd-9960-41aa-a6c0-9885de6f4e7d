@echo off
chcp 65001 > nul

:menu
cls
echo ======================================
echo     DICKPK Discord Bot - Docker 管理
echo      (唯一需要的 Docker 管理工具)
echo ======================================
echo.
echo 請選擇操作:
echo 1. 🚀 啟動正式環境
echo 2. 🧪 啟動測試環境
echo 3. ⏹️  停止正式環境
echo 4. ⏹️  停止測試環境
echo 5. 🔄 重新建置正式環境
echo 6. 🔄 重新建置測試環境
echo 7. 📊 查看狀態
echo 8. 📋 查看日誌
echo 9. 🧹 清理資源
echo 10. 🔍 環境檢查
echo 0. ❌ 退出
echo.
set /p choice=請輸入選項 (0-10): 

if "%choice%"=="1" goto start_prod
if "%choice%"=="2" goto start_test
if "%choice%"=="3" goto stop_prod
if "%choice%"=="4" goto stop_test
if "%choice%"=="5" goto rebuild_prod
if "%choice%"=="6" goto rebuild_test
if "%choice%"=="7" goto status
if "%choice%"=="8" goto logs
if "%choice%"=="9" goto cleanup
if "%choice%"=="10" goto check_env
if "%choice%"=="0" goto exit
echo ❌ 無效選項，請重新選擇
pause
goto menu

:start_prod
cls
echo ======================================
echo         啟動正式環境
echo ======================================
echo.
call :check_docker
if %ERRORLEVEL% neq 0 goto menu

echo 🔨 正在建置 Docker 鏡像...
docker-compose -f docker-compose.prod.yml build
if %ERRORLEVEL% neq 0 (
    echo ❌ 鏡像建置失敗！
    call :show_error_help
    goto menu
)

echo 🚀 正在啟動正式環境容器...
docker-compose -f docker-compose.prod.yml up -d
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 容器啟動失敗！正在進行錯誤診斷...
    echo.
    echo === 容器狀態檢查 ===
    docker-compose -f docker-compose.prod.yml ps
    echo.
    echo === 錯誤日誌 (最近50行) ===
    docker-compose -f docker-compose.prod.yml logs --tail=50 bot-prod
    echo.
    echo === 診斷完成 ===
    echo 💡 提示：您可以選擇選單中的「3. 正式環境錯誤診斷」獲得更詳細的錯誤信息
    echo.
    pause
    goto menu
)

echo ✅ 正式環境啟動成功！
echo.
call :show_status_prod
echo.
echo 選擇日誌查看方式:
choice /c 123 /m "1=查看最近日誌 2=即時跟隨 3=返回選單"
if %ERRORLEVEL%==1 (
    echo.
    echo === 正式環境最近100行日誌 ===
    docker-compose -f docker-compose.prod.yml logs --tail=100 bot-prod
    echo.
    echo === 日誌結束，按任意鍵返回選單 ===
    pause
) else if %ERRORLEVEL%==2 (
    echo.
    echo === 正式環境即時日誌 (按 Ctrl+C 返回選單) ===
    echo 🚨 警告：如果容器出錯退出，視窗可能會關閉！
    pause
    docker-compose -f docker-compose.prod.yml logs -f bot-prod
)
goto menu

:start_test
cls
echo ======================================
echo         啟動測試環境
echo ======================================
echo.
call :check_docker
if %ERRORLEVEL% neq 0 goto menu

echo 🔨 正在建置 Docker 鏡像...
docker-compose -f docker-compose.test.yml build
if %ERRORLEVEL% neq 0 (
    echo ❌ 鏡像建置失敗！
    call :show_error_help
    goto menu
)

echo 🚀 正在啟動測試環境容器...
docker-compose -f docker-compose.test.yml up -d
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 容器啟動失敗！正在進行錯誤診斷...
    echo.
    echo === 容器狀態檢查 ===
    docker-compose -f docker-compose.test.yml ps
    echo.
    echo === 錯誤日誌 (最近50行) ===
    docker-compose -f docker-compose.test.yml logs --tail=50 bot-test
    echo.
    echo === 診斷完成 ===
    echo 💡 提示：您可以選擇選單中的「4. 測試環境錯誤診斷」獲得更詳細的錯誤信息
    echo.
    pause
    goto menu
)

echo ✅ 測試環境啟動成功！
echo.
call :show_status_test
echo.
echo 選擇日誌查看方式:
choice /c 123 /m "1=查看最近日誌 2=即時跟隨 3=返回選單"
if %ERRORLEVEL%==1 (
    echo.
    echo === 測試環境最近100行日誌 ===
    docker-compose -f docker-compose.test.yml logs --tail=100 bot-test
    echo.
    echo === 日誌結束，按任意鍵返回選單 ===
    pause
) else if %ERRORLEVEL%==2 (
    echo.
    echo === 測試環境即時日誌 (按 Ctrl+C 返回選單) ===
    echo 🚨 警告：如果容器出錯退出，視窗可能會關閉！
    pause
    docker-compose -f docker-compose.test.yml logs -f bot-test
)
goto menu

:stop_prod
echo 🛑 正在停止正式環境...
docker-compose -f docker-compose.prod.yml down
echo ✅ 正式環境已停止
goto wait_and_return

:stop_test
echo 🛑 正在停止測試環境...
docker-compose -f docker-compose.test.yml down
echo ✅ 測試環境已停止
goto wait_and_return

:rebuild_prod
echo 🔄 正在重新建置正式環境...
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
echo ✅ 正式環境重建完成
goto wait_and_return

:rebuild_test
echo 🔄 正在重新建置測試環境...
docker-compose -f docker-compose.test.yml down
docker-compose -f docker-compose.test.yml build --no-cache
docker-compose -f docker-compose.test.yml up -d
echo ✅ 測試環境重建完成
goto wait_and_return

:status
cls
echo ======================================
echo         容器狀態檢查
echo ======================================
echo.
echo 🏢 正式環境狀態:
call :show_status_prod
echo.
echo 🧪 測試環境狀態:
call :show_status_test
echo.
echo ======================================
echo 📖 狀態說明:
echo ✅ Up = 運行中
echo ❌ Exited = 已停止/錯誤
echo 💤 (無顯示) = 尚未啟動
pause
goto menu

:logs
cls
echo ======================================
echo         查看日誌
echo ======================================
echo.
echo 請選擇要查看的環境:
echo 1. 正式環境日誌
echo 2. 測試環境日誌
echo 3. 正式環境錯誤診斷
echo 4. 測試環境錯誤診斷
echo 0. 返回主選單
echo.
set /p log_choice=請輸入選項 (0-4): 
if "%log_choice%"=="1" (
    call :view_logs_prod
) else if "%log_choice%"=="2" (
    call :view_logs_test
) else if "%log_choice%"=="3" (
    call :diagnose_errors_prod
) else if "%log_choice%"=="4" (
    call :diagnose_errors_test
) else if "%log_choice%"=="0" (
    goto menu
) else (
    echo ❌ 無效選項
    pause
)
goto menu

:cleanup
echo 🧹 正在清理未使用的 Docker 資源...
docker system prune -f
echo ✅ 清理完成！
pause
goto menu

:check_env
cls
echo ======================================
echo         環境檢查
echo ======================================
echo.
call :check_docker_verbose
call :check_databases
call :check_files
echo.
echo ======================================
echo 🎯 如果所有項目都是 ✅，就可以正常使用 Docker！
pause
goto menu

:wait_and_return
echo.
pause
goto menu

:exit
echo 👋 再見！
exit /b 0

REM ========== 輔助函數 ==========

:check_docker
docker info >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Docker Desktop 未運行！請先啟動 Docker Desktop
    pause
    exit /b 1
)
exit /b 0

:check_docker_verbose
echo 🐳 檢查 Docker Desktop...
docker info >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Docker Desktop 未運行或無法連接！
    echo    請先啟動 Docker Desktop 再重試
) else (
    echo ✅ Docker Desktop 運行正常
)
exit /b 0

:check_databases
echo.
echo 🐘 檢查 PostgreSQL...
netstat -an | findstr :5432 >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ PostgreSQL 未運行 (端口 5432)
) else (
    echo ✅ PostgreSQL 運行正常 (端口 5432)
)

echo.
echo 🔴 檢查 Redis...
netstat -an | findstr :6379 >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Redis 未運行 (端口 6379)
) else (
    echo ✅ Redis 運行正常 (端口 6379)
)
exit /b 0

:check_files
echo.
echo 📁 檢查必要文件...
if exist "Dockerfile" (echo ✅ Dockerfile) else (echo ❌ Dockerfile 缺失)
if exist "docker-compose.prod.yml" (echo ✅ docker-compose.prod.yml) else (echo ❌ docker-compose.prod.yml 缺失)
if exist "docker-compose.test.yml" (echo ✅ docker-compose.test.yml) else (echo ❌ docker-compose.test.yml 缺失)
if exist "requirements.txt" (echo ✅ requirements.txt) else (echo ❌ requirements.txt 缺失)
exit /b 0

:show_status_prod
docker-compose -f docker-compose.prod.yml ps 2>nul
if %ERRORLEVEL% neq 0 echo 💤 無正式環境容器運行
exit /b 0

:show_status_test
docker-compose -f docker-compose.test.yml ps 2>nul
if %ERRORLEVEL% neq 0 echo 💤 無測試環境容器運行
exit /b 0

:show_error_help
echo.
echo 💡 常見建置失敗原因:
echo    1. 網路連接問題 (無法下載套件)
echo    2. Docker 資源不足 (記憶體/磁碟)
echo    3. requirements.txt 套件版本衝突
echo.
pause
exit /b 0

REM ========== 新增的錯誤處理和日誌查看函數 ==========

:view_logs_prod
cls
echo ======================================
echo         正式環境日誌查看
echo ======================================
echo.
echo 請選擇查看方式:
echo 1. 查看最近100行日誌 (推薦)
echo 2. 即時跟隨日誌 (可能會跳掉)
echo 3. 查看最近500行日誌
echo 0. 返回
echo.
set /p view_choice=請輸入選項 (0-3): 
if "%view_choice%"=="1" (
    echo.
    echo === 正式環境最近100行日誌 ===
    docker-compose -f docker-compose.prod.yml logs --tail=100 bot-prod
    echo.
    echo === 日誌結束 ===
    pause
) else if "%view_choice%"=="2" (
    echo.
    echo === 正式環境即時日誌 (按 Ctrl+C 中止) ===
    echo 🚨 注意：如果容器出錯，視窗可能會關閉
    pause
    docker-compose -f docker-compose.prod.yml logs -f bot-prod
) else if "%view_choice%"=="3" (
    echo.
    echo === 正式環境最近500行日誌 ===
    docker-compose -f docker-compose.prod.yml logs --tail=500 bot-prod
    echo.
    echo === 日誌結束 ===
    pause
) else if "%view_choice%"=="0" (
    exit /b 0
) else (
    echo ❌ 無效選項
    pause
)
exit /b 0

:view_logs_test
cls
echo ======================================
echo         測試環境日誌查看
echo ======================================
echo.
echo 請選擇查看方式:
echo 1. 查看最近100行日誌 (推薦)
echo 2. 即時跟隨日誌 (可能會跳掉)
echo 3. 查看最近500行日誌
echo 0. 返回
echo.
set /p view_choice=請輸入選項 (0-3): 
if "%view_choice%"=="1" (
    echo.
    echo === 測試環境最近100行日誌 ===
    docker-compose -f docker-compose.test.yml logs --tail=100 bot-test
    echo.
    echo === 日誌結束 ===
    pause
) else if "%view_choice%"=="2" (
    echo.
    echo === 測試環境即時日誌 (按 Ctrl+C 中止) ===
    echo 🚨 注意：如果容器出錯，視窗可能會關閉
    pause
    docker-compose -f docker-compose.test.yml logs -f bot-test
) else if "%view_choice%"=="3" (
    echo.
    echo === 測試環境最近500行日誌 ===
    docker-compose -f docker-compose.test.yml logs --tail=500 bot-test
    echo.
    echo === 日誌結束 ===
    pause
) else if "%view_choice%"=="0" (
    exit /b 0
) else (
    echo ❌ 無效選項
    pause
)
exit /b 0

:diagnose_errors_prod
cls
echo ======================================
echo         正式環境錯誤診斷
echo ======================================
echo.
echo 🔍 正在進行容器狀態檢查...
echo.
echo === 容器狀態 ===
docker-compose -f docker-compose.prod.yml ps
echo.
echo === 最新錯誤日誌 ===
docker-compose -f docker-compose.prod.yml logs --tail=50 bot-prod 2>&1 | findstr /i "error\|exception\|traceback\|failed\|無法\|錯誤\|失敗"
if %ERRORLEVEL% neq 0 (
    echo 🎉 未發現明顯錯誤，查看完整日誌：
    docker-compose -f docker-compose.prod.yml logs --tail=20 bot-prod
)
echo.
echo === 容器檢查 ===
docker inspect dickpk_bot_prod >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 容器 dickpk_bot_prod 不存在
) else (
    echo ✅ 容器存在，檢查退出狀態：
    docker inspect dickpk_bot_prod --format="{{.State.ExitCode}}" 2>nul
)
echo.
echo === 診斷完成 ===
pause
exit /b 0

:diagnose_errors_test
cls
echo ======================================
echo         測試環境錯誤診斷
echo ======================================
echo.
echo 🔍 正在進行容器狀態檢查...
echo.
echo === 容器狀態 ===
docker-compose -f docker-compose.test.yml ps
echo.
echo === 最新錯誤日誌 ===
docker-compose -f docker-compose.test.yml logs --tail=50 bot-test 2>&1 | findstr /i "error\|exception\|traceback\|failed\|無法\|錯誤\|失敗"
if %ERRORLEVEL% neq 0 (
    echo 🎉 未發現明顯錯誤，查看完整日誌：
    docker-compose -f docker-compose.test.yml logs --tail=20 bot-test
)
echo.
echo === 容器檢查 ===
docker inspect dickpk_bot_test >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 容器 dickpk_bot_test 不存在
) else (
    echo ✅ 容器存在，檢查退出狀態：
    docker inspect dickpk_bot_test --format="{{.State.ExitCode}}" 2>nul
)
echo.
echo === 診斷完成 ===
pause
exit /b 0 