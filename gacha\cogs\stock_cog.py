import dataclasses
import io
from dataclasses import dataclass, field
from decimal import Decimal
from typing import TYPE_CHECKING, Any, Awaitable, Callable, Dict, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot


from gacha.exceptions import BusinessError
from gacha.models.market_models import StockLifecycleStatus

# 1. 【新】直接導入需要的服務模組
# 導入股票後台服務
from gacha.services import (
    price_update_service,
    scheduled_task_orchestrator,
    stock_trading_service,
    user_service,
    validation_service,
)
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# 移除自定義異常導入，改用 Python 原生異常

# ==================== 數據模型類 ====================


@dataclass
class StockData:
    asset_id: int
    asset_symbol: str
    asset_name: str
    current_price: Decimal
    description: Optional[str] = None
    previous_price: Optional[Decimal] = None
    volume_24h: Optional[Decimal] = None
    lifecycle_status: Optional[StockLifecycleStatus] = None
    total_shares: Optional[int] = None
    base_volatility: Optional[Decimal] = None
    volatility_factor: Optional[Decimal] = None
    last_updated: Optional[Any] = None

    @staticmethod
    def from_record(record: Union[Dict[str, Any], Any]) -> "StockData":
        status_val = record.get("lifecycle_status")
        lifecycle_status_enum = None
        if status_val:
            try:
                lifecycle_status_enum = StockLifecycleStatus(status_val)
            except ValueError:
                logger.warning(
                    "Invalid lifecycle_status value '%s' for asset_id %s. Defaulting to None.",
                    status_val,
                    record.get("asset_id"),
                )
        return StockData(
            asset_id=record["asset_id"],
            asset_symbol=record["asset_symbol"],
            asset_name=record["asset_name"],
            current_price=Decimal(str(record["current_price"])),
            description=record.get("description"),
            previous_price=(
                Decimal(str(record["previous_price"]))
                if record.get("previous_price") is not None
                else None
            ),
            volume_24h=(
                Decimal(str(record.get("volume_24h")))
                if record.get("volume_24h") is not None
                else None
            ),
            lifecycle_status=lifecycle_status_enum,
            total_shares=record.get("total_shares"),
            base_volatility=(
                Decimal(str(record["base_volatility"]))
                if record.get("base_volatility") is not None
                else None
            ),
            volatility_factor=(
                Decimal(str(record["volatility_factor"]))
                if record.get("volatility_factor") is not None
                else None
            ),
            last_updated=record.get("last_updated"),
        )


@dataclass
class StockListPageData:
    stocks: List[StockData]
    current_page: int
    total_pages: int
    total_stocks: int


@dataclass
class AggregatedVolumeData:
    total_buy_volume: int
    total_sell_volume: int


@dataclass
class RecentTransactionData:
    user_nickname: str
    transaction_type: str
    quantity: int
    price_per_unit: Decimal
    timestamp: Any

    @staticmethod
    def from_record(record: Union[Dict[str, Any], Any]) -> "RecentTransactionData":
        return RecentTransactionData(
            user_nickname=record["user_nickname"],
            transaction_type=record["transaction_type"],
            quantity=record["quantity"],
            price_per_unit=Decimal(str(record["price_per_unit"])),
            timestamp=record["timestamp"],
        )


@dataclass
class StockDetailData:
    stock_data: StockData
    aggregated_volume_7d: Optional[AggregatedVolumeData] = None
    recent_transactions_5: List[RecentTransactionData] = field(default_factory=list)
    chart_image_bytes: Optional[bytes] = None
    user_oil_balance: Optional[Decimal] = None
    user_stock_quantity: Optional[int] = None
    user_short_quantity: Optional[int] = None


# ==================== Modal 類 ====================

MAX_STOCKS_PER_PAGE = 12


class BuyStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="購買數量",
        placeholder="請輸入要購買的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        stock_symbol: str,
        current_price: Decimal,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="買入股票")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("購買數量必須是正整數。")

        # 3. 【修改】直接使用導入的模組
        message = await stock_trading_service.buy_stock(
            user_id=interaction.user.id,
            asset_symbol=self.stock_symbol,
            quantity=quantity,
        )

        # 使用標準化的成功 Embed
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)


class SellStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="賣出數量",
        placeholder="請輸入要賣出的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        stock_symbol: str,
        current_price: Decimal,
        user_id: int,
        max_quantity: int,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="賣出股票")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.user_id = user_id
        self.max_quantity = max_quantity
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("賣出數量必須是正整數。")

        # 直接調用，不檢查返回值
        message = await stock_trading_service.sell_stock(
            user_id=self.user_id, asset_symbol=self.stock_symbol, quantity=quantity
        )

        # 使用標準化的成功 Embed
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)


class ShortStockModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="做空數量",
        placeholder="請輸入要做空的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        stock_symbol: str,
        current_price: Decimal,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="做空股票")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("做空數量必須是正整數。")

        # 調用做空服務
        message = await stock_trading_service.short_stock(
            user_id=interaction.user.id,
            asset_symbol=self.stock_symbol,
            quantity=quantity,
        )

        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)


class CoverShortModal(BaseModal):
    quantity_input = discord.ui.TextInput(
        label="回補數量",
        placeholder="請輸入要回補的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        stock_symbol: str,
        current_price: Decimal,
        user_id: int,
        max_short_quantity: int,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="回補做空")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.user_id = user_id
        self.max_short_quantity = max_short_quantity
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label=f"您做空 {self.stock_symbol}",
                default=f"{self.max_short_quantity} 股",
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        quantity_str = self.quantity_input.value
        quantity = int(quantity_str)
        if quantity <= 0:
            raise BusinessError("回補數量必須是正整數。")

        # 調用回補服務
        message = await stock_trading_service.cover_short(
            user_id=self.user_id, asset_symbol=self.stock_symbol, quantity=quantity
        )

        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)


class StockDetailEmbedBuilder:
    def __init__(
        self,
        stock_data: Optional[Dict[str, Any]],
        asset_symbol_or_id: Any,
        aggregated_volume_7d: Optional[Dict[str, int]],
        recent_transactions_5: Optional[List[Dict[str, Any]]],
        chart_image_bytes: Optional[bytes],
        user_oil_balance: Optional[Decimal] = None,
        user_stock_quantity: Optional[int] = None,
        user_short_quantity: Optional[int] = None,
    ):
        self.stock_data = stock_data
        self.asset_symbol_or_id = asset_symbol_or_id
        self.aggregated_volume_7d = aggregated_volume_7d
        self.recent_transactions_5 = recent_transactions_5
        self.chart_image_bytes = chart_image_bytes
        self.user_oil_balance = user_oil_balance
        self.user_stock_quantity = user_stock_quantity
        self.user_short_quantity = user_short_quantity

    def _add_price_and_volatility(self, embed: discord.Embed, s: Dict[str, Any]):
        price = Decimal(s["current_price"])
        embed.add_field(name="💰 當前價格", value=f"**{price:.2f}** 油幣", inline=True)
        embed.add_field(
            name="📊 基礎波動率", value=f"{s['base_volatility']:.3f}", inline=True
        )
        embed.add_field(
            name="📊 波動放大因子", value=f"{s['volatility_factor']:.2f}", inline=True
        )

    def _add_market_cap_info(self, embed: discord.Embed, s: Dict[str, Any]):
        total_shares_val = s.get("total_shares")
        if total_shares_val is None:
            embed.add_field(name="📊 總股本", value="未知", inline=True)
            return

        try:
            total_shares = int(total_shares_val)
            price = Decimal(s["current_price"])
            market_cap = price * total_shares
            size_category = "N/A"
            if total_shares < 15000:
                size_category = "微型股 (<1.5萬股)"
            elif 15000 <= total_shares < 40000:
                size_category = "小型股 (1.5萬-4萬股)"
            elif 40000 <= total_shares < 70000:
                size_category = "中型股 (4萬-7萬股)"
            elif 70000 <= total_shares <= 100000:
                size_category = "大型股 (7萬-10萬股)"
            else:
                size_category = "超大型股 (>10萬股)"

            embed.add_field(name="📊 總股本", value=f"{total_shares:,} 股", inline=True)
            embed.add_field(
                name="🏦 當前市值", value=f"{market_cap:,.0f} 油幣", inline=True
            )
            embed.add_field(name="🔖 規模分類", value=size_category, inline=True)
        except (ValueError, TypeError):
            embed.add_field(name="📊 總股本", value="數據錯誤", inline=True)

    def _add_user_holdings(self, embed: discord.Embed, s: Dict[str, Any]):
        if (
            self.user_oil_balance is None
            and self.user_stock_quantity is None
            and self.user_short_quantity is None
        ):
            return

        if self.user_oil_balance is not None:
            embed.add_field(
                name="👤 您的油幣餘額",
                value=f"{self.user_oil_balance:,.0f} 油幣",
                inline=True,
            )
        if self.user_stock_quantity is not None and self.user_stock_quantity > 0:
            embed.add_field(
                name=f"👤 您持有 {s['asset_symbol']}",
                value=f"{self.user_stock_quantity:,} 股",
                inline=True,
            )
        if self.user_short_quantity is not None and self.user_short_quantity > 0:
            embed.add_field(
                name=f"👤 您做空 {s['asset_symbol']}",
                value=f"{self.user_short_quantity:,} 股",
                inline=True,
            )

    def _add_volume_and_transactions(self, embed: discord.Embed):
        if self.aggregated_volume_7d:
            buy_vol = self.aggregated_volume_7d.get("total_buy_volume", 0)
            sell_vol = self.aggregated_volume_7d.get("total_sell_volume", 0)
            embed.add_field(
                name="📈 買賣量 (近7日)",
                value=f"🟢 買入: {buy_vol:,} 股\n🔴 賣出: {sell_vol:,} 股",
                inline=False,
            )

        if self.recent_transactions_5:
            trans_strs = []
            for t in self.recent_transactions_5:
                if t.get("transaction_type") == "FORCED_COVER":
                    continue
                type_map = {
                    "BUY": "🟢 買入",
                    "SELL": "🔴 賣出",
                    "SHORT": "📉 做空",
                    "COVER": "📈 回補",
                    "DELISTED_SETTLEMENT": "🏛️ 下市結算",
                }
                transaction_type = t.get("transaction_type")
                emoji_text = (
                    type_map.get(transaction_type, "⚪ 未知操作")
                    if transaction_type
                    else "⚪ 未知操作"
                )
                timestamp = t.get("timestamp")
                ts = (
                    discord.utils.format_dt(timestamp, "R") if timestamp else "未知時間"
                )
                quantity = t.get("quantity", "N/A")
                quantity_str = (
                    f"{quantity:,}"
                    if isinstance(quantity, (int, float))
                    else str(quantity)
                )
                trans_strs.append(
                    f"{emoji_text} `{t.get('user_nickname', '未知')}` `{quantity_str}`股 @ `{Decimal(t.get('price_per_unit', 0)):.2f}` ({ts})"
                )

            embed.add_field(
                name="⏱️ 近期成交 (最近5筆用戶交易)",
                value="\n".join(trans_strs) if trans_strs else "暫無近期用戶交易記錄。",
                inline=False,
            )

    def _finalize_embed(self, embed: discord.Embed, s: Dict[str, Any]):
        if self.chart_image_bytes:
            embed.set_image(url="attachment://price_chart_7d.png")
        else:
            embed.add_field(
                name="📉 價格趨勢 (近7日)", value="圖表生成失敗或無數據。", inline=False
            )

        if last_updated := s.get("last_updated"):
            from datetime import timezone

            if last_updated.tzinfo is None:
                last_updated = last_updated.replace(tzinfo=timezone.utc)
            embed.timestamp = last_updated
        embed.set_footer(text="數據更新時間")

    def build(self) -> discord.Embed:
        if not self.stock_data:
            return discord.Embed(
                title="錯誤",
                description=f"找不到股票 {self.asset_symbol_or_id} 的詳細信息。",
                color=discord.Color.red(),
            )

        s = self.stock_data
        embed = discord.Embed(
            title=f"{s['asset_symbol']} - {s['asset_name']}",
            description=s["description"] or "暫無公司介紹。",
            color=discord.Color.teal(),
        )

        self._add_price_and_volatility(embed, s)
        self._add_market_cap_info(embed, s)
        self._add_user_holdings(embed, s)
        self._add_volume_and_transactions(embed)
        self._finalize_embed(embed, s)

        return embed


class StockListEmbedBuilder:
    """股票列表 Embed 建構器"""

    def __init__(self, page_data: StockListPageData):
        self.page_data = page_data

    def build(self) -> discord.Embed:
        title = f"💹 股市行情 (第 {self.page_data.current_page}/{self.page_data.total_pages} 頁)"
        embed = discord.Embed(title=title, color=discord.Color.blue())

        if not self.page_data.stocks:
            embed.description = "目前市場上沒有股票。"
            footer_text = f"總共 {self.page_data.total_stocks} 支股票，{self.page_data.total_pages} 頁"
            embed.set_footer(text=footer_text)
            return embed

        for stock in self.page_data.stocks:
            price = stock.current_price
            price_str = f"{price:.2f}"

            # 計算趨勢 - 參考原本的實現
            percentage_str = "N/A"
            trend_emoji = "➖"
            if stock.previous_price and stock.previous_price > 0:
                change_percentage = (
                    (price - stock.previous_price) / stock.previous_price
                ) * 100
                percentage_str = f"{change_percentage:+.2f}%"
                if change_percentage > 0:
                    trend_emoji = "📈"
                elif change_percentage < 0:
                    trend_emoji = "📉"

            trend_display_str = f"`{percentage_str}`{trend_emoji}"

            # 成交量 - 參考原本的實現
            volume_24h_str = "N/A"
            if stock.volume_24h is not None:
                volume_24h_str = f"{stock.volume_24h:,.0f}"

            # 狀態標記
            status_suffix = ""
            emoji_prefix = "<a:my2:1370641774950875146>"  # 默認動畫表情
            if stock.lifecycle_status == StockLifecycleStatus.ST:
                emoji_prefix = "<a:Error:1371096622053724292>"
                status_suffix = ""  # ST 股票不顯示額外後綴
            elif stock.lifecycle_status == StockLifecycleStatus.DELISTED:
                status_suffix = " (已退市)"

            display_name = f"{stock.asset_name[:20]}{status_suffix}"
            field_name = "\u200b"
            from config.app_config import get_oil_emoji

            field_value = f"{emoji_prefix} **{stock.asset_symbol}** - {display_name}\n<:ReplyCont:1383146319425699931> 價格 : `{price_str}` {get_oil_emoji()}\n<:ReplyCont:1383146319425699931> 趨勢 : {trend_display_str}\n<:Reply:1357534074830590143> 成交量 : `{volume_24h_str}` 股"
            embed.add_field(name=field_name, value=field_value, inline=True)

        footer_text = f"總共 {self.page_data.total_stocks} 支股票，{self.page_data.total_pages} 頁｜可以使用/news指令 查看最近新聞來確認股票時事。\n註：所有成交量數據均為過去24小時統計。"
        embed.set_footer(text=footer_text)
        return embed


class BaseStockView(BaseView):
    """股票視圖基類"""

    def __init__(
        self,
        cog: "StockCog",
        initiating_interaction: discord.Interaction,
        message: Optional[discord.Message] = None,
        timeout: float = 300.0,
    ):
        super().__init__(
            bot=cog.bot, user_id=initiating_interaction.user.id, timeout=timeout
        )
        self.cog = cog
        self.initiating_interaction = initiating_interaction
        self.message = message

    async def send_initial_message(self, ephemeral: bool = False, **kwargs):
        """發送初始消息 - 參考原本的實現"""
        data = await self._get_data_for_display(**kwargs)
        if data is None:
            await self.initiating_interaction.followup.send(
                "無法加載初始數據。", ephemeral=True
            )
            return

        embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)

        attachments_to_send = []
        if hasattr(data, "chart_image_bytes") and data.chart_image_bytes:
            attachments_to_send.append(
                discord.File(
                    io.BytesIO(data.chart_image_bytes), filename="price_chart_7d.png"
                )
            )

        self.message = await self.initiating_interaction.followup.send(
            embed=embed,
            view=self,
            ephemeral=ephemeral,
            files=attachments_to_send,
            wait=True,
        )

    async def update_display(
        self, interaction_for_update: discord.Interaction, **kwargs
    ):
        """更新顯示 - 參考原本的實現"""
        if not self.message:
            logger.error("%s: No message to update.", self.__class__.__name__)
            return

        data = await self._get_data_for_display(**kwargs)
        if data is None:
            logger.warning(
                "%s: Failed to get data for display.", self.__class__.__name__
            )
            return

        new_embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)

        attachments_to_send = []
        if hasattr(data, "chart_image_bytes") and data.chart_image_bytes:
            attachments_to_send.append(
                discord.File(
                    io.BytesIO(data.chart_image_bytes),
                    filename="price_chart_7d.png",
                )
            )

        await interaction_for_update.followup.edit_message(
            self.message.id,
            embed=new_embed,
            view=self,
            attachments=attachments_to_send or [],
        )

    async def _get_data_for_display(self, **kwargs):
        """獲取顯示數據 - 子類需要實現"""
        raise NotImplementedError

    def _build_embed(self, data) -> discord.Embed:
        """構建 embed - 子類需要實現"""
        raise NotImplementedError

    def _build_components(self, data):
        """構建組件 - 子類需要實現"""
        pass


class StockListView(BaseStockView):
    """股票列表視圖"""

    def __init__(
        self,
        cog: "StockCog",
        initiating_interaction: discord.Interaction,
        message: Optional[discord.Message] = None,
    ):
        super().__init__(cog, initiating_interaction, message, timeout=300.0)
        self.current_page = 1
        self.total_pages = 1

    async def _get_data_for_display(
        self, page: Optional[int] = None, **kwargs
    ) -> Optional[StockListPageData]:
        page_to_fetch = page if page is not None else self.current_page
        data = await self.cog.get_stock_list_page_data(
            page=page_to_fetch, per_page=MAX_STOCKS_PER_PAGE
        )
        if data:
            self.current_page = data.current_page
            self.total_pages = data.total_pages
        return data

    def _build_embed(self, data: StockListPageData) -> discord.Embed:
        """構建股票列表 embed"""
        return StockListEmbedBuilder(page_data=data).build()

    def _build_components(self, data: StockListPageData):
        """構建股票列表組件 - 參考原本實現"""
        # 分頁按鈕 - 與原本完全一致
        prev_button = discord.ui.Button(
            label="⬅️ 上一頁",
            style=discord.ButtonStyle.secondary,
            custom_id="stocklist_prev",
            disabled=self.current_page <= 1,
            row=0,
        )
        prev_button.callback = self.go_to_previous_page
        self.add_item(prev_button)

        page_indicator = discord.ui.Button(
            label=f"{self.current_page}/{self.total_pages}",
            style=discord.ButtonStyle.grey,
            disabled=True,
            row=0,
        )
        self.add_item(page_indicator)

        next_button = discord.ui.Button(
            label="下一頁 ➡️",
            style=discord.ButtonStyle.secondary,
            custom_id="stocklist_next",
            disabled=self.current_page >= self.total_pages,
            row=0,
        )
        next_button.callback = self.go_to_next_page
        self.add_item(next_button)

        # 教學按鈕 - 與原本完全一致
        tutorial_button = discord.ui.Button(
            label="📖 教學",
            style=discord.ButtonStyle.blurple,
            custom_id="stocklist_tutorial",
            row=0,
        )
        tutorial_button.callback = self.tutorial_callback
        self.add_item(tutorial_button)

        # 股票選擇下拉選單 - 參考原本的實現
        if data.stocks:
            options = []
            for stock in data.stocks:
                status_suffix = ""
                if stock.lifecycle_status == StockLifecycleStatus.ST:
                    status_suffix = " (ST)"
                elif stock.lifecycle_status == StockLifecycleStatus.DELISTED:
                    status_suffix = " (已退市)"

                label_text = f"{stock.asset_symbol} - {stock.asset_name}{status_suffix}"
                description_text = (stock.description or "暫無描述")[:100]

                options.append(
                    discord.SelectOption(
                        label=label_text[:100],
                        value=str(stock.asset_id),
                        description=description_text,
                    )
                )

            details_select = discord.ui.Select(
                placeholder="選擇股票查看詳細信息...",
                options=options,
                custom_id="stocklist_view_details_select",
                row=1,
            )
            details_select.callback = self.on_view_details_select
            self.add_item(details_select)

    async def go_to_page(self, interaction: discord.Interaction, page_num: int):
        await self.update_display(interaction_for_update=interaction, page=page_num)

    async def go_to_previous_page(self, interaction: discord.Interaction):
        # 雙重檢查：按鈕應該已經被禁用，但為了安全起見再次檢查
        if self.current_page <= 1:
            raise BusinessError("已經是第一頁了。")

        await interaction.response.defer()
        await self.go_to_page(interaction, self.current_page - 1)

    async def go_to_next_page(self, interaction: discord.Interaction):
        # 雙重檢查：按鈕應該已經被禁用，但為了安全起見再次檢查
        if self.current_page >= self.total_pages:
            raise BusinessError("已經是最後一頁了。")

        await interaction.response.defer()
        await self.go_to_page(interaction, self.current_page + 1)

    async def on_view_details_select(self, interaction: discord.Interaction):
        await interaction.response.defer()
        if not interaction.data or "values" not in interaction.data:
            raise BusinessError("無法獲取選擇的股票資訊。")
        selected_asset_id_str = interaction.data["values"][0]
        try:
            selected_asset_id = int(selected_asset_id_str)
        except ValueError as e:
            logger.error("Invalid asset_id from select: %s", selected_asset_id_str)
            raise BusinessError("選擇的股票ID無效。") from e

        detail_view = StockDetailView(
            cog=self.cog,
            initiating_interaction=self.initiating_interaction,
            message=self.message,
            asset_symbol_or_id=selected_asset_id,
            came_from_list_view=True,
        )
        await detail_view.update_display(
            interaction_for_update=interaction, asset_id_to_load=selected_asset_id
        )

    async def tutorial_callback(self, interaction: discord.Interaction):
        tutorial_message = "**股市機制教學**\n\n**📈 基本概念**\n- **股價**：由系統算法決定（隨機遊走+新聞影響），類似外匯或商品期貨交易。\n- **交易時間**：本市場為24/7模擬交易，但價格更新頻率依系統設定。\n- **重要**：玩家交易不會影響股價變動，這是玩家VS系統的投資遊戲。\n\n**💸 手續費計算方式**\n交易手續費的計算方式如下：\n1. **百分比費用**：交易總金額 × 1.5%\n2. **每股費用**：每股 0.05 油幣 × 交易股數\n3. **比較取高**：取上述「百分比費用」和「每股費用」中較高者\n4. **最低手續費**：如果第3步算出的費用低於 5 油幣，則實際手續費為 5 油幣\n   *(買入時，手續費會加入總成本；賣出時，手續費會從總收益中扣除。)*\n\n**⚠️ ST (Special Treatment) 股票**\n- **定義**：被標記為 <a:Error:1371096622053724292> 的股票，通常表示該公司可能存在財務或其他營運風險，例如連續虧損。\n- **交易影響**：ST 股票的每日價格漲跌幅限制可能更嚴格（例如 `±5%`）。\n- **風險**：投資 ST 股票風險較高，請謹慎評估。\n\n**📉 退市 (Delisting)**\n- **定義**：股票從交易所下市，不再公開交易。原因可能包括：公司破產、長期不符合上市標準、私有化等。\n- **影響**：一旦股票退市，您持有的股票可能難以出售或價值歸零。\n- **處理**：系統可能會以特定方式處理退市股票的剩餘價值（例如，以最後價格結算或按比例退還）。\n\n**💡 投資小提示**\n- **分散風險**：不要將所有資金投入單一股票。\n- **理性投資**：股價由系統算法決定，不要FOMO進場。\n- **市場波動**：價格波動是正常的，保持理性。\n\n*(本教學僅供參考，不構成任何投資建議。市場有風險，投資需謹慎。)*"
        await interaction.response.send_message(tutorial_message, ephemeral=True)


class StockDetailView(BaseStockView):
    """股票詳情視圖"""

    def __init__(
        self,
        cog: "StockCog",
        initiating_interaction: discord.Interaction,
        message: Optional[discord.Message] = None,
        asset_symbol_or_id: Optional[Union[str, int]] = None,
        came_from_list_view: bool = False,
    ):
        super().__init__(cog, initiating_interaction, message, timeout=300.0)
        self.asset_symbol_or_id = asset_symbol_or_id
        self.stock_data: Optional[StockDetailData] = None
        self.came_from_list_view = came_from_list_view

    async def _get_data_for_display(
        self, asset_id_to_load: Optional[Union[str, int]] = None, **kwargs
    ) -> Optional[StockDetailData]:
        target_asset = (
            asset_id_to_load
            if asset_id_to_load is not None
            else self.asset_symbol_or_id
        )
        if target_asset is None:
            logger.error(
                "StockDetailView: No asset_symbol_or_id provided to fetch data."
            )
            return None
        self.stock_data = await self.cog.get_stock_detail_data(
            asset_symbol_or_id=target_asset, user_id=self.user_id
        )
        return self.stock_data

    def _build_embed(self, data: StockDetailData) -> discord.Embed:
        stock_data_dict = (
            dataclasses.asdict(data.stock_data) if data.stock_data else None
        )
        aggregated_volume_dict = None
        if data.aggregated_volume_7d:
            if hasattr(data.aggregated_volume_7d, "__dataclass_fields__"):
                aggregated_volume_dict = dataclasses.asdict(data.aggregated_volume_7d)
            elif isinstance(data.aggregated_volume_7d, dict):
                aggregated_volume_dict = data.aggregated_volume_7d
        recent_transactions_list_of_dicts = None
        if data.recent_transactions_5:
            recent_transactions_list_of_dicts = []
            for rt in data.recent_transactions_5:
                if hasattr(rt, "__dataclass_fields__"):
                    recent_transactions_list_of_dicts.append(dataclasses.asdict(rt))
                elif isinstance(rt, dict):
                    recent_transactions_list_of_dicts.append(rt)
        asset_symbol_or_id_for_builder = self.asset_symbol_or_id
        if data.stock_data and data.stock_data.asset_symbol:
            asset_symbol_or_id_for_builder = data.stock_data.asset_symbol
        return StockDetailEmbedBuilder(
            stock_data=stock_data_dict,
            asset_symbol_or_id=asset_symbol_or_id_for_builder,
            aggregated_volume_7d=aggregated_volume_dict,
            recent_transactions_5=recent_transactions_list_of_dicts,
            chart_image_bytes=data.chart_image_bytes,
            user_oil_balance=data.user_oil_balance,
            user_stock_quantity=data.user_stock_quantity,
            user_short_quantity=data.user_short_quantity,
        ).build()

    def _build_components(self, data: StockDetailData):
        # Row 0: 交易按鈕
        buy_button = discord.ui.Button(
            label="買入股票",
            style=discord.ButtonStyle.success,
            custom_id="stockdetail_buy",
            emoji="🛒",
            row=0,
        )
        buy_button.callback = self.buy_stock_button_callback
        self.add_item(buy_button)

        sell_button = discord.ui.Button(
            label="賣出股票",
            style=discord.ButtonStyle.danger,
            custom_id="stockdetail_sell",
            emoji="💸",
            row=0,
        )
        sell_button.callback = self.sell_stock_button_callback
        self.add_item(sell_button)

        short_button = discord.ui.Button(
            label="做空股票",
            style=discord.ButtonStyle.success,
            custom_id="stockdetail_short",
            emoji="📉",
            row=0,
        )
        short_button.callback = self.short_stock_button_callback
        self.add_item(short_button)

        cover_button = discord.ui.Button(
            label="回補做空",
            style=discord.ButtonStyle.danger,
            custom_id="stockdetail_cover",
            emoji="📈",
            row=0,
        )
        cover_button.callback = self.cover_short_button_callback
        self.add_item(cover_button)

        if self.came_from_list_view:
            back_button = discord.ui.Button(
                label="返回列表",
                style=discord.ButtonStyle.grey,
                custom_id="stockdetail_back_to_list",
                emoji="↩️",
                row=0,
            )
            back_button.callback = self.back_to_list_view_callback
            self.add_item(back_button)

        # Row 1: 功能按鈕
        news_button = discord.ui.Button(
            label="近期新聞",
            style=discord.ButtonStyle.secondary,
            custom_id="stockdetail_news",
            emoji="📰",
            row=1,
        )
        news_button.callback = self.related_news_button_callback
        self.add_item(news_button)

        refresh_button = discord.ui.Button(
            label="刷新數據",
            style=discord.ButtonStyle.blurple,
            custom_id="stockdetail_refresh",
            emoji="🔄",
            row=1,
        )
        refresh_button.callback = self.refresh_button_callback
        self.add_item(refresh_button)

    async def refresh_button_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        await self.update_display(
            interaction_for_update=interaction, asset_id_to_load=self.asset_symbol_or_id
        )

    async def buy_stock_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            raise BusinessError("股票數據未加載，無法買入。")

        modal = BuyStockModal(
            bot=self.cog.bot,
            stock_symbol=self.stock_data.stock_data.asset_symbol,
            current_price=self.stock_data.stock_data.current_price,
            on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(
                modal_interaction
            ),
        )
        await interaction.response.send_modal(modal)

    async def sell_stock_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            raise BusinessError("股票數據未加載，無法賣出。")

        # 優化：直接使用已有的用戶持股數量，避免額外的數據庫查詢
        user_quantity = self.stock_data.user_stock_quantity or 0
        modal = SellStockModal(
            bot=self.cog.bot,
            stock_symbol=self.stock_data.stock_data.asset_symbol,
            current_price=self.stock_data.stock_data.current_price,
            user_id=interaction.user.id,
            max_quantity=user_quantity,
            on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(
                modal_interaction
            ),
        )
        await interaction.response.send_modal(modal)

    async def short_stock_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            raise BusinessError("股票數據未加載，無法做空。")

        modal = ShortStockModal(
            bot=self.cog.bot,
            stock_symbol=self.stock_data.stock_data.asset_symbol,
            current_price=self.stock_data.stock_data.current_price,
            on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(
                modal_interaction
            ),
        )
        await interaction.response.send_modal(modal)

    async def cover_short_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            raise BusinessError("股票數據未加載，無法回補。")

        # 使用用戶的做空數量
        user_short_quantity = self.stock_data.user_short_quantity or 0
        if user_short_quantity <= 0:
            raise BusinessError(
                f"您沒有 {self.stock_data.stock_data.asset_symbol} 的做空倉位。"
            )

        modal = CoverShortModal(
            bot=self.cog.bot,
            stock_symbol=self.stock_data.stock_data.asset_symbol,
            current_price=self.stock_data.stock_data.current_price,
            user_id=interaction.user.id,
            max_short_quantity=user_short_quantity,
            on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(
                modal_interaction
            ),
        )
        await interaction.response.send_modal(modal)

    async def related_news_button_callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        if not self.stock_data or not self.stock_data.stock_data:
            await interaction.followup.send(
                "無法獲取相關新聞，數據或服務未就緒。", ephemeral=True
            )
            return
        asset_id = self.stock_data.stock_data.asset_id
        asset_symbol = self.stock_data.stock_data.asset_symbol
        news_records_raw = await stock_trading_service.get_related_news_for_stock(
            asset_id, limit=5
        )
        if news_records_raw:
            embed = discord.Embed(
                title=f"{asset_symbol} 相關新聞", color=discord.Color.blurple()
            )
            description = ""
            for rec in news_records_raw:
                ts = discord.utils.format_dt(rec["published_at"], "d")
                headline = rec.get("headline", "無標題")
                description += f"• ({ts}) {headline}\n"
            embed.description = description if description else "暫無新聞描述。"
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(
                f"沒有找到與 {asset_symbol} 相關的新聞。", ephemeral=True
            )

    async def trigger_internal_refresh(
        self, modal_submit_interaction: discord.Interaction
    ):
        logger.info(
            "StockDetailView: Triggering internal refresh for %s due to modal action by %s",
            self.asset_symbol_or_id,
            modal_submit_interaction.user.id,
        )
        await self.update_display(
            interaction_for_update=modal_submit_interaction,
            asset_id_to_load=self.asset_symbol_or_id,
        )

    async def back_to_list_view_callback(self, interaction: discord.Interaction):
        """Callback to return to the stock list view."""
        await interaction.response.defer()
        list_view = StockListView(
            cog=self.cog,
            initiating_interaction=self.initiating_interaction,
            message=self.message,
        )
        await list_view.update_display(interaction_for_update=interaction)


class StockCog(commands.Cog, name="股票"):
    """股票市場相關指令"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # 6. 【刪除】不再在 __init__ 中獲取和儲存服務實例

        # 啟動股票相關後台服務
        self.bot.loop.create_task(self.start_stock_services())

        logger.info("StockCog initialized.")

    async def start_stock_services(self):
        """異步啟動股票相關服務"""
        try:
            # 啟動價格更新服務
            logger.info("正在啟動 PriceUpdateService...")
            await price_update_service.start_price_update_worker()
            logger.info("✅ PriceUpdateService 已由 StockCog 啟動")

            # 啟動股票相關定時任務
            logger.info("正在啟動股票相關定時任務...")
            scheduled_task_orchestrator.initialize_tasks()
            logger.info("✅ 股票定時任務已由 StockCog 啟動（包括股價更新、新聞調度等）")

        except Exception as e:
            logger.error("❌ StockCog 啟動股票服務失敗: %s", e, exc_info=True)

    async def cog_unload(self):
        """在 Cog 卸載時停止服務"""
        logger.info("StockCog 正在卸載，準備停止股票相關服務...")
        try:
            # 停止價格更新服務
            await price_update_service.stop_price_update_worker()

            # 停止定時任務
            scheduled_task_orchestrator.cancel_tasks()

            logger.info("✅ 股票服務已成功停止")
        except Exception as e:
            logger.error("❌ StockCog 卸載時停止股票服務失敗: %s", e, exc_info=True)

    async def _ensure_user_exists(self, user_id: int, nickname: str) -> None:
        """確保用戶存在，如果不存在則創建"""
        # 7. 【修改】直接調用導入的服務模組
        await validation_service.ensure_user_exists(
            user_id, nickname=nickname, create_if_missing=True
        )
        await user_service.ensure_nickname_updated(user_id, nickname)

    async def get_stock_list_page_data(
        self, page: int, per_page: int = 10
    ) -> Optional[StockListPageData]:
        """獲取股票列表頁面數據"""
        logger.info(
            "StockCog: Fetching stock list page %s, per_page %s", page, per_page
        )
        # 8. 【修改】直接調用導入的服務模組
        raw_page_data = await stock_trading_service.get_all_stocks_paginated(
            page=page, per_page=per_page
        )
        if not raw_page_data:
            return None

        stocks_dto_list: List[StockData] = []
        if raw_page_data.get("stocks"):
            for stock_dict in raw_page_data["stocks"]:
                stocks_dto_list.append(StockData.from_record(stock_dict))

        return StockListPageData(
            stocks=stocks_dto_list,
            current_page=raw_page_data.get("page", page),
            total_pages=raw_page_data.get("total_pages", 1),
            total_stocks=raw_page_data.get("total_stocks", 0),
        )

    async def get_stock_detail_data(
        self, asset_symbol_or_id: Union[str, int], user_id: int
    ) -> Optional[StockDetailData]:
        """獲取股票詳細數據"""
        logger.info(
            "StockCog: Fetching stock detail for %s, user %s",
            asset_symbol_or_id,
            user_id,
        )
        # 9. 【修改】直接調用導入的服務模組
        raw_detail_data = await stock_trading_service.get_stock_details_for_view(
            asset_symbol_or_id, user_id=user_id
        )
        # 這裡不再需要檢查 None，因為 service 會直接拋出 StockNotFoundError
        if not raw_detail_data:
            return None

        # 轉換為 DTO
        stock_data_dto = StockData.from_record(raw_detail_data["stock_data"])

        agg_vol_dto = None
        agg_vol_raw = raw_detail_data.get("aggregated_volume_7d")
        if agg_vol_raw:
            agg_vol_dto = AggregatedVolumeData(**agg_vol_raw)

        recent_trans_dto_list = []
        if raw_detail_data.get("recent_transactions_5"):
            for trans_dict in raw_detail_data["recent_transactions_5"]:
                recent_trans_dto_list.append(
                    RecentTransactionData.from_record(trans_dict)
                )

        return StockDetailData(
            stock_data=stock_data_dto,
            aggregated_volume_7d=agg_vol_dto,
            recent_transactions_5=recent_trans_dto_list,
            chart_image_bytes=raw_detail_data.get("chart_image_bytes"),
            user_oil_balance=(
                Decimal(str(raw_detail_data["user_oil_balance"]))
                if raw_detail_data.get("user_oil_balance") is not None
                else None
            ),
            user_stock_quantity=raw_detail_data.get("user_stock_quantity"),
            user_short_quantity=raw_detail_data.get("user_short_quantity"),
        )

    async def stock_symbol_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        choices = []
        # 10. 【修改】直接調用導入的服務模組
        asset_details = await stock_trading_service.search_asset_symbols_with_details(
            current
        )
        for asset_info in asset_details:
            symbol = asset_info["asset_symbol"]
            name = asset_info["asset_name"]
            price = asset_info["current_price"]
            status = asset_info["lifecycle_status"]

            # 添加狀態標記
            status_suffix = ""
            if status == "st":
                status_suffix = " (ST)"

            # 格式化顯示名稱：代碼 - 名稱 (價格) [狀態]
            display_name = (
                f"{symbol} - {name} ({Decimal(str(price)):.2f} 油幣){status_suffix}"
            )

            # 限制顯示名稱長度以符合 Discord 限制
            if len(display_name) > 100:
                display_name = display_name[:97] + "..."

            choices.append(app_commands.Choice(name=display_name, value=symbol))
        return choices

    @app_commands.command(
        name="stock", description="查看股票市場行情或特定股票詳細信息。"
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(symbol="要查詢的股票代碼 (可選，留空則顯示列表)")
    @app_commands.autocomplete(symbol=stock_symbol_autocomplete)
    async def stock(
        self, interaction: discord.Interaction, symbol: Optional[str] = None
    ):
        await interaction.response.defer(ephemeral=False)
        # 確保用戶存在
        await self._ensure_user_exists(
            interaction.user.id, interaction.user.display_name
        )

        if symbol:
            view = StockDetailView(
                cog=self,
                initiating_interaction=interaction,
                asset_symbol_or_id=symbol,
            )
        else:
            view = StockListView(cog=self, initiating_interaction=interaction)

        await view.send_initial_message(ephemeral=False)


async def setup(bot: commands.Bot):
    await bot.add_cog(StockCog(bot))
    logger.info("StockCog added successfully.")
