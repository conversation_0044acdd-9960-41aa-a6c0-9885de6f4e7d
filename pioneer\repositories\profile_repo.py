"""
Pioneer System - Profile Repository
用戶資料、時代、能量相關的資料庫存取
"""

from typing import Optional

import asyncpg

import gacha.services.economy_service as economy_service
from gacha.repositories._base_repo import execute_query, fetch_one
from pioneer.exceptions import PioneerDatabaseError, PioneerProfileNotFoundError
from pioneer.models.pioneer_models import PioneerProfile
from utils.logger import logger

# ========================================
# 用戶資料相關
# ========================================


async def get_pioneer_profile(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> PioneerProfile:
    """獲取開拓者個人資料"""
    query = "SELECT * FROM pioneer_profiles WHERE user_id = $1"
    result = await fetch_one(query, (user_id,), connection=connection)

    if not result:
        # 創建初始資料
        await create_pioneer_profile(user_id, connection=connection)
        result = await fetch_one(query, (user_id,), connection=connection)

    if not result:
        raise PioneerProfileNotFoundError(user_id)

    return PioneerProfile(
        user_id=result["user_id"],
        energy=result["energy"],
        max_energy=result["max_energy"],
        last_energy_update=result["last_energy_update"],
        pending_oil_earnings=result["pending_oil_earnings"],
        total_oil_earnings=result["total_oil_earnings"],
        current_era=result["current_era"],
        created_at=result["created_at"],
        updated_at=result["updated_at"],
    )


async def create_pioneer_profile(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """創建開拓者個人資料，並在首次創建時初始化技能"""
    try:
        # 步驟 1: 確保 gacha_users 中存在該用戶
        from gacha.services import user_service

        await user_service.create_user(user_id, connection=connection)

        # 步驟 2: 創建 pioneer_profiles 記錄
        profile_query = """
            INSERT INTO pioneer_profiles (user_id, energy, max_energy, current_era)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (user_id) DO NOTHING
            RETURNING (xmax = 0) AS created
        """
        result = await fetch_one(
            profile_query, (user_id, 100, 100, 1), connection=connection
        )

        # 如果 result is None，表示用戶已存在
        created = result and result["created"]

        if created:
            # 如果是新創建的用戶，初始化所有基礎技能
            from pioneer.core.game_data_loader import game_data

            if game_data is None:
                logger.error("GameDataLoader 尚未初始化，無法初始化技能")
                return True

            all_skills = game_data.skills

            if not all_skills:
                logger.warning(
                    f"為新用戶 {user_id} 初始化技能失敗：未從 game_data 載入任何技能。"
                )
                return True

            # 準備批量插入技能
            skill_values = [(user_id, skill_id, 1, 0) for skill_id in all_skills.keys()]

            skill_query = """
                INSERT INTO pioneer_skills (user_id, skill_id, level, xp)
                SELECT * FROM UNNEST($1::bigint[], $2::text[], $3::int[], $4::int[])
                ON CONFLICT (user_id, skill_id) DO NOTHING
            """

            user_ids, skill_ids, levels, xps = zip(*skill_values)

            await execute_query(
                skill_query,
                (list(user_ids), list(skill_ids), list(levels), list(xps)),
                connection=connection,
            )
            logger.info(
                "為新用戶 %s 初始化了 %s 個基礎技能。", user_id, len(all_skills)
            )

            # 初始化新用戶的任務
            from pioneer.services.task_initializer import task_initializer

            await task_initializer.initialize_quests_for_new_user(
                user_id, connection=connection
            )

        return bool(created)
    except Exception as e:
        logger.error("創建開拓者資料或初始化技能失敗 user_id=%s: %s", user_id, e)
        raise PioneerDatabaseError("create_pioneer_profile", str(e), e) from e


async def update_energy(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """更新用戶能量（基於時間恢復）"""
    # 每5分鐘恢復1點能量
    query = """
        UPDATE pioneer_profiles 
        SET energy = LEAST(
            max_energy, 
            energy + GREATEST(0, EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_energy_update)) / 300)::INTEGER
        ),
        last_energy_update = CURRENT_TIMESTAMP
        WHERE user_id = $1
    """
    try:
        await execute_query(query, (user_id,), connection=connection)
        return True
    except Exception as e:
        logger.error("更新能量失敗 user_id=%s: %s", user_id, e)
        raise PioneerDatabaseError("update_energy", str(e), e) from e


async def add_pending_oil_earnings(
    user_id: int, amount: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """增加待收取的油幣收益"""
    query = """
        UPDATE pioneer_profiles
        SET pending_oil_earnings = pending_oil_earnings + $2,
            total_oil_earnings = total_oil_earnings + $2,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
    """
    try:
        await execute_query(query, (user_id, amount), connection=connection)
        return True
    except Exception as e:
        logger.error(
            "增加待收取油幣收益失敗 user_id=%s, amount=%s: %s", user_id, amount, e
        )
        raise PioneerDatabaseError("add_pending_oil_earnings", str(e), e) from e


async def collect_pending_oil_earnings(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """收取待收取的油幣收益，返回收取的數量"""
    # 首先獲取當前待收取數量
    get_query = "SELECT pending_oil_earnings FROM pioneer_profiles WHERE user_id = $1"
    try:
        result = await fetch_one(get_query, (user_id,), connection=connection)
        if not result:
            return 0

        pending_amount = result["pending_oil_earnings"]
        if pending_amount <= 0:
            return 0

        # 清零待收取收益
        update_query = """
            UPDATE pioneer_profiles
            SET pending_oil_earnings = 0,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        await execute_query(update_query, (user_id,), connection=connection)

        # 發放油幣到主系統
        await economy_service.award_oil(
            user_id=user_id,
            amount=pending_amount,
            transaction_type="pioneer:collect_earnings",
            reason="Collect earnings from pioneer system",
        )

        return pending_amount

    except Exception as e:
        logger.error("收取待收取油幣收益失敗 user_id=%s: %s", user_id, e)
        raise PioneerDatabaseError("collect_pending_oil_earnings", str(e), e) from e


async def consume_energy(
    user_id: int, amount: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """消耗能量（原子操作：恢復 + 檢查 + 消耗）"""
    # 將能量恢復、檢查和消耗合併到一個原子性的數據庫操作中
    query = """
        UPDATE pioneer_profiles
        SET energy = GREATEST(0,
            LEAST(
                max_energy,
                energy + GREATEST(0, EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_energy_update)) / 300)::INTEGER
            ) - $2
        ),
        last_energy_update = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
        AND LEAST(
            max_energy,
            energy + GREATEST(0, EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_energy_update)) / 300)::INTEGER
        ) >= $2
    """
    try:
        result = await execute_query(query, (user_id, amount), connection=connection)
        return result > 0
    except Exception as e:
        logger.error("消耗能量失敗 user_id=%s, amount=%s: %s", user_id, amount, e)
        raise PioneerDatabaseError("consume_energy", str(e), e) from e


async def update_user_era(
    user_id: int, new_era: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """更新玩家的當前時代"""
    query = """
        UPDATE pioneer_profiles
        SET current_era = $2, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
    """
    try:
        await execute_query(query, (user_id, new_era), connection=connection)
        return True
    except Exception as e:
        logger.error("更新用戶時代失敗 user_id=%s, new_era=%s: %s", user_id, new_era, e)
        raise PioneerDatabaseError("update_user_era", str(e), e) from e
