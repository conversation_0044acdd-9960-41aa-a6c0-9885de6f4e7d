"""
Profile 相關的資料模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional


@dataclass
class UserProfile:
    """用戶檔案資料模型"""

    user_id: int
    showcased_card_collection_id: Optional[int] = None
    sub_card_1_collection_id: Optional[int] = None
    sub_card_2_collection_id: Optional[int] = None
    sub_card_3_collection_id: Optional[int] = None
    sub_card_4_collection_id: Optional[int] = None
    background_image_url: Optional[str] = None
    like_count: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    user_status: Optional[str] = None
    showcased_card_frame_number: Optional[int] = None  # 主卡片預覽影格編號
    sub_card_1_frame_number: Optional[int] = None
    sub_card_2_frame_number: Optional[int] = None
    sub_card_3_frame_number: Optional[int] = None
    sub_card_4_frame_number: Optional[int] = None
    profile_image_url: Optional[str] = None  # 檔案圖片 CDN URL


@dataclass
class ProfileCardInfo:
    """檔案卡片資訊"""

    collection_id: int
    card_id: int
    name: str
    series: str
    image_url: str
    rarity: int
    star_level: int
    quantity: int
    local_image_path: Optional[str] = None  # 卡片預下載的本地圖片路徑
    static_preview_path: Optional[str] = None  # 如果是GIF，這是生成的靜態預覽圖路徑


@dataclass
class ProfileData:
    """完整的檔案資料，用於圖片生成"""

    # 用戶基本資訊
    user_id: int
    nickname: Optional[str]
    oil_balance: int
    oil_ticket_balance: Decimal
    total_draws: int
    like_count: int

    # 統計資訊
    collection_completion_rate: float
    total_owned_cards: int
    rarity_counts: Dict[int, int]

    # 卡片資訊
    main_card: Optional[ProfileCardInfo] = None
    sub_cards: Dict[int, ProfileCardInfo] = field(default_factory=dict)
    background_image_url: Optional[str] = None

    # 用戶個性化信息
    user_status: Optional[str] = None
    avatar_url: Optional[str] = None

    # 清理管理
    _preview_files_to_cleanup: List[str] = field(default_factory=list)


@dataclass
class ProfileImageGenerationData:
    """圖片生成所需的資料"""

    profile_data: ProfileData
    output_size: tuple = (1536, 864)
