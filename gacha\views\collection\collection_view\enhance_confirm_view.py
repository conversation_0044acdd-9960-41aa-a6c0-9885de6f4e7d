"""
Gacha系統卡片升星確認視圖
提供卡片升星確認和連續升星功能

重構後採用統一的服務訪問模式，保持系統架構的一致性
消除重複代碼，使用基礎類統一升星邏輯
"""

import asyncio
from typing import TYPE_CHECKING, Any, Dict

import discord

from gacha.constants import RarityLevel
from gacha.exceptions import CardNotFoundError
from gacha.repositories.collection import user_collection_repository
from gacha.services import star_enhancement_service

# 移除了複雜的 interaction_utils 導入
from gacha.views import utils as view_utils

if TYPE_CHECKING:
    from .card_view import CollectionView


from utils.base_view import BaseView, BotType


class BaseEnhanceView(BaseView):
    """升星視圖基礎類

    包含所有升星視圖的共用邏輯，消除重複代碼
    """

    def __init__(
        self,
        bot: BotType,
        user_id: int,
        card_id: int,
        collection_view: "CollectionView",
        timeout: int = 120,
    ):
        """初始化基礎升星視圖"""
        super().__init__(bot=bot, user_id=user_id, timeout=timeout)
        self.card_id = card_id
        self.collection_view = collection_view
        # 移除複雜的 InteractionManager
        self.original_interaction = getattr(
            collection_view, "original_interaction", None
        )

    async def _execute_enhancement(self, interaction: discord.Interaction):
        """執行升星操作 - 乾淨的單一路徑"""
        await interaction.response.defer()

        # 根據規範，移除 try...except，讓異常自然冒泡
        # 執行升星操作
        result = await star_enhancement_service.enhance_card(self.user_id, self.card_id)

        # 獲取更新後的卡片數據
        updated_card_data = await user_collection_repository.get_user_card(
            self.user_id, self.card_id
        )

        # 檢查是否可以繼續升星
        can_continue = await self._check_can_continue_enhance()

        # 創建結果嵌入和新視圖
        card_embed = self._create_card_embed(updated_card_data, result, {})
        continue_view = EnhanceContinueView(
            self.collection_view.bot,
            self.user_id,
            self.card_id,
            self.collection_view,
            {},
            can_continue,
        )
        await interaction.edit_original_response(embed=card_embed, view=continue_view)

        # 異步更新原始視圖
        asyncio.create_task(self._update_collection_view())

    async def _check_can_continue_enhance(self) -> bool:
        """檢查是否可以繼續升星 - 靜默檢查，不拋出異常"""
        # 根據規範，只捕捉預期的業務邏輯錯誤，讓其他未知錯誤冒泡
        from gacha.exceptions import BusinessError

        try:
            await star_enhancement_service.check_enhancement_possibility(
                self.user_id, self.card_id
            )
            return True
        except BusinessError:
            return False

    def _create_card_embed(
        self,
        card_data: Any,
        result: Dict[str, Any],
        next_enhancement_info: Dict[str, Any],
    ) -> discord.Embed:
        """創建卡片嵌入 - 乾淨的單一路徑"""
        star_enhanced = result["star_enhanced"]
        card_name = card_data.card.name
        old_star_level = result["old_star_level"]
        new_star_level = result["new_star_level"]
        color = (
            discord.Color.brand_green() if star_enhanced else discord.Color.brand_red()
        )
        star_emoji = "<a:sw:1365447243863429273>"

        message = result["message"].replace("消耗了", "\n消耗了")
        embed = discord.Embed(
            title=f"{star_emoji} {card_name} 升星結果 {star_emoji}",
            description=message,
            color=color,
        )

        embed.set_image(url=card_data.card.image_url)

        card_rarity_enum = RarityLevel(card_data.card.rarity)
        rarity_image_url = view_utils.get_rarity_image(card_rarity_enum)
        embed.set_thumbnail(url=rarity_image_url)

        old_star_emoji = view_utils.get_star_emoji_string(old_star_level)
        new_star_emoji = view_utils.get_star_emoji_string(new_star_level)
        embed.add_field(
            name="⭐ 星級提升 ⭐",
            value=f"**{old_star_level}** {old_star_emoji} → **{new_star_level}** {new_star_emoji}",
            inline=False,
        )

        embed.set_footer(text=f"系列: {card_data.card.series}")
        return embed

    async def _update_collection_view(self):
        """更新原始的CollectionView - 乾淨的單一路徑"""
        # 根據規範，移除 try-except，讓錯誤由全域處理器捕獲和記錄
        await self.collection_view._refresh_page_data_only()


class EnhanceConfirmView(BaseEnhanceView):
    """用於升星確認的臨時視圖

    自包含的升星確認視圖，處理所有升星相關邏輯
    """

    def __init__(
        self,
        bot: BotType,
        original_interaction: discord.Interaction,
        user_id: int,
        card_id: int,
        collection_view: "CollectionView",
        initial_enhancement_info: Dict[str, Any] | None = None,
        timeout: int = 120,
    ):
        """初始化升星確認視圖"""
        super().__init__(bot, user_id, card_id, collection_view, timeout)
        self.original_interaction = original_interaction
        self.current_enhancement_info = initial_enhancement_info

    @classmethod
    async def create_with_validation(
        cls,
        bot: BotType,
        original_interaction: discord.Interaction,
        user_id: int,
        card_id: int,
        collection_view: "CollectionView",
    ) -> "EnhanceConfirmView":
        """創建帶驗證的升星確認視圖

        自己處理升星可行性檢查和初始化
        """
        # 檢查升星可能性 - 如果不可能會拋出異常
        enhancement_info = await star_enhancement_service.check_enhancement_possibility(
            user_id, card_id
        )

        return cls(
            bot=bot,
            original_interaction=original_interaction,
            user_id=user_id,
            card_id=card_id,
            collection_view=collection_view,
            initial_enhancement_info=enhancement_info,
        )

    async def show_confirmation(self, interaction: discord.Interaction):
        """顯示升星確認界面 - 乾淨的單一路徑"""
        if not self.current_enhancement_info:
            raise ValueError("缺少升星資訊，無法顯示確認畫面。")
        costs = self.current_enhancement_info["costs"]
        success_rate = self.current_enhancement_info["success_rate"]
        current_stars = self.current_enhancement_info["current_star_level"]

        card_data = await user_collection_repository.get_user_card(
            self.user_id, self.card_id
        )
        if not card_data or not card_data.card:
            raise CardNotFoundError("升星時找不到卡片資料。")
        card_name = card_data.card.name

        confirmation_embed = self._create_confirmation_embed(
            card_name, current_stars, costs, success_rate
        )
        await interaction.followup.send(
            embed=confirmation_embed, view=self, ephemeral=True
        )

    def _create_confirmation_embed(
        self, card_name: str, current_stars: int, costs: dict, success_rate: float
    ) -> discord.Embed:
        """創建升星確認嵌入"""
        from config.app_config import get_oil_emoji

        embed = discord.Embed(
            title=f"升星確認 {current_stars}★ → {current_stars + 1}★",
            description=f"你確定要嘗試將 **{card_name}** 升星嗎？",
            color=discord.Color.blue(),
        )
        oil_emoji = get_oil_emoji()
        embed.add_field(
            name="所需油幣", value=f"{costs.get('oil', 0)} {oil_emoji}", inline=True
        )
        embed.add_field(
            name="所需重複卡", value=f"{costs.get('duplicates', 0)} 張", inline=True
        )
        embed.add_field(name="成功率", value=f"{success_rate:.1f}%", inline=True)
        embed.set_footer(text="點擊確認消耗資源嘗試升星")
        return embed

    @discord.ui.button(
        label="確認升星",
        style=discord.ButtonStyle.danger,
        custom_id="confirm_enhance_action_v5",
    )
    async def confirm_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """確認升星按鈕回調 - 使用基礎類的統一邏輯"""
        await self._execute_enhancement(interaction)

    @discord.ui.button(
        label="取消",
        style=discord.ButtonStyle.secondary,
        custom_id="cancel_enhance_action_v5",
    )
    async def cancel_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """取消升星按鈕回調"""
        cancel_embed = discord.Embed(
            title="操作取消",
            description="升星操作已取消。",
            color=discord.Color.greyple(),
        )
        await interaction.response.edit_message(embed=cancel_embed, view=None)


class EnhanceContinueView(BaseEnhanceView):
    """升星後的繼續升星視圖

    簡化實現，使用基礎類的統一邏輯
    """

    def __init__(
        self,
        bot: BotType,
        user_id: int,
        card_id: int,
        collection_view: "CollectionView",
        enhancement_info: Dict[str, Any],
        can_enhance: bool = True,
        timeout: int = 300,
    ):
        """初始化繼續升星視圖 - 根據條件設置按鈕狀態"""
        super().__init__(bot, user_id, card_id, collection_view, timeout)
        self.enhancement_info = enhancement_info

        # 根據條件設置按鈕狀態
        self.continue_button.disabled = not can_enhance
        self.continue_button.label = "繼續升星" if can_enhance else "無法繼續升星"

    @discord.ui.button(
        label="繼續升星",
        style=discord.ButtonStyle.primary,
        custom_id="continue_enhance_v5",
    )
    async def continue_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """繼續升星按鈕回調 - 乾淨的單一路徑"""
        await self._execute_enhancement(interaction)
