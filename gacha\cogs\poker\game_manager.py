# gacha/cogs/poker/game_manager.py
"""
遊戲管理器模組

此模組作為撲克遊戲核心狀態的中央存儲庫。
通過將狀態（如活躍的遊戲處理器、匹配隊列）從 Cog 實例中分離出來，
我們實現了真正的熱重載能力。當 Cog 被重載時，這個模組中的狀態會被保留，
確保正在進行的遊戲不會中斷。
"""

import asyncio

# 由於 processor 和 logic 可能會反向依賴此模組，我們使用類型提示字串來避免循環導入
from typing import TYPE_CHECKING, Dict, Optional, Set

if TYPE_CHECKING:
    from .logic import MatchmakingQueue
    from .processor import GameProcessor

# 模組級別的狀態變量
# 這些變量的生命週期與Python進程相同，不受Cog重載影響
active_processors: Dict[str, "GameProcessor"] = {}
matchmaking_queue: Optional["MatchmakingQueue"] = None  # 將在 cog 載入時初始化
players_in_operation: Set[int] = set()

# 用於保護共享狀態的鎖
processors_lock = asyncio.Lock()
operation_lock = asyncio.Lock()


def initialize_matchmaking_queue():
    """初始化或重新初始化匹配隊列"""
    global matchmaking_queue
    from .logic import MatchmakingQueue

    matchmaking_queue = MatchmakingQueue()
