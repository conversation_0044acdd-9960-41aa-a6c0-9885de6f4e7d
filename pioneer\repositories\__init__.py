"""
Pioneer Repositories Package
"""

from .facility_repo import (
    add_facility_slot_item,
    add_facility_upgrade,
    assign_card_to_facility,
    consume_facility_slot_item,
    create_facility,
    create_facility_slot,
    get_card_assignment_by_collection_id,
    get_facility,
    get_facility_card_assignment,
    get_facility_slots,
    get_user_facilities,
    get_user_facilities_by_type,
    remove_facility_card_assignment,
    update_facility_production_time,
    update_facility_slot_quantity,
)
from .profile_repo import (
    add_pending_oil_earnings,
    collect_pending_oil_earnings,
    consume_energy,
    create_pioneer_profile,
    get_pioneer_profile,
    update_energy,
    update_user_era,
)
from .quest_repo import (
    complete_quest,
    create_quest_progress,
    get_user_active_quests,
    update_quest_progress,
)
from .research_repo import (
    add_research_investment_oil,
    create_research_level,
    get_research_level,
    upgrade_research_level,
)
from .skill_repo import (
    add_skill_xp,
    get_user_skill,
    get_user_skills,
)
from .transaction_repo import (
    move_item_to_facility_slot,
    transfer_item_from_slot_to_warehouse,
)
from .warehouse_repo import (
    add_warehouse_item,
    calculate_warehouse_capacity,
    consume_warehouse_item,
    get_item_quantity,
    get_user_warehouse,
    get_warehouse_item,
    get_warehouse_total_quantity,
)

# For backward compatibility, we can define a pioneer_repo object
# that holds all the functions, but it's better to update the imports
# in the long run. For now, let's just export everything.

__all__ = [
    # profile_repo
    "get_pioneer_profile",
    "create_pioneer_profile",
    "update_energy",
    "add_pending_oil_earnings",
    "collect_pending_oil_earnings",
    "consume_energy",
    "update_user_era",
    # skill_repo
    "get_user_skill",
    "add_skill_xp",
    "get_user_skills",
    # warehouse_repo
    "get_warehouse_item",
    "get_item_quantity",
    "add_warehouse_item",
    "consume_warehouse_item",
    "get_user_warehouse",
    "get_warehouse_total_quantity",
    "calculate_warehouse_capacity",
    # facility_repo
    "create_facility",
    "get_facility",
    "get_user_facilities",
    "get_user_facilities_by_type",
    "add_facility_upgrade",
    "create_facility_slot",
    "get_facility_slots",
    "update_facility_production_time",
    "add_facility_slot_item",
    "consume_facility_slot_item",
    "update_facility_slot_quantity",
    "assign_card_to_facility",
    "remove_facility_card_assignment",
    "get_facility_card_assignment",
    "get_card_assignment_by_collection_id",
    # quest_repo
    "create_quest_progress",
    "get_user_active_quests",
    "update_quest_progress",
    "complete_quest",
    # research_repo
    "get_research_level",
    "create_research_level",
    "upgrade_research_level",
    "add_research_investment_oil",
    # transaction_repo
    "move_item_to_facility_slot",
    "transfer_item_from_slot_to_warehouse",
]
