import asyncio
import atexit
import logging
import os
import sys
from typing import Any, Union

import discord
from discord.ext import commands

# Discord Prometheus 監控模組
from discord.ext.prometheus import PrometheusCog, PrometheusLoggingHandler
from dotenv import load_dotenv

import scripts.initialize_market_stats
from auxiliary.services.db_command_usage_service import (
    CommandUsageRecord,
    get_db_command_usage_service,
    shutdown_db_command_usage_service,
)
from database.postgresql.async_manager import close_connections, setup_connections
from pioneer.core.game_data_loader import initialize_game_data
from utils import playwright_manager
from utils.connection_manager import ConnectionManager
from utils.error_handler import handle_interaction_error
from utils.logger import initialize_logging, logger

# 啟用 asyncio 偵錯模式，以幫助追蹤潛在的阻塞操作
loop = asyncio.get_event_loop()
loop.set_debug(True)  # 重新啟用，因為 Discord.py 已經重新安裝修復
# 將慢速回呼的門檻從預設的 0.1 秒調低到 0.01 秒 (10 毫秒)
loop.slow_callback_duration = 0.1

# 類型別名，支持 Bot 和 AutoShardedBot（将在 CustomAutoShardedBot 定义后更新）
BotType = Union[commands.Bot, commands.AutoShardedBot]

# 最早載入環境變數（在日誌初始化之前）
dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
if not os.path.exists(dotenv_path):
    dotenv_path = os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")), ".env"
    )

if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path, override=True)

initialize_logging()

# 添加 Prometheus 日誌處理器
logging.getLogger().addHandler(PrometheusLoggingHandler())


# 記錄環境變數已載入的信息
logger.info("環境變數已載入完成")

# ========================================
# PROMETHEUS 配置
# ========================================
logger.info("正在配置 Discord Prometheus 監控...")

# 設定機器人 intents
intents = discord.Intents.default()

# 從環境變數讀取分片數量，預設為1
shard_count = int(os.getenv("SHARD_COUNT", "1"))


class CustomAutoShardedBot(commands.AutoShardedBot):
    """自定義的 AutoShardedBot，添加我們需要的屬性"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 添加自定義屬性
        from datetime import datetime

        from auxiliary.services.db_command_usage_service import (
            DatabaseCommandUsageService,
        )
        from command_registry import CommandRegistry
        from utils.connection_manager import ConnectionManager

        self.connection_manager: ConnectionManager | None = None
        self.db_initialized: bool = False
        self.is_services_initialized: bool = False
        self.command_usage_service: DatabaseCommandUsageService | None = None
        self.start_time: datetime | None = None
        self._command_registry: CommandRegistry | None = None
        self.playwright_manager: Any = None  # Will be set during initialization
        self.image_generator: Any = None  # Will be set during initialization
        self.private_channel_id: int | None = None  # Will be set by ProfileCog


# 更新 BotType 类型别名以包含 CustomAutoShardedBot
BotType = Union[commands.Bot, commands.AutoShardedBot, CustomAutoShardedBot]

# 創建機器人實例 - 使用官方支持的參數
bot = CustomAutoShardedBot(
    command_prefix="$$",
    intents=intents,
    shard_count=shard_count,  # 根據環境變數設置分片數量
    shard_connect_timeout=300.0,  # 分片連接超時時間（秒）- 從默認180秒增加到300秒
    log_handler=None,  # 禁用 Discord.py 的自動日誌設置，使用我們自己的日誌系統
    activity=discord.Game(name="正在啟動..."),  # 初始狀態
    status=discord.Status.online,
)


@bot.tree.error
async def on_app_command_error(
    interaction: discord.Interaction, error: discord.app_commands.AppCommandError
):
    """Slash Commands 錯誤處理 - 現在只是一個轉發器"""
    await handle_interaction_error(interaction, error, bot)


# 添加全域檢查 - 針對 global_blacklist
async def global_blacklist_check(interaction: discord.Interaction) -> bool:
    """
    全域檢查：阻止 global_blacklist 中的用戶使用任何指令
    """
    try:
        from config.app_config import get_gacha_core_settings
        from gacha.exceptions import GlobalBlacklistCheckFailureError

        activity_settings = get_gacha_core_settings().activity_monitoring
        global_blacklist_set = set(activity_settings.global_blacklist)

        if interaction.user.id in global_blacklist_set:
            # 創建自定義異常並包裝在 CheckFailure 中
            message = (
                "⚠️ **您疑似為小號，已被限制使用所有指令**\n\n"
                "若認為是誤判，請：\n"
                "• 前往支援伺服器申訴\n"
                "• 或使用 `/問題回報` 指令回報\n\n"
                "⚠️ **注意**：請謹慎回報，您的所有指令行為（包括轉帳）都有完整紀錄。\n"
                "經調查發現是惡意申訴，將直接連同主帳號一起清空。"
            )

            # 創建自定義異常作為原因
            custom_error = GlobalBlacklistCheckFailureError(message)

            # 拋出 Discord.py 的 CheckFailure，並設置 __cause__
            check_failure = discord.app_commands.CheckFailure(message)
            check_failure.__cause__ = custom_error
            raise check_failure

        return True
    except discord.app_commands.CheckFailure:
        # 重新拋出 CheckFailure（包括我們的自定義異常）
        raise
    except Exception as e:
        from utils.logger import logger

        logger.error(f"全域黑名單檢查時發生錯誤: {e}")
        return True  # 發生錯誤時允許通過，避免影響正常用戶


# 註冊全域檢查
bot.tree.interaction_check = global_blacklist_check


# discord-ext-prometheus 會自動處理指令監控，不需要手動記錄


async def _initialize_connection_manager():
    try:
        bot.connection_manager = ConnectionManager(bot)
        logger.info("✅ 連接管理器已初始化")
    except Exception as e:
        logger.error("❌ 連接管理器初始化失敗: %s", e, exc_info=True)


async def _initialize_databases():
    try:
        await setup_connections()
        from database.postgresql.async_manager import get_pool

        pool = get_pool()
        bot.db_initialized = pool is not None and not pool.is_closing()
        if not bot.db_initialized:
            raise RuntimeError(
                "數據庫連接池初始化失敗，但 setup_connections 未拋出異常。"
            )
        logger.info("✅ 數據庫和 Redis 連接已成功初始化")
        return True
    except Exception as e:
        logger.error("❌ 數據庫或 Redis 連接初始化失敗: %s", e, exc_info=True)
        bot.db_initialized = False
        return False


async def _initialize_playwright():
    try:
        await playwright_manager.initialize()
        bot.playwright_manager = playwright_manager
        logger.info("✅ PlaywrightManager 初始化完成。")
    except Exception as e:
        logger.error("❌ PlaywrightManager 初始化失敗: %s", e, exc_info=True)


async def _initialize_command_usage_service():
    try:
        from database.postgresql.async_manager import get_pool

        pool = get_pool()
        if pool and not pool.is_closing():
            command_usage_service = await get_db_command_usage_service(
                pool=pool, batch_size=50, flush_interval=5.0
            )
            bot.command_usage_service = command_usage_service
            logger.info("✅ 數據庫指令使用統計服務已初始化（批量模式：50條/5秒）")
        else:
            logger.warning("⚠️ 數據庫連接池不可用，跳過指令統計服務初始化")
    except Exception as e:
        logger.error("❌ 指令使用統計服務初始化失敗: %s", e, exc_info=True)


async def _initialize_prometheus():
    try:
        prometheus_port = int(os.getenv("PROMETHEUS_PORT", 8003))
        await bot.add_cog(PrometheusCog(bot, port=prometheus_port))
        logger.info("✅ Prometheus 監控 Cog 已初始化，端口: %s", prometheus_port)
    except Exception as e:
        logger.error("❌ Prometheus 監控 Cog 初始化失敗: %s", e, exc_info=True)


async def _initialize_pioneer_game_data():
    pioneer_enabled = os.getenv("PIONEER_ENABLED", "FALSE").upper() in (
        "TRUE",
        "1",
        "YES",
    )
    if not pioneer_enabled:
        logger.info("ℹ️ Pioneer 系統已停用，跳過遊戲數據初始化。")
        return True
    try:
        await initialize_game_data()
        logger.info("✅ Pioneer 遊戲數據 (game_data) 初始化完成。")
        return True
    except Exception as e:
        logger.error("❌ Pioneer 遊戲數據 (game_data) 初始化失敗: %s", e, exc_info=True)
        return False


async def _schedule_background_tasks():
    try:
        logger.info("將市場統計初始化/更新安排為後台任務...")
        asyncio.create_task(_background_market_stats_init())
        logger.info("市場統計初始化已安排為後台任務，機器人將繼續啟動")
    except Exception as e:
        logger.error("❌ 啟動後台服務時發生錯誤: %s", e, exc_info=True)


async def _background_market_stats_init():
    try:
        from database.postgresql.async_manager import get_pool

        logger.info("開始後台執行市場統計初始化/更新...")
        pool = get_pool()
        if not pool:
            logger.error("❌ 後台市場統計初始化失敗：連接池不可用。")
            return
        async with pool.acquire() as conn:
            await scripts.initialize_market_stats.main(conn, force_full_update=False)
        logger.info("✅ 後台市場統計初始化/更新完成")
    except Exception as e:
        logger.error("❌ 後台市場統計初始化/更新失敗: %s", e, exc_info=True)


async def _register_cogs():
    logger.info("== 開始載入所有指令模組 (Cogs) ==")
    try:
        from command_registry import get_command_registry

        registry = get_command_registry(bot)
        if not await registry.register_all_commands():
            logger.warning("⚠️ 部分指令模組 (Cogs) 載入失敗。")
        else:
            logger.info("✅ 所有指令模組 (Cogs) 載入完成。")
    except Exception as e:
        logger.error("指令模組 (Cogs) 載入時發生錯誤: %s", e, exc_info=True)


async def _initialize_ai_service():
    """初始化AI服務"""
    try:
        from auxiliary.services.ai_core import ai_service, http_client

        logger.info("正在初始化 AI HTTP 客戶端...")
        await http_client.initialize_session()
        logger.info("✅ AI HTTP 客戶端已初始化")

        logger.info("正在初始化 AI 服務...")
        ai_service.initialize_ai_service()
        logger.info("✅ AI 服務已初始化")

    except Exception as e:
        logger.error("❌ AI 服務初始化失敗: %s", e, exc_info=True)
        raise


async def setup_hook():
    """異步設置鉤子 - 在 on_ready 之前調用"""
    logger.info("== 開始異步設置 ==")
    await _initialize_connection_manager()
    if not await _initialize_databases():
        return
    await _initialize_playwright()
    await _initialize_command_usage_service()
    await _initialize_ai_service()
    await _initialize_prometheus()
    logger.info("所有核心服務初始化完成")
    if not await _initialize_pioneer_game_data():
        return
    await _schedule_background_tasks()
    bot.is_services_initialized = True
    logger.info("✅ 所有服務已初始化，is_services_initialized 標記已設置為 True")
    await _register_cogs()
    logger.info("== 指令與 Discord 的同步需透過 /sync 手動執行 ==")


# Register the setup hook
bot.setup_hook = setup_hook


# ========================================
# App Commands (Slash Commands) 統計事件
# ========================================


@bot.event
async def on_app_command_completion(
    interaction: discord.Interaction, command: discord.app_commands.Command
):
    """當 App Command 成功執行後記錄統計數據"""
    try:
        # 獲取指令名稱
        command_name = (
            command.qualified_name
            if hasattr(command, "qualified_name")
            else command.name
        )

        # discord-ext-prometheus 會自動處理指令統計

        # 檢查是否有指令統計服務
        if not hasattr(bot, "command_usage_service") or not bot.command_usage_service:
            return

        # 創建使用記錄
        record = CommandUsageRecord(
            command_name=command_name,
            user_id=interaction.user.id,
            guild_id=interaction.guild.id if interaction.guild else None,
            channel_id=interaction.channel.id if interaction.channel else None,
            command_type="slash",  # App Commands 統一為 slash
            success=True,  # 成功執行
            error_message=None,
        )

        # 異步記錄（不等待完成，避免影響指令響應速度）
        asyncio.create_task(bot.command_usage_service.record_command_usage(record))

    except Exception as e:
        # 統計記錄失敗不應該影響指令執行
        logger.warning("記錄 App Command 完成統計失敗: %s", e)


# 全局異常處理器 (Fallback)
@bot.event
async def on_error(event_method: str, *args, **kwargs):
    """
    處理所有未被其他處理器（如 on_app_command_error）捕獲的異常。
    這是最後一道防線，確保任何未預期的錯誤都會被記錄。
    """
    # exc_info=True 會自動獲取異常信息
    logger.error("全局未處理異常 (BUG) in event '%s':", event_method, exc_info=True)


# 修改為使用 async 初始化
@bot.event
async def on_ready():
    """當機器人準備就緒時觸發"""
    # 記錄機器人啟動時間
    from datetime import datetime, timezone

    bot.start_time = datetime.now(timezone.utc)

    logger.info("== Bot Ready ==")
    logger.info(
        "Logged in as: %s (ID: %s)",
        bot.user.name if bot.user else "Unknown",
        bot.user.id if bot.user else "Unknown",
    )
    logger.info("Discord.py Version: %s", discord.__version__)
    logger.info("Shard ID: %s", bot.shard_id if bot.shard_id is not None else "N/A")

    db_status = "OK" if getattr(bot, "db_initialized", False) else "ERROR"
    logger.info("Database connection status: %s", db_status)

    # 命令註冊已移到 setup_hook 中
    logger.info("== Bot Ready - 命令已在 setup_hook 中註冊 ==")


async def update_bot_status():
    """更新機器人狀態顯示伺服器數量"""
    try:
        guild_count = len(bot.guilds)

        # discord-ext-prometheus 會自動處理伺服器和用戶統計

        activity = discord.Game(name=f"{guild_count} 伺服器")
        await bot.change_presence(activity=activity, status=discord.Status.online)
        logger.info("機器人狀態已更新：正在玩 %s 伺服器", guild_count)
    except Exception as e:
        logger.error("更新機器人狀態時發生錯誤: %s", e, exc_info=True)


@bot.event
async def on_guild_join(guild):
    """當機器人加入新伺服器時觸發"""
    logger.info("機器人已加入伺服器：%s (ID: %s)", guild.name, guild.id)
    await update_bot_status()


@bot.event
async def on_guild_remove(guild):
    """當機器人離開伺服器時觸發"""
    logger.info("機器人已離開伺服器：%s (ID: %s)", guild.name, guild.id)
    await update_bot_status()


@bot.event
async def on_connect():
    """處理機器人連接事件"""
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        bot.connection_manager.on_connect()


@bot.event
async def on_disconnect():
    """處理機器人斷線事件"""
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        bot.connection_manager.on_disconnect()


@bot.event
async def on_resumed():
    """處理機器人會話恢復事件"""
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        await bot.connection_manager.on_resumed()


@bot.event
async def on_shard_connect(shard_id):
    """處理分片連接事件"""
    logger.info("分片 #%s 已連接", shard_id)
    # discord-ext-prometheus 會自動處理分片統計
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        bot.connection_manager.on_shard_connect(shard_id)


@bot.event
async def on_shard_disconnect(shard_id):
    """處理分片斷線事件"""
    logger.warning("分片 #%s 已斷線", shard_id)
    # discord-ext-prometheus 會自動處理分片統計
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        bot.connection_manager.on_shard_disconnect(shard_id)


@bot.event
async def on_shard_resumed(shard_id):
    """處理分片恢復事件"""
    if hasattr(bot, "connection_manager") and bot.connection_manager is not None:
        bot.connection_manager.on_shard_resumed(shard_id)


# discord-ext-prometheus 會自動處理事件統計


@bot.event
async def on_close():
    """處理機器人關閉事件"""
    logger.info("機器人正在關閉...")

    # 關閉指令統計服務（全域基礎設施）
    if hasattr(bot, "command_usage_service") and bot.command_usage_service:
        try:
            logger.info("正在關閉指令統計服務...")
            await shutdown_db_command_usage_service()
            logger.info("指令統計服務已成功關閉。")
        except Exception as e:
            logger.error("關閉指令統計服務時發生錯誤: %s", e, exc_info=True)

    # 關閉 AI 服務
    try:
        from auxiliary.services.ai_core import ai_service

        logger.info("正在關閉 AI 服務...")
        await ai_service.close_ai_service()
        logger.info("✅ AI 服務已成功關閉。")
    except Exception as e:
        logger.error("❌ 關閉 AI 服務時發生錯誤: %s", e, exc_info=True)

    # 其他服務的關閉已轉移到各自的 Cog 中管理

    # 關閉圖片處理進程池 (已移除，因為新版本使用 asyncio.to_thread)
    # try:
    #     from auxiliary.services.outfit_rater.rating_image_generator import (
    #         shutdown_executor,
    #     )
    #     logger.info("正在關閉圖片處理進程池...")
    #     shutdown_executor()
    #     logger.info("✅ 圖片處理進程池已成功關閉。")
    # except Exception as e:
    #     logger.error("❌ 關閉圖片處理進程池時發生錯誤: %s", e, exc_info=True)

    # 關閉 Playwright Manager
    try:
        logger.info("正在關閉 PlaywrightManager...")
        await playwright_manager.close()
        logger.info("✅ PlaywrightManager 已成功關閉。")
    except Exception as e:
        logger.error("❌ 關閉 PlaywrightManager 時發生錯誤: %s", e, exc_info=True)

    # 關閉數據庫和 Redis 連接
    try:
        logger.info("正在關閉數據庫和 Redis 連接...")
        await close_connections()
        logger.info("✅ 數據庫和 Redis 連接已成功關閉。")
    except Exception as e:
        logger.error("❌ 關閉數據庫和 Redis 連接時發生錯誤: %s", e, exc_info=True)

    # 市場統計現在使用隊列模式，已在 BOT 服務系統關閉中停止

    logger.info("機器人關閉程序完成。")


# 啟動機器人
if __name__ == "__main__":
    # 日誌系統已在頂部初始化

    # 現在可以安全地使用 logger
    if not os.path.exists(dotenv_path):
        logger.warning("找不到 .env 文件，將使用系統環境變數")

    token = os.getenv("DISCORD_TOKEN")
    if not token:
        logger.critical(
            "錯誤：找不到 DISCORD_TOKEN 環境變數，請檢查 .env 文件。機器人無法啟動。"
        )
        sys.exit(1)

    # 註冊程序退出處理函數
    def cleanup_on_exit():
        """程序退出時執行的清理函數"""
        logger.info("程序退出，清理資源...")
        # 市場統計現在使用隊列模式，無需清理獨立進程

    atexit.register(cleanup_on_exit)

    try:
        # Prometheus 服務器已在 setup_hook 中通過 PrometheusCog 啟動

        logger.info("正在啟動機器人...")
        logger.info("機器人將使用 %s 個分片運行", bot.shard_count)
        shard_mode = "自動" if isinstance(bot, commands.AutoShardedBot) else "手動"
        logger.info("分片模式: %s", shard_mode)
        bot.run(token)
    except KeyboardInterrupt:
        logger.info("收到鍵盤中斷信號，正在關閉機器人...")
        cleanup_on_exit()
    except Exception:
        logger.exception("啟動機器人時發生未預期的錯誤")
