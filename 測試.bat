@echo off
chcp 65001 > nul
echo 設定測試環境變數（禁用 WEBHOOK 通知）...
(
echo DISCORD_TOKEN=MTMzNzEzMDg4MTY4NDM0NDg0Mw.GiN58S.8dXetrG80Us6_LEDbNesT7yqm7sD07FUjdPPrM
echo GACHA_DB_NAME=test
echo LOG_LEVEL=DEBUG
echo PG_HOST=127.0.0.1
echo PG_PORT=5432
echo PG_USER=postgres
echo PG_PASSWORD=26015792
echo DEV_MODE=true
echo REDIS_HOST=localhost
echo REDIS_PORT=6379
echo REDIS_DB=1
echo REDIS_PASSWORD=
echo REDIS_ENABLED=True
echo RPG=FALSE
echo PIONEER_ENABLED=true
echo WEBHOOK_NOTIFICATIONS_ENABLED=false
echo SHARD_COUNT=1
) > .env
echo 環境變數設定完成，WEBHOOK 通知已禁用
echo 啟動測試環境...
python bot.py