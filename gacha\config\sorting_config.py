"""
統一的排序配置管理模塊

將所有排序相關的配置、常量和驗證邏輯統一管理，
解決多處定義導致的不一致問題。

區分：
- 標準排序：按卡片屬性排序（稀有度、名稱等）
- 自定義排序：用戶手動調整的卡片順序（最愛功能）
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple


@dataclass
class SortOption:
    """統一的排序選項數據類

    整合了UI顯示、數據庫字段映射等所有排序相關信息
    """

    value: str  # 排序選項值（如 "rarity"）
    label: str  # UI顯示標籤（如 "稀有度"）
    db_field: str  # 數據庫字段映射（如 "mc.rarity"）
    description: str = ""  # 詳細描述

    def to_command_choice(self):
        """轉換為Discord指令選項"""
        from discord import app_commands

        return app_commands.Choice(name=self.label, value=self.value)

    def to_select_option(self):
        """轉換為Discord UI選擇選項"""
        import discord

        return discord.SelectOption(
            label=self.label,
            value=self.value,
            description=self.description if self.description else None,
        )


class SortingConfig:
    """統一的排序配置類

    管理所有排序相關的常量和配置，確保全局一致性
    """

    # 統一的默認值
    DEFAULT_SORT_BY = "rarity"
    DEFAULT_SORT_ORDER = "desc"

    # 統一的排序選項定義（包含數據庫字段映射）
    SORT_OPTIONS = [
        # 時間相關
        SortOption("claimed_at", "取得日期", "uc.last_acquired", "按卡片獲得時間排序"),
        SortOption(
            "creation_date", "創建日期", "mc.creation_date", "按卡片創建日期排序"
        ),
        # 卡片基本屬性
        SortOption("rarity", "稀有度", "mc.rarity", "按卡片稀有度排序"),
        SortOption("name", "名稱", "mc.name", "按卡片名稱排序"),
        SortOption("series", "系列", "mc.series", "按卡片系列排序"),
        SortOption("card_id", "卡片ID", "mc.card_id", "按卡片ID排序"),
        # 用戶相關屬性
        SortOption("quantity", "數量", "uc.quantity", "按擁有數量排序"),
        SortOption("star", "星級", "uc.star_level", "按卡片星級排序"),
        # 市場統計
        SortOption("price", "價格", "mc.current_market_sell_price", "按市場價格排序"),
        SortOption(
            "owner_count", "擁有人數", "ms.unique_owner_count", "按擁有人數排序"
        ),
        SortOption("wishlist_count", "許願人數", "ms.wishlist_count", "按許願人數排序"),
    ]

    # 圖鑑專用排序選項（排除用戶收藏相關字段）
    ENCYCLOPEDIA_SORT_OPTIONS = [
        SortOption(
            "creation_date", "創建日期", "mc.creation_date", "按卡片創建日期排序"
        ),
        SortOption("rarity", "稀有度", "mc.rarity", "按卡片稀有度排序"),
        SortOption("name", "名稱", "mc.name", "按卡片名稱排序"),
        SortOption("series", "系列", "mc.series", "按卡片系列排序"),
        SortOption("card_id", "卡片ID", "mc.card_id", "按卡片ID排序"),
        SortOption("star", "星級", "hs.star_level", "按最高星級排序"),
        SortOption("price", "價格", "mc.current_market_sell_price", "按市場價格排序"),
        SortOption(
            "owner_count", "擁有人數", "ms.unique_owner_count", "按擁有人數排序"
        ),
        SortOption("wishlist_count", "許願人數", "ms.wishlist_count", "按許願人數排序"),
    ]

    # 統一的排序順序選項
    SORT_ORDER_OPTIONS = [
        SortOption("desc", "降序", "", "Z -> A, 9 -> 1, or newest first"),
        SortOption("asc", "升序", "", "A -> Z, 1 -> 9, or oldest first"),
    ]

    # 文本字段集合（用於特殊的NULL處理）
    TEXT_FIELDS = {"mc.name", "mc.series"}

    @classmethod
    def get_db_field_map(cls, use_encyclopedia: bool = False) -> Dict[str, str]:
        """獲取排序選項到數據庫字段的映射"""
        options = (
            cls.ENCYCLOPEDIA_SORT_OPTIONS if use_encyclopedia else cls.SORT_OPTIONS
        )
        return {option.value: option.db_field for option in options}

    @classmethod
    def get_sort_option(cls, value: str, use_encyclopedia: bool = False) -> SortOption:
        """根據值獲取排序選項"""
        options = (
            cls.ENCYCLOPEDIA_SORT_OPTIONS if use_encyclopedia else cls.SORT_OPTIONS
        )
        for option in options:
            if option.value == value:
                return option
        raise ValueError(f"未知的排序選項: {value}")

    @classmethod
    def get_valid_sort_values(cls, use_encyclopedia: bool = False) -> List[str]:
        """獲取所有有效的排序選項值"""
        options = (
            cls.ENCYCLOPEDIA_SORT_OPTIONS if use_encyclopedia else cls.SORT_OPTIONS
        )
        return [option.value for option in options]

    @classmethod
    def get_valid_sort_orders(cls) -> List[str]:
        """獲取所有有效的排序順序值"""
        return [option.value for option in cls.SORT_ORDER_OPTIONS]


class SortingValidator:
    """統一的排序參數驗證器

    提供統一的排序參數驗證和標準化邏輯
    """

    @staticmethod
    def validate_sort_params(
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = None,
        use_encyclopedia: bool = False,
    ) -> Tuple[str, str]:
        """驗證和標準化排序參數

        Args:
            sort_by: 排序字段
            sort_order: 排序順序
            use_encyclopedia: 是否使用圖鑑專用選項

        Returns:
            Tuple[str, str]: 驗證後的 (sort_by, sort_order)
        """
        # 標準化排序字段
        if not sort_by or sort_by not in SortingConfig.get_valid_sort_values(
            use_encyclopedia
        ):
            sort_by = SortingConfig.DEFAULT_SORT_BY

        # 標準化排序順序（統一使用小寫）
        if sort_order:
            sort_order = sort_order.lower()
        if sort_order not in SortingConfig.get_valid_sort_orders():
            sort_order = SortingConfig.DEFAULT_SORT_ORDER

        return sort_by, sort_order

    @staticmethod
    def validate_pagination_params(
        page: int = 1, sort_by: Optional[str] = None, sort_order: Optional[str] = None
    ) -> Tuple[int, str, str]:
        """驗證分頁和排序參數

        Args:
            page: 頁碼
            sort_by: 排序字段
            sort_order: 排序順序

        Returns:
            Tuple[int, str, str]: 驗證後的 (page, sort_by, sort_order)
        """
        # 驗證頁碼
        page = max(1, int(page))

        # 驗證排序參數
        sort_by, sort_order = SortingValidator.validate_sort_params(sort_by, sort_order)

        return page, sort_by, sort_order
