# rename_existing_images.py
import asyncio
import os
import re
import sys

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# --- 配置部分 ---
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)

IMAGE_BASE_PATH = "downloaded_gacha_master_cards"
CARD_TABLE_NAME = "gacha_master_cards"

RARITY_MAP = {1: "C", 2: "R", 3: "SR", 4: "SSR", 5: "UR", 6: "LR", 7: "EX"}
DEFAULT_RARITY_NAME = "UnknownRarity"


def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其适合作为文件名的一部分（最终统一标准）"""
    if not text:
        return "unknown"

    # 移除换行符
    text = text.replace("\r", "").replace("\n", "")

    # 统一处理特殊替换规则
    text = text.replace("//", "__")
    text = text.replace(", ", ",_")
    # 将冒号替换为下划线，而不是可能包含冒号的'__'
    text = text.replace(":", "_")

    # 将所有空格替换为下划线
    text = text.replace(" ", "_")

    # 移除其他不合法字符
    text = re.sub(r'[\\/*?"<>|]', "", text)

    # 限制长度
    return text[:max_length]


async def get_all_cards(conn):
    """从数据库获取所有卡片信息"""
    print("[DB] 正在从数据库获取所有卡片信息...")
    query = f"SELECT card_id, name, series, rarity, image_url FROM {CARD_TABLE_NAME};"
    rows = await conn.fetch(query)
    print(f"[DB] 成功获取 {len(rows)} 条卡片记录。")
    return {row["card_id"]: dict(row) for row in rows}


def _process_file(old_filename, rarity_dir, cards_map):
    """處理單一檔案的重命名邏輯。"""
    try:
        match = re.match(r"^(\d+)_", old_filename)
        if not match:
            print(f"    [!] 無法從檔名解析 card_id，跳過: {old_filename}")
            return "skipped"

        card_id = int(match.group(1))
        card_data = cards_map.get(card_id)

        if not card_data:
            print(
                f"    [!] 在資料庫中找不到 card_id={card_id} 的記錄，跳過檔案: {old_filename}"
            )
            return "skipped"

        _, extension = os.path.splitext(old_filename)
        name_part = sanitize_filename_part(card_data["name"] or card_data["series"])
        new_filename = f"{card_id}_{name_part}{extension}"

        if old_filename == new_filename:
            return "skipped"

        old_filepath = os.path.join(rarity_dir, old_filename)
        new_filepath = os.path.join(rarity_dir, new_filename)

        if os.path.exists(new_filepath):
            print(f"    [!] 目標檔案已存在，跳過重命名以防覆蓋: {new_filename}")
            return "skipped"

        os.rename(old_filepath, new_filepath)
        print(f"    [+] 重命名: {old_filename} -> {new_filename}")
        return "renamed"
    except Exception as e:
        print(f"    [-] 處理檔案 {old_filename} 時發生錯誤: {e}")
        return "error"


async def main():
    """主執行函數。"""
    print("--- 開始執行現有圖片檔案重命名腳本 ---")
    stats = {"renamed": 0, "skipped": 0, "error": 0}

    await setup_connections()
    try:
        pool = get_pool()
        if not pool:
            print("[DB-Error] 資料庫連接池初始化失敗。")
            return

        async with pool.acquire() as conn:
            print("[DB] 成功從連接池獲取連接。")
            cards_map = await get_all_cards(conn)

            if not os.path.isdir(IMAGE_BASE_PATH):
                print(f"[Error] 圖片基礎目錄 '{IMAGE_BASE_PATH}' 不存在。")
                return

            print(f"[*] 開始掃描目錄: {os.path.abspath(IMAGE_BASE_PATH)}")
            for rarity_name in os.listdir(IMAGE_BASE_PATH):
                rarity_dir = os.path.join(IMAGE_BASE_PATH, rarity_name)
                if not os.path.isdir(rarity_dir):
                    continue

                print(f"  [*] 正在處理稀有度目錄: {rarity_name}")
                for filename in os.listdir(rarity_dir):
                    result = _process_file(filename, rarity_dir, cards_map)
                    stats[result] += 1

    except Exception as e:
        print(f"[Error] 發生意外錯誤: {e}")
        import traceback

        traceback.print_exc()
    finally:
        await close_connections()
        print("[DB] 資料庫連接已關閉。")

    print("\n--- 腳本執行完畢 ---")
    print(f"成功重命名檔案數: {stats['renamed']}")
    print(f"跳過檔案數: {stats['skipped']}")
    print(f"發生錯誤的檔案數: {stats['error']}")
    if stats["renamed"] > 0:
        print(
            "\n重要提示：請重新運行下載腳本 "
            "`pre_download_master_card_images.py` "
            "以下載任何可能因命名錯誤而失敗的圖片。"
        )


if __name__ == "__main__":
    if os.name == "nt":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
