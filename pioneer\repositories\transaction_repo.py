"""
Pioneer System - Transaction Repository
處理跨越多個領域的原子性事務操作
"""

from gacha.repositories._base_repo import execute_query, fetch_one, get_pool
from pioneer.exceptions import PioneerDatabaseError
from utils.logger import logger

from .facility_repo import add_facility_slot_item
from .warehouse_repo import add_warehouse_item, consume_warehouse_item

# ========================================
# 跨領域事務
# ========================================


async def move_item_to_facility_slot(
    user_id: int,
    item_id: str,
    quantity: int,
    facility_id: int,
    slot_type: str,
    slot_index: int,
) -> bool:
    """
    原子性地將物品從倉庫移動到設施槽位。
    """
    async with get_pool().acquire() as connection:
        async with connection.transaction():
            try:
                # 1. 從倉庫消耗物品
                consumed = await consume_warehouse_item(
                    user_id, item_id, quantity, connection=connection
                )
                if not consumed:
                    # 如果消耗失敗（例如數量不足），則整個操作失敗
                    # 事務會自動回滾，所以我們只需要返回 False
                    return False

                # 2. 添加物品到設施槽位
                await add_facility_slot_item(
                    facility_id,
                    slot_type,
                    slot_index,
                    item_id,
                    quantity,
                    connection=connection,
                )

                return True
            except Exception as e:
                logger.error("移動物品到設施槽位失敗，事務已回滾: %s", e)
                # 異常會由 async with connection.transaction() 自動回滾
                raise PioneerDatabaseError(
                    "move_item_to_facility_slot", str(e), e
                ) from e


async def transfer_item_from_slot_to_warehouse(
    user_id: int, facility_id: int, slot_type: str, slot_index: int
) -> bool:
    """
    原子性地將設施槽位中的所有物品移動到倉庫。
    """
    async with get_pool().acquire() as connection:
        async with connection.transaction():
            try:
                # 1. 獲取槽位信息
                slot_query = "SELECT * FROM pioneer_facility_states WHERE facility_id = $1 AND slot_type = $2 AND slot_index = $3"
                slot = await fetch_one(
                    slot_query,
                    (facility_id, slot_type, slot_index),
                    connection=connection,
                )

                if not slot or not slot["item_id"] or slot["quantity"] <= 0:
                    return False  # 槽位為空或無物品，無需操作

                item_id = slot["item_id"]
                quantity = slot["quantity"]

                # 2. 清空設施槽位 (或消耗物品)
                # 為了簡單起見，我們直接將其數量和物品ID都重置
                clear_slot_query = """
                    UPDATE pioneer_facility_states 
                    SET item_id = NULL, quantity = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE facility_id = $1 AND slot_type = $2 AND slot_index = $3
                """
                await execute_query(
                    clear_slot_query,
                    (facility_id, slot_type, slot_index),
                    connection=connection,
                )

                # 3. 添加物品到倉庫
                await add_warehouse_item(
                    user_id, item_id, quantity, connection=connection
                )

                return True
            except Exception as e:
                logger.error("從槽位移動物品到倉庫失敗，事務已回滾: %s", e)
                raise PioneerDatabaseError(
                    "transfer_from_slot_to_warehouse", str(e), e
                ) from e
