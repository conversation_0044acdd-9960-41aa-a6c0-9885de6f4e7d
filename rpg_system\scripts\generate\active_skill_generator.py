"""
主動技能生成器

專門用於生成主動技能的生成器，繼承自基礎技能生成器。
"""

import os
import random
import sys
from typing import Any, Dict, Optional, Tuple

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

from utils.logger import logger

from .error_handler import error_handler
from .skill_generator_base import SkillGeneratorBase


class ActiveSkillGenerator(SkillGeneratorBase):
    """主動技能生成器"""

    def __init__(self):
        """初始化主動技能生成器"""
        super().__init__()
        # 初始化共享的輔助方法實例
        from .active_skill_helpers import ActiveSkillHelpers

        self.helpers = ActiveSkillHelpers(self)

    def _generate_name_by_template(
        self, template_name: str, rarity: int, variant_type: str = "medium_effect"
    ) -> str:
        """根據效果模板名稱和變體類型生成匹配的技能名稱

        Args:
            template_name: 模板名稱
            rarity: 稀有度等級
            variant_type: 變體類型 (small_effect, medium_effect, large_effect)

        Returns:
            匹配的技能名稱
        """
        # 從 config/skill_name_library.json 獲取模板名稱映射
        template_names = self.name_library.get("template_names", {})
        rarity_prefixes = self.name_library.get("rarity_prefixes", {})
        fallback_names = self.name_library.get(
            "fallback_names", ["能力強化", "屬性提升", "戰力增強"]
        )

        # 獲取模板配置
        template_config = template_names.get(template_name)

        if template_config and isinstance(template_config, dict):
            # 根據變體類型選擇名稱
            variant_names = template_config.get(variant_type)
            if variant_names:
                chosen_name = random.choice(variant_names)
            else:
                # 如果沒有對應變體，使用 medium_effect 作為默認
                medium_names = template_config.get("medium_effect", fallback_names)
                chosen_name = random.choice(medium_names)
        else:
            # 沒有找到模板配置，使用回退名稱
            chosen_name = random.choice(fallback_names)

        # 根據稀有度添加前綴
        rarity_str = str(rarity)
        if rarity_str in rarity_prefixes:
            prefixes = rarity_prefixes[rarity_str]
            chosen_name = f"{random.choice(prefixes)}{chosen_name}"

        return chosen_name

    @error_handler(
        default_return=lambda: ("unknown", "BASIC_PHYSICAL_DAMAGE"),
        log_message="選擇效果模板時出錯",
    )
    def select_effect_template(
        self, effect_category: str, rarity: int
    ) -> Tuple[str, str]:
        """選擇效果模板

        Args:
            effect_category: 效果分類
            rarity: 稀有度等級

        Returns:
            (模板鍵, 模板名稱) 的元組
        """
        return self.template_processor.select_template(effect_category, rarity)

    @error_handler(default_return=lambda: ({}), log_message="創建效果定義時出錯")
    def create_effect_definition(
        self,
        template_name: str,
        effect_category: str,
        rarity: int,
        variant_multiplier: float = 1.0,
    ) -> Dict[str, Any]:
        """創建效果定義 - 重構版，支持變體倍率

        Args:
            template_name: 模板名稱
            effect_category: 效果分類
            rarity: 稀有度等級
            variant_multiplier: 變體倍率（默認1.0，無變體）

        Returns:
            效果定義字典
        """
        return self.template_processor.create_effect_definition(
            template_name, effect_category, rarity, variant_multiplier
        )

    def _get_category_from_template_name(self, template_name: str) -> str:
        """從緩存中獲取模板分類

        Args:
            template_name: 模板名稱

        Returns:
            模板分類
        """
        return self.get_template_category(template_name)

    def generate_ultra_description(
        self,
        name: str,
        template_key: str,
        effect_category: str,
        rarity: int,
        primary_effect_def: Optional[Dict[str, Any]] = None,
        trigger_desc: str = "",
    ) -> str:
        """生成超級描述

        Args:
            name: 技能名稱
            template_key: 模板鍵
            effect_category: 效果分類
            rarity: 稀有度等級
            primary_effect_def: 主要效果定義
            trigger_desc: 觸發描述

        Returns:
            生成的技能描述
        """
        return self.description_generator.generate_description(
            name,
            template_key,
            effect_category,
            rarity,
            primary_effect_def,
            trigger_desc,
        )

    @error_handler(
        default_return=lambda rarity=1: {
            "name": f"ErrorSkill_R{rarity}_{random.randint(100, 999)}",
            "description_template": "技能生成錯誤，請檢查日誌。",
            "skill_rarity": rarity,
            "max_level": 1,
            "target_type": "ENEMY_SINGLE",
            "base_mp_cost": 10,
            "mp_cost_per_level": 0,
            "base_cooldown_turns": 5,
            "cooldown_reduction_per_level": 0,
            "base_effect_definitions": [],
            "xp_gain_on_sacrifice": 1,
            "xp_to_next_level_config": {"base_xp": 100, "multiplier": 2},
            "tags": ["ERROR", "ACTIVE"],
        },
        log_message="創建技能配置時出錯",
    )
    def generate_skill_config(self, rarity: int) -> Dict[str, Any]:
        """創建主動技能配置，使用新的變體系統

        Args:
            rarity: 稀有度等級

        Returns:
            主動技能配置字典
        """
        # 直接選擇模板，不再使用分類
        _, template_name = self.select_effect_template(
            "", rarity
        )  # category 參數已不使用

        # 從模板名稱推導分類（僅用於生成描述和目標類型）
        effect_category = self._get_category_from_template_name(template_name)

        # 使用共享的變體系統生成技能（不傳入名稱，讓系統內部根據最終模板生成）
        return self.helpers.generate_skill_with_variants(
            template_name, effect_category, rarity, None
        )

    def generate_all_skills(
        self, skills_per_rarity: int = 5
    ) -> Dict[str, Dict[str, Any]]:
        """生成所有稀有度的技能配置

        Args:
            skills_per_rarity: 每個稀有度生成的技能數量

        Returns:
            所有技能配置字典
        """
        active_skills = {}
        total_skills = 0

        # 添加固定的基礎攻擊技能
        basic_attack = {
            "name": "基礎攻擊",
            "description_template": "進行基礎攻擊，造成 (1.0 + skill_level * 0.1) 物理傷害",
            "skill_rarity": 1,
            "max_level": 10,
            "target_type": "ENEMY_SINGLE",
            "base_mp_cost": 0,
            "mp_cost_per_level": 0,
            "base_cooldown_turns": 0,
            "cooldown_reduction_per_level": 0,
            "base_effect_definitions": [
                {
                    "effect_template": "BASIC_PHYSICAL_DAMAGE",
                    "multiplier": "(1.0 + skill_level * 0.1)",
                    "can_crit": True,
                }
            ],
            "xp_gain_on_sacrifice": 5,
            "xp_to_next_level_config": {"base_xp": 30, "multiplier": 1.1},
            "tags": ["DAMAGE", "ACTIVE", "BASIC"],
        }

        # 初始化rarity_1存儲區
        active_skills["rarity_1"] = {}

        # 添加基礎攻擊
        basic_attack_id = self.helpers.generate_descriptive_skill_id(basic_attack)
        active_skills["rarity_1"][basic_attack_id] = basic_attack
        total_skills += 1

        # 生成其他隨機技能
        for rarity in range(1, 8):
            rarity_bucket = f"rarity_{rarity}"
            if rarity_bucket not in active_skills:
                active_skills[rarity_bucket] = {}
            for _ in range(skills_per_rarity):
                max_attempts = 20  # 每個技能最多嘗試20次
                skill_generated = False

                for _ in range(max_attempts):
                    # 創建技能配置
                    skill_config = self.generate_skill_config(rarity)

                    # 使用描述性ID
                    skill_id = self.helpers.generate_descriptive_skill_id(skill_config)

                    # 檢查是否已存在相同ID
                    if skill_id not in active_skills[rarity_bucket]:
                        active_skills[rarity_bucket][skill_id] = skill_config
                        total_skills += 1
                        skill_generated = True
                        break
                    # 技能ID重複，繼續嘗試生成

                # 如果所有嘗試都失敗，使用序號後綴
                if not skill_generated:
                    logger.warning("無法生成唯一技能ID，使用序號後綴")
                    skill_config = self.generate_skill_config(rarity)
                    base_skill_id = self.helpers.generate_descriptive_skill_id(
                        skill_config
                    )
                    counter = 1
                    skill_id = f"{base_skill_id}_{counter}"
                    while skill_id in active_skills[rarity_bucket]:
                        counter += 1
                        skill_id = f"{base_skill_id}_{counter}"

                    active_skills[rarity_bucket][skill_id] = skill_config
                    total_skills += 1

        logger.info("總共生成了 %s 個主動技能。", total_skills)
        return {"active_skills": active_skills}

    def save_skills_to_files(self, all_skills: Dict[str, Dict[str, Any]]):
        """保存所有技能到配置文件

        Args:
            all_skills: 所有技能配置字典
        """
        import json

        try:
            os.makedirs(self.output_dir, exist_ok=True)
            output_path = os.path.join(self.output_dir, "active_skills.json")
            logger.info("正在保存主動技能到: %s", output_path)

            # 扁平化技能结构，去掉嵌套层级
            flat_skills = {}
            for _, skills in all_skills["active_skills"].items():
                flat_skills.update(skills)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(flat_skills, f, ensure_ascii=False, indent=2)

            logger.info("成功保存主動技能到 %s", output_path)
            print(f"✅ 所有技能已保存到：{output_path}")

            # 输出一些技能ID示例，方便验证
            print("\n技能ID示例:")
            sample_ids = list(flat_skills.keys())[:5]  # 取前5个作为示例
            for sample_id in sample_ids:
                print(f"- {sample_id}")

        except Exception as e:
            logger.error("保存技能文件時出錯: %s", e, exc_info=True)
            print(f"❌ 保存技能失敗: {e}")
            try:
                # 尝试保存到备用位置
                fallback_path = os.path.join(
                    os.path.dirname(os.path.abspath(__file__)),
                    "active_skills_fallback.json",
                )
                with open(fallback_path, "w", encoding="utf-8") as f:
                    # 确保我们有一个可用的flat_skills变量
                    flat_skills = {}
                    if isinstance(all_skills, dict) and "active_skills" in all_skills:
                        for _, skills in all_skills["active_skills"].items():
                            flat_skills.update(skills)
                    else:
                        flat_skills = (
                            all_skills  # 如果结构不是预期的，直接使用all_skills
                        )

                    json.dump(flat_skills, f, ensure_ascii=False, indent=2)
                logger.info("已保存備用文件到: %s", fallback_path)
                print(f"✅ 已保存備用文件到：{fallback_path}")
            except Exception as fallback_error:
                logger.error("保存備用文件也失敗: %s", fallback_error, exc_info=True)
                print("❌ 保存備用文件也失敗")
