[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ["py39", "py310", "py311", "py312", "py313"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.ruff]
line-length = 88
target-version = "py39"
exclude = [
    ".git",
    ".ruff_cache",
    ".venv",
    "__pypackages__",
    "_build",
    "build",
    "dist",
]

[tool.ruff.lint]
select = ["E", "F", "N", "C", "B", "I"]
ignore = ["E501", "C901"]
fixable = ["ALL"]
unfixable = []

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
strict_optional = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[tool.pylint.main]
load-plugins = "pylint_pydantic"

[tool.pyright]
useLibraryCodeForTypes = true
typeCheckingMode = "basic"
pythonVersion = "3.12"
strictListInference = true
strictDictionaryInference = true
strictSetInference = true
strictParameterNoneValue = true
reportMissingImports = "error"
reportUnusedImport = "error"
reportUnusedClass = "error"
reportUnusedFunction = "error"
reportUnusedVariable = "error"
reportUnusedExpression = "error"
reportGeneralTypeIssues = "error"
reportDuplicateImport = "error"
reportUntypedFunctionDecorator = "error"
reportUnnecessaryTypeIgnoreComment = "warning"
exclude = ["rpg_system/**"]