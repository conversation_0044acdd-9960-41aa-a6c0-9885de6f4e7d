{"effect_templates_by_rarity": {"1": ["BASIC_DAMAGE", "BASIC_HEAL_FLAT", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF"], "2": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF"], "3": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "APPLY_REGEN_STATUS_WEAK", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "RESOURCE_ADVANTAGE_BLAST", "APPLY_STUN_STATUS", "APPLY_POISON_STATUS"], "4": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "EXECUTE_DAMAGE", "CONDITIONAL_DAMAGE_BOOST", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "BASIC_HEAL_PERCENT_MAX_HP", "APPLY_REGEN_STATUS_WEAK", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "RESOURCE_ADVANTAGE_BLAST", "CRITICAL_MASS_EXPLOSION", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS"], "5": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "EXECUTE_DAMAGE", "CONDITIONAL_DAMAGE_BOOST", "TRUE_DAMAGE", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "BASIC_HEAL_PERCENT_MAX_HP", "MISSING_HP_HEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "RESOURCE_ADVANTAGE_BLAST", "CRITICAL_MASS_EXPLOSION", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS"], "6": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "EXECUTE_DAMAGE", "CONDITIONAL_DAMAGE_BOOST", "TRUE_DAMAGE", "PERCENTAGE_HP_DAMAGE", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "BASIC_HEAL_PERCENT_MAX_HP", "MISSING_HP_HEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "APPLY_ALL_STATS_DEBUFF", "RESOURCE_ADVANTAGE_BLAST", "CRITICAL_MASS_EXPLOSION", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS"], "7": ["BASIC_DAMAGE", "MULTI_HIT_DAMAGE", "DRAIN_DAMAGE", "EXECUTE_DAMAGE", "CONDITIONAL_DAMAGE_BOOST", "TRUE_DAMAGE", "PERCENTAGE_HP_DAMAGE", "BASIC_HEAL_FLAT", "BASIC_HEAL_PERCENT_CASTER_ATK", "BASIC_HEAL_PERCENT_MAX_HP", "MISSING_HP_HEAL", "APPLY_REGEN_STATUS_WEAK", "APPLY_BASIC_SHIELD", "APPLY_MAGIC_SHIELD", "APPLY_PHYSICAL_BARRIER", "APPLY_SCALING_SHIELD", "APPLY_ATK_BOOST", "APPLY_DEF_BOOST", "APPLY_SPD_BOOST", "APPLY_HP_BOOST", "APPLY_MP_BOOST", "APPLY_CRIT_BOOST", "APPLY_ACCURACY_BOOST", "APPLY_EVASION_BOOST", "APPLY_ALL_STATS_BOOST", "APPLY_ATK_DEBUFF", "APPLY_DEF_DEBUFF", "APPLY_SPD_DEBUFF", "APPLY_ACCURACY_DEBUFF", "APPLY_EVASION_DEBUFF", "APPLY_ALL_STATS_DEBUFF", "RESOURCE_ADVANTAGE_BLAST", "CRITICAL_MASS_EXPLOSION", "APPLY_STUN_STATUS", "APPLY_FREEZE_STATUS", "APPLY_SILENCE_STATUS", "APPLY_POISON_STATUS", "APPLY_BURN_STATUS", "DISPEL_ALL_DEBUFFS", "SHIELD_REMOVAL", "TRANSFER_MP_EFFECT", "LOSE_MP_EFFECT"]}, "skill_generation": {"skill_type_compatibility": {"basic_attack": ["ATTACK", "BALANCED", "DEFENSE", "SUPPORT"], "power_strike": ["ATTACK", "BALANCED"], "heal": ["SUPPORT", "DEFENSE", "BALANCED"], "berserker_rage": ["ATTACK"], "critical_strike": ["ATTACK", "BALANCED"], "divine_blessing": ["SUPPORT", "DEFENSE"], "ultimate_strike": ["ATTACK"], "world_breaker": ["ATTACK"], "PASSIVE_BASIC_BOOST": ["ATTACK", "BALANCED", "DEFENSE", "SUPPORT"], "PASSIVE_REGEN": ["ATTACK", "BALANCED", "DEFENSE", "SUPPORT"], "PASSIVE_COUNTER_ATTACK": ["ATTACK", "DEFENSE", "BALANCED"], "PASSIVE_CRIT_BOOST": ["ATTACK", "BALANCED"]}, "skill_category_to_card_type_compatibility": {"DAMAGE": ["ATTACK", "BALANCED"], "HEAL": ["SUPPORT", "DEFENSE", "BALANCED"], "BUFF": ["DEFENSE", "SUPPORT", "BALANCED"], "DEBUFF": ["ATTACK", "SUPPORT"], "CONTROL": ["SUPPORT", "BALANCED"], "SPECIAL": ["ATTACK", "DEFENSE", "SUPPORT", "BALANCED"]}, "preinstalled_skill_thresholds": {"cpr_thresholds": [{"min_cpr": 0, "max_cpr": 39, "max_preinstalled": 1}, {"min_cpr": 40, "max_cpr": 59, "max_preinstalled": 1}, {"min_cpr": 60, "max_cpr": 79, "max_preinstalled": 2}, {"min_cpr": 80, "max_cpr": 100, "max_preinstalled": 3}]}, "innate_skill_chances": {"cpr_thresholds": [{"min_cpr": 0, "max_cpr": 19, "chance": 0.05}, {"min_cpr": 20, "max_cpr": 39, "chance": 0.2}, {"min_cpr": 40, "max_cpr": 59, "chance": 0.5}, {"min_cpr": 60, "max_cpr": 79, "chance": 0.8}, {"min_cpr": 80, "max_cpr": 100, "chance": 0.9}]}}, "passive_slot_generation": {"cpr_thresholds": [{"min_cpr": 0, "max_cpr": 19, "slots": 1}, {"min_cpr": 20, "max_cpr": 39, "slots": 1}, {"min_cpr": 40, "max_cpr": 59, "slots": 2}, {"min_cpr": 60, "max_cpr": 79, "slots": 3}, {"min_cpr": 80, "max_cpr": 100, "slots": 4}]}, "card_type_weights": {"base_weights": {"ATTACK": 0.4, "DEFENSE": 0.25, "SUPPORT": 0.25, "BALANCED": 0.1}, "cpr_adjustments": {"low_cpr": {"min_cpr": 0, "max_cpr": 39, "weights": {"ATTACK": 0.3, "DEFENSE": 0.2, "SUPPORT": 0.35, "BALANCED": 0.15}}, "mid_cpr": {"min_cpr": 40, "max_cpr": 59, "weights": {"ATTACK": 0.4, "DEFENSE": 0.25, "SUPPORT": 0.25, "BALANCED": 0.1}}, "high_cpr": {"min_cpr": 60, "max_cpr": 79, "weights": {"ATTACK": 0.45, "DEFENSE": 0.3, "SUPPORT": 0.2, "BALANCED": 0.05}}, "ultra_cpr": {"min_cpr": 80, "max_cpr": 100, "weights": {"ATTACK": 0.5, "DEFENSE": 0.35, "SUPPORT": 0.15, "BALANCED": 0.0}}}, "pool_type_modifiers": {"main": {"ATTACK": 1.0, "DEFENSE": 1.0, "SUPPORT": 1.0, "BALANCED": 1.0}, "special": {"ATTACK": 1.3, "DEFENSE": 0.8, "SUPPORT": 0.9, "BALANCED": 0.5}, "summer": {"ATTACK": 1.1, "DEFENSE": 0.95, "SUPPORT": 1.1, "BALANCED": 0.8}, "vd": {"ATTACK": 1.0, "DEFENSE": 0.9, "SUPPORT": 1.5, "BALANCED": 0.7}, "special_maid": {"ATTACK": 0.85, "DEFENSE": 1.05, "SUPPORT": 1.4, "BALANCED": 0.8}, "hololive": {"ATTACK": 1.15, "DEFENSE": 0.85, "SUPPORT": 1.2, "BALANCED": 0.8}, "ua": {"ATTACK": 1.15, "DEFENSE": 1.0, "SUPPORT": 0.8, "BALANCED": 0.9}, "ptcg": {"ATTACK": 1.2, "DEFENSE": 0.9, "SUPPORT": 0.9, "BALANCED": 0.9}}}, "cpr_calculation": {"base_cpr_by_rarity": {"1": 10, "2": 20, "3": 35, "4": 55, "5": 75, "6": 90, "7": 100}, "pool_type_modifiers": {"main": 1.0, "special": 1.15, "summer": 1.1, "vd": 1.12, "special_maid": 1.18, "hololive": 1.2, "ua": 1.15, "ptcg": 1.1}}, "rarity_conversion": {"1": "C", "2": "R", "3": "SR", "4": "SSR", "5": "UR", "6": "LR", "7": "EX"}}