{"BASIC_ATTACK": {"name": "基礎攻擊", "description_template": "進行基礎攻擊，造成 (1.0 + skill_level * 0.1) 物理傷害", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 0, "mp_cost_per_level": 0, "base_cooldown_turns": 0, "cooldown_reduction_per_level": 0, "base_effect_definitions": [{"effect_template": "BASIC_DAMAGE", "multiplier": "(1.0 + skill_level * 0.1)", "can_crit": true}], "xp_gain_on_sacrifice": 5, "xp_to_next_level_config": {"base_xp": 30, "multiplier": 1.1}, "tags": ["DAMAGE", "ACTIVE", "BASIC"]}, "ACTIVE_DAMAGE_MAGICAL_R1_LARGE_EFFECT": {"name": "魔力狂潮", "description_template": "施展魔力狂潮，造成 (2.09 + skill_level * 0.31) 魔法傷害", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.96, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.01, "base_effect_definitions": [{"effect_template": "BASIC_MAGICAL_DAMAGE", "multiplier": "(2.09 + skill_level * 0.31)", "can_crit": true}], "xp_gain_on_sacrifice": 8, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.1500000000000001}, "tags": ["DAMAGE", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DAMAGE_MAGICAL_R1_SMALL_EFFECT": {"name": "快速法術", "description_template": "施展快速法術，造成 (0.57 + skill_level * 0.08) 魔法傷害", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 8, "mp_cost_per_level": 0.42, "base_cooldown_turns": 1, "cooldown_reduction_per_level": 0.01, "base_effect_definitions": [{"effect_template": "BASIC_MAGICAL_DAMAGE", "multiplier": "(0.57 + skill_level * 0.08)", "can_crit": true}], "xp_gain_on_sacrifice": 8, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.1500000000000001}, "tags": ["DAMAGE", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DAMAGE_PHYSICAL_R1_SMALL_EFFECT": {"name": "短暫衝擊", "description_template": "施展短暫衝擊，造成 (0.62 + skill_level * 0.10) 物理傷害", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 6, "mp_cost_per_level": 0.42, "base_cooldown_turns": 1, "cooldown_reduction_per_level": 0.01, "base_effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": "(0.62 + skill_level * 0.10)", "can_crit": true}], "xp_gain_on_sacrifice": 8, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.1500000000000001}, "tags": ["DAMAGE", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DEBUFF_DEBUFF_R1_SMALL_EFFECT": {"name": "快速破防", "description_template": "對敵方施加快速破防，防禦力降低 (7.2 + skill_level * 1.26) / 100 * 100%，持續 (2 + floor(skill_level / 3)) 回合", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 8, "mp_cost_per_level": 0.42, "base_cooldown_turns": 1, "cooldown_reduction_per_level": 0.01, "base_effect_definitions": [{"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.67 + skill_level * 0.021))", "value_overrides": {"pdef": "-(7.2 + skill_level * 1.26) / 100", "mdef": "-(7.2 + skill_level * 1.26) / 100"}}], "xp_gain_on_sacrifice": 8, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.1500000000000001}, "tags": ["DEBUFF", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DEBUFF_DEBUFF_R1_LARGE_EFFECT": {"name": "護盾削弱·極", "description_template": "對敵方施加護盾削弱·極，防禦力降低 (21.6 + skill_level * 3.78) / 100 * 100%，持續 (4 + floor(skill_level / 3)) 回合", "skill_rarity": 1, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 19, "mp_cost_per_level": 0.96, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.01, "base_effect_definitions": [{"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.80 + skill_level * 0.018))", "value_overrides": {"pdef": "-(21.6 + skill_level * 3.78) / 100", "mdef": "-(21.6 + skill_level * 3.78) / 100"}}], "xp_gain_on_sacrifice": 8, "xp_to_next_level_config": {"base_xp": 50, "multiplier": 1.1500000000000001}, "tags": ["DEBUFF", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DEBUFF_DEBUFF_R2_MEDIUM_EFFECT_WITH_APPLY_WEAK_LIFESTEAL": {"name": "速度削弱", "description_template": "對敵方施加速度削弱，速度降低 (10.0 + skill_level * 1.60) / 100 * 100%，持續 (3 + floor(skill_level / 3)) 回合 並獲得微弱吸血效果", "skill_rarity": 2, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.7, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.02, "base_effect_definitions": [{"effect_template": "APPLY_SPD_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.88 + skill_level * 0.016))", "value_overrides": {"spd": "-(10.0 + skill_level * 1.60) / 100"}}, {"effect_template": "APPLY_WEAK_LIFESTEAL", "duration_turns": "(1 + floor(skill_level / 3))", "chance": "(min(1.0, 0.50 + skill_level * 0.021))"}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.2000000000000002}, "tags": ["DEBUFF", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_DAMAGE_PHYSICAL_R2_LARGE_EFFECT_WITH_APPLY_WEAK_LIFESTEAL": {"name": "猛烈斬擊", "description_template": "施展猛烈斬擊，造成 (2.14 + skill_level * 0.32) 物理傷害 並獲得微弱吸血效果", "skill_rarity": 2, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 18, "mp_cost_per_level": 1.12, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.02, "base_effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": "(2.14 + skill_level * 0.32)", "can_crit": true}, {"effect_template": "APPLY_WEAK_LIFESTEAL", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.68 + skill_level * 0.025))"}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.2000000000000002}, "tags": ["DAMAGE", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_HEAL_MAGICAL_R2_SMALL_EFFECT": {"name": "快速魔療", "description_template": "施展快速魔療，恢復 (0.44 + skill_level * 0.06) 魔法攻擊力的生命值", "skill_rarity": 2, "max_level": 10, "target_type": "ALLY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.49, "base_cooldown_turns": 1, "cooldown_reduction_per_level": 0.02, "base_effect_definitions": [{"effect_template": "BASIC_HEAL_PERCENT_CASTER_MATK", "value": "(0.44 + skill_level * 0.06)"}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.2000000000000002}, "tags": ["HEAL", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DAMAGE_MAGICAL_R2_LARGE_EFFECT_WITH_APPLY_WEAK_LIFESTEAL": {"name": "魔力狂潮", "description_template": "施展魔力狂潮，造成 (1.61 + skill_level * 0.31) 魔法傷害 並獲得微弱吸血效果", "skill_rarity": 2, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 18, "mp_cost_per_level": 1.12, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.02, "base_effect_definitions": [{"effect_template": "BASIC_MAGICAL_DAMAGE", "multiplier": "(1.61 + skill_level * 0.31)", "can_crit": true}, {"effect_template": "APPLY_WEAK_LIFESTEAL", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.77 + skill_level * 0.014))"}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.2000000000000002}, "tags": ["DAMAGE", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DAMAGE_PHYSICAL_R2_MEDIUM_EFFECT": {"name": "物理衝擊", "description_template": "施展物理衝擊，造成 (1.14 + skill_level * 0.17) 物理傷害", "skill_rarity": 2, "max_level": 10, "target_type": "ENEMY_SINGLE", "base_mp_cost": 11, "mp_cost_per_level": 0.7, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.02, "base_effect_definitions": [{"effect_template": "BASIC_PHYSICAL_DAMAGE", "multiplier": "(1.14 + skill_level * 0.17)", "can_crit": true}], "xp_gain_on_sacrifice": 11, "xp_to_next_level_config": {"base_xp": 70, "multiplier": 1.2000000000000002}, "tags": ["DAMAGE", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_CONTROL_STUN_R3_MEDIUM_EFFECT_WITH_APPLY_DEF_DEBUFF": {"name": "卓越麻痺效應", "description_template": "對敵方施展卓越麻痺效應，將其擊暈，無法行動，持續 (3 + floor(skill_level / 3)) 回合 並降低敵方防禦力", "skill_rarity": 3, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 24, "mp_cost_per_level": 0.8, "base_cooldown_turns": 6, "cooldown_reduction_per_level": 0.03, "base_effect_definitions": [{"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.86 + skill_level * 0.015))", "stack_count": 2}, {"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.53 + skill_level * 0.023))", "value_overrides": {"pdef": "-(14.0 + skill_level * 2.40) / 100", "mdef": "-(14.0 + skill_level * 2.40) / 100"}}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 90, "multiplier": 1.25}, "tags": ["CONTROL", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_DEBUFF_POISON_R3_MEDIUM_EFFECT_WITH_APPLY_DEF_DEBUFF": {"name": "卓越毒液注入", "description_template": "對敵方施加卓越毒液注入，使其陷入中毒狀態，每回合損失生命，持續 (3 + floor(skill_level / 3)) 回合 並降低敵方防禦力", "skill_rarity": 3, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.8, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.03, "base_effect_definitions": [{"effect_template": "APPLY_POISON_STATUS", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.76 + skill_level * 0.018))", "stack_count": 2}, {"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.55 + skill_level * 0.020))", "value_overrides": {"pdef": "-(14.0 + skill_level * 2.40) / 100", "mdef": "-(14.0 + skill_level * 2.40) / 100"}}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 90, "multiplier": 1.25}, "tags": ["DEBUFF", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_HEAL_MAGICAL_R3_MEDIUM_EFFECT": {"name": "卓越法術治癒", "description_template": "施展卓越法術治癒，恢復 (0.83 + skill_level * 0.09) 魔法攻擊力的生命值", "skill_rarity": 3, "max_level": 8, "target_type": "ALLY_SINGLE", "base_mp_cost": 17, "mp_cost_per_level": 0.8, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.03, "base_effect_definitions": [{"effect_template": "BASIC_HEAL_PERCENT_CASTER_MATK", "value": "(0.83 + skill_level * 0.09)"}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 90, "multiplier": 1.25}, "tags": ["HEAL", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_BUFF_BOOST_R3_SMALL_EFFECT": {"name": "卓越瞬間加速", "description_template": "獲得卓越瞬間加速效果，速度提升 (7.2 + skill_level * 1.08) / 100 * 100%，持續 (2 + floor(skill_level / 3)) 回合", "skill_rarity": 3, "max_level": 8, "target_type": "SELF", "base_mp_cost": 13, "mp_cost_per_level": 0.56, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.03, "base_effect_definitions": [{"effect_template": "APPLY_SPD_BOOST", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.71 + skill_level * 0.021))", "stack_count": 2, "value_overrides": {"spd": "(7.2 + skill_level * 1.08) / 100"}}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 90, "multiplier": 1.25}, "tags": ["BUFF", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_CONTROL_STUN_R3_MEDIUM_EFFECT_WITH_APPLY_ATK_DEBUFF": {"name": "精英昏迷術", "description_template": "對敵方施展精英昏迷術，將其擊暈，無法行動，持續 (3 + floor(skill_level / 3)) 回合 並降低敵方攻擊力", "skill_rarity": 3, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 25, "mp_cost_per_level": 0.8, "base_cooldown_turns": 5, "cooldown_reduction_per_level": 0.03, "base_effect_definitions": [{"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.61 + skill_level * 0.019))", "stack_count": 2}, {"effect_template": "APPLY_ATK_DEBUFF", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.88 + skill_level * 0.016))", "value_overrides": {"patk": "-(12.0 + skill_level * 2.10) / 100", "matk": "-(12.0 + skill_level * 2.10) / 100"}}], "xp_gain_on_sacrifice": 14, "xp_to_next_level_config": {"base_xp": 90, "multiplier": 1.25}, "tags": ["CONTROL", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_DAMAGE_CRITICAL_R4_LARGE_EFFECT_WITH_APPLY_DEF_DEBUFF": {"name": "強力攻擊總和·極", "description_template": "集中所有攻擊力發動強力攻擊總和·極，造成 (2.03 + skill_level * 0.30) 魔法爆炸傷害 並降低敵方防禦力", "skill_rarity": 4, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 25, "mp_cost_per_level": 1.44, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.04, "base_effect_definitions": [{"effect_template": "CRITICAL_MASS_EXPLOSION", "multiplier": "(2.03 + skill_level * 0.30)", "can_crit": true}, {"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.74 + skill_level * 0.016))", "stack_count": 2, "value_overrides": {"pdef": "-(16.0 + skill_level * 2.70) / 100", "mdef": "-(16.0 + skill_level * 2.70) / 100"}}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 110, "multiplier": 1.3}, "tags": ["DAMAGE", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DEBUFF_BURN_R4_LARGE_EFFECT": {"name": "強力燃燒效果·極", "description_template": "對敵方施加強力燃燒效果·極，使其陷入燃燒狀態，每回合損失生命，持續 (8 + floor(skill_level / 3)) 回合", "skill_rarity": 4, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 26, "mp_cost_per_level": 1.44, "base_cooldown_turns": 6, "cooldown_reduction_per_level": 0.04, "base_effect_definitions": [{"effect_template": "APPLY_BURN_STATUS", "duration_turns": "(8 + floor(skill_level / 3))", "chance": "(min(1.0, 0.87 + skill_level * 0.022))", "stack_count": 2}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 110, "multiplier": 1.3}, "tags": ["DEBUFF", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_HEAL_HEAL_R4_LARGE_EFFECT_WITH_APPLY_ATK_BOOST": {"name": "精英生命狂復", "description_template": "施展精英生命狂復，恢復 (227 + skill_level * 20) 點固定生命值 並提升攻擊力", "skill_rarity": 4, "max_level": 8, "target_type": "ALLY_SINGLE", "base_mp_cost": 25, "mp_cost_per_level": 1.44, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.04, "base_effect_definitions": [{"effect_template": "BASIC_HEAL_FLAT", "value": "(227 + skill_level * 20)"}, {"effect_template": "APPLY_ATK_BOOST", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.85 + skill_level * 0.019))", "stack_count": 2, "value_overrides": {"patk": "(14.0 + skill_level * 2.40) / 100", "matk": "(14.0 + skill_level * 2.40) / 100"}}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 110, "multiplier": 1.3}, "tags": ["HEAL", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DAMAGE_RESOURCE_R4_LARGE_EFFECT_WITH_APPLY_ATK_DEBUFF": {"name": "卓越資優爆發", "description_template": "利用資源優勢釋放卓越資優爆發，造成 (1.61 + skill_level * 0.26) 真實傷害 並降低敵方攻擊力", "skill_rarity": 4, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 24, "mp_cost_per_level": 1.44, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.04, "base_effect_definitions": [{"effect_template": "RESOURCE_ADVANTAGE_BLAST", "multiplier": "(1.61 + skill_level * 0.26)", "can_crit": true}, {"effect_template": "APPLY_ATK_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.70 + skill_level * 0.020))", "stack_count": 2, "value_overrides": {"patk": "-(14.0 + skill_level * 2.40) / 100", "matk": "-(14.0 + skill_level * 2.40) / 100"}}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 110, "multiplier": 1.3}, "tags": ["DAMAGE", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DEBUFF_DEBUFF_R4_MEDIUM_EFFECT_WITH_APPLY_SPD_DEBUFF": {"name": "高級力量衰減", "description_template": "對敵方施加高級力量衰減，攻擊力降低 (16.0 + skill_level * 2.70) / 100 * 100%，持續 (3 + floor(skill_level / 3)) 回合 並降低敵方速度", "skill_rarity": 4, "max_level": 8, "target_type": "ENEMY_SINGLE", "base_mp_cost": 18, "mp_cost_per_level": 0.9, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.04, "base_effect_definitions": [{"effect_template": "APPLY_ATK_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.70 + skill_level * 0.017))", "stack_count": 2, "value_overrides": {"patk": "-(16.0 + skill_level * 2.70) / 100", "matk": "-(16.0 + skill_level * 2.70) / 100"}}, {"effect_template": "APPLY_SPD_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.81 + skill_level * 0.021))", "stack_count": 2, "value_overrides": {"spd": "-(12.0 + skill_level * 1.80) / 100"}}], "xp_gain_on_sacrifice": 17, "xp_to_next_level_config": {"base_xp": 110, "multiplier": 1.3}, "tags": ["DEBUFF", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_DAMAGE_DAMAGE_R5_SMALL_EFFECT_WITH_APPLY_STUN_STATUS": {"name": "神聖瞬間斬殺", "description_template": "使用神聖瞬間斬殺，對低血量敵人造成 (0.68 + skill_level * 0.09) 斬殺傷害 並擊暈敵方", "skill_rarity": 5, "max_level": 6, "target_type": "ENEMY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.7, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.05, "base_effect_definitions": [{"effect_template": "EXECUTE_DAMAGE", "multiplier": "(0.68 + skill_level * 0.09)", "can_crit": true}, {"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.76 + skill_level * 0.015))", "stack_count": 2}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.35}, "tags": ["DAMAGE", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DAMAGE_PHYSICAL_R5_SMALL_EFFECT_WITH_APPLY_SILENCE_STATUS": {"name": "傳說輕度吸收", "description_template": "發動傳說輕度吸收，造成 (0.55 + skill_level * 0.08) 物理傷害並吸取50%傷害作為生命值 並沉默敵方", "skill_rarity": 5, "max_level": 6, "target_type": "ENEMY_SINGLE", "base_mp_cost": 11, "mp_cost_per_level": 0.7, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.05, "base_effect_definitions": [{"effect_template": "DRAIN_PHYSICAL", "multiplier": "(0.55 + skill_level * 0.08)", "can_crit": true}, {"effect_template": "APPLY_SILENCE_STATUS", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.85 + skill_level * 0.026))", "stack_count": 2}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.35}, "tags": ["DAMAGE", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_CONTROL_FREEZE_R5_SMALL_EFFECT_WITH_APPLY_STUN_STATUS": {"name": "傳說輕度寒冰", "description_template": "對敵方施展傳說輕度寒冰，將其冰凍，無法行動，持續 (2 + floor(skill_level / 3)) 回合 並擊暈敵方", "skill_rarity": 5, "max_level": 6, "target_type": "ENEMY_SINGLE", "base_mp_cost": 19, "mp_cost_per_level": 0.7, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.05, "base_effect_definitions": [{"effect_template": "APPLY_FREEZE_STATUS", "duration_turns": "(2 + floor(skill_level / 3))", "chance": "(min(1.0, 0.73 + skill_level * 0.017))", "stack_count": 2}, {"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.88 + skill_level * 0.020))", "stack_count": 2}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.35}, "tags": ["CONTROL", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_BUFF_BOOST_R5_LARGE_EFFECT_WITH_APPLY_EVASION_BOOST": {"name": "傳說極速狂奔", "description_template": "獲得傳說極速狂奔效果，速度提升 (28.8 + skill_level * 3.96) / 100 * 100%，持續 (7 + floor(skill_level / 3)) 回合 並提升閃避率", "skill_rarity": 5, "max_level": 6, "target_type": "SELF", "base_mp_cost": 40, "mp_cost_per_level": 1.6, "base_cooldown_turns": 8, "cooldown_reduction_per_level": 0.05, "base_effect_definitions": [{"effect_template": "APPLY_SPD_BOOST", "duration_turns": "(7 + floor(skill_level / 3))", "chance": "(min(1.0, 0.93 + skill_level * 0.017))", "stack_count": 2, "value_overrides": {"spd": "(28.8 + skill_level * 3.96) / 100"}}, {"effect_template": "APPLY_EVASION_BOOST", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.74 + skill_level * 0.021))", "stack_count": 2, "value_overrides": {"evasion": "(16.0 + skill_level * 2.70) / 100"}}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.35}, "tags": ["BUFF", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_DAMAGE_CRITICAL_R5_SMALL_EFFECT_WITH_APPLY_POISON_STATUS": {"name": "究極輕度火集", "description_template": "集中所有攻擊力發動究極輕度火集，造成 (0.68 + skill_level * 0.09) 魔法爆炸傷害 並使敵方中毒", "skill_rarity": 5, "max_level": 6, "target_type": "ENEMY_SINGLE", "base_mp_cost": 13, "mp_cost_per_level": 0.7, "base_cooldown_turns": 2, "cooldown_reduction_per_level": 0.05, "base_effect_definitions": [{"effect_template": "CRITICAL_MASS_EXPLOSION", "multiplier": "(0.68 + skill_level * 0.09)", "can_crit": true}, {"effect_template": "APPLY_POISON_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.76 + skill_level * 0.018))", "stack_count": 2}], "xp_gain_on_sacrifice": 20, "xp_to_next_level_config": {"base_xp": 130, "multiplier": 1.35}, "tags": ["DAMAGE", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_HEAL_HEAL_R6_LARGE_EFFECT_WITH_APPLY_SPD_BOOST": {"name": "至尊救援風暴", "description_template": "使用至尊救援風暴，治癒 (1.96 + skill_level * 0.18) 已損失生命值 並提升速度", "skill_rarity": 6, "max_level": 5, "target_type": "ALLY_SINGLE", "base_mp_cost": 34, "mp_cost_per_level": 1.76, "base_cooldown_turns": 6, "cooldown_reduction_per_level": 0.06, "base_effect_definitions": [{"effect_template": "MISSING_HP_HEAL", "value": "(1.96 + skill_level * 0.18)"}, {"effect_template": "APPLY_SPD_BOOST", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.76 + skill_level * 0.019))", "stack_count": 3, "value_overrides": {"spd": "(16.0 + skill_level * 2.20) / 100"}}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.4000000000000001}, "tags": ["HEAL", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_CONTROL_FREEZE_R6_SMALL_EFFECT_WITH_APPLY_ACCURACY_DEBUFF+APPLY_ALL_STATS_DEBUFF": {"name": "神聖瞬間凍結", "description_template": "對敵方施展神聖瞬間凍結，將其冰凍，無法行動，持續 (3 + floor(skill_level / 3)) 回合 並降低敵方命中率 並降低敵方全屬性", "skill_rarity": 6, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 22, "mp_cost_per_level": 0.77, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.06, "base_effect_definitions": [{"effect_template": "APPLY_FREEZE_STATUS", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.89 + skill_level * 0.016))", "stack_count": 3}, {"effect_template": "APPLY_ACCURACY_DEBUFF", "duration_turns": "(4 + floor(skill_level / 3))", "chance": "(min(1.0, 0.77 + skill_level * 0.016))", "stack_count": 2, "value_overrides": {"accuracy": "-(18.0 + skill_level * 3.00) / 100"}}, {"effect_template": "APPLY_ALL_STATS_DEBUFF", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.73 + skill_level * 0.018))", "stack_count": 2, "value_overrides": {"patk": "-(18.0 + skill_level * 3.00) / 100", "matk": "-(18.0 + skill_level * 3.00) / 100", "pdef": "-(20.0 + skill_level * 3.30) / 100", "mdef": "-(20.0 + skill_level * 3.30) / 100", "spd": "-(16.0 + skill_level * 2.20) / 100"}}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.4000000000000001}, "tags": ["CONTROL", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_CONTROL_SILENCE_R6_SMALL_EFFECT": {"name": "神聖快速沉默", "description_template": "對敵方施展神聖快速沉默，使其沉默，無法施法，持續 (3 + floor(skill_level / 3)) 回合", "skill_rarity": 6, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 19, "mp_cost_per_level": 0.77, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.06, "base_effect_definitions": [{"effect_template": "APPLY_SILENCE_STATUS", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.88 + skill_level * 0.023))", "stack_count": 4}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.4000000000000001}, "tags": ["CONTROL", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DAMAGE_RESOURCE_R6_MEDIUM_EFFECT": {"name": "神聖資源壓制", "description_template": "利用資源優勢釋放神聖資源壓制，造成 (0.97 + skill_level * 0.18) 真實傷害", "skill_rarity": 6, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 15, "mp_cost_per_level": 1.1, "base_cooldown_turns": 4, "cooldown_reduction_per_level": 0.06, "base_effect_definitions": [{"effect_template": "RESOURCE_ADVANTAGE_BLAST", "multiplier": "(0.97 + skill_level * 0.18)", "can_crit": true}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.4000000000000001}, "tags": ["DAMAGE", "ACTIVE", "MEDIUM_EFFECT"]}, "ACTIVE_DEBUFF_DEBUFF_R6_LARGE_EFFECT_WITH_APPLY_ALL_STATS_DEBUFF": {"name": "至尊武力封絕", "description_template": "對敵方施加至尊武力封絕，攻擊力降低 (36.0 + skill_level * 5.94) / 100 * 100%，持續 (9 + floor(skill_level / 3)) 回合 並降低敵方全屬性", "skill_rarity": 6, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 34, "mp_cost_per_level": 1.76, "base_cooldown_turns": 8, "cooldown_reduction_per_level": 0.06, "base_effect_definitions": [{"effect_template": "APPLY_ATK_DEBUFF", "duration_turns": "(9 + floor(skill_level / 3))", "chance": "(min(1.0, 0.90 + skill_level * 0.025))", "stack_count": 3, "value_overrides": {"patk": "-(36.0 + skill_level * 5.94) / 100", "matk": "-(36.0 + skill_level * 5.94) / 100"}}, {"effect_template": "APPLY_ALL_STATS_DEBUFF", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.71 + skill_level * 0.023))", "stack_count": 2, "value_overrides": {"patk": "-(18.0 + skill_level * 3.00) / 100", "matk": "-(18.0 + skill_level * 3.00) / 100", "pdef": "-(20.0 + skill_level * 3.30) / 100", "mdef": "-(20.0 + skill_level * 3.30) / 100", "spd": "-(16.0 + skill_level * 2.20) / 100"}}], "xp_gain_on_sacrifice": 23, "xp_to_next_level_config": {"base_xp": 150, "multiplier": 1.4000000000000001}, "tags": ["DEBUFF", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_BUFF_BOOST_R7_SMALL_EFFECT_WITH_APPLY_SPD_BOOST": {"name": "傳說瞬間綜合", "description_template": "獲得傳說瞬間綜合效果，全屬性提升 (13.2 + skill_level * 2.16) / 100 * 100%，持續 (3 + floor(skill_level / 3)) 回合 並提升速度", "skill_rarity": 7, "max_level": 5, "target_type": "SELF", "base_mp_cost": 20, "mp_cost_per_level": 0.84, "base_cooldown_turns": 3, "cooldown_reduction_per_level": 0.07, "base_effect_definitions": [{"effect_template": "APPLY_ALL_STATS_BOOST", "duration_turns": "(3 + floor(skill_level / 3))", "chance": "(min(1.0, 0.83 + skill_level * 0.015))", "stack_count": 3, "value_overrides": {"patk": "(13.2 + skill_level * 2.16) / 100", "matk": "(13.2 + skill_level * 2.16) / 100", "pdef": "(14.4 + skill_level * 2.34) / 100", "mdef": "(14.4 + skill_level * 2.34) / 100", "spd": "(12.0 + skill_level * 1.56) / 100"}}, {"effect_template": "APPLY_SPD_BOOST", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.81 + skill_level * 0.022))", "stack_count": 4, "value_overrides": {"spd": "(18.0 + skill_level * 2.40) / 100"}}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 170, "multiplier": 1.4500000000000002}, "tags": ["BUFF", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_SPECIAL_SHIELD_R7_LARGE_EFFECT_WITH_APPLY_DEF_DEBUFF+APPLY_STUN_STATUS": {"name": "究極護盾狂碎", "description_template": "發動究極護盾狂碎，移除敵人的所有盾牌效果 並降低敵方防禦力 並擊暈敵方", "skill_rarity": 7, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 70, "mp_cost_per_level": 1.92, "base_cooldown_turns": 14, "cooldown_reduction_per_level": 0.07, "base_effect_definitions": [{"effect_template": "SHIELD_REMOVAL", "value": "(405.0 + skill_level * 27.0)", "duration_turns": "(9 + floor(skill_level / 3))"}, {"effect_template": "APPLY_DEF_DEBUFF", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.84 + skill_level * 0.022))", "stack_count": 3, "value_overrides": {"pdef": "-(22.0 + skill_level * 3.60) / 100", "mdef": "-(22.0 + skill_level * 3.60) / 100"}}, {"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.88 + skill_level * 0.024))", "stack_count": 3}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 170, "multiplier": 1.4500000000000002}, "tags": ["SPECIAL", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_SPECIAL_SHIELD_R7_SMALL_EFFECT_WITH_APPLY_FREEZE_STATUS+APPLY_POISON_STATUS": {"name": "神聖輕度破除", "description_template": "發動神聖輕度破除，移除敵人的所有盾牌效果 並冰凍敵方 並使敵方中毒", "skill_rarity": 7, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 27, "mp_cost_per_level": 0.84, "base_cooldown_turns": 6, "cooldown_reduction_per_level": 0.07, "base_effect_definitions": [{"effect_template": "SHIELD_REMOVAL", "value": "(135.0 + skill_level * 9.0)", "duration_turns": "(3 + floor(skill_level / 3))"}, {"effect_template": "APPLY_FREEZE_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.80 + skill_level * 0.020))", "stack_count": 3}, {"effect_template": "APPLY_POISON_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.91 + skill_level * 0.021))", "stack_count": 3}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 170, "multiplier": 1.4500000000000002}, "tags": ["SPECIAL", "ACTIVE", "SMALL_EFFECT"]}, "ACTIVE_DEBUFF_BURN_R7_LARGE_EFFECT_WITH_APPLY_STUN_STATUS+LOSE_MP_EFFECT": {"name": "傳說火焰風暴", "description_template": "對敵方施加傳說火焰風暴，使其陷入燃燒狀態，每回合損失生命，持續 (9 + floor(skill_level / 3)) 回合 並擊暈敵方 並燃燒敵方法力值", "skill_rarity": 7, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 45, "mp_cost_per_level": 1.92, "base_cooldown_turns": 9, "cooldown_reduction_per_level": 0.07, "base_effect_definitions": [{"effect_template": "APPLY_BURN_STATUS", "duration_turns": "(9 + floor(skill_level / 3))", "chance": "(min(1.0, 0.95 + skill_level * 0.019))", "stack_count": 3}, {"effect_template": "APPLY_STUN_STATUS", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.90 + skill_level * 0.020))", "stack_count": 3}, {"effect_template": "LOSE_MP_EFFECT", "value": "(40.0 + skill_level * 2.0)"}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 170, "multiplier": 1.4500000000000002}, "tags": ["DEBUFF", "ACTIVE", "LARGE_EFFECT"]}, "ACTIVE_UNKNOWN_SHIELD_R7_MEDIUM_EFFECT": {"name": "傳說防護屏障", "description_template": "施展傳說防護屏障，獲得護盾，可吸收  點傷害，持續 4 回合", "skill_rarity": 7, "max_level": 5, "target_type": "ENEMY_SINGLE", "base_mp_cost": 32, "mp_cost_per_level": 1.2, "base_cooldown_turns": 6, "cooldown_reduction_per_level": 0.07, "base_effect_definitions": [{"effect_template": "APPLY_BASIC_SHIELD", "duration_turns": "(5 + floor(skill_level / 3))", "chance": "(min(1.0, 0.91 + skill_level * 0.025))", "stack_count": 4}], "xp_gain_on_sacrifice": 26, "xp_to_next_level_config": {"base_xp": 170, "multiplier": 1.4500000000000002}, "tags": ["SHIELD", "ACTIVE", "MEDIUM_EFFECT"]}}