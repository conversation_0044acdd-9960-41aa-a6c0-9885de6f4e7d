"""
Gacha系統 - 收藏相關指令的 Cog
"""

from typing import List, Optional

import discord
from discord import app_commands
from discord.ext import commands

from gacha.exceptions import (
    BusinessError,
    CardNotFoundError,
)
from gacha.models.filters import CollectionFilters
from gacha.repositories.collection import user_collection_repository

# 導入最高星級維護服務
from gacha.services import (
    collection_service,
    encyclopedia_service,
    favorite_service,
    highest_star_maintenance_service,
)
from gacha.views.collection.collection_view.card_view import CollectionView
from gacha.views.collection.collection_view.series_view import (
    SeriesListView,  # 更新導入路徑
)
from gacha.views.collection.encyclopedia_view import EncyclopediaView
from gacha.views.embeds.collection.rarity_embed_builder import RarityEmbedBuilder
from gacha.views.embeds.collection.series_embed_builder import SeriesEmbedBuilder
from gacha.views.utils import get_pool_type_choices, get_rarity_choices
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


# 排序選項輔助函數
def _get_sort_options():
    """獲取排序選項"""
    from gacha.config.sorting_config import SortingConfig

    return SortingConfig.SORT_OPTIONS


def _get_encyclopedia_sort_options():
    """獲取圖鑑專用排序選項"""
    from gacha.config.sorting_config import SortingConfig

    return SortingConfig.ENCYCLOPEDIA_SORT_OPTIONS


def _get_sort_order_options():
    """獲取排序順序選項"""
    from gacha.config.sorting_config import SortingConfig

    return SortingConfig.SORT_ORDER_OPTIONS


class CollectionCog(commands.Cog):
    """處理與卡片收藏相關的指令，例如查看卡冊、圖鑑、系列和標記最愛。"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot

        # 啟動最高星級維護服務
        self.bot.loop.create_task(self.start_highest_star_service())

        logger.info("CollectionCog 已成功加載並初始化。")

    async def start_highest_star_service(self):
        """異步啟動最高星級維護服務"""
        try:
            logger.info("正在啟動 HighestStarMaintenanceService...")
            await highest_star_maintenance_service.start()
            logger.info("✅ HighestStarMaintenanceService 已由 CollectionCog 啟動")
        except Exception as e:
            logger.error(
                "❌ CollectionCog 啟動 HighestStarMaintenanceService 失敗: %s",
                e,
                exc_info=True,
            )

    async def cog_unload(self):
        """在 Cog 卸載時停止服務"""
        logger.info("CollectionCog 正在卸載，準備停止最高星級維護服務...")
        try:
            await highest_star_maintenance_service.stop()
            logger.info("✅ HighestStarMaintenanceService 已成功停止")
        except Exception as e:
            logger.error(
                "❌ CollectionCog 卸載時停止 HighestStarMaintenanceService 失敗: %s",
                e,
                exc_info=True,
            )

    @app_commands.command(name="mw", description="查看你的卡冊")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        user="要查看的用戶（支援用戶ID或用戶名搜尋，不填則查看自己的卡冊）",
        sort_by="排序方式（rarity稀有度，name名稱，series系列，quantity數量）",
        sort_order="排序順序（desc降序，asc升序）",
        pool_type="卡池類型（不選則顯示全部）",
        rarity="稀有度篩選（C、R、SR、SSR、UR、LR、EX）",
        series_name="按系列名稱篩選",
        card_id="按卡片ID查詢",
        card_name="按卡片名稱查詢（支援模糊搜尋）",
        show_duplicates="是否只顯示重複擁有的卡片（數量 > 1）",
        favorite_filter="最愛卡片篩選（all全部，favorites僅最愛，non_favorites僅非最愛）",
        favorite_priority="最愛優先排序（預設：是，選擇 `sort_by` 時自動關閉）",
        list_mode="列表模式：一頁顯示多張卡片（預設：否，一頁一張卡片）",
    )
    @app_commands.choices(
        sort_by=[
            sort_option.to_command_choice() for sort_option in _get_sort_options()
        ],
        sort_order=[
            sort_option.to_command_choice() for sort_option in _get_sort_order_options()
        ],
        pool_type=get_pool_type_choices(show_individual_pools_only=True),
        rarity=get_rarity_choices(),  # type: ignore
        favorite_filter=[
            app_commands.Choice(name="全部卡片", value="all"),
            app_commands.Choice(name="僅最愛卡片", value="favorites"),
            app_commands.Choice(name="僅非最愛卡片", value="non_favorites"),
        ],
        favorite_priority=[
            app_commands.Choice(name="是（最愛卡片優先）", value="true"),
            app_commands.Choice(name="否（統一排序）", value="false"),
        ],
    )
    async def view_collection(
        self,
        interaction: discord.Interaction,
        user: Optional[discord.User] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "desc",
        pool_type: Optional[str] = None,
        rarity: Optional[int] = None,
        series_name: Optional[str] = None,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        show_duplicates: Optional[bool] = False,
        favorite_filter: Optional[str] = "all",
        favorite_priority: Optional[str] = None,
        list_mode: Optional[bool] = False,
    ):
        """查看卡冊命令處理"""
        await interaction.response.defer(thinking=True)

        # 根據用戶輸入決定排序邏輯
        if sort_by is None:
            # 用戶未指定 sort_by：預設為稀有度排序 + 最愛優先
            final_sort_by = "rarity"
            # 除非用戶明確設置 favorite_priority="false"，否則保持最愛優先
            use_favorite_priority = favorite_priority != "false"
        else:
            # 用戶指定了 sort_by：使用用戶選擇的排序方式
            final_sort_by = sort_by
            # 除非用戶明確設置 favorite_priority="true"，否則停用最愛優先
            use_favorite_priority = favorite_priority == "true"

        # 確定要查看的用戶
        target_user = user or interaction.user
        user_id = target_user.id
        is_viewing_others = user is not None

        # 建立篩選條件
        filters = CollectionFilters(
            pool_type=None if pool_type == "all" else pool_type,
            rarity_in=[rarity] if rarity is not None else None,
            series=series_name,
            quantity_greater_than=1 if show_duplicates else None,
            card_name=card_name,
        )
        if card_id is not None:
            filters.set_card_id(card_id)

        # 處理最愛卡片篩選
        if favorite_filter == "favorites":
            filters.favorite_status = "favorite_only"
        elif favorite_filter == "non_favorites":
            filters.favorite_status = "non_favorite_only"
        else:
            filters.favorite_status = "all"

        target_page_number = 1
        pre_fetched_unique_cards_for_call: Optional[int] = None

        # 處理卡片ID搜尋
        if card_id is not None:
            id_search_filters = CollectionFilters(
                pool_type=filters.pool_type,
                rarity_in=filters.rarity_in,
                series=filters.series,
                quantity_greater_than=(
                    filters.quantity_greater_than if show_duplicates else None
                ),
                card_id=None,
                card_name=None,
                extra_filters=filters.extra_filters.copy()
                if filters.extra_filters
                else None,
            )
            (
                page_from_find,
                unique_cards_for_id_filter,
            ) = await collection_service.find_card_page(
                user_id,
                card_id,
                final_sort_by,
                sort_order or "desc",
                id_search_filters,
                favorite_priority=use_favorite_priority,
            )
            if page_from_find > 0:
                target_page_number = page_from_find
                pre_fetched_unique_cards_for_call = unique_cards_for_id_filter
                filters.set_card_id(None)
            else:
                raise CardNotFoundError(
                    f"未找到ID為 `{card_id}` 的卡片，或在此篩選條件下你尚未擁有此卡"
                )

        # 處理卡片名稱搜尋
        elif card_name is not None:
            name_search_filters = CollectionFilters(
                pool_type=filters.pool_type,
                rarity_in=filters.rarity_in,
                series=filters.series,
                quantity_greater_than=(
                    filters.quantity_greater_than if show_duplicates else None
                ),
                card_name=card_name,
            )
            card_result = await user_collection_repository.find_user_card_by_name(
                user_id, name_search_filters
            )
            found_card_id = card_result["card_id"]
            (
                page_from_find,
                unique_cards_for_name_filter,
            ) = await collection_service.find_card_page(
                user_id,
                found_card_id,
                final_sort_by,
                sort_order or "desc",
                name_search_filters,
                favorite_priority=use_favorite_priority,
                list_mode=list_mode or False,
            )
            if page_from_find > 0:
                target_page_number = page_from_find
                pre_fetched_unique_cards_for_call = unique_cards_for_name_filter
            else:
                raise CardNotFoundError(
                    f"找到了名為 `{card_name}` 的卡片，但無法定位其頁面。"
                )

        # 獲取卡冊數據
        collection_data = await collection_service.get_user_cards_paginated(
            user_id,
            target_page_number,
            final_sort_by,
            sort_order or "desc",
            filters,
            pre_fetched_unique_cards=pre_fetched_unique_cards_for_call,
            favorite_priority=use_favorite_priority,
            list_mode=list_mode or False,
        )

        # 檢查是否有卡片
        if collection_data.get("unique_cards", 0) == 0:
            user_display = target_user.display_name if is_viewing_others else "你"
            raise BusinessError(
                f"{user_display}還沒有收集到任何符合條件的卡片{'' if is_viewing_others else '，使用`/w`命令可以抽卡'}"
            )

        # 創建視圖
        view = CollectionView(
            bot=self.bot,
            user=interaction.user,
            cards=collection_data["cards"],
            current_page=collection_data["current_page"],
            total_pages=collection_data["total_pages"],
            total_cards=collection_data["total_cards"],
            unique_cards=collection_data["unique_cards"],
            sort_by=final_sort_by,
            sort_order=sort_order or "desc",
            filters=filters,
            interaction=interaction,
            target_user=target_user,
            is_viewing_others=is_viewing_others,
            favorite_priority=use_favorite_priority,
            list_mode=list_mode or False,
        )

        await view.async_init()
        embed = await view.get_current_page_embed()
        message = await interaction.followup.send(embed=embed, view=view, wait=True)
        view.message = message

    @view_collection.autocomplete("series_name")
    async def mw_series_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        all_series = await collection_service.get_all_series(pool_type=None)
        filtered_series = [
            series for series in all_series if current.lower() in series.lower()
        ][:25]
        return [
            app_commands.Choice(name=series, value=series) for series in filtered_series
        ]

    @app_commands.command(name="mwr", description="查看你的稀有度收藏情況")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(pool_type="卡池類型（不選則顯示全部）")
    @app_commands.choices(
        pool_type=get_pool_type_choices(show_individual_pools_only=True)
    )
    async def view_rarity_collection(
        self, interaction: discord.Interaction, pool_type: Optional[str] = None
    ):
        """查看稀有度收藏情況命令處理"""
        await interaction.response.defer(thinking=True)
        user_id = interaction.user.id
        if pool_type == "all":
            pool_type = None
        from gacha.repositories.collection import user_collection_repository

        cards_summary = await user_collection_repository.get_user_cards_summary(
            user_id, pool_type
        )
        if not cards_summary:
            raise BusinessError("你的卡冊是空的，使用`/w`命令可以抽卡")
        collection_stats = await user_collection_repository.get_user_collection_stats(
            user_id, CollectionFilters(pool_type=pool_type)
        )
        rarities_total = await user_collection_repository.get_rarities_total_count(
            pool_type
        )
        builder = RarityEmbedBuilder(
            user=interaction.user,
            cards_summary=cards_summary,
            collection_stats=collection_stats,
            rarities_total=rarities_total,
        )
        await interaction.followup.send(embed=builder.build_embed())

    @app_commands.command(name="mws", description="查看特定系列的收集情況")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        series="系列名稱（留空則列出所有可用系列）",
        pool_type="卡池類型（不選則顯示全部）",
    )
    @app_commands.choices(
        pool_type=get_pool_type_choices(show_individual_pools_only=True)
    )
    async def view_series(
        self,
        interaction: discord.Interaction,
        series: Optional[str] = None,
        pool_type: Optional[str] = None,
    ):
        """查看系列收集情況命令處理"""
        await interaction.response.defer(thinking=True)
        if pool_type == "all":
            pool_type = None
        user_id = interaction.user.id
        if not series:
            series_data = await collection_service.get_all_series_paginated(
                1, pool_type
            )
            view = await SeriesListView.create_view(
                bot=self.bot,
                user=interaction.user,  # type: ignore
                series_data=series_data,
                pool_type=pool_type,
            )
            message = await interaction.followup.send(
                embed=view.build_embed(), view=view, wait=True
            )
            view.message = message
            return
        all_series = await collection_service.get_all_series(pool_type)
        if series not in all_series:
            raise BusinessError(
                f"未找到名為「{series}」的系列，使用`/mws`命令查看所有可用系列"
            )

        series_collections = (
            await user_collection_repository.get_user_series_collections_batch(
                user_id, [series], pool_type
            )
        )
        if not series_collections or series not in series_collections:
            raise BusinessError(f"未找到「{series}」系列的收集記錄")

        series_collection = series_collections[series]
        builder = SeriesEmbedBuilder(user=interaction.user)
        await interaction.followup.send(
            embed=builder.build_single_series_embed(series_collection)
        )

    @view_series.autocomplete("series")
    async def mws_series_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        # 這裡的 pool_type 應該從 interaction 的選項中獲取，但為了快速修復，暫時設為 None
        all_series = await collection_service.get_all_series(pool_type=None)
        filtered_series = [
            series for series in all_series if current.lower() in series.lower()
        ][:25]
        return [
            app_commands.Choice(name=series, value=series) for series in filtered_series
        ]

    @app_commands.command(name="aw", description="查看全圖鑑")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        sort_by="排序方式（rarity: 稀有度, name: 名稱, creation_date: 創建日期）",
        sort_order="排序順序（desc: 降序, asc: 升序）",
        pool_type="卡池類型（main: 主卡池, special: 典藏卡池, summer: 泳裝卡池）",
        rarity="稀有度",
        series_name="系列名稱",
        card_id="按卡片ID查詢",
        card_name="按卡片名稱查詢（支援模糊搜尋）",
        ownership_filter="擁有狀態篩選（all: 全部, owned: 僅已擁有, not_owned: 僅未擁有）",
    )
    @app_commands.choices(
        sort_by=[
            sort_option.to_command_choice()
            for sort_option in _get_encyclopedia_sort_options()
        ],
        sort_order=[
            sort_option.to_command_choice() for sort_option in _get_sort_order_options()
        ],
        pool_type=get_pool_type_choices(show_individual_pools_only=True),
        rarity=get_rarity_choices(),  # type: ignore
        ownership_filter=[
            app_commands.Choice(name="全部卡片", value="all"),
            app_commands.Choice(name="僅已擁有", value="owned"),
            app_commands.Choice(name="僅未擁有", value="not_owned"),
        ],
    )
    async def encyclopedia_command(
        self,
        interaction: discord.Interaction,
        sort_by: str = "rarity",
        sort_order: str = "desc",
        pool_type: Optional[str] = None,
        rarity: Optional[int] = None,
        series_name: Optional[str] = None,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        ownership_filter: str = "all",
    ):
        """全圖鑑指令"""
        await interaction.response.defer()

        if pool_type == "all":
            pool_type = None
        rarity_internal_value: Optional[int] = None
        if rarity is not None:
            rarity_internal_value = rarity

        view = await EncyclopediaView.create(
            interaction=interaction,
            page=1,  # 頁碼計算將轉移到 View 內部
            sort_by=sort_by,
            sort_order=sort_order,
            pool_type=pool_type,
            rarity=rarity_internal_value,
            series_name=series_name,
            card_id=card_id,  # 直接傳遞 card_id
            card_name=card_name,
            ownership_filter=ownership_filter,
        )

        if view is None:
            raise BusinessError("無法創建圖鑑視圖，可能沒有符合條件的卡片。")

        embed = await view.get_current_page_embed()
        await interaction.followup.send(embed=embed, view=view)

    @encyclopedia_command.autocomplete("series_name")
    async def aw_series_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        all_series = await encyclopedia_service.get_card_series_list()
        filtered_series = [s for s in all_series if current.lower() in s.lower()][:25]
        return [app_commands.Choice(name=s, value=s) for s in filtered_series]

    @app_commands.command(
        name="favorite", description="標記/取消標記一張卡片為最愛，最愛的卡片不會被賣出"
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(card_id="要標記/取消標記的卡片ID（可在卡冊頁面底部找到）")
    async def toggle_favorite_command(
        self, interaction: discord.Interaction, card_id: int
    ):
        """標記/取消標記一張卡片為最愛"""
        await interaction.response.defer(ephemeral=True)
        user_id = interaction.user.id

        # 驗證 card_id 範圍
        from gacha.models.filters import validate_card_id

        validate_card_id(card_id)

        # 使用新的純異常模式，ValueError 將被全局捕獲
        is_favorite = await favorite_service.toggle_favorite_card(
            user_id, card_id, operator_id=user_id
        )

        # 根據結果創建訊息
        status_text = "標記為最愛" if is_favorite else "取消最愛標記"
        embed = SuccessEmbed(description=f"已將卡片{status_text}！")
        await interaction.followup.send(embed=embed)


async def setup(bot: commands.Bot):
    """將 CollectionCog 添加到機器人中。"""
    await bot.add_cog(CollectionCog(bot))
    logger.info("CollectionCog 已成功註冊到機器人。")
