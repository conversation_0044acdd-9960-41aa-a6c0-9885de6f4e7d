"""評分圖片生成器模組 - 負責生成穿搭評分可視化圖片"""

import asyncio
import base64
import io
import json
import logging
import pathlib
import urllib.parse
from typing import Any, Dict

from PIL import Image
from playwright.async_api import Page

from utils import playwright_manager

logger = logging.getLogger(__name__)

# 1. 【新】提升配置和狀態為模組級變數
DEFAULT_MAX_CONCURRENT_PAGES = 10
RENDER_DELAY = 0.05
WEBP_QUALITY = 100

# 不再使用 ProcessPoolExecutor，改用 asyncio.to_thread() 避免子進程重新載入模組

# 模仿項目中其他服務的作法，建立一個可靠的基礎目錄路徑
BASE_DIR = pathlib.Path(__file__).resolve().parent.parent.parent.parent
_TEMPLATE_PATH = (
    BASE_DIR / "auxiliary" / "data" / "templates" / "outfit_rating_simple_template.html"
)
_FONT_PATH = BASE_DIR / "fonts" / "NotoSansTC-Regular.ttf"
_FONT_URL = _FONT_PATH.as_uri()
_page_semaphore = asyncio.Semaphore(DEFAULT_MAX_CONCURRENT_PAGES)

logger.info(
    "RatingImageGenerator module loaded. Template: %s, Font: %s, Max concurrent pages: %d",
    _TEMPLATE_PATH,
    _FONT_PATH,
    DEFAULT_MAX_CONCURRENT_PAGES,
)


# 2. 【新】將所有方法轉換為模組級函數，移除 self
def _sync_create_html(request_id: str) -> str:
    """【新增】同步讀取和處理 HTML 範本"""
    with open(_TEMPLATE_PATH, "r", encoding="utf-8") as f:
        template_content = f.read()

    # 動態注入字體檔案的絕對路徑 URI
    template_content = template_content.replace("{{FONT_URL}}", _FONT_URL)

    if "<body" in template_content:
        template_content = template_content.replace(
            "<body", f'<body data-request-id="{request_id}"'
        )
    else:
        template_content = (
            f'<body data-request-id="{request_id}">{template_content}</body>'
        )

    return urllib.parse.quote(template_content)


async def _create_isolated_html_content(request_id: str) -> str:
    """【修改】異步版本，在線程中執行同步操作"""
    return await asyncio.to_thread(_sync_create_html, request_id)


def _sync_convert_to_base64(image_data: bytes) -> str:
    """同步函數：將圖片 bytes 數據轉換為 Base64 data URL"""
    mime_type_map = {
        "jpeg": "image/jpeg",
        "png": "image/png",
        "webp": "image/webp",
        "gif": "image/gif",
    }
    try:
        with Image.open(io.BytesIO(image_data)) as img:
            format_lower = img.format.lower() if img.format else "png"
            mime_type = mime_type_map.get(format_lower, "image/png")
    except Exception:
        mime_type = "image/png"
    base64_data = base64.b64encode(image_data).decode("utf-8")
    return f"data:{mime_type};base64,{base64_data}"


async def _convert_image_bytes_to_base64_data_url(image_data: bytes) -> str:
    """將圖片 bytes 數據轉換為 Base64 data URL，使用線程池"""
    return await asyncio.to_thread(_sync_convert_to_base64, image_data)


def _sync_process_screenshot(screenshot_bytes: bytes) -> bytes:
    """同步函數：處理截圖，轉換為WebP"""
    img = Image.open(io.BytesIO(screenshot_bytes))
    webp_buffer = io.BytesIO()
    img.save(webp_buffer, "WEBP", quality=WEBP_QUALITY, lossless=True)
    return webp_buffer.getvalue()


async def generate_rating_image(
    rating_result: Dict[str, Any], user_image_data: bytes, user_name: str | None = None
) -> bytes:
    """使用 Playwright 生成評分圖片"""
    request_id = rating_result.get("request_id", "unknown_req_id")
    logger.info(
        "[%s] 開始生成評分圖片，用戶: %s，圖像大小: %s bytes",
        request_id,
        user_name,
        len(user_image_data),
    )
    logger.debug(
        "[%s] 評分結果: %s", request_id, json.dumps(rating_result, ensure_ascii=False)
    )

    async with _page_semaphore:
        logger.debug("[%s] 獲得頁面信號量，開始處理", request_id)
        return await _generate_rating_image_with_isolation(
            rating_result, user_image_data, user_name
        )


async def _generate_rating_image_with_isolation(
    rating_result: Dict[str, Any], user_image_data: bytes, user_name: str | None = None
) -> bytes:
    """使用獨立頁面執行圖片生成邏輯，確保請求隔離"""
    page: Page | None = None
    request_id = rating_result.get("request_id", "unknown_req_id")
    max_retries = 3

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                logger.warning(
                    "[%s] 第 %d 次嘗試，先重新初始化瀏覽器", request_id, attempt + 1
                )
                try:
                    await playwright_manager._reinitialize_browser()
                    logger.info("[%s] 瀏覽器重新初始化完成", request_id)
                except Exception as reinit_error:
                    logger.error(
                        "[%s] 瀏覽器重新初始化失敗: %s", request_id, reinit_error
                    )

            page = await playwright_manager.acquire_page()
            logger.info(
                "[%s] 已獲取 Playwright 頁面 (嘗試 %d/%d)",
                request_id,
                attempt + 1,
                max_retries + 1,
            )

            score = rating_result.get("score", "?/10")
            review = rating_result.get("review", "無法獲取評價")
            suggestion = rating_result.get("suggestion", "無法獲取建議")
            conclusion = rating_result.get("conclusion", "超讚!")
            user_name = user_name or "用戶"

            html_content = await _create_isolated_html_content(request_id)
            data_url = f"data:text/html;charset=utf-8,{html_content}"
            await page.goto(data_url, wait_until="domcontentloaded")

            image_base64_data_url = await _convert_image_bytes_to_base64_data_url(
                user_image_data
            )

            await page.evaluate(
                """
                ([selector, value]) => {
                    document.querySelector(selector).setAttribute('src', value);
                }
                """,
                ["#user-image", image_base64_data_url],
            )

            text_updates = [
                ("#username-text", user_name),
                ("#rating-score", score),
                ("#rating-content", review),
                ("#suggestion-content", suggestion),
                (".text-sm.text-pink-600.font-medium", conclusion),
            ]

            for selector, value in text_updates:
                await page.evaluate(
                    """
                    ([selector, value]) => {
                        document.querySelector(selector).innerText = value;
                    }
                    """,
                    [selector, value],
                )

            await asyncio.sleep(RENDER_DELAY)

            card_locator = page.locator(".max-w-md.mx-auto")
            await card_locator.wait_for(state="visible")

            screenshot_bytes = await card_locator.screenshot(
                type="png", timeout=10000, animations="disabled"
            )

            webp_bytes = await asyncio.to_thread(
                _sync_process_screenshot, screenshot_bytes
            )

            logger.info(
                "[%s] 評分圖像已生成 (WebP): %s bytes", request_id, len(webp_bytes)
            )
            return webp_bytes

        except Exception as e:
            error_msg = str(e)
            logger.error(
                "[%s] 生成評分圖像時出錯 (嘗試 %d/%d): %s",
                request_id,
                attempt + 1,
                max_retries + 1,
                error_msg,
                exc_info=True,
            )
            connection_errors = [
                "Connection closed",
                "Browser has been closed",
                "Target page, context or browser has been closed",
                "Protocol error",
                "new_context",
            ]
            is_connection_error = any(err in error_msg for err in connection_errors)
            if is_connection_error and attempt < max_retries:
                logger.warning(
                    "[%s] 檢測到 Playwright 連接錯誤，將重試 (嘗試 %d/%d)",
                    request_id,
                    attempt + 1,
                    max_retries + 1,
                )
                await asyncio.sleep(1.0)
                continue
            else:
                raise
        finally:
            if page:
                try:
                    await playwright_manager.release_page(page)
                except Exception as release_error:
                    logger.warning("[%s] 釋放頁面時出錯: %s", request_id, release_error)
                page = None

    raise RuntimeError(f"[{request_id}] 圖片生成在 {max_retries + 1} 次嘗試後仍然失敗")


def shutdown_executor():
    """關閉執行器（現在使用 asyncio.to_thread，無需手動關閉）"""
    logger.info("使用 asyncio.to_thread，無需手動關閉執行器。")
