"""
Gacha系統全圖鑑視圖
處理全圖鑑指令的視圖和交互
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Union

import discord
from discord.ext import commands

from config.app_config import get_config, get_oil_emoji, get_rarity_images_url
from gacha.constants import RarityLevel
from gacha.exceptions import BusinessError
from gacha.models.filters import CollectionFilters
from gacha.services import encyclopedia_service
from gacha.views import utils as view_utils
from gacha.views.collection.card_users_view import CardUsersView
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.logger import logger


class EncyclopediaView(BasePaginationView):
    """全圖鑑視圖"""

    def __init__(
        self,
        interaction: discord.Interaction,
        page: int = 1,
        sort_by: str = "rarity",
        sort_order: str = "desc",
        pool_type: Optional[str] = None,
        rarity: Optional[Union[int, List[int]]] = None,
        series_name: Optional[str] = None,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        ownership_filter: str = "all",
        initial_data: Optional[dict] = None,
    ):
        """初始化全圖鑑視圖"""
        total_pages = initial_data.get("total_pages", 1) if initial_data else 1
        super().__init__(
            bot=interaction.client,  # type: ignore
            user_id=interaction.user.id,
            current_page=page,
            total_pages=total_pages,
            timeout=300,
        )

        # 設置基本屬性
        self.interaction = interaction
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.ownership_filter = ownership_filter
        # 注意：self.page 已經在父類中設置為 current_page

        # 設置篩選條件
        self.filters = self._create_filters(
            pool_type, rarity, series_name, card_id, card_name
        )

        # 設置初始數據
        self._set_initial_data(initial_data)

    def _create_filters(
        self,
        pool_type: Optional[str],
        rarity: Optional[Union[int, List[int]]],
        series_name: Optional[str],
        card_id: Optional[int],
        card_name: Optional[str],
    ) -> CollectionFilters:
        """創建篩選條件"""
        filters = CollectionFilters()
        filters.pool_type = None if pool_type == "all" else pool_type
        filters.rarity_in = self._parse_rarity(rarity)
        filters.series = series_name
        filters.set_card_id(card_id)
        filters.card_name = card_name
        return filters

    def _parse_rarity(
        self, rarity: Optional[Union[int, List[int]]]
    ) -> Optional[List[int]]:
        """解析稀有度參數"""
        if rarity is None:
            return None

        if isinstance(rarity, list):
            return [
                int(r) for r in rarity if isinstance(r, (int, str)) and str(r).isdigit()
            ]

        if isinstance(rarity, (int, str)) and str(rarity).isdigit():
            return [int(rarity)]

        return None

    @discord.ui.button(
        label="查看用戶",
        style=discord.ButtonStyle.secondary,
        custom_id="view_users",
        emoji="👥",
        row=1,
    )
    async def view_users_button(
        self, interaction: discord.Interaction, _button: discord.ui.Button
    ):
        """查看用戶列表按鈕"""
        await interaction.response.defer(ephemeral=True)
        # 移除 try-except，讓 BaseView.on_error 處理
        if not self.card_data:
            raise BusinessError("無法獲取卡片信息")

        card_id = self.card_data.get("card_id")
        if not card_id:
            raise BusinessError("無法獲取卡片ID")

        # 創建用戶列表視圖
        if not isinstance(self.bot, (commands.Bot, commands.AutoShardedBot)):
            raise TypeError(
                "Bot is not a commands.Bot or commands.AutoShardedBot instance"
            )
        users_view = await CardUsersView.create(
            bot=self.bot, user=interaction.user, card_id=card_id, view_type="owners"
        )

        if not users_view:
            raise BusinessError("無法載入用戶列表")

        embed = await users_view.get_current_page_embed()
        await interaction.followup.send(embed=embed, view=users_view, ephemeral=True)

    def _set_initial_data(self, initial_data: Optional[dict]) -> None:
        """設置初始數據"""
        if not initial_data:
            self.has_card = False
            self.card_data = {}
            self.owner_count = 0
            self.total_cards = 0
            self.user_owns_card = False
            return

        self.has_card = initial_data.get("has_card", False)
        self.card_data = initial_data.get("card_data", {})
        self.owner_count = initial_data.get("owner_count", 0)
        self.total_cards = initial_data.get("total_cards", 0)
        self.user_owns_card = initial_data.get("user_owns_card", False)

    @classmethod
    async def create(
        cls,
        interaction: discord.Interaction,
        page: int = 1,
        sort_by: str = "rarity",
        sort_order: str = "desc",
        pool_type: Optional[str] = None,
        rarity: Optional[Union[int, List[int]]] = None,
        series_name: Optional[str] = None,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        ownership_filter: str = "all",
    ):
        """非同步創建並初始化 EncyclopediaView 實例"""
        # 創建臨時實例來使用輔助方法
        temp_instance = cls.__new__(cls)
        filters = temp_instance._create_filters(
            pool_type, rarity, series_name, card_id, card_name
        )

        target_page = page
        # 如果提供了 card_id，則在創建視圖前先找到它所在的頁碼
        if card_id is not None:
            target_page = await encyclopedia_service.find_card_page_info_by_id(
                target_card_id=card_id,
                sort_by=sort_by,
                sort_order=sort_order,
                pool_type=filters.pool_type,
                rarity=filters.rarity_in,
                series_name=filters.series,
                user_id=interaction.user.id,
                ownership_filter=ownership_filter,
            )
            # 找到頁碼後，清除卡片ID篩選，以顯示該頁面的正確卡片
            # 但保留其他篩選條件（如 card_name）
            filters.set_card_id(None)

        # 獲取目標頁面的數據
        initial_result = await cls._fetch_page_data(
            target_page,
            sort_by,
            sort_order,
            filters,
            interaction.user.id,
            ownership_filter,
        )

        instance = cls(
            interaction=interaction,
            page=initial_result.get("current_page", 1),
            sort_by=sort_by,
            sort_order=sort_order,
            pool_type=pool_type,
            rarity=rarity,
            series_name=series_name,
            card_id=None,  # 已用於查找，重置為 None
            card_name=card_name,  # 保留 card_name 篩選條件
            ownership_filter=ownership_filter,
            initial_data=initial_result,
        )
        return instance

    @staticmethod
    async def _fetch_page_data(
        page: int,
        sort_by: str,
        sort_order: str,
        filters: CollectionFilters,
        user_id: int,
        ownership_filter: str,
    ):
        """獲取頁面數據"""
        return await encyclopedia_service.get_card_for_page(
            page=page,
            sort_by=sort_by,
            sort_order=sort_order,
            pool_type=filters.pool_type,
            rarity=filters.rarity_in,
            series_name=filters.series,
            card_id=filters.card_id,
            card_name=filters.card_name,
            user_id=user_id,
            ownership_filter=ownership_filter,
        )

    def _update_instance_data(self, result: dict) -> None:
        """更新實例數據"""
        self.has_card = result.get("has_card", False)
        self.card_data = result.get("card_data", {})
        self.owner_count = result.get("owner_count", 0)
        self.total_cards = result.get("total_cards", 0)
        self.user_owns_card = result.get("user_owns_card", False)
        self.total_pages = result.get("total_pages", 1)

    async def get_current_page_embed(self) -> discord.Embed:
        """實現父類的抽象方法，返回當前頁面的 Embed

        返回:
            discord.Embed: 當前頁面的 Embed 對象
        """
        return self._build_encyclopedia_embed()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """實現父類的抽象方法，更新頁面數據"""
        # 移除 try-except，讓 BaseView.on_error 處理
        self.current_page = page

        result = await self._fetch_page_data(
            page,
            self.sort_by,
            self.sort_order,
            self.filters,
            self.interaction.user.id,
            self.ownership_filter,
        )

        self._update_instance_data(result)

        self._refresh_button_states()
        embed = await self.get_current_page_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    def _build_encyclopedia_embed(self) -> discord.Embed:
        """構建全圖鑑嵌入消息"""
        if not self.card_data:
            return discord.Embed(
                title="全圖鑑",
                description="沒有找到卡片。",
                color=discord.Color.light_grey(),
            )

        # 解析卡片基本信息
        card_info = self._parse_card_info()

        # 構建embed
        embed = self._create_card_embed(card_info)

        # 添加最高星級記錄
        self._add_star_record_field(embed, card_info)

        # 設置圖片和縮圖
        self._set_embed_images(embed, card_info)

        # 設置footer
        self._set_embed_footer(embed, card_info)

        return embed

    def _parse_card_info(self) -> dict:
        """解析卡片信息"""
        card_rarity_val = self.card_data.get("rarity")
        card_rarity_enum = (
            RarityLevel(int(card_rarity_val)) if card_rarity_val is not None else None
        )
        pool_type = self.card_data.get("pool_type", "main")

        return {
            "card_id": self.card_data.get("card_id"),
            "name": self.card_data.get("name", "未知卡片"),
            "series": self.card_data.get("series", "未知系列"),
            "rarity_enum": card_rarity_enum,
            "pool_type": pool_type,
            "image_url": self.card_data.get("image_url"),
            "price_info": self._get_price_info(self.card_data),
            "star_info": self._get_star_info(self.card_data),
            "color": self._get_rarity_color(card_rarity_enum, pool_type),
        }

    def _get_price_info(self, card_data: dict) -> dict:
        """獲取價格信息"""
        stored_price = card_data.get("current_market_sell_price")
        if stored_price is not None and not isinstance(stored_price, Decimal):
            try:
                stored_price = Decimal(str(stored_price))
            except Exception:
                stored_price = None

        if stored_price is not None:
            return {
                "label": "市場價",
                "display": f"`{int(stored_price)}` {get_oil_emoji()}",
            }

        return {"label": "市場價", "display": "價格計算中"}

    def _get_star_info(self, card_data: dict) -> dict:
        """獲取星級信息"""
        return {
            "user_id": card_data.get("highest_star_user_id"),
            "level": card_data.get("highest_star_level", 0),
            "nickname": card_data.get("nickname", "無玩家"),
            "achieved_at": card_data.get("achieved_at"),
            "custom_description": card_data.get("custom_description"),
            "description_user_id": card_data.get("description_user_id"),
        }

    def _get_rarity_color(
        self, rarity_enum: Optional[RarityLevel], pool_type: str
    ) -> discord.Color:
        """獲取稀有度顏色"""
        if rarity_enum is not None:
            color_int = view_utils.get_rarity_color(rarity_enum, pool_type)
            return discord.Color(value=color_int)
        else:
            return discord.Color.light_grey()

    def _create_card_embed(self, card_info: dict) -> discord.Embed:
        """創建卡片embed"""
        # 獲取顯示文本
        display_rarity = (
            view_utils.get_user_friendly_rarity_name(card_info["rarity_enum"])
            if card_info["rarity_enum"] is not None
            else "未知"
        )
        pool_prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
        pool_display_text = (
            pool_prefixes.get(card_info["pool_type"], "")
            if isinstance(pool_prefixes, dict)
            else ""
        )
        rarity_emoji = (
            view_utils.get_encyclopedia_rarity_emoji(
                card_info["rarity_enum"], card_info["pool_type"]
            )
            if card_info["rarity_enum"] is not None
            else "❓"
        )

        # 構建description
        description_lines = [
            f"{rarity_emoji} **{card_info['name']}**",
            f"<:ReplyCont:1383146319425699931> *{card_info['series']}*",
            f"<:ReplyCont:1383146319425699931>{display_rarity} | {pool_display_text}",
            f"<:Reply:1357534074830590143>{card_info['price_info']['label']}: {card_info['price_info']['display']}",
        ]

        # 添加擁有狀態顯示（僅在有篩選時顯示）
        if self.ownership_filter != "all":
            ownership_status = (
                "<a:check2:1357795058782441766> 你已擁有此卡片"
                if self.user_owns_card
                else "<:nocheck:1357796970160455892> 你未擁有此卡片"
            )
            description_lines.append(ownership_status)
        elif self.user_owns_card:
            # 即使沒有篩選，如果用戶擁有這張卡片，也顯示一個小標記
            description_lines.append("<a:check2:1357795058782441766> 你已擁有此卡片")

        embed = discord.Embed(
            title="",
            description="\n".join(description_lines),
            color=card_info["color"],
        )

        # 根據擁有狀態篩選調整標題
        author_name = "全圖鑑紀錄"
        if self.ownership_filter == "owned":
            author_name = "全圖鑑紀錄 - 已擁有"
        elif self.ownership_filter == "not_owned":
            author_name = "全圖鑑紀錄 - 未擁有"

        embed.set_author(
            name=author_name,
            icon_url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif",
        )

        return embed

    def _add_star_record_field(self, embed: discord.Embed, card_info: dict) -> None:
        """添加最高星級記錄字段"""
        star_info = card_info["star_info"]
        if not star_info["user_id"]:
            return

        star_emoji = view_utils.get_star_emoji_string(star_info["level"])
        # 在標題前面加上星數
        title = f"<a:pu:1365482490478989353>最高{star_info['level']}星級紀錄"

        if star_info["achieved_at"]:
            achieved_date = (
                star_info["achieved_at"].strftime("%Y-%m-%d")
                if isinstance(star_info["achieved_at"], datetime)
                else str(star_info["achieved_at"])
            )
            title += f" `({achieved_date})`"

        embed.add_field(
            name=title,
            value=f"<:Reply:1357534074830590143>{star_info['nickname']} {star_emoji}",
            inline=False,
        )

        # 添加自定義描述
        if (
            star_info["custom_description"]
            and star_info["description_user_id"] == star_info["user_id"]
        ):
            embed.add_field(
                name="", value=f"「{star_info['custom_description']}」", inline=False
            )

    def _set_embed_images(self, embed: discord.Embed, card_info: dict) -> None:
        """設置embed圖片和縮圖"""
        if card_info["image_url"]:
            embed.set_image(url=card_info["image_url"])

        self._set_rarity_thumbnail(
            embed, card_info["rarity_enum"], card_info["pool_type"]
        )

    def _set_embed_footer(self, embed: discord.Embed, card_info: dict) -> None:
        """設置embed footer"""
        footer_parts = []

        # 擁有者數量（始終顯示）
        footer_parts.append(f"擁有者: {self.owner_count:,}")

        # 許願數量
        wishlist_count = self._get_wishlist_count(self.card_data)
        if wishlist_count > 0:
            footer_parts.append(f"✨ {wishlist_count:,}")

        # 加入日期
        creation_date = self._format_creation_date(self.card_data.get("creation_date"))
        if creation_date:
            footer_parts.append(creation_date)

        # Card ID
        if card_info["card_id"]:
            footer_parts.append(f"Card ID: {card_info['card_id']}")

        if not footer_parts:
            return

        footer_text = " • ".join(footer_parts)
        # icon 始終顯示
        footer_icon_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1382158954649489522/question.png"

        embed.set_footer(text=footer_text, icon_url=footer_icon_url)

    def _get_wishlist_count(self, card_data: dict) -> int:
        """從卡片數據中獲取許願數量"""
        return card_data.get("wishlist_count", 0) if card_data else 0

    def _format_creation_date(self, creation_date) -> Optional[str]:
        """格式化卡片加入日期"""
        if not creation_date:
            return None

        try:
            if isinstance(creation_date, datetime):
                return creation_date.strftime("%Y-%m")
            elif isinstance(creation_date, str):
                # 嘗試解析字符串日期
                parsed_date = datetime.fromisoformat(
                    creation_date.replace("Z", "+00:00")
                )
                return parsed_date.strftime("%Y-%m")
        except Exception:
            # 如果解析失敗，返回None
            pass

        return None

    def _set_rarity_thumbnail(
        self, embed: discord.Embed, rarity_level: Optional[RarityLevel], pool_type: str
    ):
        """設置稀有度縮圖"""
        if rarity_level is None:
            return

        rarity_value = rarity_level.value

        # 使用正確的配置路徑獲取稀有度圖片

        rarity_images_config = get_rarity_images_url()
        all_pools_images = rarity_images_config.get("all_pools", {})

        # 嘗試獲取特定卡池的縮圖（如果將來支持）
        pool_specific_images = rarity_images_config.get(pool_type, {})
        if pool_specific_images and rarity_value in pool_specific_images:
            thumbnail_url = pool_specific_images[rarity_value]
        else:
            # 使用通用縮圖
            thumbnail_url = all_pools_images.get(rarity_value)
            if thumbnail_url is None:
                thumbnail_url = all_pools_images.get(str(rarity_value))

        if thumbnail_url:
            embed.set_thumbnail(url=thumbnail_url)
        else:
            logger.warning(
                "找不到稀有度 '%s' (值: %s, 卡池: '%s') 的縮圖 URL。",
                rarity_level.name,
                rarity_value,
                pool_type or "all_pools",
            )
