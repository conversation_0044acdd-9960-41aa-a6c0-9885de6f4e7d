--- 🌟 Gacha 機器人玩法速覽 🌟 ---

**【基礎 & 資源】**
*   `/balance`：查看油幣💰與抽卡券。
*   `/daily`：每日簽到。
*   `/hourly`：每小時領油幣。

**【核心：抽卡 & 許願】**
*   `/w`：**單抽**。`/w multi:True`：**十連抽**。可選不同卡池。
*   `/wish`：開啟許願介面，提高指定卡片抽中機率。

**【收藏 & 展示】**
*   `/mw`：開啟 **卡冊**。可操作：標記最愛❤️, 賣卡💲, 升星✨, 排序📊, 設描述✍️。
    *   `favorite_filter`：篩選最愛卡片（all全部，favorites僅最愛，non_favorites僅非最愛）
    *   `favorite_priority:False`：統一排序模式（最愛卡片與非最愛卡片混合排列）
    *   `list_mode:True`：列表模式（一頁顯示多張卡片）
*   `/aw`：開啟 **全圖鑑**，瀏覽遊戲內所有卡片。
    *   `ownership_filter`：擁有狀態篩選（all全部，owned僅已擁有，not_owned僅未擁有）
*   `/mwr`：查看各稀有度收集。
*   `/mws [series:系列名]`：查看系列收集進度。
*   `/favorite card_id:[ID]`：標記/取消最愛卡。
*   `/profile`：查看/編輯個人檔案，展示你的卡片與成就。
*   `/stats`：查看個人詳細統計數據儀表板。

**【交易 & 經濟】**
*   `/sw`：賣卡通用指令。可指定 `card_id_param`, `card_name`, `series`, `rarity` 等條件。
*   `/trade user:[玩家]`：與其他玩家交易卡片。
*   `/transfer user:[玩家] amount:[金額]`：轉帳油幣給其他玩家。

**【商店 & 煉金】**
*   `/shop`：開啟油票商店，用油票購買兌換券等物品。
*   `/alchemy amount:[金額]`：高風險煉金，用油幣換油票。

**【市場 & 投資📈】**
*   `/cardinfo query:[ID或名稱]`：查卡片市場行情。
*   `/news`：看市場新聞。
*   `/stock [symbol:代碼]`：查股票列表或指定股票詳情。
*   `/portfolio`：看你的油幣和股票投資組合 (持股/盈虧)。

**【小遊戲🎲🃏】**
*   `/blackjack`：玩21點。
*   `/dice`：玩三骰子比大小。
*   `/mines`：玩尋寶礦區。
*   `/baccarat`：玩百家樂。
*   `/slot`：玩拉霸機。
*   `/tower`：玩爬塔。
*   `/spin`：玩風險轉盤。
*   `/poker1v1`：玩德州撲克1v1。

**【娛樂指令🎭】**
*   `/ask`：向AI提問，支援圖片。
*   `/rate`：AI評分你的穿搭照。
*   `/rateb`：AI分析角色圖片戰力。
*   `/gif`：製作個人化GIF。
*   `/story start`：開始或繼續AI互動式文字冒險。
*   `/story list`：查看你的所有故事。
*   `/story view`：查看分享的故事。

**【排行榜🏆】**
*   `/lb [leaderboard_type:類型]`：查看各類排行榜。

**【系統 & 幫助】**
*   `/help`：開啟詳細的功能說明選單。
*   `/更新資訊`：查看最新的系統更新日誌。
*   `/問題回報`：回報你遇到的問題或Bug。
*   `/機器人資訊`：查看機器人當前的運行狀態與統計。

--- ✨ 祝歐氣滿滿！✨ ---