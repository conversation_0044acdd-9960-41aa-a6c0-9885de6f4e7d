import datetime
import logging
import os
from typing import Optional

# 導入彩色格式化器
from utils.colored_formatter import (
    COLORAMA_AVAILABLE,
    create_colored_console_handler,
    create_colored_formatter,
)

# 建立日誌目錄
LOG_DIR = "logs"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 設定日誌格式
FORMATTER_STRING = (
    "%(asctime)s - %(name)s - %(levelname)s - "
    "%(module)s:%(funcName)s:%(lineno)d - %(message)s"
)

# 創建普通格式化器（用於文件輸出）
file_formatter = logging.Formatter(FORMATTER_STRING)

# 創建彩色格式化器（用於控制台輸出）
colored_formatter = create_colored_formatter(fmt=FORMATTER_STRING)

# 設定日誌文件
current_time_obj = datetime.datetime.now()
timestamp_str = current_time_obj.strftime("%Y-%m-%d_%H-%M-%S")
log_file_name_with_path = os.path.join(LOG_DIR, f"app_{timestamp_str}.log")

# 文件處理器（不使用顏色，因為文件不需要ANSI顏色代碼）
file_handler = logging.FileHandler(
    filename=log_file_name_with_path, mode="a", encoding="utf-8"
)
file_handler.setFormatter(file_formatter)

# 彩色控制台處理器（自動檢測是否使用顏色）
console_handler = create_colored_console_handler()
console_handler.setFormatter(colored_formatter)

# 初始化日誌器，級別將由 init_logging 統一管理
root_logger = logging.getLogger()
# 將處理器添加到 root logger，這樣所有子 logger 都會繼承
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# 設置應用日誌器
logger = logging.getLogger("app")

# 從 init_logging.py 搬移過來的日誌級別映射表
LOG_LEVEL_MAP = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

_logging_initialized = False


def initialize_logging(force_level: Optional[str] = None) -> str:
    """初始化全局日誌系統

    Args:
        force_level: 強制設置的日誌級別，覆蓋環境變數

    Returns:
        str: 設置的日誌級別名稱
    """
    global _logging_initialized
    if _logging_initialized:
        return logging.getLevelName(root_logger.level)
    # 從環境變數或傳入參數獲取日誌級別
    env_log_level = force_level or os.getenv("LOG_LEVEL", "INFO").upper()
    level = LOG_LEVEL_MAP.get(env_log_level, logging.INFO)

    # 設置根日誌級別
    # root_logger 已經在前面被取得並設定了 handlers
    root_logger.setLevel(level)

    # 手動配置 Discord logger，避免重複輸出
    discord_logger = logging.getLogger("discord")
    discord_logger.setLevel(level)

    # 清除 Discord logger 的所有現有處理器，避免重複
    if discord_logger.hasHandlers():
        discord_logger.handlers.clear()

    # 不需要再手動添加 handler，它會從 root logger 繼承
    # 確保 propagate 為 True，以便繼承 root 的 handler
    discord_logger.propagate = True

    # 設置常用模組的日誌級別
    logging.getLogger("app").setLevel(level)
    logging.getLogger("ai_assistant").setLevel(level)
    logging.getLogger("database").setLevel(level)
    logging.getLogger("gacha").setLevel(level)
    logging.getLogger("image_processing").setLevel(level)

    logging.getLogger("TimerUtils").setLevel(level)
    logging.getLogger("AI_Service_Base").setLevel(level)
    logging.getLogger("AI_Commands").setLevel(level)
    logging.getLogger("MessageHandler").setLevel(level)
    logging.getLogger("QAService").setLevel(level)
    logging.getLogger("ImageUtils").setLevel(level)
    logging.getLogger("OutfitRaterCommands").setLevel(level)
    logging.getLogger("RatingImageGenerator").setLevel(level)
    logging.getLogger("RatingHistory").setLevel(level)
    logging.getLogger("OutfitRater").setLevel(level)

    # 完全禁用第三方庫的所有日誌
    noisy_loggers = [
        "PIL",
        "PIL.Image",
        "PIL.PngImagePlugin",
        "PIL.JpegImagePlugin",
        "openai",
        "openai._base_client",
        "openai._client",
        "httpx",
        "httpx._client",
        "httpcore",
    ]

    for logger_name in noisy_loggers:
        noisy_logger = logging.getLogger(logger_name)
        noisy_logger.setLevel(logging.CRITICAL + 1)  # 設置為最高級別+1，完全禁用
        noisy_logger.disabled = True  # 額外保險：直接禁用 logger

    # 顯示彩色日誌狀態信息
    color_status = "啟用" if COLORAMA_AVAILABLE else "未啟用 (colorama未安裝)"

    # 使用彩色輸出顯示初始化信息
    if COLORAMA_AVAILABLE:
        from colorama import Fore, Style

        print(f"{Fore.GREEN}✓ 全局日誌級別已設定為: {env_log_level}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}✓ 彩色日誌輸出: {color_status}{Style.RESET_ALL}")
    else:
        print(f"✓ 全局日誌級別已設定為: {env_log_level}")
        print(f"✓ 彩色日誌輸出: {color_status}")

    _logging_initialized = True
    return env_log_level


# 導出
__all__ = ["logger", "root_logger", "initialize_logging"]
