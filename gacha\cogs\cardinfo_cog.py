from typing import Any, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

from config.app_config import get_oil_emoji
from gacha.constants import RarityLevel
from gacha.exceptions import (
    BusinessError,
)
from gacha.models.market_models import StockLifecycleStatus
from gacha.services import card_info_service
from gacha.services.card_info_service import (
    CardInfoData,
    CardMarketStatsData,
    LinkedStockData,
)
from gacha.views import utils as view_utils
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from utils.base_view import BaseView
from utils.logger import logger

# ==================== Embed 建構器 ====================


class CardInfoEmbedBuilder(BaseEmbedBuilder):
    """卡片資訊 Embed 建構器"""

    def __init__(
        self,
        card_info_data: CardInfoData,
        interaction: Optional[discord.Interaction] = None,
    ):
        super().__init__(data=card_info_data, interaction=interaction)

    def _build_market_stats_text(
        self, market_stats: Optional[CardMarketStatsData]
    ) -> str:
        if not market_stats:
            return self._format_optional_value(None)
        lines = [
            f"總擁有量: `{self._format_optional_value(market_stats.total_owned_quantity)}`",
            f"獨立擁有者: `{self._format_optional_value(market_stats.unique_owner_count)}`",
            f"許願數量: `{self._format_optional_value(market_stats.wishlist_count)}`",
            f"收藏數量: `{self._format_optional_value(market_stats.favorite_count)}`",
        ]
        return "\n".join(lines)

    def build(self) -> discord.Embed:
        card_data: CardInfoData = self.data
        if not card_data:
            embed = self._create_base_embed(
                title="錯誤", description="卡片資料遺失。", color=discord.Color.red()
            )
            self._set_footer(embed, text="請稍後再試")
            return embed

        embed_title = f"{self._truncate_text(card_data.name, 200, field_type='卡片名稱')} (ID: {card_data.card_id})"
        rarity_level_enum_for_visuals: Optional[RarityLevel] = None

        if (
            card_data.raw_card_data
            and hasattr(card_data.raw_card_data, "rarity")
            and isinstance(card_data.raw_card_data.rarity, RarityLevel)
        ):
            rarity_level_enum_for_visuals = card_data.raw_card_data.rarity
        elif card_data.rarity is not None:
            try:
                rarity_level_enum_for_visuals = RarityLevel(card_data.rarity)
            except ValueError:
                logger.warning(
                    "CardInfoEmbedBuilder: Invalid integer rarity %s for card %s",
                    card_data.rarity,
                    card_data.card_id,
                )

        embed_color_int = view_utils.get_rarity_color(
            rarity_level=rarity_level_enum_for_visuals, pool_type=card_data.pool_type
        )
        embed_color = discord.Color(value=embed_color_int)
        embed = self._create_base_embed(title=embed_title, color=embed_color)

        if card_data.image_url:
            embed.set_image(url=card_data.image_url)

        rarity_icon_url = view_utils.get_rarity_image(rarity_level_enum_for_visuals)
        self._set_thumbnail(embed, rarity_icon_url)

        self._add_field(
            embed,
            name="系列",
            value=self._format_optional_value(card_data.series),
            inline=True,
        )

        rarity_to_display = self.DEFAULT_NO_DATA_TEXT
        if rarity_level_enum_for_visuals:
            rarity_to_display = view_utils.get_user_friendly_rarity_name(
                rarity_level_enum_for_visuals
            )
        elif card_data.rarity is not None:
            rarity_to_display = f"T{card_data.rarity}"

        self._add_field(embed, name="稀有度", value=rarity_to_display, inline=True)
        self._add_field(
            embed,
            name="卡池類型",
            value=self._format_optional_value(card_data.pool_type),
            inline=True,
        )

        price_str = self._format_decimal(
            card_data.current_market_sell_price,
            precision=0,
            default_on_none="價格計算中",
        )
        self._add_field(
            embed,
            name="目前市場價",
            value=f"`{price_str}` {get_oil_emoji()}",
            inline=True,
        )

        if card_data.description:
            self._add_field(
                embed, name="描述", value=card_data.description, inline=False
            )

        if card_data.market_stats:
            stats_text = self._build_market_stats_text(card_data.market_stats)
            self._add_field(embed, name="市場統計", value=stats_text, inline=False)

        self._set_footer(embed, text="卡片基本信息")
        return embed


class LinkedStocksEmbedBuilder(BaseEmbedBuilder):
    """關聯股票 Embed 建構器"""

    def __init__(
        self,
        card_name: str,
        linked_stocks_data: List[LinkedStockData],
        interaction: Optional[discord.Interaction] = None,
    ):
        super().__init__(data=linked_stocks_data, interaction=interaction)
        self.card_name = card_name

    def build(self) -> discord.Embed:
        linked_stocks: List[LinkedStockData] = self.data
        embed_title = f"影響 {self._truncate_text(self.card_name, 200, field_type='卡片名稱')} 的部分關聯股票"
        embed = self._create_base_embed(title=embed_title, color=discord.Color.orange())

        if not linked_stocks:
            embed.description = f"目前沒有找到與 {self._truncate_text(self.card_name, 100, field_type='卡片名稱')} 明確關聯的股票信息。"
            return embed

        description_lines = []
        for stock in linked_stocks:
            asset_symbol_str = self._truncate_text(
                stock.asset_symbol, 50, field_type="股票代碼"
            )
            asset_name_str = self._truncate_text(
                stock.asset_name, 100, field_type="股票名稱"
            )
            price_str = self._format_decimal(stock.current_price, precision=2)
            status_suffix = ""
            if stock.lifecycle_status == StockLifecycleStatus.ST:
                status_suffix = " <a:Error:1371096622053724292>"
            elif stock.lifecycle_status == StockLifecycleStatus.DELISTED:
                status_suffix = " (已退市)"
            description_lines.append(
                f"**{asset_symbol_str}** ({asset_name_str}{status_suffix}): `{price_str}` {get_oil_emoji()}"
            )

        final_description = "\n".join(description_lines)
        embed.description = self._truncate_text(
            final_description, 4096, field_type="關聯股票列表"
        )
        return embed


# ==================== View 層 ====================


class BaseCardInfoView(BaseView):
    """基礎卡片資訊視圖，繼承自統一的 BaseView"""

    def __init__(
        self,
        initiating_interaction: discord.Interaction,
        bot_instance: commands.Bot,
        message: Optional[discord.Message] = None,
        timeout: Optional[float] = 180.0,
    ):
        # 調用父類 BaseView 的 __init__
        super().__init__(
            bot=bot_instance, user_id=initiating_interaction.user.id, timeout=timeout
        )
        self.initiating_interaction = initiating_interaction
        self.message: Optional[discord.Message] = message
        self.current_page: int = 1

    # interaction_check 和 on_error 已由 BaseView 提供

    async def on_timeout(self):
        """超時處理"""
        logger.info("%s for user %s timed out.", self.__class__.__name__, self.user_id)
        self.stop()
        # 可以在這裡編輯訊息，例如禁用所有按鈕
        if self.message:
            try:
                await self.message.edit(view=None)
            except discord.NotFound:
                pass  # 訊息可能已被刪除

    async def _get_data_for_display(self, **kwargs) -> Any:
        raise NotImplementedError("Subclasses must implement _get_data_for_display")

    def _build_embed(self, data: Any) -> discord.Embed:
        raise NotImplementedError("Subclasses must implement _build_embed")

    def _build_components(self, data: Any) -> List[discord.ui.Item]:
        raise NotImplementedError("Subclasses must implement _build_components")

    async def update_display(
        self, interaction_for_update: discord.Interaction, **kwargs
    ):
        if not self.message:
            logger.error("%s: No message to update.", self.__class__.__name__)
            return

        # 移除 try-except，讓錯誤冒泡到 on_error
        data = await self._get_data_for_display(**kwargs)
        if data is None:
            logger.warning(
                "%s: Failed to get data for display.", self.__class__.__name__
            )
            # 可以選擇拋出一個錯誤或靜默返回
            raise BusinessError("無法獲取更新後的顯示數據。")

        new_embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)
        await interaction_for_update.followup.edit_message(
            self.message.id, embed=new_embed, view=self
        )

    async def send_initial_message(self, ephemeral: bool = False, **kwargs):
        """發送初始訊息"""
        # 錯誤將被全局處理器捕獲
        data = await self._get_data_for_display(**kwargs)
        if data is None:
            raise BusinessError("無法加載初始數據。")

        embed = self._build_embed(data)
        self.clear_items()
        self._build_components(data)

        self.message = await self.initiating_interaction.followup.send(
            embed=embed, view=self, ephemeral=ephemeral, wait=True
        )


class CardInfoDetailView(BaseCardInfoView):
    """卡片詳細資訊視圖"""

    def __init__(
        self,
        initiating_interaction: discord.Interaction,
        bot_instance: commands.Bot,
        card_query: Union[str, int],
        message: Optional[discord.Message] = None,
    ):
        super().__init__(initiating_interaction, bot_instance, message, timeout=180.0)
        self.card_query = card_query
        self.card_data: Optional[CardInfoData] = None

    async def _get_data_for_display(self, **kwargs) -> Optional[CardInfoData]:
        # 確保 card_query 是字符串類型
        query_str = str(self.card_query)
        self.card_data = await card_info_service.get_card_info_data(query_str)
        return self.card_data

    def _build_embed(self, data: CardInfoData) -> discord.Embed:
        return CardInfoEmbedBuilder(card_info_data=data).build()

    def _build_components(self, data: CardInfoData) -> List[discord.ui.Item]:
        stocks_button = discord.ui.Button(
            label="關聯股票",
            style=discord.ButtonStyle.secondary,
            custom_id="cardinfo_linked_stocks",
        )
        stocks_button.callback = self.linked_stocks_button_callback
        self.add_item(stocks_button)
        return self.children

    async def linked_stocks_button_callback(self, interaction: discord.Interaction):
        """關聯股票按鈕回調"""
        await interaction.response.defer()

        if not self.card_data:
            raise BusinessError("卡片資料不可用")

        if not self.card_data.linked_stocks:
            raise BusinessError("此卡片沒有關聯的股票")

        linked_stocks_embed = LinkedStocksEmbedBuilder(
            card_name=self.card_data.name,
            linked_stocks_data=self.card_data.linked_stocks,
        ).build()

        await interaction.followup.send(embed=linked_stocks_embed, ephemeral=True)


# ==================== COG 類別 ====================


class CardInfoCog(commands.Cog):
    """卡片資訊 COG - 整合所有 cardinfo 相關功能"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="cardinfo", description="查看卡片的詳細市場信息。")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(query="輸入卡片ID或卡片確切名稱")
    async def cardinfo(self, interaction: discord.Interaction, query: str):
        """卡片資訊指令"""
        await interaction.response.defer(ephemeral=False)

        # 移除 try-except，讓所有異常（包括 MarketCardNotFoundError）由全局處理器處理
        view = CardInfoDetailView(
            initiating_interaction=interaction, bot_instance=self.bot, card_query=query
        )
        await view.send_initial_message(ephemeral=False)


# ==================== COG 設置函數 ====================


async def setup(bot: commands.Bot):
    """設置 COG"""
    await bot.add_cog(CardInfoCog(bot))
