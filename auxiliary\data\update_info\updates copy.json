{"updates": [{"date": "2025-07-14", "title": "⚙️ 系統優化 & 📜 劇情更新", "content": "### **📈 指令修復與優化**\n\n• 修復了 `/stats` 中最近消費數字不正確的問題。\n• 修復了 `/lb` 在篩選特定卡池時，抽卡次數排行榜顯示不正確的問題。\n\n### **📜 文字劇情冒險 (`/story`)**\n\n• **新增「時光回溯」功能**：在回顧歷史劇情時，現在可以點擊「🌀 從此處繼續」按鈕，將故事回溯到該時間點，並刪除後續所有進度。\n• **錯誤修復**：修復了各種邊緣情況下可能導致`/story`出錯的問題。(如果還有遇到麻煩回報一下)\n• **新增劇本**：增加了兩個全新的冒險劇本。\n 另外Story目前是測試版，架構和提示詞什麼的都還比較簡略，所以最近會比較常更新，導致故事會被重製或出問題，麻煩大家請見諒，等到出正式版就會以較穩定了。", "image_url": null, "buttons": [{"name": "太神啦米米", "count": 0}, {"name": "我要改變未來", "count": 0}, {"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}]}, {"date": "2025-07-13", "title": "📜 劇情系統優化", "content": "### **📜 文字劇情冒險 (`/story`)**\n\n• **記憶能力優化**：優化了劇情AI的長短期記憶協作能力，改善了對話中偶發的失憶問題，提升了劇情的長期連貫性。\n\n### **⚙️ 系統公告**\n\n• **VPS遷移作業**：遷移VPS還在持續進行中，相關更新進度會比較緩慢，請各位見諒。\n• **開店系統**：開店系統也還在製作中，絕對沒有忘記！", "image_url": null, "buttons": [{"name": "好耶", "count": 0}, {"name": "柚希還記得我嗎", "count": 0}, {"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}]}, {"date": "2025-07-12", "title": "🆕 圖鑑功能新增 & ⚙️ 系統遷移", "content": "### **🆕 圖鑑功能新增**\n\n• **`/aw` 新增用戶列表查詢**：在圖鑑中可使用「查看用戶」按鈕，查詢該卡片的「擁有者」與「許願者」列表。\n\n### **⚙️ 系統遷移**\n\n• 目前正在將機器人遷移至新的VPS主機，預計明天能完成全部遷移作業。\n\n### **📜 文字劇情冒險 (`/story`)**\n\n• `/story` 指令目前為測試版本，感謝許多玩家的試用。由於尚在開發初期，功能可能不穩定，敬請見諒。未來將持續更新與完善。", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "還在跟柚希約會==", "count": 0}, {"name": "好耶", "count": 0}]}, {"date": "2025-07-11", "title": "📊 排序功能擴充 & 財務統計", "content": "### **📊 排序功能擴充**\n\n• **卡冊排序新增**：`/mw` 新增按價格排序、按擁有人數排序\n• **圖鑑排序新增**：`/aw` 新增按價格排序、按擁有人數排序、按許願人數排序\n• **排序名稱統一**：統一所有排序選項的命名格式為「按XX排序」\n• **排行榜優化**：抽卡次數排行榜移動到卡牌相關分類，支援按卡池篩選統計\n• **系統優化**：清理冗餘代碼，統一排序邏輯實現\n\n### **📈 財務統計面板**\n\n• **`/stats` 儀表板新增**：玩家資訊頁面現在會顯示「卡片總價值」、「股票總價值」和「最近花費」\n\n### **📜 文字劇情冒險**\n\n• **新增 `/story` (測試版)指令**：發展你的文字劇情冒險，目前有兩個劇本\n• **劇本選擇**：「與千奈的午後」、「無盡的廢墟」\n• **互動體驗**：AI 驅動的劇情發展，支援重新生成和歷史回顧", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "兔田野太貴==", "count": 0}, {"name": "終於能按價格排了", "count": 0}]}, {"date": "2025-07-10", "title": "⚙️ 指令可用性更新 & 🃏 新卡池", "content": "### **⚙️ 指令可用性更新**\n\n• `/rateb` 現在可在私訊使用\n• `/lb` 現在可在私訊使用\n\n### **🃏 新卡池**\n\n• 新增Shadowverse evolve卡池 (去掉重複弄有夠久==)", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "馬娘我來了", "count": 0}, {"name": "SV好耶", "count": 0}]}, {"date": "2025-07-08", "title": "🖼️ 典藏卡池圖片連結更新 & 商店體驗優化", "content": "### **🖼️ 圖片連結更新**\n\n• **典藏&典藏女僕卡池圖片更新**：所有典藏卡池的卡片圖片連結已更新為Discord CDN。\n\n### **🏪 商店體驗優化**\n\n• 指定卷跟隨機卷現在兌換成功後可以點收藏按鈕了。\n\n### **🛡️ 安全性更新**\n\n• **交易限制**：現在交易指令`/trade` 跟 `/transfer` 需要Discord帳號創建一定時間才能使用。", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "圖片載入正常了", "count": 0}, {"name": "操我才剛註冊耶", "count": 0}]}, {"date": "2025-07-07", "title": "🖼️ 預覽幀功能重構 & ⚠️ 重要通知", "content": "### **🖼️ 預覽幀功能重構**\n\n• **設定方式變更**：`/profile` 設定中的「設定預覽幀」功能，現在從**「秒」**改為**「影格編號」**。\n\n• **變更原因**：由於 Pillow 函式庫限制，無法穩定讀取 `.webp` 動畫的每秒影格數，導致之前基於秒數的設定對 `.webp` 格式無效。為了統一處理 `.gif` 和 `.webp`，改為更可靠的「影格編號」。\n\n### **⚠️ 重要通知**\n\n• **影響範圍**：此次更新後，所有先前設定的預覽幀都已**失效**。\n\n• **需要重新設定**：抱歉造成大家困擾，請使用 `/profile` 中的設定功能，**重新為您的動畫卡片設定預覽影格**。\n\n### **📈 社群好消息**\n\n• **股票分析機器人**：目前群裡 https://discord.gg/kDua5dDt4v 有Aleph大佬特地為了米米開發了股票分析機器人，功能非常強大！歡迎大家來群內使用體驗。\n\n### **💫 卡冊體驗優化**\n\n• **即時更新**：現在在卡冊中取消和加入最愛時，會立即更新當前頁面的embed內容，不需要手動重新整理或切換頁面。\n\n• **重要提醒**：最愛卡片會無視所有排序條件優先顯示在最前面，除非關閉「最愛優先」參數 (`favorite_priority`) 才會與其他卡片公平排序。", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "我才剛設定好耶", "count": 0}, {"name": "股票分析也太猛==", "count": 0}]}, {"date": "2025-07-06", "title": "📋 卡冊列表模式 & 🎯 排行榜系統優化", "content": "**🆕 列表模式`list_mode`**\n• `/mw` 指令新增「列表模式」參數\n• 一頁可顯示 **10張卡片**，資訊更加緊湊\n• 顯示格式：卡片ID + 稀有度 + 最愛標記 + 星級 + 卡片名稱 + 系列名稱\n\n**⚖️ 最愛優先模式`favorite_priority`**\n• 新增「最愛優先」參數，控制最愛卡片是否優先顯示\n• **最愛優先模式**：最愛卡片優先顯示\n• **統一排序模式**：所有卡片公平排序，最愛卡片不享特權\n\n**🎯 排行榜系統升級**\n\n**📊 全新分類架構**\n• 「卡牌收集排行榜」→「卡牌相關排行榜」\n• 新增三大核心指標：\n  - **🍀 運氣指數**：綜合出貨率評估\n - **🎯 平均出貨抽數**：頂級卡獲取效率\n - **🏜️ 最長乾旱**：連續未出貨記錄\n\n### **🔧 系統優化與修復**\n\n• 修正玩家資訊頁面篩選時的收集率顯示問題\n• `/trade` 交易完成後新增「收藏按鈕」，方便直接加入最愛\n• `/trade` 發起者現可取消自己的交易\n• 改善交易指令的卡片ID識別準確性", "image_url": null, "buttons": [{"name": "媽的米米", "count": 3}, {"name": "我愛米米", "count": 0}, {"name": "==", "count": 0}, {"name": "窩操", "count": 1}]}, {"date": "2025-07-05", "title": "⏱️ GIF預覽幀自訂 & 📈 股票盈虧排行榜", "content": "• **`/profile` 設定新增「⏱️ 設定預覽幀」**\n  - 現在可以為您的主卡片及四張副卡片，手動設定GIF的預覽定格時間點 (單位為秒)。\n  - 不需要再靠賽刷新，或是看到卡片出現搞笑的畫面了！\n\n• **`/lb` (排行榜) 新增「股票總盈虧」排行**\n  - 現在可以查看基於已實現盈虧的玩家財富排行。\n\n• **`/portfolio` 交易歷史新增「總盈虧」統計**\n  - 在交易歷史記錄的頁尾，新增了所有已實現交易的總盈虧統計。\n\n 關於金庫&搶劫系統計畫有相關變動，\n詳情加入伺服器查看並投票 https://discord.gg/uZ2k43qpwB", "image_url": null, "buttons": [{"name": "我愛米米", "count": 0}, {"name": "太神啦", "count": 0}, {"name": "媽的米米", "count": 0}, {"name": "媽的我也虧太多", "count": 6}]}, {"date": "2025-07-04", "title": "📈 投資組合優化 & 💰 金庫/搶劫系統方案投票", "content": "• **`/portfolio` 指令整合「交易歷史」**\n  - 於投資組合介面新增「📜 交易歷史」按鈕，可查詢個人完整交易記錄。\n\n• **新增「已實現盈虧」計算**\n  - 交易歷史中的「賣出」與「回補」操作，現在會正確計算並顯示該筆交易的已實現盈虧(PNL)。\n  - 舊有的交易記錄已透過腳本回填，但部分資料可能不完整，將顯示為 N/A。\n\n### **【第二輪意見徵求】「金庫 & 搶劫」系統詳細方案預覽**\n\n各位玩家好，\n根據大家的意見，我們優化了「金庫 & 搶劫」系統的詳細方案，現公佈並徵求最終意見。\n### **🏰 第一部分：你的安全堡壘 —— 全新金庫系統**\n\n金庫它不再只是一個倉庫，而是你**個人安全的象徵**和**可以成長的堡壘**！\n\n**【核心機制】**\n*   **永久安全門檻：** 每個玩家都有一個「安全門檻」。**只要你的隨身現金低於此門檻，你將 100% 免疫搶劫！**\n*   **金庫即實力：** 升級你的金庫，將**永久提升**你的安全門檻！\n    *   **LV 1 金庫 (初始):** 安全門檻 **100,000** 油幣\n    *   **LV 10 金庫:** 安全門檻提升至 **280,000** 油幣\n    *   **LV 20 金庫:** 安全門檻提升至 **780,000** 油幣\n    *   **LV 30 金庫 (滿級):** 安全門檻高達 **1,980,000** 油幣！\n*   **安全的升級體驗：** **升級金庫的費用將優先從你的金庫餘額中直接支付。**\n\n### **⚔️ 第二部分：智慧與勇氣的較量 —— 深度搶劫玩法**\n\n搶劫不再是簡單的數字比拼，而是一場充滿變數的攻防大戲！\n\n**【參與資格】**\n為了確保所有參與者都對遊戲有基本了解，發起搶劫需要滿足以下任一經驗條件：\n*   `總抽卡次數 >= 1000 次`  **或**  `小遊戲總遊玩次數 >= 1000 次`\n\n**【核心玩法】**\n*   **目標列表 (`/rob list`)：** 系統會提供一份匿名的、從所有**超出自身安全門檻**的玩家中隨機加權選出的目標列表。你無法惡意鎖定單一玩家！\n*   **分級戰利品：** 搶劫成功後，你可能只「偷走一小部分」(10%)，也可能上演一場「世紀大劫案」(75%)！每次結果都充滿懸念。\n*   **豐富的戰術道具：** 商店將上架一系列戰術道具，為攻防兩端提供更多策略選擇。例如：\n    *   **防守方**可部署「安全掛鎖🔒」或「地雷💣」來保護財產。\n    *   **攻擊方**可使用「斷線鉗✂️」來應對特定防禦，或戴上「搶匪面具🎭」進行匿名作案。\n    *   更有「威士忌🥃」、「卡琵莉亞雞尾酒🍹」等高風險道具，等待大膽的你來發掘！\n\n### **💡 懶人包 & 核心 Q&A**\n\n*   **這個系統對我有什麼影響？**\n    *   **如果你是休閒/新手玩家：** 你的遊戲體驗**幾乎不受影響**。只要你隨身現金不超過你的安全門檻（初始 10 萬，可通過升級金庫輕鬆提升），你就**絕對安全**。這個系統反而給了你一個清晰的成長目標。\n    *   **如果你是活躍/核心玩家：** 這將是一個全新的、充滿挑戰和回報的日常玩法。它考驗你的資產管理能力、策略規劃和一點點勇氣。\n\n*   **我會不會一直被搶到退坑？**\n    *   **不會。** 我們有多重保護機制：\n        1.  **安全門檻**是你最堅實的護盾。\n        2.  **匿名列表**防止了惡意針對。\n        3.  **被搶後 24 小時保護期**讓你高枕無憂。\n        4.  **豐富的防禦道具**讓你擁有主動權。\n\n也歡迎加入伺服器來討論：https://discord.gg/uZ2k43qpwB", "image_url": null, "buttons": [{"name": "<PERSON><PERSON>【全力支持】", "count": 8}, {"name": "B.【基本同意，但有小建議】", "count": 0}, {"name": "C.【我還是不喜歡】", "count": 11}, {"name": "D.【無所謂/再看看】", "count": 3}]}, {"date": "2025-07-03", "title": "💰 「金庫」與「搶劫」系統提案", "content": "各位好。\n為了給未來的「開店」等新功能打下穩定的經濟基礎，我在此提出一套旨在增加貨幣回收管道、平衡遊戲經濟的系統方案，並徵求各位的意見。\n---\n### **背景：當前的經濟挑戰**\n目前的經濟模型，在面對未來產出型新系統（如開店）時，存在潛在的通膨風險。若要維持經濟穩定，有幾個可能的方向：\n1.  降低現有的每日/每小時產出。\n2.  提高新系統的參與門檻和成本。\n3.  引入新的貨幣回收與互動機制。\n方案 1 和 2 可能會降低遊戲的吸引力。因此，我主要想和大家探討方案 3 的可能性。\n---\n### **提案系統：「金庫」與「搶劫」**\n**一、 金庫 (Vault)**\n*   **功能**：一個 100% 安全的資金存放處，存入的資金免疫搶劫。\n*   **成本**：每次**存入**資金，收取 **3%** 的保管費。取出免費。\n*   **升級 `/upgrade_vault`**：可花費油幣升級金庫。更高等級的金庫，將提供更低的保管費或更高的存款上限。\n**二、 搶劫 (/rob)**\n*   **機制**：\n    *   使用 `/rob_list` 獲取一份隨機目標列表（只顯示錢包現金 > 10萬的活躍玩家）。\n    *   支付成本後，可對列表中的目標發起搶劫。\n    *   **成功**：搶走目標錢包 **10% - 25%** 的現金（有單次上限）。\n    *   **失敗**：損失成本並支付罰金。\n*   **平衡措施**：\n    *   **CD**：搶劫後有冷卻時間。\n    *   **保護**：被成功搶劫的玩家，在 24 小時內不會再成為目標。\n-# 以上所有提及的具體數值（如3%、10%-25%等）均為初步構想，未來可能會根據討論進行調整。\n---\n### **需要各位做出的選擇**\n引入此系統，將增加一個有效的貨幣回收管道，為未來的內容更新提供支持。但同時，它也會給遊戲帶來直接的 PvP 風險。\n請根據以下選項，表達您的看法。您的決定將直接影響遊戲的後續開發方向。\n**【投票選項】**\n*   **A.【同意實裝】** 支持引入「金庫 & 搶劫」系統以平衡經濟。\n*   **B.【同意，但希望有配套措施】** 支持引入，但希望先看到更詳細的配套規則或新手保護方案。\n*   **C.【反對，寧願降低產出】** 反對此 PvP 系統，我更傾向於直接降低每日/每小時的油幣獎勵。\n*   **D.【反對，維持現狀】** 反對任何形式的改動，接受未來可能出現的通膨風險。\n\n---\n**懶人包：**\n*   **問題**：未來「開店」等新功能可能導致遊戲幣通膨。\n*   **提案**：新增「金庫」與「搶劫」系統來回收貨幣。\n    *   **金庫**：安全存錢的地方，但要付 3% 手續費。\n    *   **搶劫**：可以搶其他玩家的錢（10%~25%），但有風險、CD 和保護機制。\n*   **你的選擇**：投票決定是否實裝此系統，或有其他想法。", "image_url": null, "buttons": [{"name": "<PERSON><PERSON>【同意實裝】", "count": 4}, {"name": "B.【同意，但希望有配套措施】", "count": 8}, {"name": "C.【反對，寧願降低產出】", "count": 1}, {"name": "D.【反對，維持現狀】", "count": 0}]}, {"date": "2025-07-03", "title": "📊 全新儀表板 & 指令優化", "content": "**📊 全新 `/stats` 儀表板**\n• `/draw_stats` 現已重構並更名為 `/stats`(沒看到指令請重開DC)\n• 整合「玩家資訊」、「抽卡」、「收藏」、「小遊戲」等多個統計頁面\n\n**📈 統計功能優化**\n• **運氣指數**：現在僅基於「頂級卡」的獲取率計算，定義更清晰\n\n**🔧 圖像處理系統優化**\n• 修復所有圖像生成功能的性能問題\n• 將CPU密集型的圖像處理操作移至背景線程執行\n\n**🃏 德州撲克 (Poker1v1) 體驗優化**\n• **簡化匹配流程**：進入匹配時，將直接更新原訊息，不再跳出多餘的提示。\n• **改善取消體驗**：取消匹配時，會直接在原訊息上顯示取消狀態，介面更整潔。", "image_url": null, "buttons": [{"name": "媽的米米", "count": 1}, {"name": "是要把我所有虧損全部秀出來484...", "count": 7}, {"name": "我愛米米", "count": 0}, {"name": "沒關係，就繼續不做開店", "count": 6}]}, {"date": "2025-07-02", "title": "系統調整 & 開發進度", "content": "🔄 **抽卡指令重新加入冷卻**\n• 觀察到有瘋狂抽卡行為導致系統頻繁超時，並可能觸發Discord限速，因此暫時為抽卡指令加回冷卻時間作為保險措施。\n\n**🏪 開店系統**\n• 系統銳意製作中...", "image_url": null, "buttons": [{"name": "哈哈是我啦", "count": 4}, {"name": "媽的米米", "count": 4}, {"name": "==", "count": 0}, {"name": "操", "count": 4}]}, {"date": "2025-07-01", "title": "🃏 新增百家樂 & 🎡 轉盤賭博遊戲", "content": "**🃏 百家樂**\n• 新增 `/baccarat` 百家樂遊戲\n• 經典賭場遊戲，可多人同時下注！\n• 房主開局後，其他玩家可在90秒內跟注\n• 90秒後或房主手動開始後，遊戲將自動進行\n\n**🎡 轉盤賭博**\n• 新增 `/spin` 轉盤賭博遊戲\n• 提供低、中、高三種風險等級，不同風險對應不同勝率與獎勵倍率。", "image_url": null, "buttons": [{"name": "賭博我來了", "count": 3}, {"name": "媽的米米", "count": 3}, {"name": "開店哩...要做了沒...", "count": 12}, {"name": "沒關係米米就繼續撈錢", "count": 7}]}, {"date": "2025-06-30", "title": "🎵新增音擊卡池", "content": "🕹️ **新增音擊卡池 - 總共1906張卡片**\n  • 來自SEGA旗下的街機遊戲，有很多合作活動\n  • 卡片原素材只有圖片，名字跟系列名稱都是圖像辨識硬幹的，如果有發現打錯的地方請跟我說\n\n• `/健康檢查`指令改名成-> `/機器人資訊`", "image_url": "https://cdn.discordapp.com/attachments/1381695249956732958/1388821840117629018/100090_E78FA0E6B4B2E5B3B620E69C89E6A096_E38282E381B5E38282E381B5E382B7E383A5E383BCE382BFE383BC.png?ex=686260a1&is=68610f21&hm=2ee2365464d4fd5c09e287a937c8ef7f42f7631d12e96b88bc65a1a0f551a0db&", "buttons": [{"name": "媽的米米", "count": 0}, {"name": "我愛米米", "count": 0}, {"name": "啥時要開店==", "count": 5}, {"name": "窩操是音遊佬", "count": 9}]}, {"date": "2025-06-29", "title": "專注開發開店系統，無功能更新", "content": "• 今天沒更新，開發開店系統中。\n\n📈 **市場觀察**\n  • 話說回來，女僕特斯價格跌到 900是三小...\n  • 市場的波動真是難以預測，提醒各位玩家謹慎投資。\n\n🤖 **AI 相關**\n  • AI 服務最近好像穩定多了。(偷偷說，模型是 Gemini 2.5 Pro 0605)\n  • 然後我偶爾會看到 Google 審核出現警告??\n  • 我自己測試傳一些蠻誇張的圖都沒事，到底是傳了多怪的圖才會被警告??", "image_url": null, "buttons": [{"name": "媽的米米", "count": 2}, {"name": "媽的開店趕緊", "count": 10}, {"name": "WTF", "count": 0}, {"name": "哈哈是我啦", "count": 2}]}, {"date": "2025-06-28", "title": "新增煉金術系統 & 商店價格更新", "content": "🧪 **新增高風險煉金術系統 (`/alchemy`)**\n  • **機制**: 投入油幣換取油票，結果完全隨機。\n  • **結果預覽**:\n    • `20%` 機率血本無歸 (回報率極低)\n    • `~70%` 機率保底小虧 (回報率略低於成本)\n    • `5%` 機率暴擊大賺 (回報率顯著高於成本)\n    • `0.15%` 機率傳奇大獎 (回報率極高)\n  • **門檻**: 最低投入 100,000 油幣。(這個系統適合給錢太多，但懶得抽卡，但想快速換油票的玩家。)\n\n🛒 **商店指定券價格更新**\n  • **定價模型變更**：引入新模型，指定券價格現在與該稀有度的「卡片總數」掛鉤。\n  • **價格影響**：卡池內卡片總數越多，指定券價格越高；反之則越低。\n  • **具體範例**：`main_R6`, `ua_R6` 等大型卡池價格上漲；`special_maid`, `hololive` 等小型卡池價格下降。\n\n所有價格已在商店內更新。\n\n**🎲 骰子遊戲更新**\n  • 骰子遊戲改為三骰子系統，新增押圍骰玩法\n  • 大小點數調整：大(11-17點)、小(4-10點)\n  • 圍骰賠率1:30，圍骰時大小押注失敗", "image_url": null, "buttons": [{"name": "誰錢這麼多==", "count": 21}, {"name": "媽的米米", "count": 3}, {"name": "RRRRRRRR", "count": 0}, {"name": "骰子...真的好強...", "count": 1}]}, {"date": "2025-06-27", "title": "🎉 重構完成！抽卡無限制 + 多項優化", "content": "**🚨 重要通知：寶可夢股票即將下市**\n• **寶可夢相關股票（PTCG等）將在1-2天內下市**\n• **建議行動**：如果覺得沒有希望的玩家請趕緊賣出，下市後會以極低價格回購\n\n**🎰 抽卡系統解除限制**\n• 移除抽卡冷卻限制，讓各位抽個爽！（但還是建議別按太快）\n• 優化抽卡速度，現在應該會比之前快\n\n**🔧 系統架構全面更新**\n• 把整個機器人架構更新了一遍，現在可以專心開發新功能了，也比較好維護\n• 因為上面這點，短時間內我盡可能測試了所有功能都算正常，但可能還是有一些問題，請使用問題回報\n\n**💰 `/sw` 賣卡指令優化**\n• 新增預覽確認機制：賣卡前會顯示詳細清單和預計收入\n\n** 修復與優化**\n• 修正 `/stock` 指令時間戳顯示問題\n• 修復買賣股票金額小數點顯示問題\n• 重製抽卡次數統計，統一為只保留 6/18 以後的詳細記錄\n• 優化 `/aw` 圖鑑顯示，footer新增許願數和加入日期信息\n• 優化 `/mw` 卡冊顯示，footer移除頁碼、簡化排序顯示，並將持有人數移至footer統一顯示\n• 修正了 `/mws` 系列列表最後一項的排版對齊問題\n• 修復 `/draw stats` 最常抽到統計顯示錯誤的問題\n\n**🌐 支援伺服器開放**\n• 機器人開放支援伺服器：https://discord.gg/Z8BXXqGR\n• 但目前荒廢中，頻道啥的都沒改，之後再調整，以後可能要交易可以來這邊，或是直接來找我問問題\n\n**⚠️ AI功能警告**\n• AI現在服務有點炸開(AI那邊的問題)，現在使用大概率會被轉到比較弱智的版本", "image_url": null, "buttons": [{"name": "媽的米米", "count": 5}, {"name": "蹲得越低...", "count": 4}, {"name": "天臺見", "count": 6}, {"name": "我愛米米", "count": 0}]}, {"date": "2025-06-26", "title": "🔧 今日暫停更新 - 重構進行中", "content": "**🛠️ 代碼重構進行中**\n• 今天暫停功能更新，專注於重構屎山代碼\n• 為了方便以後開發開店系統，正在優化代碼架構\n\n**🏪 開店系統預覽**\n即將推出的商店系統是一個自動化放置類遊戲：\n\n**🎮 遊戲流程：**\n• **收集材料** → **加工製作** → **放到商店** → **自動販售** → **結算收益**\n\n**⛏️ 初期玩法（手動模式）：**\n• 伐木、挖礦等資源收集（打指令操作）\n• 手動加工材料製作商品\n• 自己擺放商品到商店\n• 系統會定時自動購買玩家商品\n\n**🤖 後期解鎖（自動化）：**\n• **自動資源產出**：伐木場每小時產50木頭\n• **自動加工生產**：每小時50木頭 → 自動製作木椅\n• **自動商店管理**：自動補貨、自動結算\n\n**🎯 設計理念：**\n• 結合自動化遊戲 + 放置遊戲概念\n• 前期手動操作會比較枯燥，激勵玩家解鎖自動化功能\n\n**🎴 卡片系統融合：**\n• 商店系統會與現有卡片系統融合\n• 把卡片放進設施，不同稀有度效率不一樣\n• 卡片星級會影響效率加成，讓星級更有實用價值", "image_url": null, "buttons": [{"name": "媽的米米", "count": 1}, {"name": "開店美賣喔", "count": 9}, {"name": "不要商店好爆，需要更多賭博遊戲", "count": 1}, {"name": "我不愛米米", "count": 4}]}, {"date": "2025-06-25", "title": "📰 新聞系統功能更新", "content": "• `/news` 指令新增股票代碼參數 - 可查看指定股票新聞\n• 新增「我的做空相關」新聞過濾選項 - 快速查看做空倉位相關新聞\n\n**💰 股票交易手續費調整**\n• 股票交易手續費從 3% 降低至 1.5%\n\n**🎴 典藏卡池大更新**\n• 典藏卡池新增了 1,132 張全新卡片！\n\n**📊 抽卡統計系統優化**\n• 大幅優化 `/draw_stats` 查詢速度和穩定性", "image_url": "https://cdn7.mazoku.cc/cards/809635c6-66ac-417c-8cbf-4834e2ea09ed.webp", "buttons": [{"name": "媽的米米", "count": 0}, {"name": "窩操沒錢抽了", "count": 7}, {"name": "我愛米米", "count": 3}, {"name": "寶可夢是要倒閉了沒==", "count": 10}]}, {"date": "2025-06-24", "title": "📈 股票做空系統正式上線！", "content": "**🎯 全新股票做空功能**\n• 現在可以在 `/stock` 和 `/portfolio` 中進行做空操作\n• 做空機制：預測股價下跌，價格下跌時獲利\n• 手續費計算：與買賣股票相同（3%或每股0.05油幣，取較高者，最低5油幣）\n\n**💼 投資組合界面升級**\n• 新增「做空倉位」分頁，可切換查看持有股票和做空倉位\n• 做空盈虧計算：(做空價 - 現價) × 數量\n• 支援同時持有和做空同一檔股票\n\n**📖 補充油票獲取說明**\n• 在 `/help` 和 `/shop` 指令中補充油票獲取方式說明\n• 每花費100油幣抽卡可獲得1油票\n\n**📰 加強新聞數據準確性**\n• 修正AI生成新聞時會亂編數字的問題\n\n**🎫 隨機票券現在會抽到重複的**", "image_url": null, "buttons": [{"name": "媽的米米", "count": 0}, {"name": "媽的我是寶可夢難民", "count": 11}, {"name": "我愛米米", "count": 4}, {"name": "==", "count": 1}]}, {"date": "2025-06-23", "title": "🔧 小修BUG和未來更新預計", "content": "**🛡️ 優化系統各種錯誤處理，提高系統穩定性**\n\n**🔧 德撲邊緣情況修復**\n• 修正全押時對手還能過牌的問題\n• 現在會正確限制行動選項\n\n**💸 `/trade` 卡片交易介面小優化**\n• 新增手續費說明，讓玩家更清楚交易成本\n\n**📊 抽卡次數統計問題**\n• 再次確認是否要重製抽卡次數統計\n• 原本系統只記錄抽卡次數，沒有記錄抽到哪張卡的詳細資料\n• 6/18 新增的統計系統才開始記錄每張卡的抽卡詳情\n• 重製的話會統一成只保留 6/18 之後有詳細記錄的資料\n• 詳細原因可參考 6/19 的更新說明\n\n---\n\n今天主要專注在系統穩定性提升，來聊聊未來計畫好了\n\n**🔧 進行中的功能：**\n• **RPG系統** - 讓卡片可以養成升級，目前60%進度，架構完成但技能怪物內容是最難的部分，可能先放置不做\n• **混合池專屬卡池** - 預計出一個只有在混合池才抽得到的卡池，數量大概3~400張，沒辦法單獨抽\n\n**📋 還在構思階段的功能 (可能會優先開始做開店)：**\n• **開店功能** - 除了賭博外的賺錢方式，還在想怎麼設計\n• **市場交易** - 上架卡片等人買的系統，但以目前玩家數量需要優先做嗎?\n• **小遊戲擴充** - 增加更多遊戲類型\n• **成就系統** - 可能會有徽章在個人檔案裝飾\n• **賽季排行榜** - 每月重置小遊戲排名，前幾名給特殊獎勵", "image_url": null, "buttons": [{"name": "重製ok", "count": 6}, {"name": "別重製R", "count": 0}, {"name": "我愛米米", "count": 7}, {"name": "媽的米米", "count": 3}]}, {"date": "2025-06-22", "title": "🎰 一些小優化調整...", "content": "**📊 調整場次配置：**\n• 新手場：盲注 10/20，帶入 1,000~5,000\n• 普通場：盲注 100/200，帶入 10,000~50,000\n• 高手場：盲注 1,000/2,000，帶入 100,000~500,000\n\n**💸 轉錢指令增加手續費**\n• `/transfer` 轉錢指令現在會收取 3% 手續費\n• 參照交易卡片指令的手續費機制\n\n**📊 重製德撲排行榜**\n• 修復 all-in 次數和棄牌率統計問題\n• 現在會正確記錄每手牌的統計數據\n\n**🎯 抽卡統計系統優化**\n• 修正運氣指數計算：現在會根據用戶實際抽的卡池組合計算期望值\n• 統一頂級卡定義：按機率分類而非稀有度名稱，解決不同卡池比較不公平問題\n• 新增全服歐洲人紀錄：可查看最近20筆全服好卡記錄\n\n**🔧 修正HELP指令**\n• 修正拉霸機的過時倍率敘述", "image_url": null, "buttons": [{"name": "媽的米米", "count": 5}, {"name": "都沒人匹配是要玩個迪奧", "count": 10}, {"name": "我愛米米", "count": 1}, {"name": "非洲人前來報到", "count": 2}]}, {"date": "2025-06-21", "title": "🃏 德撲1V1系統上線啦！", "content": "**🎰 德州撲克1v1終於做好了！**\n終於把這個系統搞出來了，可以用 `/poker1v1` 來玩1v1德撲對戰\n\n**🎮 怎麼玩：**\n• 有三種場次：新手場、普通場、高手場\n• 可以選場次慢慢選，也可以直接快速匹配\n• 找不到對手會3分鐘後超時\n• 遊戲結束可以直接繼續匹配下一場\n\n**⚠️ 但是...**\n雖然我測試了很多次，感覺沒什麼大問題，但德撲的規則和情況實在太複雜了，肯定還有一堆我沒想到的邊緣情況和BUG\n\n所以如果遇到奇怪的問題請多多回報，我會盡快修復\n\n**💸 還有新增了轉錢功能**\n現在可以用 `/transfer` 指令轉油幣給其他玩家了。\n\n**🎰 拉霸機平衡性調整**\nBUFF了拉霸機的獲勝機率和賠付結構，現在會比較好賺。🎲\n\n**使用方法：**\n• `/poker1v1` - 選場次模式\n• `/poker1v1 tier:新手場 buyin:500` - 快速匹配\n• `/transfer user:@某人 amount:1000` - 轉錢給別人", "image_url": null, "buttons": [{"name": "德撲好耶", "count": 5}, {"name": "遇到BUG了操", "count": 2}, {"name": "我愛米米", "count": 1}, {"name": "又要騙錢?", "count": 1}]}, {"date": "2025-06-20", "title": "🔧 系統優化與重要澄清", "content": "**🔧 十連抽系統優化**\n• 移除了緩存機制，解決圖片加載問題\n• 簡化 embed 構建邏輯，提升穩定性\n\n**🃏 德州撲克1v1開發中**\n• 目前正在開發1v1隨機匹配德撲系統\n• 開發進度：**70%**\n\n• 敬請期待！\n\n**📊 重要澄清：關於股票系統的說明修正**\n各位玩家好，我發現之前的教學文件中有不一致的說明，造成了混淆，在此向大家道歉！\n\n**❌ 之前錯誤說法：**\n「股價受市場供需影響」\n\n**✅ 實際情況：**\n「股價由系統算法決定，玩家交易不影響價格」\n\n**📊 系統設計說明：**\n• 這個系統設計其實更像**外匯**或**商品期貨**\n• 而不是傳統的玩家VS玩家股票市場\n• 股價由隨機遊走+新聞影響決定\n• 每個人都是平等的價格接受者\n\n**🔧 已修正內容：**\n• `/help` 指令中的股票說明\n• `/stock` 教學按鈕內容\n• 所有相關教學文件\n\n**🎁 澄清補償：**\n每位玩家可獲得 **20,000 油幣 + 1,000 油票** 作為歉意\n\n**米米我真的不是故意要騙大家的 QQ**", "image_url": null, "buttons": [{"name": "媽的米米", "count": 6}, {"name": "操真的假的", "count": 10}, {"name": "我完了", "count": 0}, {"name": "還錢RRRRR", "count": 4}]}, {"date": "2025-06-19", "title": "🎯 系統優化與新功能", "content": "**視圖優化**\n• 優化 tower 視圖顯示\n• 統一餘額不足的顯示格式\n\n**📊 排行榜系統修復**\n• 修正排行榜名次顯示問題\n• 修復排行榜「過五關」的排序顯示\n\n**📈 全新統計功能**\n• 新增 `/draw_stats` 指令\n• 提供詳細的抽卡統計分析\n• 包含運氣指數、全服比較、最常抽到卡片等功能\n\n🤔 關於抽卡次數統一性問題**\n• 由於抽卡記錄功能是昨天才新增的，目前有兩套不同的抽卡次數：\n  📊 原本的抽卡次數：包含所有歷史（含已賣掉的卡）\n  📈 新統計系統：只有 6/18 後的記錄\n• 正在考慮是否要統一兩套系統：\n  🔄 **從現有收藏推算歷史**：根據目前持有的卡片推算抽卡記錄 + 重製原本的抽卡次數\n  📊 **保持現狀**：維持兩套不同的計算方式\n• ⚠️ 回填只能補齊目前持有的卡片，已賣掉的無法回填\n\n如果遇到打指令OR各種操作會顯示錯誤的問題，很有可能是被伺服器的AUTOMOD擋住了，要把米米加進伺服器才能解決", "image_url": null, "buttons": [{"name": "媽的爛米米", "count": 0}, {"name": "重製抽卡次數OK", "count": 6}, {"name": "不要重製ㄅ", "count": 3}, {"name": "沒差", "count": 1}]}, {"date": "2025-06-18", "title": "🔧 重大修復與功能更新", "content": "**功能更新**\n• 在`/健康檢查`指令中新增了`Gacha統計`頁面，可以查看詳細的卡池與玩家數據。\n\n**🃏 21點遊戲更新**\n• `/blackjack` 新增特殊勝利條件 **「過五關」**！當手牌達到`5張`且未爆牌時，將直接獲勝並獲得 **3倍** 賭注獎勵。\n\n**重大修復：**\n1. **終於真的修復抽卡會衝突問題了，嗎?** 原因超詭異，`response.send_message` 竟然會衝突？現在統一改用 `defer + followup` 模式，應該徹底解決了\n2. **修復隨機兌換券只會出現 CARD ID 比較前面的卡的問題** 😭 抱歉各位，這問題超大！已經兌換的各位可以截圖找我 `Honkomagake`，我可以把卡退掉補油票給各位\n\n**其他優化：**\n• 統一了所有遊戲的互動模式，避免併發問題\n• 提升了系統穩定性", "image_url": null, "buttons": [{"name": "媽的爛米米", "count": 15}, {"name": "退卡一下3Q", "count": 2}, {"name": "21點好耶", "count": 23}, {"name": "抽卡還是有問題欸", "count": 0}]}, {"date": "2025-06-17", "title": "系統更新與優化", "content": "**更新內容：**\n1. 優化了抽卡流程，現在如果還會抽到一半突然顯示\"只有原始用戶才能使用此按鈕\"，請回報一下，幹我也不知道這問題哪來的\n2. `/trade` 交易系統現在會過濾掉**最愛**卡片了，選單不會顯示出來。\n3. 穿搭&戰力分析指令CD延長到5秒(網路快炸開了:sob: )\n4. 現在卡牌排行榜的三個排行都新增了`按照卡池篩選的下拉式選單`，可以只看指定卡池的排行了", "image_url": null, "buttons": [{"name": "媽的爛米米", "count": 0}, {"name": "酷喔", "count": 0}, {"name": "還是有BUGㄟ", "count": 2}]}, {"date": "2025-06-16", "title": "🎮 系統更新", "content": "**更新內容：**\n1. 增加了 `/slot` 拉霸機~~(又一個來騙錢的)~~指令\n2. Hololive 卡池加入了 6/20 新出的補充包，共169張卡片\n3. 新增了 `/rateb` 戰力分析指令，就是穿搭分析的戰力分析版，不過目前還沒調教好，可能評價重點會有點怪，先放出來玩玩看\n\n 如果遇到拉霸機指令錯誤問題，那就是被伺服器的automod擋住了，要把米米加進伺服器。", "image_url": "https://cdn.discordapp.com/attachments/1336020673730187334/1383752585789308938/image.png?ex=684fef85&is=684e9e05&hm=edc6957771168859f46fbbfe67922e999cb5055c4727d30a1a2f3621ed8d53b7&", "buttons": [{"name": "媽的爛米米", "count": 13}, {"name": "窩操我要抽holo", "count": 6}, {"name": "都被米米騙光錢了4要抽個屌", "count": 37}]}]}