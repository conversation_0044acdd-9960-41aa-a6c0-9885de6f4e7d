"""
技能實例模型
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict


class SkillType(Enum):
    """技能類型枚舉"""

    ACTIVE = "ACTIVE"
    PASSIVE = "PASSIVE"
    INNATE_PASSIVE = "INNATE_PASSIVE"
    PRIMARY_ATTACK = "PRIMARY_ATTACK"


@dataclass
class SkillInstance:
    """技能實例模型"""

    skill_id: str
    skill_type: SkillType
    current_level: int = 1
    current_cooldown: int = 0  # 僅適用於主動技能

    async def get_definition(self, all_configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        從全局配置中獲取技能定義

        Args:
            all_configs: 全局配置數據

        Returns:
            技能在當前等級下的詳細定義
        """
        try:
            if (
                self.skill_type == SkillType.ACTIVE
                or self.skill_type == SkillType.PRIMARY_ATTACK
            ):
                return self._get_active_skill_definition(all_configs)
            elif self.skill_type == SkillType.PASSIVE:
                return self._get_passive_skill_definition(all_configs)
            elif self.skill_type == SkillType.INNATE_PASSIVE:
                return self._get_innate_passive_definition(all_configs)

            return {}

        except Exception:
            # 記錄錯誤但不拋出異常
            return {}

    def _get_active_skill_definition(
        self, all_configs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """獲取主動技能定義（增量式格式）"""
        active_skills = all_configs.get("active_skills", {})
        skill_config = active_skills.get(self.skill_id, {})

        if not skill_config:
            return {}

        return self._build_incremental_active_skill(skill_config)

    def _get_passive_skill_definition(
        self, all_configs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """獲取通用被動技能定義（增量式格式）"""
        passive_skills = all_configs.get("passive_skills", {})
        skill_config = passive_skills.get(self.skill_id, {})

        if not skill_config:
            return {}

        return self._build_incremental_passive_skill(skill_config)

    def _get_innate_passive_definition(
        self, all_configs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """獲取天賦被動技能定義（增量式格式）"""
        innate_skills = all_configs.get("innate_passive_skills", {})
        skill_config = innate_skills.get(self.skill_id, {})

        if not skill_config:
            return {}

        return self._build_incremental_innate_passive(skill_config)

    def _build_incremental_active_skill(
        self, skill_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """構建增量式主動技能定義"""
        # 計算當前等級的MP消耗和冷卻時間
        base_mp_cost = skill_config.get("base_mp_cost", 0)
        mp_cost_per_level = skill_config.get("mp_cost_per_level", 0)
        current_mp_cost = max(
            0, base_mp_cost + (self.current_level - 1) * mp_cost_per_level
        )

        base_cooldown = skill_config.get("base_cooldown_turns", 0)
        cooldown_reduction = skill_config.get("cooldown_reduction_per_level", 0)
        current_cooldown = max(
            0, base_cooldown - (self.current_level - 1) * cooldown_reduction
        )

        return {
            "name": skill_config.get("name", ""),
            "description_template": skill_config.get("description_template", ""),
            "target_type": skill_config.get("target_type", ""),
            "mp_cost": current_mp_cost,
            "cooldown_turns": current_cooldown,
            "effect_definitions": skill_config.get("base_effect_definitions", []),
            "tags": skill_config.get("tags", []),
        }

    def _build_incremental_passive_skill(
        self, skill_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """構建增量式被動技能定義"""
        return {
            "name": skill_config.get("name", ""),
            "description_template": skill_config.get("description_template", ""),
            "effect_blocks": skill_config.get("base_effects", []),
            "tags": skill_config.get("tags", []),
        }

    def _build_incremental_innate_passive(
        self, skill_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """構建增量式天賦被動技能定義"""
        # 使用新的 effects_by_star_level 結構
        effects_by_star = skill_config.get("effects_by_star_level", {})

        # 找到適用的星級配置（取不大於當前星級的最大鍵值）
        applicable_star_levels = [
            int(k) for k in effects_by_star.keys() if int(k) <= self.current_level
        ]
        all_effects = []

        if applicable_star_levels:
            max_applicable_level = max(applicable_star_levels)
            star_config = effects_by_star.get(str(max_applicable_level), {})
            all_effects = star_config.get("passive_effect_blocks", [])

        # 獲取描述模板
        description_by_star = skill_config.get("description_template_by_star_level", {})
        description_template = ""

        if description_by_star and applicable_star_levels:
            max_applicable_level = max(applicable_star_levels)
            description_template = description_by_star.get(
                str(max_applicable_level), ""
            )

        return {
            "name": skill_config.get("name", ""),
            "description_template": description_template,
            "effect_blocks": all_effects,
            "tags": skill_config.get("tags", []),
        }

    def is_usable(self, caster_mp: int) -> bool:
        """
        判斷技能是否可用

        Args:
            caster_mp: 施法者當前MP

        Returns:
            是否可用
        """
        # 被動技能總是"可用"（但不會被主動使用）
        if self.skill_type in [SkillType.PASSIVE, SkillType.INNATE_PASSIVE]:
            return True

        # 主動技能需要檢查冷卻和MP
        if self.current_cooldown > 0:
            return False

        # 這裡需要從配置中獲取MP消耗，暫時返回True
        # 實際實現中需要調用 get_definition 並檢查 mp_cost
        return True

    async def put_on_cooldown(self, all_configs: Dict[str, Any]) -> None:
        """
        將技能置於冷卻狀態

        Args:
            all_configs: 全局配置數據
        """
        if self.skill_type not in [SkillType.ACTIVE, SkillType.PRIMARY_ATTACK]:
            return

        definition = await self.get_definition(all_configs)
        cooldown_turns = definition.get("cooldown_turns", 0)
        self.current_cooldown = cooldown_turns

    async def get_mp_cost(self, all_configs: Dict[str, Any]) -> int:
        """
        獲取技能的MP消耗

        Args:
            all_configs: 全局配置數據

        Returns:
            MP消耗
        """
        if self.skill_type not in [SkillType.ACTIVE, SkillType.PRIMARY_ATTACK]:
            return 0

        definition = await self.get_definition(all_configs)
        return definition.get("mp_cost", 0)
