import glob
import os
import sys
from datetime import datetime


def get_sort_key(filepath):
    """從檔名或修改時間中獲取用於排序的鍵（datetime 物件）。"""
    filename = os.path.basename(filepath)
    name_part = os.path.splitext(filename)[0]
    parts = name_part.split("_")
    if len(parts) == 3 and parts[0] == "backup":
        timestamp_str = f"{parts[1]}_{parts[2]}"
        try:
            return datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
        except ValueError:
            pass  # 如果解析失敗，則繼續嘗試下一個方法

    try:
        return datetime.fromtimestamp(os.path.getmtime(filepath))
    except Exception:
        return datetime.min


def delete_old_backups(files_to_delete):
    """刪除指定的備份檔案。"""
    for file_path in files_to_delete:
        try:
            os.remove(file_path)
            print(f"Deleted old backup: {file_path}")
        except OSError as e:
            print(f"Error deleting file {file_path}: {e}")


def manage_backups(backup_dir, max_backups_str, backup_pattern="backup_*.dump"):
    """
    管理備份目錄，僅保留最新的 'max_backups' 個檔案。
    """
    backup_dir_cleaned = backup_dir.strip('"\'')
    backup_pattern_cleaned = backup_pattern.strip('"\'')

    if not os.path.isdir(backup_dir_cleaned):
        print(f"Error: Backup directory '{backup_dir_cleaned}' does not exist.")
        sys.exit(1)

    try:
        max_backups = int(max_backups_str)
        if max_backups <= 0:
            raise ValueError("max_backups must be a positive integer.")
    except ValueError as e:
        print(f"Error: Invalid value for max_backups ('{max_backups_str}'). {e}")
        sys.exit(1)

    search_path = os.path.join(backup_dir_cleaned, backup_pattern_cleaned)
    backup_files = glob.glob(search_path)

    if not backup_files:
        print(
            f"No backup files found matching '{backup_pattern_cleaned}' "
            f"in '{backup_dir_cleaned}'."
        )
        return

    try:
        backup_files.sort(key=get_sort_key)
    except Exception as e_sort:
        print(
            f"Warning: Error during sorting: {e_sort}. "
            "Attempting sort by modification time."
        )
        try:
            backup_files.sort(key=os.path.getmtime)
        except Exception as e_mtime_sort:
            print(
                f"Fatal: Could not sort files by modification time: {e_mtime_sort}. "
                "Aborting cleanup."
            )
            sys.exit(1)

    num_to_delete = len(backup_files) - max_backups
    if num_to_delete > 0:
        print(
            f"Found {len(backup_files)} backups. Keeping {max_backups}. "
            f"Deleting {num_to_delete} oldest..."
        )
        delete_old_backups(backup_files[:num_to_delete])
    else:
        print(
            f"Found {len(backup_files)} backups. No old backups to delete "
            f"(max_backups: {max_backups})."
        )


if __name__ == "__main__":
    if len(sys.argv) < 3:
        print(
            "Usage: python manage_backups.py <backup_directory> "
            "<max_backups_to_keep> [backup_pattern]"
        )
        print(
            'Example: python manage_backups.py "D:\\PostgreSQL_Backups" 72 '
            '"backup_*.dump"'
        )
        sys.exit(1)

    backup_dir_arg = sys.argv[1]
    max_backups_arg = sys.argv[2]
    backup_pattern_arg = sys.argv[3] if len(sys.argv) > 3 else "backup_*.dump"

    manage_backups(backup_dir_arg, max_backups_arg, backup_pattern_arg)
