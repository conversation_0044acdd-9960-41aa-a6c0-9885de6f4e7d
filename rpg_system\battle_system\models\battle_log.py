"""
戰鬥日誌模型
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional


@dataclass
class BattleLogEntry:
    """戰鬥日誌條目"""

    turn_number: int
    action_type: str  # SKILL_CAST, PASSIVE_PROC, STATUS_EFFECT_TICK, ENVIRONMENT
    actor_id: Optional[str]  # 行動者的instance_id
    target_ids: List[str] = field(default_factory=list)  # 目標的instance_id列表
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    # 可選的本地化支持
    message_template_key: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "turn_number": self.turn_number,
            "action_type": self.action_type,
            "actor_id": self.actor_id,
            "target_ids": self.target_ids,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "message_template_key": self.message_template_key,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BattleLogEntry":
        """從字典創建實例"""
        timestamp = (
            datetime.fromisoformat(data["timestamp"])
            if "timestamp" in data
            else datetime.now(timezone.utc)
        )

        return cls(
            turn_number=data["turn_number"],
            action_type=data["action_type"],
            actor_id=data.get("actor_id"),
            target_ids=data.get("target_ids", []),
            message=data.get("message", ""),
            details=data.get("details", {}),
            timestamp=timestamp,
            message_template_key=data.get("message_template_key"),
        )
