import asyncio
import time
from decimal import Decimal
from typing import Dict, List, Optional, Set

from config.app_config import get_settings
from gacha.repositories.card import master_card_repository
from gacha.services import market_price_service
from utils.logger import logger

# --- 全局狀態和配置 ---
price_update_cfg = get_settings().price_update_service
_update_queue: asyncio.Queue[int] = asyncio.Queue(
    maxsize=price_update_cfg.queue_max_size
)
_worker_task: Optional[asyncio.Task] = None
_active = False
BATCH_SIZE: int = price_update_cfg.batch_size
BATCH_INTERVAL: float = price_update_cfg.batch_interval_seconds


async def start_price_update_worker():
    """啟動價格更新背景工作程序。應由應用程式主循環調用。"""
    global _worker_task, _active
    if _worker_task is None or _worker_task.done():
        _active = True
        _worker_task = asyncio.create_task(_price_update_worker())
        logger.info("價格更新服務工作程序已啟動。")
    else:
        logger.warning("價格更新服務工作程序已在運行中。")


async def stop_price_update_worker():
    """優雅地停止價格更新背景工作程序。應由應用程式關閉鉤子調用。"""
    global _worker_task, _active
    _active = False
    if _update_queue:
        try:
            # 發送一個停止信號
            _update_queue.put_nowait(-1)
        except asyncio.QueueFull:
            logger.warning("嘗試添加停止信號時價格更新隊列已滿。")

    if _worker_task:
        try:
            await asyncio.wait_for(_worker_task, timeout=BATCH_INTERVAL + 5.0)
            logger.info("價格更新服務工作程序已成功停止。")
        except asyncio.TimeoutError:
            logger.warning("價格更新服務工作程序未能及時停止，正在取消。")
            _worker_task.cancel()
        except Exception as e:
            logger.error("停止價格更新服務時發生錯誤: %s", e, exc_info=True)
    _worker_task = None


async def schedule_price_update(card_ids: List[int]):
    """
    將卡片ID列表排入隊列以進行市場價格重新計算和更新。
    這是一個無狀態的接口，供其他服務調用。
    """
    if not _active and (_worker_task is None or _worker_task.done()):
        logger.warning("價格更新服務未激活。請考慮先啟動服務。")

    count = 0
    for card_id in card_ids:
        try:
            _update_queue.put_nowait(card_id)
            count += 1
        except asyncio.QueueFull:
            logger.warning("價格更新隊列已滿。卡片ID %s 及後續ID未能加入。", card_id)
            break
    if count > 0:
        pass


async def schedule_full_price_recalculation():
    """
    將所有主卡片ID排入隊列以進行全面的市場價格重新計算。
    此操作在背景執行緒中執行，並具有反壓機制，可安全處理大量數據。
    """
    if not _active and (_worker_task is None or _worker_task.done()):
        logger.warning("價格更新服務未激活，無法執行全卡重新計算。")
        return

    try:
        all_card_ids = await master_card_repository.get_all_master_card_ids()
        if not all_card_ids:
            logger.info("沒有需要重新計算價格的卡片。")
            return

        total_ids = len(all_card_ids)
        logger.info(
            "檢索到 %s 個卡片ID。開始在背景執行緒中進行穩健的入隊操作...", total_ids
        )

        def robust_schedule_sync(
            card_ids_list: List[int], loop: asyncio.AbstractEventLoop
        ):
            """
            一個健壯的同步函數，在背景執行緒中運行。
            它使用 run_coroutine_threadsafe 和 future.result() 來實現阻塞式提交，
            從而實現反壓（Back-pressure）。
            """
            count = 0
            for card_id in card_ids_list:
                while True:
                    try:
                        # 創建一個協程任務
                        coro = _update_queue.put(card_id)
                        # 將協程安全地提交到主事件循環執行，並獲取一個 future
                        future = asyncio.run_coroutine_threadsafe(coro, loop)
                        # 同步阻塞等待 future 的結果，並設置超時
                        future.result(timeout=10.0)
                        count += 1
                        if count % 1000 == 0:
                            logger.info("[背景入隊] 進度: %s / %s", count, total_ids)
                        break  # 成功放入隊列，跳出 while 循環
                    except asyncio.QueueFull:
                        # 理論上不應發生，因為 put() 會等待。但作為防禦性措施保留。
                        logger.warning(
                            "[背景入隊] 隊列已滿，正在等待... (此訊息不應頻繁出現)"
                        )
                        time.sleep(1)
                    except Exception as e:
                        logger.error(
                            "[背景入隊] 將卡片ID %s 入隊時發生意外錯誤: %s。正在重試...",
                            card_id,
                            e,
                            exc_info=True,
                        )
                        time.sleep(1)  # 等待一秒後重試
            logger.info(f"背景執行緒成功將全部 {count} 個ID放入更新隊列。")
            return count

        main_loop = asyncio.get_running_loop()
        # 使用 to_thread 執行這個阻塞的、健壯的提交任務
        await asyncio.to_thread(robust_schedule_sync, all_card_ids, main_loop)

        logger.info("全卡價格重新計算的排程請求已全部提交完成。")

    except Exception as e:
        logger.error("執行全卡價格重新計算排程時發生頂層錯誤: %s", e, exc_info=True)


async def _process_batch(card_ids_to_process: Set[int]):
    """
    處理一批卡片ID：計算新價格並在數據庫中更新它們。
    """
    if not card_ids_to_process:
        return

    num_ids = len(card_ids_to_process)
    try:
        new_prices: Dict[
            int, Decimal
        ] = await market_price_service.calculate_and_get_market_prices_for_ids(
            list(card_ids_to_process)
        )
        if not new_prices:
            return

        updated_rows = await master_card_repository.bulk_update_market_prices(
            new_prices
        )
        if updated_rows != len(new_prices):
            logger.warning(
                "[批次處理] 更新行數不匹配。預期 %s 行，實際 %s 行。有新價格的卡片ID: %s。初始批次大小: %s。",
                len(new_prices),
                updated_rows,
                list(new_prices.keys()),
                num_ids,
            )
    except Exception as e:
        logger.error(
            "[批次處理] 處理 %s 張卡片的價格更新批次時發生錯誤: %s",
            num_ids,
            e,
            exc_info=True,
        )


async def _price_update_worker():
    """
    持續從隊列中獲取卡片ID，將它們分批並處理的背景工作程序。
    """
    batch_card_ids: Set[int] = set()
    last_processed_time = asyncio.get_event_loop().time()

    while _active or not _update_queue.empty():
        try:
            timeout = None
            if batch_card_ids:
                time_since_last_process = (
                    asyncio.get_event_loop().time() - last_processed_time
                )
                timeout = max(0, BATCH_INTERVAL - time_since_last_process)

            card_id = await asyncio.wait_for(_update_queue.get(), timeout=timeout)

            if card_id == -1:  # 停止信號
                _update_queue.task_done()
                break

            batch_card_ids.add(card_id)
            _update_queue.task_done()

            if len(batch_card_ids) >= BATCH_SIZE:
                await _process_batch(batch_card_ids.copy())
                batch_card_ids.clear()
                last_processed_time = asyncio.get_event_loop().time()

        except asyncio.TimeoutError:
            if batch_card_ids:
                await _process_batch(batch_card_ids.copy())
                batch_card_ids.clear()
                last_processed_time = asyncio.get_event_loop().time()
        except asyncio.CancelledError:
            logger.info("價格更新工作程序被取消。")
            break
        except Exception as e:
            logger.error("價格更新工作程序發生意外錯誤: %s", e, exc_info=True)
            await asyncio.sleep(1)  # 避免在連續錯誤時快速循環

    # 處理在主循環結束後隊列中剩餘的任何項目
    if batch_card_ids:
        await _process_batch(batch_card_ids.copy())
        batch_card_ids.clear()

    logger.info("價格更新工作程序已完成其執行。")
