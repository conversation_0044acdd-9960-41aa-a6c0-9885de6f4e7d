"""
Gacha系統幫助命令 COG
實現/help指令，為用戶提供完整的gacha系統功能教學
使用兩層下拉式選單系統：主分類 → 子分類
"""

from typing import Dict, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

from gacha.exceptions import BusinessError
from utils.base_view import BaseView
from utils.logger import logger


class HelpCog(commands.Cog):
    """處理 /help 指令的 Cog，使用兩層下拉式選單系統"""

    # 主分類和對應的子分類
    HELP_CATEGORIES = {
        "gacha_system": {
            "name": "🎯 抽卡系統",
            "main_page_description": "抽卡、收藏、圖鑑、許願相關功能",  # 主頁面顯示
            "category_page_description": (
                "**🎯 抽卡運作機制：**\n"
                "• `/w` 單抽（50油），`/w multi:True` 十連抽（500油）\n"
                "• 稀有度：C → R → SR → SSR → UR → LR → EX\n"
                "• **各卡池最高稀有度：** 主卡池/Hololive/UNION ARENA/Pokemon TCG/WIXOSS/Shadowverse Evolve最高EX，典藏/典藏女僕/Ongeki最高UR，泳裝/情人節最高LR\n\n"
                "**📚 收藏管理系統：**\n"
                "• `/mw` 卡冊瀏覽，`/mwr` 稀有度統計，`/mws` 系列收集\n"
                "• `/favorite` 標記最愛卡片\n\n"
                "**✨ 許願系統：**\n"
                "• `/wish` 開啟許願介面，提高指定卡片抽中機率\n"
                "• 可擴充槽位（最多10個）和提升力度（最多10級）"
            ),  # 分類頁面顯示
            "dropdown_description": "抽卡、收藏、圖鑑、許願相關功能",  # 下拉選單顯示
            "subcategories": {
                "draw": {"name": "抽卡", "description": "單抽、十連抽功能"},
                "collection": {"name": "收藏", "description": "查看卡冊、稀有度統計"},
                "stats_dashboard": {
                    "name": "統計儀表板",
                    "description": "玩家資訊、抽卡、收藏、遊戲等綜合統計",
                },
                "profile": {"name": "檔案", "description": "個人檔案展示和設定"},
                "encyclopedia": {"name": "圖鑑", "description": "查看全圖鑑"},
                "wish": {"name": "許願", "description": "卡片許願系統"},
                "star_enhancement": {
                    "name": "升星",
                    "description": "卡片升星系統與圖鑑記錄",
                },
            },
        },
        "economy_system": {
            "name": "💰 經濟系統",
            "main_page_description": "交易、股票、獎勵、商店、煉金相關功能",
            "category_page_description": (
                "**💰 基礎收入來源：**\n"
                "• `/daily` 每日簽到（5000油），`/hourly` 每小時獎勵（2000油）\n"
                "• `/balance` 查看餘額\n\n"
                "**📈 股票投資系統：**\n"
                "• `/stock` 查看股票列表，`/portfolio` 查看投資組合\n"
                "• `/news` 查看市場新聞，影響股價波動\n"
                "• 股價由系統算法決定（隨機遊走+新聞影響）\n\n"
                "**🧪 煉金術系統：**\n"
                "• `/alchemy` 高風險投入油幣換取油票，最低投入100,000油幣\n\n"
                "**💱 交易系統：**\n"
                "• `/sw` 賣出卡片，`/trade` 玩家交易，`/transfer` 轉錢\n"
                "• 支援多種篩選和操作模式"
            ),
            "dropdown_description": "交易、股票、獎勵、商店、煉金相關功能",
            "subcategories": {
                "trade": {"name": "交易", "description": "賣卡和玩家交易"},
                "stock_market": {"name": "股票", "description": "股票市場投資"},
                "alchemy": {"name": "煉金術", "description": "高風險油幣轉油票"},
                "daily_hourly": {
                    "name": "每日/時獎勵",
                    "description": "每日和每小時獎勵",
                },
                "shop": {"name": "商店", "description": "油票商店"},
            },
        },
        "mini_games": {
            "name": "🎮 小遊戲",
            "main_page_description": "百家樂、拉霸機、挖礦、21點、三骰子、爬塔、轉盤、德州撲克1v1等遊戲",
            "category_page_description": (
                "**🃏 百家樂：** `/baccarat` 經典多人賭場遊戲，可押莊、閒、和\n\n"
                "**🎰 拉霸機：** `/slot` 3×3網格，8條獲勝線，連續3個相同符號獲勝\n\n"
                "**⛏️ 挖礦：** `/mines` 點擊土地挖掘，避開地雷，可隨時提現\n\n"
                "**🃏 21點：** `/blackjack` 讓手牌接近21點但不超過\n\n"
                "**🎲 三骰子：** `/dice` 三骰子大小遊戲，大(11-17點)、小(4-10點)、押圍骰(三個相同)\n\n"
                "**🏗️ 爬塔：** `/tower` 8層塔，每層選擇格子，選中金幣格完成該層\n\n"
                "**🎡 轉盤：** `/spin` 選擇風險等級轉動轉盤，低風險75%勝率，中風險50%勝率，高風險25%勝率\n\n"
                "**🃏 德州撲克1v1：** `/poker1v1` 1v1德州撲克對戰，三種場次等級"
            ),
            "dropdown_description": "百家樂、拉霸機、挖礦、21點、三骰子、爬塔、轉盤、德州撲克1v1等遊戲",
            "subcategories": {
                "baccarat": {"name": "百家樂", "description": "經典多人賭場遊戲"},
                "slot": {"name": "拉霸機", "description": "拉霸機遊戲"},
                "mines": {"name": "挖礦", "description": "尋寶礦區遊戲"},
                "blackjack": {"name": "21點", "description": "21點遊戲"},
                "dice": {"name": "骰子", "description": "骰子遊戲"},
                "tower": {"name": "爬塔", "description": "爬塔遊戲"},
                "spin": {"name": "轉盤", "description": "風險轉盤賭博遊戲"},
                "poker1v1": {"name": "德州撲克1v1", "description": "1v1德州撲克對戰"},
            },
        },
        "entertainment": {
            "name": "🎭 娛樂指令",
            "main_page_description": "AI問答、穿搭評分、戰力分析、GIF圖製作、AI文字冒險",
            "category_page_description": (
                "**🤖 AI問答：** `/ask` 向AI助手提問，支援圖片分析\n\n"
                "**👗 穿搭評分：** `/rate` AI評分穿搭照片，1-10分\n\n"
                "**⚔️ 戰力分析：** `/rateb` AI分析角色戰力，SSS-D等級評定\n\n"
                "**🎬 GIF製作：** `/gif` 製作個人化GIF動畫\n\n"
                "**📖 文字冒險：** `/story start` 開始AI互動故事，支援多故事管理和分享功能"
            ),
            "dropdown_description": "AI問答、穿搭評分、戰力分析、GIF圖製作、AI文字冒險故事系統",
            "subcategories": {
                "ai_qa": {"name": "AI問答", "description": "智能問答助手"},
                "outfit_rating": {"name": "穿搭評分", "description": "AI穿搭評分系統"},
                "power_analysis": {"name": "戰力分析", "description": "AI戰力分析系統"},
                "gif_creation": {"name": "GIF製作", "description": "頭像GIF圖製作"},
                "story_adventure": {
                    "name": "AI文字冒險",
                    "description": "互動式AI故事創作",
                },
            },
        },
        "rules": {
            "name": "📜 機器人使用規範",
            "main_page_description": "核心規則、遊戲系統規則、處分機制、問題回報",
            "category_page_description": (
                "**🤖 機器人遊戲系統規範**\n\n"
                "**� 核心規則：** 禁止開小號、自動化濫用、漏洞回報等重要規則\n\n"
                "**📋 遊戲系統規則：** 禁止詐騙、遊戲外交易等遊戲相關規範\n\n"
                "**⚖️ 處分機制：** 違規處分等級與申訴流程\n\n"
                "**🐛 問題回報：** 機器人故障或錯誤的回報方法\n\n"
                "**💡 遵守使用規範，享受最佳遊戲體驗**"
            ),
            "dropdown_description": "核心規則、遊戲系統規則、處分機制、問題回報",
            "subcategories": {
                "usage_rules": {
                    "name": "核心規則",
                    "description": "禁止開小號、自動化濫用、漏洞回報",
                },
                "limitations": {
                    "name": "遊戲系統規則",
                    "description": "禁止詐騙、遊戲外交易等規範",
                },
                "violation_handling": {
                    "name": "處分機制",
                    "description": "違規處分等級與申訴流程",
                },
                "bug_reporting": {
                    "name": "問題回報",
                    "description": "機器人故障與錯誤回報",
                },
            },
        },
    }

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("HelpCog loaded successfully.")

    def _create_main_help_embed(
        self, user: Union[discord.User, discord.Member]
    ) -> discord.Embed:
        """創建主幫助頁面 embed"""
        embed = discord.Embed(
            title="",
            description="歡迎來到Gacha系統！請使用下方的選單來探索各種功能。\n",
            color=discord.Color.blue(),
        )
        embed.set_author(name="Gacha系統功能說明", icon_url=user.display_avatar.url)
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1375186010798948463/notification.webp?ex=6830c546&is=682f73c6&hm=bb708ce4a788afebba8c1d8451720090b578497608c673b5f6106d615be686c1&"
        )

        # 改善排版：使用更好的視覺分隔和格式
        categories_list = list(self.HELP_CATEGORIES.items())

        # 第一行：抽卡系統和經濟系統
        if len(categories_list) >= 2:
            embed.add_field(
                name=categories_list[0][1]["name"],
                value=f"📋 {categories_list[0][1]['main_page_description']}\n\u200b",
                inline=True,
            )
            embed.add_field(
                name=categories_list[1][1]["name"],
                value=f"📋 {categories_list[1][1]['main_page_description']}\n\u200b",
                inline=True,
            )
            # 空欄位強制換行
            embed.add_field(name="\u200b", value="\u200b", inline=True)

        # 第二行：小遊戲和娛樂指令
        if len(categories_list) >= 4:
            embed.add_field(
                name=categories_list[2][1]["name"],
                value=f"📋 {categories_list[2][1]['main_page_description']}\n\u200b",
                inline=True,
            )
            embed.add_field(
                name=categories_list[3][1]["name"],
                value=f"📋 {categories_list[3][1]['main_page_description']}\n\u200b",
                inline=True,
            )
            # 空欄位強制換行
            embed.add_field(name="\u200b", value="\u200b", inline=True)

        # 如果還有更多分類，繼續添加
        for i in range(4, len(categories_list)):
            category_data = categories_list[i][1]
            embed.add_field(
                name=category_data["name"],
                value=f"📋 {category_data['main_page_description']}\n\u200b",
                inline=True,
            )

        # 添加使用說明
        embed.add_field(
            name="📖 使用說明",
            value=(
                "• 選擇**主分類**查看該分類下的所有功能\n"
                "• 選擇**子分類**查看具體功能的詳細說明\n"
                "• 隨時可以切換不同的分類和子分類"
            ),
            inline=False,
        )

        embed.set_footer(text="💡 使用下方的主分類選單開始探索功能")
        return embed

    def _create_category_help_embed(
        self,
        user: Union[discord.User, discord.Member],
        category_key: str,
        subcategory_key: Optional[str] = None,
    ) -> discord.Embed:
        """創建特定分類或子分類的幫助 embed"""
        category_data = self.HELP_CATEGORIES[category_key]

        embed = discord.Embed(color=discord.Color.blue())
        embed.set_author(name="Gacha系統功能說明", icon_url=user.display_avatar.url)
        embed.set_thumbnail(url="https://cdn.dev.conquest.bot/thumbnails/transfer.png")

        if subcategory_key:
            # 顯示特定子分類的詳細說明
            subcategories = category_data.get("subcategories", {})
            if isinstance(subcategories, dict) and subcategory_key in subcategories:
                subcategory_data = subcategories[subcategory_key]
                embed.title = f"{category_data['name']} - {subcategory_data['name']}"

                # 根據子分類顯示具體內容
                content = self._get_subcategory_content(subcategory_key)
                embed.add_field(
                    name=content["title"], value=content["content"], inline=False
                )
            else:
                embed.title = "錯誤"
                embed.description = "找不到指定的子分類"
        else:
            # 顯示該主分類下的所有子分類
            category_name = category_data.get("name", "未知分類")
            if isinstance(category_name, str):
                embed.title = category_name
            else:
                embed.title = "未知分類"

            category_desc = category_data.get("category_page_description", "")
            if isinstance(category_desc, str):
                embed.description = f"{category_desc}\n\n請選擇子分類來查看詳細說明："
            else:
                embed.description = "請選擇子分類來查看詳細說明："

            subcategories = category_data.get("subcategories", {})
            if isinstance(subcategories, dict):
                for _, sub_data in subcategories.items():
                    embed.add_field(
                        name=f"📋 {sub_data['name']}",
                        value=sub_data["description"],
                        inline=True,
                    )

        embed.set_footer(text="使用下方的選單切換分類或子分類")
        return embed

    def _get_subcategory_content(self, subcategory_key: str) -> Dict[str, str]:
        """獲取子分類的詳細內容"""
        content_map = {
            "draw": {
                "title": "🎯 抽卡系統 - `/w`",
                "content": (
                    "**基本功能：**\n"
                    "`/w` - 單抽卡片 (50油)\n"
                    "`/w multi:True` - 十連抽 (500油)\n\n"
                    "**卡池類型與費用：**\n"
                    "- 混合池 - 50油 (包含主卡池+典藏池)\n"
                    "- 典藏池 - 140油 (最高UR稀有度)\n"
                    "- 泳裝池 - 130油 (最高LR稀有度)\n"
                    "- 情人節卡池 - 150油 (最高LR稀有度)\n"
                    "- 典藏女僕卡池 - 160油 (最高UR稀有度)\n"
                    "- Hololive卡池 - 150油 (最高EX稀有度)\n"
                    "- UNION ARENA卡池 - 120油 (最高EX稀有度)\n"
                    "- Pokemon TCG卡池 - 90油 (最高EX稀有度)\n"
                    "- WIXOSS卡池 - 140油 (最高EX稀有度)\n"
                    "- Ongeki卡池 - 140油 (最高UR稀有度)\n"
                    "- Shadowverse Evolve卡池 - 170油 (最高EX稀有度)\n\n"
                    "**稀有度說明：**\n"
                    "C → R → SR → SSR → UR → LR → EX (由低到高)"
                ),
            },
            "collection": {
                "title": "📚 收藏系統 - `/mw`、`/mwr` 和 `/mws`",
                "content": (
                    "**查看指令：**\n"
                    "`/mw` - 查看卡冊\n"
                    "`/mwr` - 查看稀有度統計\n"
                    "`/mws` - 查看系列收集\n\n"
                    "**主要篩選：**\n"
                    "- 頁碼、排序、稀有度\n"
                    "- 卡池、系列、ID、名稱\n"
                    "- 顯示重複卡片\n\n"
                    "**最愛卡片篩選：**\n"
                    "- **全部卡片** (`favorite_filter:all`)：顯示所有卡片（預設）\n"
                    "- **僅最愛卡片** (`favorite_filter:favorites`)：只顯示標記為最愛的卡片\n"
                    "- **僅非最愛卡片** (`favorite_filter:non_favorites`)：只顯示未標記為最愛的卡片\n\n"
                    "**顯示模式：**\n"
                    "- **最愛優先模式** (`favorite_priority`)：最愛卡片優先顯示（預設：是，最愛卡片在前）\n"
                    "- **列表模式** (`list_mode`)：一頁顯示多張卡片（預設：否，一頁一張卡片）\n\n"
                    "**最愛功能：**\n"
                    "- 使用 `/favorite` 指令標記最愛卡片\n"
                    "- 最愛卡片會顯示在最前面（最愛優先模式下）\n"
                    "- 最愛卡片之間可調整順序"
                ),
            },
            "stats_dashboard": {
                "title": "📊 統計儀表板 - `/stats`",
                "content": (
                    "`/stats` - 查看綜合統計儀表板\n\n"
                    "**統計模組：**\n"
                    "• **玩家資訊**：帳號資訊、資產餘額、核心統計總覽\n"
                    "• **抽卡統計**：抽卡次數、稀有度分布、運氣分析、全服比較\n"
                    "• **收藏統計**：卡冊完成度、稀有度進度、卡池收集進度\n"
                    "• **小遊戲統計**：遊戲場次、勝率、盈虧、排名數據\n"
                    "• **全服歐洲人**：最近高稀有度抽卡記錄"
                ),
            },
            "profile": {
                "title": "🎴 檔案系統 - `/profile`",
                "content": (
                    "**檔案指令：**\n"
                    "`/profile` - 查看自己的檔案\n"
                    "`/profile user:@某人` - 查看其他人的檔案\n"
                    "`/profile edit:True` - 開啟檔案設定介面\n"
                    "`/profile background_image:<圖片>` - 設定背景圖片\n\n"
                    "**檔案功能：**\n"
                    "• 個人資訊展示：油量、油票、總抽卡數、圖鑑完成度\n"
                    "• 主展示卡片：設定1張主要展示卡片\n"
                    "• 副展示卡片：設定最多4張副展示卡片\n"
                    "• 自訂背景：上傳背景圖片\n"
                    "• 個性簽名：設定個性簽名\n"
                    "• 按讚互動：為其他玩家按讚（1小時冷卻）"
                ),
            },
            "encyclopedia": {
                "title": "📖 圖鑑系統 - `/aw`",
                "content": (
                    "**圖鑑指令：**\n"
                    "• `/aw` - 開啟圖鑑瀏覽器\n"
                    "• `/aw card_id:[ID]` - 查看指定卡片\n"
                    "• `/aw card_name:[名稱]` - 搜尋卡片名稱\n"
                    "• `/aw pool_type:[卡池]` - 篩選卡池\n"
                    "• `/aw rarity:[稀有度]` - 篩選稀有度\n"
                    "• `/aw series_name:[系列]` - 篩選系列\n\n"
                    "**圖鑑功能：**\n"
                    "• 卡片資料：顯示卡片資訊、稀有度、系列\n"
                    "• 最高星級記錄：顯示最高星級達成者和時間\n"
                    "• 自定義描述：顯示玩家設置的描述\n"
                    "• 多重篩選：可同時使用多個條件\n"
                    "• 排序功能：支援按稀有度、ID、名稱排序"
                ),
            },
            "wish": {
                "title": "✨ 許願系統 - `/wish`",
                "content": (
                    "**許願指令：**\n"
                    "`/wish` - 開啟許願系統界面\n\n"
                    "**許願功能：**\n"
                    "• 添加許願：點擊按鈕輸入卡片ID\n"
                    "• 移除許願：點擊按鈕輸入卡片ID\n"
                    "• 擴充槽位：花費油幣增加槽位數量\n"
                    "• 提升力度：花費油幣提升權重倍數\n\n"
                    "**許願機制：**\n"
                    "• 許願卡片在抽卡時有更高機率\n"
                    "• 力度等級越高，權重倍數越大\n"
                    "• 抽中許願卡時會有特殊標記\n"
                    "• 默認1個槽位，最多擴充到10個\n"
                    "• 默認1級力度，最多提升到10級"
                ),
            },
            "trade": {
                "title": "💱 交易系統 - `/sw`、`/trade` 和 `/transfer`",
                "content": (
                    "**💰 賣卡系統 (`/sw`)：**\n"
                    "• `/sw` - 開啟賣卡介面\n"
                    "• 支援篩選：卡片ID、名稱、系列、稀有度、卡池類型\n"
                    "• 三種模式：`one`(賣1張)、`leave_one`(賣到剩1張)、`all`(全部賣出)\n"
                    "• 最愛卡片預設受保護\n\n"
                    "**🤝 玩家交易系統 (`/trade`)：**\n"
                    "• 卡片換油幣：`/trade user:玩家 offer_card_id:卡片ID price:金額`\n"
                    "• 支援數量設定：`offer_quantity` 參數\n"
                    "• 交易需要雙方確認，收取3%手續費（最低10油幣）\n\n"
                    "**💸 轉錢系統 (`/transfer`)：**\n"
                    "• `/transfer user:玩家 amount:金額` - 轉錢給其他玩家\n\n"
                    "**🛡️ 安全機制：**\n"
                    "• 使用 `/favorite card_id:卡片ID` 設為最愛\n"
                    "• 最愛卡片在批量賣卡時會被跳過"
                ),
            },
            "stock_market": {
                "title": "📈 股票市場系統 - `/stock`、`/portfolio` 和 `/news`",
                "content": (
                    "**📊 股票查看系統：**\n"
                    "• `/stock` - 查看股票列表，包含價格和漲跌幅\n"
                    "• 股票狀態：Active(正常交易)、ST(特別處理)、Delisted(已退市)\n\n"
                    "**💼 投資組合管理：**\n"
                    "• `/portfolio` - 查看持股、市值、盈虧統計\n"
                    "• 顯示持有數量、平均成本、當前市值\n"
                    "• 計算總資產估值（油幣餘額 + 股票市值）\n\n"
                    "**📰 市場新聞系統：**\n"
                    "• `/news` - 查看市場新聞和公告\n"
                    "• 新聞會影響股票價格波動\n\n"
                    "**💰 交易機制：**\n"
                    "• 在股票詳情頁面點擊「買入」或「賣出」按鈕\n"
                    "• 股價由系統算法決定（隨機遊走+新聞影響）"
                ),
            },
            "alchemy": {
                "title": "🧪 煉金術系統 - `/alchemy`",
                "content": (
                    "**🧪 煉金術機制：**\n"
                    "• `/alchemy amount:[金額]` - 投入油幣換取油票\n"
                    "• 最低投入：100,000油幣\n"
                    "• 結果完全隨機，高風險高回報\n\n"
                    "**📊 機率分佈：**\n"
                    "• 20% 機率血本無歸\n"
                    "• ~70% 機率保底小虧\n"
                    "• 5% 機率暴擊大賺\n"
                    "• 0.15% 機率傳奇大獎"
                ),
            },
            "daily_hourly": {
                "title": "💰 每日/時獎勵系統 - `/daily` 和 `/hourly`",
                "content": (
                    "**💎 每日獎勵系統：**\n"
                    "• `/daily` - 領取每日簽到獎勵（5000油幣）\n"
                    "• 每24小時可領取一次\n\n"
                    "**⏰ 每小時獎勵系統：**\n"
                    "• `/hourly` - 領取每小時獎勵（2000油幣）\n"
                    "• 每60分鐘可領取一次\n\n"
                    "**💳 餘額查詢：**\n"
                    "• `/balance` - 查看當前油幣和油票餘額"
                ),
            },
            "shop": {
                "title": "🛍️ 油票商店系統 - `/shop`",
                "content": (
                    "**🏪 商店系統：**\n"
                    "• `/shop` - 開啟油票商店\n"
                    "• 使用油票兌換物品\n\n"
                    "**🎫 油票來源：**\n"
                    "• 抽卡回饋：每花費100油幣抽卡可獲得1油票\n"
                    "• 透過 `/w` 抽卡指令自動累積\n\n"
                    "**🎫 兌換券分類：**\n"
                    "• 指定券：可選擇特定卡片（價格較高）\n"
                    "• 隨機券：隨機獲得指定稀有度卡片（價格較低）\n"
                    "• 涵蓋稀有度：EX、LR、UR、SSR、SR、R、C\n"
                    "• 支援不同卡池：主卡池、典藏池、泳裝池等\n\n"
                    "**💰 價格範例：**\n"
                    "• 主卡池 EX 指定券：75000油票\n"
                    "• 主卡池 EX 隨機券：5000油票\n"
                    "• 主卡池 LR 指定券：15000油票\n"
                    "• 主卡池 LR 隨機券：1000油票"
                ),
            },
            "slot": {
                "title": "🎰 拉霸機遊戲 - `/slot`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/slot amount:[金額]` - 開始拉霸機遊戲\n\n"
                    "**遊戲機制：**\n"
                    "• 3×3網格，8條獲勝線（3水平、3垂直、2對角線）\n"
                    "• 連續3個相同符號即可獲勝\n"
                    "• 全螢幕9個相同符號為超級大獎\n\n"
                    "**符號倍數：**\n"
                    "• 幸運7（100倍）、四葉草（30倍）、鑽石（15倍）、鈴鐺（8倍）\n"
                    "• 櫻桃（0.5倍）、葡萄（0.4倍）、西瓜（0.3倍）、檸檬（0.2倍）\n\n"
                    "**獎勵計算：**\n"
                    "• 對角線：基礎倍數 × 3.8倍\n"
                    "• 直線：基礎倍數 × 7.0倍\n"
                    "• 全螢幕：基礎倍數 × 250倍\n\n"
                    "**計算公式：**\n"
                    "• 最終獎勵 = 下注金額 × 獲勝線倍數 × 符號倍數"
                ),
            },
            "mines": {
                "title": "⛏️ 尋寶礦區遊戲 - `/mines`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/mines` - 開始挖礦遊戲\n\n"
                    "**難度選擇：**\n"
                    "• 簡單模式：3顆地雷，25格土地\n"
                    "• 中等模式：7顆地雷，25格土地\n"
                    "• 困難模式：12顆地雷，25格土地\n\n"
                    "**遊戲機制：**\n"
                    "• 點擊土地格子進行挖掘，避開隱藏的地雷\n"
                    "• 每挖到安全土地，獎勵倍數增加\n"
                    "• 可隨時提現當前累積獎勵\n"
                    "• 踩到地雷遊戲結束，損失全部下注金額\n\n"
                    "**特殊獎勵：**\n"
                    "• 挖掘過程中可能發現金幣或星星\n"
                    "• 特殊獎勵立即發放"
                ),
            },
            "tower": {
                "title": "🏗️ 爬塔遊戲 - `/tower`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/tower` - 開始爬塔遊戲\n\n"
                    "**難度選擇：**\n"
                    "• 簡單模式：每層1個TNT + 3個金幣 (75%成功率)\n"
                    "• 中等模式：每層2個TNT + 2個金幣 (50%成功率)\n"
                    "• 困難模式：每層3個TNT + 1個金幣 (25%成功率)\n\n"
                    "**遊戲機制：**\n"
                    "• 8層塔，每層4個格子，從底層向頂層爬\n"
                    "• 每層選擇4個格子中的一個\n"
                    "• 選中金幣格完成該層並獲得獎勵\n"
                    "• 完成整層後可選擇提現或繼續挑戰\n"
                    "• 踩到TNT遊戲結束，失去所有未提現獎勵"
                ),
            },
            "blackjack": {
                "title": "🃏 21點遊戲 - `/blackjack`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/blackjack bet:[金額]` - 開始21點遊戲\n\n"
                    "**牌面計算：**\n"
                    "• 數字牌：2-10按面值計算\n"
                    "• 人頭牌：J、Q、K都算10點\n"
                    "• A牌：可算1點或11點（自動選擇最有利值）\n"
                    "• 目標：讓手牌點數接近21點但不超過\n\n"
                    "**遊戲流程：**\n"
                    "1. 下注後發給你和莊家各2張牌\n"
                    "2. 你的牌全部可見，莊家有1張暗牌\n"
                    "3. 選擇「要牌(Hit)」或「停牌(Stand)」\n"
                    "4. 莊家16點以下必須要牌\n\n"
                    "**勝負判定：**\n"
                    "• 過五關：手牌達到5張且未爆牌，獲得3倍獎勵\n"
                    "• Blackjack：起手21點，獲得2.5倍獎勵\n"
                    "• 一般獲勝：點數比莊家高且不超過21，獲得2倍獎勵\n"
                    "• 平局：與莊家點數相同，退還下注金額"
                ),
            },
            "dice": {
                "title": "🎲 三骰子大小遊戲 - `/dice`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/dice bet:[金額] choice:[大/小/押圍骰]` - 開始三骰子遊戲\n\n"
                    "**遊戲規則：**\n"
                    "• 系統投擲三顆六面骰子並計算總和\n"
                    "• **大**：總點數 11-17\n"
                    "• **小**：總點數 4-10\n"
                    "• **押圍骰**：三個點數相同（賠率 1:30）\n"
                    "• ⚠️ **注意**：如果開出圍骰，押注「大」或「小」均視為莊家贏！\n\n"
                    "**獎勵機制：**\n"
                    "• 押大小獲勝：獲得下注金額的2倍\n"
                    "• 押圍骰獲勝：獲得下注金額的31倍\n"
                    "• 失敗：損失全部下注金額"
                ),
            },
            "baccarat": {
                "title": "🃏 百家樂 - `/baccarat`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/baccarat amount:[金額] on:[閒家/莊家/和局]` - 開設一局百家樂\n\n"
                    "**遊戲規則：**\n"
                    "• 預測「閒家」或「莊家」哪一方的點數總和最接近9點為勝。也可押「和局」。\n\n"
                    "**點數計算：**\n"
                    "• **A** 算 1 點。\n"
                    "• **10, J, Q, K** 算 0 點。\n"
                    "• **2-9** 按牌面點數計算。\n"
                    "• 總和超過9時，只取個位數 (例如 7+8=15，計為5點)。\n\n"
                    "**補牌規則：**\n"
                    "1. **例牌**：任何一方首兩張牌合計8或9點，即為「例牌」，遊戲結束，不再補牌。\n"
                    "2. **閒家**：總點數 0-5 點補牌，6-7 點停牌。\n"
                    "3. **莊家**：根據閒家是否補牌及所補牌的點數，按固定規則決定是否補牌 (遊戲內會自動處理)。\n\n"
                    "**賠率與結算：**\n"
                    "• **押閒家贏**：賠率 1:1\n"
                    "• **押莊家贏**：賠率 1:0.95 (莊家贏需付5%佣金)\n"
                    "• **押和局贏**：賠率 1:8\n"
                    "• 若開出和局，押注「閒家」或「莊家」的賭金將被退還。\n\n"
                    "**多人遊玩：**\n"
                    "• 房主開局後，其他玩家可在90秒內點擊按鈕加入下注。\n"
                    "• 遊戲將在90秒後或房主手動開始後自動進行。"
                ),
            },
            "spin": {
                "title": "🎡 轉盤賭博遊戲 - `/spin`",
                "content": (
                    "**遊戲指令：**\n"
                    "• `/spin bet:[金額] risk:[風險等級]` - 開始轉盤遊戲\n\n"
                    "**風險等級與勝率：**\n"
                    "• **低風險**：75% 勝率\n"
                    "• **中風險**：50% 勝率\n"
                    "• **高風險**：25% 勝率"
                ),
            },
            "ai_qa": {
                "title": "🤖 AI問答系統 - `/ask`",
                "content": (
                    "**問答指令：**\n"
                    "`/ask question:[問題]` - 向AI助手提問\n"
                    "`/ask image:[圖片] question:[問題]` - 上傳圖片並提問\n"
                    "`/ask use_modal:True` - 使用大型文本輸入框"
                ),
            },
            "outfit_rating": {
                "title": "👗 AI穿搭評分系統 - `/rate`",
                "content": (
                    "**📸 評分指令：**\n"
                    "• `/rate image:[圖片]` - 上傳穿搭照片進行AI評分\n"
                    "• `/rate url:[圖片URL]` - 提供圖片網址進行評分\n\n"
                    "**評分機制：**\n"
                    "• 評分範圍：1-10分"
                ),
            },
            "gif_creation": {
                "title": "🎬 個人化GIF製作系統 - `/gif`",
                "content": (
                    "**🎨 GIF製作指令：**\n"
                    "• `/gif` - 查看所有可用的GIF模板列表\n"
                    "• `/gif template:[模板名稱]` - 使用指定模板製作個人化GIF"
                ),
            },
            "star_enhancement": {
                "title": "⭐ 升星系統 - 卡片強化與圖鑑記錄",
                "content": (
                    "**⭐ 升星機制：**\n"
                    "• 在收藏卡冊中選擇卡片，點擊「升星」按鈕\n"
                    "• 升星需要消耗**油幣**和**重複卡片**\n"
                    "• 升星有成功率，失敗時消耗素材但星級不變\n"
                    "• 最高星級為**35星**，星級越高成功率越低\n\n"
                    "**🎯 成功率階段：**\n"
                    "• **0-5星**：90% 成功率\n"
                    "• **6-10星**：80% 成功率\n"
                    "• **11-15星**：70% 成功率\n"
                    "• **16-20星**：60% 成功率\n"
                    "• **21-24星**：40% 成功率\n"
                    "• **25-27星**：25% 成功率\n"
                    "• **28-30星**：15% 成功率\n"
                    "• **31-35星**：8% 成功率\n\n"
                    "**💎 星級顯示說明：**\n"
                    "• 星級採用**分層顯示**系統，每5星為一個層級\n"
                    "• **1-5星**：<a:diamante6:1366109166158217226> 白鑽石\n"
                    "• **6-10星**：<a:diamante5:1366109193118941284> 藍鑽石\n"
                    "• **11-15星**：<a:diamante7:1366109201398628452> 紫鑽石\n"
                    "• **16-20星**：<a:diamante3:1366109173481209856> 黃鑽石\n"
                    "• **21-25星**：<a:diamante2:1366109178493534331> 紅鑽石\n"
                    "• **26-30星**：<a:diamante4:1366109211586465856> 綠鑽石\n"
                    "• **31-35星**：<a:diamante:1366109158616596500> 彩虹鑽石\n"
                    "• 例如：17星會顯示為 <a:diamante3:1366109173481209856><a:diamante3:1366109173481209856> (黃鑽石x2)\n\n"
                    "**📚 圖鑑記錄系統：**\n"
                    "• 升星成功時會更新圖鑑中的**最高星級記錄**\n"
                    "• 圖鑑顯示最高星級達成者和時間\n"
                    "• 圖鑑標題會顯示具體星數，如「最高17星級紀錄」\n"
                    "• 只有達到**最高星級**的玩家才能設置**自定義描述**\n"
                    "• 自定義描述會顯示在圖鑑中\n\n"
                    "**✍️ 描述系統：**\n"
                    "• 卡片達到**10星以上**才能設置自定義描述\n"
                    "• 在收藏卡冊中點擊「設置描述」按鈕\n"
                    "• 描述長度限制：5-200字符"
                ),
            },
            "power_analysis": {
                "title": "⚔️ AI戰力分析 - `/rateb`",
                "content": (
                    "**基本功能：**\n"
                    "`/rateb image:[圖片]` - 上傳角色圖片進行戰力分析\n"
                    "`/rateb url:[圖片URL]` - 使用圖片URL進行戰力分析\n\n"
                    "**等級評定：**\n"
                    "SSS/SS/S/A/B/C/D 七個等級"
                ),
            },
            "story_adventure": {
                "title": "📖 AI文字冒險故事系統 - `/story`",
                "content": (
                    "**基本指令：**\n"
                    "• `/story start` - 開始或繼續一個互動式文字冒險故事\n"
                    "• `/story start new_game:True` - 強制開始全新冒險（會存檔當前故事）\n"
                    "• `/story list` - 查看你的所有故事列表\n"
                    "• `/story view story_id:[ID]` - 查看分享的公開故事\n\n"
                    "**遊戲機制：**\n"
                    "• 選擇故事主題開始冒險\n"
                    "• 從AI提供的選項中選擇，或輸入自定義行動\n"
                    "• 每個決定都會影響故事走向和結局"
                ),
            },
            "poker1v1": {
                "title": "🃏 德州撲克1v1對戰 - `/poker1v1`",
                "content": (
                    "**🎮 遊戲指令：**\n"
                    "• `/poker1v1` - 開啟場次選擇界面\n"
                    "• `/poker1v1 tier:新手場 buyin:1000` - 快速匹配\n\n"
                    "**🎯 場次配置：**\n"
                    "• **🟢 新手場**：盲注 10/20，帶入 1,000~5,000\n"
                    "• **🟡 普通場**：盲注 100/200，帶入 10,000~50,000\n"
                    "• **🔴 高手場**：盲注 1,000/2,000，帶入 100,000~500,000\n\n"
                    "**🃏 遊戲規則：**\n"
                    "• 標準德州撲克規則，1v1對戰模式\n"
                    "• 自動匹配系統，3分鐘匹配超時\n"
                    "• 抽水率：5%（僅翻牌後），各場次有抽水上限\n"
                    "• 籌碼不足時自動結束遊戲\n\n"
                    "**💰 經濟機制：**\n"
                    "• 有效籌碼 = min(雙方帶入金額)\n"
                    "• 獲勝者獲得底池（扣除抽水）\n"
                    "• 遊戲結束後可選擇繼續匹配或離開"
                ),
            },
            "usage_rules": {
                "title": "� 核心規則",
                "content": (
                    "**➔ 1. 禁止開小號 *(No Alternate Accounts)***\n"
                    "小號使用嚴格禁止。所有交易、市場操作和轉帳指令都有完整記錄。\n"
                    "※ **違規者終將被發現，主帳號和小號都將被永久封禁。**\n\n"
                    "**➔ 2. 禁止自動化濫用 *(No Automation Abuse)***\n"
                    "嚴格禁止使用自動機器人、宏指令、自動農場工具或任何其他自動化遊戲方式。\n\n"
                    "**➔ 3. 漏洞回報 *(Exploit Reporting)***\n"
                    "如果您發現漏洞，請立即回報。常見漏洞類型：\n"
                    "◇ **漏洞洗錢**：無限刷取油幣或資源\n"
                    "◇ **重複抽卡**：重複獲得卡片而不消耗油幣\n"
                    "◇ **轉帳漏洞**：異常轉帳操作\n\n"
                    "**回報方式**：使用 `/問題回報` 指令或前往 <#1395821430532210738> 開單\n"
                    "▲ **利用漏洞將導致帳號處罰，包括資產清空和永久封禁。**\n"
                    "★ 主動回報重大漏洞的玩家將獲得適當獎勵。"
                ),
            },
            "limitations": {
                "title": "📋 遊戲系統規則",
                "content": (
                    "**➔ 4. 禁止詐騙 *(No Scamming)***\n"
                    "詐騙是嚴格禁止的。這包括從其他玩家那裡拿走卡片或遊戲內貨幣而不履行協議。\n"
                    "◆ **注意**：買家後悔不是詐騙。交易完成後即為最終。\n\n"
                    "**➔ 5. 禁止遊戲外交易**\n"
                    "任何遊戲內資產均不得在遊戲外進行買賣、交易或轉讓。\n\n"
                    "**➔ 6. 正確使用機器人功能**\n"
                    "• 請按照指令說明正確使用各項功能\n"
                    "• 不要嘗試繞過指令限制或濫用功能\n"
                    "• 機器人功能僅供正常遊戲使用"
                ),
            },
            "violation_handling": {
                "title": "⚖️ 處分機制",
                "content": (
                    "**處分等級：**\n"
                    "```\n"
                    "○ 輕微違規  | 警告或短期禁言\n"
                    "△ 嚴重違規  | 長期禁言或功能禁用\n"
                    "● 極嚴重違規 | 永久封禁並清空資產\n"
                    "```\n\n"
                    "**處分原則：**\n"
                    "• 根據違規程度和影響範圍決定處分等級\n"
                    "• 重複違規者將加重處分\n"
                    "• 極嚴重違規（如開小號、詐騙）直接永久封禁\n\n"
                    "**申訴方式：**\n"
                    "如認為處分有誤，可使用 `/問題回報` 指令申訴或前往 <#1395821430532210738> 開單\n"
                    "⚠️ **重要**：每次處分最多只有**一次**申訴機會\n\n"
                    "**最終解釋權：**\n"
                    "管理員擁有最終解釋權。如有爭議或條文模糊之處，由管理員保留最終決定與解釋之權利。"
                ),
            },
            "bug_reporting": {
                "title": "🐛 問題回報指南",
                "content": (
                    "**常見問題類型：**\n"
                    "• 指令執行錯誤或無回應\n"
                    "• 功能異常或結果不正確\n"
                    "• 機器人崩潰或連線問題\n"
                    "• 資料顯示錯誤或遺失\n\n"
                    "**回報方式：**\n"
                    "• 使用 `/問題回報` 指令\n"
                    "• 詳細描述問題發生的情況\n"
                    "• 提供錯誤訊息截圖（如有）\n"
                    "• 說明重現問題的步驟\n\n"
                    "**回報內容建議：**\n"
                    "1. **問題描述**：清楚說明遇到什麼問題\n"
                    "2. **發生時間**：問題發生的大概時間\n"
                    "3. **使用指令**：當時使用的具體指令\n"
                    "4. **預期結果**：您期望看到的正常結果\n"
                    "5. **實際結果**：實際發生的異常情況\n\n"
                    "**處理時效：**\n"
                    "• 一般問題：1-3 個工作日內回應\n"
                    "• 緊急問題：24 小時內優先處理\n"
                    "• 系統性問題：立即調查並修復"
                ),
            },
        }

        return content_map.get(
            subcategory_key, {"title": "未知分類", "content": "此分類暫無詳細說明。"}
        )

    @app_commands.command(
        name="help", description="查看Gacha系統的各項功能說明和使用教學"
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def help_command(self, interaction: discord.Interaction):
        """幫助命令處理（新版本，無參數）"""
        # 延遲響應，避免Discord超時
        await interaction.response.defer(thinking=True)

        # 創建主幫助 embed
        embed = self._create_main_help_embed(interaction.user)

        # 創建帶有下拉選單的視圖
        view = HelpView(self, interaction.user.id)

        # 發送幫助消息
        await interaction.followup.send(embed=embed, view=view)


class HelpView(BaseView):
    """幫助系統的UI視圖，包含兩個下拉式選單"""

    def __init__(self, help_cog: HelpCog, user_id: int):
        super().__init__(bot=help_cog.bot, user_id=user_id, timeout=300)  # 5分鐘超時
        self.help_cog = help_cog
        self.current_category: Optional[str] = None
        self.current_subcategory: Optional[str] = None

        # 添加主分類選單
        self.main_category_select = MainCategorySelect(self)
        self.add_item(self.main_category_select)

        # 子分類選單（初始為空）
        self.subcategory_select = SubcategorySelect(self)
        self.add_item(self.subcategory_select)


class MainCategorySelect(discord.ui.Select):
    """主分類選擇下拉選單"""

    def __init__(self, help_view: HelpView):
        self.help_view = help_view

        options = []
        for category_key, category_data in help_view.help_cog.HELP_CATEGORIES.items():
            # Ensure we have string values for SelectOption
            label = category_data.get("name", "未知分類")
            if not isinstance(label, str):
                label = "未知分類"

            description = category_data.get("dropdown_description", "")
            if not isinstance(description, str):
                description = ""

            options.append(
                discord.SelectOption(
                    label=label,
                    value=category_key,
                    description=description,
                )
            )

        super().__init__(
            placeholder="請選擇主分類...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="main_category_select",
        )

    async def callback(self, interaction: discord.Interaction):
        """主分類選擇回調"""
        selected_category = self.values[0]
        self.help_view.current_category = selected_category
        self.help_view.current_subcategory = None

        # 更新選單顯示的選擇
        for option in self.options:
            option.default = option.value == selected_category

        # 更新子分類選單
        self.help_view.subcategory_select.update_options(selected_category)

        # 創建新的 embed 顯示該分類的概覽
        embed = self.help_view.help_cog._create_category_help_embed(
            interaction.user, selected_category
        )

        await interaction.response.edit_message(embed=embed, view=self.help_view)


class SubcategorySelect(discord.ui.Select):
    """子分類選擇下拉選單"""

    def __init__(self, help_view: HelpView):
        self.help_view = help_view

        # 初始時沒有選項
        super().__init__(
            placeholder="請先選擇主分類...",
            min_values=1,
            max_values=1,
            options=[discord.SelectOption(label="無可用選項", value="none")],
            disabled=True,
            custom_id="subcategory_select",
        )

    def update_options(self, category_key: str):
        """更新子分類選項"""
        category_data = self.help_view.help_cog.HELP_CATEGORIES[category_key]

        self.options.clear()
        subcategories = category_data.get("subcategories", {})
        if isinstance(subcategories, dict):
            for sub_key, sub_data in subcategories.items():
                self.options.append(
                    discord.SelectOption(
                        label=sub_data["name"],
                        value=sub_key,
                        description=sub_data["description"],
                        default=False,  # 重置所有選項為未選中
                    )
                )

        self.placeholder = "請選擇子分類..."
        self.disabled = False

    async def callback(self, interaction: discord.Interaction):
        """子分類選擇回調"""
        if not self.help_view.current_category:
            raise BusinessError("請先選擇主分類！")

        selected_subcategory = self.values[0]
        self.help_view.current_subcategory = selected_subcategory

        # 更新選單顯示的選擇
        for option in self.options:
            option.default = option.value == selected_subcategory

        # 創建新的 embed 顯示該子分類的詳細內容
        embed = self.help_view.help_cog._create_category_help_embed(
            interaction.user, self.help_view.current_category, selected_subcategory
        )

        await interaction.response.edit_message(embed=embed, view=self.help_view)


async def setup(bot: commands.Bot):
    """將 HelpCog 添加到 bot"""
    await bot.add_cog(HelpCog(bot))
    logger.info("HelpCog has been added to the bot.")
