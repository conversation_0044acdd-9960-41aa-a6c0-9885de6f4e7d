{"APPLY_ATK_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ATK_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"patk": "0.10 + skill_level * 0.05", "matk": "0.10 + skill_level * 0.05"}}, "APPLY_DEF_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "DEF_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"pdef": "0.15 + skill_level * 0.05", "mdef": "0.15 + skill_level * 0.05"}}, "APPLY_SPD_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "SPD_BOOST", "duration_turns": "2 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"spd": "0.20 + skill_level * 0.10"}}, "APPLY_HP_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "HP_BOOST", "duration_turns": "4 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"max_hp": "0.15 + skill_level * 0.05"}}, "APPLY_MP_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "MP_BOOST", "duration_turns": "4 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"max_mp": "0.20 + skill_level * 0.05"}}, "APPLY_CRIT_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "CRIT_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"crit_rate": "0.10 + skill_level * 0.05", "crit_dmg_multiplier": "0.20 + skill_level * 0.10"}}, "APPLY_ACCURACY_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ACCURACY_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"accuracy": "0.15 + skill_level * 0.05"}}, "APPLY_EVASION_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "EVASION_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"evasion": "0.15 + skill_level * 0.05"}}, "APPLY_ALL_STATS_BOOST": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ALL_STATS_BOOST", "duration_turns": "3 + skill_level", "chance": 1.0, "stack_count": 1, "value_overrides": {"patk": "0.08 + skill_level * 0.03", "matk": "0.08 + skill_level * 0.03", "pdef": "0.08 + skill_level * 0.03", "mdef": "0.08 + skill_level * 0.03", "spd": "0.08 + skill_level * 0.03"}}, "APPLY_ATK_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ATK_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.7 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"patk": "-(0.15 + skill_level * 0.05)", "matk": "-(0.15 + skill_level * 0.05)"}}, "APPLY_DEF_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "DEF_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.7 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"pdef": "-(0.20 + skill_level * 0.05)", "mdef": "-(0.20 + skill_level * 0.05)"}}, "APPLY_SPD_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "SPD_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.8 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"spd": "-(0.25 + skill_level * 0.10)"}}, "APPLY_ACCURACY_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ACCURACY_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.8 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"accuracy": "-(0.20 + skill_level * 0.05)"}}, "APPLY_EVASION_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "EVASION_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.8 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"evasion": "-(0.20 + skill_level * 0.05)"}}, "APPLY_HP_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "HP_DEBUFF", "duration_turns": "3 + skill_level", "chance": "0.7 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"max_hp": "-(0.15 + skill_level * 0.05)"}}, "APPLY_MP_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "MP_DEBUFF", "duration_turns": "3 + skill_level", "chance": "0.7 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"max_mp": "-(0.20 + skill_level * 0.05)"}}, "APPLY_CRIT_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "CRIT_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.8 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"crit_rate": "-(0.10 + skill_level * 0.05)", "crit_dmg_multiplier": "-(0.20 + skill_level * 0.10)"}}, "APPLY_ALL_STATS_DEBUFF": {"effect_type": "APPLY_STATUS_EFFECT", "status_effect_id": "ALL_STATS_DEBUFF", "duration_turns": "2 + skill_level", "chance": "0.6 + skill_level * 0.1", "stack_count": 1, "value_overrides": {"patk": "-(0.08 + skill_level * 0.03)", "matk": "-(0.08 + skill_level * 0.03)", "pdef": "-(0.08 + skill_level * 0.03)", "mdef": "-(0.08 + skill_level * 0.03)", "spd": "-(0.08 + skill_level * 0.03)"}}}