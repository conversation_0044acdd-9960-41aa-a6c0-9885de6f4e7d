from typing import List, Optional

import asyncpg

from gacha.exceptions import EntityNotFoundError
from gacha.models.trade_models import CardTradeHistoryModel
from gacha.repositories._base_repo import (
    fetch_all,
    fetch_one,
)
from utils.logger import logger

# 表名常量
TABLE_NAME = "card_trade_history"


async def add_trade_history(
    trade_data: CardTradeHistoryModel, connection: Optional[asyncpg.Connection] = None
) -> CardTradeHistoryModel:
    """
    Adds a new completed trade record to the card_trade_history table.
    The 'id' and 'completed_at' fields are typically generated by the database.

    Args:
        trade_data: A CardTradeHistoryModel instance containing the trade details.
                    The 'id' and 'completed_at' fields from this model will be ignored,
                    as they are set by the database or within this method.
        connection: Optional asyncpg.Connection to use for the operation.

    Returns:
        A CardTradeHistoryModel instance representing the newly inserted record,
        including the database-generated 'id' and 'completed_at'.

    Raises:
        DatabaseOperationError: If the database operation fails.
        EntityNotFoundError: If the insert operation does not return the expected record.
    """
    query = f"""
        INSERT INTO {TABLE_NAME} (
            initiator_user_id, receiver_user_id,
            offered_master_card_id, offered_quantity,
            requested_master_card_id, requested_quantity,
            price_amount, trade_type, fee_charged
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id, initiator_user_id, receiver_user_id,
                  offered_master_card_id, offered_quantity,
                  requested_master_card_id, requested_quantity,
                  price_amount, trade_type, fee_charged, completed_at;
    """
    actual_requested_quantity = trade_data.requested_quantity
    if trade_data.requested_master_card_id is None:
        actual_requested_quantity = None
    params = (
        trade_data.initiator_user_id,
        trade_data.receiver_user_id,
        trade_data.offered_master_card_id,
        trade_data.offered_quantity,
        trade_data.requested_master_card_id,
        actual_requested_quantity,
        trade_data.price_amount,
        trade_data.trade_type,
        trade_data.fee_charged,
    )
    record = await fetch_one(query, params, connection=connection)
    if record:
        return CardTradeHistoryModel(**dict(record))
    else:
        logger.error(
            "Failed to insert trade history for initiator %s, data: %s",
            trade_data.initiator_user_id,
            trade_data.model_dump_json(indent=2),
        )
        raise EntityNotFoundError(
            "Failed to insert trade history, no record returned after executing query."
        )


async def get_trade_history_by_user(
    user_id: int,
    limit: int = 20,
    offset: int = 0,
    connection: Optional[asyncpg.Connection] = None,
) -> List[CardTradeHistoryModel]:
    """
    Retrieves trade history for a specific user (as initiator or receiver), paginated.

    Args:
        user_id: The Discord User ID.
        limit: Maximum number of records to return.
        offset: Offset for pagination.
        connection: Optional asyncpg.Connection to use for the operation.

    Returns:
        A list of CardTradeHistoryModel instances, ordered by completion_at DESC.

    Raises:
        DatabaseOperationError: If the database operation fails.
    """
    query = f"""
        SELECT id, initiator_user_id, receiver_user_id,
               offered_master_card_id, offered_quantity,
               requested_master_card_id, requested_quantity,
               price_amount, trade_type, fee_charged, completed_at
        FROM {TABLE_NAME}
        WHERE initiator_user_id = $1 OR receiver_user_id = $2
        ORDER BY completed_at DESC, id DESC
        LIMIT $3 OFFSET $4;
    """
    params = (user_id, user_id, limit, offset)
    records = await fetch_all(query, params, connection=connection)
    return [CardTradeHistoryModel(**dict(row)) for row in records]


async def get_trade_history_entry_by_id(
    trade_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[CardTradeHistoryModel]:
    """
    Retrieves a specific trade history entry by its ID.

    Args:
        trade_id: The ID of the trade history entry to retrieve.
        connection: Optional asyncpg.Connection to use for the operation.

    Returns:
        CardTradeHistoryModel if found, None if not found.

    Raises:
        DatabaseOperationError: If the database operation fails.
    """
    query = f"""
        SELECT id, initiator_user_id, receiver_user_id,
               offered_master_card_id, offered_quantity,
               requested_master_card_id, requested_quantity,
               price_amount, trade_type, fee_charged, completed_at
        FROM {TABLE_NAME}
        WHERE id = $1;
    """
    params = (trade_id,)
    record = await fetch_one(query, params, connection=connection)
    return CardTradeHistoryModel(**dict(record)) if record else None
