"""
戰鬥事件系統
負責管理戰鬥中的事件觸發和處理
"""

from typing import Any, Dict, List, Optional

from utils.logger import logger

from ..models.battle import Battle
from ..models.combatant import Combatant
from ..models.enums import EventType
from . import passive_trigger_handler


async def trigger_event(
    event_type: str, event_data: Dict[str, Any], battle_context: "Battle"
) -> List[Dict[str, Any]]:
    """
    觸發戰鬥事件
    """
    try:
        results = []
        potential_passive_owners = await _get_potential_passive_owners(
            event_type, event_data, battle_context
        )

        if potential_passive_owners:
            passive_results = await passive_trigger_handler.check_and_apply_passives(
                event_type=event_type,
                event_data=event_data,
                potential_passive_owners=potential_passive_owners,
                battle_context=battle_context,
            )
            results.extend(passive_results)

        await _log_event(event_type, event_data, battle_context)
        return results
    except Exception as e:
        logger.error("觸發事件 %s 時發生錯誤: %s", event_type, e)
        return []


async def _get_potential_passive_owners(
    event_type: str, event_data: Dict[str, Any], battle_context: "Battle"
) -> List["Combatant"]:
    """
    根據事件類型確定潛在的被動技能擁有者
    """
    try:
        if event_type in [
            EventType.ON_BATTLE_START.value,
            EventType.ON_BATTLE_END.value,
            EventType.ON_TURN_START.value,
            EventType.ON_TURN_END.value,
            EventType.ON_DAMAGE_DEALT.value,
            EventType.ON_DAMAGE_TAKEN.value,
            EventType.ON_HEAL_DEALT.value,
            EventType.ON_HEAL_RECEIVED.value,
            EventType.ON_STATUS_EFFECT_APPLIED.value,
            EventType.ON_ALLY_DEATH.value,
            EventType.ON_ENEMY_DEATH.value,
            EventType.ON_COMBATANT_DEATH.value,
            EventType.ON_HP_THRESHOLD_REACHED.value,
        ]:
            return battle_context.get_all_alive_combatants()
        else:
            return battle_context.get_all_alive_combatants()
    except Exception as e:
        logger.error("確定潛在被動擁有者時發生錯誤: %s", e)
        return []


async def _log_event(
    event_type: str, event_data: Dict[str, Any], battle_context: "Battle"
) -> None:
    """
    記錄事件到戰鬥日誌
    """
    try:
        log_message = _format_event_log_message(event_type, event_data)
        battle_context.add_log_entry(
            message=log_message,
            action_type="SYSTEM",
            actor_id=None,
            target_ids=None,
            details=event_data,
        )
    except Exception as e:
        logger.error("記錄事件日誌時發生錯誤: %s", e)


def _format_event_log_message(event_type: str, event_data: Dict[str, Any]) -> str:
    """
    格式化事件日誌消息
    """
    try:
        if event_type == EventType.ON_DAMAGE_DEALT.value:
            attacker_id = event_data.get("source_attacker_id", "未知")
            target_id = event_data.get("target_id", "未知")
            damage = event_data.get("damage_amount", 0)
            damage_type = event_data.get("damage_type", "未知")
            is_crit = event_data.get("is_crit", False)
            crit_text = " (暴擊)" if is_crit else ""
            return f"{attacker_id} 對 {target_id} 造成了 {damage} 點 {damage_type} 傷害{crit_text}"
        elif event_type == EventType.ON_HEAL_DEALT.value:
            healer_id = event_data.get("source_healer_id", "未知")
            target_id = event_data.get("target_id", "未知")
            heal = event_data.get("heal_amount", 0)
            return f"{healer_id} 為 {target_id} 恢復了 {heal} 點生命值"
        elif event_type == EventType.ON_STATUS_EFFECT_APPLIED.value:
            caster_id = event_data.get("source_caster_id", "未知")
            target_id = event_data.get("target_id", "未知")
            status_id = event_data.get("status_effect_id", "未知")
            duration = event_data.get("duration_turns", 0)
            return f"{caster_id} 對 {target_id} 施加了狀態效果 {status_id} (持續 {duration} 回合)"
        elif event_type == EventType.ON_TURN_START.value:
            acting_id = event_data.get("acting_combatant_id", "未知")
            turn_num = event_data.get("current_turn_number", 0)
            return f"第 {turn_num} 回合開始，{acting_id} 行動"
        else:
            return f"事件觸發: {event_type}"
    except Exception as e:
        logger.error("格式化事件日誌消息時發生錯誤: %s", e)
        return f"事件觸發: {event_type}"


class EventDataBuilder:
    """事件數據構建器，用於構建標準化的事件數據"""

    @staticmethod
    def build_damage_dealt_data(
        attacker_id: str,
        target_id: str,
        damage_amount: float,
        damage_type: str = "PHYSICAL",
        is_crit: bool = False,
        is_fatal: bool = False,
        skill_id: Optional[str] = None,
        is_primary_attack: bool = False,
    ) -> Dict[str, Any]:
        """構建傷害造成事件數據"""
        return {
            "source_attacker_id": attacker_id,
            "target_id": target_id,
            "damage_amount": damage_amount,
            "damage_type": damage_type,
            "is_crit": is_crit,
            "is_fatal_to_target": is_fatal,
            "damage_source_skill_id": skill_id,
            "is_primary_attack_damage": is_primary_attack,
            "is_skill_damage": skill_id is not None,
        }

    @staticmethod
    def build_damage_taken_data(
        target_id: str,
        attacker_id: str,
        damage_amount: float,
        damage_type: str = "PHYSICAL",
        is_crit: bool = False,
        was_fatal: bool = False,
        skill_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """構建傷害承受事件數據"""
        return {
            "target_id": target_id,
            "source_attacker_id": attacker_id,
            "damage_amount": damage_amount,
            "damage_type": damage_type,
            "is_crit": is_crit,
            "was_fatal": was_fatal,
            "damage_source_skill_id": skill_id,
        }

    @staticmethod
    def build_heal_dealt_data(
        healer_id: str,
        target_id: str,
        heal_amount: float,
        skill_id: Optional[str] = None,
        is_crit_heal: bool = False,
    ) -> Dict[str, Any]:
        """構建治療施加事件數據"""
        return {
            "source_healer_id": healer_id,
            "target_id": target_id,
            "heal_amount": heal_amount,
            "heal_source_skill_id": skill_id,
            "is_crit_heal": is_crit_heal,
        }

    @staticmethod
    def build_status_effect_applied_data(
        caster_id: str,
        target_id: str,
        status_effect_id: str,
        is_buff: bool,
        duration_turns: int,
        stack_count: int = 1,
    ) -> Dict[str, Any]:
        """構建狀態效果施加事件數據"""
        return {
            "target_id": target_id,
            "source_caster_id": caster_id,
            "status_effect_id": status_effect_id,
            "is_buff": is_buff,
            "duration_turns": duration_turns,
            "stack_count": stack_count,
        }
