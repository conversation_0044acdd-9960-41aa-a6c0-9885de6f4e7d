"""
更新資訊 COG
提供 /更新資訊 指令來顯示系統更新內容和收集用戶反饋
"""

import json
import os
from typing import Dict, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button

from gacha.exceptions import OnCooldownError
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.logger import logger


class UpdateInfoView(BasePaginationView):
    """更新資訊分頁視圖"""

    def __init__(
        self,
        bot: commands.Bo<PERSON>,
        user: Union[discord.User, discord.Member],
        updates_data: List[Dict],
        cog: "UpdateInfoCog",
        timeout: Optional[int] = None,  # 設置為 None，永不超時
    ):
        """初始化更新資訊視圖

        參數:
            bot: Bot 實例
            user: Discord用戶對象
            updates_data: 更新資訊數據列表
            cog: UpdateInfoCog 實例，用於冷卻檢查
            timeout: 超時時間(秒)
        """
        self.updates_data = updates_data
        self.cog = cog
        total_pages = len(updates_data)
        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=timeout,
        )

        # 添加反饋按鈕
        self._add_feedback_buttons()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查交互權限和冷卻"""
        # 先檢查基礎權限（用戶是否為原始用戶）
        if not await super().interaction_check(interaction):
            return False

        # 檢查是否為反饋按鈕
        custom_id = interaction.data.get("custom_id", "") if interaction.data else ""
        if custom_id.startswith("feedback_"):
            # 檢查冷卻（1小時）
            retry_after = self.cog.feedback_cooldown_mapping.update_rate_limit(
                interaction
            )
            if retry_after:
                raise OnCooldownError(
                    "⏰ 反饋按鈕冷卻中，請稍後再試！", retry_after=retry_after
                )
        return True

    def _add_feedback_buttons(self):
        """添加當前頁面的反饋按鈕"""
        if not self.updates_data:
            return

        current_update = self.updates_data[self.current_page - 1]
        buttons = current_update.get("buttons", [])

        # 清除現有的反饋按鈕（保留分頁按鈕）
        self._clear_feedback_buttons()

        # 添加反饋按鈕到第一行
        for i, button_config in enumerate(buttons):
            if i >= 5:  # 最多5個反饋按鈕
                break

            button = Button(
                label=f"{button_config['name']} ({button_config['count']})",
                style=discord.ButtonStyle.secondary,
                custom_id=f"feedback_{i}",
                row=1,
            )
            button.callback = self._create_feedback_callback(i)
            self.add_item(button)

    def _clear_feedback_buttons(self):
        """清除反饋按鈕，保留分頁按鈕"""
        # 移除所有在第1行的按鈕（反饋按鈕）
        items_to_remove = [
            item for item in self.children if hasattr(item, "row") and item.row == 1
        ]
        for item in items_to_remove:
            self.remove_item(item)

    def _create_feedback_callback(self, button_index: int):
        """創建反饋按鈕的回調函數"""

        async def feedback_callback(interaction: discord.Interaction):
            await interaction.response.defer()

            # 更新計數
            await self._update_feedback_count(button_index)

            # 重新添加按鈕以更新計數顯示
            self._add_feedback_buttons()

            # 更新消息
            embed = await self.get_current_page_embed()
            await interaction.edit_original_response(embed=embed, view=self)

        return feedback_callback

    async def _update_feedback_count(self, button_index: int):
        """更新反饋計數並保存到 JSON 文件"""
        try:
            # 更新內存中的數據
            current_update = self.updates_data[self.current_page - 1]
            if button_index < len(current_update["buttons"]):
                current_update["buttons"][button_index]["count"] += 1

            # 保存到文件
            json_path = os.path.join("auxiliary", "data", "update_info", "updates.json")
            data_to_save = {"updates": self.updates_data}

            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)

            logger.info(
                "已更新反饋計數: 頁面 %s, 按鈕 %s", self.current_page, button_index
            )

        except Exception as e:
            logger.error("保存反饋計數時發生錯誤: %s", e, exc_info=True)
            raise

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的 Embed"""
        if not self.updates_data:
            embed = discord.Embed(
                title="❌ 沒有更新資訊",
                description="目前沒有可顯示的更新資訊",
                color=0xFF0000,
            )
            return embed

        current_update = self.updates_data[self.current_page - 1]

        embed = discord.Embed(
            title=current_update["title"],
            description=current_update["content"],
            color=0x00FF88,
        )

        # 設置圖片（如果有的話）
        image_url = current_update.get("image_url", "")
        if image_url:
            embed.set_image(url=image_url)

        # 設置縮圖（右上角）
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1383752680010416128/productive.png"
        )

        # 設置頁腳，包含更新日期和分頁信息
        footer_text = (
            f"📅 {current_update['date']} • 頁面 {self.current_page}/{self.total_pages}"
        )
        embed.set_footer(text=footer_text)

        return embed

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面內容（BasePaginationView 要求的方法）"""
        self.current_page = page

        # 重新添加當前頁面的反饋按鈕
        self._add_feedback_buttons()

        # 更新 embed
        embed = await self.get_current_page_embed()

        # 更新消息
        await interaction.response.edit_message(embed=embed, view=self)


class UpdateInfoCog(commands.Cog, name="更新資訊"):
    """更新資訊系統 COG"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.json_path = os.path.join(
            "auxiliary", "data", "update_info", "updates.json"
        )

        # 設置反饋按鈕冷卻（1小時，針對用戶）
        self.feedback_cooldown_mapping = commands.CooldownMapping.from_cooldown(
            1.0, 3600.0, lambda interaction: interaction.user
        )

        # 確保數據目錄存在
        os.makedirs(os.path.dirname(self.json_path), exist_ok=True)

        # 如果 JSON 文件不存在，創建默認文件
        if not os.path.exists(self.json_path):
            self._create_default_json()

    def _create_default_json(self):
        """創建默認的更新資訊 JSON 文件"""
        default_data = {
            "updates": [
                {
                    "date": "2024-12-15",
                    "title": "🎉 更新資訊系統上線",
                    "content": (
                        "全新的更新資訊系統正式上線！\n\n"
                        "**功能特色：**\n"
                        "• 分頁顯示歷史更新\n"
                        "• 即時反饋收集\n"
                        "• 美觀的界面設計\n\n"
                        "歡迎大家使用並給予反饋！"
                    ),
                    "image_url": "",
                    "buttons": [
                        {"name": "🎉 超讚", "count": 0},
                        {"name": "👍 不錯", "count": 0},
                        {"name": "😐 普通", "count": 0},
                    ],
                }
            ]
        }

        try:
            with open(self.json_path, "w", encoding="utf-8") as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
            logger.info("已創建默認的更新資訊 JSON 文件")
        except Exception as e:
            logger.error("創建默認 JSON 文件時發生錯誤: %s", e, exc_info=True)

    def _load_updates_data(self) -> List[Dict]:
        """載入更新資訊數據"""
        try:
            with open(self.json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            updates = data.get("updates", [])
            # 按日期排序，最新的在前面
            updates.sort(key=lambda x: x.get("date", ""), reverse=True)

            return updates

        except FileNotFoundError:
            logger.warning("更新資訊 JSON 文件不存在，創建默認文件")
            self._create_default_json()
            return self._load_updates_data()

        except json.JSONDecodeError as e:
            logger.error("解析更新資訊 JSON 文件時發生錯誤: %s", e, exc_info=True)
            return []

        except Exception as e:
            logger.error("載入更新資訊數據時發生錯誤: %s", e, exc_info=True)
            return []

    @app_commands.command(name="更新資訊", description="📋 查看系統更新資訊並提供反饋")
    @app_commands.checks.cooldown(1, 3.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def update_info_command(self, interaction: discord.Interaction):
        """顯示更新資訊的主指令"""
        await interaction.response.defer(thinking=True)

        # 載入更新資訊數據
        updates_data = self._load_updates_data()

        if not updates_data:
            embed = discord.Embed(
                title="❌ 沒有更新資訊",
                description="目前沒有可顯示的更新資訊",
                color=0xFF0000,
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # 創建更新資訊視圖
        view = UpdateInfoView(
            bot=self.bot,
            user=interaction.user,
            updates_data=updates_data,
            cog=self,
            timeout=None,  # 永不超時
        )

        # 獲取第一頁的 embed
        embed = await view.get_current_page_embed()

        # 發送消息
        message = await interaction.followup.send(embed=embed, view=view)
        view.message = message

        logger.info("用戶 %s 查看了更新資訊", interaction.user.id)


async def setup(bot: commands.Bot):
    """註冊更新資訊 COG"""
    await bot.add_cog(UpdateInfoCog(bot))
    logger.info("UpdateInfoCog 已載入")
