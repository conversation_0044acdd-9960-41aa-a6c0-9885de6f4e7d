"""
德州撲克1v1遊戲邏輯 - 修復版
包含手牌評估、匹配隊列管理和遊戲邏輯處理
添加了線程安全和原子性操作保護
"""

import asyncio
import time
from itertools import combinations
from typing import Dict, List, Optional, Union

from gacha.exceptions import (
    InsufficientBalanceError,
    InvalidGameActionError,
    NotPlayerTurnError,
)
from utils.logger import logger

from .models import (
    RAKE_PERCENTAGE,
    STAKE_CONFIGS,
    GameStage,
    HandEvaluation,
    LeaveGameCommand,
    MatchmakingEntry,
    PlayerAction,
    PlayerActionCommand,
    PokerCard,
    PokerGameState,
    PokerHandRank,
    StakeTier,
    TimeoutEvent,
)

# ==============================================================================
# Poker Hand Evaluation Functions (無狀態函數)
# ==============================================================================


def evaluate_hand(
    hole_cards: List[PokerCard], community_cards: List[PokerCard]
) -> HandEvaluation:
    """評估最佳5張牌組合"""
    all_cards = hole_cards + community_cards
    if len(all_cards) < 5:
        # 如果牌不足5張，返回高牌
        sorted_values = sorted([card.numeric_value for card in all_cards], reverse=True)
        return HandEvaluation(PokerHandRank.HIGH_CARD, sorted_values, "高牌")

    # 生成所有可能的5張牌組合
    best_hand = None

    for combo in combinations(all_cards, 5):
        hand_eval = _evaluate_five_cards(list(combo))
        if (
            best_hand is None
            or hand_eval.rank.value > best_hand.rank.value
            or (
                hand_eval.rank.value == best_hand.rank.value
                and hand_eval.high_cards > best_hand.high_cards
            )
        ):
            best_hand = hand_eval

    # This should never happen since we have at least 5 cards, but add safety check
    if best_hand is None:
        # Fallback to high card with available cards
        sorted_values = sorted([card.numeric_value for card in all_cards], reverse=True)
        return HandEvaluation(PokerHandRank.HIGH_CARD, sorted_values[:5], "高牌")

    return best_hand


def _get_hand_rank(is_straight, is_flush, counts):
    if is_straight and is_flush:
        return "straight_flush"
    if counts == [4, 1]:
        return "four_of_a_kind"
    if counts == [3, 2]:
        return "full_house"
    if is_flush:
        return "flush"
    if is_straight:
        return "straight"
    if counts == [3, 1, 1]:
        return "three_of_a_kind"
    if counts == [2, 2, 1]:
        return "two_pair"
    if counts == [2, 1, 1, 1]:
        return "pair"
    return "high_card"


def _evaluate_five_cards(cards: List[PokerCard]) -> HandEvaluation:
    """評估5張牌的牌型"""
    sorted_cards = sorted(cards, key=lambda x: x.numeric_value, reverse=True)
    values = [card.numeric_value for card in sorted_cards]
    suits = [card.suit for card in sorted_cards]
    value_counts = {v: values.count(v) for v in set(values)}
    counts = sorted(value_counts.values(), reverse=True)
    is_flush = len(set(suits)) == 1
    is_straight = _is_straight(values)

    if not is_straight and set(values) == {14, 5, 4, 3, 2}:
        is_straight = True
        values = [5, 4, 3, 2, 1]

    rank = _get_hand_rank(is_straight, is_flush, counts)

    if rank == "straight_flush":
        return _evaluate_straight_flush(values)
    if rank == "four_of_a_kind":
        return _evaluate_four_of_kind(value_counts)
    if rank == "full_house":
        return _evaluate_full_house(value_counts)
    if rank == "flush":
        return HandEvaluation(PokerHandRank.FLUSH, values, "同花")
    if rank == "straight":
        return HandEvaluation(PokerHandRank.STRAIGHT, [values[0]], "順子")
    if rank == "three_of_a_kind":
        return _evaluate_three_of_kind(value_counts)
    if rank == "two_pair":
        return _evaluate_two_pair(value_counts)
    if rank == "pair":
        return _evaluate_pair(value_counts)

    return HandEvaluation(PokerHandRank.HIGH_CARD, values, "高牌")


def _evaluate_straight_flush(values: List[int]) -> HandEvaluation:
    """評估同花順"""
    if values[0] == 14 and values[1] == 13:  # A-K-Q-J-10
        return HandEvaluation(PokerHandRank.ROYAL_FLUSH, [14], "皇家同花順")
    else:
        return HandEvaluation(PokerHandRank.STRAIGHT_FLUSH, [values[0]], "同花順")


def _evaluate_four_of_kind(value_counts: Dict[int, int]) -> HandEvaluation:
    """評估四條"""
    four_kind = [v for v, c in value_counts.items() if c == 4][0]
    kicker = [v for v, c in value_counts.items() if c == 1][0]
    return HandEvaluation(PokerHandRank.FOUR_OF_A_KIND, [four_kind, kicker], "四條")


def _evaluate_full_house(value_counts: Dict[int, int]) -> HandEvaluation:
    """評估葫蘆"""
    three_kind = [v for v, c in value_counts.items() if c == 3][0]
    pair = [v for v, c in value_counts.items() if c == 2][0]
    return HandEvaluation(PokerHandRank.FULL_HOUSE, [three_kind, pair], "葫蘆")


def _evaluate_three_of_kind(value_counts: Dict[int, int]) -> HandEvaluation:
    """評估三條"""
    three_kind = [v for v, c in value_counts.items() if c == 3][0]
    kickers = sorted([v for v, c in value_counts.items() if c == 1], reverse=True)
    return HandEvaluation(PokerHandRank.THREE_OF_A_KIND, [three_kind] + kickers, "三條")


def _evaluate_two_pair(value_counts: Dict[int, int]) -> HandEvaluation:
    """評估兩對"""
    pairs = sorted([v for v, c in value_counts.items() if c == 2], reverse=True)
    kicker = [v for v, c in value_counts.items() if c == 1][0]
    return HandEvaluation(PokerHandRank.TWO_PAIR, pairs + [kicker], "兩對")


def _evaluate_pair(value_counts: Dict[int, int]) -> HandEvaluation:
    """評估一對"""
    pair = [v for v, c in value_counts.items() if c == 2][0]
    kickers = sorted([v for v, c in value_counts.items() if c == 1], reverse=True)
    return HandEvaluation(PokerHandRank.PAIR, [pair] + kickers, "一對")


def _is_straight(values: List[int]) -> bool:
    """檢查是否為順子"""
    sorted_values = sorted(set(values))
    if len(sorted_values) != 5:
        return False
    return sorted_values[-1] - sorted_values[0] == 4


# ==============================================================================
# Matchmaking Queue (有狀態的類別，保持不變)
# ==============================================================================


class MatchmakingQueue:
    """匹配隊列管理器 - 修復版（線程安全）"""

    def __init__(self):
        self.queues: Dict[StakeTier, List[MatchmakingEntry]] = {
            StakeTier.NEWBIE: [],
            StakeTier.REGULAR: [],
            StakeTier.PRO: [],
        }
        self.active_games: Dict[str, PokerGameState] = {}

        # 為每個場次添加鎖
        self._queue_locks: Dict[str, asyncio.Lock] = {
            tier.value: asyncio.Lock() for tier in StakeTier
        }

    async def add_to_queue_atomic(
        self, entry: MatchmakingEntry
    ) -> Optional[MatchmakingEntry]:
        """原子性添加到隊列，如果有匹配則返回對手（線程安全版本）"""
        async with self._queue_locks[entry.stake_tier.value]:
            queue = self.queues[entry.stake_tier]

            # 檢查是否已在隊列中
            for existing in queue:
                if existing.user_id == entry.user_id:
                    return None

            # 如果隊列為空，加入隊列
            if not queue:
                queue.append(entry)
                return None

            # 找到對手，進行匹配
            opponent = queue.pop(0)
            return opponent

    async def remove_from_queue_atomic(
        self, user_id: int, stake_tier: StakeTier
    ) -> bool:
        """原子性從隊列中移除玩家（線程安全版本）"""
        async with self._queue_locks[stake_tier.value]:
            queue = self.queues[stake_tier]
            for i, entry in enumerate(queue):
                if entry.user_id == user_id:
                    queue.pop(i)
                    return True
            return False

    async def remove_user_from_all_queues(self, user_id: int) -> bool:
        """從所有隊列中移除用戶（線程安全版本）"""
        removed = False
        for tier in StakeTier:
            if await self.remove_from_queue_atomic(user_id, tier):
                removed = True
        return removed

    def get_queue_stats(self) -> Dict[str, Dict[str, int]]:
        """獲取隊列統計"""
        stats = {}
        for tier in StakeTier:
            try:
                # 嘗試直接訪問
                waiting_count = len(self.queues[tier])
            except KeyError:
                # 如果直接訪問失敗，通過值匹配找到正確的隊列
                waiting_count = 0
                for queue_tier, queue in self.queues.items():
                    if queue_tier.value == tier.value:
                        waiting_count = len(queue)
                        break

            # 計算正在遊戲的玩家數量
            playing_count = 0
            for game in self.active_games.values():
                # 通過值比較而不是實例比較
                if hasattr(game, "stake_tier") and game.stake_tier.value == tier.value:
                    playing_count += 2

            stats[tier.value] = {"waiting": waiting_count, "playing": playing_count}
        return stats

    async def cleanup_expired_entries_atomic(self, timeout_seconds: int = 180):
        """原子性清理過期的隊列條目（線程安全版本）"""
        current_time = time.time()
        cleanup_tasks = []

        for tier in StakeTier:
            cleanup_tasks.append(
                self._cleanup_tier_expired(tier, current_time, timeout_seconds)
            )

        await asyncio.gather(*cleanup_tasks)

    async def _cleanup_tier_expired(
        self, tier: StakeTier, current_time: float, timeout_seconds: int
    ):
        """清理特定場次的過期條目"""
        async with self._queue_locks[tier.value]:
            queue = self.queues[tier]
            self.queues[tier] = [
                entry
                for entry in queue
                if current_time - entry.timestamp < timeout_seconds
            ]


# ==============================================================================
# Poker Game Logic Functions (無狀態函數)
# ==============================================================================


def apply_command(
    game_state: PokerGameState,
    command: Union[PlayerActionCommand, LeaveGameCommand, TimeoutEvent],
):
    """
    應用命令到遊戲狀態（直接修改狀態）。
    成功則無返回，失敗則拋出 BusinessError。
    """
    if isinstance(command, PlayerActionCommand):
        # 處理玩家動作命令
        execute_action(game_state, command.player_index, command.action, command.amount)

    elif isinstance(command, LeaveGameCommand):
        # 處理離開遊戲命令
        _apply_leave_game(game_state, command)

    elif isinstance(command, TimeoutEvent):
        # 處理超時事件 - 當前玩家自動棄牌
        if not game_state.game_over and game_state.winner_id is None:
            timeout_command = PlayerActionCommand(
                player_index=game_state.current_player_index,
                action=PlayerAction.FOLD,
                timestamp=command.timestamp,
            )
            # 遞歸調用
            apply_command(game_state, timeout_command)
    else:
        logger.warning("未知的命令類型: %s", type(command))
        raise InvalidGameActionError(f"未知的命令類型: {type(command)}", "poker")


def _apply_leave_game(game_state: PokerGameState, command: LeaveGameCommand):
    """應用離開遊戲命令"""
    if game_state.game_over:
        return

    # 🔧 修復：在離開遊戲時先收集當前下注到total_bet_this_hand，確保統計正確
    game_state.collect_bets_to_pot()

    # 離開的玩家算輸，對手獲勝
    winner_index = 1 - command.player_index
    game_state.winner_id = (
        game_state.player1.user_id if winner_index == 0 else game_state.player2.user_id
    )
    game_state.win_reason = "對手離開遊戲"
    game_state.game_over = True

    # 計算最終底池
    game_state.final_pot = game_state.pot

    # 🔧 修復：調用正確的結算流程，包含抽水處理
    _finalize_game(game_state)


def _get_player(game_state: PokerGameState, player_index: int):
    """獲取指定索引的玩家"""
    return game_state.player1 if player_index == 0 else game_state.player2


def _get_opponent(game_state: PokerGameState, player_index: int):
    """獲取對手玩家"""
    return game_state.player2 if player_index == 0 else game_state.player1


def _get_opponent_index(player_index: int) -> int:
    """獲取對手索引"""
    return 1 - player_index


def _calculate_call_amount(game_state: PokerGameState, player) -> int:
    """計算跟注金額"""
    return game_state.current_bet - player.current_bet


def can_player_act(game_state: PokerGameState, player_index: int) -> bool:
    """檢查玩家是否可以行動"""
    if game_state.game_over:
        logger.debug(
            "遊戲 %s: 玩家 %s 不能行動 - 遊戲已結束", game_state.game_id, player_index
        )
        return False

    # 🔧 修復BUG：如果遊戲已經有勝負結果或進入攤牌階段，玩家不能行動
    if (
        game_state.winner_id is not None
        or game_state.current_stage == GameStage.SHOWDOWN
    ):
        logger.debug(
            "遊戲 %s: 玩家 %s 不能行動 - 已有勝負結果或攤牌階段",
            game_state.game_id,
            player_index,
        )
        return False

    # 🔧 修復BUG：如果雙方都全押，不需要再行動
    if game_state.player1.is_all_in and game_state.player2.is_all_in:
        logger.debug(
            "遊戲 %s: 玩家 %s 不能行動 - 雙方都全押", game_state.game_id, player_index
        )
        return False

    player = _get_player(game_state, player_index)
    can_act = player.can_act()
    is_current_player = game_state.current_player_index == player_index

    result = can_act and is_current_player
    logger.debug(
        "遊戲 %s: 玩家 %s 行動檢查 - can_act=%s, is_current_player=%s, current_player_index=%s, result=%s",
        game_state.game_id,
        player_index,
        can_act,
        is_current_player,
        game_state.current_player_index,
        result,
    )

    return result


def get_valid_actions(
    game_state: PokerGameState, player_index: int
) -> List[PlayerAction]:
    """獲取玩家可執行的動作"""
    if not can_player_act(game_state, player_index):
        logger.debug(
            "遊戲 %s: 玩家 %s 無法行動，返回空動作列表",
            game_state.game_id,
            player_index,
        )
        return []

    player = _get_player(game_state, player_index)
    opponent = _get_opponent(game_state, player_index)
    call_amount = _calculate_call_amount(game_state, player)

    actions = [PlayerAction.FOLD]  # 棄牌總是可用

    if call_amount == 0:
        actions.append(PlayerAction.CHECK)  # 可以過牌
    elif call_amount <= player.chips:
        actions.append(PlayerAction.CALL)  # 需要跟注

    # 檢查是否可以加注
    # 🔧 修復BUG：如果對手已經全押，不能再加注
    if player.chips > call_amount and not opponent.is_all_in:
        actions.append(PlayerAction.RAISE)

    # 全押總是可用（如果有籌碼）
    if player.chips > 0:
        actions.append(PlayerAction.ALL_IN)

    logger.debug(
        "遊戲 %s: 玩家 %s 可用動作 - call_amount=%s, player_chips=%s, opponent_all_in=%s, actions=%s",
        game_state.game_id,
        player_index,
        call_amount,
        player.chips,
        opponent.is_all_in,
        [a.value for a in actions],
    )

    return actions


def execute_action(
    game_state: PokerGameState,
    player_index: int,
    action: PlayerAction,
    raise_amount: Union[int, str] = 0,
):
    """執行玩家動作"""
    if not can_player_act(game_state, player_index):
        raise NotPlayerTurnError("現在不是你的回合喔！")

    # Convert raise_amount to int if it's a string
    if isinstance(raise_amount, str):
        try:
            raise_amount = int(raise_amount)
        except ValueError as e:
            raise InvalidGameActionError("無效的加注金額", "poker") from e
    elif raise_amount is None:
        raise_amount = 0

    # 執行具體動作
    action_handlers = {
        PlayerAction.FOLD: _execute_fold,
        PlayerAction.CHECK: _execute_check,
        PlayerAction.CALL: _execute_call,
        PlayerAction.RAISE: _execute_raise,
        PlayerAction.ALL_IN: _execute_all_in,
    }

    handler = action_handlers.get(action)
    if not handler:
        raise InvalidGameActionError(f"無效的動作: {action}", "poker")

    # handler 失敗會直接拋出異常
    handler(game_state, player_index, raise_amount)

    # 處理動作後的遊戲流程
    _process_post_action(game_state)


def _execute_fold(game_state: PokerGameState, player_index: int, raise_amount: int = 0):
    """執行棄牌動作"""
    _ = raise_amount  # 未使用的參數
    player = _get_player(game_state, player_index)
    opponent_index = _get_opponent_index(player_index)
    opponent = _get_player(game_state, opponent_index)

    player.has_folded = True
    player.last_action = PlayerAction.FOLD

    # 🔧 修復：在棄牌時先收集當前下注到total_bet_this_hand，確保統計正確
    # 這樣可以避免盲注沒有被計入統計的問題
    game_state.collect_bets_to_pot()

    # 一手牌結束，對手獲勝
    game_state.winner_id = opponent.user_id
    game_state.win_reason = "對手棄牌"
    game_state.final_pot = game_state.pot


def _execute_check(
    game_state: PokerGameState, player_index: int, raise_amount: Union[int, str] = 0
):
    """執行過牌動作"""
    _ = raise_amount  # 未使用的參數
    player = _get_player(game_state, player_index)
    call_amount = _calculate_call_amount(game_state, player)

    if call_amount != 0:
        raise InvalidGameActionError("無法過牌，需要跟注", "poker")

    player.last_action = PlayerAction.CHECK


def _execute_call(
    game_state: PokerGameState, player_index: int, raise_amount: Union[int, str] = 0
):
    """執行跟注動作"""
    _ = raise_amount  # 未使用的參數
    player = _get_player(game_state, player_index)
    call_amount = _calculate_call_amount(game_state, player)

    if call_amount > player.chips:
        raise InsufficientBalanceError(required=call_amount, current=player.chips)

    player.chips -= call_amount
    player.current_bet += call_amount
    player.last_action = PlayerAction.CALL

    if player.chips == 0:
        player.is_all_in = True
        logger.info("遊戲 %s: 玩家 %s 跟注後全押", game_state.game_id, player.user_id)

    logger.info(
        "遊戲 %s: 玩家 %s 跟注 %s，剩餘籌碼 %s",
        game_state.game_id,
        player.user_id,
        call_amount,
        player.chips,
    )


def _execute_raise(
    game_state: PokerGameState, player_index: int, raise_amount: Union[int, str]
):
    """執行加注動作"""
    # Convert to int if string
    if isinstance(raise_amount, str):
        try:
            raise_amount = int(raise_amount)
        except ValueError as e:
            raise InvalidGameActionError("無效的加注金額", "poker") from e
    elif raise_amount is None:
        raise_amount = 0
    player = _get_player(game_state, player_index)
    call_amount = _calculate_call_amount(game_state, player)
    config = STAKE_CONFIGS[game_state.stake_tier]

    # 🔧 修復BUG：最小加注金額應該是大盲注，不是當前最高下注
    min_raise = config["big_blind"]
    # Ensure min_raise is an int
    min_raise = int(min_raise) if isinstance(min_raise, str) else min_raise

    if raise_amount < min_raise:
        raise InvalidGameActionError(f"最小加注金額為 {min_raise}", "poker")

    total_amount = call_amount + raise_amount
    if total_amount > player.chips:
        raise InsufficientBalanceError(required=total_amount, current=player.chips)

    player.chips -= total_amount
    player.current_bet += total_amount
    game_state.current_bet = player.current_bet
    game_state.last_aggressor = player_index
    player.last_action = PlayerAction.RAISE

    if player.chips == 0:
        player.is_all_in = True


def _execute_all_in(
    game_state: PokerGameState, player_index: int, raise_amount: Union[int, str] = 0
):
    """執行全押動作"""
    _ = raise_amount  # 未使用的參數
    player = _get_player(game_state, player_index)

    all_in_amount = player.chips
    player.chips = 0
    player.current_bet += all_in_amount
    player.is_all_in = True
    player.last_action = PlayerAction.ALL_IN

    if player.current_bet > game_state.current_bet:
        game_state.current_bet = player.current_bet
        game_state.last_aggressor = player_index


def _process_post_action(game_state: PokerGameState):
    """處理動作後的遊戲流程"""
    logger.debug(
        f"遊戲 {game_state.game_id}: 處理動作後流程 - "
        f"player1_all_in={game_state.player1.is_all_in}, "
        f"player2_all_in={game_state.player2.is_all_in}, "
        f"current_player_index={game_state.current_player_index}"
    )

    # 🔧 修復BUG：優先檢查全押情況，避免進入錯誤的遊戲流程
    if game_state.player1.is_all_in and game_state.player2.is_all_in:
        logger.info("遊戲 %s: 雙方全押，直接進入攤牌階段", game_state.game_id)
        # 雙方全押，收集下注到底池並直接到攤牌
        game_state.collect_bets_to_pot()
        _deal_remaining_cards(game_state)
        _determine_winner(game_state)
        return

    # 🔧 修復：一方全押且雙方投注相等的情況
    if game_state.player1.is_all_in or game_state.player2.is_all_in:
        # 檢查雙方投注是否相等（表示非全押方已經跟注全押金額）
        if game_state.player1.current_bet == game_state.player2.current_bet:
            logger.info(
                f"遊戲 {game_state.game_id}: 一方全押且雙方投注相等，直接進入攤牌階段"
            )
            game_state.collect_bets_to_pot()
            _deal_remaining_cards(game_state)
            _determine_winner(game_state)
            return

    betting_round_complete = game_state.is_betting_round_complete()
    logger.debug(
        f"遊戲 {game_state.game_id}: 下注輪完成檢查 = {betting_round_complete}"
    )

    if betting_round_complete:
        game_state.collect_bets_to_pot()

        # 檢查是否有一手牌結束
        hand_ended = (
            game_state.winner_id is not None
            or game_state.player1.has_folded
            or game_state.player2.has_folded
        )

        if hand_ended:
            logger.info("遊戲 %s: 一手牌結束，進行結算", game_state.game_id)
            _finalize_game(game_state)
        elif game_state.current_stage == GameStage.RIVER:
            # 河牌圈結束，攤牌
            logger.info("遊戲 %s: 河牌圈結束，進行攤牌", game_state.game_id)
            _determine_winner(game_state)
        else:
            # 進入下一階段
            logger.info("遊戲 %s: 進入下一階段", game_state.game_id)
            game_state.advance_stage()
            game_state.reset_betting_round()

            # 🔧 修復BUG：檢查進入新階段後是否還有人能行動
            # 如果雙方都全押，直接進入攤牌
            if game_state.player1.is_all_in and game_state.player2.is_all_in:
                logger.info(
                    f"遊戲 {game_state.game_id}: 新階段開始但雙方都全押，直接進入攤牌"
                )
                _deal_remaining_cards(game_state)
                _determine_winner(game_state)
                return
    else:
        # 切換到下一個玩家
        old_player_index = game_state.current_player_index
        game_state.switch_current_player()
        logger.debug(
            f"遊戲 {game_state.game_id}: 切換玩家 {old_player_index} -> {game_state.current_player_index}"
        )


def _deal_remaining_cards(game_state: PokerGameState):
    """發完剩餘的公共牌"""
    while len(game_state.community_cards) < 5:
        game_state.community_cards.append(game_state.deck.deal())
    game_state.current_stage = GameStage.SHOWDOWN


def _determine_winner(game_state: PokerGameState):
    """決定勝負"""
    game_state.current_stage = GameStage.SHOWDOWN

    # 評估雙方手牌
    player1_hand = evaluate_hand(
        game_state.player1.hole_cards, game_state.community_cards
    )
    player2_hand = evaluate_hand(
        game_state.player2.hole_cards, game_state.community_cards
    )

    # 比較手牌強度
    comparison = _compare_hands(player1_hand, player2_hand)

    if comparison > 0:
        # 玩家1獲勝
        game_state.winner_id = game_state.player1.user_id
        game_state.win_reason = f"比牌獲勝 ({player1_hand.description})"
    elif comparison < 0:
        # 玩家2獲勝
        game_state.winner_id = game_state.player2.user_id
        game_state.win_reason = f"比牌獲勝 ({player2_hand.description})"
    else:
        # 平局
        game_state.winner_id = None
        game_state.win_reason = "平局"

    game_state.final_pot = game_state.pot
    _finalize_game(game_state)


def _compare_hands(hand1: HandEvaluation, hand2: HandEvaluation) -> int:
    """比較兩手牌的強度
    返回值：1 表示 hand1 更強，-1 表示 hand2 更強，0 表示平局
    """
    if hand1.rank.value > hand2.rank.value:
        return 1
    elif hand1.rank.value < hand2.rank.value:
        return -1
    else:
        # 牌型相同，比較高牌
        if hand1.high_cards > hand2.high_cards:
            return 1
        elif hand1.high_cards < hand2.high_cards:
            return -1
        else:
            return 0


def _finalize_game(game_state: PokerGameState):
    """結算遊戲"""
    # 計算抽水
    if game_state.flop_reached and game_state.final_pot > 0:
        config = STAKE_CONFIGS[game_state.stake_tier]
        rake = min(int(game_state.final_pot * RAKE_PERCENTAGE), config["rake_cap"])
        # Ensure rake is an int
        rake = int(rake) if isinstance(rake, (str, float)) else rake
        game_state.rake_amount = rake
        game_state.final_pot -= rake

    # 分配獎金
    if game_state.winner_id is None:
        # 平局，各自拿回一半
        game_state.player1.chips += game_state.final_pot // 2
        game_state.player2.chips += game_state.final_pot // 2
    elif game_state.winner_id == game_state.player1.user_id:
        game_state.player1.chips += game_state.final_pot
    else:
        game_state.player2.chips += game_state.final_pot

    # 標記需要記錄手牌統計
    game_state._hand_stats_pending = True

    # 一手牌結束，不在這裡決定下一步
    # 讓 COG 層處理延遲和繼續邏輯
