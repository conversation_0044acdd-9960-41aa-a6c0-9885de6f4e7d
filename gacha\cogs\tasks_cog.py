import asyncio

import asyncpg
from discord.ext import commands, tasks

from config.app_config import get_settings
from utils.logger import logger


class TasksCog(commands.Cog):
    """一個專門用於管理資料庫定期刷新任務的 Cog。"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.refresh_stats_materialized_views.start()
        logger.info("TasksCog 已載入，統計物化視圖刷新任務已啟動。")

    async def cog_unload(self):
        self.refresh_stats_materialized_views.cancel()
        logger.info("TasksCog 已卸載，統計物化視圖刷新任務已停止。")

    @tasks.loop(hours=1)
    async def refresh_stats_materialized_views(self):
        logger.info("[TASKS] 開始提交背景任務以刷新物化視圖...")
        try:
            db_url = get_settings().DATABASE_URL
            if not db_url:
                logger.error("[TASKS] 無法獲取 DATABASE_URL，取消刷新任務。")
                return
            # 使用 to_thread 將阻塞操作（包括建立新連接）放到背景執行緒
            await asyncio.to_thread(self._blocking_refresh_views, db_url)
            logger.info("[TASKS] 背景物化視圖刷新任務已成功提交。")
        except Exception as e:
            logger.error("[TASKS] 提交背景刷新任務時發生錯誤: %s", e, exc_info=True)

    def _blocking_refresh_views(self, db_url: str):
        """
        這是一個同步函式，在獨立線程中執行。
        它會建立自己的事件循環和獨立的資料庫連接。
        """

        async def refresh_in_new_loop():
            conn = None
            try:
                # 1. 在新線程的新循環中建立一個全新的、獨立的連接
                conn = await asyncpg.connect(dsn=db_url)
                logger.info("[TASKS][BG] 已在背景線程中成功建立獨立的資料庫連接。")

                views_to_refresh = [
                    "gacha_user_rankings_mv",
                    "gacha_server_stats_mv",
                    "gacha_user_game_rankings_mv",
                    "gacha_user_luck_summary_mv",
                ]
                logger.info(
                    f"[TASKS][BG] 開始刷新 {len(views_to_refresh)} 個物化視圖..."
                )
                for view_name in views_to_refresh:
                    try:
                        logger.info(f"[TASKS][BG] 正在刷新 {view_name}...")
                        await conn.execute(
                            f"REFRESH MATERIALIZED VIEW CONCURRENTLY {view_name};",
                            timeout=300.0,
                        )
                        logger.info(f"[TASKS][BG] 已成功刷新 {view_name}。")
                    except Exception as e:
                        logger.error(
                            f"[TASKS][BG] 刷新 {view_name} 時發生錯誤: {e}",
                            exc_info=True,
                        )
                logger.info("[TASKS][BG] 所有統計相關物化視圖刷新完成。")
            except Exception as e:
                logger.error(
                    f"[TASKS][BG] 在背景線程中執行刷新時發生頂層錯誤: {e}",
                    exc_info=True,
                )
            finally:
                # 3. 確保無論如何都關閉這個獨立的連接
                if conn and not conn.is_closed():
                    await conn.close()
                    logger.info("[TASKS][BG] 已關閉背景線程中的獨立資料庫連接。")

        # 2. 為這個新線程創建一個新的事件循環來運行異步的 refresh_in_new_loop 函式
        asyncio.run(refresh_in_new_loop())

    @refresh_stats_materialized_views.before_loop
    async def before_refresh(self):
        await self.bot.wait_until_ready()
        logger.info("[TASKS] 機器人已上線，統計刷新任務即將開始循環。")


async def setup(bot: commands.Bot):
    await bot.add_cog(TasksCog(bot))
