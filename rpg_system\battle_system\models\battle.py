"""
戰鬥（Battle）領域模型
"""

import random
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from ..handlers import event_system
from .combatant import Combatant
from .enums import BattleStatus, EventType
from .skill_instance import SkillInstance


@dataclass
class Battle:
    """戰鬥領域模型"""

    battle_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    player_team: List[Combatant] = field(default_factory=list)
    monster_team: List[Combatant] = field(default_factory=list)
    battle_status: BattleStatus = BattleStatus.IN_PROGRESS
    current_turn: int = 0
    turn_order: List[Combatant] = field(default_factory=list)
    battle_log: List[Any] = field(default_factory=list)
    _rng: random.Random = field(default_factory=random.Random)

    async def start(self, config_loader, attribute_calculator) -> None:
        """初始化戰鬥"""
        for combatant in self.player_team + self.monster_team:
            await combatant.calculate_final_stats(config_loader, attribute_calculator)
        self.determine_turn_order()
        self.current_turn = 1
        await self.trigger_event(EventType.ON_BATTLE_START.value, {})

    def determine_turn_order(self) -> None:
        """根據速度決定行動順序"""
        all_combatants = self.player_team + self.monster_team
        self.turn_order = sorted(
            all_combatants, key=lambda c: c.current_stats.get("spd", 0), reverse=True
        )

    def is_battle_over(self) -> bool:
        """檢查戰鬥是否結束"""
        if not any(c.is_alive() for c in self.player_team):
            self.battle_status = BattleStatus.MONSTER_WIN
            return True
        if not any(c.is_alive() for c in self.monster_team):
            self.battle_status = BattleStatus.PLAYER_WIN
            return True
        return False

    async def get_acting_combatant(self) -> Optional[Combatant]:
        """獲取當前行動的戰鬥單位"""
        if not self.turn_order:
            return None
        return self.turn_order[0]

    async def advance_turn(self) -> None:
        """推進到下一個行動單位"""
        if not self.is_battle_over():
            self.turn_order.append(self.turn_order.pop(0))
            if self.turn_order[0] == self.player_team[0]:
                self.current_turn += 1
            await self.trigger_event(
                EventType.ON_TURN_START.value,
                {
                    "acting_combatant_id": self.turn_order[0].instance_id,
                    "current_turn_number": self.current_turn,
                },
            )

    async def process_action(
        self, caster: Combatant, skill_id: str, target_ids: List[str]
    ) -> None:
        """處理單個行動"""
        skill_instance = caster.get_skill_instance(skill_id)
        if not skill_instance:
            raise ValueError(f"找不到技能: {skill_id}")

        from rpg_system.config.loader import get_config_loader

        config_loader = get_config_loader()
        all_configs = await config_loader.get_all_configs()
        skill_definition = await skill_instance.get_definition(all_configs)

        targets = await self._select_skill_targets(caster, skill_definition)
        await self._handle_skill_resources(caster, skill_instance, skill_definition)
        effect_results = await self._trigger_skill_execution_event(
            caster, skill_id, skill_definition, targets
        )

        self.add_log_entry(
            f"{caster.name} 使用了 {skill_definition.get('name', skill_id)}",
            "SKILL",
            caster.instance_id,
            [t.instance_id for t in targets],
            {"skill_id": skill_id, "results": effect_results},
        )

    async def _select_skill_targets(
        self, caster: Combatant, skill_definition: Dict[str, Any]
    ) -> List[Combatant]:
        """選擇技能目標"""
        from ..handlers import target_selector

        target_logic = skill_definition.get("target_logic")
        if not target_logic:
            return []
        return await target_selector.select_targets(caster, target_logic, self)

    async def _handle_skill_resources(
        self,
        caster: Combatant,
        skill_instance: SkillInstance,
        skill_definition: Dict[str, Any],
    ) -> None:
        """處理技能資源消耗和冷卻"""
        mp_cost = skill_definition.get("mp_cost", 0)
        if not caster.consume_mp(mp_cost):
            raise ValueError("MP不足")
        cooldown = skill_definition.get("cooldown_turns", 0)
        skill_instance.current_cooldown = cooldown

    async def _trigger_skill_execution_event(
        self,
        caster: Combatant,
        skill_id: str,
        skill_definition: Dict[str, Any],
        targets: List[Combatant],
    ) -> List[Dict[str, Any]]:
        """觸發技能執行事件並應用效果"""
        from ..handlers import effect_applier

        skill_instance = caster.get_skill_instance(skill_id)
        if not skill_instance:
            return []
        return await effect_applier.apply_skill_effects(
            caster, targets, skill_instance, self
        )

    def add_log_entry(
        self,
        message: str,
        action_type: str,
        actor_id: Optional[str],
        target_ids: Optional[List[str]],
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """添加戰鬥日誌條目"""
        from .battle_log import BattleLogEntry

        log_entry = BattleLogEntry(
            turn_number=self.current_turn,
            message=message,
            action_type=action_type,
            actor_id=actor_id,
            target_ids=target_ids or [],
            details=details or {},
        )
        self.battle_log.append(log_entry)

    def get_all_alive_combatants(self) -> List[Combatant]:
        """獲取所有存活的戰鬥單位"""
        return [c for c in self.player_team + self.monster_team if c.is_alive()]

    def get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]:
        """獲取指定戰鬥單位的所有存活敵方"""
        target_team = (
            self.monster_team if combatant.is_player_side else self.player_team
        )
        return [c for c in target_team if c.is_alive()]

    def get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]:
        """獲取指定戰鬥單位的所有存活友方（包括自己）"""
        target_team = (
            self.player_team if combatant.is_player_side else self.monster_team
        )
        return [c for c in target_team if c.is_alive()]

    def get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]:
        """根據實例ID查找戰鬥單位"""
        for c in self.player_team + self.monster_team:
            if c.instance_id == instance_id:
                return c
        return None

    async def auto_battle_step(self) -> Dict[str, Any]:
        """自動執行一個戰鬥步驟"""
        if self.is_battle_over():
            return {"battle_ended": True}

        actor = await self.get_acting_combatant()
        if not actor or not await actor.can_act():
            await self.advance_turn()
            return {"turn_advanced": True}

        skill_id, _ = await actor.get_next_action()
        await self.process_action(actor, skill_id, [])
        await self.advance_turn()
        return {"action_processed": True}

    async def trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """觸發戰鬥事件"""
        await event_system.trigger_event(event_type, event_data, self)

    async def trigger_death_event(self, dead_combatant: Combatant):
        """觸發戰鬥單位死亡事件"""
        await self.trigger_event(
            EventType.ON_COMBATANT_DEATH.value,
            {"dead_combatant_id": dead_combatant.instance_id},
        )
