from __future__ import annotations

from typing import TYPE_CHECKING, List, Optional, TypedDict

import discord

from config.app_config import get_config
from gacha.constants import RarityLevel
from gacha.models.shop_models import ExchangeSessionData, ShopItemDefinition
from gacha.views import utils as view_utils
from utils.logger import logger

if TYPE_CHECKING:
    from gacha.models.models import Card
ENCYCLOPEDIA_ICON_URL = "https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif"


class _CardDisplayInfo(TypedDict):
    name: str
    card_id: Optional[int]
    series: str
    image_url: Optional[str]
    rarity_enum: Optional[RarityLevel]
    pool_type: str


def _extract_card_display_info(
    card: Card, ticket_definition: ShopItemDefinition
) -> _CardDisplayInfo:
    """Helper to extract and process card details for display."""
    card_id_val = getattr(card, "card_id", None)
    rarity_attribute = getattr(card, "rarity", None)
    rarity_enum_val: Optional[RarityLevel] = None
    if isinstance(rarity_attribute, RarityLevel):
        rarity_enum_val = rarity_attribute
    elif rarity_attribute is not None:
        try:
            rarity_enum_val = RarityLevel(int(rarity_attribute))
        except (ValueError, TypeError):
            logger.warning(
                "無法將卡片稀有度值 '%s' 轉換為 RarityLevel Enum。卡片ID: %s",
                rarity_attribute,
                card_id_val,
            )
    pool_type_val = getattr(card, "pool_type", None)
    if pool_type_val is None:
        pool_type_val = ticket_definition.pool_type
    if pool_type_val is None:
        pool_type_val = "main"
    return _CardDisplayInfo(
        name=getattr(card, "name", "未知卡片"),
        card_id=card_id_val,
        series=getattr(card, "series", "未知系列"),
        image_url=getattr(card, "image_url", None),
        rarity_enum=rarity_enum_val,
        pool_type=pool_type_val,
    )


def build_specific_ticket_selection_embed(
    interaction: discord.Interaction,
    session_data: ExchangeSessionData,
    cards_on_page: List[Card],
    pending_selected_card_objects: List[Card],
    current_page: int,
    total_pages: int,
    search_query: Optional[str] = None,
) -> discord.Embed:
    """
    建立用於指定券選擇卡片的 Embed。
    """
    ticket_name = session_data.ticket_definition.display_name
    author_text = f"正在選擇【{ticket_name}】的卡片"
    if search_query:
        author_text += f" (搜尋: `{search_query}`)"
    total_items_on_page = len(cards_on_page)
    embed_color = discord.Color.light_grey()
    card_info_on_current_page: Optional[_CardDisplayInfo] = None
    if cards_on_page:
        card_info_on_current_page = _extract_card_display_info(
            cards_on_page[0], session_data.ticket_definition
        )
        if card_info_on_current_page["rarity_enum"]:
            embed_color = view_utils.get_rarity_color(
                card_info_on_current_page["rarity_enum"],
                card_info_on_current_page["pool_type"],
            )
    embed = discord.Embed(title=None, color=embed_color)
    embed.set_author(name=author_text, icon_url=ENCYCLOPEDIA_ICON_URL)
    description_lines = []
    if pending_selected_card_objects:
        description_lines.append("**已選擇的卡片:**")
        for i, selected_card_object in enumerate(pending_selected_card_objects):
            selected_card_info = _extract_card_display_info(
                selected_card_object, session_data.ticket_definition
            )
            card_name = selected_card_info["name"]
            card_series = selected_card_info["series"]
            rarity_enum_val = selected_card_info["rarity_enum"]
            pool_type = selected_card_info["pool_type"]
            rarity_emoji = "❓"
            if rarity_enum_val is not None:
                rarity_emoji = view_utils.get_encyclopedia_rarity_emoji(
                    rarity_enum_val, pool_type
                )
            else:
                logger.warning(
                    "Missing rarity_enum for selected card ID %s in embed builder.",
                    selected_card_info["card_id"],
                )
            description_lines.append(
                f"  `{i + 1}.` {rarity_emoji} **{card_name}** (*{card_series}*)"
            )
    description_lines.append(f"**兌換券**: {ticket_name}")
    num_selected = len(session_data.pending_selected_cards)
    total_to_select = session_data.total_quantity_to_redeem
    if num_selected >= total_to_select:
        progress_message = "選擇完畢，請點擊「確認兌換」。"
    else:
        next_selection_number = num_selected + 1
        progress_message = f"您正在選擇第 {next_selection_number} 張卡，共需選擇 {total_to_select} 張。"
    description_lines.append(f"**進度**: {progress_message}")
    current_embed_description = "\n".join(description_lines)
    if not card_info_on_current_page:
        embed.description = current_embed_description
        embed.add_field(
            name="沒有可選的卡片", value="當前條件下沒有符合的卡片。", inline=False
        )
    else:
        rarity_emoji = "❓"
        display_rarity_name = "未知"
        if card_info_on_current_page["rarity_enum"]:
            rarity_emoji = view_utils.get_encyclopedia_rarity_emoji(
                card_info_on_current_page["rarity_enum"],
                card_info_on_current_page["pool_type"],
            )
            display_rarity_name = view_utils.get_user_friendly_rarity_name(
                card_info_on_current_page["rarity_enum"]
            )
        pool_prefixes = get_config("gacha_core_settings.pool_type_prefixes", {})
        pool_display_text = (
            pool_prefixes.get(card_info_on_current_page["pool_type"], "")
            if isinstance(pool_prefixes, dict)
            else ""
        )
        temp_card_name = card_info_on_current_page["name"]
        temp_card_series = card_info_on_current_page["series"]
        card_detail_lines_for_desc = [
            f"{rarity_emoji} **{temp_card_name}**",
            f"<:ReplyCont:1383146319425699931> *{temp_card_series}*",
            f"<:ReplyCont:1383146319425699931>{display_rarity_name} | {pool_display_text}",
        ]
        embed.description = (
            current_embed_description + "\n\n" + "\n".join(card_detail_lines_for_desc)
        )
        if card_info_on_current_page["image_url"]:
            embed.set_image(url=card_info_on_current_page["image_url"])
        if card_info_on_current_page["rarity_enum"]:
            from config.app_config import get_rarity_images_url

            rarity_images_config = get_rarity_images_url()
            all_pools_images = rarity_images_config.get("all_pools", {})
            temp_rarity_enum_value = card_info_on_current_page["rarity_enum"].value
            thumbnail_url = all_pools_images.get(temp_rarity_enum_value)
            if thumbnail_url is None:
                thumbnail_url = all_pools_images.get(str(temp_rarity_enum_value))
            if thumbnail_url:
                embed.set_thumbnail(url=thumbnail_url)
            else:
                temp_rarity_enum_name = card_info_on_current_page["rarity_enum"].name
                temp_pool_type = card_info_on_current_page["pool_type"]
                temp_card_id = card_info_on_current_page["card_id"]
                logger.warning(
                    "無法獲取稀有度 '%s' (值: %s, 池: %s) 的縮圖 URL。卡片ID: %s",
                    temp_rarity_enum_name,
                    temp_rarity_enum_value,
                    temp_pool_type,
                    temp_card_id,
                )
    page_info_for_footer = (
        f"第 {current_page} 頁 / 共 {total_pages} 頁 ({total_items_on_page} 張卡片)"
    )
    footer_card_id_part = ""
    if card_info_on_current_page and card_info_on_current_page["card_id"] is not None:
        temp_footer_card_id = card_info_on_current_page["card_id"]
        footer_card_id_part = f" • Card ID: {temp_footer_card_id}"
    embed.set_footer(text=f"{page_info_for_footer}{footer_card_id_part}")
    return embed
