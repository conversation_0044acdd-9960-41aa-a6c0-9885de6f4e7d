from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class StockLifecycleStatus(str, Enum):
    """股票生命週期狀態"""

    ACTIVE = "active"  # 活躍
    ST = "st"  # Special Treatment / 特別處理
    DELISTED = "delisted"  # 已退市


class StockAssetCreate(BaseModel):
    """
    用於創建新股票資產的 Pydantic 模型。
    只包含應用程式在創建時需要提供的欄位。
    """

    asset_symbol: str
    asset_name: str
    description: Optional[str] = None
    current_price: Decimal  # 新股的初始當前價格
    linked_criteria_type: Optional[str] = None
    linked_criteria_value: Optional[str] = None
    linked_pool_context: Optional[str] = None
    base_volatility: Decimal
    volatility_factor: Decimal
    influence_weight: Decimal
    initial_anchor_price: Decimal  # 新股的初始錨定價格
    total_shares: int
    lifecycle_status: StockLifecycleStatus = StockLifecycleStatus.ACTIVE

    class Config:
        use_enum_values = True


class StockAsset(BaseModel):
    """
    對應 public.virtual_assets 資料庫表格的 Pydantic 模型，代表市場上的股票資產。
    """

    asset_id: int = Field(..., description="資產的唯一ID")
    asset_symbol: str = Field(..., description="資產的交易代碼/符號")
    asset_name: str = Field(..., description="資產的名稱")
    description: Optional[str] = Field(None, description="資產的描述文本")

    current_price: Decimal = Field(..., description="資產當前價格")

    linked_criteria_type: Optional[str] = Field(
        None, description="關聯標準的類型 (例如 'gacha_series', 'gacha_rarity')"
    )
    linked_criteria_value: Optional[str] = Field(
        None, description="關聯標準的值 (例如系列名稱, 稀有度等級)"
    )
    linked_pool_context: Optional[str] = Field(
        None, description="關聯的卡池上下文 (例如 'main', 'special')"
    )

    base_volatility: Decimal = Field(..., description="基礎波動率")
    volatility_factor: Decimal = Field(..., description="波動放大因子")
    influence_weight: Decimal = Field(
        ..., description="影響力權重 (可能用於新聞或其他外部因素)"
    )
    total_shares: int = Field(..., description="股票的總股本數量")

    created_at: datetime = Field(..., description="資產創建時間")
    last_updated: datetime = Field(..., description="資產資訊最後更新時間")

    initial_anchor_price: Decimal = Field(..., description="初始錨定價格")
    current_anchor_price: Optional[Decimal] = Field(None, description="當前錨定價格")
    anchor_price_updated_at: Optional[datetime] = Field(
        None, description="錨定價格最後更新時間"
    )

    lifecycle_status: StockLifecycleStatus = Field(
        default=StockLifecycleStatus.ACTIVE, description="股票的生命週期狀態"
    )

    class Config:
        from_attributes = True  # 更新自 Pydantic v1 的 orm_mode
        use_enum_values = True  # 確保枚舉值在序列化時使用其 value
