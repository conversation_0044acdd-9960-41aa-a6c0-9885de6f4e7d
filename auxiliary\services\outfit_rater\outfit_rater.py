"""
OutfitRater模組 - 處理穿搭評分的主要邏輯
重構為模組級函數架構，移除類和實例依賴
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, Optional

from auxiliary.data.prompts.prompts.unified_prompts import (
    OUTFIT_SYSTEM_PROMPT,
    OUTFIT_USER_PROMPT,
    build_prompt_with_history,
)

# 相對導入
from ..ai_core import ai_service
from .rating_history import RatingHistory

# 設置日誌
logger = logging.getLogger("OutfitRater")

# 模組級別的評分歷史管理器
_rating_history = RatingHistory()


async def rate_outfit_from_binary(
    image_data: bytes,
    prompt: Optional[str] = None,
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    從二進制圖像數據評分穿搭

    參數:
        image_data (bytes): 圖像的二進制數據
        prompt (str, optional): 自定義提示，如果不提供則使用默認提示
        request_id (str, optional): 請求標識符
        user_id (str, optional): 用戶ID，用於獲取歷史評分

    返回:
        Dict: 評分結果
    """
    # 記錄請求ID，用於追蹤
    # 移除詳細的請求處理日誌

    try:
        # 使用用戶提示或默認提示
        user_prompt = prompt or OUTFIT_USER_PROMPT

        # 獲取並整合用戶歷史記錄到系統提示中
        system_prompt = OUTFIT_SYSTEM_PROMPT
        if user_id:
            # 獲取並整合用戶歷史
            user_history_text = await _rating_history.format_history_for_prompt(user_id)
            if user_history_text:
                system_prompt = build_prompt_with_history(
                    system_prompt, user_history_text
                )
        # 移除歷史記錄添加日誌

        # 使用 ai_service 處理圖像
        api_response = await ai_service.process_with_image(
            image_data=image_data,
            prompt=user_prompt,
            system_prompt=system_prompt,
            request_id=request_id,
        )

        # 提取評分結果
        result = extract_rating_result(api_response)

        # 如果提供了用戶ID，保存評分結果
        if user_id and result:
            await _rating_history.add_rating(user_id, result)
        # 移除評分結果保存日誌

        return result
    except ValueError:
        # 直接重新拋出由 ai_service.process_with_image 引發的 ValueError
        # 讓上層的 outfit_service 來處理給使用者的提示
        raise
    except Exception as e:
        logger.error("處理圖像數據時發生未知錯誤: %s", str(e), exc_info=True)
        # 對於其他未知錯誤，也向上拋出
        raise


async def rate_outfit_from_url(
    image_url: str,
    prompt: Optional[str] = None,
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    從URL評分穿搭

    參數:
        image_url (str): 圖像的URL
        prompt (str, optional): 自定義提示，如果不提供則使用默認提示
        request_id (str, optional): 請求標識符
        user_id (str, optional): 用戶ID，用於獲取歷史評分

    返回:
        Dict: 評分結果
    """
    try:
        # 使用統一的圖片下載方法
        from ..ai_core.message_handler import _download_image_from_url

        image_data = await _download_image_from_url(image_url)

        if not image_data:
            raise Exception(f"從URL下載圖像失敗: {image_url}")

        return await rate_outfit_from_binary(image_data, prompt, request_id, user_id)
    except Exception as e:
        logger.error("從URL處理圖像時出錯: %s", str(e))
        raise


def extract_rating_result(api_response) -> Dict[str, Any]:
    """
    從API響應中提取評分結果，直接解析XML內容
    """
    from ..ai_core import ai_service

    response_text: Optional[str] = None
    try:
        # 使用通用的 parser 函數
        response_text = ai_service._get_raw_content_from_response(api_response)
        if not response_text:
            raise ValueError("AI 回應文本為空或無效")

        if not isinstance(response_text, str) or not response_text.strip():
            logger.warning("AI響應文本為空或無效: '%s'", response_text)
            raise ValueError("AI響應文本為空或無效")

        # 清理響應文本，移除特殊編碼字符和nonce信息
        cleaned_response = re.sub(r"<0x[0-9A-Fa-f]{2}>", "", response_text)
        cleaned_response = re.sub(
            r"\[Request Nonce: [a-f0-9\-]+\]", "", cleaned_response
        )

        # 直接在整個響應中查找評分數據
        content = cleaned_response

        # 使用正則表達式提取評分數據
        score_match = re.search(r"<評分>(.*?)</評分>", content, re.DOTALL)
        review_match = re.search(r"<評價>(.*?)</評價>", content, re.DOTALL)
        suggestion_match = re.search(r"<建議>(.*?)</建議>", content, re.DOTALL)
        conclusion_match = re.search(r"<結論>(.*?)</結論>", content, re.DOTALL)

        # 獲取當前時間戳
        timestamp = datetime.now().isoformat(timespec="milliseconds")

        # 提取評分數值 (去除 "/10" 後綴)
        score_value = score_match.group(1) if score_match else "N/A"
        if score_value != "N/A":
            # 提取純數字部分
            score_number_match = re.match(
                r"^(\d+(?:\.\d+)?)(?:/10(?:分)?)?$", score_value.strip()
            )
            if score_number_match:
                score_value = score_number_match.group(1)  # 只保留數字部分

        return {
            "score": score_value,
            "review": review_match.group(1) if review_match else "無法提取評價",
            "suggestion": (
                suggestion_match.group(1) if suggestion_match else "無法提取建議"
            ),
            "conclusion": (
                conclusion_match.group(1) if conclusion_match else "無法提取結論"
            ),
            "timestamp": timestamp,
        }

    except Exception as e:
        logger.error(
            "提取評分結果時出錯: %s. Response text (first 500 chars): %s",
            str(e),
            str(response_text)[:500] if response_text is not None else "None",
        )

        fallback_timestamp = datetime.now().isoformat(timespec="milliseconds")

        return {
            "score": "N/A",
            "review": "評分處理時發生錯誤",
            "suggestion": "請稍後再試",
            "conclusion": "暫時無法提供評分",
            "timestamp": fallback_timestamp,
        }


# 模組級函數已經在上面定義，不需要額外的實例或委託函數
