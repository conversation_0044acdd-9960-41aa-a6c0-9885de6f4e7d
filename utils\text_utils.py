"""
文本處理工具模組
提供常用的文本操作函數
"""

from typing import List


def split_text_nicely(text: str, max_length: int) -> List[str]:
    """
    將長文本分割成多個部分，盡量在自然的斷句點（如換行符）分割。
    
    Args:
        text: 要分割的文本
        max_length: 每個部分的最大長度
        
    Returns:
        分割後的文本列表
        
    Examples:
        >>> split_text_nicely("Hello\nWorld\nThis is a test", 10)
        ['Hello\nWorld', 'This is a test']
    """
    if len(text) <= max_length:
        return [text]

    parts = []
    while len(text) > max_length:
        # 找到在長度限制內最後一個換行符
        split_pos = text.rfind("\n", 0, max_length)
        # 如果找不到換行符，就硬性分割
        if split_pos == -1:
            split_pos = max_length

        parts.append(text[:split_pos])
        text = text[split_pos:].lstrip()  # 移除下一段開頭的空白

    parts.append(text)
    return parts