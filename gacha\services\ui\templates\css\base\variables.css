/* variables.css - 全局CSS變數 */
:root {
  /* 主題顏色 - 更現代的配色方案 */
  --color-primary: #6366f1; /* 靛藍色，作為強調色 */
  --color-primary-light: #818cf8;
  --color-primary-rgb: 99, 102, 241;
  
  /* 功能顏色 - 更柔和現代的色調 */
  --color-achievements: #f59e0b;  /* 橙色 - Achievements */
  
  /* 中性色 - 更深邃精緻的深色主題 */
  --color-bg-darker: #0a0a0a; /* 更深的背景，用於層次感 */
  --color-text-light: #f3f4f6; /* 淺色文字，更清晰 */
  --color-text-gray: #94a3b8; /* 更柔和的灰色文字 */
  --color-border: rgba(255, 255, 255, 0.08); /* 更微妙的邊框 */
  
  /* 動畫時間 */
  
  /* 陰影 - 更現代的陰影效果 */
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  
  /* 圓角 - 更現代的圓角 */
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;
  
  /* 間距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字體大小 */
  --font-xxs: 0.75rem; /* 添加更小的字體尺寸 */
  --font-xs: 0.875rem;
  --font-sm: 1rem;
  --font-md: 1.125rem;
  --font-lg: 1.375rem;
  --font-3xl: 2.75rem;
  
  /* 卡片尺寸 */
  --main-card-width: 320px;
  --main-card-height: 448px;
  --sub-card-width: 180px;
  --sub-card-height: 252px;
  
  /* 用戶頭像尺寸 */
  --avatar-size: 120px;
  --avatar-font-size: 3.5rem;
} 