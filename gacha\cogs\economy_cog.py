"""
Gacha系統經濟相關指令的COG。
包含 /sw (sell_card) 指令。
注意：/daily、/hourly 和 /balance 指令已拆分到獨立的 cog 中。
"""

from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.collection_service as collection_service
import gacha.services.economy_service as economy_service

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from gacha.constants import RarityLevel
from gacha.exceptions import BusinessError
from gacha.models.filters import CollectionFilters
from gacha.views import utils as view_utils
from gacha.views.ui_components.confirmation import ConfirmationView
from gacha.views.utils import get_pool_type_choices
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class EconomyCog(commands.Cog, name="經濟"):
    """處理經濟相關指令，如卡片販賣。"""

    def __init__(self, bot: BotType):
        self.bot = bot
        logger.info("EconomyCog initialized.")

    async def cog_load(self):
        logger.info("EconomyCog has been loaded.")

    @staticmethod
    def _create_base_embed(
        interaction: discord.Interaction, sell_plan: Dict[str, Any], stage: str
    ) -> discord.Embed:
        if stage == "confirmation":
            total_cards = sell_plan.get("total_quantity_sold", 0)
            title = f"⚠️ 確認賣出 {total_cards} 張卡片？"
            color = discord.Color.red()
            embed = discord.Embed(title=title, color=color, timestamp=datetime.now())
        else:
            total_cards = sell_plan.get("total_cards_sold", 0)
            embed = SuccessEmbed(title="賣出操作成功", timestamp=datetime.now())

        embed.set_author(
            name=interaction.user.display_name,
            icon_url=(
                interaction.user.display_avatar.url
                if interaction.user.display_avatar
                else None
            ),
        )
        return embed

    @staticmethod
    def _add_operation_description(
        embed: discord.Embed, sell_plan: Dict[str, Any], stage: str
    ):
        from config.app_config import get_oil_emoji

        operation_summary = sell_plan.get("operation_summary", {})
        if not operation_summary:
            return

        op_type_disp = operation_summary.get("operation_type_display", "未知操作")
        filters_disp = operation_summary.get("filters_display", "卡片")
        fav_disp = operation_summary.get("include_favorites_display", "")
        is_single = operation_summary.get("is_single_card_operation", False)

        op_desc = (
            f"賣出1張 {filters_disp}"
            if is_single
            else f"{op_type_disp} {filters_disp or '所有'} 卡片 {fav_disp}".strip()
        )
        description = f"**操作詳情：** {op_desc}"

        if stage == "confirmation":
            revenue = int(sell_plan.get("total_revenue", 0))
            description += f"\n\n預計總收入為 **{revenue:,}** {get_oil_emoji()}。\n此操作**無法撤銷**！"
        embed.description = description

    @staticmethod
    def _add_success_stats(embed: discord.Embed, sell_plan: Dict[str, Any]):
        from config.app_config import get_oil_emoji

        total_cards = sell_plan.get("total_cards_sold", 0)
        revenue = int(sell_plan.get("total_revenue", 0))
        balance = sell_plan.get("new_balance", "未知")
        embed.add_field(name="總共賣出", value=f"{total_cards:,}張卡片", inline=True)
        embed.add_field(
            name="總獲得", value=f"{revenue:,} {get_oil_emoji()}", inline=True
        )
        embed.add_field(
            name="當前餘額", value=f"{balance:,} {get_oil_emoji()}", inline=True
        )

    @staticmethod
    def _add_rarity_stats(embed: discord.Embed, cards_summary: List[Dict[str, Any]]):
        pool_rarity_counts = view_utils.get_pool_rarity_stats(cards_summary)
        for pool_type, counts in pool_rarity_counts.items():
            if not counts:
                continue
            stats = []
            for rarity_val in sorted(counts.keys(), reverse=True):
                try:
                    level = RarityLevel(rarity_val)
                    emoji = view_utils.get_rarity_display_code(level, pool_type)
                    stats.append(f"{emoji} x{counts[rarity_val]}")
                except ValueError:
                    stats.append(f"❓ x{counts[rarity_val]}")
            if stats:
                from config.app_config import get_pool_type_names

                pool_name = get_pool_type_names().get(pool_type, pool_type)
                embed.add_field(
                    name=f"{pool_name}稀有度統計", value=" | ".join(stats), inline=False
                )

    @staticmethod
    def _add_card_list(
        embed: discord.Embed, cards_summary: List[Dict[str, Any]], stage: str
    ):
        from config.app_config import get_oil_emoji

        title = "預計賣出清單" if stage == "confirmation" else "賣出卡片範例"
        sorted_cards = sorted(
            cards_summary,
            key=lambda x: x.get("total_value_sold_for_card", 0),
            reverse=True,
        )
        parts = []
        for card in sorted_cards[:15]:
            name = card.get("name", "未知")
            qty = card.get("quantity_sold", 0)
            val = int(card.get("total_value_sold_for_card", 0))
            fav = " ❤️" if card.get("is_favorite") else ""
            parts.append(f"**{name}** x{qty} (共 {val} {get_oil_emoji()}){fav}")

        text = "\n".join(parts)
        if len(sorted_cards) > 15:
            text += f"\n...以及其他 {len(sorted_cards) - 15} 種卡片"
        if len(text) > 1024:
            text = text[:1020] + "..."
        embed.add_field(name=title, value=text or "無", inline=False)

    @staticmethod
    def _finalize_embed_style(embed: discord.Embed, stage: str):
        if stage == "success":
            embed.set_thumbnail(
                url="https://cdn.discordapp.com/attachments/1336020673730187334/1375776759953363074/watashi-ni-tenshi-money.gif?ex=6832eb74&is=683199f4&hm=b778efe267717dd1bbca456cf2edaa13b2f5516aa9b99f30d945970a61281acb&"
            )
            embed.set_footer(
                text="交易已完成",
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1382178449791127562/clock.png?ex=684a357e&is=6848e3fe&hm=3707985489de28e6335f4e9944b6a8743712c2c6f916f2ac007aac1de88b1382&",
            )
        else:
            embed.set_footer(
                text="請仔細考慮後再確認。",
                icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1387855412942082239/confirmation.png?ex=685edc93&is=685d8b13&hm=5e05fe346fd991275bfd7dbfe1fcb78c844820affb1462f17b979fdccb1310d5&",
            )

    @classmethod
    def _create_sell_embed(
        cls, interaction: discord.Interaction, sell_plan: Dict[str, Any], stage: str
    ) -> discord.Embed:
        embed = cls._create_base_embed(interaction, sell_plan, stage)
        cls._add_operation_description(embed, sell_plan, stage)

        if stage == "success":
            cls._add_success_stats(embed, sell_plan)

        cards_summary = sell_plan.get(
            "sold_cards_details_for_response", []
        ) or sell_plan.get("sold_cards_summary", [])
        if cards_summary:
            cls._add_rarity_stats(embed, cards_summary)
            cls._add_card_list(embed, cards_summary, stage)

        cls._finalize_embed_style(embed, stage)
        return embed

    async def _build_sell_filters(
        self,
        user_id: int,
        card_id: Optional[int],
        card_name: Optional[str],
        series: Optional[str],
        rarity: Optional[str],
        pool_type: Optional[str],
    ) -> CollectionFilters:
        filters = CollectionFilters()
        final_card_id = card_id
        if card_name and not final_card_id:
            name_filter = CollectionFilters(card_name=card_name)
            found_ids = await collection_service.get_filtered_card_ids(
                user_id, name_filter
            )
            if not found_ids:
                raise BusinessError(f"找不到名稱包含「{card_name}」的卡片。")
            final_card_id = found_ids[0]

        if final_card_id:
            filters.set_card_id(final_card_id)
        if series:
            filters.series = series
        if pool_type and pool_type.lower() != "all":
            filters.pool_type = pool_type
        if rarity:
            from config.app_config import get_rarity_display_codes

            rarity_map = {v: k for k, v in get_rarity_display_codes().items()}
            rarity_val = rarity_map.get(rarity.upper())
            if rarity_val:
                filters.rarity_in = [rarity_val]
            else:
                raise BusinessError(f"無法識別的稀有度代碼: {rarity}")
        return filters

    @app_commands.command(name="sw", description="賣出卡片獲取油幣 (統一接口)")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        card_id="【精確指定】通過卡片的數字ID來指定要賣的單張卡片",
        card_name="【模糊搜尋】通過卡片名稱來搜尋要賣的單張卡片",
        series="【篩選條件】只賣出屬於這個系列的所有卡片",
        rarity="【篩選條件】只賣出指定稀有度的所有卡片",
        pool_type="【篩選條件】只賣出指定卡池的所有卡片",
        operation="操作類型：'one'(賣1張)、'leave_one'(賣到剩1張)、'all'(全部賣出)",
        include_favorites="是否包含最愛卡片一起賣出（默認為否）",
    )
    @app_commands.choices(
        rarity=[
            app_commands.Choice(name="普通 (C)", value="C"),
            app_commands.Choice(name="稀有 (R)", value="R"),
            app_commands.Choice(name="超稀有 (SR)", value="SR"),
            app_commands.Choice(name="特殊稀有 (SSR)", value="SSR"),
            app_commands.Choice(name="極稀有 (UR)", value="UR"),
            app_commands.Choice(name="傳說稀有 (LR)", value="LR"),
            app_commands.Choice(name="特殊限定 (EX)", value="EX"),
        ],
        operation=[
            app_commands.Choice(name="賣出1張 (one)", value="one"),
            app_commands.Choice(name="賣到剩1張 (leave_one)", value="leave_one"),
            app_commands.Choice(name="全部賣出 (all)", value="all"),
        ],
        pool_type=get_pool_type_choices(show_individual_pools_only=True),
    )
    async def sell_card(
        self,
        interaction: discord.Interaction,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        series: Optional[str] = None,
        rarity: Optional[str] = None,
        pool_type: Optional[str] = None,
        operation: str = "one",
        include_favorites: bool = False,
    ):
        await interaction.response.defer(ephemeral=False, thinking=True)
        user_id = interaction.user.id

        filters = await self._build_sell_filters(
            user_id, card_id, card_name, series, rarity, pool_type
        )
        operation_type = str(operation).upper()

        if not filters.has_any_filter() and operation_type == "ONE":
            await self._send_sell_help_embed(interaction)
            return

        sell_plan = await economy_service.prepare_sell_preview(
            user_id=user_id,
            filters=filters,
            operation_type=operation_type,  # type: ignore
            force_sell=include_favorites,
        )

        confirmation_embed = self._create_sell_embed(
            interaction, sell_plan, "confirmation"
        )

        async def on_confirm_callback(confirm_interaction: discord.Interaction):
            # 確認回調中不需要 defer，因為視圖會處理
            sell_result = await economy_service.sell_cards_universal(
                user_id=user_id,
                filters=filters,
                operation_type=operation_type,  # type: ignore
                force_sell=include_favorites,
            )
            success_embed = self._create_sell_embed(
                confirm_interaction, sell_result, "success"
            )
            await confirm_interaction.response.edit_message(
                embed=success_embed, view=None
            )

        async def on_cancel_callback(cancel_interaction: discord.Interaction):
            # 取消回調中不需要 defer
            cancel_embed = discord.Embed(
                title="操作已取消",
                description="您已取消賣出操作，沒有任何卡片被賣出。",
                color=discord.Color.blue(),
            )
            await cancel_interaction.response.edit_message(
                embed=cancel_embed, view=None
            )

        confirmation_view = ConfirmationView(
            bot=self.bot,
            user_id=user_id,
            on_confirm=on_confirm_callback,
            on_cancel=on_cancel_callback,
            confirm_label="確認賣出",
            cancel_label="取消",
            confirm_style=discord.ButtonStyle.danger,
        )

        await interaction.followup.send(
            embed=confirmation_embed, view=confirmation_view, ephemeral=False
        )

    async def _send_sell_help_embed(self, interaction: discord.Interaction):
        help_embed = discord.Embed(
            title="💡 卡片賣出使用指南",
            description="歡迎使用卡片賣出功能！以下是詳細的使用方法：",
            color=discord.Color.blue(),
        )
        help_embed.add_field(
            name="🎯 賣出單張卡片",
            value='**方法1：** 指定卡片ID\n`/sw card_id=123`\n\n**方法2：** 指定卡片名稱\n`/sw card_name="某卡片"`\n\n💡 **小提示：** 卡片名稱支持模糊搜尋',
            inline=False,
        )
        help_embed.add_field(
            name="📦 批量賣出操作",
            value="**賣出所有：** `/sw operation=all`\n• 賣出符合條件的所有卡片\n\n**保留一張：** `/sw operation=leave_one`\n• 每種卡片保留1張，其餘全部賣出\n\n**賣出一張：** `/sw operation=one`\n• 每種卡片只賣出1張",
            inline=False,
        )
        help_embed.add_field(
            name="🔍 篩選條件組合",
            value='**按系列：** `series="某系列"`\n**按稀有度：** `rarity=C` (C/R/SR/SSR/UR/LR/EX)\n**按卡池：** `pool_type=main` (main/special)\n\n**組合範例：**\n`/sw operation=all series="某系列" rarity=C`\n賣出某系列的所有C卡',
            inline=False,
        )
        help_embed.add_field(
            name="❤️ 收藏卡片處理",
            value="**預設：** 不會賣出收藏卡片\n**強制賣出：** 加上 `include_favorites=True`",
            inline=False,
        )
        help_embed.set_footer(
            text="💰 所有賣出操作都會顯示預覽並需要確認，請放心使用！"
        )
        await interaction.followup.send(embed=help_embed, ephemeral=True)

    @sell_card.autocomplete("series")
    async def series_autocomplete(
        self, interaction: discord.Interaction, current: str
    ) -> List[app_commands.Choice[str]]:
        try:
            all_series = await collection_service.get_all_series()
            filtered_series = [
                series for series in all_series if current.lower() in series.lower()
            ][:25]
            return [
                app_commands.Choice(name=series, value=series)
                for series in filtered_series
            ]
        except Exception:
            return []


async def setup(bot: BotType):
    """載入 EconomyCog"""
    await bot.add_cog(EconomyCog(bot))
    logger.info("EconomyCog has been added to the bot.")
