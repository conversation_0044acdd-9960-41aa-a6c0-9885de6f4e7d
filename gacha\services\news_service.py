from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, Optional

import asyncpg

from database.postgresql.async_manager import get_pool
from gacha.constants import NewsFilterType, NewsTypeFilterEnum
from gacha.models.market_models import StockLifecycleStatus
from gacha.services import stock_trading_service
from utils.logger import logger

# ==================== Data Models ====================


@dataclass
class NewsFilterOptions:
    """新聞過濾選項"""

    filter_type: NewsFilterType
    news_type_filter: NewsTypeFilterEnum
    specific_stock_symbol: Optional[str] = None


@dataclass
class NewsItemData:
    """單條新聞數據"""

    id: int
    headline: str
    content: str
    published_at: datetime
    sentiment: Optional[float]
    source: Optional[str]
    news_type: Optional[str]
    character_archetype: Optional[str]
    character_name: Optional[str]
    impacts_price: bool
    asset_symbol: Optional[str]
    asset_name: Optional[str]
    asset_lifecycle_status: Optional[StockLifecycleStatus]

    @classmethod
    def from_record(cls, record: asyncpg.Record) -> "NewsItemData":
        """從數據庫記錄創建實例"""
        return cls(**dict(record))


@dataclass
class NewsPageData:
    """新聞分頁數據"""

    news_items: List[NewsItemData]
    current_page: int
    total_items: int
    filters_applied: NewsFilterOptions


# ==================== Logic Functions ====================


async def _build_news_query(
    user_id: int, filters: NewsFilterOptions
) -> tuple[str, List[Any], str, List[Any]]:
    """
    構建新聞查詢的 SELECT 和 COUNT 語句
    返回: (select_query_template, select_params_template, count_query_template, count_params)
    """
    select_base = """
        SELECT
            mn.id, mn.headline, mn.content, mn.published_at, mn.sentiment, mn.source,
            mn.news_type, mn.character_archetype, mn.character_name, mn.impacts_price,
            va.asset_symbol, va.asset_name, va.lifecycle_status AS asset_lifecycle_status
        FROM market_news mn
        LEFT JOIN virtual_assets va ON mn.affected_asset_id = va.asset_id
    """
    count_base = "SELECT COUNT(mn.id) FROM market_news mn LEFT JOIN virtual_assets va ON mn.affected_asset_id = va.asset_id"

    where_conditions: List[str] = []
    query_params: List[Any] = []
    param_idx = 1

    # 新聞類型過濾
    if filters.news_type_filter and filters.news_type_filter.value != "all_types":
        where_conditions.append(f"mn.news_type = ${param_idx}")
        query_params.append(filters.news_type_filter.value)
        param_idx += 1

    # 指定股票過濾
    if filters.specific_stock_symbol:
        where_conditions.append(f"va.asset_symbol = ${param_idx}")
        query_params.append(filters.specific_stock_symbol.upper())
        param_idx += 1

    # 範圍過濾
    if filters.filter_type:
        if filters.filter_type.value == "portfolio":
            user_portfolio_asset_ids = (
                await stock_trading_service.get_user_portfolio_assets(user_id)
            )
            if user_portfolio_asset_ids:
                where_conditions.append(
                    f"mn.affected_asset_id = ANY(${param_idx}::INT[])"
                )
                query_params.append(user_portfolio_asset_ids)
                param_idx += 1
            else:
                where_conditions.append("1 = 0")  # 沒有持倉時返回空結果
        elif filters.filter_type.value == "short_positions":
            user_short_position_asset_ids = (
                await stock_trading_service.get_user_short_position_assets(user_id)
            )
            if user_short_position_asset_ids:
                where_conditions.append(
                    f"mn.affected_asset_id = ANY(${param_idx}::INT[])"
                )
                query_params.append(user_short_position_asset_ids)
                param_idx += 1
            else:
                where_conditions.append("1 = 0")  # 沒有做空倉位時返回空結果
        elif filters.filter_type.value == "global":
            where_conditions.append("(mn.affected_asset_id IS NULL)")

    query_where_clause = ""
    if where_conditions:
        query_where_clause = "WHERE " + " AND ".join(where_conditions)

    select_query_template = (
        f"{select_base} {query_where_clause} ORDER BY mn.published_at DESC"
    )
    count_query_full = f"{count_base} {query_where_clause}"

    return (
        select_query_template,
        list(query_params),
        count_query_full,
        list(query_params),
    )


async def get_news_page_data(
    user_id: int,
    filters: NewsFilterOptions,
    current_page_idx: int,
    items_per_page: int = 1,
) -> NewsPageData:
    """獲取新聞分頁數據，items_per_page=1 用於單條新聞視圖"""
    logger.info(
        "Fetching news page for user %s, filters: %s, page_idx: %s, items_per_page: %s",
        user_id,
        filters,
        current_page_idx,
        items_per_page,
    )

    try:
        (
            select_template,
            select_params_template,
            count_query,
            count_params,
        ) = await _build_news_query(user_id, filters)
        news_items_dto: List[NewsItemData] = []
        total_items = 0

        pool = get_pool()
        if pool is None:
            raise RuntimeError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            # 獲取總數
            total_items_record = await conn.fetchval(count_query, *count_params)
            total_items = total_items_record if total_items_record is not None else 0

            # 獲取新聞數據
            if total_items > 0:
                limit_offset_params = [
                    items_per_page,
                    current_page_idx * items_per_page,
                ]
                final_select_query = f"{select_template} LIMIT ${len(select_params_template) + 1} OFFSET ${len(select_params_template) + 2}"
                final_select_params = select_params_template + limit_offset_params
                raw_news_records = await conn.fetch(
                    final_select_query, *final_select_params
                )

                for rec in raw_news_records:
                    news_items_dto.append(NewsItemData.from_record(rec))

        return NewsPageData(
            news_items=news_items_dto,
            current_page=current_page_idx + 1,
            total_items=total_items,
            filters_applied=filters,
        )

    except Exception as e:
        logger.error("Error in get_news_page_data: %s", e, exc_info=True)
        raise RuntimeError(f"獲取新聞數據時發生錯誤: {str(e)}") from e
