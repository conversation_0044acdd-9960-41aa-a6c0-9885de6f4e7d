"""
RPG卡牌系統的嵌入構建器
用於創建卡牌相關的Discord嵌入消息
"""

from typing import Any, Dict, List, Optional

import discord

from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class CardEmbedBuilder:
    """卡牌嵌入構建器"""

    # 稀有度顏色映射
    RARITY_COLORS = {
        "N": discord.Color.light_grey(),
        "R": discord.Color.green(),
        "SR": discord.Color.blue(),
        "SSR": discord.Color.purple(),
        "UR": discord.Color.gold(),
    }

    # 稀有度圖標映射
    RARITY_ICONS = {"N": "⚪", "R": "🟢", "SR": "🔵", "SSR": "🟣", "UR": "🟡"}

    @staticmethod
    def create_card_details_embed(card_details: Dict[str, Any]) -> discord.Embed:
        """
        創建卡牌詳細信息嵌入

        Args:
            card_details: 卡牌詳細信息

        Returns:
            Discord嵌入對象
        """
        try:
            card_name = card_details.get("name", "Unknown Card")
            rarity = card_details.get("rarity", "N")
            rarity_icon = CardEmbedBuilder.RARITY_ICONS.get(rarity, "❓")

            embed = discord.Embed(
                title=f"{rarity_icon} {card_name}",
                color=CardEmbedBuilder.RARITY_COLORS.get(
                    rarity, discord.Color.default()
                ),
                timestamp=discord.utils.utcnow(),
            )

            # 基本信息
            rpg_level = card_details.get("rpg_level", 1)
            star_level = card_details.get("star_level", 0)
            collection_id = card_details.get("collection_id", "Unknown")
            is_favorite = card_details.get("is_favorite", False)

            basic_info = f"稀有度：{rarity_icon} {rarity}\n"
            basic_info += f"RPG等級：{rpg_level:,}\n"
            basic_info += f"星級：{'⭐' * min(star_level, 10)}{f' (+{star_level - 10:,})' if star_level > 10 else ''}\n"
            basic_info += f"收藏ID：{collection_id}\n"
            basic_info += f"最愛：{'💖' if is_favorite else '🤍'}"

            embed.add_field(name="📊 基本信息", value=basic_info, inline=True)

            # 基礎屬性
            base_stats = card_details.get("base_stats", {})
            if base_stats:
                stats_text = CardEmbedBuilder._format_stats(base_stats)
                embed.add_field(name="⚔️ 基礎屬性", value=stats_text, inline=True)

            # 成長屬性
            growth_stats = card_details.get("growth_per_rpg_level", {})
            if growth_stats:
                growth_text = CardEmbedBuilder._format_stats(
                    growth_stats, prefix="每級+"
                )
                embed.add_field(name="📈 成長屬性", value=growth_text, inline=True)

            # 主動技能
            active_skills = card_details.get("equipped_active_skill_ids", [])
            if active_skills:
                skills_text = ""
                for i, skill_id in enumerate(active_skills):
                    if skill_id:
                        skills_text += f"槽位 {i + 1}：{skill_id}\n"
                    else:
                        skills_text += f"槽位 {i + 1}：空\n"

                embed.add_field(
                    name="🎯 主動技能", value=skills_text.strip(), inline=False
                )

            # 被動技能
            passive_skills = card_details.get("equipped_common_passives", {})
            if passive_skills:
                passives_text = ""
                for slot_key, passive_data in passive_skills.items():
                    if isinstance(passive_data, dict):
                        skill_id = passive_data.get("skill_id", "空")
                        level = passive_data.get("level", 1)
                        passives_text += f"{slot_key}：{skill_id} (Lv.{level})\n"
                    else:
                        passives_text += f"{slot_key}：空\n"

                embed.add_field(
                    name="🛡️ 被動技能", value=passives_text.strip(), inline=False
                )

            # 天賦被動技能
            innate_passive = card_details.get("innate_passive_skill_id")
            if innate_passive:
                embed.add_field(
                    name="✨ 天賦被動",
                    value=f"{innate_passive} (星級 {star_level})",
                    inline=False,
                )

            # 經驗信息
            rpg_xp = card_details.get("rpg_xp", 0)
            if rpg_xp > 0:
                embed.add_field(
                    name="💫 經驗值", value=f"當前經驗：{rpg_xp}", inline=True
                )

            return embed

        except Exception as e:
            logger.error("創建卡牌詳細信息嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 卡牌信息",
                description="無法顯示卡牌詳細信息。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def create_card_list_embed(
        cards: List[Dict[str, Any]],
        page: int = 1,
        total_pages: int = 1,
        title: str = "卡牌列表",
    ) -> discord.Embed:
        """
        創建卡牌列表嵌入

        Args:
            cards: 卡牌列表
            page: 當前頁數
            total_pages: 總頁數
            title: 標題

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title=f"🃏 {title}",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow(),
            )

            if not cards:
                embed.description = "沒有找到任何卡牌。"
                return embed

            # 卡牌列表
            card_lines = []
            for card in cards:
                rarity = card.get("rarity", "N")
                rarity_icon = CardEmbedBuilder.RARITY_ICONS.get(rarity, "❓")
                name = card.get("name", "Unknown")
                rpg_level = card.get("rpg_level", 1)
                star_level = card.get("star_level", 0)
                collection_id = card.get("collection_id", "Unknown")
                is_favorite = card.get("is_favorite", False)

                favorite_icon = "💖" if is_favorite else ""
                star_display = f"⭐{star_level}" if star_level > 0 else ""

                card_line = f"{rarity_icon} **{name}** {favorite_icon}"
                card_line += f"\n└ ID:{collection_id} | Lv.{rpg_level} {star_display}"

                card_lines.append(card_line)

            embed.description = "\n\n".join(card_lines)

            # 分頁信息
            if total_pages > 1:
                embed.set_footer(
                    text=f"第 {page}/{total_pages} 頁 | 共 {len(cards)} 張卡牌"
                )
            else:
                embed.set_footer(text=f"共 {len(cards)} 張卡牌")

            return embed

        except Exception as e:
            logger.error("創建卡牌列表嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 卡牌列表",
                description="無法顯示卡牌列表。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def create_skill_operation_result_embed(
        operation: str,
        success: bool,
        message: str,
        card_name: Optional[str] = None,
        skill_id: Optional[str] = None,
    ) -> discord.Embed:
        """
        創建技能操作結果嵌入

        Args:
            operation: 操作類型（如 "裝備", "卸下"）
            success: 是否成功
            message: 結果消息
            card_name: 卡牌名稱
            skill_id: 技能ID

        Returns:
            Discord嵌入對象
        """
        try:
            if success:
                embed = SuccessEmbed(
                    title=f"✅ 技能{operation}成功",
                    description=message,
                    timestamp=discord.utils.utcnow(),
                )
            else:
                title = f"❌ 技能{operation}失敗"
                color = discord.Color.red()
                embed = discord.Embed(
                    title=title,
                    description=message,
                    color=color,
                    timestamp=discord.utils.utcnow(),
                )

            if card_name:
                embed.add_field(name="🃏 卡牌", value=card_name, inline=True)

            if skill_id:
                embed.add_field(name="🎯 技能", value=skill_id, inline=True)

            return embed

        except Exception as e:
            logger.error("創建技能操作結果嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 操作結果",
                description="無法顯示操作結果。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def create_skill_proficiency_embed(
        learned_skills: List[Dict[str, Any]],
    ) -> discord.Embed:
        """
        創建技能熟練度嵌入

        Args:
            learned_skills: 已學習的技能列表

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title="🎓 技能熟練度",
                color=discord.Color.purple(),
                timestamp=discord.utils.utcnow(),
            )

            if not learned_skills:
                embed.description = "您還沒有學習任何技能。"
                return embed

            # 分類技能
            active_skills = [
                s for s in learned_skills if s.get("skill_type") == "ACTIVE"
            ]
            passive_skills = [
                s for s in learned_skills if s.get("skill_type") == "PASSIVE"
            ]

            # 主動技能
            if active_skills:
                active_text = ""
                for skill in active_skills[:10]:  # 限制顯示數量
                    skill_id = skill.get("skill_id", "Unknown")
                    level = skill.get("skill_level", 1)
                    xp = skill.get("skill_xp", 0)
                    active_text += f"• {skill_id} (Lv.{level})\n"
                    if xp > 0:
                        active_text += f"  經驗：{xp}\n"

                embed.add_field(
                    name="⚔️ 主動技能", value=active_text.strip(), inline=True
                )

            # 被動技能
            if passive_skills:
                passive_text = ""
                for skill in passive_skills[:10]:  # 限制顯示數量
                    skill_id = skill.get("skill_id", "Unknown")
                    level = skill.get("skill_level", 1)
                    xp = skill.get("skill_xp", 0)
                    passive_text += f"• {skill_id} (Lv.{level})\n"
                    if xp > 0:
                        passive_text += f"  經驗：{xp}\n"

                embed.add_field(
                    name="🛡️ 被動技能", value=passive_text.strip(), inline=True
                )

            # 統計信息
            total_skills = len(learned_skills)
            total_levels = sum(skill.get("skill_level", 1) for skill in learned_skills)

            embed.add_field(
                name="📊 統計",
                value=f"技能總數：{total_skills}\n總等級：{total_levels}",
                inline=False,
            )

            embed.set_footer(text=f"共學習了 {total_skills} 個技能")

            return embed

        except Exception as e:
            logger.error("創建技能熟練度嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 技能熟練度",
                description="無法顯示技能熟練度信息。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def _format_stats(stats: Dict[str, Any], prefix: str = "") -> str:
        """
        格式化屬性信息

        Args:
            stats: 屬性字典
            prefix: 前綴文本

        Returns:
            格式化的屬性文本
        """
        try:
            if not stats:
                return "無"

            # 屬性名稱映射
            stat_names = {
                "hp": "生命值",
                "mp": "魔力值",
                "patk": "物理攻擊",
                "matk": "魔法攻擊",
                "pdef": "物理防禦",
                "mdef": "魔法防禦",
                "spd": "速度",
                "crit_rate": "暴擊率",
                "crit_dmg": "暴擊傷害",
                "accuracy": "命中率",
                "evasion": "閃避率",
            }

            stat_lines = []
            for stat_key, stat_value in stats.items():
                stat_name = stat_names.get(stat_key, stat_key)

                # 格式化數值
                if isinstance(stat_value, float):
                    if stat_key in ["crit_rate", "crit_dmg", "accuracy", "evasion"]:
                        # 百分比屬性
                        formatted_value = f"{stat_value:.1%}"
                    else:
                        formatted_value = f"{stat_value:.1f}"
                else:
                    formatted_value = str(stat_value)

                stat_lines.append(f"{stat_name}：{prefix}{formatted_value}")

            return "\n".join(stat_lines)

        except Exception as e:
            logger.error("格式化屬性信息時發生錯誤: %s", e, exc_info=True)
            return "屬性信息錯誤"
