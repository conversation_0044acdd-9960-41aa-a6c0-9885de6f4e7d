"""
爬塔遊戲 Cog - 單檔案完整實現
高風險高收益的爬塔遊戲，玩家需要從下往上爬塔，避開TNT炸彈和各種陷阱，收集金幣獎勵
"""

import random
from typing import Any, Dict, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.economy_service as economy_service
from gacha.exceptions import (
    InsufficientBalanceError,
    InvalidBetAmountError,
    InvalidOperationError,
)
from gacha.services import game_stats_service
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger


# 爬塔遊戲配置類
class TowerConfig:
    """爬塔遊戲配置"""

    @staticmethod
    def get_emojis():
        """獲取遊戲表情符號配置"""
        from config.app_config import get_oil_emoji

        return {
            "padlock": "<:padlock:1382181513243791470>",
            "tnt": "<:tnt:1382189080959258756>",
            "explosion": "💥",
            "oil": get_oil_emoji(),
        }

    # 金幣配置 (類型: (彩色emoji, 灰色emoji, 倍率))
    GOLD_CONFIG = {
        2: (
            "<:gold2:1382181648501706752>",
            "<:gold2G:1382182883711516823>",
            1.0,
        ),  # 保持 1.0
        3: (
            "<:gold3:1382181574178504786>",
            "<:gold3G:1382182889290076251>",
            1.0,
        ),  # 保持 1.0
        4: (
            "<:gold4:1382181729959149689>",
            "<:gold4G:1382182872764514354>",
            1.1,
        ),  # 保持 1.1
        5: (
            "<:gold5:1382181736754053151>",
            "<:gold5G:1382182878527361044>",
            1.2,
        ),  # 保持 1.2
    }

    @classmethod
    def get_gold_emoji(cls, gold_type: int, is_colored: bool = True) -> str:
        """獲取金幣emoji"""
        colored, gray, _ = cls.GOLD_CONFIG[gold_type]
        return colored if is_colored else gray

    @classmethod
    def get_gold_multiplier(cls, gold_type: int) -> float:
        """獲取金幣倍率"""
        return cls.GOLD_CONFIG[gold_type][2]

    @classmethod
    def get_tile_emoji(
        cls,
        tile_type: str,
        is_revealed: bool = True,
        is_explosion: bool = False,
        level_completed: bool = True,
    ) -> str:
        """獲取格子emoji"""
        emojis = cls.get_emojis()
        if not is_revealed:
            return emojis["padlock"]

        if tile_type.startswith("gold"):
            gold_num = int(tile_type[4:])
            return cls.get_gold_emoji(gold_num, level_completed)
        elif tile_type == "tnt":
            return emojis["explosion"] if is_explosion else emojis["tnt"]
        else:
            return emojis.get(tile_type, emojis["padlock"])


# 遊戲常數
TOWER_LEVELS = 8  # 塔層數
TILES_PER_LEVEL = 4  # 每層格子數
TOTAL_TILES = TOWER_LEVELS * TILES_PER_LEVEL  # 32格

MIN_BET = 100
MAX_BET = 250000
HOUSE_EDGE = 0.07  # 7% 莊家優勢，與mines遊戲保持一致
# 注意：PAYOUT_FACTOR已內置於LEVEL_MULTIPLIERS中，無需額外計算

# 考慮金幣倍率後的正確賠率表（目標 95% RTP）
# 基礎倍率 = 0.95 / (success_rate ^ level) / 1.075 (金幣平均倍率)
EFFECTIVE_LEVEL_MULTIPLIERS = {
    "easy": {  # 每層 75% 成功率
        1: 1.18,
        2: 1.57,
        3: 2.09,
        4: 2.79,
        5: 3.72,
        6: 4.97,
        7: 6.62,
        8: 8.83,
    },
    "medium": {  # 每層 50% 成功率
        1: 1.77,
        2: 3.53,
        3: 7.07,
        4: 14.14,
        5: 28.28,
        6: 56.56,
        7: 113.12,
        8: 226.23,
    },
    "hard": {  # 每層 25% 成功率
        1: 3.53,
        2: 14.14,
        3: 56.56,
        4: 226.23,
        5: 904.93,
        6: 3619.72,
        7: 14478.88,
        8: 57915.53,
    },
}

# 難度模式配置 - 純機率遊戲（只有TNT和金幣）
DIFFICULTY_MODES = {
    "easy": {
        "name": "🟢 簡單",
        "tnt_per_level": 1,  # 每層1個TNT，3個金幣（75%成功率）
    },
    "medium": {
        "name": "🟡 中等",
        "tnt_per_level": 2,  # 每層2個TNT，2個金幣（50%成功率）
    },
    "hard": {
        "name": "🔴 困難",
        "tnt_per_level": 3,  # 每層3個TNT，1個金幣（25%成功率）
    },
}

# 遊戲圖片
GAME_IMAGE_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1382580424370556988/control-tower.png?ex=684babdc&is=684a5a5c&hm=4ec574d41d152a378612b0a22ffdc790e1fba98c9c9c715375338e1cc08d4f01&"


class TowerGameState:
    """爬塔遊戲狀態管理"""

    def __init__(
        self, user_id: int, bet_amount: int, difficulty: str, initial_balance: int = 0
    ):
        self.user_id = user_id
        self.bet_amount = bet_amount
        self.difficulty = difficulty
        self.current_level = 0  # 當前層級 (0-7)
        self.game_over = False
        self.hit_tnt = False
        self.total_reward = 0  # 累積獎勵
        self.revealed_tiles = []  # 已揭示的格子位置 [(level, tile_index), ...]
        self.tower_layout = {}  # 塔的佈局 {(level, tile_index): tile_type}
        self.can_cashout = False  # 是否可以提現
        self.cached_balance = (
            initial_balance - bet_amount
        )  # 緩存的餘額（避免頻繁查詢數據庫）
        self.initial_stats: Optional[Dict[str, Any]] = (
            None  # 緩存的統計數據（避免頻繁查詢）
        )
        self.is_processing = False  # 防止重複點擊的鎖

        # 生成塔佈局
        self._generate_tower_layout()

    def _generate_tower_layout(self):
        """生成隨機的塔佈局 - 純機率遊戲（只有TNT和金幣）"""
        mode_config = DIFFICULTY_MODES[self.difficulty]
        tnt_per_level = mode_config["tnt_per_level"]

        # 生成每層的佈局（只有TNT + 金幣）
        for level in range(TOWER_LEVELS):
            level_tiles = []

            # 填充TNT格子
            level_tiles.extend(["tnt"] * int(tnt_per_level))

            # 剩餘格子都是金幣
            remaining_slots = TILES_PER_LEVEL - int(tnt_per_level)
            for _ in range(remaining_slots):
                gold_type = random.choice([2, 3, 4, 5])
                level_tiles.append(f"gold{gold_type}")

            # 隨機打亂這一層的格子順序
            random.shuffle(level_tiles)

            # 分配到具體位置
            for tile_index, tile_type in enumerate(level_tiles):
                self.tower_layout[(level, tile_index)] = tile_type

    def get_tile_type(self, level: int, tile_index: int) -> str:
        """獲取指定位置的格子類型"""
        return self.tower_layout.get((level, tile_index), "empty")

    def reveal_tile(self, level: int, tile_index: int) -> str:
        """揭示格子並返回類型"""
        position = (level, tile_index)
        if position not in self.revealed_tiles:
            self.revealed_tiles.append(position)
        return self.get_tile_type(level, tile_index)

    def calculate_level_reward(self, level: int, gold_type: int) -> int:
        """計算基於層數的獎勵"""
        # 根據難度獲取對應的層級倍率
        difficulty_multipliers = EFFECTIVE_LEVEL_MULTIPLIERS[self.difficulty]

        # 確保層數在範圍內（層數從1開始計算）
        reward_level = level + 1
        if reward_level < 1 or reward_level > 8:
            return 0

        level_multiplier = difficulty_multipliers[reward_level]
        gold_multiplier = TowerConfig.get_gold_multiplier(gold_type)
        return int(self.bet_amount * level_multiplier * gold_multiplier)

    def complete_level(self):
        """完成當前層級"""
        self.current_level += 1
        self.can_cashout = True

        # 檢查是否完成整個塔
        if self.current_level >= TOWER_LEVELS:
            self.game_over = True

    def end_game(self, hit_tnt: bool = False):
        """結束遊戲"""
        self.game_over = True
        self.hit_tnt = hit_tnt
        if hit_tnt:
            self.total_reward = 0  # 踩到TNT失去所有獎勵


class TowerGameActiveView(BaseView):
    """爬塔遊戲進行中UI視圖"""

    def __init__(self, game_state: TowerGameState, cog: "TowerCog"):
        super().__init__(bot=cog.bot, user_id=game_state.user_id, timeout=300)
        self.game_state = game_state
        self.cog = cog
        self.message: Optional[discord.Message] = None

        # 如果不能提現，就移除提現按鈕
        if not self.game_state.can_cashout:
            self.remove_item(self.cashout_button)
        else:
            # 否則更新標籤
            self.cashout_button.label = f"提現 ({self.game_state.total_reward:,})"

    async def on_timeout(self):
        """處理視圖超時"""
        if (
            not self.game_state.game_over
            and self.game_state.user_id in self.cog.active_games
        ):
            logger.info("Tower game for user %s timed out.", self.game_state.user_id)
            del self.cog.active_games[self.game_state.user_id]
            if self.message:
                try:
                    timeout_embed = (
                        self.message.embeds[0]
                        if self.message.embeds
                        else discord.Embed()
                    )
                    if timeout_embed.description:
                        timeout_embed.description += "\n\n**⌛ 遊戲已超時並自動結束。**"
                    else:
                        timeout_embed.description = "\n\n**⌛ 遊戲已超時並自動結束。**"
                    timeout_embed.color = discord.Color.dark_grey()
                    await self.message.edit(embed=timeout_embed, view=None)
                except discord.NotFound:
                    # 根據規範，NotFound 錯誤可以被靜默處理
                    pass

    async def _handle_tile_click(
        self, interaction: discord.Interaction, tile_index: int
    ):
        """統一處理格子點擊"""
        await interaction.response.defer()
        await self.cog._handle_tile_selection(interaction, self.game_state, tile_index)

    @discord.ui.button(
        emoji="1️⃣", style=discord.ButtonStyle.secondary, custom_id="tower_tile_0", row=0
    )
    async def tile_0_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._handle_tile_click(interaction, 0)

    @discord.ui.button(
        emoji="2️⃣", style=discord.ButtonStyle.secondary, custom_id="tower_tile_1", row=0
    )
    async def tile_1_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._handle_tile_click(interaction, 1)

    @discord.ui.button(
        emoji="3️⃣", style=discord.ButtonStyle.secondary, custom_id="tower_tile_2", row=0
    )
    async def tile_2_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._handle_tile_click(interaction, 2)

    @discord.ui.button(
        emoji="4️⃣", style=discord.ButtonStyle.secondary, custom_id="tower_tile_3", row=0
    )
    async def tile_3_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._handle_tile_click(interaction, 3)

    @discord.ui.button(
        label="提現",
        style=discord.ButtonStyle.success,
        emoji=TowerConfig.get_emojis()["oil"],
        custom_id="tower_cashout",
        row=1,
    )
    async def cashout_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await interaction.response.defer()
        await self.cog._handle_cashout(interaction, self.game_state)


class TowerGameOverView(BaseView):
    """爬塔遊戲結束UI視圖"""

    def __init__(self, game_state: TowerGameState, cog: "TowerCog"):
        super().__init__(bot=cog.bot, user_id=game_state.user_id, timeout=180)
        self.game_state = game_state
        self.cog = cog
        self.message: Optional[discord.Message] = None

    async def on_timeout(self):
        """超時後，視圖將自動停止監聽，無需額外操作以節省API"""
        pass

    @discord.ui.button(
        label="再來一局",
        style=discord.ButtonStyle.primary,
        emoji="🔄",
        custom_id="tower_play_again",
        row=0,
    )
    async def play_again_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await interaction.response.defer()
        await self.cog._start_tower_game(
            interaction, self.game_state.bet_amount, self.game_state.difficulty
        )

    @discord.ui.button(
        label="調整設定",
        style=discord.ButtonStyle.secondary,
        emoji="⚙️",
        custom_id="tower_settings",
        row=0,
    )
    async def settings_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await interaction.response.defer()
        await self.cog._show_difficulty_selection_new_message(interaction, None)


class TowerDifficultyView(BaseView):
    """爬塔遊戲難度選擇視圖"""

    def __init__(self, user_id: int, cog: "TowerCog", bet_amount: Optional[int] = None):
        super().__init__(bot=cog.bot, user_id=user_id, timeout=300)
        self.cog = cog
        self.bet_amount = bet_amount
        self.message: Optional[discord.Message] = None

    async def _on_difficulty_select(
        self, interaction: discord.Interaction, difficulty: str
    ):
        """處理難度選擇的通用邏輯"""
        await interaction.response.defer()
        bet_amount = self.bet_amount or MIN_BET
        await self.cog._start_tower_game(
            interaction, bet_amount, difficulty, edit_message=True
        )

    @discord.ui.button(
        label="簡單 (每層1個TNT)",
        style=discord.ButtonStyle.success,
        emoji="🟢",
        custom_id="tower_easy",
        row=0,
    )
    async def easy_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._on_difficulty_select(interaction, "easy")

    @discord.ui.button(
        label="中等 (每層2個TNT)",
        style=discord.ButtonStyle.primary,
        emoji="🟡",
        custom_id="tower_medium",
        row=0,
    )
    async def medium_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._on_difficulty_select(interaction, "medium")

    @discord.ui.button(
        label="困難 (每層3個TNT)",
        style=discord.ButtonStyle.danger,
        emoji="🔴",
        custom_id="tower_hard",
        row=0,
    )
    async def hard_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self._on_difficulty_select(interaction, "hard")

    @discord.ui.button(
        label="自訂金額",
        style=discord.ButtonStyle.secondary,
        emoji="⚙️",
        custom_id="tower_custom_bet",
        row=1,
    )
    async def custom_bet_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await self.cog._show_custom_bet_modal(interaction)


class TowerCog(commands.Cog):
    """爬塔遊戲Cog - 單檔案完整實現"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.active_games: Dict[int, TowerGameState] = {}
        logger.info("TowerCog initialized.")

    async def _handle_gold_tile(
        self, game_state: TowerGameState, tile_type: str
    ) -> dict:
        """處理金幣格子，返回處理結果"""
        result = {
            "reward_change": 0,
            "level_progress": True,  # 金幣格子總是前進到下一層
            "message": "",
        }

        if tile_type.startswith("gold"):
            # 金幣格子 - 計算當前層的獎勵
            gold_num = int(tile_type[4:])

            # 基於當前層數計算新的總獎勵
            new_reward = game_state.calculate_level_reward(
                game_state.current_level, gold_num
            )

            previous_reward = game_state.total_reward
            game_state.total_reward = new_reward

            result["reward_change"] = new_reward - previous_reward
            result["message"] = f"獲得金幣！完成第{game_state.current_level + 1}層"

        return result

    def _create_difficulty_selection_embed(
        self, current_bet: int, current_balance: int
    ) -> discord.Embed:
        """創建難度選擇embed"""
        embed = discord.Embed(
            title="🏗️ 爬塔遊戲 - 難度選擇",
            description="""歡迎來到爬塔遊戲！選擇一個難度開始遊戲。

**遊戲規則：**
1. 從最底層開始，每層選擇4個格子中的一個向上爬
2. 選中金幣格就完成該層，獲得對應獎勵並前進到下一層
3. 完成整層後可以選擇提現或繼續挑戰
4. 踩到💥TNT遊戲結束，失去所有未提現的獎勵

**格子類型：**
• 💰 金幣格：完成該層，獲得對應獎勵，繼續向上爬
• 💥 TNT：遊戲結束，失去所有累積獎勵

**請選擇難度：**""",
            color=discord.Color.gold(),
        )

        # 添加難度說明 - 純機率遊戲（考慮金幣倍率的平衡賠率）
        difficulty_descriptions = {
            "easy": "每層1個TNT + 3個金幣 (75%成功率)\n💰 最高獎勵：8.83倍 (完成8層)",
            "medium": "每層2個TNT + 2個金幣 (50%成功率)\n💰 最高獎勵：226.23倍 (完成8層)",
            "hard": "每層3個TNT + 1個金幣 (25%成功率)\n💰 最高獎勵：57,915.53倍 (完成8層)",
        }

        for difficulty, config in DIFFICULTY_MODES.items():
            embed.add_field(
                name=f"{config['name']} 模式",
                value=difficulty_descriptions[difficulty],
                inline=True,
            )

        embed.add_field(
            name="💰 當前設定",
            value=f"下注金額: {current_bet:,} {TowerConfig.get_emojis()['oil']}\n"
            f"當前餘額: {current_balance:,} {TowerConfig.get_emojis()['oil']}",
            inline=False,
        )

        embed.set_thumbnail(url=GAME_IMAGE_URL)
        embed.set_footer(text="💡 提示：可以點擊「自訂金額」來調整下注金額")

        return embed

    async def _create_game_embed(
        self,
        game_state: TowerGameState,
        current_balance: int,
        user: Union[discord.User, discord.Member],
    ) -> discord.Embed:
        """創建遊戲進行中的embed"""
        if game_state.game_over:
            return await self._create_game_over_embed(game_state, current_balance, user)

        # 遊戲進行中
        author_title = (
            f"🏗️ 爬塔遊戲 - {DIFFICULTY_MODES[game_state.difficulty]['name']} 模式"
        )
        oil_emoji = TowerConfig.get_emojis()["oil"]

        if game_state.hit_tnt:
            status_text = "💥 **踩到TNT！遊戲結束！**"
            color = discord.Color.red()
        else:
            status_text = "🎯 **選擇一個格子繼續向上爬**"
            color = discord.Color.blue()

        # 簡化的遊戲信息，只保留當前層級和累積獎勵
        game_info = (
            f"當前層級: `{game_state.current_level + 1}/{TOWER_LEVELS}` | "
            f"累積獎勵: `{game_state.total_reward:,}` {oil_emoji}"
        )

        tower_display = self._generate_tower_display(game_state)

        full_description = (
            f"{status_text}\n\n{game_info}\n\n**🏗️ 塔的狀態:**\n{tower_display}"
        )

        embed = discord.Embed(description=full_description, color=color)
        embed.set_author(name=author_title)
        embed.set_thumbnail(url=GAME_IMAGE_URL)

        # 設置footer - 參考blackjack格式
        footer_parts = [f"下注: {game_state.bet_amount:,}"]
        footer_parts.append(f"餘額: {current_balance:,}")

        # 使用緩存的統計數據（避免頻繁查詢）
        if game_state.initial_stats:
            total_profit_loss = game_state.initial_stats.get("total_profit_loss", 0)
            footer_parts.append(f"總盈虧: {total_profit_loss:,}")

        footer_text = " | ".join(footer_parts)
        embed.set_footer(text=footer_text, icon_url=user.display_avatar.url)

        return embed

    async def _create_game_over_embed(
        self,
        game_state: TowerGameState,
        current_balance: int,
        user: Union[discord.User, discord.Member],
    ) -> discord.Embed:
        """創建遊戲結束embed"""
        author_title = (
            f"🏗️ 爬塔遊戲 - {DIFFICULTY_MODES[game_state.difficulty]['name']} 模式"
        )
        oil_emoji = TowerConfig.get_emojis()["oil"]

        if game_state.hit_tnt:
            status_text = "💥 **遊戲結束 - 踩到TNT！**\n很遺憾，你踩到了TNT炸彈！失去了所有未提現的獎勵。"
            color = discord.Color.red()
        elif game_state.current_level >= TOWER_LEVELS:
            status_text = "🎉 **恭喜！成功完成爬塔！**\n太棒了！你成功爬到了塔頂！獲得了豐厚的獎勵！"
            color = discord.Color.gold()
        else:
            status_text = "💰 **成功提現！**\n明智的選擇！你成功提現了獎勵！"
            color = discord.Color.green()

        # 簡化的遊戲結果信息
        net_profit = game_state.total_reward - game_state.bet_amount
        game_result = (
            f"📊 **遊戲結果:**\n"
            f"最終獎勵: `{game_state.total_reward:,}` {oil_emoji} | "
            f"淨收益: `{net_profit:+,}` {oil_emoji}\n"
            f"到達層級: `{game_state.current_level}/{TOWER_LEVELS}`"
        )

        # 構建完整的description，包含遊戲結果和塔佈局
        tower_display = self._generate_tower_display(game_state, show_all=True)
        full_description = (
            f"{status_text}\n\n{game_result}\n\n**🏗️ 完整塔佈局:**\n{tower_display}"
        )

        embed = discord.Embed(description=full_description, color=color)
        embed.set_author(name=author_title)
        embed.set_thumbnail(url=GAME_IMAGE_URL)

        # 設置footer - 參考blackjack格式
        footer_parts = [f"下注: {game_state.bet_amount:,}"]
        footer_parts.append(f"餘額: {current_balance:,}")

        # 遊戲結束時重新獲取統計數據以顯示最新的總盈虧
        stats = await self._get_user_stats(user.id)
        if stats:
            total_profit_loss = stats.get("total_profit_loss", 0)
            footer_parts.append(f"總盈虧: {total_profit_loss:,}")

        footer_text = " | ".join(footer_parts)
        embed.set_footer(text=footer_text, icon_url=user.display_avatar.url)

        return embed

    def _generate_tower_display(
        self, game_state: TowerGameState, show_all: bool = False
    ) -> str:
        """生成塔的顯示字符串"""

        def get_tile_emoji(level: int, tile_index: int) -> str:
            """獲取單個格子的emoji"""
            position = (level, tile_index)

            # 判斷是否應該顯示該格子的內容
            should_show_content = (
                show_all
                or level < game_state.current_level
                or position in game_state.revealed_tiles
            )

            emojis = TowerConfig.get_emojis()
            if not should_show_content:
                return emojis["padlock"]

            tile_type = game_state.get_tile_type(level, tile_index)

            # 處理金幣類型
            if tile_type.startswith("gold"):
                gold_num = int(tile_type[4:])
                is_colored = not (show_all and level > game_state.current_level)
                return TowerConfig.get_gold_emoji(gold_num, is_colored)

            # 處理TNT爆炸
            if (
                tile_type == "tnt"
                and position in game_state.revealed_tiles
                and game_state.hit_tnt
            ):
                return emojis["explosion"]

            # 其他類型直接映射
            return emojis.get(tile_type, emojis["padlock"])

        # 生成每層的emoji行（從頂層到底層）
        lines = [
            f"### {' '.join(get_tile_emoji(level, tile_index) for tile_index in range(TILES_PER_LEVEL))}"
            for level in range(TOWER_LEVELS - 1, -1, -1)
        ]

        return "\n".join(lines)

    async def _show_difficulty_selection_new_message(
        self, interaction: discord.Interaction, bet_amount: Optional[int] = None
    ):
        """顯示難度選擇界面 - 發送新訊息"""
        # 注意：調用此方法前應該已經 defer 過了

        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # UserNotFoundError 將由 on_error 處理
        # 獲取用戶餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        # 設定下注金額
        if bet_amount is None:
            bet_amount = MIN_BET

        # 檢查餘額
        if current_balance < bet_amount:
            raise InsufficientBalanceError(required=bet_amount, current=current_balance)

        # 創建embed和視圖
        embed = self._create_difficulty_selection_embed(bet_amount, current_balance)
        view = TowerDifficultyView(interaction.user.id, self, bet_amount)

        # 發送新訊息
        message = await interaction.followup.send(embed=embed, view=view)
        view.message = message

    async def _show_custom_bet_modal(self, interaction: discord.Interaction):
        """顯示自訂金額模態框"""

        class CustomBetModal(BaseModal):
            def __init__(self, cog: "TowerCog"):
                super().__init__(bot=cog.bot, title="自訂下注金額")
                self.cog = cog

            bet_amount = discord.ui.TextInput(
                label="下注金額",
                placeholder=f"請輸入 {MIN_BET:,} - {MAX_BET:,} 之間的金額",
                min_length=1,
                max_length=10,
            )

            async def on_submit(self, interaction: discord.Interaction):
                amount = int(self.bet_amount.value.replace(",", ""))

                if amount < MIN_BET or amount > MAX_BET:
                    raise InvalidBetAmountError(
                        f"下注金額必須在 {MIN_BET:,} - {MAX_BET:,} {TowerConfig.get_emojis()['oil']} 之間！"
                    )

                # 檢查餘額
                balance_info = await economy_service.get_balance(interaction.user.id)
                current_balance = balance_info.get("balance", 0)

                if current_balance < amount:
                    raise InsufficientBalanceError(
                        required=amount, current=current_balance
                    )

                # 顯示難度選擇
                await self.cog._show_difficulty_selection_edit_message(
                    interaction, amount
                )

        await interaction.response.send_modal(CustomBetModal(self))

    async def _show_difficulty_selection_edit_message(
        self, interaction: discord.Interaction, bet_amount: int
    ):
        """顯示難度選擇界面 - 編輯現有訊息"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # 獲取用戶餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        # 創建embed和視圖
        embed = self._create_difficulty_selection_embed(bet_amount, current_balance)
        view = TowerDifficultyView(interaction.user.id, self, bet_amount)

        # 編輯訊息
        await interaction.response.edit_message(embed=embed, view=view)
        view.message = interaction.message

    async def _start_tower_game(
        self,
        interaction: discord.Interaction,
        bet_amount: int,
        difficulty: str,
        edit_message: bool = False,
    ):
        """開始爬塔遊戲"""
        # 注意：調用此方法前應該已經 defer 過了（在按鈕回調或命令中）

        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # 檢查餘額並扣款
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        if current_balance < bet_amount:
            raise InsufficientBalanceError(required=bet_amount, current=current_balance)

        # 扣款
        await economy_service.award_oil(
            user_id=interaction.user.id,
            amount=-bet_amount,
            transaction_type="game:tower_bet",
            reason="Tower bet",
        )

        # 創建遊戲狀態（傳遞初始餘額以避免後續查詢）
        game_state = TowerGameState(
            interaction.user.id, bet_amount, difficulty, current_balance
        )

        # 緩存統計數據（避免遊戲過程中頻繁查詢）
        game_state.initial_stats = await self._get_user_stats(interaction.user.id)

        self.active_games[interaction.user.id] = game_state

        # 使用緩存的餘額
        updated_balance = game_state.cached_balance

        # 創建遊戲embed和視圖
        embed = await self._create_game_embed(
            game_state, updated_balance, interaction.user
        )
        view = TowerGameActiveView(game_state, self)

        # 根據參數決定是編輯訊息還是發送新訊息
        # discord.HTTPException 會被全域處理器捕捉並靜默記錄
        if edit_message:
            await interaction.edit_original_response(embed=embed, view=view)
            view.message = interaction.message
        else:
            message = await interaction.followup.send(embed=embed, view=view)
            view.message = message

        logger.info(
            "用戶 %s 開始爬塔遊戲: %s 模式, 下注 %s",
            interaction.user.id,
            difficulty,
            bet_amount,
        )

    async def _update_view(
        self,
        interaction: discord.Interaction,
        game_state: TowerGameState,
        current_balance: int,
    ):
        embed = await self._create_game_embed(
            game_state, current_balance, interaction.user
        )
        view = (
            TowerGameOverView(game_state, self)
            if game_state.game_over
            else TowerGameActiveView(game_state, self)
        )
        await interaction.edit_original_response(embed=embed, view=view)
        view.message = interaction.message

    async def _handle_tile_selection(
        self,
        interaction: discord.Interaction,
        game_state: TowerGameState,
        tile_index: int,
    ):
        if game_state.is_processing:
            return
        game_state.is_processing = True
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        position = (game_state.current_level, tile_index)
        if position in game_state.revealed_tiles:
            await self._update_view(interaction, game_state, game_state.cached_balance)
            game_state.is_processing = False
            return

        tile_type = game_state.reveal_tile(game_state.current_level, tile_index)
        if tile_type == "tnt":
            game_state.end_game(hit_tnt=True)
        elif tile_type.startswith("gold"):
            await self._handle_gold_tile(game_state, tile_type)
            game_state.complete_level()

        current_balance = game_state.cached_balance
        if game_state.game_over:
            if game_state.current_level >= TOWER_LEVELS and game_state.total_reward > 0:
                await economy_service.award_oil(
                    user_id=interaction.user.id,
                    amount=game_state.total_reward,
                    transaction_type="game:tower_win",
                    reason="Tower completion",
                )

            balance_info = await economy_service.get_balance(interaction.user.id)
            current_balance = balance_info.get("balance", 0)
            await self._record_game_stats(game_state)
            if interaction.user.id in self.active_games:
                del self.active_games[interaction.user.id]

        await self._update_view(interaction, game_state, current_balance)
        game_state.is_processing = False

    async def _handle_cashout(
        self, interaction: discord.Interaction, game_state: TowerGameState
    ):
        """處理提現"""
        # interaction 已經在回調函數中被 defer 了

        if game_state.is_processing:
            return

        game_state.is_processing = True
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # 防止重複處理：檢查遊戲是否已經結束
        if game_state.game_over:
            logger.warning("用戶 %s 重複點擊提現，遊戲已結束", interaction.user.id)
            # 直接更新界面顯示當前狀態，不執行任何遊戲邏輯
            balance_info = await economy_service.get_balance(interaction.user.id)
            current_balance = balance_info.get("balance", 0)
            embed = await self._create_game_embed(
                game_state, current_balance, interaction.user
            )
            view = TowerGameOverView(game_state, self)
            # discord.HTTPException 會被全域處理器捕捉並靜默記錄
            await interaction.edit_original_response(embed=embed, view=view)
            view.message = interaction.message
            game_state.is_processing = False
            return

        if not game_state.can_cashout or game_state.total_reward <= 0:
            raise InvalidOperationError("目前無法提現！")

        # 一次性發放所有累積獎勵到用戶餘額
        await economy_service.award_oil(
            user_id=interaction.user.id,
            amount=game_state.total_reward,
            transaction_type="game:tower_cashout",
            reason=f"Tower cash out at level {game_state.current_level}",
        )

        # 結束遊戲
        game_state.end_game()

        # 【修復】提現後，查詢真實餘額而非使用緩存
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)

        # 【修復】先記錄統計，再顯示界面，確保總盈虧能正確更新
        await self._record_game_stats(game_state)

        # 更新遊戲顯示
        embed = await self._create_game_embed(
            game_state, current_balance, interaction.user
        )
        view = TowerGameOverView(game_state, self)

        # discord.HTTPException 會被全域處理器捕捉並靜默記錄
        await interaction.edit_original_response(embed=embed, view=view)
        view.message = interaction.message

        # 清理活躍遊戲
        if interaction.user.id in self.active_games:
            del self.active_games[interaction.user.id]

        logger.info(
            "用戶 %s 提現 %s 油幣", interaction.user.id, game_state.total_reward
        )
        game_state.is_processing = False

    async def _record_game_stats(self, game_state: TowerGameState):
        """記錄遊戲統計"""
        # 判斷遊戲結果
        if game_state.hit_tnt:
            result = "lose"
        elif game_state.current_level >= TOWER_LEVELS:
            result = "win"
        elif game_state.total_reward > game_state.bet_amount:
            result = "win"
        else:
            # 理論上不應該到達這裡，因為所有獎勵都大於下注金額
            result = "lose"

        # 計算層級數據
        levels_completed = game_state.current_level  # 已完成的層數
        # 到達的最高層數：如果踩到TNT，則比完成層數多1層
        max_level_reached = game_state.current_level + (1 if game_state.hit_tnt else 0)

        # 構建遊戲數據 - 簡化後的純機率遊戲
        game_data = {
            "bet": game_state.bet_amount,
            "payout": game_state.total_reward,
            "profit": game_state.total_reward - game_state.bet_amount,
            "result": result,
            "difficulty": game_state.difficulty,
            "levels_completed": levels_completed,
            "max_level_reached": max_level_reached,
            "hit_tnt": game_state.hit_tnt,
            "early_cashout": game_state.current_level < TOWER_LEVELS
            and not game_state.hit_tnt,
            "tiles_revealed": len(game_state.revealed_tiles),
        }

        await game_stats_service.record_game_result(
            game_state.user_id, "tower", game_data
        )
        logger.info(
            "成功記錄爬塔遊戲統計: user_id=%s, result=%s",
            game_state.user_id,
            result,
        )

    async def _get_user_stats(self, user_id: int) -> Optional[Dict[str, Any]]:
        """獲取用戶統計數據"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        stats = await game_stats_service.get_user_game_stats(user_id, "tower")
        return stats

    @app_commands.command(name="tower", description="開始一局爬塔遊戲")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        difficulty="選擇遊戲難度 (可選)",
        bet_amount=(
            f"您想下注的油幣金額 (可選，預設 {MIN_BET}，範圍 {MIN_BET:,}-{MAX_BET:,})"
        ),
    )
    @app_commands.choices(
        difficulty=[
            app_commands.Choice(name="🟢 簡單 (每層1個TNT)", value="easy"),
            app_commands.Choice(name="🟡 中等 (每層2個TNT)", value="medium"),
            app_commands.Choice(name="🔴 困難 (每層3個TNT)", value="hard"),
        ]
    )
    @app_commands.checks.cooldown(1, 3.0, key=lambda i: i.user.id)
    async def tower_command(
        self,
        interaction: discord.Interaction,
        difficulty: Optional[str] = None,
        bet_amount: Optional[app_commands.Range[int, MIN_BET, MAX_BET]] = None,
    ):
        """處理爬塔遊戲命令"""
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # 先 defer
        await interaction.response.defer()

        # 如果指定了難度和金額，直接開始遊戲
        if difficulty and bet_amount:
            await self._start_tower_game(interaction, bet_amount, difficulty)
        else:
            # 顯示難度選擇界面
            await self._show_difficulty_selection_new_message(interaction, bet_amount)


async def setup(bot: commands.Bot):
    """設置Cog"""
    await bot.add_cog(TowerCog(bot))
