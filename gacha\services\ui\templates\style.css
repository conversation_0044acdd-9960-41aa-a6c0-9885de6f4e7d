/* --- Global Resets & Base --- */
body {
    margin: 0;
    padding: 0;
    width: 1536px; 
    height: 864px;  
    overflow: hidden;
    font-family: 'Noto Sans TC', 'Microsoft JhengHei', sans-serif; 
    color: #e0e0e0; 
}

/* --- Profile Container & Background --- */
.profile-container {
    width: 100%;
    height: 100%;
    position: relative;
    /* 設置背景圖，並疊加一層半透明黑色濾鏡 */
    background: 
        linear-gradient(rgba(0, 0, 0, 0.35), rgba(0, 0, 0, 0.35)), /* 黑色濾鏡，調整0.35以改變深淺 */
        url('backgound.png'); /* 你的背景圖片 */
    background-size: cover;
    background-position: center;
}

/* --- Layout Structure --- */
.main-content-area {
    /* padding 已在 HTML 中用 Tailwind 設置 */
}

.left-panel {
    /* width: 40%; 在 HTML 中用 Tailwind w-2/5 */
}

.right-panel {
    /* width: 60%; 在 HTML 中用 Tailwind w-3/5 */
}

/* --- Info Panels --- */
.info-panel {
    background: rgba(29, 24, 49, 0.65); 
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    border-radius: 16px; 
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    color: #f0f0f0; 
}

/* --- User Info Panel Specifics --- */
.user-info-panel {
    /* 特定樣式 */
}

.user-avatar-container { /* ID user-avatar in HTML */
    width: 80px; 
    height: 80px;
    border-radius: 50%;
    background-color: #4a5568; /* Placeholder color if no image */
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem; 
    font-weight: bold;
    color: white;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(255,255,255,0.2);
}
#username-initial {
    /* 樣式已在 .user-avatar-container 中處理文字部分 */
}

#username {
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

/* --- User Stats --- */
.user-stats .stat-item {
    margin-bottom: 0px; 
    font-size: 0.95rem; 
    display: flex;
    justify-content: space-between; 
}
.user-stats .stat-label {
    color: #a0aec0; 
    margin-right: 8px;
}
.user-stats .stat-value {
    font-weight: 600; 
}

/* --- Badge Icons (Optional) --- */
.badge-icon {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}


/* --- Card Styles --- */
.card-image {
    object-fit: cover;
    width: 100%;
    height: 100%;
    border-radius: 12px; 
    background-color: rgba(0,0,0, 0.2); 
    border: 2px solid rgba(255,255,255,0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card-image:hover {
    transform: scale(1.03);
    box-shadow: 0 0 20px rgba(228, 123, 205, 0.5); /* 粉色光暈示例 */
}

.main-card-container {
    width: 320px; 
    height: 450px;
    filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
}

.sub-card-container {
    width: 100px; 
    height: 140px;
     filter: drop-shadow(0 5px 8px rgba(0,0,0,0.2));
}

.card-stars {
    color: #fbbf24; 
    text-shadow: 0 0 5px rgba(251, 191, 36, 0.7);
    padding: 2px 4px;
    background-color: rgba(0,0,0,0.4);
    border-radius: 4px;
}

/* --- Main Card Info Panel Specifics --- */
.main-card-info-panel {
    max-width: 320px; 
    background: rgba(29, 24, 49, 0.75); 
}
#main-card-name {
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}
#main-card-series {
    /* text-base text-gray-300 已在 Tailwind 設置 */
}