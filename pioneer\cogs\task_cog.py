"""
Pioneer System 任務 Cog
提供與任務相關的 Discord 命令
"""

from typing import List

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data


class TaskCog(commands.Cog):
    """處理與任務相關的命令"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="tasks", description="查看你當前的任務列表")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def tasks(self, interaction: discord.Interaction):
        """顯示玩家的任務列表"""
        await interaction.response.defer(ephemeral=True)
        user_id = interaction.user.id

        active_quests = await repositories.get_user_active_quests(user_id)

        if not active_quests:
            embed = discord.Embed(
                title="📝 你的任務",
                description="你目前沒有任何進行中的任務。",
                color=discord.Color.blue(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # 將任務按類型分類
        categorized_quests = {
            "daily": [],
            "weekly": [],
            "achievement": [],
            "era_quest": [],
        }
        for quest in active_quests:
            if game_data:
                config = game_data.get_task_config(quest.quest_id)
                if config and config.type in categorized_quests:
                    categorized_quests[config.type].append((quest, config))

        embed = discord.Embed(
            title="📝 你的任務",
            description="以下是你目前正在進行的任務。",
            color=discord.Color.gold(),
        )

        # 格式化顯示每個類別的任務
        for category, quests in categorized_quests.items():
            if not quests:
                continue

            category_name = {
                "daily": "📅 每日任務",
                "weekly": "🗓️ 每週任務",
                "achievement": "🏆 成就",
                "era_quest": "🗺️ 時代任務",
            }.get(category, "其他任務")

            value = ""
            for quest, config in quests:
                # 格式化進度條
                progress_bar = TaskCog._format_progress_bar(
                    quest.progress, quest.target
                )
                # 格式化獎勵
                reward_str = TaskCog._format_rewards(config.rewards)

                value += (
                    f"**{config.name}**\n"
                    f"*{config.description}*\n"
                    f"`{quest.progress:,} / {quest.target:,}` {progress_bar}\n"
                    f"獎勵: {reward_str}\n\n"
                )

            if value:
                embed.add_field(name=category_name, value=value, inline=False)

        await interaction.followup.send(embed=embed, ephemeral=True)

    @staticmethod
    def _format_progress_bar(progress: int, target: int, length: int = 10) -> str:
        """創建一個簡單的文本進度條"""
        if target == 0:
            return "N/A"
        progress = min(progress, target)
        filled_len = int(length * progress // target)
        bar = "█" * filled_len + "░" * (length - filled_len)
        return f"[{bar}]"

    @staticmethod
    def _format_rewards(rewards: List[dict]) -> str:
        """格式化獎勵列表以便顯示"""
        parts = []
        for reward in rewards:
            if reward["type"] == "currency" and reward["currency"] == "oil":
                parts.append(f"{reward['amount']:,} 油幣")
            elif reward["type"] == "item":
                item_name = reward["item_id"]
                if game_data:
                    item_config = game_data.get_item_config(reward["item_id"])
                    if item_config:
                        item_name = item_config.name
                parts.append(f"{item_name} x{reward['quantity']:,}")
            elif reward["type"] == "xp":
                skill_name = reward["skill"]
                if game_data:
                    skill_name = game_data.get_skill_name(reward["skill"])
                parts.append(f"{reward['amount']:,} {skill_name} 經驗")

        return ", ".join(parts) if parts else "無"


async def setup(bot: commands.Bot):
    await bot.add_cog(TaskCog(bot))
