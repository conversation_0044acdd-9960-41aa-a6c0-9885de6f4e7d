import random
import uuid
import xml.etree.ElementTree as ET
from decimal import ROUND_DOWN, Decimal
from typing import Any, List, Optional

import asyncpg

from auxiliary.services.ai_core import ai_service, prompt_service
from config.app_config import get_gacha_stock_integration_config
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import GachaRuntimeError
from gacha.models.market_models import (
    StockAsset,
    StockAssetCreate,
    StockLifecycleStatus,
)
from gacha.repositories.market import stock_asset_repository as stock_asset_repo
from gacha.repositories.portfolio import (
    player_portfolio_repository as player_portfolio_repo,
)
from gacha.services import stock_news_service, user_service
from utils.logger import logger

# Configuration for the stock market
config = get_gacha_stock_integration_config().stock_market


def _get_redis_key(asset_id: int, metric_type: str) -> str:
    """生成用於存儲連續檢查次數的 Redis Key。"""
    return f"stock_lifecycle:asset_id:{asset_id}:counter:{metric_type}"


async def _get_consecutive_checks_data(asset_id: int, metric_type: str) -> int:
    """輔助方法：從 Redis 獲取特定指標連續不達標的檢查次數。"""
    key = _get_redis_key(asset_id, metric_type)
    value = None
    try:
        redis_client = get_redis_client()
        if redis_client is None:
            logger.warning("Redis client is not available.")
            return 0
        value = await redis_client.get(key)
        if value is None:
            return 0
        count = int(value)
        return count
    except ValueError:
        logger.error(
            "Redis GET for key '%s' returned non-integer value '%s'. Returning 0.",
            key,
            value,
            exc_info=True,
        )
        return 0
    except Exception as e:
        logger.error(
            "Error getting consecutive checks data from Redis for key '%s': %s",
            key,
            e,
            exc_info=True,
        )
        return 0


async def _reset_consecutive_checks_data(
    asset_id: int,
    metric_type: Optional[str] = None,
    reset_all_st: bool = False,
    reset_all_delist: bool = False,
):
    """輔助方法：重置 Redis 中特定指標連續不達標的檢查次數。"""
    keys_to_delete = []
    if metric_type:
        keys_to_delete.append(_get_redis_key(asset_id, metric_type))
    if reset_all_st:
        keys_to_delete.append(_get_redis_key(asset_id, "at_min_price"))
        keys_to_delete.append(_get_redis_key(asset_id, "below_st_market_cap"))
    if reset_all_delist:
        keys_to_delete.append(_get_redis_key(asset_id, "in_st"))
        keys_to_delete.append(_get_redis_key(asset_id, "below_delist_market_cap"))
    unique_keys_to_delete = list(set(keys_to_delete))
    if not unique_keys_to_delete:
        logger.debug("No keys specified for reset for asset_id %s.", asset_id)
        return
    try:
        redis_client = get_redis_client()
        if redis_client is None:
            logger.warning("Redis client is not available.")
            return
        deleted_count = await redis_client.delete(*unique_keys_to_delete)
        logger.info(
            "已為資產ID %s 重置 %s 個連續檢查鍵：%s",
            asset_id,
            deleted_count,
            unique_keys_to_delete,
        )
    except Exception as e:
        logger.error(
            "Error resetting consecutive checks data in Redis for asset_id %s, keys %s: %s",
            asset_id,
            unique_keys_to_delete,
            e,
            exc_info=True,
        )


async def _increment_consecutive_checks_data(asset_id: int, metric_type: str):
    """輔助方法：增加 Redis 中特定指標連續不達標的檢查次數。"""
    key = _get_redis_key(asset_id, metric_type)
    try:
        redis_client = get_redis_client()
        if redis_client is None:
            logger.warning("Redis client is not available.")
            return
        new_value = await redis_client.incr(key)
        logger.info(
            "資產ID %s 的連續檢查計數已增加，指標 '%s'。鍵 '%s' 的新值為：%s",
            asset_id,
            metric_type,
            key,
            new_value,
        )
    except Exception as e:
        logger.error(
            "Error incrementing consecutive checks data in Redis for key '%s': %s",
            key,
            e,
            exc_info=True,
        )


async def set_stock_to_st(asset_id: int) -> bool:
    """
    將指定股票的狀態更新為 ST (Special Treatment)。

    :param asset_id: 要更新狀態的股票資產ID。
    :return: 如果更新成功則返回 True，否則 False。
    """
    logger.info("Attempting to set stock %s to ST status.", asset_id)
    try:
        pool = get_pool()
        if pool is None:
            raise GachaRuntimeError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                current_stock_asset = await stock_asset_repo.get_asset_by_id(
                    asset_id, conn=conn
                )
                if (
                    not current_stock_asset
                    or current_stock_asset.lifecycle_status
                    != StockLifecycleStatus.ACTIVE
                ):
                    logger.warning(
                        "Stock %s cannot be set to ST. Current asset details: %s",
                        asset_id,
                        current_stock_asset,
                    )
                    return False
                update_success = await stock_asset_repo.update_lifecycle_status(
                    asset_id, StockLifecycleStatus.ST, conn
                )
                if update_success:
                    await _reset_consecutive_checks_data(asset_id, reset_all_st=True)

                    # 強制結算所有做空倉位
                    try:
                        forced_cover_count = await _force_cover_all_short_positions(
                            asset_id, current_stock_asset.current_price, conn
                        )
                        if forced_cover_count > 0:
                            logger.info(
                                "Successfully forced cover %s short positions for asset %s entering ST status.",
                                forced_cover_count,
                                asset_id,
                            )
                        else:
                            logger.info(
                                "No short positions found to force cover for asset %s entering ST status.",
                                asset_id,
                            )
                    except Exception as e_force_cover:
                        logger.error(
                            "Error forcing cover of short positions for asset %s entering ST status. Transaction will be rolled back: %s",
                            asset_id,
                            e_force_cover,
                            exc_info=True,
                        )
                        raise  # 重新拋出異常以觸發事務回滾

                    try:
                        news_result = await stock_news_service.generate_st_warning_news(
                            asset_id=asset_id,
                            asset_name=current_stock_asset.asset_name,
                            asset_symbol=current_stock_asset.asset_symbol,
                            current_price=current_stock_asset.current_price,
                        )
                        if news_result:
                            logger.info(
                                "Successfully generated ST warning news for asset %s. News ID: %s",
                                asset_id,
                                news_result.get("id"),
                            )
                        else:
                            logger.warning(
                                "Failed to generate ST warning news for asset %s.",
                                asset_id,
                            )
                    except Exception as e_news:
                        logger.error(
                            "Error calling generate_st_warning_news for asset %s (will not rollback DB): %s",
                            asset_id,
                            e_news,
                            exc_info=True,
                        )
                    logger.info("Stock %s successfully set to ST.", asset_id)
                    return True
                else:
                    logger.error(
                        "Failed to update stock %s status to ST in DB.", asset_id
                    )
                    return False
    except Exception as e:
        logger.error(
            "Exception in set_stock_to_st for asset_id %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return False


async def set_stock_to_active(asset_id: int) -> bool:
    """將指定ST股票的狀態更新回 ACTIVE。"""
    logger.info("Attempting to set stock %s to ACTIVE status.", asset_id)
    try:
        pool = get_pool()
        if pool is None:
            raise GachaRuntimeError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                current_stock_asset = await stock_asset_repo.get_asset_by_id(
                    asset_id, conn=conn
                )
                if (
                    not current_stock_asset
                    or current_stock_asset.lifecycle_status != StockLifecycleStatus.ST
                ):
                    logger.warning(
                        "Stock %s cannot be set to ACTIVE. Current asset details: %s",
                        asset_id,
                        current_stock_asset,
                    )
                    return False
                update_success = await stock_asset_repo.update_lifecycle_status(
                    asset_id, StockLifecycleStatus.ACTIVE, conn
                )
                if update_success:
                    await _reset_consecutive_checks_data(asset_id, reset_all_st=True)
                    await _reset_consecutive_checks_data(asset_id, metric_type="in_st")
                    try:
                        news_result = (
                            await stock_news_service.generate_st_recovery_news(
                                asset_id=asset_id,
                                asset_name=current_stock_asset.asset_name,
                                asset_symbol=current_stock_asset.asset_symbol,
                                current_price=current_stock_asset.current_price,
                            )
                        )
                        if news_result:
                            logger.info(
                                "Successfully generated ST recovery news for asset %s. News ID: %s",
                                asset_id,
                                news_result.get("id"),
                            )
                        else:
                            logger.warning(
                                "Failed to generate ST recovery news for asset %s.",
                                asset_id,
                            )
                    except Exception as e_news:
                        logger.error(
                            "Error calling generate_st_recovery_news for asset %s (will not rollback DB): %s",
                            asset_id,
                            e_news,
                            exc_info=True,
                        )
                    logger.info("Stock %s successfully set to ACTIVE.", asset_id)
                    return True
                else:
                    logger.error(
                        "Failed to update stock %s status to ACTIVE in DB.", asset_id
                    )
                    return False
    except Exception as e:
        logger.error(
            "Exception in set_stock_to_active for asset_id %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return False


async def set_stock_to_delisted(asset_id: int) -> bool:
    """
    將指定股票的狀態更新為 DELISTED (已退市)，並觸發相關的清算流程。

    :param asset_id: 要退市的股票資產ID。
    :return: 如果更新和初步清算成功則返回 True，否則 False。
    """
    logger.info("Attempting to set stock %s to DELISTED status.", asset_id)
    try:
        pool = get_pool()
        if pool is None:
            raise GachaRuntimeError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            async with conn.transaction():
                current_stock_asset = await stock_asset_repo.get_asset_by_id(
                    asset_id, conn=conn
                )
                if (
                    not current_stock_asset
                    or current_stock_asset.lifecycle_status
                    not in [StockLifecycleStatus.ST, StockLifecycleStatus.ACTIVE]
                ):
                    logger.warning(
                        "Stock %s cannot be set to DELISTED. Current asset details: %s",
                        asset_id,
                        current_stock_asset,
                    )
                    return False
                asset_name_for_news = current_stock_asset.asset_name
                asset_symbol_for_news = current_stock_asset.asset_symbol
                update_success = await stock_asset_repo.update_lifecycle_status(
                    asset_id, StockLifecycleStatus.DELISTED, conn
                )
                if update_success:
                    await _reset_consecutive_checks_data(asset_id, reset_all_st=True)
                    await _reset_consecutive_checks_data(
                        asset_id, reset_all_delist=True
                    )
                    calculated_buyback_price = await _get_dynamic_buyback_price(
                        conn, asset_id
                    )
                    await process_delisted_stock_assets_internal(
                        conn, asset_id, calculated_buyback_price
                    )
                    try:
                        news_result = await stock_news_service.generate_delist_news(
                            asset_id=asset_id,
                            asset_name=asset_name_for_news,
                            asset_symbol=asset_symbol_for_news,
                            final_buyback_price=calculated_buyback_price,
                        )
                        if news_result:
                            logger.info(
                                "Successfully generated delist news for asset %s. News ID: %s",
                                asset_id,
                                news_result.get("id"),
                            )
                        else:
                            logger.warning(
                                "Failed to generate delist news for asset %s.", asset_id
                            )
                    except Exception as e_news:
                        logger.error(
                            "Error calling generate_delist_news for asset %s (will not rollback DB): %s",
                            asset_id,
                            e_news,
                            exc_info=True,
                        )
                    created_count = await trigger_new_company_creation_if_needed(conn)
                    logger.info(
                        "Triggered new company creation after delisting asset %s (within transaction). Created %s new companies.",
                        asset_id,
                        created_count,
                    )
                    logger.info(
                        "Stock %s successfully set to DELISTED and processed.", asset_id
                    )
                    return True
                else:
                    logger.error(
                        "Failed to update stock %s status to DELISTED in DB.", asset_id
                    )
                    return False
    except Exception as e:
        logger.error(
            "Exception in set_stock_to_delisted for asset_id %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return False


async def _get_dynamic_buyback_price(
    conn: asyncpg.Connection, asset_id: int
) -> Decimal:
    """
    計算動態退市回購價。
    查詢 asset_price_history 表獲取最近 N 次價格記錄，計算其平均價格，
    然後應用配置的百分比和價格限制。
    """
    num_checks = config.delisted_buyback_avg_price_checks
    logger.info(
        "Asset %s: Calculating dynamic buyback price, using last %s price checks.",
        asset_id,
        num_checks,
    )
    avg_price: Decimal
    try:
        query = """
            SELECT price
            FROM asset_price_history
            WHERE asset_id = $1
            ORDER BY timestamp DESC
            LIMIT $2
        """
        price_records = await conn.fetch(query, asset_id, num_checks)
        if not price_records:
            logger.warning(
                "Asset %s: No price history found for dynamic buyback calculation. Using global_min_asset_price as fallback average.",
                asset_id,
            )
            avg_price = Decimal(str(config.global_min_asset_price))
        else:
            prices = [
                record["price"]
                for record in price_records
                if record["price"] is not None
            ]
            if not prices:
                logger.warning(
                    "Asset %s: Price history found but all prices are NULL. Using global_min_asset_price as fallback average.",
                    asset_id,
                )
                avg_price = Decimal(str(config.global_min_asset_price))
            else:
                avg_price = sum(prices) / Decimal(len(prices))
                logger.info(
                    "Asset %s: Found %s price records. Calculated average price: %s",
                    asset_id,
                    len(prices),
                    avg_price,
                )
    except Exception as e:
        logger.error(
            "Asset %s: Error querying price history: %s. Using global_min_asset_price as fallback average.",
            asset_id,
            e,
            exc_info=True,
        )
        avg_price = Decimal(str(config.global_min_asset_price))
    buyback_price = avg_price * Decimal(str(config.delisted_buyback_price_percentage))
    logger.info(
        "Asset %s: Buyback price after applying percentage (%s%%): %s",
        asset_id,
        config.delisted_buyback_price_percentage * 100,
        buyback_price,
    )
    min_buyback_price = Decimal(str(config.delisted_buyback_min_price))
    max_buyback_price = Decimal(str(config.delisted_buyback_max_price))
    buyback_price = max(min_buyback_price, buyback_price)
    buyback_price = min(max_buyback_price, buyback_price)
    logger.info(
        "Asset %s: Final dynamic buyback price after limits (min: %s, max: %s): %s",
        asset_id,
        min_buyback_price,
        max_buyback_price,
        buyback_price,
    )
    return buyback_price


async def process_delisted_stock_assets_internal(
    conn: asyncpg.Connection, asset_id: int, calculated_buyback_price: Decimal
) -> None:
    """
    處理已退市股票的股東資產（內部方法，使用已計算的回購價和傳入的資料庫連接）。
    當處理單個玩家的油幣返還或持倉清除操作失敗時，系統應詳細記錄該錯誤，然後繼續處理下一個玩家。
    """
    logger.info(
        "Processing delisted stock assets for asset_id=%s at calculated_buyback_price=%s using provided connection.",
        asset_id,
        calculated_buyback_price,
    )
    try:
        player_holdings = await player_portfolio_repo.get_holders_of_asset(
            asset_id, conn
        )
    except Exception as e:
        logger.error(
            "Failed to get holders for asset_id=%s during delisting process: %s",
            asset_id,
            e,
            exc_info=True,
        )
        return
    if not player_holdings:
        logger.info(
            "No players found holding asset_id=%s. No assets to process.", asset_id
        )
        return
    for holder_data in player_holdings:
        user_id = holder_data["user_id"]
        quantity = holder_data["quantity"]
        avg_cost = holder_data.get("avg_cost")
        if not isinstance(quantity, (int, Decimal)) or quantity <= 0:
            logger.warning(
                "Skipping holder user_id=%s for asset_id=%s due to invalid quantity: %s",
                user_id,
                asset_id,
                quantity,
            )
            continue
        total_buyback_amount_decimal = Decimal(quantity) * calculated_buyback_price
        total_buyback_amount_int = int(
            total_buyback_amount_decimal.to_integral_value(rounding=ROUND_DOWN)
        )
        logger.info(
            "Processing buyback for user_id=%s, asset_id=%s: quantity=%s, avg_cost=%s, buyback_price=%s, total_decimal=%s, total_int=%s",
            user_id,
            asset_id,
            quantity,
            avg_cost,
            calculated_buyback_price,
            total_buyback_amount_decimal,
            total_buyback_amount_int,
        )

        operation_stage = "未知操作"
        new_balance = None
        asset_removed = False

        try:
            operation_stage = "更新油幣餘額"
            new_balance = await user_service.award_balance(
                user_id=user_id,
                amount=total_buyback_amount_int,
                transaction_type="stock_buyback",
                reason=f"Asset {asset_id} delisted buyback",
                connection=conn,
            )
            if new_balance is None:
                logger.error(
                    "Failed to award oil to user %s for delisted asset %s. Amount: %s. award_balance returned None/False.",
                    user_id,
                    asset_id,
                    total_buyback_amount_int,
                )
            operation_stage = "清除持倉記錄"
            asset_removed = await player_portfolio_repo.remove_asset_holding(
                user_id, asset_id, conn
            )
            if not asset_removed:
                logger.warning(
                    "Failed to remove asset holding for user %s, asset %s after oil award attempt (oil award status: %s). remove_asset_holding returned False.",
                    user_id,
                    asset_id,
                    (
                        "succeeded with new_balance"
                        if new_balance is not None
                        else "failed/returned None"
                    ),
                )

            # 記錄下市結算交易
            if new_balance is not None and asset_removed:
                operation_stage = "記錄下市結算交易"
                from gacha.repositories._base_repo import execute_query

                await execute_query(
                    "INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee, context) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)",
                    (
                        user_id,
                        asset_id,
                        "DELISTED_SETTLEMENT",
                        quantity,
                        calculated_buyback_price,
                        quantity * calculated_buyback_price,
                        0,  # 下市結算無手續費
                        {"avg_cost": float(avg_cost)} if avg_cost else None,
                    ),
                    connection=conn,
                )
                logger.info(
                    "Successfully processed buyback for user_id=%s, asset_id=%s. Oil credited: %s (New Balance: %s). Asset holding removed: %s. Transaction recorded.",
                    user_id,
                    asset_id,
                    total_buyback_amount_int,
                    new_balance,
                    asset_removed,
                )
            elif new_balance is not None and (not asset_removed):
                logger.info(
                    "Successfully awarded oil for user_id=%s, asset_id=%s (Amount: %s, New Balance: %s), but failed to remove asset holding or it was already removed. No transaction recorded.",
                    user_id,
                    asset_id,
                    total_buyback_amount_int,
                    new_balance,
                )
        except Exception as e:
            logger.error(
                "Error processing delisted asset for user_id=%s, asset_id=%s. Operation stage: %s. Attempted buyback amount: %s. Error: %s",
                user_id,
                asset_id,
                operation_stage,
                total_buyback_amount_int,
                e,
                exc_info=True,
            )
            continue
    logger.info("Finished processing delisted stock assets for %s.", asset_id)


async def _force_cover_all_short_positions(
    asset_id: int, settlement_price: Decimal, conn: asyncpg.Connection
) -> int:
    """
    強制結算特定股票的所有做空倉位（ST狀態觸發時調用）

    Args:
        asset_id: 資產ID
        settlement_price: 結算價格
        conn: 數據庫連接

    Returns:
        int: 被強制結算的倉位數量
    """
    logger.info(
        "Starting forced cover of all short positions for asset %s at price %s",
        asset_id,
        settlement_price,
    )

    try:
        fee_rate = Decimal(str(config.transaction_fee_rate))
        min_fee = Decimal(str(config.minimum_transaction_fee))
        per_share_fee = Decimal(str(config.per_share_fee_amount))

        forced_cover_count = await stock_asset_repo.batch_force_cover_short_positions(
            asset_id=asset_id,
            settlement_price=settlement_price,
            fee_rate=fee_rate,
            min_fee=min_fee,
            per_share_fee=per_share_fee,
            conn=conn,
        )

        logger.info(
            "Completed forced cover of %s short positions for asset %s",
            forced_cover_count,
            asset_id,
        )
        return forced_cover_count

    except Exception as e:
        logger.error(
            "Error during forced cover of short positions for asset %s: %s",
            asset_id,
            e,
            exc_info=True,
        )
        raise


async def check_and_update_all_stocks_lifecycle() -> None:
    """
    由排程任務調用，檢查所有活躍和ST狀態的股票，並根據規則更新其生命週期。
    """
    logger.info("開始執行檢查並更新所有股票生命週期任務。")
    stocks_to_check: List[StockAsset] = []
    try:
        pool = get_pool()
        if pool is None:
            raise Exception("Database pool is not initialized.")
        async with pool.acquire() as conn:
            stocks_to_check = await stock_asset_repo.get_active_or_st_stocks(conn)
    except Exception as e:
        logger.error(
            "Error fetching stocks for lifecycle check using repository: %s",
            e,
            exc_info=True,
        )
        return
    if not stocks_to_check:
        logger.info("No active or ST stocks found to check for lifecycle updates.")
        return
    for stock_asset in stocks_to_check:
        asset_id = stock_asset.asset_id
        current_price = stock_asset.current_price
        current_status = stock_asset.lifecycle_status
        total_shares = stock_asset.total_shares
        if total_shares <= 0:
            logger.warning(
                "Stock %s has invalid total_shares (%s). Skipping lifecycle check.",
                asset_id,
                total_shares,
            )
            continue
        market_cap = current_price * Decimal(total_shares)
        if current_status == StockLifecycleStatus.ACTIVE:
            is_at_min_price = current_price <= Decimal(
                str(config.st_trigger_low_price_threshold)
            )
            if is_at_min_price:
                await _increment_consecutive_checks_data(asset_id, "at_min_price")
            else:
                await _reset_consecutive_checks_data(asset_id, "at_min_price")
            is_below_st_market_cap = market_cap < Decimal(
                str(config.st_trigger_min_market_cap)
            )
            if is_below_st_market_cap:
                await _increment_consecutive_checks_data(
                    asset_id, "below_st_market_cap"
                )
            else:
                await _reset_consecutive_checks_data(asset_id, "below_st_market_cap")
            consecutive_at_min_price = await _get_consecutive_checks_data(
                asset_id, "at_min_price"
            )
            consecutive_below_st_cap = await _get_consecutive_checks_data(
                asset_id, "below_st_market_cap"
            )
            triggered_st = False
            if (
                consecutive_at_min_price
                >= config.st_trigger_consecutive_checks_at_min_price
            ):
                logger.info(
                    "Stock %s meets ST criteria (Condition A: Price below or equal to %s). Checks: %s/%s.",
                    asset_id,
                    config.st_trigger_low_price_threshold,
                    consecutive_at_min_price,
                    config.st_trigger_consecutive_checks_at_min_price,
                )
                triggered_st = True
            elif (
                consecutive_below_st_cap
                >= config.st_trigger_consecutive_checks_below_market_cap
            ):
                logger.info(
                    "Stock %s meets ST criteria (Condition B: Below ST Market Cap). Checks: %s/%s. Market Cap: %s",
                    asset_id,
                    consecutive_below_st_cap,
                    config.st_trigger_consecutive_checks_below_market_cap,
                    market_cap,
                )
                triggered_st = True
            if triggered_st:
                exemption_roll = random.random()
                if exemption_roll < 0.15:  # 15%豁免機率
                    logger.info(
                        "Stock %s triggered ST but got exemption (15%% chance). Roll: %.4f",
                        asset_id,
                        exemption_roll,
                    )
                    await _reset_consecutive_checks_data(asset_id, reset_all_st=True)
                else:
                    logger.info(
                        "Stock %s triggered ST and exemption failed (85%% chance). Roll: %.4f. Proceeding to ST.",
                        asset_id,
                        exemption_roll,
                    )
                    await set_stock_to_st(asset_id)
        elif current_status == StockLifecycleStatus.ST:
            can_recover_price = current_price >= Decimal(
                str(config.st_recovery_min_price)
            )
            can_recover_market_cap = market_cap >= Decimal(
                str(config.st_recovery_min_market_cap)
            )
            if can_recover_price and can_recover_market_cap:
                logger.info(
                    "Stock %s (ST) meets recovery criteria. Price: %s, Market Cap: %s. Attempting to set to ACTIVE.",
                    asset_id,
                    current_price,
                    market_cap,
                )
                await set_stock_to_active(asset_id)
                continue
            await _increment_consecutive_checks_data(asset_id, "in_st")
            consecutive_in_st = await _get_consecutive_checks_data(asset_id, "in_st")
            is_below_delist_market_cap = market_cap < Decimal(
                str(config.delist_trigger_min_market_cap)
            )
            if is_below_delist_market_cap:
                await _increment_consecutive_checks_data(
                    asset_id, "below_delist_market_cap"
                )
            else:
                await _reset_consecutive_checks_data(
                    asset_id, "below_delist_market_cap"
                )
            consecutive_below_delist_cap = await _get_consecutive_checks_data(
                asset_id, "below_delist_market_cap"
            )
            triggered_delist = False
            if consecutive_in_st >= config.delist_trigger_consecutive_checks_in_st:
                logger.info(
                    "Stock %s (ST) meets DELIST criteria (Condition C: In ST too long). Checks: %s/%s.",
                    asset_id,
                    consecutive_in_st,
                    config.delist_trigger_consecutive_checks_in_st,
                )
                triggered_delist = True
            elif (
                consecutive_below_delist_cap
                >= config.delist_trigger_consecutive_checks_below_delist_market_cap
            ):
                logger.info(
                    "Stock %s (ST) meets DELIST criteria (Condition D: Below Delist Market Cap). Checks: %s/%s. Market Cap: %s",
                    asset_id,
                    consecutive_below_delist_cap,
                    config.delist_trigger_consecutive_checks_below_delist_market_cap,
                    market_cap,
                )
                triggered_delist = True
            if triggered_delist:
                await set_stock_to_delisted(asset_id)
    logger.info("Finished check_and_update_all_stocks_lifecycle task.")


async def trigger_new_company_creation_if_needed(
    conn: Optional[asyncpg.Connection] = None,
) -> int:
    """
    檢查是否需要創建新的股票公司以維持市場上活躍公司的最低數量。
    如果需要，則創建新的公司。

    :param conn: 可選的 asyncpg.Connection。如果提供，則在該連接上執行操作。
                 如果為 None，則方法內部會從 self.pool 獲取連接。
    :return: 實際創建的新公司數量。
    """
    logger.info("正在檢查是否需要創建新公司。")
    conn_to_use: Optional[asyncpg.Connection] = conn
    created_companies_count = 0
    try:
        active_companies_count: int
        if conn_to_use:
            count_row = await conn_to_use.fetchrow(
                "SELECT COUNT(*) FROM virtual_assets WHERE lifecycle_status = $1",
                StockLifecycleStatus.ACTIVE.value,
            )
            active_companies_count = count_row["count"] if count_row else 0
        else:
            active_companies_count = await _get_active_companies_count(conn_to_use)
        logger.info(
            "目前活躍公司數量：%s。最低要求數量：%s",
            active_companies_count,
            config.min_active_companies,
        )
        num_to_create = config.min_active_companies - active_companies_count
        if num_to_create > 0:
            logger.info("需要創建 %s 家新公司。", num_to_create)
            for i in range(num_to_create):
                logger.info(
                    "嘗試創建第 %s 家新公司（共 %s 家）。", i + 1, num_to_create
                )
                new_company = await create_new_stock_company(conn_param=conn)
                if new_company:
                    created_companies_count += 1
                    logger.info(
                        "Successfully created new company: %s (ID: %s). Total created so far: %s",
                        new_company.asset_name,
                        new_company.asset_id,
                        created_companies_count,
                    )
                else:
                    logger.error(
                        "Failed to create new company #%s of %s.", i + 1, num_to_create
                    )
        else:
            logger.info("No new companies need to be created at this time.")
    except Exception as e:
        logger.error(
            "Error in trigger_new_company_creation_if_needed: %s", e, exc_info=True
        )
    logger.info(
        "Finished trigger_new_company_creation_if_needed. Total new companies created: %s",
        created_companies_count,
    )
    return created_companies_count


async def _get_active_companies_count(
    conn_param: Optional[asyncpg.Connection] = None,
) -> int:
    """
    輔助方法：獲取當前活躍公司數量。
    :param conn_param: 可選的 asyncpg.Connection。如果提供，則在該連接上執行操作。
    """
    if conn_param:
        return await stock_asset_repo.get_active_companies_count(conn_param)
    else:
        pool = get_pool()
        if pool is None:
            raise Exception("Database pool is not initialized.")
        async with pool.acquire() as conn:
            return await stock_asset_repo.get_active_companies_count(conn)


async def create_new_stock_company(
    conn_param: Optional[asyncpg.Connection] = None,
    trigger_event_details: Optional[Any] = None,
) -> Optional[StockAsset]:
    logger.info(
        "Attempting to create a new stock company. Trigger: %s", trigger_event_details
    )
    conn_to_use: Optional[asyncpg.Connection] = conn_param

    async def _create_company_logic(conn: asyncpg.Connection) -> Optional[StockAsset]:
        try:
            linked_criteria_type: Optional[str] = None
            linked_criteria_value: Optional[str] = None
            linked_pool_context: Optional[str] = None
            criteria_types = ["POOL_TYPE", "RARITY_IN_POOL", "GLOBAL"]
            chosen_criteria_type = random.choice(criteria_types)
            if chosen_criteria_type == "POOL_TYPE":
                from config.app_config import get_gacha_core_settings

                gacha_core = get_gacha_core_settings()
                if gacha_core.pool_type_names:
                    pool_keys = list(gacha_core.pool_type_names.keys())
                    if pool_keys:
                        linked_criteria_type = "POOL_TYPE"
                        linked_criteria_value = random.choice(pool_keys)
                        linked_pool_context = None
                        logger.info(
                            "Selected linked_criteria: POOL_TYPE - %s",
                            linked_criteria_value,
                        )
                    else:
                        logger.warning(
                            "gacha_core.pool_type_names is empty, cannot select POOL_TYPE criteria."
                        )
                else:
                    logger.warning(
                        "gacha_core.pool_type_names is not available, cannot select POOL_TYPE criteria."
                    )
            elif chosen_criteria_type == "RARITY_IN_POOL":
                from config.app_config import get_gacha_core_settings

                gacha_core = get_gacha_core_settings()
                if gacha_core.pool_type_names and gacha_core.rarity_mapping:
                    pool_keys = list(gacha_core.pool_type_names.keys())
                    if pool_keys:
                        selected_pool_key = random.choice(pool_keys)
                        if (
                            selected_pool_key in gacha_core.rarity_mapping
                            and gacha_core.rarity_mapping[selected_pool_key]
                        ):
                            linked_criteria_type = "RARITY_IN_POOL"
                            linked_pool_context = selected_pool_key
                            linked_criteria_value = str(
                                random.choice(
                                    gacha_core.rarity_mapping[selected_pool_key]
                                )
                            )
                            logger.info(
                                "選擇的關聯標準：卡池稀有度 - 卡池：%s，稀有度：%s",
                                linked_pool_context,
                                linked_criteria_value,
                            )
                        else:
                            logger.warning(
                                "No rarities defined for pool %s in gacha_core.rarity_mapping.",
                                selected_pool_key,
                            )
                    else:
                        logger.warning(
                            "gacha_core.pool_type_names is empty, cannot select RARITY_IN_POOL context."
                        )
                else:
                    logger.warning(
                        "gacha_core.pool_type_names or gacha_core.rarity_mapping not available for RARITY_IN_POOL."
                    )
            elif chosen_criteria_type == "GLOBAL":
                linked_criteria_type = "GLOBAL"
                linked_criteria_value = "__all__"
                linked_pool_context = None
                logger.info("Selected linked_criteria: GLOBAL - __all__")
            if not linked_criteria_type:
                logger.warning(
                    "Failed to determine linked_criteria, proceeding without it."
                )
                linked_criteria_type = None
                linked_criteria_value = None
                linked_pool_context = None
            company_name: Optional[str] = None
            stock_symbol: Optional[str] = None
            company_description: Optional[str] = None
            try:
                prompt_context = {
                    "linked_criteria_type": linked_criteria_type,
                    "linked_criteria_value": linked_criteria_value,
                    "linked_pool_context": (
                        linked_pool_context if linked_pool_context is not None else ""
                    ),
                }
                news_type_for_stock_creation = "STOCK_CREATION"
                name_symbol_prompt_key = "new_stock_company_name_and_symbol_linked"
                name_symbol_prompt_template = None
                formatted_prompts_name_symbol = prompt_service.get_formatted_prompt(
                    news_type=news_type_for_stock_creation,
                    character_archetype=name_symbol_prompt_key,
                    data=prompt_context,
                )
                if (
                    formatted_prompts_name_symbol
                    and len(formatted_prompts_name_symbol) > 1
                    and formatted_prompts_name_symbol[1]
                ):
                    name_symbol_prompt_template = formatted_prompts_name_symbol[1]
                else:
                    logger.warning(
                        "Failed to get prompt template for name/symbol. News type: %s, Archetype: %s. Fallback will be used if AI generation fails.",
                        news_type_for_stock_creation,
                        name_symbol_prompt_key,
                    )
                for _ in range(3):
                    if name_symbol_prompt_template:
                        api_response_obj = await ai_service.process_text(
                            system_prompt=formatted_prompts_name_symbol[0]
                            if formatted_prompts_name_symbol
                            else "",
                            prompt=name_symbol_prompt_template,
                        )
                        current_ai_response_text = ai_service.extract_response_text(
                            api_response_obj
                        )
                        temp_name_from_ai = None
                        temp_symbol_from_ai = None
                        parsed_using_xml = False
                        try:
                            root = ET.fromstring(current_ai_response_text)
                            if root.tag == "creation_result":
                                name_node = root.find("company_name")
                                symbol_node = root.find("stock_symbol")
                                if (
                                    name_node is not None
                                    and name_node.text
                                    and (symbol_node is not None)
                                    and symbol_node.text
                                ):
                                    temp_name_from_ai = name_node.text.strip()
                                    temp_symbol_from_ai = (
                                        symbol_node.text.strip().upper()
                                    )
                                    logger.info(
                                        "AI response parsed with ElementTree (<creation_result>): Name='%s', Symbol='%s'",
                                        temp_name_from_ai,
                                        temp_symbol_from_ai,
                                    )
                                    parsed_using_xml = True
                                else:
                                    logger.warning(
                                        "ElementTree parsed <creation_result> but <company_name> or <stock_symbol> tags were missing or empty. XML: %s",
                                        current_ai_response_text,
                                    )
                            else:
                                logger.warning(
                                    "AI response was valid XML but root tag was '%s', not 'creation_result' as expected. XML: %s",
                                    root.tag,
                                    current_ai_response_text,
                                )
                        except ET.ParseError as e_xml:
                            logger.warning(
                                "XML parsing failed for name/symbol: %s. Full response: %s",
                                e_xml,
                                current_ai_response_text,
                            )
                        if not parsed_using_xml:
                            logger.warning(
                                "Attempting fallback parsing with '|' for name/symbol as XML parsing failed or yielded no data. Response: %s",
                                current_ai_response_text,
                            )
                            if "|" in current_ai_response_text:
                                parts = current_ai_response_text.split("|", 1)
                                temp_name_from_ai = parts[0].strip()
                                if len(parts) > 1 and parts[1]:
                                    temp_symbol_from_ai = (
                                        parts[1].splitlines()[0].strip().upper()
                                    )
                                else:
                                    temp_symbol_from_ai = None
                                logger.info(
                                    "AI response parsed with '|' fallback: Name='%s', Symbol='%s'",
                                    temp_name_from_ai,
                                    temp_symbol_from_ai,
                                )
                            else:
                                logger.warning(
                                    "AI response for name/symbol is empty or invalid (no parsable XML structure and no '|'): %s",
                                    current_ai_response_text,
                                )
                        if temp_name_from_ai and temp_symbol_from_ai:
                            if (
                                3 <= len(temp_symbol_from_ai) <= 5
                                and temp_symbol_from_ai.isalpha()
                            ):
                                existing_stock = await conn.fetchrow(
                                    "SELECT 1 FROM virtual_assets WHERE asset_symbol = $1",
                                    temp_symbol_from_ai,
                                )
                                if not existing_stock:
                                    company_name = temp_name_from_ai
                                    stock_symbol = temp_symbol_from_ai
                                    break
                                else:
                                    logger.warning(
                                        "AI generated stock symbol %s already exists. Retrying...",
                                        temp_symbol_from_ai,
                                    )
                            else:
                                logger.warning(
                                    "AI generated name/symbol format invalid. Name: '%s', Symbol: '%s'. Retrying...",
                                    temp_name_from_ai,
                                    temp_symbol_from_ai,
                                )
                        else:
                            logger.warning(
                                "Could not extract name/symbol from AI response: %s. Retrying...",
                                current_ai_response_text,
                            )
                    else:
                        logger.warning(
                            "Skipping AI generation for name/symbol as prompt template was not available."
                        )
                        break
                if not company_name or not stock_symbol:
                    logger.error(
                        "Failed to generate unique company name/symbol via AI after multiple attempts. Using fallback."
                    )
                    company_name = f"新創公司_{uuid.uuid4().hex[:6]}"
                    for _ in range(10):
                        temp_symbol = "".join(
                            random.choices(
                                "ABCDEFGHIJKLMNOPQRSTUVWXYZ", k=random.randint(3, 5)
                            )
                        )
                        existing_stock = await conn.fetchrow(
                            "SELECT 1 FROM virtual_assets WHERE asset_symbol = $1",
                            temp_symbol,
                        )
                        if not existing_stock:
                            stock_symbol = temp_symbol
                            break
                    if not stock_symbol:
                        logger.critical(
                            "Could not generate a unique stock symbol even with fallback. Aborting creation."
                        )
                        raise RuntimeError(
                            "Failed to generate unique fallback stock symbol"
                        )
                desc_prompt_key = "new_stock_company_description_linked"
                description_data = {
                    "company_name": company_name,
                    "stock_symbol": stock_symbol,
                    **prompt_context,
                }
                desc_prompt_template = None
                formatted_prompts_desc = prompt_service.get_formatted_prompt(
                    news_type=news_type_for_stock_creation,
                    character_archetype=desc_prompt_key,
                    data=description_data,
                )
                if (
                    formatted_prompts_desc
                    and len(formatted_prompts_desc) > 1
                    and formatted_prompts_desc[1]
                ):
                    desc_prompt_template = formatted_prompts_desc[1]
                else:
                    logger.warning(
                        "Failed to get prompt template for description. News type: %s, Archetype: %s. Fallback will be used.",
                        news_type_for_stock_creation,
                        desc_prompt_key,
                    )
                company_description = None
                if desc_prompt_template:
                    api_response_desc_obj = await ai_service.process_text(
                        system_prompt=formatted_prompts_desc[0]
                        if formatted_prompts_desc
                        else "",
                        prompt=desc_prompt_template,
                    )
                    raw_description_payload = ai_service.extract_response_text(
                        api_response_desc_obj
                    )
                    if raw_description_payload and (
                        not raw_description_payload.startswith(
                            "錯誤：AI回應未包含期望的 <ai_payload> 標籤。"
                        )
                    ):
                        company_description = raw_description_payload.strip()
                        logger.info(
                            "AI response for description (direct text from <ai_payload>): '%s...'",
                            company_description[:100],
                        )
                    else:
                        logger.warning(
                            "AI response payload for description was problematic or missing <ai_payload>. Payload: %s",
                            raw_description_payload,
                        )
                        company_description = None
                if not company_description:
                    logger.info("Using fallback company description.")
                    company_description = f"{company_name} 是一家與 {linked_criteria_value or '市場'} 相關的充滿潛力的新興企業。"
                logger.info(
                    "AI generated: Name='%s', Symbol='%s', Desc='%s...' with criteria: %s",
                    company_name,
                    stock_symbol,
                    company_description[:50],
                    prompt_context,
                )
            except Exception as e_ai:
                logger.error(
                    "Error during AI content generation (will use fallback): %s",
                    e_ai,
                    exc_info=True,
                )
                company_name = company_name or f"緊急備用公司_{uuid.uuid4().hex[:4]}"
                stock_symbol = stock_symbol or "".join(
                    random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZ", k=4)
                )
                if not stock_symbol:
                    for _ in range(10):
                        temp_symbol = "".join(
                            random.choices(
                                "ABCDEFGHIJKLMNOPQRSTUVWXYZ", k=random.randint(3, 5)
                            )
                        )
                        existing_stock = await conn.fetchrow(
                            "SELECT 1 FROM virtual_assets WHERE asset_symbol = $1",
                            temp_symbol,
                        )
                        if not existing_stock:
                            stock_symbol = temp_symbol
                            break
                    if not stock_symbol:
                        logger.critical(
                            "Could not generate a unique fallback stock symbol. Aborting creation."
                        )
                        raise RuntimeError(
                            "Failed to generate unique fallback stock symbol"
                        ) from e_ai
                desc_fallback = "一家在關鍵時刻成立的公司。"
                if linked_criteria_value:
                    desc_fallback = (
                        f"一家與 {linked_criteria_value} 相關，在關鍵時刻成立的公司。"
                    )
                company_description = company_description or desc_fallback
            total_shares = random.randint(
                config.min_initial_total_shares, config.max_initial_total_shares
            )
            initial_anchor_price = config.default_initial_anchor_price
            current_price = initial_anchor_price
            base_volatility = Decimal(str(config.default_base_volatility))
            volatility_factor = Decimal(str(config.default_volatility_factor))
            influence_weight = config.default_stock_influence_weight
            lifecycle_status = StockLifecycleStatus.ACTIVE
            stock_create_data = StockAssetCreate(
                asset_name=company_name,
                asset_symbol=stock_symbol,
                description=company_description,
                current_price=current_price,
                initial_anchor_price=initial_anchor_price,
                total_shares=total_shares,
                base_volatility=base_volatility,
                volatility_factor=volatility_factor,
                influence_weight=influence_weight,
                lifecycle_status=lifecycle_status,
                linked_criteria_type=linked_criteria_type,
                linked_criteria_value=linked_criteria_value,
                linked_pool_context=linked_pool_context,
            )
            stock_data_to_insert = stock_create_data.model_dump(exclude_none=True)
            created_stock_asset = await stock_asset_repo.insert_stock(
                stock_data_to_insert, conn
            )
            if not created_stock_asset:
                logger.error(
                    "Failed to create new stock company in DB (insert_stock returned None)."
                )
                raise RuntimeError("insert_stock failed to return a StockAsset object.")
            logger.info(
                "Successfully created new stock: ID=%s, Name='%s', Symbol='%s', LinkedCriteria: Type=%s, Value=%s, Context=%s",
                created_stock_asset.asset_id,
                created_stock_asset.asset_name,
                created_stock_asset.asset_symbol,
                created_stock_asset.linked_criteria_type,
                created_stock_asset.linked_criteria_value,
                created_stock_asset.linked_pool_context,
            )
            try:
                news_context = {
                    "linked_criteria_type": created_stock_asset.linked_criteria_type,
                    "linked_criteria_value": created_stock_asset.linked_criteria_value,
                    "linked_pool_context": created_stock_asset.linked_pool_context,
                }
                linked_criteria_description = "綜合市場型"
                lct = created_stock_asset.linked_criteria_type
                lcv = created_stock_asset.linked_criteria_value
                lpc = created_stock_asset.linked_pool_context
                if lct == "POOL_TYPE" and lcv:
                    linked_criteria_description = f"與 {lcv} 卡池相關"
                elif lct == "RARITY_IN_POOL" and lpc and lcv:
                    linked_criteria_description = f"與 {lpc} 卡池的 {lcv} 稀有度相關"
                news_context["linked_criteria_description"] = (
                    linked_criteria_description
                )
                news_context = {k: v for k, v in news_context.items() if v is not None}
                news_result = await stock_news_service.generate_new_listing_news(
                    asset_id=created_stock_asset.asset_id,
                    asset_name=created_stock_asset.asset_name,
                    asset_symbol=created_stock_asset.asset_symbol,
                    initial_price=created_stock_asset.current_price,
                    total_shares=int(created_stock_asset.total_shares),
                    **news_context,
                )
                if news_result:
                    logger.info(
                        "Successfully generated new listing news for asset %s. News ID: %s",
                        created_stock_asset.asset_id,
                        news_result.get("id"),
                    )
                else:
                    logger.warning(
                        "Failed to generate new listing news for asset %s.",
                        created_stock_asset.asset_id,
                    )
            except Exception as e_news:
                logger.error(
                    "Error calling generate_new_listing_news for asset %s (will not affect DB transaction): %s",
                    created_stock_asset.asset_id,
                    e_news,
                    exc_info=True,
                )
            return created_stock_asset
        except FileNotFoundError as e_prompt:
            logger.error(
                "Prompt file not found during new stock creation: %s",
                e_prompt,
                exc_info=True,
            )
            raise
        except asyncpg.UniqueViolationError as e_db_unique:
            logger.error(
                "Database unique constraint violation during new stock creation: %s",
                e_db_unique,
                exc_info=True,
            )
            raise
        except RuntimeError as e_runtime:
            logger.error(
                "Runtime error during company creation logic: %s",
                e_runtime,
                exc_info=True,
            )
            raise
        except Exception as e_create_logic:
            logger.error(
                "Critical error inside _create_company_logic: %s",
                e_create_logic,
                exc_info=True,
            )
            raise

    try:
        if conn_to_use:
            return await _create_company_logic(conn_to_use)
        else:
            pool = get_pool()
            if pool is None:
                raise GachaRuntimeError("資料庫連線池未初始化")
            async with pool.acquire() as new_conn:
                async with new_conn.transaction():
                    return await _create_company_logic(new_conn)
    except Exception as e_outer:
        logger.error(
            "Error in create_new_stock_company (transaction might have rolled back): %s",
            e_outer,
            exc_info=True,
        )
        return None
    logger.warning("create_new_stock_company reached end unexpectedly.")
    return None
