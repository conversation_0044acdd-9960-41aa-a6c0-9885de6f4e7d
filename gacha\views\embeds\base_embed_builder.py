from datetime import datetime
from decimal import Decimal
from typing import Any, Callable, Optional, Union

import discord

from gacha.constants import RarityLevel
from gacha.models import models as gacha_models
from gacha.services.ui import card_formatting_service
from utils.logger import logger


class BaseEmbedBuilder:
    DEFAULT_BOT_FOOTER_TEXT: str = ""
    DEFAULT_NO_DATA_TEXT: str = "N/A"
    DEFAULT_EMPTY_FIELD_VALUE: str = "\u200b"
    DEFAULT_EMBED_COLOR: discord.Color = discord.Color.blue()

    def __init__(self, data: Any, interaction: Optional[discord.Interaction] = None):
        """
        初始化 BaseEmbedBuilder。
        Args:
            data: 用於填充 Embed 的資料物件 (DTO)。
            interaction: 可選的 Discord interaction，用於需要用戶特定上下文的情況。
        """
        self.data = data
        self.interaction = interaction

    def _create_base_embed(
        self,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: Optional[discord.Color] = None,
        url: Optional[str] = None,
        timestamp: Optional[datetime] = None,
    ) -> discord.Embed:
        """
        創建一個基本的 discord.Embed 物件，並進行通用初始化。
        確保標題和描述在超出 Discord 限制時被截斷。
        """
        final_color = color if color is not None else self.DEFAULT_EMBED_COLOR
        embed = discord.Embed(
            title=self._truncate_text(title, 256, field_type="標題") if title else None,
            description=(
                self._truncate_text(description, 4096, field_type="描述")
                if description
                else None
            ),
            color=final_color,
            url=url,
        )
        if timestamp:
            embed.timestamp = timestamp
        return embed

    def _add_field(
        self, embed: discord.Embed, name: str, value: str, inline: bool = True
    ):
        """
        向 Embed 添加一個欄位，如果名稱或值超出 Discord 限制，則進行截斷。
        如果發生截斷，會記錄警告。
        確保欄位值在截斷後不為空字串。
        """
        truncated_name = self._truncate_text(name, 256, field_type="欄位名稱")
        truncated_value = self._truncate_text(value, 1024, field_type="欄位值")
        if not truncated_value or truncated_value.strip() == "":
            truncated_value = self.DEFAULT_EMPTY_FIELD_VALUE
        embed.add_field(name=truncated_name, value=truncated_value, inline=inline)

    def _set_footer(
        self,
        embed: discord.Embed,
        text: Optional[str] = None,
        icon_url: Optional[str] = None,
        include_bot_signature: bool = True,
    ):
        """
        設置 Embed 的頁腳，可選擇是否包含預設的機器人簽名。
        如果頁腳文字超出 Discord 限制，則進行截斷。
        """
        footer_parts = []
        if text:
            footer_parts.append(text)
        if include_bot_signature and self.DEFAULT_BOT_FOOTER_TEXT:
            footer_parts.append(self.DEFAULT_BOT_FOOTER_TEXT)
        final_footer_text = " | ".join(filter(None, footer_parts))
        truncated_footer_text = self._truncate_text(
            final_footer_text, 2048, field_type="頁腳文字"
        )
        if truncated_footer_text:
            embed.set_footer(text=truncated_footer_text, icon_url=icon_url)

    def _set_author(
        self,
        embed: discord.Embed,
        name: str,
        url: Optional[str] = None,
        icon_url: Optional[str] = None,
    ):
        """設置 Embed 的作者，如果名稱超出 Discord 限制，則進行截斷。"""
        truncated_name = self._truncate_text(name, 256, field_type="作者名稱")
        embed.set_author(name=truncated_name, url=url, icon_url=icon_url)

    def _set_thumbnail(self, embed: discord.Embed, url: Optional[str]):
        """如果提供了 URL，則設置 Embed 的縮圖。"""
        if url:
            embed.set_thumbnail(url=url)

    def _set_image(self, embed: discord.Embed, url: Optional[str]):
        """如果提供了 URL，則設置 Embed 的圖片。"""
        if url:
            embed.set_image(url=url)

    @staticmethod
    def _truncate_text(
        text: Optional[str],
        max_length: int,
        suffix: str = "...",
        field_type: str = "文字",
    ) -> Optional[str]:
        """
        將文字截斷到最大長度，如果截斷則附加後綴。
        如果輸入文字為 None，則返回 None。
        """
        if text is None:
            return None
        if len(text) > max_length:
            actual_max_length = max_length - len(suffix)
            if actual_max_length < 0:
                actual_max_length = 0
            logger.warning(
                "截斷 %s (長度 %s) 至 %s 字元。原始內容 (前50字元): '%s...'",
                field_type,
                len(text),
                max_length,
                text[:50],
            )
            return text[:actual_max_length] + suffix
        return text

    @staticmethod
    def _format_optional_value(
        value: Any,
        default_text: Optional[str] = None,
        formatter: Optional[Callable[[Any], str]] = None,
        prefix: str = "",
        suffix: str = "",
    ) -> str:
        """
        格式化可選值。如果值為 None，則返回 default_text。
        否則，應用可選的 formatter 並添加前綴/後綴。
        """
        actual_default = (
            default_text
            if default_text is not None
            else BaseEmbedBuilder.DEFAULT_NO_DATA_TEXT
        )
        if value is None:
            return actual_default
        formatted_value_str = str(value)
        if formatter:
            try:
                formatted_value_str = formatter(value)
            except Exception as e:
                logger.error(
                    "格式化工具處理值 '%s' 時失敗: %s", value, e, exc_info=True
                )
        return f"{prefix}{formatted_value_str}{suffix}"

    @staticmethod
    def _format_decimal(
        value: Optional[Decimal],
        precision: int = 2,
        default_on_none: Optional[str] = None,
    ) -> str:
        """將 Decimal 值格式化為具有指定精度的字串。"""
        if value is None:
            return (
                default_on_none
                if default_on_none is not None
                else BaseEmbedBuilder.DEFAULT_NO_DATA_TEXT
            )
        return f"{value:.{precision}f}"

    @staticmethod
    def _format_percentage_change(
        current_price: Optional[Decimal],
        previous_price: Optional[Decimal],
        trend_emojis: Optional[dict[str, str]] = None,
    ) -> tuple[str, str]:
        """
        計算並格式化百分比變化。
        返回一個元組 (百分比字串, 趨勢Emoji字串)。
        例如: ("+10.50%", "📈")
        """
        default_emojis = {"up": "📈", "down": "📉", "neutral": "📊"}
        active_emojis = trend_emojis if trend_emojis is not None else default_emojis
        if current_price is None:
            return (BaseEmbedBuilder.DEFAULT_NO_DATA_TEXT, "")
        if previous_price is None or previous_price == current_price:
            change = Decimal(0)
            change_percent = Decimal(0)
        elif previous_price == Decimal(0):
            change = current_price
            if current_price > 0:
                change_percent = Decimal("inf")
            elif current_price < 0:
                change_percent = Decimal("-inf")
            else:
                change_percent = Decimal(0)
        else:
            change = current_price - previous_price
            change_percent = change / previous_price * 100
        trend_emoji_char = ""
        if change > 0:
            trend_emoji_char = active_emojis.get("up", "")
        elif change < 0:
            trend_emoji_char = active_emojis.get("down", "")
        else:
            trend_emoji_char = active_emojis.get("neutral", "")
        if change_percent.is_infinite():
            sign = "+" if change_percent > 0 else "-"
            return (f"{sign}∞%", trend_emoji_char)
        return (f"{change_percent:+.2f}%", trend_emoji_char)

    def build(self) -> discord.Embed:
        """
        構建 Embed 的主要方法。
        子類別必須覆寫此方法以定義其特定的 Embed 結構。
        """
        raise NotImplementedError("子類別必須實現 build() 方法。")

    def _get_rarity_color(
        self, rarity_level: Optional[RarityLevel], pool_type: Optional[str]
    ) -> discord.Color:
        """
        根據稀有度和卡池類型獲取 Embed 顏色。

        Args:
            rarity_level: 卡片的稀有度等級 (RarityLevel Enum)。
            pool_type: 卡池類型 (str)。

        Returns:
            discord.Color: 對應的顏色。
        """
        if rarity_level is None:
            logger.warning(
                "傳入的 rarity_level 為 None (卡池: '%s')。將使用預設 Embed 顏色: %s",
                pool_type,
                self.DEFAULT_EMBED_COLOR,
            )
            return self.DEFAULT_EMBED_COLOR
        rarity_value = rarity_level.value
        from config.app_config import get_rarity_colors_int

        rarity_colors = get_rarity_colors_int()
        actual_pool_key = (
            pool_type if pool_type and pool_type in rarity_colors else "default"
        )
        pool_colors_map = rarity_colors.get(actual_pool_key)
        color_value = None
        if pool_colors_map:
            color_value = pool_colors_map.get(rarity_value)
        if color_value is None and actual_pool_key != "default":
            default_pool_colors_map = rarity_colors.get("default")
            if default_pool_colors_map:
                color_value = default_pool_colors_map.get(rarity_value)
        if color_value is not None:
            return discord.Color(value=color_value)
        else:
            logger.warning(
                "在卡池 '%s' 和 'default' 卡池中找不到稀有度 '%s' (值: %s) 的顏色。將使用預設 Embed 顏色: %s",
                pool_type,
                rarity_level.name,
                rarity_value,
                self.DEFAULT_EMBED_COLOR,
            )
            return self.DEFAULT_EMBED_COLOR

    def _format_owner_count(
        self, owner_count: Optional[int], format_type: str = "display"
    ) -> str:
        """
        格式化卡片擁有者數量以供顯示。

        Args:
            owner_count: 擁有者的數量。
            format_type: 格式類型 ("display" 或 "draw")。

        Returns:
            格式化後的擁有者數量字串。
        """
        if owner_count is None or owner_count < 0:
            return ""
        if format_type == "draw":
            return f"擁有者: {owner_count:,}" if owner_count > 0 else ""
        elif format_type == "display":
            return f"持有人數: `{owner_count:,}`"
        else:
            return f"擁有者: {owner_count:,}"

    def _set_rarity_thumbnail(
        self,
        embed: discord.Embed,
        rarity_level: Optional[RarityLevel],
        pool_type: Optional[str] = None,
    ):
        """
        根據稀有度設置 Embed 的縮圖。

        Args:
            embed: 要修改的 discord.Embed 物件。
            rarity_level: 卡片的稀有度等級 (RarityLevel Enum)。
            pool_type: 卡池類型 (str)，目前未使用，但為將來可能的擴展保留。
        """
        if rarity_level is None:
            logger.warning(
                "試圖為 None 的 rarity_level 設置縮圖 (卡池: '%s')。",
                pool_type or "all_pools",
            )
            return
        rarity_value = rarity_level.value
        from config.app_config import get_rarity_images_url

        rarity_images_config = get_rarity_images_url()
        all_pools_images = rarity_images_config.get("all_pools", {})

        thumbnail_url = all_pools_images.get(rarity_value)
        if thumbnail_url is None:
            thumbnail_url = all_pools_images.get(str(rarity_value))

        if thumbnail_url:
            self._set_thumbnail(embed, url=thumbnail_url)
        else:
            logger.warning(
                "在 RARITY_IMAGES 中找不到稀有度 '%s' (值: %s, 卡池: '%s') 的縮圖 URL。",
                rarity_level.name,
                rarity_value,
                pool_type or "all_pools",
            )

    def _set_common_footer(
        self,
        embed: discord.Embed,
        card: Optional[gacha_models.Card] = None,
        page_info: Optional[str] = None,
        include_bot_signature: bool = True,
        footer_icon_url: Optional[str] = None,
    ):
        """
        設置通用的 Embed 頁腳，可選擇包含卡片 ID 和頁面資訊。

        Args:
            embed: 要修改的 discord.Embed 物件。
            card: 可選的卡片物件，用於提取卡片 ID。
            page_info: 可選的頁面資訊字串。
            include_bot_signature: 是否包含預設的機器人簽名。
            footer_icon_url: 可選的頁腳圖標 URL。
        """
        footer_text_parts = []
        if page_info:
            footer_text_parts.append(page_info)
        if card and hasattr(card, "card_id") and (card.card_id is not None):
            footer_text_parts.append(f"Card ID: {card.card_id}")
        final_text = " • ".join(filter(None, footer_text_parts))
        self._set_footer(
            embed,
            text=final_text if final_text else None,
            icon_url=footer_icon_url,
            include_bot_signature=include_bot_signature,
        )

    def _set_draw_author(
        self,
        embed: discord.Embed,
        user: Union[discord.User, discord.Member],
        nickname: Optional[str] = None,
        is_multi_draw: bool = False,
        single_draw_card_name: Optional[str] = None,
        single_draw_is_wish: bool = False,
        single_draw_pool_prefix: Optional[str] = None,
        single_draw_is_new_card: bool = False,
    ):
        """
        設置抽卡操作的 Embed 作者欄。
        對於單抽，作者行將顯示統一格式。
        對於多抽，作者行相對簡單。
        """
        display_name = nickname or user.display_name
        icon_url = user.display_avatar.url
        if is_multi_draw:
            author_name = f"{display_name} 的十連抽結果"
        else:
            author_name = f"{display_name} 的單抽結果"
        self._set_author(embed, name=author_name, icon_url=icon_url)

        # 對於單抽，將抽卡標題設置為 embed 的 title
        if not is_multi_draw:
            if single_draw_pool_prefix is None:
                logger.warning(
                    "_set_draw_author called for single draw without single_draw_pool_prefix."
                )
                actual_pool_prefix = "某個卡池"
            else:
                actual_pool_prefix = single_draw_pool_prefix
            draw_title = card_formatting_service.format_draw_title(
                display_name=display_name,
                is_wish=single_draw_is_wish,
                pool_name_or_prefix=actual_pool_prefix,
                is_new_card=single_draw_is_new_card,
            )
            embed.title = draw_title

    def _build_card_primary_details_value(
        self,
        card: gacha_models.Card,
        is_favorite: bool,
        is_new_card: bool,
        is_wish: bool,
        star_level: int = 0,
        owner_count: Optional[int] = None,
        pool_type: Optional[str] = None,
        include_price_and_balance: bool = False,
        balance: Optional[int] = None,
    ) -> str:
        """
        構建卡片主要詳細資訊的組合字串，現在主要委託給 CardFormattingService。
        包含狀態表情、卡片名稱、星級、系列、擁有者數量，以及可選的價格和餘額。
        """
        lines = []
        pool_emoji_prefix = ""
        if pool_type:
            from config.app_config import get_pool_type_emojis

            pool_emoji_prefix = get_pool_type_emojis().get(pool_type, "")
            if pool_emoji_prefix:
                pool_emoji_prefix += " "
        name_star_line = card_formatting_service.format_card_name_and_star_line(
            card_name=card.name,
            is_wish=is_wish,
            pool_emoji_prefix=pool_emoji_prefix,
            star_level=star_level,
        )
        if name_star_line:
            lines.append(name_star_line)
        series_line = card_formatting_service.format_card_series_line(
            card_series=card.series
        )
        if series_line:
            lines.append(series_line)
        # 擁有者信息已移動到頁腳，不再在主要內容中顯示

        # 添加價格和餘額信息（如果需要）
        if include_price_and_balance and balance is not None:
            # 獲取卡片稀有度
            card_rarity_enum = None
            try:
                if isinstance(card.rarity, int):
                    card_rarity_enum = RarityLevel(card.rarity)
                elif isinstance(card.rarity, RarityLevel):
                    card_rarity_enum = card.rarity
            except ValueError:
                pass

            price_label, price_display = (
                card_formatting_service.format_market_price_display(
                    current_market_sell_price=card.current_market_sell_price,
                    card_rarity_for_fallback=card_rarity_enum,
                    pool_type_for_fallback=pool_type or card.pool_type or "default",
                )
            )
            balance_display = card_formatting_service.format_balance_display(balance)
            price_balance_line = card_formatting_service.format_price_and_balance_line(
                price_label, price_display, balance_display
            )
            if price_balance_line:
                lines.append(price_balance_line)

        return "\n".join(filter(None, lines))

    def _add_card_primary_details_field_or_description_with_price(
        self,
        embed: discord.Embed,
        card: gacha_models.Card,
        is_favorite: bool,
        is_new_card: bool,
        is_wish: bool,
        star_level: int = 0,
        owner_count: Optional[int] = None,
        as_description: bool = False,
        field_name: Optional[str] = None,
        inline: bool = False,
        pool_type: Optional[str] = None,
        balance: Optional[int] = None,
    ):
        """
        將卡片主要資訊 (名稱、系列、擁有者、價格和餘額) 添加到 Embed 的 description 或作為一個 field。
        """
        actual_pool_type = pool_type if pool_type is not None else card.pool_type
        value_str = self._build_card_primary_details_value(
            card=card,
            is_favorite=is_favorite,
            is_new_card=is_new_card,
            is_wish=is_wish,
            star_level=star_level,
            owner_count=owner_count,
            pool_type=actual_pool_type,
            include_price_and_balance=True,
            balance=balance,
        )
        if as_description:
            embed.description = (
                self._truncate_text(value_str, 4096, field_type="描述")
                if value_str
                else self.DEFAULT_EMPTY_FIELD_VALUE
            )
        else:
            actual_field_name = field_name
            if not actual_field_name:
                logger.debug(
                    "_add_card_primary_details_field_or_description_with_price: field_name 未提供，將使用 '卡片資訊'"
                )
                actual_field_name = "卡片資訊"
            self._add_field(
                embed, name=actual_field_name, value=value_str, inline=inline
            )

    def _set_card_visuals(
        self,
        embed: discord.Embed,
        card_image_url: Optional[str],
        card_rarity_enum: Optional[RarityLevel],
        pool_type: Optional[str] = None,
    ):
        """
        設置卡片的圖片和稀有度縮圖。

        Args:
            embed: Discord Embed 對象
            card_image_url: 卡片圖片 URL
            card_rarity_enum: 卡片稀有度枚舉
            pool_type: 卡池類型
        """
        if card_image_url:
            self._set_image(embed, url=card_image_url)
        self._set_rarity_thumbnail(
            embed, rarity_level=card_rarity_enum, pool_type=pool_type
        )

    def _set_draw_specific_footer(
        self,
        embed: discord.Embed,
        card_for_id: Optional[gacha_models.Card] = None,
        is_wish: bool = False,
        multi_draw_page_info: Optional[str] = None,
        owner_count: Optional[int] = None,
        sell_command_text: str = "",
        include_bot_signature: bool = True,
    ):
        """
        設置特定於抽卡操作的 Embed 頁腳。
        利用 CardFormattingService 生成主要內容，然後結合頁面資訊，
        最後由 _set_common_footer 完成頁腳的最終構建（包括卡片ID和簽名）。
        """
        main_command_and_status_text = (
            card_formatting_service.format_draw_footer_text_main_content(
                is_wish=is_wish,
                owner_count=owner_count,
                sell_command_text=sell_command_text,
            )
        )
        current_context_parts = []
        if main_command_and_status_text:
            current_context_parts.append(main_command_and_status_text)
        if multi_draw_page_info:
            current_context_parts.append(multi_draw_page_info)
        current_context_footer_text = " • ".join(filter(None, current_context_parts))

        # 如果有擁有者信息，添加圖標
        footer_icon_url = None
        if owner_count is not None and owner_count > 0:
            footer_icon_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1382158954649489522/question.png"

        self._set_common_footer(
            embed=embed,
            card=card_for_id,
            page_info=(
                current_context_footer_text if current_context_footer_text else None
            ),
            footer_icon_url=footer_icon_url,
            include_bot_signature=include_bot_signature,
        )
