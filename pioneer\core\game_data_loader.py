"""
Pioneer System 遊戲資料載入器
支援熱重載的配置文件管理系統
"""

from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

from pioneer.exceptions import PioneerConfigError, PioneerValidationError
from pioneer.models.pioneer_models import (
    ActionConfig,
    EconomyConfig,
    EraConfig,
    FacilityConfig,
    ItemConfig,
    RecipeConfig,
    ResearchProjectConfig,
    SkillConfig,
    TaskConfig,
)
from utils.logger import logger


class GameDataLoader:
    """遊戲數據載入器，支援熱重載和配置驗證"""

    def __init__(self, config_dir: str = "pioneer/config"):
        """初始化載入器

        Args:
            config_dir: 配置文件目錄路徑
        """
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Any] = {}
        self._validators = {
            "economy": EconomyConfig,
            "items": Dict[str, ItemConfig],
            "recipes": Dict[str, RecipeConfig],
            "facilities": Dict[str, FacilityConfig],
            "actions": Dict[str, ActionConfig],
            "eras": List[EraConfig],
            "research_projects": Dict[str, ResearchProjectConfig],
            "tasks": Dict[str, TaskConfig],
            "skills": Dict[str, SkillConfig],
        }

        # 確保配置目錄存在
        self.config_dir.mkdir(parents=True, exist_ok=True)

    async def initialize(self):
        """初始化載入所有配置文件"""
        logger.info("開始載入 Pioneer 系統配置文件...")

        config_files = [
            "economy.yaml",
            "items.yaml",
            "recipes.yaml",
            "facilities.yaml",
            "actions.yaml",
            "eras.yaml",
            "research_projects.yaml",
            "tasks.yaml",
            "skills.yaml",
            "stories.yaml",
        ]

        for config_file in config_files:
            config_name = config_file.replace(".yaml", "")
            try:
                await self._load_config_file(config_name, config_file)
                logger.info("成功載入配置文件: %s", config_file)
            except FileNotFoundError:
                logger.warning("配置文件不存在: %s，將使用空配置", config_file)
                self._configs[config_name] = self._get_default_config(config_name)
            except Exception as e:
                logger.error("載入配置文件失敗: %s, 錯誤: %s", config_file, e)
                raise PioneerConfigError(f"載入配置文件 {config_file} 失敗: {e}") from e

        logger.info("Pioneer 系統配置文件載入完成")

    async def _load_config_file(self, config_name: str, filename: str):
        """載入單個配置文件並進行驗證"""
        file_path = self.config_dir / filename

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                raw_data = yaml.safe_load(f)

            # 驗證配置數據
            validated_data = self._validate_config(config_name, raw_data)
            self._configs[config_name] = validated_data

        except yaml.YAMLError as e:
            logger.error("YAML 解析錯誤 %s: %s", filename, e)
            raise PioneerConfigError(f"YAML 解析錯誤 {filename}: {e}") from e
        except Exception as e:
            logger.error("讀取配置文件錯誤 %s: %s", filename, e)
            raise PioneerConfigError(f"讀取配置文件錯誤 {filename}: {e}") from e

    def _validate_config(self, config_name: str, raw_data: Any) -> Any:
        """使用 Pydantic 模型驗證配置數據"""
        if config_name not in self._validators:
            return raw_data

        validator = self._validators[config_name]

        try:
            if config_name in [
                "items",
                "recipes",
                "facilities",
                "actions",
                "research_projects",
                "tasks",
                "skills",
            ]:
                # 字典類型配置
                validated_dict = {}
                model_map = {
                    "items": ItemConfig,
                    "recipes": RecipeConfig,
                    "facilities": FacilityConfig,
                    "actions": ActionConfig,
                    "research_projects": ResearchProjectConfig,
                    "tasks": TaskConfig,
                    "skills": SkillConfig,
                }
                if config_name in model_map:
                    model = model_map[config_name]
                    for key, value in raw_data.items():
                        validated_dict[key] = model(**value)
                    return validated_dict
            elif config_name == "eras":
                # 列表類型配置
                return [EraConfig(**era) for era in raw_data]
            else:
                # 單一對象配置
                return validator(**raw_data)

        except Exception as e:
            raise PioneerValidationError(f"配置驗證失敗 {config_name}: {e}") from e

    def _get_default_config(self, config_name: str) -> Any:
        """獲取默認配置"""
        defaults = {
            "economy": {
                "base_production": {"manual_gathering_efficiency": 0.5},
                "offline_limits": {"max_offline_hours": 168},
            },
            "items": {},
            "recipes": {},
            "facilities": {},
            "actions": {},
            "eras": [],
            "research_projects": {},
            "tasks": {},
        }
        return defaults.get(config_name, {})

    async def reload_config(self, config_name: str) -> bool:
        """重新載入指定配置文件"""
        filename = f"{config_name}.yaml"
        try:
            await self._load_config_file(config_name, filename)
            logger.info("成功重新載入配置文件: %s", filename)
            return True
        except Exception as e:
            logger.error("重新載入配置文件失敗: %s, 錯誤: %s", filename, e)
            return False

    async def reload_all_configs(self) -> bool:
        """重新載入所有配置文件"""
        try:
            await self.initialize()
            return True
        except Exception as e:
            logger.error("重新載入所有配置文件失敗: %s", e)
            return False

    # ========================================
    # 配置獲取方法
    # ========================================

    def get_config(self, config_name: str) -> Any:
        """獲取指定配置"""
        return self._configs.get(config_name, {})

    @property
    def economy(self) -> EconomyConfig:
        """獲取經濟配置"""
        return self._configs.get("economy", self._get_default_config("economy"))

    def get_item_config(self, item_id: str) -> Optional[ItemConfig]:
        """獲取物品配置"""
        return self._configs.get("items", {}).get(item_id)

    def get_recipe_config(self, recipe_id: str) -> Optional[RecipeConfig]:
        """獲取配方配置"""
        return self._configs.get("recipes", {}).get(recipe_id)

    def get_facility_config(self, facility_type: str) -> Optional[FacilityConfig]:
        """獲取設施配置"""
        return self._configs.get("facilities", {}).get(facility_type)

    def get_action_config(self, action_id: str) -> Optional[ActionConfig]:
        """獲取動作配置"""
        return self._configs.get("actions", {}).get(action_id)

    def get_era_config(self, era_id: int) -> Optional[EraConfig]:
        """獲取時代配置"""
        eras = self._configs.get("eras", [])
        for era in eras:
            if era.era_id == era_id:
                return era
        return None

    def get_research_project_config(
        self, project_id: str
    ) -> Optional[ResearchProjectConfig]:
        """獲取研究項目配置"""
        return self._configs.get("research_projects", {}).get(project_id)

    def get_task_config(self, task_id: str) -> Optional[TaskConfig]:
        """獲取任務配置"""
        return self._configs.get("tasks", {}).get(task_id)

    def get_skill_config(self, skill_id: str) -> Optional[SkillConfig]:
        """獲取技能配置"""
        return self._configs.get("skills", {}).get(skill_id)

    def get_skill_name(self, skill_id: str) -> str:
        """獲取技能中文名稱"""
        skill_config = self.get_skill_config(skill_id)
        return skill_config.name if skill_config else skill_id

    def get_item_name(self, item_id: str) -> str:
        """獲取物品中文名稱"""
        item_config = self.get_item_config(item_id)
        return item_config.name if item_config else item_id

    def get_all_items(self) -> Dict[str, ItemConfig]:
        """獲取所有物品配置"""
        return self._configs.get("items", {})

    def get_all_facilities(self) -> Dict[str, FacilityConfig]:
        """獲取所有設施配置"""
        return self._configs.get("facilities", {})

    def get_all_eras(self) -> List[EraConfig]:
        """獲取所有時代配置"""
        return self._configs.get("eras", [])

    def get_all_actions(self) -> Dict[str, ActionConfig]:
        """獲取所有動作配置"""
        return self._configs.get("actions", {})

    def get_all_recipes(self) -> Dict[str, RecipeConfig]:
        """獲取所有配方配置"""
        return self._configs.get("recipes", {})

    @property
    def skills(self) -> Dict[str, SkillConfig]:
        """獲取所有技能配置"""
        return self._configs.get("skills", {})

    def validate_config_integrity(self) -> List[str]:
        """驗證配置完整性，返回錯誤列表"""
        errors = []

        # 檢查配方引用的物品是否存在
        items = self.get_all_items()
        recipes = self._configs.get("recipes", {})

        for recipe_id, recipe in recipes.items():
            for input_item in recipe.inputs:
                if input_item.get("item_id") not in items:
                    errors.append(
                        f"配方 {recipe_id} 引用了不存在的物品: {input_item.get('item_id')}"
                    )

            for output_item in recipe.outputs:
                if output_item.get("item_id") not in items:
                    errors.append(
                        f"配方 {recipe_id} 產出了不存在的物品: {output_item.get('item_id')}"
                    )

        # 檢查設施引用的物品是否存在
        facilities = self.get_all_facilities()
        for facility_id, facility in facilities.items():
            if facility.inputs:
                for input_req in facility.inputs:
                    if input_req.get("item_id") not in items:
                        errors.append(
                            f"設施 {facility_id} 引用了不存在的物品: {input_req.get('item_id')}"
                        )

            for output in facility.outputs:
                if output.get("item_id") and output.get("item_id") not in items:
                    errors.append(
                        f"設施 {facility_id} 產出了不存在的物品: {output.get('item_id')}"
                    )

        return errors


# ========================================
# 單例模式實現
# ========================================

game_data: Optional[GameDataLoader] = None
_initialized = False


async def initialize_game_data(config_dir: str = "pioneer/config"):
    """
    初始化全局的 GameDataLoader 單例。
    這個函數應該在應用程式啟動時被調用一次。
    """
    global game_data, _initialized
    if not _initialized:
        logger.info("正在初始化全局 Pioneer GameDataLoader...")
        instance = GameDataLoader(config_dir=config_dir)
        await instance.initialize()
        game_data = instance
        _initialized = True
        logger.info("全局 Pioneer GameDataLoader 初始化完成。")
    else:
        logger.info("全局 Pioneer GameDataLoader 已初始化，跳過。")


def get_game_data() -> GameDataLoader:
    """
    獲取 GameDataLoader 的全局單例。

    Raises:
        RuntimeError: 如果 game_data 尚未初始化。

    Returns:
        GameDataLoader: 全局單例實例。
    """
    if not game_data:
        raise RuntimeError(
            "Pioneer GameDataLoader 尚未初始化。請先調用 initialize_game_data()。"
        )
    return game_data
