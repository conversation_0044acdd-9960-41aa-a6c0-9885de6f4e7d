# Pioneer System 故事配置
# 定義各個階段的故事背景和引導文本

# 新手故事背景
newbie_story:
  title: "🌟 歡迎來到開拓者世界！"
  background: |
    💸 **你的故事**
    曾經在米米賭場揮金如土的 {player_name}，一夜之間輸光了所有積蓄...
    面對空蕩蕩的錢包，你決定痛改前非，遠離賭桌，用雙手重新開始！

    🌲 **開拓之路**
    在這片未開發的土地上，你將從最基本的伐木和採礦開始，
    一步步建立屬於自己的產業帝國！

  guide_steps:
    - title: "🌲 第一步：採集資源"
      description: "使用 `/gather 伐木` 和 `/gather 採礦` 獲得基礎材料"

    - title: "🔨 第二步：製作工具"
      description: "使用 `/craft 木板` 然後 `/craft 木斧` 提升採集效率"

    - title: "💎 第三步：尋找燧石"
      description: "持續採礦，3%機率獲得燧石來製作更好的石製工具"

    - title: "⚡ 第四步：技能發展"
      description: "用石製工具快速提升技能等級，為後期發展打基礎"

  tips:
    - "💡 工具可以大幅提升採集效率，優先製作"
    - "💡 燧石是稀有材料，獲得後可製作強力的石製工具"
    - "💡 新手階段重點是工具進階和技能發展，設施建造是後期內容"

  footer: "💪 從零開始，重新崛起！戒賭從今天開始！"

# 時代故事
era_stories:
  1:
    name: "拓荒時代"
    description: "開拓者的起步階段，學習基本的採集和製作技能"
    story: |
      🌲 **拓荒時代的開始**
      你站在這片未開發的土地上，手中只有最基本的工具。
      曾經的賭徒生涯已成過去，現在是時候用雙手創造新的未來。
      
      從砍伐第一棵樹開始，從挖掘第一塊石頭開始，
      你的開拓之路正式啟程！
    
    goals:
      - "掌握伐木和採礦的基本技能"
      - "學會製作基礎的建築材料"
      - "建造你的第一個生產設施"
    
    next_era_hint: "當你掌握了基本技能並建立了穩定的資源來源，工業萌芽時代就會到來..."

  2:
    name: "工業萌芽"
    description: "建造基礎設施，開始自動化生產"
    story: |
      🏭 **工業的萌芽**
      經過一段時間的努力，你已經不再是那個一無所有的賭徒。
      手中的資源越來越多，是時候建立更大規模的生產設施了。
      
      自動化的設施將為你帶來穩定的收入，
      讓你有更多時間規劃未來的發展方向。
    
    goals:
      - "建造多個自動化生產設施"
      - "開始研究提升效率的技術"
      - "積累足夠的資本進入重工業時代"
    
    next_era_hint: "當你的設施網絡足夠龐大，重工業時代的大門就會為你敞開..."

  3:
    name: "重工業時代"
    description: "發展冶金和研究，提升生產效率"
    story: |
      ⚙️ **重工業的崛起**
      你的產業帝國已初具規模，基礎設施遍布各地。
      現在是時候投資更先進的技術，發展重工業了。
      
      冶金技術將讓你生產更高級的材料，
      研究投資將為你帶來長期的競爭優勢。
    
    goals:
      - "發展先進的冶金技術"
      - "大量投資研究項目"
      - "建立完整的產業鏈"
    
    next_era_hint: "當重工業達到一定規模，自動化革命就在眼前..."

# 成就故事
achievement_stories:
  first_gather:
    title: "初次採集"
    story: "你拿起工具，砍下了第一棵樹。這一刻，你正式告別了賭徒的過去，開始了開拓者的新生活。"
  
  first_craft:
    title: "初次製作"
    story: "看著手中親手製作的物品，你感受到了創造的喜悅。這比任何賭局的勝利都更加真實和持久。"
  
  first_facility:
    title: "建立產業"
    story: "第一個設施建成了！看著它自動運轉，你意識到自己已經從一個賭徒變成了真正的企業家。"

# 進度提示
progress_hints:
  era_1:
    suggestion: "跟隨「下一步行動」的指引，逐步建立你的產業基礎"

  era_2:
    suggestion: "建造更多設施來自動生產資源和賺取油幣"

  era_3:
    suggestion: "投資研究項目來提升生產效率"

  default:
    suggestion: "探索更高級的技術和設施"
