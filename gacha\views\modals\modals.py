"""
包含 Gacha 系統中使用的 Modals
"""

from decimal import Decimal
from typing import TYPE_CHECKING, Awaitable, Callable, Optional

import discord

from gacha.services import stock_trading_service
from utils.base_modal import BaseModal
from utils.response_embeds import SuccessEmbed

if TYPE_CHECKING:
    from utils.base_view import BotType


class BuyStockModal(BaseModal, title="買入股票"):
    quantity_input = discord.ui.TextInput(
        label="購買數量",
        placeholder="請輸入要購買的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        user_id: int,
        stock_symbol: str,
        current_price: Decimal,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="買入股票")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        # 根據規範，UI層不應包含 try-except 塊，讓錯誤自然冒泡
        await interaction.response.defer(ephemeral=True, thinking=True)

        quantity_str = self.quantity_input.value
        try:
            quantity = int(quantity_str)
        except ValueError as e:
            # 這是唯一應該在UI層處理的輸入驗證，因為它不涉及業務邏輯
            # 或者，也可以將其包裝成 BusinessError 由服務層拋出
            from gacha.exceptions import InvalidQuantityError

            raise InvalidQuantityError(quantity=quantity_str) from e

        # 直接調用服務，所有業務邏輯錯誤 (如餘額不足、數量 <= 0) 應由服務層拋出 BusinessError
        message = await stock_trading_service.buy_stock(
            user_id=interaction.user.id,
            asset_symbol=self.stock_symbol,
            quantity=quantity,
        )

        # 成功後發送訊息
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        # 成功後執行回調，同樣不捕捉異常
        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)


class SellStockModal(BaseModal, title="賣出股票"):
    quantity_input = discord.ui.TextInput(
        label="賣出數量",
        placeholder="請輸入要賣出的股數",
        min_length=1,
        max_length=10,
        required=True,
    )

    def __init__(
        self,
        bot: "BotType",
        stock_symbol: str,
        current_price: Decimal,
        user_id: int,
        max_quantity: int,
        on_trade_complete_callback: Optional[
            Callable[[discord.Interaction], Awaitable[None]]
        ] = None,
    ):
        super().__init__(bot=bot, title="賣出股票")
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.user_id = user_id
        self.max_quantity = max_quantity
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(
            discord.ui.TextInput(
                label="股票代碼",
                default=self.stock_symbol,
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="當前每股價格 (油幣)",
                default=f"{self.current_price:.2f}",
                style=discord.TextStyle.short,
                required=False,
            )
        )
        self.add_item(
            discord.ui.TextInput(
                label="您目前持有股數",
                default=str(self.max_quantity),
                style=discord.TextStyle.short,
                required=False,
            )
        )

    async def on_submit(self, interaction: discord.Interaction):
        # 根據規範，UI層不應包含 try-except 塊，讓錯誤自然冒泡
        await interaction.response.defer(ephemeral=True, thinking=True)

        quantity_str = self.quantity_input.value
        try:
            quantity = int(quantity_str)
        except ValueError as e:
            from gacha.exceptions import InvalidQuantityError

            raise InvalidQuantityError(quantity=quantity_str) from e

        # 業務邏輯檢查 (quantity > self.max_quantity) 應由服務層處理
        # 並拋出 InsufficientStockQuantityError

        # 直接調用服務
        message = await stock_trading_service.sell_stock(
            user_id=self.user_id, asset_symbol=self.stock_symbol, quantity=quantity
        )

        # 成功後發送訊息
        embed = SuccessEmbed(description=message)
        await interaction.followup.send(embed=embed, ephemeral=False)

        # 成功後執行回調，同樣不捕捉異常
        if self.on_trade_complete_callback:
            await self.on_trade_complete_callback(interaction)
