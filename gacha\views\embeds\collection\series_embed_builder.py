"""
Gacha系統系列收集Embed構建器
"""

from datetime import datetime
from typing import Dict, List, Optional, Union

import discord

from gacha.models.models import SeriesCollection
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views.utils import get_completion_indicator


class SeriesEmbedBuilder(BaseEmbedBuilder):
    """構建系列收集嵌入消息的類"""

    def __init__(
        self, user: Union[discord.User, discord.Member], nickname: Optional[str] = None
    ):
        """初始化系列Embed構建器

        參數:
            user: Discord用戶對象
            nickname: 用戶暱稱，如果有的話優先使用
        """
        series_data = {"user": user, "nickname": nickname}
        super().__init__(data=series_data)
        self.user = user
        self.nickname = nickname
        self.display_name = nickname or user.display_name

    def build_single_series_embed(
        self, series_collection: SeriesCollection
    ) -> discord.Embed:
        """創建單個系列收集情況的Embed顯示

        參數:
            series_collection: 系列收集信息

        返回:
            格式化後的Discord Embed對象
        """
        completion_rate = series_collection.completion_rate
        indicator, color = get_completion_indicator(completion_rate)
        embed = self._create_base_embed(
            title=f"{indicator} 「{series_collection.series}」系列收集進度",
            description=f"{self.display_name}已收集此系列的卡片",
            color=color,
            timestamp=datetime.now(),
        )
        progress = completion_rate / 100.0
        bar_length = 10
        filled_length = min(bar_length, round(bar_length * progress))
        empty_length = bar_length - filled_length
        filled_char = "■"
        empty_char = "□"
        progress_bar = filled_char * filled_length + empty_char * empty_length
        embed.add_field(
            name="收集進度",
            value=f"{series_collection.collected_cards}/{series_collection.total_cards}張卡片（{completion_rate:.1f}%）\n{progress_bar}",
            inline=False,
        )
        if completion_rate == 100:
            embed.add_field(name="恭喜！", value="你已完成此系列的收集！", inline=False)
        elif completion_rate >= 75:
            embed.add_field(
                name="接近完成", value="繼續抽卡以完成此系列的收集", inline=False
            )
        elif completion_rate >= 50:
            embed.add_field(name="過半完成", value="已收集一半以上的卡片", inline=False)
        else:
            embed.add_field(
                name="收集提示", value="使用`/mws`命令查看其他系列", inline=False
            )
        embed.set_thumbnail(url=self.user.display_avatar.url)
        embed.set_footer(
            text=f"第{series_collection.collected_cards}/{series_collection.total_cards}張 • {datetime.now().strftime('%Y-%m-%d')}"
        )
        return embed

    def build_embed(
        self,
        series_list: List[str],
        series_collections: Dict[str, SeriesCollection],
        overall_completion_percentage: float,
        total_collected: int,
        total_all_cards: int,
        current_page: int,
        total_pages: int,
        total_series: int,
        pool_type: Optional[str] = None,
    ) -> discord.Embed:
        """創建系列列表分頁的Embed

        參數:
            series_list: 當前頁的系列名稱列表
            series_collections: 當前頁系列的收集數據
            overall_completion_percentage: 總體完成百分比
            total_collected: 總收集卡片數
            total_all_cards: 總卡片數
            current_page: 當前頁碼
            total_pages: 總頁數
            total_series: 總系列數
            pool_type: 卡池類型 (可選)

        返回:
            格式化後的Discord Embed對象
        """
        overall_indicator, overall_color = get_completion_indicator(
            overall_completion_percentage
        )
        title = f"{self.display_name}的卡片收藏 {overall_indicator} {overall_completion_percentage:.1f}%"
        if pool_type:
            from config.app_config import get_pool_type_names

            pool_name = get_pool_type_names().get(pool_type, pool_type.capitalize())
            title += f" [{pool_name}]"
        embed = self._create_base_embed(
            title=title,
            description=f"總體收集進度: {total_collected}/{total_all_cards}張卡片\n使用`/mws [系列名稱]`查看詳細收集情況",
            color=overall_color,
            timestamp=datetime.now(),
        )
        indicator_legend = "<:nocheck:1357796970160455892>未收集 | <a:check3:1365494496577458256>1-49% | <a:check4:1365494507163877457>50%+ | <a:check5:1365494516966228058>75%+ | <a:check2:1357795058782441766>100%"
        if embed.description:
            embed.description += "\n" + indicator_legend
        # ... 在 build_embed 函數中 ...
        if not series_list or len(series_list) == 0:
            if embed.description:
                embed.description += "\n暫無可用系列"
        else:
            # 以2為步長遍歷，相當於以「行」為單位處理
            for i in range(0, len(series_list), 2):
                # --- 處理第一欄 (永遠存在) ---
                series_name_1 = series_list[i]
                series_collection_1 = series_collections.get(series_name_1)
                if series_collection_1:
                    indicator_1, _ = get_completion_indicator(
                        series_collection_1.completion_rate
                    )
                    embed.add_field(
                        name=f"{indicator_1} {series_name_1}",
                        value=f"收集情況`{series_collection_1.collected_cards}/{series_collection_1.total_cards}`",
                        inline=True,
                    )
                else:
                    embed.add_field(
                        name=f"<:nocheck:1357796970160455892> {series_name_1}",
                        value="收集情況`0/0`",
                        inline=True,
                    )

                # --- 處理第二欄 (可能不存在) ---
                if (i + 1) < len(series_list):
                    # 如果存在第二個項目，正常添加
                    series_name_2 = series_list[i + 1]
                    series_collection_2 = series_collections.get(series_name_2)
                    if series_collection_2:
                        indicator_2, _ = get_completion_indicator(
                            series_collection_2.completion_rate
                        )
                        embed.add_field(
                            name=f"{indicator_2} {series_name_2}",
                            value=f"收集情況`{series_collection_2.collected_cards}/{series_collection_2.total_cards}`",
                            inline=True,
                        )
                    else:
                        embed.add_field(
                            name=f"<:nocheck:1357796970160455892> {series_name_2}",
                            value="收集情況`0/0`",
                            inline=True,
                        )
                else:
                    # 如果不存在（列表總數為奇數），則用空白欄位佔位，確保第二欄存在
                    embed.add_field(name="\u200b", value="\u200b", inline=True)

                # --- 關鍵：為每一行都添加第三個「看不見的」欄位來固定佈局 ---
                # 這會強制前兩個欄位靠左和居中，而不是分散在兩端。
                # 無論是不是最後一行，我們都添加它，以保證佈局一致！
                embed.add_field(name="\u200b", value="\u200b", inline=True)

        embed.set_footer(
            text=f"第{current_page}/{total_pages}頁 • 共{total_series}個系列"
        )
        return embed
