from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class TransactionType(str, Enum):
    """交易類型枚舉"""

    BUY = "BUY"
    SELL = "SELL"
    SHORT = "SHORT"
    COVER = "COVER"
    FORCED_COVER = "FORCED_COVER"
    DELISTED_SETTLEMENT = "DELISTED_SETTLEMENT"


class TradeHistory(BaseModel):
    """
    代表單筆交易歷史記錄的 Pydantic 模型。
    """

    id: int = Field(..., description="交易的唯一ID", alias="transaction_id")
    user_id: int = Field(..., description="用戶ID")
    asset_id: int = Field(..., description="資產ID")
    asset_symbol: str = Field(..., description="資產的交易代碼/符號")
    asset_name: str = Field(..., description="資產的名稱")
    transaction_type: TransactionType = Field(
        ...,
        description="交易類型 (BUY, SELL, SHORT, COVER, FORCED_COVER, DELISTED_SETTLEMENT)",
    )
    quantity: int = Field(..., description="交易數量")
    price_per_unit: Decimal = Field(..., description="每單位價格")
    total_amount: Decimal = Field(..., description="交易總額（不含手續費）")
    fee: Decimal = Field(..., description="交易手續費")
    timestamp: datetime = Field(..., description="交易時間戳")
    context: Optional[Dict[str, Any]] = Field(
        None, description="儲存交易上下文的JSON欄位"
    )

    class Config:
        from_attributes = True
        use_enum_values = True
