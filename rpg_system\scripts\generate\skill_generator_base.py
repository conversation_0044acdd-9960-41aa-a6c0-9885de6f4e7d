"""
基礎技能生成器

提供技能生成的通用邏輯和基礎功能，可被主動技能、被動技能和天賦技能生成器繼承。
"""

import json
import os
import random
import sys
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

# 添加項目根目錄到Python路徑
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

from utils.logger import logger

from .description_generator import DescriptionGenerator
from .rarity_manager import RarityManager
from .template_processor import TemplateProcessor


class SkillGeneratorBase(ABC):
    """基礎技能生成器，提供通用的技能生成邏輯"""

    def __init__(self):
        """初始化基礎技能生成器"""
        self._setup_paths()
        self._load_data()
        self._initialize_managers()

        # 初始化名稱統計字典
        self.names_by_rarity = {}

    def _setup_paths(self):
        """設置路徑"""
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
        self.output_dir = os.path.join(project_root, "rpg_system", "config", "data")
        os.makedirs(self.output_dir, exist_ok=True)

        # 腳本目錄（用於載入配置文件）- 現在指向 generate 目錄
        self.script_dir = script_dir

    def _load_data(self):
        """載入所有必要的數據文件"""
        # Load name library
        name_lib_path = os.path.join(
            self.script_dir, "config", "skill_name_library.json"
        )
        self.name_library = self._load_json_data(
            name_lib_path, "skill_name_library.json"
        )

        # Load and process effect templates from multiple files
        self._raw_effect_templates = self._load_effect_templates_from_directory()
        self._process_raw_effect_templates()

        # Load and process balance config
        balance_config_path = os.path.join(
            self.script_dir, "config", "balance_config.json"
        )
        raw_balance_config = self._load_json_data(
            balance_config_path, "balance_config.json"
        )
        self._process_balance_config(raw_balance_config)

        # Load skill descriptions
        skill_desc_path = os.path.join(
            self.script_dir, "config", "skill_descriptions.json"
        )
        self.skill_descriptions = self._load_json_data(
            skill_desc_path, "skill_descriptions.json"
        )

    def _initialize_managers(self):
        """初始化管理器"""
        # 創建稀有度管理器
        self.rarity_manager = RarityManager(self.balance)

        # 建立模板分類緩存
        self._build_template_category_cache()

        # 創建模板處理器
        self.template_processor = TemplateProcessor(
            self.effect_templates, self._raw_effect_templates, self.rarity_manager
        )

        # 創建描述生成器
        self.description_generator = DescriptionGenerator(
            self.skill_descriptions, self._raw_effect_templates, self.rarity_manager
        )

    def _build_template_category_cache(self):
        """建立模板分類緩存，避免重複查找"""
        self.template_category_cache = {}

        # 遍歷 config/skill_descriptions.json 建立映射表
        for category, templates in self.skill_descriptions.items():
            for template_name in templates.keys():
                if template_name != "default":  # 跳過 default 條目
                    self.template_category_cache[template_name] = category

        logger.info("建立了 %s 個模板的分類緩存", len(self.template_category_cache))

    def get_template_category(self, template_name: str) -> str:
        """從緩存中獲取模板分類

        Args:
            template_name: 模板名稱

        Returns:
            模板分類
        """
        # 優先從緩存查找
        if template_name in self.template_category_cache:
            return self.template_category_cache[template_name]

        # 如果緩存中沒有，使用後備邏輯並加入緩存
        category = self._fallback_category_detection(template_name)
        self.template_category_cache[template_name] = category
        return category

    def _fallback_category_detection(self, template_name: str) -> str:
        """後備分類檢測邏輯"""
        template_upper = template_name.upper()

        if any(
            keyword in template_upper
            for keyword in [
                "DAMAGE",
                "STRIKE",
                "HIT",
                "EXECUTE",
                "DRAIN",
                "PHYSICAL",
                "MAGICAL",
                "EXPLOSION",
            ]
        ):
            return "damage"
        elif "HEAL" in template_upper:
            return "heal"
        elif any(
            keyword in template_upper
            for keyword in [
                "BOOST",
                "SHIELD",
                "REGEN",
                "BLESSING",
                "INVINCIBLE",
                "LIFESTEAL",
            ]
        ):
            return "buff"
        elif any(
            keyword in template_upper
            for keyword in ["DEBUFF", "WEAKEN", "CURSE", "POISON", "BURN", "BLEED"]
        ):
            return "debuff"
        elif any(
            keyword in template_upper
            for keyword in ["STUN", "FREEZE", "SILENCE", "SLEEP", "FEAR"]
        ):
            return "control"
        else:
            return "special"

    def _load_json_data(self, file_path: str, file_description: str) -> Any:
        """載入JSON數據

        Args:
            file_path: 文件路徑
            file_description: 文件描述

        Returns:
            載入的數據
        """
        if not os.path.exists(file_path):
            logger.error("找不到 %s 文件於: %s", file_description, file_path)
            raise FileNotFoundError(f"{file_description} not found at {file_path}")
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            logger.info("成功從 %s 載入 %s", file_path, file_description)
            return data
        except json.JSONDecodeError:
            logger.error("解析 %s 文件失敗於 %s", file_description, file_path)
            raise
        except Exception as e:
            logger.error(
                "載入 %s 文件 (%s) 時發生未知錯誤: %s",
                file_description,
                file_path,
                e,
                exc_info=True,
            )
            raise

    def _load_effect_templates_from_directory(self) -> Dict[str, Any]:
        """從效果模板目錄載入所有效果模板文件"""
        effect_templates = {}
        templates_dir = os.path.join(self.output_dir, "effect_templates")

        if not os.path.exists(templates_dir):
            logger.error("效果模板目錄不存在: %s", templates_dir)
            return effect_templates

        # 自動掃描目錄下所有 JSON 文件
        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".json"):
                    template_path = os.path.join(templates_dir, filename)
                    try:
                        template_data = self._load_json_data(
                            template_path, f"效果模板文件 {filename}"
                        )
                        effect_templates.update(template_data)
                        logger.info("成功載入效果模板文件: %s", filename)
                    except Exception as e:
                        logger.error("載入效果模板文件失敗: %s, 錯誤: %s", filename, e)
                        # 繼續載入其他文件，不中斷整個過程
                        continue
        except Exception as e:
            logger.error("掃描效果模板目錄失敗: %s, 錯誤: %s", templates_dir, e)

        if not effect_templates:
            logger.error("沒有載入任何效果模板，請檢查 effect_templates 目錄中的文件")
        else:
            logger.info("總共載入了 %s 個效果模板", len(effect_templates))

        return effect_templates

    def _process_raw_effect_templates(self):
        """處理原始效果模板"""
        self.effect_templates: Dict[str, Dict[str, str]] = {
            "damage": {},
            "heal": {},
            "buff": {},
            "debuff": {},
            "control": {},
            "special": {},
        }
        if not self._raw_effect_templates:
            logger.error("原始效果模板 (_raw_effect_templates) 為空，無法處理。")
            return

        # 預處理：先嘗試將所有模板分類，不跳過任何模板
        for template_id, effect_data in self._raw_effect_templates.items():
            category = self._get_category_from_effect_type(template_id, effect_data)
            if category:
                subtype_key = template_id.lower()
                if category == "damage":
                    subtype_key = subtype_key.replace("_damage", "")
                elif category == "heal":
                    subtype_key = subtype_key.replace("basic_heal_", "")
                elif (
                    category == "buff" or category == "debuff" or category == "control"
                ):
                    subtype_key = (
                        subtype_key.replace("apply_", "")
                        .replace("_status", "")
                        .replace("stat_", "")
                    )

                original_subtype_key = subtype_key
                counter = 1
                while subtype_key in self.effect_templates[category]:
                    subtype_key = f"{original_subtype_key}_{counter}"
                    counter += 1

                if subtype_key != original_subtype_key and counter > 1:
                    logger.warning(
                        "模板子類型鍵名衝突，已將 '%s' 重命名為 '%s' (模板ID: %s)",
                        original_subtype_key,
                        subtype_key,
                        template_id,
                    )

                self.effect_templates[category][subtype_key] = template_id
            else:
                # 強制將無法分類的模板放入 special 類別，而不是跳過
                logger.warning(
                    "模板ID %s 無法自動分類，強制分類為 'special' 類別", template_id
                )

                subtype_key = template_id.lower()
                original_subtype_key = subtype_key
                counter = 1
                while subtype_key in self.effect_templates["special"]:
                    subtype_key = f"{original_subtype_key}_{counter}"
                    counter += 1

                self.effect_templates["special"][subtype_key] = template_id

        for cat, templates in self.effect_templates.items():
            if not templates:
                logger.warning(
                    "效果分類 '%s' 在從JSON加載後沒有任何模板。請檢查分類邏輯或 effect_templates.json 文件。",
                    cat,
                )
            else:
                logger.info("效果分類 '%s' 共有 %s 個模板。", cat, len(templates))

    def _process_balance_config(self, raw_balance_config: Dict[str, Any]):
        """處理平衡配置"""
        try:
            self.balance = {
                "rarity_multipliers": {
                    int(k): v
                    for k, v in raw_balance_config["rarity_multipliers"].items()
                },
                "max_levels": {
                    int(k): v for k, v in raw_balance_config["max_levels"].items()
                },
                "base_mp_costs": raw_balance_config["base_mp_costs"],
                "base_cooldowns": raw_balance_config["base_cooldowns"],
            }
        except KeyError as e:
            logger.error("balance_config.json 文件中缺少鍵：%s", e)
            raise
        except Exception as e:
            logger.error("處理 balance_config 時出錯: %s", e, exc_info=True)
            raise

    def _get_category_from_effect_type(
        self, template_id: str, effect_data: Dict[str, Any]
    ) -> Optional[str]:
        """根據 effect_type 和 status_effect_id 推斷效果分類

        Args:
            template_id: 模板ID
            effect_data: 效果數據

        Returns:
            效果分類，如果無法分類則返回None
        """
        effect_type = effect_data.get("effect_type")

        if effect_type == "DAMAGE":
            return "damage"
        elif effect_type == "HEAL":
            return "heal"
        elif effect_type == "APPLY_STATUS_EFFECT":
            status_id = effect_data.get("status_effect_id", "").upper()

            # 使用硬編碼的關鍵詞進行分類
            buff_keywords = [
                "BOOST",
                "INVINCIBLE",
                "REGEN",
                "REFLECT",
                "SHIELD",
                "BLESSING",
                "DEFENSE",
            ]
            debuff_keywords = [
                "POISON",
                "BURN",
                "BLEED",
                "CURSE",
                "WEAKEN",
                "VULNERABLE",
                "SLOW",
                "DEBUFF",
            ]
            control_keywords = [
                "STUN",
                "FREEZE",
                "SILENCE",
                "SLEEP",
                "FEAR",
                "TAUNT",
                "CONFUSE",
                "DISABLE",
            ]

            if any(kw in status_id for kw in buff_keywords):
                if "BERSERK" in status_id:
                    return "special"
                return "buff"
            elif any(kw in status_id for kw in debuff_keywords):
                return "debuff"
            elif any(kw in status_id for kw in control_keywords):
                return "control"
            elif "BERSERKER" in template_id.upper():
                return "special"
            logger.warning(
                "無法從 APPLY_STATUS_EFFECT 的 status_effect_id '%s' (模板ID: %s) 明確分類，默認為 'special'",
                status_id,
                template_id,
            )
            return "special"

        elif effect_type == "STAT_MODIFICATION":
            modifications = effect_data.get("modifications", [])
            if modifications:
                first_mod = modifications[0]
                val_from_value_field = first_mod.get("value")
                val_from_formula_field = first_mod.get("value_formula")
                numeric_val_to_check = None
                processed_as_numeric = False

                if isinstance(val_from_value_field, (int, float)):
                    numeric_val_to_check = val_from_value_field
                    processed_as_numeric = True
                elif isinstance(val_from_formula_field, str):
                    try:
                        numeric_val_to_check = float(val_from_formula_field)
                        processed_as_numeric = True
                    except ValueError:
                        pass

                if processed_as_numeric and numeric_val_to_check is not None:
                    if numeric_val_to_check > 0:
                        return "buff"
                    elif numeric_val_to_check < 0:
                        return "debuff"

            logger.warning(
                "STAT_MODIFICATION 模板 %s 無法明確確定是 buff 還是 debuff，默認為 'buff'",
                template_id,
            )
            return "buff"
        elif effect_type == "DISPEL_DEBUFF":
            return "special"
        elif effect_type == "SHIELD_REMOVAL":
            return "special"
        # 新增對其他效果類型的處理
        elif effect_type == "APPLY_SHIELD" or "SHIELD" in template_id.upper():
            logger.info("自動分類護盾效果 %s 為 'buff' 類別", template_id)
            return "buff"
        elif effect_type == "LOSE_MP":
            logger.info("自動分類 MP 損失效果 %s 為 'debuff' 類別", template_id)
            return "debuff"
        elif effect_type == "TRANSFER_MP":
            logger.info("自動分類 MP 轉移效果 %s 為 'special' 類別", template_id)
            return "special"
        elif "COUNTER" in template_id.upper() or "REFLECT" in template_id.upper():
            logger.info("根據名稱模式將 %s 分類為 'special' 類別", template_id)
            return "special"
        elif "HEAL" in template_id.upper() or "REGEN" in template_id.upper():
            logger.info("根據名稱模式將 %s 分類為 'heal' 類別", template_id)
            return "heal"
        elif "DAMAGE" in template_id.upper() or "STRIKE" in template_id.upper():
            logger.info("根據名稱模式將 %s 分類為 'damage' 類別", template_id)
            return "damage"
        elif "BUFF" in template_id.upper() or "BOOST" in template_id.upper():
            logger.info("根據名稱模式將 %s 分類為 'buff' 類別", template_id)
            return "buff"
        elif "DEBUFF" in template_id.upper() or "WEAKEN" in template_id.upper():
            logger.info("根據名稱模式將 %s 分類為 'debuff' 類別", template_id)
            return "debuff"
        else:
            logger.warning(
                "未知的 effect_type '%s' 對於模板ID %s，基於名稱嘗試分類",
                effect_type,
                template_id,
            )
            # 基於模板ID嘗試分類
            template_id_upper = template_id.upper()
            if "SHIELD" in template_id_upper or "BARRIER" in template_id_upper:
                logger.info("根據名稱將 %s 分類為 'buff' 類別", template_id)
                return "buff"
            elif "MP" in template_id_upper and (
                "DRAIN" in template_id_upper or "LOSE" in template_id_upper
            ):
                logger.info("根據名稱將 %s 分類為 'debuff' 類別", template_id)
                return "debuff"
            elif "SPECIAL" in template_id_upper or "UNIQUE" in template_id_upper:
                logger.info("根據名稱將 %s 分類為 'special' 類別", template_id)
                return "special"

            logger.warning("無法分類效果 %s，默認為 'special' 類別", template_id)
            return "special"

    def _set_seed(self, seed_str: str):
        """設置確定性種子

        Args:
            seed_str: 種子字符串
        """
        random.seed(hash(seed_str))

    def _get_action_type(self, effect_category: str) -> str:
        """根據效果分類獲取動作類型

        Args:
            effect_category: 效果分類

        Returns:
            動作類型
        """
        mapping = {
            "damage": "strike",
            "heal": "support",
            "buff": "magic",
            "debuff": "magic",
            "control": "control",
            "special": "magic",
        }
        return mapping.get(effect_category, "strike")

    def _get_target_type(self, effect_category: str) -> str:
        """根據效果分類獲取目標類型

        Args:
            effect_category: 效果分類

        Returns:
            目標類型
        """
        mapping = {
            "damage": "ENEMY_SINGLE",
            "heal": "ALLY_SINGLE",
            "buff": "SELF",
            "debuff": "ENEMY_SINGLE",
            "control": "ENEMY_SINGLE",
            "special": "ENEMY_SINGLE",
        }
        return mapping.get(effect_category, "ENEMY_SINGLE")

    @abstractmethod
    def generate_skill_config(self, rarity: int) -> Dict[str, Any]:
        """生成技能配置（抽象方法，由子類實現）

        Args:
            rarity: 稀有度等級

        Returns:
            技能配置字典
        """
        pass

    @abstractmethod
    def generate_all_skills(
        self, skills_per_rarity: int = 5
    ) -> Dict[str, Dict[str, Any]]:
        """生成所有稀有度的技能配置（抽象方法，由子類實現）

        Args:
            skills_per_rarity: 每個稀有度生成的技能數量

        Returns:
            所有技能配置字典
        """
        pass

    @abstractmethod
    def save_skills_to_files(self, all_skills: Dict[str, Dict[str, Any]]):
        """保存所有技能到配置文件（抽象方法，由子類實現）

        Args:
            all_skills: 所有技能配置字典
        """
        pass
