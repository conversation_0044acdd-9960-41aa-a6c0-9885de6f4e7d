"""
Pioneer System Action Module
負責處理所有與玩家動作相關的業務邏輯。
"""

from typing import Any, Dict, List

from database.postgresql.async_manager import get_pool
from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.exceptions import (
    PioneerActionError,
    PioneerInsufficientItemsError,
    PioneerNotFoundError,
    RequirementsNotMetError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

# 處理器延遲導入
processors = {}


async def _initialize_processors():
    """延遲初始化處理器以避免循環導入"""
    global processors
    if processors:
        return

    if game_data is None:
        raise PioneerActionError("GameDataLoader 尚未初始化")

    try:
        from pioneer.core.processors.craft_processor import CraftProcessor
        from pioneer.core.processors.earnings_processor import EarningsProcessor
        from pioneer.core.processors.facility_processor import FacilityProcessor
        from pioneer.core.processors.gather_processor import GatherProcessor
        from pioneer.core.processors.market_processor import MarketProcessor
        from pioneer.core.processors.research_processor import ResearchProcessor

        processors = {
            "gather": GatherProcessor(game_data, repositories),
            "craft": CraftProcessor(game_data, repositories),
            "build_facility": FacilityProcessor(game_data, repositories),
            "upgrade_facility": FacilityProcessor(game_data, repositories),
            "research": ResearchProcessor(game_data, repositories),
            "collect_earnings": EarningsProcessor(repositories, game_data),
            "check_earnings": EarningsProcessor(repositories, game_data),
            "facility_sell": FacilityProcessor(game_data, repositories),
            "sell": FacilityProcessor(game_data, repositories),
            "sell_resource": MarketProcessor(game_data, repositories),
        }
        logger.info("Action Module 處理器初始化完成")

    except ImportError as e:
        logger.error("處理器導入失敗: %s", e)
        raise PioneerActionError(f"處理器初始化失敗: {e}") from e


async def execute_action(
    user_id: int, action_id: str, params: Dict[str, Any] | None = None
) -> ActionResult:
    """
    執行任意動作的核心模組函數。
    """
    if params is None:
        params = {}

    try:
        await _initialize_processors()

        if game_data is None:
            raise PioneerActionError(f"{action_id}: 遊戲數據未初始化")

        action_config = game_data.get_action_config(action_id)
        if not action_config:
            raise PioneerNotFoundError(f"動作: {action_id}")

        processor = processors.get(action_config.type)
        if not processor:
            raise PioneerActionError(f"{action_id}: 未找到處理器: {action_config.type}")

        return await _execute_with_processor(user_id, action_config, params, processor)

    except Exception as e:
        logger.error("動作執行失敗 %s for user %s: %s", action_id, user_id, e)
        if isinstance(e, (PioneerActionError, PioneerNotFoundError)):
            raise
        return ActionResult.failed_result(f"動作執行失敗: {str(e)}", "EXECUTION_ERROR")


async def _execute_with_processor(
    user_id: int, action_config: ActionConfig, params: Dict[str, Any], processor
) -> ActionResult:
    """使用指定處理器執行動作（支持資料庫交易）"""
    pool = get_pool()
    if not pool:
        raise PioneerActionError("資料庫連接池未初始化")
    async with pool.acquire() as conn:
        async with conn.transaction():
            try:
                # 1. 檢查前置條件
                from pioneer.utils.requirement_checker import (
                    check_requirements as check_reqs,
                )

                if not game_data:
                    raise PioneerActionError("遊戲數據未初始化")

                if action_config.requirements:
                    await check_reqs(
                        user_id,
                        action_config.requirements,
                        game_data,
                        repositories,
                        conn,
                    )

                # 2. 檢查輸入資源
                if action_config.inputs:
                    await _check_input_resources(user_id, action_config.inputs, conn)

                # 3. 執行動作邏輯
                if hasattr(processor.repository, "set_connection"):
                    processor.repository.set_connection(conn)

                try:
                    result = await processor.execute(user_id, action_config, params)

                    # 4. 如果成功，消耗資源和能量
                    if result.success:
                        if action_config.energy_cost > 0:
                            energy_consumed = await repositories.consume_energy(
                                user_id, action_config.energy_cost, conn
                            )
                            if not energy_consumed:
                                raise PioneerInsufficientItemsError(
                                    f"能量不足，需要 {action_config.energy_cost}"
                                )
                        await _consume_input_resources(user_id, action_config, conn)

                    return result

                finally:
                    if hasattr(processor.repository, "clear_connection"):
                        processor.repository.clear_connection()

            except (RequirementsNotMetError, PioneerInsufficientItemsError) as e:
                # 這些是預期內的業務邏輯錯誤，直接向上拋出
                raise PioneerActionError(str(e)) from e
            except Exception as e:
                logger.error("處理器在交易中執行失敗: %s", e)
                raise PioneerActionError(f"處理器執行失敗: {str(e)}") from e


async def _check_input_resources(
    user_id: int, inputs: List[Dict[str, Any]], conn=None
) -> None:
    """檢查輸入資源是否足夠，不足時拋出 PioneerInsufficientItemsError"""
    for input_item in inputs:
        item_id = input_item.get("item_id")
        if not isinstance(item_id, str):
            continue
        quantity = input_item.get("quantity", 1)
        warehouse_item = await repositories.get_warehouse_item(user_id, item_id, conn)
        if not warehouse_item or warehouse_item.quantity < quantity:
            available = warehouse_item.quantity if warehouse_item else 0
            item_name = game_data.get_item_name(item_id) if game_data else item_id
            raise PioneerInsufficientItemsError(
                f"材料 {item_name} 數量不足，需要 {quantity}，可用 {available}"
            )


async def _consume_input_resources(
    user_id: int, action_config: ActionConfig, conn=None
):
    """消耗輸入材料"""
    if not action_config.inputs:
        return
    for input_item in action_config.inputs:
        item_id = input_item.get("item_id")
        if not isinstance(item_id, str):
            continue
        quantity = input_item.get("quantity", 1)
        consumed = await repositories.consume_warehouse_item(
            user_id, item_id, quantity, conn
        )
        if not consumed:
            item_name = game_data.get_item_name(item_id) if game_data else item_id
            raise PioneerInsufficientItemsError(
                f"材料 {item_name} 消耗失敗，可能在執行過程中數量發生變化。"
            )
