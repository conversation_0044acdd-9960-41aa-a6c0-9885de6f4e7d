"""
Pioneer System - Warehouse Repository
倉庫相關的資料庫存取
"""

from typing import List, Optional

import asyncpg

from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)
from pioneer.exceptions import PioneerDatabaseError
from pioneer.models.pioneer_models import WarehouseItem
from utils.logger import logger

# ========================================
# 倉庫相關
# ========================================


async def get_warehouse_item(
    user_id: int, item_id: str, connection: Optional[asyncpg.Connection] = None
) -> Optional[WarehouseItem]:
    """獲取倉庫物品"""
    query = "SELECT * FROM pioneer_warehouse WHERE user_id = $1 AND item_id = $2"
    result = await fetch_one(query, (user_id, item_id), connection=connection)

    if not result:
        return None

    return WarehouseItem(
        id=result["id"],
        user_id=result["user_id"],
        item_id=result["item_id"],
        quantity=result["quantity"],
    )


async def get_item_quantity(
    user_id: int, item_id: str, connection: Optional[asyncpg.Connection] = None
) -> int:
    """安全地獲取倉庫中單個物品的數量，如果物品不存在則返回 0。"""
    item = await get_warehouse_item(user_id, item_id, connection)
    return item.quantity if item else 0


async def add_warehouse_item(
    user_id: int,
    item_id: str,
    quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """增加倉庫物品（帶容量檢查）"""
    try:
        # 使用倉庫容量管理器進行容量檢查
        from pioneer.modules.warehouse_capacity_manager import (
            validate_warehouse_capacity,
        )

        await validate_warehouse_capacity(user_id, item_id, quantity, connection)

        query = """
            INSERT INTO pioneer_warehouse (user_id, item_id, quantity)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, item_id)
            DO UPDATE SET quantity = pioneer_warehouse.quantity + $3
        """
        await execute_query(query, (user_id, item_id, quantity), connection=connection)

        # 觸發任務更新
        from pioneer.services.task_updater import task_updater

        await task_updater.check_and_update_tasks(
            user_id, "gather", item_id=item_id, amount=quantity, connection=connection
        )

        return True
    except Exception as e:
        logger.error("增加倉庫物品失敗 user_id=%s, item_id=%s: %s", user_id, item_id, e)
        if isinstance(e, PioneerDatabaseError):
            raise
        raise PioneerDatabaseError("add_warehouse_item", str(e), e) from e


async def consume_warehouse_item(
    user_id: int,
    item_id: str,
    quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """消耗倉庫物品"""
    query = """
        UPDATE pioneer_warehouse 
        SET quantity = quantity - $3
        WHERE user_id = $1 AND item_id = $2 AND quantity >= $3
    """
    try:
        result = await execute_query(
            query, (user_id, item_id, quantity), connection=connection
        )

        # 如果數量變為0，刪除記錄
        if result > 0:
            cleanup_query = "DELETE FROM pioneer_warehouse WHERE user_id = $1 AND item_id = $2 AND quantity <= 0"
            await execute_query(
                cleanup_query, (user_id, item_id), connection=connection
            )
            return True
        return False
    except Exception as e:
        logger.error("消耗倉庫物品失敗 user_id=%s, item_id=%s: %s", user_id, item_id, e)
        raise PioneerDatabaseError("consume_warehouse_item", str(e), e) from e


async def get_user_warehouse(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[WarehouseItem]:
    """獲取用戶倉庫所有物品"""
    query = "SELECT * FROM pioneer_warehouse WHERE user_id = $1 ORDER BY item_id"
    results = await fetch_all(query, (user_id,), connection=connection)

    return [
        WarehouseItem(
            id=row["id"],
            user_id=row["user_id"],
            item_id=row["item_id"],
            quantity=row["quantity"],
        )
        for row in results
    ]


async def get_warehouse_total_quantity(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """獲取倉庫物品總數量"""
    query = (
        "SELECT COALESCE(SUM(quantity), 0) FROM pioneer_warehouse WHERE user_id = $1"
    )
    try:
        result = await fetch_value(query, (user_id,), connection=connection)
        return int(result) if result else 0
    except Exception as e:
        logger.error("獲取倉庫總數量失敗 user_id=%s: %s", user_id, e)
        return 0


# 使用倉庫容量管理器的便利函數
async def calculate_warehouse_capacity(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """計算用戶倉庫總容量（便利函數）"""
    from pioneer.modules.warehouse_capacity_manager import (
        calculate_warehouse_capacity as calc_capacity,
    )

    return await calc_capacity(user_id, connection)
