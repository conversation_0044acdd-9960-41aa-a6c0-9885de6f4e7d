# Pioneer System 研究項目配置
# 工坊研究室的無限升級項目

# 生產效率研究
production_efficiency:
  name: "生產效率優化"
  description: "提升所有設施的生產效率"
  effect_per_level: 0.02  # 每級提升2%效率
  cost_formula: "1000000 * (2.5 ** level)"  # 指數增長成本
  max_level: null  # 無限制
  currency: "oil"  # 使用油幣

# 採集精通研究
gathering_mastery:
  name: "採集技術精通"
  description: "提升手動採集的產出和經驗獲得"
  effect_per_level: 0.015  # 每級提升1.5%
  cost_formula: "800000 * (2.3 ** level)"
  max_level: null
  currency: "oil"

# 經濟優化研究
economic_optimization:
  name: "經濟系統優化"
  description: "提升商店銷售價格和銷售速度"
  effect_per_level: 0.01  # 每級提升1%
  cost_formula: "1500000 * (2.8 ** level)"
  max_level: null
  currency: "oil"

# 能量管理研究
energy_management:
  name: "能量管理系統"
  description: "減少動作的能量消耗"
  effect_per_level: 0.01  # 每級減少1%能量消耗
  cost_formula: "1200000 * (2.4 ** level)"
  max_level: 50  # 最大50級 (50%減少)
  currency: "oil"

# 技能加速研究
skill_acceleration:
  name: "技能學習加速"
  description: "提升所有技能的經驗獲得速度"
  effect_per_level: 0.025  # 每級提升2.5%經驗
  cost_formula: "2000000 * (3.0 ** level)"
  max_level: null
  currency: "oil"

# 自動化技術研究
automation_technology:
  name: "自動化技術"
  description: "降低設施自動化升級的成本"
  effect_per_level: 0.02  # 每級降低2%成本
  cost_formula: "3000000 * (2.6 ** level)"
  max_level: 40  # 最大40級 (80%降低)
  currency: "oil"

# 資源保存研究
resource_preservation:
  name: "資源保存技術"
  description: "減少製作過程中的材料損失"
  effect_per_level: 0.005  # 每級減少0.5%材料消耗
  cost_formula: "1800000 * (2.7 ** level)"
  max_level: 20  # 最大20級 (10%減少)
  currency: "oil"

# 品質控制研究
quality_control:
  name: "品質控制系統"
  description: "提升製作物品的品質和價值"
  effect_per_level: 0.01  # 每級提升1%品質
  cost_formula: "2500000 * (2.9 ** level)"
  max_level: null
  currency: "oil"

# 市場分析研究
market_analysis:
  name: "市場分析系統"
  description: "提升商店NPC的購買頻率"
  effect_per_level: 0.015  # 每級提升1.5%購買頻率
  cost_formula: "2200000 * (2.8 ** level)"
  max_level: null
  currency: "oil"



# 基礎研究項目 (較便宜但效果較小)
basic_efficiency:
  name: "基礎效率改進"
  description: "小幅提升生產效率"
  effect_per_level: 0.005  # 每級提升0.5%
  cost_formula: "10000 * (1.8 ** level)"
  max_level: 50
  currency: "oil"

basic_gathering:
  name: "基礎採集改進"
  description: "小幅提升採集效率"
  effect_per_level: 0.003  # 每級提升0.3%
  cost_formula: "8000 * (1.7 ** level)"
  max_level: 50
  currency: "oil"

basic_crafting:
  name: "基礎製作改進"
  description: "小幅提升製作效率"
  effect_per_level: 0.004  # 每級提升0.4%
  cost_formula: "12000 * (1.9 ** level)"
  max_level: 50
  currency: "oil"
