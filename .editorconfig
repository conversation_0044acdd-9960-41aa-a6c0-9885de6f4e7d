# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Python files
[*.py]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 88

# Markdown files
[*.md]
trim_trailing_whitespace = false

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# HTML files
[*.{html,htm}]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# JavaScript files
[*.js]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
end_of_line = lf 