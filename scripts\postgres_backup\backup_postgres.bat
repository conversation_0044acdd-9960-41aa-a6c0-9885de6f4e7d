@echo off
SETLOCAL ENABLEDELAYEDEXPANSION

REM --- 用户配置 START ---
REM ** 请务必修改以下配置项 **
SET PGUSER=postgres
SET PGPASSWORD=26015792 REM 强烈建议: 出于安全考虑, 请配置并使用 .pgpass 文件代替此处直接写入密码
SET PGDATABASE=gacha_database
SET PGHOST=127.0.0.1
SET PGPORT=5432
REM 修改为你的 pg_dump.exe 实际路径 (例如 C:\Program Files\PostgreSQL\15\bin\pg_dump.exe)
SET PGDUMP_PATH="C:\Program Files\PostgreSQL\17\bin\pg_dump.exe"
REM 如果 python 不在 PATH, 请指定完整路径, 例如 "C:\Python39\python.exe"
SET PYTHON_EXE="python"

REM 假设此脚本位于 <项目根目录>/scripts/postgres_backup/
SET SCRIPT_DIR=%~dp0
SET PROJECT_ROOT=%SCRIPT_DIR%..\..
REM 备份文件存放目录
SET BACKUP_DIR=%PROJECT_ROOT%\database\backups\postgresql
REM Python 脚本的路径 (与此批处理文件同目录)
SET PYTHON_SCRIPT_PATH="%SCRIPT_DIR%manage_backups.py"
SET MAX_BACKUPS=72
SET BACKUP_FILE_PATTERN="backup_*.dump"
REM --- 用户配置 END ---

ECHO Debug: Current PGDUMP_PATH is [%PGDUMP_PATH%]
REM 检查 PGDUMP_PATH 是否有效
IF NOT EXIST %PGDUMP_PATH% (
    ECHO ERROR: pg_dump.exe not found at %PGDUMP_PATH%. Please check PGDUMP_PATH setting.
    EXIT /B 1
)

REM 检查 PYTHON_SCRIPT_PATH 是否有效
IF NOT EXIST %PYTHON_SCRIPT_PATH% (
    ECHO ERROR: manage_backups.py not found at %PYTHON_SCRIPT_PATH%.
    ECHO Make sure 'manage_backups.py' is in the same directory as this batch script.
    EXIT /B 1
)

REM 生成带时间戳的文件名 (YYYYMMDD_HHMMSS)
FOR /F "tokens=2 delims==" %%I IN ('wmic os get LocalDateTime /value') DO SET CT=%%I
SET YEAR=%CT:~0,4%
SET MONTH=%CT:~4,2%
SET DAY=%CT:~6,2%
SET HOUR=%CT:~8,2%
SET MINUTE=%CT:~10,2%
SET SECOND=%CT:~12,2%
SET TIMESTAMP=%YEAR%%MONTH%%DAY%_%HOUR%%MINUTE%%SECOND%
REM 替换 wmic 可能输出的空格为0 (例如 ' 9' -> '09')
SET HOUR=0%HOUR%
SET HOUR=%HOUR:~-2%
SET MINUTE=0%MINUTE%
SET MINUTE=%MINUTE:~-2%
SET SECOND=0%SECOND%
SET SECOND=%SECOND:~-2%
SET TIMESTAMP=%YEAR%%MONTH%%DAY%_%HOUR%%MINUTE%%SECOND%


SET BACKUP_FILENAME=backup_%TIMESTAMP%.dump
SET BACKUP_FULL_PATH="%BACKUP_DIR%\%BACKUP_FILENAME%"

REM 确保备份目录存在
IF NOT EXIST "%BACKUP_DIR%" (
    ECHO Creating backup directory: "%BACKUP_DIR%"
    MKDIR "%BACKUP_DIR%"
    IF !ERRORLEVEL! NEQ 0 (
        ECHO FAILED to create backup directory: "%BACKUP_DIR%". Check permissions.
        EXIT /B 1
    )
)

ECHO Starting PostgreSQL backup for database '%PGDATABASE%' to %BACKUP_FULL_PATH% ...
%PGDUMP_PATH% -U %PGUSER% -h %PGHOST% -p %PGPORT% -d %PGDATABASE% -F c -f %BACKUP_FULL_PATH%

IF !ERRORLEVEL! NEQ 0 (
    ECHO Backup FAILED! Errorlevel: !ERRORLEVEL!
    ECHO Please check PostgreSQL credentials, pg_dump path, and database connectivity.
    ECHO Ensure the user %PGUSER% has necessary permissions.
    ECHO Also, ensure you are not using PGPASSWORD directly in the script in a production environment. Use .pgpass file instead.
    EXIT /B !ERRORLEVEL!
) ELSE (
    ECHO Backup successful: %BACKUP_FULL_PATH%
)

ECHO Running backup retention policy in %BACKUP_DIR% ...
%PYTHON_EXE% %PYTHON_SCRIPT_PATH% %BACKUP_DIR% %MAX_BACKUPS% %BACKUP_FILE_PATTERN%

IF !ERRORLEVEL! NEQ 0 (
    ECHO Retention policy script FAILED! Errorlevel: !ERRORLEVEL!
    ECHO Please check the Python script path and its execution.
) ELSE (
    ECHO Retention policy script completed.
)

ECHO Backup process finished.
ENDLOCAL
EXIT /B 0 