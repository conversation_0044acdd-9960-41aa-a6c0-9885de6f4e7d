from typing import TYPE_CHECKING, List, Optional

import discord
from discord.ui import Button

from gacha.exceptions import BusinessError
from gacha.models.models import Card
from gacha.models.shop_models import ExchangeSessionData
from gacha.repositories.card import master_card_repository
from gacha.services import shop_service
from gacha.views.embeds.shop.specific_ticket_selection_embed_builder import (
    build_specific_ticket_selection_embed,
)
from gacha.views.modals.card_search_modal import CardSearchModal
from utils.logger import logger

if TYPE_CHECKING:
    pass
from gacha.views.collection.collection_view.base_pagination import (
    BasePaginationView,
)
from gacha.views.shop.specific_ticket_result_view import SpecificTicketResultView
from utils.response_embeds import SuccessEmbed

if TYPE_CHECKING:
    from utils.error_handler import BotType


class SpecificTicketSelectionView(BasePaginationView):
    SEARCH_CARDS_CUSTOM_ID = "search_cards_specific"
    SELECT_CARD_PREFIX_CUSTOM_ID = "select_card_specific_"
    UNSELECT_CARD_CUSTOM_ID = "unselect_current_card_specific"
    CONFIRM_EXCHANGE_CUSTOM_ID = "confirm_exchange_specific"
    CANCEL_SELECTION_CUSTOM_ID = "cancel_selection_specific"

    def __init__(
        self,
        bot: "BotType",
        original_interaction: discord.Interaction,
        session_id: str,
        initial_session_data: ExchangeSessionData,
        items_per_page: int = 1,
        *,
        timeout: Optional[float] = 300.0,
    ):
        self.original_interaction = original_interaction
        self.user_id = original_interaction.user.id
        self.session_id = session_id
        self.session_data = initial_session_data
        self.search_query: Optional[str] = None
        self.items_per_page = items_per_page
        self.current_page_cards: List[Card] = []
        current_page = 1
        total_pages = 1
        super().__init__(
            bot=bot,
            user_id=original_interaction.user.id,
            current_page=current_page,
            total_pages=total_pages,
            timeout=timeout,
        )
        self._add_custom_action_buttons()

    def _add_custom_action_buttons(self):
        search_button = Button(
            label="🔍 搜尋 (ID/名稱)",
            custom_id=self.SEARCH_CARDS_CUSTOM_ID,
            style=discord.ButtonStyle.blurple,
            row=1,
        )
        search_button.callback = self._search_button_callback
        self.add_item(search_button)
        confirm_exchange_button = Button(
            label="✅ 確認兌換",
            custom_id=self.CONFIRM_EXCHANGE_CUSTOM_ID,
            style=discord.ButtonStyle.success,
            row=2,
        )
        confirm_exchange_button.callback = self._confirm_exchange_button_callback
        self.add_item(confirm_exchange_button)
        cancel_button = Button(
            label="❌ 取消兌換",
            custom_id=self.CANCEL_SELECTION_CUSTOM_ID,
            style=discord.ButtonStyle.danger,
            row=2,
        )
        cancel_button.callback = self._cancel_button_callback
        self.add_item(cancel_button)
        self._update_select_unselect_buttons()

    def _update_select_unselect_buttons(self):
        self.remove_item_by_custom_id(f"{self.SELECT_CARD_PREFIX_CUSTOM_ID}.*")
        self.remove_item_by_custom_id(self.UNSELECT_CARD_CUSTOM_ID)
        if self.current_page_cards:
            card = self.current_page_cards[0]
            select_button = Button(
                label="➡️ 選擇此卡",
                custom_id=f"{self.SELECT_CARD_PREFIX_CUSTOM_ID}{card.card_id}",
                style=discord.ButtonStyle.primary,
                row=1,
            )
            select_button.callback = self._card_button_callback
            self.add_item(select_button)
            unselect_current_card_button = Button(
                label="↩️ 取消選擇此卡",
                custom_id=self.UNSELECT_CARD_CUSTOM_ID,
                style=discord.ButtonStyle.secondary,
                row=1,
                disabled=True,
            )
            if self.session_data and any(
                (
                    sel_card_id == card.card_id
                    for sel_card_id in self.session_data.pending_selected_cards
                )
            ):
                unselect_current_card_button.disabled = False
            unselect_current_card_button.callback = (
                self._unselect_current_card_button_callback
            )
            self.add_item(unselect_current_card_button)
        else:
            pass

    def remove_item_by_custom_id(self, custom_id_pattern: str):
        import re

        is_regex = custom_id_pattern.endswith(".*")
        if is_regex:
            pattern = re.compile(custom_id_pattern.replace(".*", ".*"))
        items_to_remove = []
        pattern = None
        if is_regex:
            pattern = re.compile(custom_id_pattern.replace(".*", ".*"))
        items_to_remove = []
        for item in self.children:
            if isinstance(item, Button) and item.custom_id:
                if is_regex:
                    if pattern and pattern.fullmatch(item.custom_id):
                        items_to_remove.append(item)
                elif item.custom_id == custom_id_pattern:
                    items_to_remove.append(item)
        for item in items_to_remove:
            self.remove_item(item)

    async def refresh_page_data(
        self, interaction: discord.Interaction, is_initial_call: bool = False
    ):
        """Fetches data for the current page, updates view state, and rebuilds UI if necessary."""
        logger.debug(
            "SpecificTicketSelectionView: Refreshing page data for page %s, session %s, search: '%s'",
            self.current_page,
            self.session_id,
            self.search_query,
        )
        rebuild_components = False
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        updated_data = await shop_service.get_specific_ticket_card_page(
            session_id=self.session_id, page_number=self.current_page
        )
        # 重構後的直接返回卡片數據而非包含status的字典
        page_data = updated_data
        card_list = page_data.get("cards", []) if page_data else []
        self.current_page_cards = card_list if isinstance(card_list, list) else []
        pagination_info = page_data.get("pagination", {}) if page_data else {}
        new_total_pages = pagination_info.get("total_pages", 1)
        if is_initial_call or new_total_pages != self.total_pages:
            self.total_pages = new_total_pages
            self.current_page = pagination_info.get("current_page", self.current_page)
            rebuild_components = True
        raw_session_data = page_data.get("session_data")
        if raw_session_data:
            if isinstance(raw_session_data, dict):
                self.session_data = ExchangeSessionData(**raw_session_data)
            elif isinstance(raw_session_data, ExchangeSessionData):
                self.session_data = raw_session_data

        if rebuild_components:
            self.clear_items()
            self.add_pagination_buttons()
            self._add_custom_action_buttons()
        self._update_select_unselect_buttons()
        self._refresh_button_states()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """Overrides BasePaginationView._update_page to refresh data and edit the message."""
        self.current_page = page
        await self.refresh_page_data(interaction)
        embed = await self.get_current_page_embed()
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # discord.HTTPException 會被全域處理器捕捉並靜默記錄
        await interaction.response.edit_message(embed=embed, view=self)

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的嵌入式視圖。"""
        cards_for_embed = []
        if self.current_page_cards and len(self.current_page_cards) > 0:
            cards_for_embed = self.current_page_cards
        pending_selected_card_objects: List[Card] = []
        if self.session_data and self.session_data.pending_selected_cards:
            # 根據規範，移除 try...except，讓錯誤向上冒泡
            # 如果 get_card 失敗，它會拋出異常，由全域處理器處理
            card_ids = self.session_data.pending_selected_cards
            # 使用正確的函數名 get_cards_details_by_ids
            card_objects = await master_card_repository.get_cards_details_by_ids(
                card_ids
            )

            # 建立一個 ID 到卡片對象的映射以便查找
            card_map = {card.card_id: card for card in card_objects}

            for card_id in card_ids:
                card_object = card_map.get(card_id)
                if card_object:
                    pending_selected_card_objects.append(card_object)
                else:
                    logger.warning(
                        "Could not find card with ID %s while building embed for session %s",
                        card_id,
                        self.session_id,
                    )
        return build_specific_ticket_selection_embed(
            interaction=self.original_interaction,
            session_data=self.session_data,
            cards_on_page=cards_for_embed,
            pending_selected_card_objects=pending_selected_card_objects,
            current_page=self.current_page,
            total_pages=self.total_pages,
            search_query=self.search_query,
        )

    async def send_initial_message(self, interaction: discord.Interaction):
        if not self.current_page_cards and self.total_pages > 0:
            await self._update_page(self.current_page, interaction)
        embed = await self.get_current_page_embed()
        return (embed, self)

    async def prepare_initial_message_payload(
        self, interaction_for_initial_data_fetch: discord.Interaction
    ) -> tuple[discord.Embed, "SpecificTicketSelectionView"]:
        self.current_page = 1
        await self.refresh_page_data(
            interaction_for_initial_data_fetch, is_initial_call=True
        )
        embed = await self.get_current_page_embed()
        return (embed, self)

    async def _search_button_callback(self, interaction: discord.Interaction):
        search_modal = CardSearchModal(bot=self.bot, parent_view=self)
        await interaction.response.send_modal(search_modal)

    async def handle_search_submit(
        self, interaction: discord.Interaction, search_query: str
    ):
        """處理用戶的卡片搜索提交。"""
        self.search_query = search_query
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        result = await shop_service.find_target_page_for_specific_exchange(
            session_id=self.session_id, search_query=search_query
        )
        target_page = result.get("target_page")

        if target_page is None:
            raise BusinessError(f"找不到匹配 '{search_query}' 的卡片。")

        self.current_page = target_page
        logger.info(
            "Search query '%s' found target card on page %s for session %s",
            search_query,
            target_page,
            self.session_id,
        )

        await interaction.response.defer(ephemeral=True)

        await self.refresh_page_data(interaction)
        embed = await self.get_current_page_embed()
        # 直接編輯訊息，Discord 異常會拋到 bot.py 處理
        await interaction.edit_original_response(embed=embed, view=self)

    async def _card_button_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        if not interaction.data or "custom_id" not in interaction.data:
            return
        selected_card_id_str = interaction.data["custom_id"].replace(
            self.SELECT_CARD_PREFIX_CUSTOM_ID, ""
        )
        selected_card_id = int(selected_card_id_str)

        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        result = await shop_service.process_specific_ticket_selection(
            session_id=self.session_id, selected_card_id=selected_card_id
        )
        logger.debug("Card selection result: %s", result)
        # 重構後的返回不再包含status字段
        # message = result.get("message")  # Currently unused
        # next_step = result.get("next_step")  # Currently unused

        # 檢查是否有新的會話數據
        session_data_summary = result.get("session_data_summary")
        if session_data_summary:
            # 更新當前會話數據
            await self.refresh_page_data(interaction)

        final_embed = await self.get_current_page_embed()
        # 直接編輯訊息，Discord 異常會拋到 bot.py 處理
        content = None
        await interaction.edit_original_response(
            content=content, embed=final_embed, view=self
        )

    async def _unselect_current_card_button_callback(
        self, interaction: discord.Interaction
    ):
        await interaction.response.defer()
        if not self.current_page_cards:
            raise BusinessError("當前頁面沒有可取消選擇的卡片。")

        card_to_unselect = self.current_page_cards[0]
        result = await shop_service.unselect_card_for_exchange(
            session_id=self.session_id, card_id_to_unselect=card_to_unselect.card_id
        )
        logger.debug("Card unselection result: %s", result)

        # 更新視圖
        await self.refresh_page_data(interaction)
        final_embed = await self.get_current_page_embed()
        # 直接編輯訊息，Discord 異常會拋到 bot.py 處理
        await interaction.edit_original_response(embed=final_embed, view=self)

    async def _confirm_exchange_button_callback(self, interaction: discord.Interaction):
        if not self.session_data.pending_selected_cards:
            raise BusinessError("您尚未選擇任何卡片。請先選卡再確認兌換。")

        if (
            len(self.session_data.pending_selected_cards)
            < self.session_data.total_quantity_to_redeem
        ):
            needed = self.session_data.total_quantity_to_redeem - len(
                self.session_data.pending_selected_cards
            )
            raise BusinessError(f"您尚未選擇足夠的卡片。還需選擇 {needed} 張才能兌換。")

        await interaction.response.defer()
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        result = await shop_service.finalize_specific_ticket_exchange(
            session_id=self.session_id
        )

        # 兌換成功，顯示結果
        exchanged_cards_data = result.get("exchanged_cards", []) if result else []

        # 將字典轉換為Card對象
        exchanged_cards = []
        if exchanged_cards_data:
            for card_data in exchanged_cards_data:
                if isinstance(card_data, dict):
                    exchanged_cards.append(Card(**card_data))
                elif hasattr(card_data, "card_id"):  # 已經是Card對象
                    exchanged_cards.append(card_data)
                else:
                    logger.warning("Unexpected card data format: %s", type(card_data))

        if not exchanged_cards:
            await interaction.edit_original_response(
                content="錯誤：兌換成功，但未返回任何卡片資訊。",
                embed=None,
                view=None,
            )
            return

        # 創建並準備結果視圖
        result_view = SpecificTicketResultView(
            original_interaction=interaction,
            ticket_name=self.session_data.ticket_definition.display_name,
            exchanged_cards=exchanged_cards,
        )

        (
            initial_embed,
            configured_view,
        ) = await result_view.prepare_initial_message_payload()

        await interaction.edit_original_response(
            content=None, embed=initial_embed, view=configured_view
        )

    async def _cancel_button_callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        await shop_service.cancel_exchange_session(self.session_id)
        embed = SuccessEmbed(description="已取消兌換。")
        await interaction.followup.send(embed=embed, ephemeral=True)
        self.stop()

    async def disable_view(
        self,
        interaction: Optional[discord.Interaction],
        is_final_action: bool = False,
        message_on_disable: Optional[str] = None,
    ):
        for item in self.children:
            if hasattr(item, "disabled"):
                item.disabled = True  # type: ignore
        # 簡化版本：只在有 interaction 時更新消息
        if interaction and self.message:
            # 直接編輯訊息，Discord 異常會拋到 bot.py 處理
            content = message_on_disable if message_on_disable else self.message.content
            view_to_set = None if is_final_action else self
            await interaction.response.defer()
            await interaction.edit_original_response(content=content, view=view_to_set)
        if is_final_action:
            self.stop()

    async def on_timeout(self) -> None:
        logger.info(
            "SpecificTicketSelectionView timed out for session %s (user: %s).",
            self.session_id,
            self.user_id,
        )
        # 根據規範，移除 try...except，讓錯誤向上冒泡
        # 如果取消會話失敗，異常將由全域處理器記錄
        await shop_service.cancel_exchange_session(self.session_id)
        logger.info(
            "Successfully cancelled shop session %s on view timeout.",
            self.session_id,
        )
        self.stop()
