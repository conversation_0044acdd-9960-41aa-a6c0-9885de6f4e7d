"""
Pioneer System 任務更新服務
負責在遊戲事件發生時，檢查並更新玩家的任務進度。
"""

from typing import Any, Dict

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.models.pioneer_models import QuestProgress
from utils.logger import logger


class TaskUpdater:
    """處理任務進度更新的核心邏輯"""

    async def check_and_update_tasks(
        self, user_id: int, event_type: str, **kwargs: Any
    ) -> None:
        """
        在一個遊戲事件發生後，檢查並更新所有相關的任務進度。

        Args:
            user_id (int): 玩家ID。
            event_type (str): 事件類型，對應任務目標的類型 (e.g., 'gather', 'craft')。
            **kwargs: 事件的詳細參數 (e.g., action="gather_wood", quantity=1)。
        """
        try:
            assert game_data is not None
            # 1. 獲取玩家所有進行中的任務
            active_quests = await repositories.get_user_active_quests(user_id)
            if not active_quests:
                return

            # 2. 遍歷每個進行中的任務，檢查是否與當前事件匹配
            for quest in active_quests:
                task_config = game_data.get_task_config(quest.quest_id)
                if not task_config:
                    continue

                # 檢查任務是否已解鎖
                # (此處應有時代檢查邏輯，暫時簡化)

                for objective in task_config.objectives:
                    if self._is_objective_match(objective, event_type, **kwargs):
                        # 3. 如果匹配，更新進度
                        await self._update_quest_progress(quest, objective, **kwargs)

        except Exception as e:
            logger.error("檢查和更新用戶 %s 的任務失敗: %s", user_id, e)

    def _is_objective_match(
        self, objective: Dict[str, Any], event_type: str, **kwargs: Any
    ) -> bool:
        """檢查單個任務目標是否與發生的事件匹配。"""
        obj_type = objective.get("type")
        if obj_type != event_type:
            return False

        # 對於需要精確匹配子條件的類型
        if obj_type in ["gather", "craft", "facility_build", "facility_upgrade"]:
            if "action" in objective and objective["action"] != kwargs.get("action"):
                return False
            if "recipe" in objective and objective["recipe"] != kwargs.get("recipe"):
                return False
            if "facility_type" in objective and objective[
                "facility_type"
            ] != kwargs.get("facility_type"):
                return False
            if "upgrade_type" in objective and objective["upgrade_type"] != kwargs.get(
                "upgrade_type"
            ):
                return False

        # 對於 skill_level，需要匹配 skill_id (如果有的話)
        if obj_type == "skill_level":
            if "skill" in objective and objective["skill"] != kwargs.get("skill_id"):
                return False

        # 對於 skill_xp，也需要匹配 skill_id (如果有的話)
        if obj_type == "skill_xp":
            if "skill" in objective and objective["skill"] != kwargs.get("skill_id"):
                return False

        return True

    async def _update_quest_progress(
        self, quest: QuestProgress, objective: Dict[str, Any], **kwargs: Any
    ) -> None:
        """根據事件類型和目標配置，更新任務進度。"""

        obj_type = objective.get("type")
        current_progress = quest.progress
        new_progress = current_progress

        # 處理不同類型的進度更新
        if obj_type in [
            "gather",
            "craft",
            "facility_build",
            "facility_upgrade",
            "facility_collect",
        ]:
            # 計數類：進度增加 quantity
            quantity = kwargs.get("quantity", 1)
            new_progress += quantity

        elif obj_type in ["skill_xp", "total_oil_earnings"]:
            # 累加類：進度增加 amount
            amount = kwargs.get("amount", 0)
            new_progress += amount

        elif obj_type in ["skill_level", "research_level"]:
            # 狀態類：進度更新為當前狀態值，取較大者
            current_value = kwargs.get("level", 0)
            new_progress = max(current_progress, current_value)

        # 如果進度沒有變化，則不更新數據庫
        if new_progress == current_progress:
            return

        # 更新數據庫
        await repositories.update_quest_progress(quest.id, new_progress)
        logger.info(
            f"任務 '{quest.quest_id}' 進度更新: {current_progress} -> {new_progress}"
        )

        # 檢查是否完成
        if new_progress >= quest.target:
            await self._complete_quest(quest)

    async def _complete_quest(self, quest: QuestProgress) -> None:
        """
        完成一個任務，標記為已完成並發放獎勵。
        """
        # 標記為完成
        await repositories.complete_quest(quest.id)
        logger.info("用戶 %s 完成了任務 '%s'！", quest.user_id, quest.quest_id)

        # TODO: 發放獎勵
        assert game_data is not None
        task_config = game_data.get_task_config(quest.quest_id)
        if task_config and task_config.rewards:
            # 在此處實現獎勵發放邏輯
            # 例如: await reward_service.grant_rewards(quest.user_id, task_config.rewards)
            pass


# 創建一個單例供其他模塊使用
task_updater = TaskUpdater()
