"""
開拓者系統收益處理器
處理待收取收益的收取邏輯
"""

from typing import Any, Dict

from pioneer.exceptions import PioneerActionError, PioneerNotFoundError
from pioneer.models.pioneer_models import ActionResult
from utils.logger import logger


class EarningsProcessor:
    """收益處理器"""

    def __init__(self, repository, game_data):
        self.repository = repository
        self.game_data = game_data

    async def execute(
        self, user_id: int, action_config, params: Dict[str, Any]
    ) -> ActionResult:
        """
        根據動作類型執行對應的收益操作
        """
        action_type = action_config.type
        if action_type == "collect_earnings":
            return await self.collect_earnings(user_id, action_config, params)
        elif action_type == "check_earnings":
            return await self.check_earnings(user_id, action_config, params)
        else:
            raise PioneerActionError(f"未知的收益動作類型: {action_type}")

    async def collect_earnings(
        self, user_id: int, action_config, params: Dict[str, Any]
    ) -> ActionResult:
        """
        收取待收取的油幣收益

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 參數字典

        Returns:
            ActionResult: 執行結果
        """
        try:
            # 收取待收取收益
            collected_amount = await self.repository.collect_pending_oil_earnings(
                user_id
            )

            if collected_amount > 0:
                message = f"成功收取 {collected_amount:,} 油幣！"
                rewards = [{"type": "oil", "amount": collected_amount}]
            else:
                message = "目前沒有待收取的收益"
                rewards = []

            return ActionResult.success_result(message=message, rewards=rewards)

        except Exception as e:
            logger.error("收取收益時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError("收取收益時發生系統錯誤，請聯繫管理員。") from e

    async def check_earnings(
        self, user_id: int, action_config, params: Dict[str, Any]
    ) -> ActionResult:
        """
        檢查待收取的油幣收益

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 參數字典

        Returns:
            ActionResult: 執行結果
        """
        try:
            # 獲取用戶資料
            profile = await self.repository.get_pioneer_profile(user_id)
            if not profile:
                raise PioneerNotFoundError(f"找不到玩家 {user_id} 的開拓者檔案。")

            pending_amount = profile.pending_oil_earnings
            total_amount = profile.total_oil_earnings

            if pending_amount > 0:
                message = f"💰 **待收取收益**: {pending_amount:,} 油幣\n📊 **累計總收益**: {total_amount:,} 油幣"
            else:
                message = (
                    f"目前沒有待收取的收益。\n📊 **累計總收益**: {total_amount:,} 油幣"
                )

            return ActionResult.success_result(message=message)

        except PioneerNotFoundError:
            # 如果找不到 profile，直接重新拋出，讓上層處理
            raise
        except Exception as e:
            logger.error("檢查收益時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError("檢查收益時發生系統錯誤，請聯繫管理員。") from e
