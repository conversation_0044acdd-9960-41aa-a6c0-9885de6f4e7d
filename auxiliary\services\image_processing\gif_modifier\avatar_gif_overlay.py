"""
GIF與頭像疊加模塊
將用戶頭像疊加到GIF圖像上，確保頭像被GIF正確疊加，並保留GIF透明背景。
"""

import asyncio
import importlib.util
import io
import math
import random
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import List, Optional, Tuple

from PIL import Image, ImageSequence

from utils.logger import logger

from .config import get_gif_config

# 檢查imageio是否可用，用於更快的GIF保存
if importlib.util.find_spec("imageio") and importlib.util.find_spec("numpy"):
    IMAGEIO_AVAILABLE = True
    logger.info("imageio可用，將使用快速GIF保存")
else:
    IMAGEIO_AVAILABLE = False
    logger.info("imageio不可用，使用標準Pillow保存")


def _convert_single_frame(
    frame_data: Tuple[Image.Image, Tuple[int, int, int]],
) -> Image.Image:
    """轉換單個幀的輔助函數，用於並行處理"""
    frame, transparent_color = frame_data

    # 創建RGB背景並合成
    background = Image.new("RGB", frame.size, transparent_color)
    background.paste(frame, (0, 0), frame)

    # 轉換為P模式並處理透明度
    paletted = background.quantize(colors=255, method=2)
    alpha = frame.getchannel("A")
    mask = Image.eval(alpha, lambda a: 0 if a > 0 else 255)
    paletted.paste(255, mask)
    paletted.info["transparency"] = 255

    return paletted


def create_global_palette_and_convert(
    frames: List[Image.Image],
    transparent_color: Tuple[int, int, int] = (0, 0, 0),
    colors: int = 255,
) -> List[Image.Image]:
    """
    為所有幀創建一個全局調色板，然後將所有幀轉換為P模式。
    這比逐幀轉換快得多，並且生成的GIF檔案更小。
    """
    if not frames:
        return []

    logger.info("正在為 %s 幀生成全局調色板...", len(frames))

    # 1. 創建一個包含所有幀像素的「超級圖像」用於採樣
    # 為了節省記憶體，我們可以只取每幀的一部分像素，或者隔幾幀取一幀
    # 這裡為了簡單和高品質，我們先將所有幀合併
    try:
        combined_image = Image.new(
            "RGBA", (frames[0].width, frames[0].height * len(frames))
        )
        for i, frame in enumerate(frames):
            combined_image.paste(frame, (0, i * frame.height))

        # 2. 從「超級圖像」生成一個優化的全局調色板
        # 先將RGBA轉換為RGB模式，因為quantize只支持RGB或L模式
        combined_rgb = Image.new("RGB", combined_image.size, transparent_color)
        combined_rgb.paste(combined_image, (0, 0), combined_image)
        palette_image = combined_rgb.quantize(
            colors=colors, method=2
        )  # method=2 (MEDIANCUT) 通常效果不錯

        logger.info("全局調色板生成完畢，開始轉換所有幀...")

        # 3. 將每一幀應用上這個全局調色板，並正確處理透明度
        converted_frames = []
        for frame in frames:
            if frame.mode == "RGBA":
                # 創建RGB背景並合成
                background = Image.new("RGB", frame.size, transparent_color)
                background.paste(frame, (0, 0), frame)

                # 轉換為P模式並處理透明度
                paletted = background.quantize(
                    palette=palette_image, dither=Image.Dither.FLOYDSTEINBERG
                )

                # 提取alpha通道並設置透明度
                alpha = frame.getchannel("A")
                mask = Image.eval(alpha, lambda a: 0 if a > 0 else 255)
                paletted.paste(255, mask)  # 255是透明色索引
                paletted.info["transparency"] = 255

                converted_frames.append(paletted)
            else:
                # 非RGBA模式直接量化
                converted_frame = frame.quantize(
                    palette=palette_image, dither=Image.Dither.FLOYDSTEINBERG
                )
                converted_frames.append(converted_frame)

        logger.info("所有幀轉換完成。")
        return converted_frames

    except Exception as e:
        logger.warning("全局調色板轉換失敗，回退到原始方法: %s", e)
        # 回退到原始的逐幀轉換方法
        return optimize_palette_conversion_fallback(frames)


def optimize_palette_conversion_fallback(
    frames: List[Image.Image], transparent_color: Tuple[int, int, int] = (0, 0, 0)
) -> List[Image.Image]:
    """回退的調色板轉換方法（原始實現）"""
    if not frames:
        return []

    # 小批量直接處理，避免線程開銷
    if len(frames) <= 3:
        return [_convert_single_frame((frame, transparent_color)) for frame in frames]

    # 並行處理大批量幀
    with ThreadPoolExecutor(max_workers=min(4, len(frames))) as executor:
        frame_data = [(frame, transparent_color) for frame in frames]
        return list(executor.map(_convert_single_frame, frame_data))


async def fetch_avatar(user) -> Optional[Image.Image]:
    """獲取用戶頭像並轉換為RGBA格式 - 使用 Discord.py 內建方法"""
    try:
        # 使用 Discord.py 內建的 read() 方法，自動處理快取和連接池
        avatar_data = await user.display_avatar.read()
        # 將PIL操作移到線程中避免阻塞
        return await asyncio.to_thread(
            lambda: Image.open(io.BytesIO(avatar_data)).convert("RGBA")
        )
    except Exception:
        return None


def _calculate_base_position(
    position: str,
    gif_width: int,
    gif_height: int,
    avatar_size: Tuple[int, int],
    offset_x: int,
    offset_y: int,
) -> Tuple[int, int]:
    """計算頭像基礎位置"""
    avatar_width, avatar_height = avatar_size

    position_map = {
        "center_bottom": (
            (gif_width - avatar_width) // 2,
            gif_height - avatar_height + offset_y,
        ),
        "left_bottom": (offset_x, gif_height - avatar_height + offset_y),
        "right_bottom": (
            gif_width - avatar_width - offset_x,
            gif_height - avatar_height + offset_y,
        ),
        "center": ((gif_width - avatar_width) // 2, (gif_height - avatar_height) // 2),
    }

    return position_map.get(position, position_map["center_bottom"])


def _calculate_animation_offset(
    frame_index: int, animation_config: dict, num_frames: int
) -> Tuple[int, int]:
    """計算動畫偏移量"""
    x_amp = animation_config["x_amplitude"]
    y_amp = animation_config["y_amplitude"]
    phase = animation_config["phase_shift"]
    frequency = animation_config.get("frequency", 1.0)

    if x_amp == 0 and y_amp == 0:
        return 0, 0

    # 使用決定性隨機數
    random.seed(frame_index)
    random_factor_x = 1.0 + (random.random() * 0.3 - 0.15)
    random_factor_y = 1.0 + (random.random() * 0.3 - 0.15)

    safe_frames = max(1, num_frames - 1)

    anim_x = 0
    anim_y = 0

    if x_amp > 0:
        normalized_pos = (frame_index * frequency) / safe_frames
        anim_x = int(x_amp * random_factor_x * math.sin(normalized_pos * 2 * math.pi))

    if y_amp > 0:
        shifted_index = ((frame_index * frequency) + (num_frames * phase)) % num_frames
        normalized_pos = shifted_index / safe_frames
        anim_y = int(y_amp * random_factor_y * math.cos(normalized_pos * 2 * math.pi))

    return anim_x, anim_y


def precompute_animation_positions(
    config: dict,
    gif_width: int,
    gif_height: int,
    avatar_size: Tuple[int, int],
    num_frames: int,
) -> List[Tuple[int, int]]:
    """預計算所有幀的動畫位置，避免重複計算"""
    base_x, base_y = _calculate_base_position(
        config["avatar_position"],
        gif_width,
        gif_height,
        avatar_size,
        config["offset_x"],
        config["offset_y"],
    )

    return [
        (base_x + anim_x, base_y + anim_y)
        for anim_x, anim_y in (
            _calculate_animation_offset(i, config["animation"], num_frames)
            for i in range(num_frames)
        )
    ]


def _process_single_frame(frame_data: Tuple) -> Image.Image:
    """處理單個幀的輔助函數，用於並行處理"""
    frame_rgba, avatar, position, gif_width, gif_height = frame_data

    new_frame = Image.new("RGBA", (gif_width, gif_height), (0, 0, 0, 0))
    new_frame.paste(avatar, position, avatar)
    new_frame.alpha_composite(frame_rgba)
    return new_frame


async def _process_frames_parallel(
    gif_frames: List[Image.Image],
    avatar: Image.Image,
    animation_positions: List[Tuple[int, int]],
    gif_width: int,
    gif_height: int,
) -> List[Image.Image]:
    """並行處理所有幀的疊加操作"""
    frame_data_list = [
        (frame_rgba, avatar, animation_positions[i], gif_width, gif_height)
        for i, frame_rgba in enumerate(gif_frames)
    ]

    # 小批量直接處理
    if len(gif_frames) <= 3:
        return [_process_single_frame(data) for data in frame_data_list]

    # 大批量並行處理
    event_loop = asyncio.get_event_loop()
    with ThreadPoolExecutor(max_workers=min(4, len(gif_frames))) as executor:
        return await event_loop.run_in_executor(
            executor, lambda: list(map(_process_single_frame, frame_data_list))
        )


def _create_output_path(
    gif_path: str, user_id: int, output_path: Optional[str] = None
) -> str:
    """創建輸出文件路徑"""
    if output_path:
        return output_path

    # 使用臨時文件管理器創建唯一文件名
    try:
        from auxiliary.services.image_processing.temp_file_manager import (
            create_temp_filename,
        )

        gif_name = Path(gif_path).stem
        return create_temp_filename(gif_name, user_id)
    except ImportError:
        # 回退到原始方法
        output_dir = Path("image_processing") / "temp"
        output_dir.mkdir(parents=True, exist_ok=True)
        gif_name = Path(gif_path).stem
        return str(output_dir / ("%s_%s.gif" % (gif_name, user_id)))


def _save_gif_with_imageio(
    frames: List[Image.Image], output_path: str, duration: int, loop: int
) -> None:
    """使用 imageio 保存 GIF，通常更快"""
    if not frames:
        raise ValueError("沒有幀可以保存")
    if not IMAGEIO_AVAILABLE:
        logger.warning("嘗試使用 imageio 保存，但它不可用。")
        return

    import imageio
    import numpy as np

    # imageio 需要 numpy 數組
    # duration 在 imageio 中以秒為單位
    imageio_frames = [np.array(frame.convert("RGBA")) for frame in frames]

    # imageio 的 duration 參數是每幀的秒數
    duration_sec = duration / 1000.0

    try:
        imageio.mimsave(
            output_path,
            imageio_frames,  # type: ignore
            duration=duration_sec,
            loop=loop,
        )
    except Exception as e:
        logger.warning("imageio.mimsave failed: %s, falling back to alternative", e)
        # Fallback to basic imageio save
        imageio.mimsave(output_path, imageio_frames)  # type: ignore


def save_gif(
    frames: List[Image.Image],
    output_path: str,
    duration: int,
    loop: int = 0,
    use_imageio: bool = False,
    transparency: Optional[int] = 255,
) -> None:
    """
    統一的GIF保存函數

    Args:
        frames: 圖像幀列表
        output_path: 輸出路徑
        duration: 幀持續時間（毫秒）
        loop: 循環次數（0為無限循環）
        use_imageio: 是否使用imageio保存
        transparency: 透明色索引，None表示不設置透明度
    """
    if not frames:
        raise ValueError("沒有幀可以保存")

    # 確保輸出目錄存在
    output_dir = Path(output_path).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    # 如果文件已存在，先嘗試刪除（避免文件鎖定問題）
    if Path(output_path).exists():
        try:
            Path(output_path).unlink()
            logger.debug("已刪除現有文件: %s", output_path)
        except Exception as e:
            logger.warning("無法刪除現有文件 %s: %s", output_path, e)

    try:
        # 如果可用且要求使用imageio，則使用imageio保存RGBA幀
        if use_imageio and IMAGEIO_AVAILABLE:
            logger.info("使用imageio快速保存GIF...")
            _save_gif_with_imageio(frames, output_path, duration, loop)
            return

        # 否則使用標準Pillow方法保存P模式幀
        logger.info("使用Pillow標準方法保存GIF...")
        # 確保所有參數都是正確的類型
        append_images = frames[1:] if len(frames) > 1 else []
        save_kwargs = {
            "format": "GIF",
            "save_all": True,
            "append_images": append_images,
            "duration": int(duration),
            "loop": int(loop),
            "disposal": 2,
            "optimize": False,
        }

        # 只有在指定透明度時才添加透明度設置
        if transparency is not None:
            save_kwargs["transparency"] = int(transparency)

        logger.info("正在保存GIF: %s幀...", len(frames))
        # 明確傳遞參數以避免類型問題
        frames[0].save(
            output_path,
            format="GIF",
            save_all=True,
            append_images=append_images,
            duration=int(duration),
            loop=int(loop),
            disposal=2,
            optimize=False,
            **({"transparency": int(transparency)} if transparency is not None else {}),
        )
        logger.info("GIF保存完成: %s", output_path)

    except Exception as e:
        logger.error("保存GIF失敗: %s", e)
        # 如果保存失敗，嘗試清理可能創建的不完整文件
        if Path(output_path).exists():
            try:
                Path(output_path).unlink()
                logger.debug("已清理不完整的文件: %s", output_path)
            except Exception as cleanup_error:
                logger.warning("清理不完整文件失敗: %s", cleanup_error)
        raise ValueError(f"保存GIF失敗: {e}") from e


async def overlay_avatar_on_gif(
    gif_path: str,
    user,
    output_path: Optional[str] = None,
    fixed_size: Tuple[int, int] = (0, 0),
    use_fast_save: bool = False,
) -> str:
    """將用戶頭像疊加到GIF上"""
    try:
        # 獲取配置和頭像
        gif_config = get_gif_config(gif_path)
        avatar = await fetch_avatar(user)
        if not avatar:
            raise ValueError("無法獲取用戶頭像")

        # 將PIL操作移到線程中避免阻塞
        def _sync_process_gif():
            # 調整頭像大小
            # 使用新的 Pillow API
            avatar_size = gif_config["avatar_size"]
            if not isinstance(avatar_size, (tuple, list)) or len(avatar_size) != 2:
                avatar_size = (80, 80)  # Default size

            try:
                # Pillow 10.0.0+ 使用 Image.Resampling.LANCZOS
                if avatar is not None:
                    resized_avatar = avatar.resize(
                        avatar_size, Image.Resampling.LANCZOS
                    )
                else:
                    raise ValueError("Avatar is None")
            except AttributeError:
                # 舊版本 Pillow 使用 Image.LANCZOS
                if avatar is not None:
                    resized_avatar = avatar.resize(
                        avatar_size, getattr(Image, "LANCZOS", 1)
                    )
                else:
                    raise ValueError("Avatar is None") from None

            # 處理原始GIF - 確保文件句柄被正確關閉
            gif_frames = []
            gif_info = {}

            try:
                with Image.open(gif_path) as original_gif:
                    gif_info = {
                        "duration": original_gif.info.get("duration", 100),
                        "loop": original_gif.info.get("loop", 0),
                        "size": original_gif.size,
                    }
                    # 轉換所有幀並立即複製到內存中
                    for frame in ImageSequence.Iterator(original_gif):
                        # 創建幀的副本以避免引用已關閉的文件
                        frame_copy = frame.convert("RGBA").copy()
                        gif_frames.append(frame_copy)
            except Exception as e:
                logger.error("處理 GIF 文件時發生錯誤: %s", e)
                raise ValueError(f"無法處理 GIF 文件 {gif_path}: {e}") from e

            return resized_avatar, gif_info, gif_frames

        avatar, gif_info, gif_frames = await asyncio.to_thread(_sync_process_gif)

        # 計算動畫位置並處理幀
        # 確保 avatar_size 是正確的元組格式
        avatar_size_raw = gif_config["avatar_size"]
        if not isinstance(avatar_size_raw, (tuple, list)) or len(avatar_size_raw) != 2:
            avatar_size: Tuple[int, int] = (80, 80)  # Default size
        else:
            # Ensure it's a tuple with exactly 2 int elements
            avatar_size = (int(avatar_size_raw[0]), int(avatar_size_raw[1]))

        gif_width, gif_height = gif_info["size"]
        animation_positions = precompute_animation_positions(
            gif_config, gif_width, gif_height, avatar_size, len(gif_frames)
        )

        frames = await _process_frames_parallel(
            gif_frames,
            avatar,
            animation_positions,
            gif_info["size"][0],
            gif_info["size"][1],
        )

        # 調整尺寸（如果需要）(移到線程中避免阻塞)
        if fixed_size != (0, 0):

            def _resize_frames():
                try:
                    # Pillow 10.0.0+ 使用 Image.Resampling.LANCZOS
                    return [
                        frame.resize(fixed_size, Image.Resampling.LANCZOS)
                        for frame in frames
                    ]
                except AttributeError:
                    # 舊版本 Pillow 使用 Image.LANCZOS
                    return [
                        frame.resize(fixed_size, getattr(Image, "LANCZOS", 1))
                        for frame in frames
                    ]

            frames = await asyncio.to_thread(_resize_frames)

        # 根據選擇的保存方法處理
        output_file_path = _create_output_path(gif_path, user.id, output_path)

        if use_fast_save and IMAGEIO_AVAILABLE:
            # 使用imageio快速保存，直接保存RGBA幀 (移到線程中避免阻塞)
            duration = gif_info.get("duration", 100)
            loop = gif_info.get("loop", 0)
            # Ensure they are integers
            if not isinstance(duration, int):
                duration = 100
            if not isinstance(loop, int):
                loop = 0

            await asyncio.to_thread(
                save_gif,
                frames,
                output_file_path,
                duration,
                loop,
                use_imageio=True,
            )
        else:
            # 使用標準方法：先轉換調色板再保存
            logger.info("使用標準保存模式（全局調色板優化）...")
            converted_frames = await asyncio.to_thread(
                create_global_palette_and_convert, frames
            )
            duration = gif_info.get("duration", 100)
            loop = gif_info.get("loop", 0)
            # Ensure they are integers
            if not isinstance(duration, int):
                duration = 100
            if not isinstance(loop, int):
                loop = 0

            await asyncio.to_thread(
                save_gif,
                converted_frames,
                output_file_path,
                duration,
                loop,
            )

        logger.info("GIF處理完成，返回路徑: %s", output_file_path)
        return output_file_path

    except Exception as e:
        raise ValueError("GIF處理失敗: %s" % e) from e
