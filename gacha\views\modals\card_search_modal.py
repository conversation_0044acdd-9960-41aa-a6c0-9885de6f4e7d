from typing import TYPE_CHECKING, Any, Optional, cast

import discord
from discord.ui import TextInput

from utils.base_modal import BaseModal
from utils.logger import logger

if TYPE_CHECKING:
    from utils.error_handler import BotType


class CardSearchModal(BaseModal):
    def __init__(
        self,
        bot: "BotType",
        parent_view: discord.ui.View,
        current_query: Optional[str] = None,
        title: str = "搜尋卡片",
        *,
        timeout: Optional[float] = None,
    ):
        super().__init__(bot=bot, title=title, timeout=timeout)
        self.parent_view = parent_view
        self.search_input = TextInput(
            label="卡片ID或名稱 (留空清除搜尋)",
            placeholder="請輸入要搜尋的卡片ID或名稱...",
            required=False,
            max_length=100,
            default=current_query if current_query else "",
        )
        self.add_item(self.search_input)

    async def on_submit(self, interaction: discord.Interaction):
        search_query = (
            self.search_input.value.strip() if self.search_input.value else None
        )
        logger.info(
            "Search query submitted: '%s' by user %s", search_query, interaction.user.id
        )
        # 使用 cast 來處理靜態分析器的警告，同時保留動態調用的彈性
        parent_view_with_handler = cast(Any, self.parent_view)
        await parent_view_with_handler.handle_search_submit(interaction, search_query)
