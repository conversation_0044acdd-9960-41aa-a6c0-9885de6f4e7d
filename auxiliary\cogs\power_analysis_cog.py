# -*- coding: utf-8 -*-
"""
戰力分析系統 COG - 模組化版本
"""

import io
from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from gacha.exceptions import BusinessError
from utils.logger import logger

# --- 修正導入路徑 ---
from ..services.ai_core import message_handler as message_handler_service
from ..services.power_analysis import analyzer, helpers, image_generator
from ..services.power_analysis.config import default_config

# 檢查相依性是否可用
try:
    from ..services.ai_core import ai_service

    POWER_SERVICES_AVAILABLE = True
    del ai_service  # 只用於檢查依賴，檢查後刪除引用
except ImportError as e:
    logger.warning("戰力分析服務的相依性導入失敗: %s", e)
    POWER_SERVICES_AVAILABLE = False


class PowerAnalysisCog(commands.Cog, name="戰力分析"):
    """戰力分析系統 COG"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("PowerAnalysisCog 初始化完成")

    async def cog_unload(self):
        """COG 卸載時清理資源"""
        try:
            self.bot.tree.remove_command(
                "戰力分析", type=discord.AppCommandType.message
            )
            logger.info("✅ 戰力分析 Context Menu 已移除")
        except Exception as e:
            logger.error("❌ 移除戰力分析 Context Menu 失敗: %s", e)

    @property
    def is_service_available(self) -> bool:
        """檢查戰力分析服務是否可用"""
        return POWER_SERVICES_AVAILABLE

    async def _process_power_analysis(
        self,
        image_data: bytes,
        interaction: discord.Interaction,
        processing_message: discord.WebhookMessage,
        request_id: str,
    ) -> None:
        """在背景任務中處理戰力分析的主要邏輯"""
        user_display_name = helpers.get_sanitized_user_display_name(interaction)

        if not image_data:
            logger.error("[%s] 未收到有效的圖像數據", request_id)
            await processing_message.edit(content="❌ 未能獲取圖像數據進行分析，請重試")
            return

        logger.info(
            "[%s] 收到 %s bytes 圖像數據，用戶: %s",
            request_id,
            len(image_data),
            interaction.user.id,
        )

        power_result = await analyzer.analyze_power_from_binary(
            image_data=image_data, request_id=request_id
        )
        power_result["request_id"] = request_id

        if not power_result or not power_result.get("level"):
            logger.error("[%s] 戰力分析結果無效: %s", request_id, power_result)
            await processing_message.edit(
                content="😕 AI 未能完成戰力分析，請稍後再試或換張圖片試試"
            )
            return

        await processing_message.edit(content="⚔️ 分析完成，正在生成戰力報告...")

        image_bytes = await image_generator.generate_power_image(
            power_result=power_result,
            user_image_data=image_data,
            user_name=user_display_name,
        )

        file = discord.File(
            io.BytesIO(image_bytes), filename=f"power_analysis_{request_id}.webp"
        )
        edited_message = await processing_message.edit(content="", attachments=[file])

        if edited_message.attachments:
            cdn_url = edited_message.attachments[0].url
            level = power_result.get("level", "C")
            await helpers.send_power_image_to_webhook(
                default_config, cdn_url, user_display_name, level, request_id
            )

    async def _analyze_power_command(
        self,
        interaction: discord.Interaction,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
    ) -> None:
        """分析戰力的內部處理方法"""
        if not (image or url):
            raise BusinessError(
                "⚠️ 請上傳一張角色圖片或提供一個有效的圖片URL進行戰力分析"
            )

        processing_message = (
            "🔗 正在從URL下載並分析您的角色戰力..."
            if url and not image
            else "⚔️ 正在分析您的角色戰力..."
        )

        async def processor_wrapper(
            image_data, user_id, response_message, request_id, user_prompt=None
        ):
            await self._process_power_analysis(
                image_data, interaction, response_message, request_id
            )

        await message_handler_service.handle_interaction(
            interaction=interaction,
            processing_message=processing_message,
            processor_func=processor_wrapper,
            image=image,
            url=url,
        )

    @app_commands.command(name="rateb", description="上傳角色圖片進行戰力分析")
    @app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        image="上傳角色圖片 (可選，與URL二選一)",
        url="或直接提供圖片URL (可選，與上傳圖片二選一)",
    )
    async def rateb_command(
        self,
        interaction: discord.Interaction,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
    ):
        if not self.is_service_available:
            await interaction.response.send_message(
                "❌ 戰力分析服務暫時不可用。", ephemeral=True
            )
            return
        await self._analyze_power_command(interaction, image, url)


@app_commands.context_menu(name="戰力分析")
@app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
@app_commands.allowed_installs(guilds=True, users=True)
@app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
async def power_analysis_context(
    interaction: discord.Interaction, message: discord.Message
):
    """消息上下文菜單 - 戰力分析功能"""
    # 類型斷言，確保 client 是 Bot 類型
    from discord.ext import commands

    if isinstance(interaction.client, (commands.Bot, commands.AutoShardedBot)):
        cog = interaction.client.get_cog("戰力分析")
    else:
        cog = None
    if not cog or not getattr(cog, "is_service_available", False):
        await interaction.response.send_message(
            "❌ 戰力分析服務暫時不可用，請稍後再試。", ephemeral=True
        )
        return

    image_url = None
    if message.attachments:
        for attachment in message.attachments:
            if attachment.content_type and attachment.content_type.startswith("image/"):
                image_url = attachment.url
                break
    if not image_url and message.embeds:
        for embed in message.embeds:
            if embed.image and embed.image.url:
                image_url = embed.image.url
                break

    if not image_url:
        await interaction.response.send_message(
            "❌ 所選消息不包含任何圖片。", ephemeral=True
        )
        return

    await cog._analyze_power_command(interaction, url=image_url)  # type: ignore


async def setup(bot: commands.Bot):
    """設置 Cog"""
    cog = PowerAnalysisCog(bot)
    await bot.add_cog(cog)
    bot.tree.add_command(power_analysis_context)
