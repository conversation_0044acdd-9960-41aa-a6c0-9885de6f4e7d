import argparse
import asyncio
import json
import os
import sys

# 將專案根目錄添加到 sys.path
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv  # noqa: E402

from database.postgresql.async_manager import (  # noqa: E402
    close_connections,
    get_pool,
    setup_connections,
)

# 載入 .env 檔案中的環境變數 (假設 .env 在專案根目錄)
dotenv_path = os.path.join(project_root, ".env")
load_dotenv(dotenv_path=dotenv_path)

# 稀有度名稱到整數的映射 (用於 mazoku 格式)
RARITY_MAP_MAZOKU = {"C": 1, "R": 2, "SR": 3, "SSR": 4, "UR": 5}

# 情人節卡池稀有度字串 (來自 JSON) 到整數的映射
# 假設 T1 -> 1, T2 -> 2, ..., T6 -> 6
RARITY_MAP_VALENTINE = {
    "1": 1,  # T1
    "2": 2,  # T2
    "3": 3,  # T3
    "4": 4,  # T4
    "5": 5,  # T5
    "6": 6,  # T6 - 根據您的價格表，情人節卡池有T6
}

# 不同 pool_type 的價格設定 (鍵為稀有度整數值)
# 注意：這些價格必須與 config/gacha_settings.yaml 中的 pool_rarity_prices 保持一致
# 最後更新：2025-06-15 - 修正 vd 和 special_maid 卡池價格以符合遊戲設定
PRICE_CONFIG = {
    "special": {  # 典藏卡池 - 符合 config/gacha_settings.yaml
        1: 7,  # C
        2: 50,  # R
        3: 400,  # SR
        4: 13000,  # SSR
        5: 42000,  # UR
    },
    "vd": {  # 情人節卡池 (Valentine's Day) - 已修正為符合 config/gacha_settings.yaml
        1: 3,
        2: 12,
        3: 70,
        4: 1000,
        5: 15000,
        6: 45000,
    },
    "special_maid": {  # 典藏女僕卡池 - 已修正為符合 config/gacha_settings.yaml
        1: 4,  # C
        2: 80,  # R
        3: 500,  # SR
        4: 17000,  # SSR
        5: 50000,  # UR
    },
    "hololive": {  # Hololive 卡池
        7: 60000,  # R7
        6: 22000,  # R6
        5: 7000,  # R5
        4: 800,  # R4
        3: 80,  # R3
        2: 12,  # R2
        1: 4,  # R1
    },
    "ua": {  # Union Arena 卡池
        7: 50000,  # R7
        6: 20000,  # R6
        5: 7000,  # R5
        4: 750,  # R4
        3: 70,  # R3
        2: 10,  # R2
        1: 3,  # R1
    },
    "ptcg": {  # Pokemon Trading Card Game 卡池
        7: 35000,  # R7
        6: 15000,  # R6
        5: 4000,  # R5
        4: 500,  # R4
        3: 50,  # R3
        2: 8,  # R2
        1: 3,  # R1
    },
    "wixoss": {  # WIXOSS 卡池
        7: 65000,  # R7 - 1st, SP (39張)
        6: 25000,  # R6 - UR, DiR, GR, PR(等級6) (197張)
        5: 8000,  # R5 - SRP, LRP, SCR, PR(等級5) (386張)
        4: 1200,  # R4 - SR, LR, PI, R(P'), PR(等級4) (571張)
        3: 80,  # R3 - R(P), LC(P), L(P), C(P'), PR(等級3) (720張)
        2: 15,  # R2 - R, LC/L, C(P), ST(P), TK(P), CO(P), PR(等級2) (1485張)
        1: 4,  # R1 - C, ST, TK, CO, PR(等級1) (856張)
    },
    "ongeki": {  # Ongeki 卡池
        5: 50000,  # R5 - UR (355張)
        4: 18000,  # R4 - SSR (93張)
        3: 800,  # R3 - SR (916張)
        2: 50,  # R2 - R (525張)
        1: 4,  # R1 - C (17張)
    },
    "sve": {  # Shadowverse Evolve 卡池 - 根據平方根增長溢價模型
        7: 70000,  # R7 - 0.01% 機率
        6: 28000,  # R6 - 0.05% 機率
        5: 9000,  # R5 - 0.50% 機率
        4: 1500,  # R4 - 2.00% 機率
        3: 80,  # R3 - 8.00% 機率
        2: 15,  # R2 - 25.00% 機率
        1: 4,  # R1 - 64.44% 機率
    },
}

# 不同 pool_type 的描述後綴
POOL_DESCRIPTION_SUFFIX = {
    "special": "典藏版本",
    "vd": "情人節版本",
    "special_maid": "典藏女僕",
    # "ua" 不需要特殊後綴，將使用預設描述
}


def create_description(name, series, pool_type):
    """根據提供的格式生成描述"""
    suffix = POOL_DESCRIPTION_SUFFIX.get(pool_type, "")
    if suffix:
        return f"{name} from {series} ({suffix})"
    return f"{name} from {series}"


async def import_cards_from_file(file_path, pool_type, file_format_type):
    """從指定檔案匯入卡片資料"""
    cards_to_insert = []
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"錯誤：找不到檔案 {file_path}")
        return
    except json.JSONDecodeError:
        print(f"錯誤：解析 JSON 檔案 {file_path} 失敗")
        return

    print(f"開始處理檔案: {file_path}，卡池類型: {pool_type}，格式: {file_format_type}")

    # 處理 Shadowverse Evolve 的特殊 JSON 結構
    if file_format_type == "sve" and isinstance(data, dict) and "cards" in data:
        data = data["cards"]
        print(f"檢測到 Shadowverse Evolve 格式，共 {len(data)} 張卡片")

    for card_data in data:
        original_id = None
        name = None
        series = None
        image_url = None
        rarity_int = None

        if file_format_type == "mazoku":
            original_id = card_data.get("uuid")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_name_str = card_data.get("rarityName")
            if rarity_name_str:
                rarity_int = RARITY_MAP_MAZOKU.get(rarity_name_str)
            if not all([original_id, name, series, image_url, rarity_name_str]):
                print(
                    f"警告 (mazoku)：卡片資料不完整，跳過："
                    f"{card_data.get('uuid') or '未知ID'}"
                )
                continue
            if rarity_int is None:
                print(
                    f"警告 (mazoku)：卡片 '{name}' 的稀有度 '{rarity_name_str}' 無法對應，跳過。"
                )
                continue

        elif file_format_type == "valentine":
            original_id = card_data.get("id")
            name = card_data.get("name", card_data.get("character_name"))  # 'name' 優先
            series = card_data.get("series")
            image_url = card_data.get("mediaURL")
            rarity_str_from_json = card_data.get("rarity")  # 這是字串 "1", "2" 等
            if rarity_str_from_json:
                rarity_int = RARITY_MAP_VALENTINE.get(rarity_str_from_json)
            if not all([original_id, name, series, image_url, rarity_str_from_json]):
                print(
                    f"警告 (valentine)：卡片資料不完整，跳過："
                    f"{card_data.get('id') or '未知ID'}"
                )
                continue
            if rarity_int is None:
                print(
                    f"警告 (valentine)：卡片 '{name}' 的稀有度字串 '{rarity_str_from_json}' 無法對應，跳過。"
                )
                continue

        elif file_format_type == "hololive":
            original_id = card_data.get("uuid")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel")  # 直接是整數 1-7
            description = original_id  # 描述直接使用 original_id

            if not all(
                [original_id, name, series, image_url, isinstance(rarity_int, int)]
            ):
                print(
                    f"警告 (hololive)：卡片資料不完整或稀有度格式錯誤，跳過："
                    f"{card_data.get('uuid') or '未知ID'}"
                )
                continue
            if not 1 <= rarity_int <= 7:
                print(
                    f"警告 (hololive)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。"
                )
                continue

        elif file_format_type == "ua":  # 新增 Union Arena 格式處理
            original_id = card_data.get("original_id")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel")  # 直接是整數 1-7

            if not all(
                [original_id, name, series, image_url, isinstance(rarity_int, int)]
            ):
                print(
                    f"警告 (ua)：卡片資料不完整或稀有度格式錯誤，跳過："
                    f"{card_data.get('original_id') or '未知ID'}"
                )
                continue
            if not 1 <= rarity_int <= 7:
                print(
                    f"警告 (ua)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。"
                )
                continue

        elif file_format_type == "ptcg":  # 新增 Pokemon TCG 格式處理
            original_id = card_data.get("original_id")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel")  # 直接是整數 1-7

            if not all(
                [original_id, name, series, image_url, isinstance(rarity_int, int)]
            ):
                print(
                    f"警告 (ptcg)：卡片資料不完整或稀有度格式錯誤，跳過："
                    f"{card_data.get('original_id') or '未知ID'}"
                )
                continue
            if not 1 <= rarity_int <= 7:
                print(
                    f"警告 (ptcg)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。"
                )
                continue

        elif file_format_type == "ongeki":  # 新增 Ongeki 格式處理
            original_id_raw = card_data.get("original_id")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get(
                "MEDIAURL"
            )  # 注意：Ongeki 使用 MEDIAURL 而不是 mediaUrl
            rarity_int = card_data.get("rarityLevel")  # 直接是整數 1-5

            # 為 original_id 添加 ongeki 前綴
            if original_id_raw:
                original_id = f"ongeki{original_id_raw}"
            else:
                original_id = None

            if not all(
                [original_id_raw, name, series, image_url, isinstance(rarity_int, int)]
            ):
                print(
                    f"警告 (ongeki)：卡片資料不完整或稀有度格式錯誤，跳過："
                    f"{card_data.get('original_id') or '未知ID'}"
                )
                continue
            if not 1 <= rarity_int <= 5:
                print(
                    f"警告 (ongeki)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-5)，跳過。"
                )
                continue

        elif file_format_type == "sve":  # 新增 Shadowverse Evolve 格式處理
            original_id = card_data.get("uuid")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_str = card_data.get("rarity")  # 字符串格式 "1" 到 "7"

            # 將稀有度字符串轉換為整數
            try:
                rarity_int = int(rarity_str) if rarity_str else None
            except (ValueError, TypeError):
                rarity_int = None

            if not all([original_id, name, series, image_url, rarity_str]):
                print(
                    f"警告 (sve)：卡片資料不完整，跳過："
                    f"{card_data.get('uuid') or '未知ID'}"
                )
                continue
            if rarity_int is None or not 1 <= rarity_int <= 7:
                print(f"警告 (sve)：卡片 '{name}' 的稀有度 '{rarity_str}' 無效，跳過。")
                continue
        else:
            print(f"錯誤：未知的檔案格式類型 {file_format_type}")
            return

        sell_price = PRICE_CONFIG.get(pool_type, {}).get(rarity_int)
        if sell_price is None:
            print(
                f"警告：卡片 '{name}' (稀有度 {rarity_int}) 在池子 '{pool_type}' "
                f"中找不到價格設定，將使用預設售價 10。"
            )
            sell_price = 10  # schema.sql 中的預設值

        description = create_description(name, series, pool_type)

        cards_to_insert.append(
            {
                "original_id": original_id,
                "name": name,
                "series": series,
                "image_url": image_url,
                "description": description,
                "sell_price": sell_price,
                "pool_type": pool_type,
                "rarity": rarity_int,
                # current_market_sell_price, last_price_update_at 使用資料庫預設或NULL
            }
        )

    await insert_cards_to_db(cards_to_insert)


async def insert_cards_to_db(cards):
    """將卡片資料插入到資料庫"""
    if not cards:
        print("沒有卡片需要插入。")
        return

    pool = get_pool()
    if not pool:
        print("數據庫連接池未初始化。")
        return

    inserted_count = 0
    skipped_count = 0

    async with pool.acquire() as conn:
        async with conn.transaction():
            # 先重置序列值以避免主鍵衝突
            await conn.execute(
                "SELECT setval('gacha_master_cards_card_id_seq', (SELECT COALESCE(MAX(card_id), 0) + 1 FROM gacha_master_cards), false);"
            )

            # schema.sql 中 gacha_master_cards 的 original_id 有 UNIQUE 約束
            # 因此，如果 original_id 已存在，插入會失敗或被 ON CONFLICT 處理
            insert_query = """
            INSERT INTO public.gacha_master_cards
            (original_id, name, series, image_url, description, sell_price, pool_type, rarity, creation_date)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
            ON CONFLICT (original_id) DO NOTHING
            RETURNING card_id;
            """

            for card in cards:
                try:
                    result = await conn.fetchval(
                        insert_query,
                        card["original_id"],
                        card["name"],
                        card["series"],
                        card["image_url"],
                        card["description"],
                        card["sell_price"],
                        card["pool_type"],
                        card["rarity"],
                    )
                    if result:
                        inserted_count += 1
                    else:
                        skipped_count += 1
                        print(
                            f"資訊：卡片 original_id '{card['original_id']}' "
                            f"({card['name']}) 已存在，跳過。"
                        )
                except Exception as e:
                    print(
                        f"資料庫錯誤，插入卡片 '{card.get('name')}' "
                        f"(ID: {card.get('original_id')}) 失敗: {e}"
                    )
                    # 繼續處理下一張卡片

    print(f"成功插入 {inserted_count} 張卡片。")
    if skipped_count > 0:
        print(f"因 original_id 已存在或其他原因跳過 {skipped_count} 張卡片。")


async def main():
    parser = argparse.ArgumentParser(description="匯入卡片資料到資料庫")
    parser.add_argument(
        "file_format_type",
        choices=["mazoku", "valentine", "hololive", "ua", "ptcg", "ongeki", "sve"],
        help=(
            "要匯入的檔案格式類型 ('mazoku', 'valentine', 'hololive', 'ua', "
            "'ptcg', 'ongeki' 或 'sve')"
        ),
    )
    parser.add_argument("file_path", type=str, help="JSON 檔案的路徑")
    parser.add_argument(
        "pool_type", type=str, help="卡池類型 (例如: special_maid, vd, ptcg, sve)"
    )

    args = parser.parse_args()

    print(
        f"準備匯入 {args.file_format_type} 格式的檔案: {args.file_path} "
        f"到卡池 {args.pool_type}"
    )

    await setup_connections()
    try:
        await import_cards_from_file(
            args.file_path, args.pool_type, args.file_format_type
        )
    finally:
        await close_connections()

    print("\n匯入流程結束。")
    print("提醒：根據您的指示，可能需要手動執行『系統卡池更新調用』。")


if __name__ == "__main__":
    asyncio.run(main())
