"""
都不帶我玩GIF疊加模塊
將指令使用者的頭像疊加到GIF中間正上方，並在底部添加文字
"""

from pathlib import Path
from typing import List

from PIL import Image, ImageDraw, ImageFont

from utils.logger import logger

from .avatar_gif_overlay import fetch_avatar, save_gif
from .config import get_gif_config


def get_resampling_filter(filter_name: str):
    """獲取 Pillow 重採樣濾鏡，兼容新舊版本"""
    try:
        # Pillow 10.0.0+ 使用 Image.Resampling
        return getattr(Image.Resampling, filter_name)
    except AttributeError:
        # 舊版本 Pillow 直接從 Image 獲取
        return getattr(Image, filter_name, 1)  # 1 是 LANCZOS 的默認值


def convert_frames_for_play(frames: List[Image.Image]) -> List[Image.Image]:
    """專門為"都不帶我玩"設計的調色板轉換函數，不設置透明度"""
    if not frames:
        return []

    logger.info("正在為都不帶我玩的 %s 幀生成調色板...", len(frames))

    try:
        # 創建合併圖像用於生成全局調色板
        combined_image = Image.new(
            "RGBA", (frames[0].width, frames[0].height * len(frames))
        )
        for i, frame in enumerate(frames):
            combined_image.paste(frame, (0, i * frame.height))

        # 生成全局調色板，使用黑色背景
        combined_rgb = Image.new("RGB", combined_image.size, (0, 0, 0))
        combined_rgb.paste(combined_image, (0, 0), combined_image)
        palette_image = combined_rgb.quantize(colors=256, method=2)

        logger.info("都不帶我玩調色板生成完畢，開始轉換所有幀...")

        # 轉換所有幀（不設置透明度）
        converted_frames = []
        for frame in frames:
            if frame.mode == "RGBA":
                frame_rgb = Image.new("RGB", frame.size, (0, 0, 0))
                frame_rgb.paste(frame, (0, 0), frame)
                frame = frame_rgb
            converted_frame = frame.quantize(
                palette=palette_image, dither=Image.Dither.FLOYDSTEINBERG
            )
            converted_frames.append(converted_frame)

        logger.info("都不帶我玩所有幀轉換完成。")
        return converted_frames

    except Exception as e:
        logger.warning("都不帶我玩調色板轉換失敗，回退到原始方法: %s", e)
        return [
            frame.convert("P", palette=Image.ADAPTIVE, colors=256)  # type: ignore
            for frame in frames
        ]


def wrap_text(text, font, max_width):
    """
    智能文字換行

    參數:
        text (str): 要換行的文字
        font (ImageFont): 字體對象
        max_width (int): 最大寬度

    返回:
        list: 換行後的文字列表
    """
    lines = []
    words = list(text)  # 中文按字符分割
    current_line = ""

    for word in words:
        test_line = current_line + word
        bbox = font.getbbox(test_line)
        text_width = bbox[2] - bbox[0]

        if text_width <= max_width:
            current_line = test_line
        else:
            if current_line:
                lines.append(current_line)
                current_line = word
            else:
                lines.append(word)

    if current_line:
        lines.append(current_line)

    return lines


def draw_text_with_outline(
    draw,
    position,
    text,
    font,
    text_color,
    outline_color=(255, 255, 255),
    outline_width=2,
):
    """
    繪製帶描邊的文字

    參數:
        draw (ImageDraw): 繪圖對象
        position (tuple): 文字位置 (x, y)
        text (str): 文字內容
        font (ImageFont): 字體對象
        text_color (tuple): 文字顏色
        outline_color (tuple): 描邊顏色
        outline_width (int): 描邊寬度
    """
    x, y = position

    # 繪製描邊
    for dx in range(-outline_width, outline_width + 1):
        for dy in range(-outline_width, outline_width + 1):
            if dx != 0 or dy != 0:
                draw.text((x + dx, y + dy), text, font=font, fill=outline_color)

    # 繪製主文字
    draw.text((x, y), text, font=font, fill=text_color)


def generate_play_frames_sync(command_user_avatar, target_username, config):
    """
    生成都不帶我玩動畫幀

    參數:
        command_user_avatar (PIL.Image): 指令使用者的頭像
        target_username (str): 被tag用戶的用戶名
        config (dict): 配置參數

    返回:
        list: 處理後的幀列表
    """
    # 獲取配置參數
    frame_numbers = config["frame_numbers"]
    output_size = config["output_gif_size"]
    avatar_size = config["avatar_size"]
    play_config = config["play_phase"]

    # 載入字體
    font_path = Path.cwd() / play_config["text_area"]["font_path"]
    try:
        font = ImageFont.truetype(str(font_path), play_config["text_area"]["font_size"])
    except Exception:
        # 載入字體失敗時使用默認字體
        font = ImageFont.load_default()

    # 準備文字內容
    text_content = "今天，%s又沒有帶我玩，這個仇我先記下了。" % target_username

    # 文字換行
    wrapped_lines = wrap_text(text_content, font, play_config["text_area"]["max_width"])

    # 調整頭像大小
    resized_avatar = command_user_avatar.resize(
        avatar_size, get_resampling_filter("LANCZOS")
    )

    # 計算頭像位置（中間正上方）
    avatar_x = (output_size[0] - avatar_size[0]) // 2 + play_config["avatar_offset"][0]
    avatar_y = play_config["avatar_offset"][1]

    # 載入所有背景幀
    background_frames = []
    source_pattern = config["source_path_pattern"]

    for frame_num in frame_numbers:
        frame_path = Path(source_pattern.format(frame_num))
        if frame_path.exists():
            frame = Image.open(frame_path).convert("RGBA")
            if frame.size != output_size:
                frame = frame.resize(output_size, get_resampling_filter("LANCZOS"))
            background_frames.append(frame)
        else:
            # 找不到幀文件時跳過
            continue

    if not background_frames:
        raise ValueError("沒有找到任何背景幀文件")

    # 生成所有動畫幀
    all_frames = []

    for frame_index in range(len(background_frames)):
        # 創建新的幀畫布
        frame_canvas = Image.new("RGBA", output_size, (0, 0, 0, 0))

        # 先放置頭像（底層）
        frame_canvas.paste(resized_avatar, (avatar_x, avatar_y), resized_avatar)

        # 疊加背景幀
        frame_canvas = Image.alpha_composite(
            frame_canvas, background_frames[frame_index]
        )

        # 添加文字（頂層）
        draw = ImageDraw.Draw(frame_canvas)

        # 計算文字起始位置
        text_x = play_config["text_area"]["position"][0]
        text_y = play_config["text_area"]["position"][1]
        line_spacing = play_config["text_area"]["line_spacing"]

        # 繪製每一行文字
        for i, line in enumerate(wrapped_lines):
            line_y = text_y + i * (play_config["text_area"]["font_size"] + line_spacing)
            draw_text_with_outline(
                draw,
                (text_x, line_y),
                line,
                font,
                play_config["text_area"]["text_color"],
                outline_color=(255, 255, 255),
                outline_width=1,
            )

        all_frames.append(frame_canvas)

    return all_frames


async def overlay_play_gif(
    gif_path_identifier: str, command_user, target_user, output_path=None
):
    """
    處理都不帶我玩GIF的疊加邏輯

    參數:
        gif_path_identifier (str): GIF路徑標識符 ("都不帶我玩.gif")
        command_user (discord.User): 指令使用者
        target_user (discord.User): 被tag的用戶
        output_path (str, 可選): 輸出文件路徑，默認為臨時文件

    返回:
        str: 生成的GIF文件路徑
    """
    # 獲取配置
    config = get_gif_config(gif_path_identifier)

    # 獲取指令使用者的頭像
    command_user_avatar = await fetch_avatar(command_user)
    if command_user_avatar is None:
        raise ValueError("無法獲取指令使用者頭像")

    # 將PIL操作移到線程中避免阻塞
    import asyncio

    if command_user_avatar:
        command_user_avatar = await asyncio.to_thread(
            lambda: command_user_avatar.convert("RGBA") if command_user_avatar else None
        )
    else:
        raise ValueError("無法獲取用戶頭像")

    # 獲取被tag用戶的用戶名
    target_username = target_user.display_name if target_user else "某人"

    # 生成所有動畫幀 (移到線程中避免阻塞)
    processed_frames = await asyncio.to_thread(
        generate_play_frames_sync, command_user_avatar, target_username, config
    )

    if not processed_frames:
        raise ValueError("未能為都不帶我玩生成任何影格")

    # 創建輸出文件路徑 (移到線程中避免阻塞)
    if output_path is None:
        output_dir = Path("image_processing") / "temp"
        await asyncio.to_thread(output_dir.mkdir, parents=True, exist_ok=True)
        target_id = target_user.id if target_user else "none"
        output_path = str(
            output_dir / ("play_%s_%s.gif" % (command_user.id, target_id))
        )

    # 使用專門的調色板轉換，黑色背景且不設置透明度 (移到線程中避免阻塞)
    converted_frames = await asyncio.to_thread(
        convert_frames_for_play, processed_frames
    )

    # 保存GIF文件，不設置透明度以保留黑色背景 (移到線程中避免阻塞)
    frame_duration = config.get("frame_duration", 50)
    if not isinstance(frame_duration, int):
        frame_duration = 50
    await asyncio.to_thread(
        save_gif,
        converted_frames,
        output_path,
        frame_duration,
        transparency=None,
    )

    logger.info("都不帶我玩GIF處理完成，返回路徑: %s", output_path)
    return output_path
