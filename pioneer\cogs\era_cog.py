"""
Pioneer System 時代 Cog
提供與時代晉升相關的 Discord 命令
"""

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.core.processors.era_processor import EraProcessor
from pioneer.models.pioneer_models import ActionConfig


class EraCog(commands.Cog):
    """處理與時代晉升相關的命令"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # 創建處理器實例
        assert game_data is not None
        self.era_processor = EraProcessor(game_data, repositories)

    async def _get_era_embed(self, user_id: int) -> discord.Embed:
        """生成顯示當前時代和晉升需求的 Embed"""
        assert game_data is not None
        profile = await repositories.get_pioneer_profile(user_id)
        current_era_config = game_data.get_era_config(profile.current_era)

        if not current_era_config:
            return discord.Embed(
                title="❌ 錯誤",
                description="無法獲取當前時代信息，請聯繫管理員。",
                color=discord.Color.red(),
            )

        next_era_config = game_data.get_era_config(profile.current_era + 1)

        embed = discord.Embed(
            title="🕰️ 時代進程",
            description=f"你目前處於 **{current_era_config.name}**。",
            color=discord.Color.dark_gold(),
        )
        embed.add_field(
            name="當前時代描述",
            value=f"*{current_era_config.description}*",
            inline=False,
        )

        if not next_era_config:
            embed.add_field(
                name="下一個時代", value="你已達到時代的終點！", inline=False
            )
        else:
            from pioneer.exceptions import RequirementsNotMetError
            from pioneer.utils.requirement_checker import check_requirements

            try:
                await check_requirements(
                    user_id, next_era_config.requirements, game_data, repositories
                )
                requirements_met = True
                missing_req_message = "所有條件已滿足！"
            except RequirementsNotMetError as e:
                requirements_met = False
                missing_req_message = e.message

            req_value = ""
            for req in next_era_config.requirements:
                # 這部分可以做得更精細，以顯示每個需求的當前進度
                req_value += f"• {req.get('type')}: ...\n"  # 簡化顯示

            cost_value = ""
            for cost in next_era_config.costs:
                if cost["type"] == "currency":
                    cost_value += f"• {cost['amount']:,} 油幣\n"
                elif cost["type"] == "item":
                    item_name = game_data.get_item_name(cost["item_id"])
                    cost_value += f"• {item_name} x{cost['quantity']}\n"

            embed.add_field(
                name=f"晉升到 **{next_era_config.name}**",
                value=f"*{next_era_config.description}*",
                inline=False,
            )
            embed.add_field(name="晉升需求", value=req_value or "無", inline=True)
            embed.add_field(name="晉升成本", value=cost_value or "無", inline=True)

            if not requirements_met:
                embed.add_field(
                    name="❌ 未滿足條件", value=missing_req_message, inline=False
                )
                embed.set_footer(text="滿足所有條件後，使用 /advance_era 嘗試晉升")
            else:
                embed.set_footer(text="所有條件已滿足！使用 /advance_era 進行晉升")

        return embed

    @app_commands.command(name="era", description="查看當前時代信息和晉升需求")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def era(self, interaction: discord.Interaction):
        """顯示時代信息"""
        await interaction.response.defer(ephemeral=True)
        embed = await self._get_era_embed(interaction.user.id)
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="advance_era", description="嘗試晉升到下一個時代")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def advance_era(self, interaction: discord.Interaction):
        """嘗試晉升時代"""
        await interaction.response.defer(ephemeral=True)
        user_id = interaction.user.id

        # 創建一個虛擬的 action_config 以符合處理器簽名
        action_config = ActionConfig(
            name="advance_era",
            type="era_advance",
            energy_cost=0,
            outputs=[],
        )
        result = await self.era_processor.execute(user_id, action_config, {})

        if result.success:
            color = discord.Color.green()
        else:
            color = discord.Color.red()

        embed = discord.Embed(title="時代晉升", description=result.message, color=color)
        await interaction.followup.send(embed=embed, ephemeral=True)


async def setup(bot: commands.Bot):
    await bot.add_cog(EraCog(bot))
