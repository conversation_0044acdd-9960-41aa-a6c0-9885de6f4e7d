from typing import List

import discord
from discord import app_commands
from discord.ext import commands

from pioneer import repositories
from pioneer.core.game_data_loader import game_data
from pioneer.exceptions import PioneerError
from pioneer.modules import action_module
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


async def get_recipe_choices(
    interaction: discord.Interaction, current: str
) -> List[app_commands.Choice[str]]:
    """
    從 GameDataLoader 獲取配方選項列表，包含製作材料信息
    """
    choices = []
    try:
        if not game_data:
            return []
        all_recipes = game_data.get_all_recipes()
        all_items = game_data.get_all_items()

        for recipe_id, recipe_config in all_recipes.items():
            # 如果有搜索條件，進行過濾
            if (
                current
                and current.lower() not in recipe_config.name.lower()
                and current.lower() not in recipe_id.lower()
            ):
                continue

            # 構建材料信息
            materials = []
            for input_item in recipe_config.inputs:
                item_id = str(input_item["item_id"])
                item_config = all_items.get(item_id)
                item_name = item_config.name if item_config else item_id
                materials.append(f"{item_name}x{input_item['quantity']}")

            # 構建顯示名稱：配方名稱 (材料1, 材料2...)
            if materials:
                materials_text = ", ".join(materials)
                display_name = f"{recipe_config.name} ({materials_text})"
            else:
                display_name = recipe_config.name

            # Discord 選項名稱限制為 100 字符
            if len(display_name) > 100:
                display_name = display_name[:97] + "..."

            choices.append(app_commands.Choice(name=display_name, value=recipe_id))

        # 限制返回的選項數量（Discord 限制 25 個）
        return choices[:25]

    except Exception as e:
        logger.error("從 game_data 載入配方配置失敗: %s", e)
        return []


class CraftCog(commands.Cog):
    """開拓者製作指令"""

    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="craft", description="執行製作動作")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        recipe="要製作的配方（顯示所需材料）",
        quantity="製作數量 (可輸入 'max' 來製作最大數量)",
    )
    @app_commands.autocomplete(recipe=get_recipe_choices)
    async def craft(
        self, interaction: discord.Interaction, recipe: str, quantity: str = "1"
    ):
        """執行製作動作"""
        await interaction.response.defer(thinking=False)
        user_id = interaction.user.id
        await repositories.create_pioneer_profile(user_id)
        params = {"recipe_id": recipe, "quantity": quantity}
        result = await action_module.execute_action(user_id, "craft", params)

        if result.success:
            embed = SuccessEmbed(title="🔨 製作成功", description=result.message)

            if result.rewards:
                reward_text = []
                for reward in result.rewards:
                    if reward["type"] == "item":
                        item_name = reward["item_id"]
                        if game_data:
                            item_config = game_data.get_item_config(reward["item_id"])
                            if item_config:
                                item_name = item_config.name
                        reward_text.append(f"• {item_name} x{reward['quantity']}")

                if reward_text:
                    embed.add_field(
                        name="製作完成", value="\n".join(reward_text), inline=False
                    )

            if result.xp_gained:
                xp_text = []
                for skill_id, xp in result.xp_gained.items():
                    skill_name = skill_id
                    if game_data:
                        skill_name = game_data.get_skill_name(skill_id)
                    xp_text.append(f"• {skill_name}: +{xp} XP")

                if xp_text:
                    embed.add_field(
                        name="獲得經驗", value="\n".join(xp_text), inline=False
                    )
            await interaction.followup.send(embed=embed)
        else:
            raise PioneerError(result.message)


async def setup(bot):
    """設置 Cog"""
    await bot.add_cog(CraftCog(bot))
