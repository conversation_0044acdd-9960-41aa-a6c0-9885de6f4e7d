"""
RPG技能管理系統的Discord Cog
處理技能裝備、卸下、查看等相關的Discord命令和交互
支持双表卡牌实例系统的分离式管理
使用純異常模式進行錯誤處理
職責分離：Cog只負責命令處理，embed創建交給View層
"""

from typing import Optional

import discord
from discord import app_commands
from discord.ext import commands

from bot import CustomAutoShardedBot
from gacha.constants import RarityLevel
from gacha.models.filters import CollectionFilters
from gacha.services import collection_service
from gacha.views.ui_components.confirmation import ConfirmationView
from rpg_system.exceptions import PlayerStateError, RPGSystemError
from rpg_system.services import (
    player_card_management_service,
    player_global_skill_proficiency_service,
)
from rpg_system.views.embeds.skill_embeds import SkillEmbedBuilder
from rpg_system.views.global_skills_view import GlobalSkillsView
from utils.logger import logger


class SkillManagementCog(commands.Cog, name="RPG技能管理"):
    """處理RPG技能管理相關指令"""

    def __init__(self, bot: CustomAutoShardedBot):
        self.bot = bot
        logger.info("SkillManagementCog initialized.")

    @app_commands.command(
        name="card_details", description="查看卡牌詳細信息（支持特殊实例）"
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        collection_id="卡牌收藏ID（可以是默认配置或特殊配置的ID）",
        show_all_instances="是否显示该卡牌的所有实例（默认只显示指定ID的详情）",
    )
    async def card_details(
        self,
        interaction: discord.Interaction,
        collection_id: int,
        show_all_instances: bool = False,
    ):
        """
        查看指定卡牌的詳細信息
        """
        await interaction.response.defer()
        user_id = interaction.user.id

        card_details = (
            await player_card_management_service.get_card_details_for_display(
                user_id, collection_id
            )
        )
        if not card_details:
            raise PlayerStateError("找不到指定的卡牌或您沒有權限查看。")

        if show_all_instances:
            card_id = card_details["card_id"]
            instances = await player_card_management_service.get_card_instances(
                user_id, card_id
            )
            embed = SkillEmbedBuilder.create_card_instances_embed(
                instances, card_details.get("name", "Unknown Card")
            )
            await interaction.followup.send(embed=embed)
        else:
            embed = SkillEmbedBuilder.create_card_details_embed(card_details)
            await interaction.followup.send(embed=embed)

    @commands.cooldown(1, 2.0, commands.BucketType.user)
    @app_commands.command(name="skill_proficiency", description="查看技能熟練度")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def skill_proficiency(self, interaction: discord.Interaction):
        """
        查看玩家的全局技能熟練度
        """
        await interaction.response.defer()
        user_id = interaction.user.id
        learned_skills = (
            await player_global_skill_proficiency_service.get_player_learned_skills(
                user_id
            )
        )
        embed = SkillEmbedBuilder.create_skill_proficiency_embed(learned_skills)
        await interaction.followup.send(embed=embed)

    @app_commands.command(name="global_skills", description="全局技能查看和管理")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def global_skills(self, interaction: discord.Interaction):
        """
        查看和管理全局技能
        """
        await interaction.response.defer()
        user_id = interaction.user.id
        learned_skills = (
            await player_global_skill_proficiency_service.get_player_learned_skills(
                user_id
            )
        )
        skills_view = GlobalSkillsView(
            bot=self.bot,
            user=interaction.user,
            skills_data=learned_skills,
            skill_proficiency_service=player_global_skill_proficiency_service,
        )
        initial_embed = await skills_view.get_current_page_embed()
        await interaction.followup.send(embed=initial_embed, view=skills_view)

    @app_commands.command(
        name="sacrifice_cards", description="卡牌獻祭獲取技能經驗 (統一接口)"
    )
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        card_id="卡牌ID，直接指定要獻祭的卡牌 (如果同時提供名稱，ID優先)",
        card_name="卡牌名稱，根據名稱尋找卡牌",
        series="系列名稱，指定要獻祭的卡牌系列",
        rarity="稀有度，指定要獻祭的卡牌稀有度 (C, R, SR, SSR, UR, LR, EX)",
        pool_type="卡池類型，指定要獻祭的卡牌卡池",
        operation="操作類型：'single'(獻祭1張)、'keep_one'(獻祭到剩1張)、'all'(全部獻祭)",
        include_favorites="是否包含最愛卡片（默認為False，True則包含）",
    )
    async def sacrifice_cards(
        self,
        interaction: discord.Interaction,
        card_id: Optional[int] = None,
        card_name: Optional[str] = None,
        series: Optional[str] = None,
        rarity: Optional[str] = None,
        pool_type: Optional[str] = None,
        operation: str = "single",
        include_favorites: bool = False,
    ):
        """
        卡牌獻祭命令
        """
        await interaction.response.defer(ephemeral=False, thinking=True)
        user_id = interaction.user.id
        filters = CollectionFilters()
        final_card_id: Optional[int] = card_id

        if card_name and (not final_card_id):
            name_filter_for_search = CollectionFilters(card_name=card_name)
            matching_card_ids = await collection_service.get_filtered_card_ids(
                user_id, name_filter_for_search
            )
            if not matching_card_ids:
                raise PlayerStateError(f'找不到名稱包含 "{card_name}" 的卡片。')
            elif len(matching_card_ids) > 1:
                raise PlayerStateError(
                    f'找到多張名稱包含 "{card_name}" 的卡片，請使用更具體的名稱或直接使用卡片ID。'
                )
            final_card_id = matching_card_ids[0]

        if final_card_id:
            filters.set_card_id(final_card_id)
        if series:
            filters.series = series
        if rarity:
            try:
                rarity_num = RarityLevel[rarity.upper()].value
                filters.rarity_in = [rarity_num]
            except (ValueError, KeyError) as e:
                raise RPGSystemError(f"無效的稀有度: {rarity}") from e
        if pool_type:
            filters.pool_type = pool_type

        operation_type_str = operation.lower()
        valid_operations = ["single", "all", "keep_one"]
        if operation_type_str not in valid_operations:
            raise RPGSystemError(
                f"無效的操作類型。請使用: {', '.join(valid_operations)}"
            )

        force_sacrifice_flag = include_favorites
        needs_confirmation = False
        confirm_title = ""
        confirm_description = ""
        confirm_field_value = ""

        is_broad_sacrifice_all = operation_type_str == "all" and (
            not filters.has_any_filter()
        )
        is_broad_sacrifice_keep_one = operation_type_str == "keep_one" and (
            not filters.has_any_filter()
        )

        if is_broad_sacrifice_all:
            needs_confirmation = True
            confirm_title = "⚠️ 危險警告：即將獻祭所有卡牌 ⚠️"
            confirm_description = f"你確定要獻祭收藏中所有{('**包括最愛**的' if force_sacrifice_flag else '**未標記為最愛**的')}卡牌來獲取技能經驗嗎？\n此操作**無法撤銷**！"
            confirm_field_value = "所有卡牌" + (
                " (包括最愛)" if force_sacrifice_flag else " (不包括最愛)"
            )
        elif is_broad_sacrifice_keep_one:
            needs_confirmation = True
            confirm_title = "⚠️ 警告：即將獻祭所有重複卡牌 ⚠️"
            confirm_description = f"你確定要獻祭所有重複卡牌來獲取技能經驗，對每種卡片保留1張 ({('**包括最愛**' if force_sacrifice_flag else '**不包括最愛**')})嗎？\n此操作**無法撤銷**！"
            confirm_field_value = "所有重複卡牌，每種保留1張" + (
                " (包括最愛)" if force_sacrifice_flag else " (不包括最愛)"
            )
        elif force_sacrifice_flag and filters.has_any_filter():
            needs_confirmation = True
            confirm_title = "⚠️ 確認獻祭最愛卡牌 ⚠️"
            confirm_description = "你選擇了包含最愛卡片進行獻祭。此操作會將符合篩選條件的最愛卡片一併獻祭來獲取技能經驗。\n此操作**無法撤銷**！"
            confirm_field_value = "符合篩選條件的最愛卡片"

        if not filters.has_any_filter() and operation_type_str == "single":
            await interaction.followup.send(
                embed=SkillEmbedBuilder.create_sacrifice_help_embed(),
                ephemeral=True,
            )
            return

        async def perform_sacrifice_action(
            current_interaction: discord.Interaction,
        ):
            result = (
                await player_global_skill_proficiency_service.sacrifice_cards_universal(
                    user_id=user_id,
                    filters=filters,
                    operation_type=operation_type_str,
                    force_sacrifice=force_sacrifice_flag,
                )
            )
            success_embed = SkillEmbedBuilder.create_sacrifice_result_embed(
                operation_type=operation, result=result
            )
            await current_interaction.response.edit_message(
                embed=success_embed, view=None
            )

        if needs_confirmation:

            async def on_confirm_callback(
                confirm_interaction: discord.Interaction,
            ):
                await perform_sacrifice_action(confirm_interaction)

            async def on_cancel_callback(
                cancel_interaction: discord.Interaction,
            ):
                cancel_embed = discord.Embed(
                    title="獻祭操作已取消",
                    description="您已取消獻祭操作，沒有任何卡片被獻祭。",
                    color=discord.Color.blue(),
                )
                await cancel_interaction.response.edit_message(
                    embed=cancel_embed, view=None
                )

            embed = discord.Embed(
                title=confirm_title,
                description=confirm_description,
                color=discord.Color.orange(),
            )
            if confirm_field_value:
                embed.add_field(
                    name="操作影響", value=confirm_field_value, inline=False
                )
            view = ConfirmationView(
                bot=self.bot,
                user_id=user_id,
                on_confirm=on_confirm_callback,
                on_cancel=on_cancel_callback,
                confirm_label="確認獻祭",
                cancel_label="取消",
                confirm_style=discord.ButtonStyle.danger,
                cancel_style=discord.ButtonStyle.secondary,
            )
            await interaction.followup.send(embed=embed, view=view, ephemeral=False)
        else:
            # For direct actions, we need to send a new message, not edit one.
            result = (
                await player_global_skill_proficiency_service.sacrifice_cards_universal(
                    user_id=user_id,
                    filters=filters,
                    operation_type=operation_type_str,
                    force_sacrifice=force_sacrifice_flag,
                )
            )
            success_embed = SkillEmbedBuilder.create_sacrifice_result_embed(
                operation_type=operation, result=result
            )
            await interaction.followup.send(embed=success_embed)

    @app_commands.command(name="transfer_skill", description="卡牌間技能轉移")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        source_collection_id="源卡牌收藏ID",
        source_slot="源槽位（主動技能：0,1,2 或 被動技能：slot_0,slot_1,slot_2）",
        target_collection_id="目標卡牌收藏ID",
        target_slot="目標槽位（必須與源槽位類型相同）",
    )
    async def transfer_skill(
        self,
        interaction: discord.Interaction,
        source_collection_id: int,
        source_slot: str,
        target_collection_id: int,
        target_slot: str,
    ):
        """
        卡牌間技能轉移
        """
        await interaction.response.defer()
        user_id = interaction.user.id
        result = await player_card_management_service.transfer_skill_between_cards(
            user_id,
            source_collection_id,
            source_slot,
            target_collection_id,
            target_slot,
        )
        success_embed = SkillEmbedBuilder.create_skill_transfer_result_embed(result)
        await interaction.followup.send(embed=success_embed)


async def setup(bot: CustomAutoShardedBot):
    """設置Cog"""
    await bot.add_cog(SkillManagementCog(bot))
    logger.info("SkillManagementCog added successfully.")
