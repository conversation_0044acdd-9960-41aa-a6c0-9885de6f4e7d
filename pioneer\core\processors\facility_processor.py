"""
Pioneer System 設施處理器
處理設施相關動作（建造、升級、管理等）
"""

from datetime import datetime
from typing import Any, Dict

import gacha.services.economy_service as economy_service
from pioneer.exceptions import (
    PioneerActionError,
    PioneerDatabaseError,
    PioneerInsufficientFundsError,
    PioneerInsufficientItemsError,
    PioneerNotFoundError,
    PioneerValidationError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult
from utils.logger import logger

from .base_processor import BaseProcessor


class FacilityProcessor(BaseProcessor):
    """設施動作處理器"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """執行設施動作

        Args:
            user_id: 用戶ID
            action_config: 動作配置
            params: 動作參數

        Returns:
            ActionResult: 執行結果
        """
        action_type = action_config.type

        if action_type == "build_facility":
            return await self._build_facility(user_id, action_config, params)
        elif action_type == "upgrade_facility":
            return await self._upgrade_facility(user_id, action_config, params)
        elif action_type == "sell":
            return await self._process_shop_sale(user_id, action_config, params)
        elif action_type == "stock_shop_item":
            return await self._stock_shop_item(user_id, action_config, params)
        else:
            raise PioneerValidationError(f"未知的設施動作類型: {action_type}")

    async def _build_facility(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """建造設施 (已重構)"""
        try:
            facility_type = params.get("facility_type")
            if not facility_type:
                raise PioneerValidationError("操作失敗：未指定要建造的設施類型。")

            # 獲取設施配置
            facility_config = self.game_data.get_facility_config(facility_type)
            if not facility_config:
                raise PioneerNotFoundError(
                    f"找不到設施類型「{facility_type}」的設定資料。"
                )

            # 檢查是否可建造
            if not facility_config.is_buildable:
                raise PioneerActionError(
                    f"設施「{facility_config.name}」目前不可建造。"
                )

            # 檢查並收集所有需求
            # 1. 檢查油幣
            if facility_config.cost_oil and facility_config.cost_oil > 0:
                balance_info = await economy_service.get_balance(user_id)
                current_balance = balance_info.get("balance", 0)
                if current_balance < facility_config.cost_oil:
                    raise PioneerInsufficientFundsError(
                        f"油幣不足，建造「{facility_config.name}」需要 {facility_config.cost_oil:,} 油幣，但您只有 {current_balance:,}。"
                    )

            # 2. 檢查物品
            if facility_config.required_items:
                missing_items_messages = []
                for item_req in facility_config.required_items:
                    item_id = item_req["item_id"]
                    required_quantity = item_req["quantity"]
                    current_quantity = await self.repository.get_item_quantity(
                        user_id, item_id
                    )
                    if current_quantity < required_quantity:
                        item_config = self.game_data.get_item_config(item_id)
                        item_name = item_config.name if item_config else item_id
                        missing_items_messages.append(
                            f"• {item_name}: 需要 {required_quantity}, 現有 {current_quantity}"
                        )
                if missing_items_messages:
                    raise PioneerInsufficientItemsError(
                        "建造所需材料不足：\n" + "\n".join(missing_items_messages)
                    )

            # 消耗所有資源
            if facility_config.cost_oil and facility_config.cost_oil > 0:
                await economy_service.award_oil(
                    user_id=user_id,
                    amount=-facility_config.cost_oil,
                    transaction_type="pioneer:build_facility",
                    reason=f"Build facility {facility_type}",
                )
            if facility_config.required_items:
                await self.repository.remove_items(
                    user_id, facility_config.required_items
                )

            # 創建設施
            facility_name = params.get("facility_name", facility_config.name)
            facility_id = await self.repository.create_facility(
                user_id=user_id,
                facility_type=facility_type,
                facility_name=facility_name,
            )

            await self._initialize_facility_slots(facility_id, facility_config)

            from pioneer.services.task_updater import task_updater

            await task_updater.check_and_update_tasks(
                user_id, "facility_build", facility_type=facility_type, quantity=1
            )

            return ActionResult.success_result(
                message=f"恭喜！您已成功建造「{facility_config.name}」。",
                rewards=[
                    {
                        "type": "facility",
                        "facility_id": facility_id,
                        "facility_type": facility_type,
                    }
                ],
            )
        except (
            PioneerNotFoundError,
            PioneerActionError,
            PioneerInsufficientFundsError,
            PioneerInsufficientItemsError,
            PioneerValidationError,
        ) as e:
            # 對於已知的業務邏輯錯誤，直接重新拋出
            raise e
        except Exception as e:
            logger.error("建造設施時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError(
                f"建造設施時發生系統錯誤，請聯繫管理員。詳細資訊: {e}"
            ) from e

    async def _upgrade_facility(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """升級設施"""
        try:
            facility_id = params.get("facility_id")
            upgrade_type = params.get("upgrade_type")

            if not facility_id or not upgrade_type:
                raise PioneerValidationError("操作失敗：必須提供設施ID和升級類型。")

            # 獲取設施信息
            facility = await self.repository.get_facility(facility_id)
            if not facility or facility.user_id != user_id:
                raise PioneerNotFoundError(
                    f"找不到ID為 {facility_id} 的設施，或該設施不屬於您。"
                )

            # 獲取設施配置
            facility_config = self.game_data.get_facility_config(facility.facility_type)
            if not facility_config:
                raise PioneerNotFoundError(
                    f"找不到設施類型「{facility.facility_type}」的設定資料。"
                )

            # 檢查升級配置
            upgrade_config = facility_config.upgrades.get(upgrade_type)
            if not upgrade_config:
                raise PioneerNotFoundError(
                    f"在設施「{facility_config.name}」上找不到名為「{upgrade_type}」的升級選項。"
                )

            # 檢查是否已經升級
            if upgrade_type in facility.upgrades:
                raise PioneerActionError(
                    f"設施「{facility.facility_name or facility_config.name}」已經擁有「{upgrade_type}」升級，無需重複操作。"
                )

            # 計算升級成本
            cost = await self._calculate_upgrade_cost(facility, upgrade_config)
            total_cost = cost.get("oil", 0)

            # 檢查成本
            if total_cost > 0:
                balance_info = await economy_service.get_balance(user_id)
                current_balance = balance_info.get("balance", 0)
                if current_balance < total_cost:
                    raise PioneerInsufficientFundsError(
                        f"油幣不足，升級需要 {total_cost:,}，但您只有 {current_balance:,}。"
                    )

            # 消耗成本並應用升級
            if total_cost > 0:
                await economy_service.award_oil(
                    user_id=user_id,
                    amount=-total_cost,
                    transaction_type="pioneer:upgrade_facility",
                    reason=f"Upgrade {facility.facility_type} with {upgrade_type}",
                )
            await self.repository.add_facility_upgrade(facility_id, upgrade_type)

            from pioneer.services.task_updater import task_updater

            await task_updater.check_and_update_tasks(
                user_id, "facility_upgrade", upgrade_type=upgrade_type, quantity=1
            )

            return ActionResult.success_result(
                message=f"升級成功！「{facility.facility_name or facility_config.name}」已成功應用「{upgrade_type}」升級。",
                costs=[{"type": "oil", "amount": total_cost}],
            )
        except (
            PioneerNotFoundError,
            PioneerActionError,
            PioneerInsufficientFundsError,
            PioneerValidationError,
        ) as e:
            raise e
        except Exception as e:
            logger.error("升級設施時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError(
                f"升級設施時發生系統錯誤，請聯繫管理員。詳細資訊: {e}"
            ) from e

    async def _process_shop_sale(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """處理商店銷售"""
        try:
            facility_id = params.get("facility_id")
            if not facility_id:
                raise PioneerValidationError("操作失敗：未指定設施ID。")

            facility = await self.repository.get_facility(facility_id)
            if not facility or facility.user_id != user_id:
                raise PioneerNotFoundError(
                    f"找不到ID為 {facility_id} 的設施，或該設施不屬於您。"
                )

            facility_config = self.game_data.get_facility_config(facility.facility_type)
            if not facility_config:
                raise PioneerValidationError(
                    f"設施「{facility.facility_name}」的設定資料有誤或不存在。"
                )
            if facility_config.process_type != "sell":
                raise PioneerValidationError(
                    f"設施「{facility.facility_name or facility_config.name}」不支持銷售操作。"
                )

            shelf_slots = await self.repository.get_facility_slots(facility_id, "shelf")
            if not shelf_slots:
                return ActionResult.success_result(
                    message="商店貨架是空的，沒有任何銷售發生。"
                )

            total_earnings = 0
            sold_items_details = []

            time_passed_seconds = (
                datetime.now() - facility.last_production_time
            ).total_seconds()
            if time_passed_seconds <= 0:
                return ActionResult.success_result(
                    message="銷售時間間隔不足，沒有任何銷售發生。"
                )

            cycles = time_passed_seconds / facility_config.process_time
            sell_rate_bonus = 1.3 if "marketing" in facility.upgrades else 1.0

            for slot in filter(lambda s: s.item_id and s.quantity > 0, shelf_slots):
                item_config = self.game_data.get_item_config(slot.item_id)
                if not item_config:
                    logger.warning("商店銷售中找不到物品配置: %s", slot.item_id)
                    continue

                sellable_quantity = int(min(slot.quantity, cycles) * sell_rate_bonus)
                if sellable_quantity > 0:
                    earnings = sellable_quantity * item_config.base_sell_price
                    new_quantity = slot.quantity - sellable_quantity
                    await self.repository.update_facility_slot_quantity(
                        facility_id, "shelf", slot.slot_index, new_quantity
                    )
                    total_earnings += earnings
                    sold_items_details.append(
                        f"• {item_config.name} x{sellable_quantity} = {earnings:,} 油幣"
                    )

            if total_earnings > 0:
                await self.repository.add_pending_oil_earnings(user_id, total_earnings)

            await self.repository.update_facility_production_time(facility_id)

            if sold_items_details:
                message = "商店銷售報告：\n" + "\n".join(sold_items_details)
                message += (
                    f"\n\n總收益: **{total_earnings:,}** 油幣已加入您的待收取餘額。"
                )
            else:
                message = "您的商店正在營業，但本次沒有售出任何商品。"

            return ActionResult.success_result(
                message=message,
                rewards=[{"type": "pending_oil", "amount": total_earnings}]
                if total_earnings > 0
                else [],
            )
        except (PioneerNotFoundError, PioneerValidationError) as e:
            raise e
        except Exception as e:
            logger.error("處理商店銷售時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError(
                f"處理商店銷售時發生系統錯誤，請聯繫管理員。詳細資訊: {e}"
            ) from e

    async def _initialize_facility_slots(self, facility_id: int, facility_config):
        """初始化設施槽位"""
        # 根據設施配置初始化輸入、輸出槽位
        for input_config in facility_config.inputs:
            slot_type = input_config.get("slot_type", "input")
            capacity = input_config.get("capacity", 100)
            await self.repository.create_facility_slot(
                facility_id=facility_id,
                slot_type=slot_type,
                slot_index=0,
                max_capacity=capacity,
            )

        # 初始化輸出槽位
        if facility_config.outputs:
            await self.repository.create_facility_slot(
                facility_id=facility_id,
                slot_type="output",
                slot_index=0,
                max_capacity=1000,  # 默認輸出槽容量
            )

    async def _calculate_upgrade_cost(self, facility, upgrade_config) -> Dict[str, int]:
        """計算升級成本"""
        cost = {"oil": 0}
        formula = ""  # Initialize formula to avoid unbound error

        if upgrade_config.cost_formula:
            # 使用公式計算成本
            try:
                # 簡單的公式解析，支援 level 變量
                formula = upgrade_config.cost_formula.replace(
                    "level", str(facility.level)
                )
                # 安全的數學表達式求值
                import ast
                import operator

                # 支援的操作符
                ops = {
                    ast.Add: operator.add,
                    ast.Sub: operator.sub,
                    ast.Mult: operator.mul,
                    ast.Div: operator.truediv,
                    ast.Pow: operator.pow,
                    ast.USub: operator.neg,
                }

                def eval_expr(node):
                    if isinstance(
                        node, (ast.Constant, ast.Num)
                    ):  # Support Constant for Python 3.8+
                        return node.n if isinstance(node, ast.Num) else node.value
                    elif isinstance(node, ast.BinOp):
                        return ops[type(node.op)](
                            eval_expr(node.left), eval_expr(node.right)
                        )
                    elif isinstance(node, ast.UnaryOp):
                        return ops[type(node.op)](eval_expr(node.operand))
                    else:
                        raise TypeError(node)

                result = eval_expr(ast.parse(formula, mode="eval").body)
                if isinstance(result, (int, float)):
                    cost["oil"] = int(result)
                else:
                    logger.error(
                        "公式計算結果類型不正確: %s, 結果: %s", formula, result
                    )
                    raise PioneerActionError(
                        f"無法計算升級成本，公式「{formula}」可能存在問題。"
                    )

            except Exception as e:
                logger.error("公式計算失敗: %s, 錯誤: %s", formula, e)
                raise PioneerActionError(
                    f"計算升級成本時發生錯誤，公式「{formula}」可能無效。"
                ) from e

        if upgrade_config.cost_oil:
            cost["oil"] = upgrade_config.cost_oil

        return cost

    async def _stock_shop_item(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        """將物品從倉庫上架到商店設施"""
        try:
            facility_id = params.get("facility_id")
            item_id = params.get("item_id")
            quantity_param = params.get("quantity")

            if not facility_id or not item_id or quantity_param is None:
                raise PioneerValidationError("操作失敗：必須提供設施ID、物品ID和數量。")

            try:
                quantity = int(quantity_param)
                if quantity <= 0:
                    raise PioneerValidationError("上架數量必須是一個大於零的整數。")
            except (ValueError, TypeError) as e:
                raise PioneerValidationError("數量格式錯誤，請輸入有效的數字。") from e

            if not item_id:
                raise PioneerValidationError("操作失敗：未提供物品ID。")
            item_config = self.game_data.get_item_config(item_id)
            if not item_config:
                raise PioneerNotFoundError(f"找不到物品ID「{item_id}」的設定資料。")

            warehouse_quantity = await self.repository.get_item_quantity(
                user_id, item_id
            )
            if warehouse_quantity < quantity:
                raise PioneerInsufficientItemsError(
                    f"倉庫存貨不足。您想上架 {quantity} 個「{item_config.name}」，但倉庫中只有 {warehouse_quantity} 個。"
                )

            if not facility_id:
                raise PioneerValidationError("操作失敗：未提供設施ID。")
            facility = await self.repository.get_facility(facility_id)
            if (
                not facility
                or facility.user_id != user_id
                or facility.facility_type != "pioneer_shop"
            ):
                raise PioneerNotFoundError(
                    f"找不到ID為 {facility_id} 的商店設施，或該設施不屬於您。"
                )

            slots = await self.repository.get_facility_slots(facility_id, "shelf")
            target_slot = next(
                (s for s in slots if s.item_id is None or s.item_id == item_id), None
            )

            if not target_slot:
                raise PioneerActionError(
                    "商店貨架已滿，且沒有與您想上架的物品相同的貨架。"
                )

            # 執行上架操作
            success = await self.repository.move_item_to_facility_slot(
                user_id=user_id,
                item_id=item_id,
                quantity=quantity,
                facility_id=facility_id,
                slot_type=target_slot.slot_type,
                slot_index=target_slot.slot_index,
            )

            if not success:
                raise PioneerDatabaseError(
                    operation="move_item_to_facility_slot",
                    details=f"將物品 {item_id} 從倉庫移動到設施 {facility_id} 失敗。",
                )

            return ActionResult.success_result(
                f"上架成功！您已將 {quantity} 個「{item_config.name}」成功上架到商店中。"
            )

        except (
            PioneerNotFoundError,
            PioneerActionError,
            PioneerInsufficientItemsError,
            PioneerValidationError,
            PioneerDatabaseError,
        ) as e:
            raise e
        except Exception as e:
            logger.error("上架物品到商店時發生未預期錯誤: %s", e, exc_info=True)
            raise PioneerActionError(
                f"上架物品時發生系統錯誤，請聯繫管理員。詳細資訊: {e}"
            ) from e
