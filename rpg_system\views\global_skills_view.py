"""
RPG全局技能管理視圖
重用gacha系統的BasePaginationView實現技能分頁顯示和獻祭功能
"""

import math
from typing import Any, Dict, List, Union

import discord
from discord.ui import Button, Modal, TextInput

from gacha.exceptions import BusinessError
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from rpg_system.exceptions import RPGSystemError, SkillError
from rpg_system.views.embeds.skill_embeds import SkillEmbedBuilder
from utils.base_view import BaseView, BotType
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# 每頁顯示的技能數量
SKILLS_PER_PAGE = 5


class SkillSacrificeModal(Modal):
    """技能獻祭模態框"""

    def __init__(self, view: "GlobalSkillsView"):
        super().__init__(title="選擇要獻祭的技能")
        self.skills_view = view

        self.skill_input = TextInput(
            label="技能ID或序號",
            placeholder="輸入要獻祭的技能ID或在當前頁面的序號",
            required=True,
            min_length=1,
            max_length=20,
        )
        self.add_item(self.skill_input)

    async def on_submit(self, interaction: discord.Interaction):
        """提交處理"""
        try:
            skill_identifier = self.skill_input.value.strip()

            try:
                skill_index = int(skill_identifier) - 1
                current_page_skills = self.skills_view.get_current_page_skills()

                if 0 <= skill_index < len(current_page_skills):
                    skill_id = current_page_skills[skill_index]["skill_id"]
                else:
                    skill_id = skill_identifier
            except ValueError:
                skill_id = skill_identifier

            if not self.skills_view.is_valid_skill_id(skill_id):
                raise SkillError(f"無效的技能ID或序號: {skill_identifier}")

            await self.skills_view.show_sacrifice_options(interaction, skill_id)

        except Exception as e:
            logger.error("處理技能獻祭輸入時發生錯誤: %s", e, exc_info=True)
            raise RPGSystemError("處理技能選擇時發生錯誤") from e


class SacrificeOptionsView(BaseView):
    """獻祭選項視圖"""

    def __init__(
        self,
        bot: BotType,
        user_id: int,
        skill_id: str,
        skill_proficiency_service: Any,
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=60)
        self.skill_id = skill_id
        self.skill_proficiency_service = skill_proficiency_service

    @discord.ui.button(
        label="獻祭所有同名技能卡牌", style=discord.ButtonStyle.danger, emoji="🔥"
    )
    async def sacrifice_all_excluding_favorites(
        self, interaction: discord.Interaction, button: Button
    ):
        """獻祭所有同名技能卡牌（不包含最愛）"""
        await interaction.response.defer()

        # 根據規範，移除 try-except，讓錯誤自然冒泡由 BaseView.on_error 處理
        result = await self.skill_proficiency_service.sacrifice_cards_for_skill(
            self.user_id, self.skill_id, include_favorites=False, keep_one=False
        )

        embed = SuccessEmbed(
            title="🔥 技能獻祭成功",
            description=f"已獻祭擁有技能 `{self.skill_id}` 的卡牌（不包含最愛）",
        )
        embed.color = discord.Color.orange()

        embed.add_field(
            name="獻祭統計",
            value=f"獻祭卡牌: {result['sacrificed_count']} 張\n"
            f"剩餘卡牌: {result['remaining_cards']} 張",
            inline=False,
        )

        if result["skill_exp_gained"]:
            skill_exp_text = "\n".join(
                [
                    f"• {skill_id}: +{exp} 經驗"
                    for skill_id, exp in result["skill_exp_gained"].items()
                ]
            )
            embed.add_field(
                name="技能經驗獲得", value=skill_exp_text[:1024], inline=False
            )

        await interaction.followup.send(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="包含最愛但保留一張", style=discord.ButtonStyle.secondary, emoji="💎"
    )
    async def sacrifice_keep_one(
        self, interaction: discord.Interaction, button: Button
    ):
        """包含最愛卡片，但保留一張"""
        raise BusinessError("⚠️ 此功能尚未實現，請等待後續更新。")

    @discord.ui.button(label="取消", style=discord.ButtonStyle.gray, emoji="❌")
    async def cancel_sacrifice(self, interaction: discord.Interaction, button: Button):
        """取消獻祭"""
        await interaction.response.edit_message(content="❌ 已取消獻祭操作", view=None)
        self.stop()


class GlobalSkillsView(BasePaginationView):
    """
    全局技能管理視圖
    """

    def __init__(
        self,
        bot: BotType,
        user: Union[discord.User, discord.Member],
        skills_data: List[Dict[str, Any]],
        skill_proficiency_service: Any,
        current_page: int = 1,
    ):
        total_pages = max(1, math.ceil(len(skills_data) / SKILLS_PER_PAGE))
        super().__init__(
            bot=bot, user_id=user.id, current_page=current_page, total_pages=total_pages
        )

        self.skills_data = skills_data
        self.skill_proficiency_service = skill_proficiency_service

        self.sacrifice_button = Button(
            label="選擇技能獻祭",
            style=discord.ButtonStyle.danger,
            emoji="🔥",
            custom_id="sacrifice_skill",
            row=1,
        )
        self.sacrifice_button.callback = self.sacrifice_callback
        self.add_item(self.sacrifice_button)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面內容"""
        try:
            embed = await self.get_current_page_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        except Exception as e:
            logger.error("更新技能頁面失敗: %s", e, exc_info=True)
            raise

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁面的嵌入"""
        try:
            current_skills = self.get_current_page_skills()
            start_index = (self.current_page - 1) * SKILLS_PER_PAGE
            return SkillEmbedBuilder.create_global_skills_page_embed(
                current_skills, self.current_page, self.total_pages, start_index
            )
        except Exception as e:
            logger.error("創建技能頁面嵌入失敗: %s", e)
            raise RPGSystemError("創建技能信息時發生錯誤") from e

    def get_current_page_skills(self) -> List[Dict[str, Any]]:
        """獲取當前頁面的技能列表"""
        start_index = (self.current_page - 1) * SKILLS_PER_PAGE
        end_index = start_index + SKILLS_PER_PAGE
        return self.skills_data[start_index:end_index]

    def is_valid_skill_id(self, skill_id: str) -> bool:
        """檢查技能ID是否有效"""
        return any(skill.get("skill_id") == skill_id for skill in self.skills_data)

    async def sacrifice_callback(self, interaction: discord.Interaction):
        """技能獻祭按鈕回調"""
        if not self.skills_data:
            raise SkillError("您還沒有學習任何全局技能")
        modal = SkillSacrificeModal(self)
        await interaction.response.send_modal(modal)

    async def show_sacrifice_options(
        self, interaction: discord.Interaction, skill_id: str
    ):
        """顯示獻祭選項"""
        if self.bot is None:
            logger.error("無法顯示獻祭選項，因為 GlobalSkillsView 中的 bot 為 None")
            raise RPGSystemError("內部錯誤，無法顯示獻祭選項")

        options_view = SacrificeOptionsView(
            bot=self.bot,
            user_id=interaction.user.id,
            skill_id=skill_id,
            skill_proficiency_service=self.skill_proficiency_service,
        )
        embed = SkillEmbedBuilder.create_sacrifice_options_embed(skill_id)
        await interaction.response.send_message(
            embed=embed, view=options_view, ephemeral=True
        )
