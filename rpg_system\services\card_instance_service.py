"""
RPG卡牌實例服務 - 統一重構版
整合了卡牌初始化、實例管理和生命週期管理的所有功能
實現文檔中的簡單設計：只在兩個地方判斷卡牌類型
"""

from typing import Any, Dict, List, Optional

import asyncpg

from gacha.exceptions import CardNotFoundError, DatabaseOperationError
from rpg_system.config.loader import get_config_loader
from rpg_system.repositories import player_collection_repository
from rpg_system.repositories.player_collection_repository import is_special_card
from rpg_system.services import player_global_skill_proficiency_service
from utils.logger import logger

# ==================== 卡牌初始化功能（整合自CardInitializationService）====================


async def initialize_new_card(
    user_id: int,
    card_id: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """
    初始化新獲得的卡牌
    整合自CardInitializationService的功能

    Args:
        user_id: 玩家ID
        card_id: 卡牌ID
        connection: 數據庫連接（可選）

    Returns:
        是否初始化成功

    Raises:
        Exception: 初始化失敗時拋出異常
    """
    try:
        config_loader = get_config_loader()

        # 1. 獲取卡牌配置
        card_config = await config_loader.get_card_config(str(card_id))
        if not card_config:
            logger.warning("找不到卡牌配置，跳過RPG初始化: card_id=%s", card_id)
            return False

        # 2. 提取初始裝備技能
        initial_skills = _extract_initial_skills(card_config)

        # 3. 更新數據庫
        success = await _update_card_equipment(
            user_id=user_id,
            card_id=card_id,
            equipped_active_skill_ids=initial_skills["active"],
            equipped_common_passives=initial_skills["passive"],
            connection=connection,
        )

        # 4. 自動解鎖全局技能（新功能）
        if success:
            try:
                unlock_result = await player_global_skill_proficiency_service.unlock_skills_from_card(
                    user_id=user_id, card_id=card_id
                )
                logger.info(
                    "卡牌 %s 技能解鎖結果: 新解鎖=%s, 已擁有=%s",
                    card_id,
                    len(unlock_result["newly_unlocked"]),
                    len(unlock_result["already_unlocked"]),
                )
            except Exception as e:
                logger.warning(
                    "自動解鎖技能失敗，但卡牌初始化繼續: card_id=%s, error=%s",
                    card_id,
                    e,
                )
                # 技能解鎖失敗不影響卡牌初始化流程

        if success:
            logger.info("卡牌初始化成功: user_id=%s, card_id=%s", user_id, card_id)
        else:
            logger.warning("卡牌初始化失敗: user_id=%s, card_id=%s", user_id, card_id)

        return success

    except Exception as e:
        logger.error(
            "初始化卡牌時發生錯誤: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise


async def batch_initialize_new_cards(
    user_id: int,
    card_ids: List[int],
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[int, bool]:
    """
    批量初始化新獲得的卡牌

    Args:
        user_id: 玩家ID
        card_ids: 新卡牌ID列表
        connection: 數據庫連接（可選）

    Returns:
        初始化結果字典 {card_id: success}
    """
    results = {}

    for card_id in card_ids:
        try:
            success = await initialize_new_card(
                user_id=user_id, card_id=card_id, connection=connection
            )
            results[card_id] = success
        except Exception as e:
            logger.error("批量初始化卡牌失敗: card_id=%s, 錯誤: %s", card_id, e)
            results[card_id] = False

    return results


def _extract_initial_skills(card_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    從卡牌配置中提取初始裝備技能

    Args:
        card_config: 卡牌配置字典

    Returns:
        包含active和passive技能的字典
    """
    try:
        # 獲取初始裝備技能（使用正確的字段名）
        equipped_active_skill_ids = card_config.get(
            "initial_equipped_active_skills", []
        )
        equipped_common_passives = card_config.get("initial_equipped_passives", {})

        # 確保數據類型正確
        if not isinstance(equipped_active_skill_ids, list):
            equipped_active_skill_ids = []
        if not isinstance(equipped_common_passives, dict):
            equipped_common_passives = {}

        return {
            "active": equipped_active_skill_ids,
            "passive": equipped_common_passives,
        }

    except Exception as e:
        logger.error("提取初始技能失敗: %s", e)
        return {"active": [], "passive": {}}


async def _update_card_equipment(
    user_id: int,
    card_id: int,
    equipped_active_skill_ids: List[str],
    equipped_common_passives: Dict[str, Any],
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """
    更新卡牌的裝備技能

    Args:
        user_id: 玩家ID
        card_id: 卡牌ID
        equipped_active_skill_ids: 主動技能ID列表
        equipped_common_passives: 被動技能字典
        connection: 數據庫連接（可選）

    Returns:
        是否更新成功
    """
    query = """
        UPDATE gacha_user_collections
        SET equipped_active_skill_ids = $3,
            equipped_common_passives = $4
        WHERE user_id = $1 AND card_id = $2 AND quantity > 0
    """

    try:
        # 讓 asyncpg 自動處理 JSONB 編碼
        if connection:
            status = await connection.execute(
                query,
                user_id,
                card_id,
                equipped_active_skill_ids,
                equipped_common_passives,
            )
        else:
            # 使用repository的方法執行查詢
            pool = player_collection_repository.get_pool()
            if not pool:
                raise DatabaseOperationError("Database pool is not initialized.")
            async with pool.acquire() as conn:
                status = await conn.execute(
                    query,
                    user_id,
                    card_id,
                    equipped_active_skill_ids,
                    equipped_common_passives,
                )

        return status == "UPDATE 1"

    except Exception as e:
        logger.error(
            "更新卡牌裝備失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise


# ==================== 核心判斷點1：戰鬥後處理 ====================


async def add_battle_experience(user_id: int, card_id: int, exp_gained: int) -> int:
    """
    戰鬥後獲得經驗，根據卡牌類型分別處理
    這是第一個需要判斷卡牌類型的地方
    """
    try:
        if is_special_card(card_id):
            # 情況2：特殊卡參與戰鬥 - 直接更新經驗
            await player_collection_repository.update_special_card_experience(
                card_id, exp_gained
            )

            # 檢查是否需要升級
            await _check_and_level_up(card_id)
            return card_id
        else:
            # 情況1：默認卡參與戰鬥 - 分離後添加經驗
            special_card_id = (
                await player_collection_repository.separate_card_for_battle(
                    user_id, card_id, exp_gained
                )
            )

            # 檢查是否需要升級
            await _check_and_level_up(special_card_id)
            return special_card_id

    except Exception as e:
        logger.error(
            "戰鬥經驗處理失敗: user_id=%s, card_id=%s, exp=%s, 錯誤: %s",
            user_id,
            card_id,
            exp_gained,
            e,
        )
        raise DatabaseOperationError(f"戰鬥經驗處理失敗: {str(e)}") from e


# ==================== 核心判斷點2：技能修改 ====================


async def modify_card_skills(
    user_id: int, card_id: int, new_skills: Dict[str, Any]
) -> int:
    """
    修改卡牌技能，支持自動分離
    這是第二個需要判斷卡牌類型的地方
    """
    try:
        if is_special_card(card_id):
            # 特殊卡：直接修改
            await player_collection_repository.update_special_card_skills(
                card_id, new_skills
            )
            return card_id
        else:
            # 默認卡：分離後修改
            special_card_id = (
                await player_collection_repository.separate_card_for_skills(
                    user_id, card_id, new_skills
                )
            )
            return special_card_id

    except Exception as e:
        logger.error(
            "技能修改失敗: user_id=%s, card_id=%s, 錯誤: %s", user_id, card_id, e
        )
        raise DatabaseOperationError(f"技能修改失敗: {str(e)}") from e


# ==================== 輔助方法 ====================


async def _check_and_level_up(card_id: int) -> None:
    """檢查並處理自動升級"""
    try:
        # 獲取特殊卡信息
        card_data = await player_collection_repository.get_special_card(card_id)

        current_level = card_data["rpg_level"]
        current_xp = card_data["rpg_xp"]

        # 簡單的升級邏輯：每100經驗升1級
        required_xp = current_level * 100

        if current_xp >= required_xp:
            new_level = current_level + 1
            remaining_xp = current_xp - required_xp

            # 更新等級和經驗
            await player_collection_repository.update_rpg_level_and_xp(
                card_data["id"], new_level, remaining_xp
            )

            logger.info(
                "卡牌自動升級: card_id=%s, %s -> %s",
                card_id,
                current_level,
                new_level,
            )

    except Exception as e:
        logger.error("自動升級檢查失敗: card_id=%s, 錯誤: %s", card_id, e)
        # 升級失敗不影響主流程，只記錄錯誤


# ==================== 卡牌刪除方法 ====================


async def delete_card_instance(user_id: int, card_id: int) -> bool:
    """刪除卡牌實例（自動判斷類型）"""
    try:
        if is_special_card(card_id):
            # 刪除特殊卡
            return await player_collection_repository.sacrifice_special_card(card_id)
        else:
            # 刪除默認卡（減少數量）
            return await player_collection_repository.sacrifice_default_card(
                user_id, card_id
            )
    except Exception as e:
        logger.error(
            "刪除卡牌實例失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"刪除卡牌實例失敗: {str(e)}") from e


# ==================== 統一查詢方法（消除重複邏輯）====================


async def get_card_info(user_id: int, card_id: int) -> Optional[Dict[str, Any]]:
    """
    獲取卡牌信息（自動判斷類型）
    統一的卡牌查詢入口，其他服務應該通過此方法獲取卡牌信息
    """
    try:
        if is_special_card(card_id):
            try:
                card_data = await player_collection_repository.get_special_card(card_id)
                # 驗證用戶權限
                if card_data.get("user_id") != user_id:
                    logger.warning("用戶 %s 無權限查看特殊卡 %s", user_id, card_id)
                    return None
                return card_data
            except CardNotFoundError:
                return None
        else:
            return await player_collection_repository.get_default_card(user_id, card_id)
    except Exception as e:
        logger.error(
            "獲取卡牌信息失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"獲取卡牌信息失敗: {str(e)}") from e


async def get_card_details_with_config(
    user_id: int, card_id: int
) -> Optional[Dict[str, Any]]:
    """
    獲取卡牌詳細信息（包含配置數據）
    整合自PlayerCardManagementService的重複邏輯
    """
    try:
        # 1. 獲取卡牌基本信息
        card_data = await get_card_info(user_id, card_id)
        if not card_data:
            return None

        # 2. 如果有配置加載器，添加配置信息
        config_loader = get_config_loader()
        # 獲取原始card_id（特殊卡需要從original_card_id獲取）
        original_card_id = card_data.get("original_card_id", card_id)
        if is_special_card(card_id):
            original_card_id = card_data.get("original_card_id", card_id)
        else:
            original_card_id = card_id

        card_config = await config_loader.get_card_config(str(original_card_id))
        if card_config:
            card_data["config"] = card_config
        else:
            logger.warning("找不到卡牌配置: %s", original_card_id)

        return card_data

    except Exception as e:
        logger.error(
            "獲取卡牌詳細信息失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        raise DatabaseOperationError(f"獲取卡牌詳細信息失敗: {str(e)}") from e


async def get_user_all_cards(user_id: int) -> List[Dict[str, Any]]:
    """
    獲取用戶所有卡牌（默認卡+特殊卡）
    統一的用戶卡牌查詢方法，使用UNION ALL查詢
    """
    try:
        return await player_collection_repository.get_all_user_cards(user_id)
    except Exception as e:
        logger.error("獲取用戶所有卡牌失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶所有卡牌失敗: {str(e)}") from e


async def check_card_ownership(user_id: int, card_id: int) -> bool:
    """
    檢查用戶是否擁有指定卡牌
    """
    try:
        card_data = await get_card_info(user_id, card_id)
        return card_data is not None
    except Exception as e:
        logger.error(
            "檢查卡牌所有權失敗: user_id=%s, card_id=%s, 錯誤: %s",
            user_id,
            card_id,
            e,
        )
        return False


async def get_user_cards_by_type(user_id: int) -> Dict[str, Any]:
    """獲取用戶卡牌並按類型分組（如果需要分別處理）"""
    try:
        # 分別查詢兩個表
        default_cards = await player_collection_repository.get_default_cards(user_id)
        special_cards = await player_collection_repository.get_special_cards(user_id)

        return {
            "default_cards": default_cards,
            "special_cards": special_cards,
            "total_default": len(default_cards),
            "total_special": len(special_cards),
        }
    except Exception as e:
        logger.error("獲取用戶卡牌失敗: user_id=%s, 錯誤: %s", user_id, e)
        raise DatabaseOperationError(f"獲取用戶卡牌失敗: {str(e)}") from e


async def get_cards_with_skill(user_id: int, skill_id: str) -> Dict[str, Any]:
    """獲取擁有指定技能的卡牌（用於批量獻祭）"""
    try:
        cards = await player_collection_repository.get_cards_with_skill(
            user_id, skill_id
        )

        # 按類型分組
        default_cards = [card for card in cards if card["card_type"] == "default"]
        special_cards = [card for card in cards if card["card_type"] == "special"]

        return {
            "default_cards": default_cards,
            "special_cards": special_cards,
            "total_found": len(cards),
        }
    except Exception as e:
        logger.error(
            "按技能查詢卡牌失敗: user_id=%s, skill_id=%s, 錯誤: %s",
            user_id,
            skill_id,
            e,
        )
        raise DatabaseOperationError(f"按技能查詢卡牌失敗: {str(e)}") from e
