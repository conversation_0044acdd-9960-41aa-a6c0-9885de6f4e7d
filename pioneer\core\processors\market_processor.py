from typing import Any, Dict

import gacha.services.economy_service as economy_service
from pioneer.exceptions import (
    PioneerDatabaseError,
    PioneerInsufficientItemsError,
    PioneerNotFoundError,
    PioneerValidationError,
)
from pioneer.models.pioneer_models import ActionConfig, ActionResult

from .base_processor import BaseProcessor


class MarketProcessor(BaseProcessor):
    """處理與市場/貿易站相關的動作"""

    async def execute(
        self, user_id: int, action_config: ActionConfig, params: Dict[str, Any]
    ) -> ActionResult:
        if action_config.type == "sell_resource":
            return await self._sell_resource_to_system(user_id, params)
        raise PioneerValidationError(f"未知的市場動作類型: {action_config.type}")

    async def _sell_resource_to_system(
        self, user_id: int, params: Dict[str, Any]
    ) -> ActionResult:
        """玩家向系統出售資源的核心邏輯"""
        item_id = params.get("item_id")
        quantity_param = params.get("quantity")

        if not item_id or not quantity_param:
            raise PioneerValidationError(
                "操作失敗：必須同時提供要出售的「物品ID」和「數量」。"
            )

        try:
            quantity = int(quantity_param)
            if quantity <= 0:
                raise PioneerValidationError(
                    "數量無效：出售的數量必須是一個大於零的整數。"
                )
        except (ValueError, TypeError) as e:
            raise PioneerValidationError(
                "數量格式錯誤：請輸入有效的數字作為數量。"
            ) from e

        item_config = self.game_data.get_item_config(item_id)
        if not item_config:
            raise PioneerNotFoundError(
                f"找不到物品「{item_id}」。請確認該物品是否存在於遊戲設定中。"
            )

        # 檢查倉庫庫存
        warehouse_item = await self.repository.get_warehouse_item(user_id, item_id)
        if not warehouse_item or warehouse_item.quantity < quantity:
            available = warehouse_item.quantity if warehouse_item else 0
            raise PioneerInsufficientItemsError(
                f"物品「{item_config.name}」數量不足。您想出售 {quantity} 個，但庫存中只有 {available} 個。"
            )

        # 計算收益並執行交易
        total_earnings = item_config.base_sell_price * quantity

        # 原子操作：消耗物品並增加油幣
        consumed = await self.repository.consume_warehouse_item(
            user_id, item_id, quantity
        )
        if not consumed:
            # 這種情況通常是競爭條件或資料庫問題，屬於更嚴重的錯誤
            raise PioneerDatabaseError(
                operation="consume_warehouse_item",
                details=f"嘗試消耗玩家 {user_id} 的物品 {item_id} (數量: {quantity}) 時返回 False，可能表示庫存不足或資料庫延遲。",
            )

        await economy_service.award_oil(
            user_id=user_id,
            amount=total_earnings,
            transaction_type="pioneer:sell_item",
            reason=f"Sold {quantity} of item {item_id}",
        )

        return ActionResult.success_result(
            message=f"交易成功！您出售了 {quantity} 個 {item_config.name}，獲得了 {total_earnings:,} 油幣。",
            rewards=[{"type": "oil", "amount": total_earnings}],
        )
