from typing import Any, Dict, List, Optional

import asyncpg

from gacha.exceptions import DatabaseOperationError
from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
)
from utils.logger import logger

# 表名常量
TABLE_NAME = "player_portfolios"


async def get_holders_of_asset(
    asset_id: int, conn: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """
    查詢 player_portfolios 表，返回所有持有指定 asset_id 的玩家記錄列表。
    每條記錄至少應包含 user_id 和 quantity。
    注意：只返回 quantity > 0 的記錄，不包括只有做空倉位的記錄。

    Args:
        asset_id: 資產ID
        conn: 可選的資料庫連接

    Returns:
        List[Dict[str, Any]]: 持有該資產的玩家記錄列表

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = f"SELECT user_id, quantity FROM {TABLE_NAME} WHERE asset_id = $1 AND quantity > 0"
        records = await fetch_all(query, (asset_id,), connection=conn)
        return [dict(record) for record in records]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"查詢資產 {asset_id} 持有者失敗: {e}") from e


async def remove_asset_holding(
    user_id: int, asset_id: int, conn: Optional[asyncpg.Connection] = None
) -> bool:
    """
    從 player_portfolios 表中刪除指定 user_id 和 asset_id 的持股記錄。

    Args:
        user_id: 用戶ID
        asset_id: 資產ID
        conn: 可選的資料庫連接

    Returns:
        bool: 成功刪除返回 True，記錄不存在返回 False

    Raises:
        DatabaseOperationError: 資料庫操作失敗時拋出
    """
    try:
        query = f"DELETE FROM {TABLE_NAME} WHERE user_id = $1 AND asset_id = $2"
        status = await execute_query(query, (user_id, asset_id), connection=conn)

        if status > 0:
            logger.info(
                "Successfully removed asset %s for user %s. (Deleted %s rows)",
                asset_id,
                user_id,
                status,
            )
            return True
        else:
            logger.warning(
                "No asset %s found for user %s to remove. (Deleted %s rows)",
                asset_id,
                user_id,
                status,
            )
            return False

    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"刪除用戶 {user_id} 的資產 {asset_id} 失敗: {e}"
        ) from e
