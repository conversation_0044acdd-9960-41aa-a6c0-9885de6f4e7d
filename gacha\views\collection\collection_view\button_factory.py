"""
統一按鈕工廠模塊
提供所有視圖中按鈕的創建和配置功能，消除重複實現
"""

from typing import TYPE_CHECKING, Callable, Optional

import discord
from discord.ui import Button

from config.app_config import get_ui_button_emojis

# 避免循環引用
if TYPE_CHECKING:
    pass


class UnifiedButtonFactory:
    """統一按鈕工廠類，負責創建和配置所有類型的按鈕

    合併了原本的 ButtonFactory, MultiDrawButtonFactory 和 FavoriteComponent 的按鈕創建功能
    確保所有按鈕創建邏輯統一管理，避免重複實現
    """

    @staticmethod
    def create_button(
        label: str,
        style: discord.ButtonStyle,
        row: int = 0,
        disabled: bool = False,
        emoji: Optional[str] = None,
        custom_id: Optional[str] = None,
    ) -> Button:
        """創建基本按鈕

        參數:
            label: 按鈕標籤
            style: 按鈕樣式
            row: 按鈕所在行
            disabled: 是否禁用
            emoji: 按鈕表情符號
            custom_id: 按鈕自定義ID

        返回:
            Button: 創建的按鈕
        """
        return Button(
            label=label,
            style=style,
            row=row,
            disabled=disabled,
            emoji=emoji,
            custom_id=custom_id,
        )

    @staticmethod
    def create_favorite_button(
        is_favorite: bool, row: int = 1, disabled: bool = False
    ) -> Button:
        """創建最愛按鈕

        參數:
            is_favorite: 是否已是最愛
            row: 按鈕所在行
            disabled: 是否禁用

        返回:
            Button: 創建的最愛按鈕
        """
        ui_emojis = get_ui_button_emojis()
        if is_favorite:
            # 已收藏狀態 - 從配置讀取emoji
            return UnifiedButtonFactory.create_button(
                label="已收藏",
                style=discord.ButtonStyle.success,
                row=row,
                disabled=disabled,
                emoji=ui_emojis.favorite,
                custom_id="toggle_favorite",
            )
        else:
            # 未收藏狀態 - 從配置讀取emoji
            return UnifiedButtonFactory.create_button(
                label="加入最愛",
                style=discord.ButtonStyle.secondary,
                row=row,
                disabled=disabled,
                emoji=ui_emojis.old_card,
                custom_id="toggle_favorite",
            )

    @staticmethod
    def _create_sell_base_button(
        label: str, custom_id: str, has_cards: bool, row: int = 1
    ) -> Button:
        """創建賣卡按鈕的基礎方法 - 避免重複代碼"""
        ui_emojis = get_ui_button_emojis()
        return UnifiedButtonFactory.create_button(
            label=label,
            style=discord.ButtonStyle.danger,
            row=row,
            disabled=not has_cards,
            emoji=ui_emojis.sell,
            custom_id=custom_id,
        )
    
    @staticmethod
    def create_sell_button(has_cards: bool, row: int = 1) -> Button:
        """創建賣卡按鈕"""
        return UnifiedButtonFactory._create_sell_base_button(
            "賣1張", "sell_one_card", has_cards, row
        )

    @staticmethod
    def create_sell_all_button(has_cards: bool, row: int = 1) -> Button:
        """創建賣出所有按鈕"""
        return UnifiedButtonFactory._create_sell_base_button(
            "賣全部", "sell_all_current_card", has_cards, row
        )

    @staticmethod
    def create_function_button(has_cards: bool, row: int = 1) -> Button:
        """創建功能按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的功能按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="功能",
            style=discord.ButtonStyle.primary,
            row=row,
            disabled=not has_cards,
            custom_id="function_button",
        )

    @staticmethod
    def create_enhance_button(
        has_cards: bool, has_duplicates: bool, row: int = 2
    ) -> Button:
        """創建升星按鈕

        參數:
            has_cards: 是否有卡片
            has_duplicates: 是否有重複卡片
            row: 按鈕所在行

        返回:
            Button: 創建的升星按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="升星",
            style=discord.ButtonStyle.primary,
            row=row,
            disabled=not (has_cards and has_duplicates),
            custom_id="enhance_card",
        )

    @staticmethod
    def create_sort_mode_button(
        has_cards: bool,
        is_favorite: bool,
        has_filters: bool = False,
        favorite_priority: bool = True,
        row: int = 2,
    ) -> Button:
        """創建排序模式按鈕

        參數:
            has_cards: 是否有卡片
            is_favorite: 是否為最愛卡片
            has_filters: 是否有篩選條件
            favorite_priority: 是否為最愛優先模式
            row: 按鈕所在行

        返回:
            Button: 創建的排序模式按鈕
        """
        label, style, disabled = UnifiedButtonFactory._get_sort_button_state(
            has_filters, favorite_priority, is_favorite
        )

        return UnifiedButtonFactory.create_button(
            label=label,
            style=style,
            row=row,
            disabled=not has_cards or disabled,
            custom_id="sort_mode",
        )

    @staticmethod
    def create_description_button(has_cards: bool, row: int = 2) -> Button:
        """創建設置描述按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的設置描述按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="設置描述",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,  # 移除星級檢查，僅檢查是否有卡
            emoji="📝",
            custom_id="set_description",
        )

    @staticmethod
    def create_filter_button(has_filters: bool, row: int = 2) -> Button:
        """創建篩選按鈕

        參數:
            has_filters: 是否有篩選條件
            row: 按鈕所在行

        返回:
            Button: 創建的篩選按鈕
        """
        if has_filters:
            return UnifiedButtonFactory.create_button(
                label="篩選中",
                style=discord.ButtonStyle.success,
                row=row,
                emoji="🔍",
                custom_id="filter_management",
            )
        else:
            return UnifiedButtonFactory.create_button(
                label="篩選",
                style=discord.ButtonStyle.primary,
                row=row,
                emoji="🔍",
                custom_id="filter_management",
            )

    @staticmethod
    def create_back_button(row: int = 2) -> Button:
        """創建返回按鈕

        參數:
            row: 按鈕所在行

        返回:
            Button: 創建的返回按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="返回",
            style=discord.ButtonStyle.secondary,
            row=row,
            custom_id="back_to_main",
        )

    @staticmethod
    def _create_batch_favorite_base_button(
        label: str, 
        style: discord.ButtonStyle, 
        emoji_attr: str, 
        custom_id: str, 
        has_cards: bool, 
        row: int = 1
    ) -> Button:
        """創建批次最愛按鈕的基礎方法 - 避免重複代碼"""
        ui_emojis = get_ui_button_emojis()
        emoji = getattr(ui_emojis, emoji_attr)
        return UnifiedButtonFactory.create_button(
            label=label,
            style=style,
            row=row,
            disabled=not has_cards,
            emoji=emoji,
            custom_id=custom_id,
        )

    @staticmethod
    def create_batch_favorite_button(has_cards: bool, row: int = 1) -> Button:
        """創建批次加入最愛按鈕"""
        return UnifiedButtonFactory._create_batch_favorite_base_button(
            "批次加入最愛", 
            discord.ButtonStyle.success, 
            "favorite", 
            "batch_favorite", 
            has_cards, 
            row
        )

    @staticmethod
    def create_batch_unfavorite_button(has_cards: bool, row: int = 1) -> Button:
        """創建批次取消最愛按鈕"""
        return UnifiedButtonFactory._create_batch_favorite_base_button(
            "批次取消最愛", 
            discord.ButtonStyle.danger, 
            "old_card", 
            "batch_unfavorite", 
            has_cards, 
            row
        )

    # 自定義排序模式按鈕

    @staticmethod
    def create_move_to_top_button(has_cards: bool, row: int = 2) -> Button:
        """創建置頂按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的置頂按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="置頂",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_top",
        )

    @staticmethod
    def create_move_up_button(has_cards: bool, row: int = 2) -> Button:
        """創建上移按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的上移按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="上移",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_up",
        )

    @staticmethod
    def create_move_down_button(has_cards: bool, row: int = 2) -> Button:
        """創建下移按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的下移按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="下移",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_down",
        )

    @staticmethod
    def create_move_to_bottom_button(has_cards: bool, row: int = 2) -> Button:
        """創建置底按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的置底按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="置底",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_bottom",
        )

    @staticmethod
    def create_move_to_position_button(has_cards: bool, row: int = 3) -> Button:
        """創建移至指定位置按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的移至指定位置按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="移至位置...",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_position",
        )

    @staticmethod
    def create_back_to_function_button(row: int = 3) -> Button:
        """創建返回功能列表按鈕

        參數:
            row: 按鈕所在行

        返回:
            Button: 創建的返回功能列表按鈕
        """
        return UnifiedButtonFactory.create_button(
            label="返回功能列表",
            style=discord.ButtonStyle.secondary,
            row=row,
            custom_id="back_to_function",
        )

    @staticmethod
    def create_confirm_button(label: str = "確認", row: int = 0) -> Button:
        """創建確認按鈕

        參數:
            label: 按鈕標籤
            row: 按鈕所在行

        返回:
            Button: 創建的確認按鈕
        """
        ui_emojis = get_ui_button_emojis()
        return UnifiedButtonFactory.create_button(
            label=label,
            style=discord.ButtonStyle.danger,
            row=row,
            emoji=ui_emojis.confirm,
            custom_id="confirm_action",
        )

    @staticmethod
    def create_cancel_button(label: str = "取消", row: int = 0) -> Button:
        """創建取消按鈕

        參數:
            label: 按鈕標籤
            row: 按鈕所在行

        返回:
            Button: 創建的取消按鈕
        """
        ui_emojis = get_ui_button_emojis()
        return UnifiedButtonFactory.create_button(
            label=label,
            style=discord.ButtonStyle.secondary,
            row=row,
            emoji=ui_emojis.cancel,
            custom_id="cancel_action",
        )

    # === 多連抽視圖按鈕 (來自 MultiDrawButtonFactory) ===

    @staticmethod
    def create_continue_multi_draw_button(callback: Callable) -> Button:
        """創建 '繼續十連' 按鈕"""
        button = Button(
            label="繼續十連",
            style=discord.ButtonStyle.primary,
            emoji="🎴",
            row=0,
            custom_id="md_continue_multi_draw",
        )
        button.callback = callback
        return button

    @staticmethod
    def create_prev_card_button(callback: Callable) -> Button:
        """創建 '上一張' 按鈕"""
        button = Button(
            emoji="⬅️",
            style=discord.ButtonStyle.secondary,
            row=0,
            custom_id="md_prev_card",
        )
        button.callback = callback
        return button

    @staticmethod
    def create_next_card_button(callback: Callable) -> Button:
        """創建 '下一張' 按鈕"""
        button = Button(
            emoji="➡️",
            style=discord.ButtonStyle.secondary,
            row=0,
            custom_id="md_next_card",
        )
        button.callback = callback
        return button

    # === 最愛按鈕 (來自 FavoriteComponent) ===

    @staticmethod
    def create_initial_favorite_button(
        view: discord.ui.View,
        user_id: int,
        card_id: int,
        is_favorite: bool,
        row: int,
        custom_id: str,
    ) -> Button:
        """創建並返回一個初始狀態配置好的最愛按鈕

        參數:
            view: 按鈕所在的 discord.ui.View 實例
            user_id: 用戶ID
            card_id: 卡片ID
            is_favorite: 是否已是最愛
            row: 按鈕所在行
            custom_id: 按鈕自定義ID

        返回:
            Button: 配置好的最愛按鈕
        """
        button = UnifiedButtonFactory.create_favorite_button(
            is_favorite=is_favorite, row=row
        )
        button.custom_id = custom_id

        async def favorite_callback(interaction: discord.Interaction):
            # The logic is now within the FavoriteButton's callback
            pass

        button.callback = favorite_callback
        return button

    # === 按鈕狀態更新方法 ===

    @staticmethod
    def update_sort_mode_button_state(
        button: discord.ui.Button,
        is_available: bool,
        is_favorite: bool,
        has_filters: bool = False,
        favorite_priority: bool = True,
    ):
        """根據卡片狀態更新排序模式按鈕的 label, style, 和 disabled 狀態"""
        if not is_available:
            button.label = "排序模式"
            button.style = discord.ButtonStyle.secondary
            button.disabled = True
        else:
            label, style, disabled = UnifiedButtonFactory._get_sort_button_state(
                has_filters, favorite_priority, is_favorite
            )
            button.label = label
            button.style = style
            button.disabled = disabled

    @staticmethod
    def _get_sort_button_state(
        has_filters: bool, favorite_priority: bool, is_favorite: bool
    ) -> tuple:
        """獲取排序按鈕的狀態 (label, style, disabled)"""
        if has_filters:
            return "篩選模式下無法排序", discord.ButtonStyle.secondary, True
        elif not favorite_priority:
            return "統一排序模式下無法排序", discord.ButtonStyle.secondary, True
        elif is_favorite:
            return "排序模式", discord.ButtonStyle.success, False
        else:
            return "先加入最愛才能排序", discord.ButtonStyle.secondary, True
