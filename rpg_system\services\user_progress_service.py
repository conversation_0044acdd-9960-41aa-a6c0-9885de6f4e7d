"""
RPG用戶進度服務
負責管理用戶在RPG系統中的進度邏輯
"""

import logging
from typing import Any, Dict, List

import gacha.services.user_service as user_service
from rpg_system.config.loader import get_config_loader
from rpg_system.exceptions import (
    CardNotOwnedError,
    FloorConfigNotFoundError,
    PlayerStateError,
    TeamSizeExceededError,
)
from rpg_system.repositories import (
    player_collection_repository,
    user_progress_repository,
)
from rpg_system.repositories.player_collection_repository import is_special_card

logger = logging.getLogger(__name__)

# 配置常量
MAX_TEAM_SIZE = 1  # 目前限制為1人，未來可調整
MIN_WINS_TO_UNLOCK_NEXT_FLOOR = 3  # 需要3次勝利才能解鎖下一樓層


async def get_user_progress(user_id: int) -> Dict[str, Any]:
    """
    獲取用戶進度信息

    Args:
        user_id: 用戶ID

    Returns:
        用戶進度信息字典
    """
    # 確保用戶存在
    await user_service.get_user(user_id, create_if_missing=True)

    progress = await user_progress_repository.get_or_create_user_progress(user_id)
    if not progress:
        raise PlayerStateError(f"找不到用戶 {user_id} 的進度資料。")

    # 添加額外的計算字段
    progress["can_unlock_next_floor"] = _can_unlock_next_floor(progress)
    progress["next_floor_id"] = progress["current_floor_unlocked"] + 1
    progress["wins_needed_for_next_floor"] = max(
        0, MIN_WINS_TO_UNLOCK_NEXT_FLOOR - progress["current_floor_wins"]
    )

    return progress


async def handle_battle_victory(user_id: int, floor_id: int) -> Dict[str, Any]:
    """
    處理戰鬥勝利

    Args:
        user_id: 用戶ID
        floor_id: 樓層ID

    Returns:
        處理結果字典
    """
    # 確保用戶存在
    await user_service.get_user(user_id, create_if_missing=True)

    progress = await user_progress_repository.get_or_create_user_progress(user_id)
    current_floor = progress["current_floor_unlocked"]
    current_wins = progress["current_floor_wins"]
    max_cleared = progress["max_floor_cleared"]

    result = {
        "floor_id": floor_id,
        "wins_added": 0,
        "floor_unlocked": False,
        "new_max_cleared": max_cleared,
        "next_floor_unlocked": None,
    }

    # 只有在當前解鎖樓層才計算勝利
    if floor_id == current_floor:
        new_wins = current_wins + 1
        new_max_cleared = max(max_cleared, floor_id)

        # 更新勝利次數和最高通關
        await user_progress_repository.update_floor_progress(
            user_id, floor_wins=new_wins, max_floor_cleared=new_max_cleared
        )

        result["wins_added"] = 1
        result["new_max_cleared"] = new_max_cleared

        # 檢查是否可以解鎖下一樓層
        if new_wins >= MIN_WINS_TO_UNLOCK_NEXT_FLOOR:
            try:
                await _try_unlock_next_floor(user_id, current_floor)
                result["floor_unlocked"] = True
                result["next_floor_unlocked"] = current_floor + 1
            except FloorConfigNotFoundError:
                # 下一樓層不存在，這是正常情況（已到最高層）
                logger.info(
                    "已達到最高樓層: user_id=%s, floor=%s",
                    user_id,
                    current_floor,
                )

    logger.info(
        "處理戰鬥勝利: user_id=%s, floor_id=%s, result=%s",
        user_id,
        floor_id,
        result,
    )
    return result


async def get_team_formation(user_id: int) -> List[int]:
    """
    獲取用戶隊伍配置

    Args:
        user_id: 用戶ID

    Returns:
        卡牌收藏ID列表，目前只有一個元素
    """
    # 確保用戶存在
    await user_service.get_user(user_id, create_if_missing=True)

    formation = await user_progress_repository.get_team_formation(user_id)
    return formation or []


async def set_team_formation(user_id: int, card_collection_ids: List[int]) -> None:
    """
    設置用戶隊伍配置

    Args:
        user_id: 用戶ID
        card_collection_ids: 卡牌收藏ID列表

    Raises:
        TeamSizeExceededError: 隊伍人數超過限制
        CardNotOwnedError: 卡牌不屬於用戶
        DatabaseOperationError: 數據庫操作失敗
    """
    # 確保用戶存在
    await user_service.get_user(user_id, create_if_missing=True)

    # 確保用戶進度記錄存在
    await user_progress_repository.get_or_create_user_progress(user_id)

    # 驗證隊伍大小
    if len(card_collection_ids) > MAX_TEAM_SIZE:
        logger.warning(
            "隊伍人數超過限制: user_id=%s, size=%s, max=%s",
            user_id,
            len(card_collection_ids),
            MAX_TEAM_SIZE,
        )
        raise TeamSizeExceededError(len(card_collection_ids), MAX_TEAM_SIZE)

    # 驗證卡牌所有權
    for card_id in card_collection_ids:
        if is_special_card(card_id):
            card_data = await player_collection_repository.get_special_card(card_id)
        else:
            card_data = await player_collection_repository.get_default_card(
                user_id, card_id
            )

        if not card_data or card_data["user_id"] != user_id:
            logger.warning(
                "卡牌不存在或不屬於用戶: user_id=%s, card_id=%s", user_id, card_id
            )
            raise CardNotOwnedError(card_id, user_id)

    # 設置隊伍配置（純異常模式）
    await user_progress_repository.set_team_formation(user_id, card_collection_ids)
    logger.info(
        "設置隊伍配置成功: user_id=%s, formation=%s",
        user_id,
        card_collection_ids,
    )


async def get_team_details(user_id: int) -> List[Dict[str, Any]]:
    """
    獲取隊伍詳細信息

    Args:
        user_id: 用戶ID

    Returns:
        隊伍卡牌詳細信息列表
    """
    formation = await get_team_formation(user_id)
    team_details = []

    for card_collection_id in formation:
        if is_special_card(card_collection_id):
            card_data = await player_collection_repository.get_special_card(
                card_collection_id
            )
        else:
            card_data = await player_collection_repository.get_default_card(
                user_id, card_collection_id
            )

        if card_data:
            team_details.append(card_data)
        else:
            logger.warning(
                "隊伍中的卡牌不存在: user_id=%s, card_id=%s",
                user_id,
                card_collection_id,
            )

    return team_details


async def can_enter_floor(user_id: int, floor_id: int) -> bool:
    """
    檢查用戶是否可以進入指定樓層

    Args:
        user_id: 用戶ID
        floor_id: 樓層ID

    Returns:
        是否可以進入
    """
    # 確保用戶存在
    await user_service.get_user(user_id, create_if_missing=True)

    progress = await user_progress_repository.get_or_create_user_progress(user_id)
    return floor_id <= progress["current_floor_unlocked"]


def _can_unlock_next_floor(progress: Dict[str, Any]) -> bool:
    """
    檢查是否可以解鎖下一樓層

    Args:
        progress: 用戶進度數據

    Returns:
        是否可以解鎖
    """
    return progress["current_floor_wins"] >= MIN_WINS_TO_UNLOCK_NEXT_FLOOR


async def _try_unlock_next_floor(user_id: int, current_floor: int) -> None:
    """
    嘗試解鎖下一樓層

    Args:
        user_id: 用戶ID
        current_floor: 當前樓層

    Raises:
        FloorConfigNotFoundError: 樓層配置未找到
        DatabaseOperationError: 數據庫操作失敗
    """
    next_floor = current_floor + 1
    config_loader = get_config_loader()

    # 檢查下一樓層是否存在於配置中
    floor_config = await config_loader.get_floor_config(str(next_floor))
    if not floor_config:
        logger.info("下一樓層不存在於配置中: floor_id=%s", next_floor)
        raise FloorConfigNotFoundError(next_floor)

    # 解鎖下一樓層並重置勝利次數（純異常模式）
    await user_progress_repository.update_floor_progress(
        user_id,
        floor_unlocked=next_floor,
        floor_wins=0,  # 重置勝利次數
    )
    logger.info("解鎖下一樓層成功: user_id=%s, new_floor=%s", user_id, next_floor)
