"""
RPG配置加載器
負責在服務器啟動時加載所有JSON配置文件到內存
使用pydantic模型進行配置驗證
"""

import json
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import ValidationError

from utils.logger import logger


class ConfigLoader:
    """RPG配置文件加載器"""

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加載器

        Args:
            config_dir: 配置文件目錄路徑，默認為當前模組下的data目錄
        """
        if config_dir is None:
            self.config_dir = Path(__file__).parent / "data"
        else:
            self.config_dir = Path(config_dir)
        self._configs: Dict[str, Any] = {}
        self._loaded = False

    async def load_all_configs(self) -> Dict[str, Any]:
        """
        加載所有配置文件

        Returns:
            包含所有配置的字典
        """
        if self._loaded:
            return self._configs

        logger.info("開始加載RPG配置文件...")

        # 定義需要加載的配置文件
        config_files = [
            "cards.json",
            "active_skills.json",
            "passive_skills.json",
            "innate_passive_skills.json",
            "star_level_effects.json",
            "monsters.json",
            "floors.json",
            "rpg_balance.json",
        ]

        for config_file in config_files:
            config_name = config_file.replace(".json", "")
            try:
                config_data = await self._load_config_file(config_file)
                self._configs[config_name] = config_data
                logger.info("成功加載配置文件: %s", config_file)
            except FileNotFoundError:
                logger.warning("配置文件不存在: %s，將使用空配置", config_file)
                self._configs[config_name] = {}
            except Exception as e:
                logger.error("加載配置文件失敗: %s, 錯誤: %s", config_file, e)
                raise

        # 加載效果模板（從多個文件合併）
        try:
            effect_templates = await self._load_effect_templates()
            self._configs["effect_templates"] = effect_templates
            logger.info("成功加載效果模板配置")
        except Exception as e:
            logger.error("加載效果模板失敗: %s", e)
            raise

        # 加載狀態效果（從多個文件合併）
        try:
            status_effects = await self._load_status_effects()
            self._configs["status_effects"] = status_effects
            logger.info("成功加載狀態效果配置")
        except Exception as e:
            logger.error("加載狀態效果失敗: %s", e)
            raise

        self._loaded = True
        logger.info("RPG配置文件加載完成")
        return self._configs

    async def _load_config_file(self, filename: str) -> Dict[str, Any]:
        """
        加載單個配置文件並進行pydantic驗證

        Args:
            filename: 配置文件名

        Returns:
            配置數據字典
        """
        file_path = self.config_dir / filename

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8-sig") as f:
                raw_data = json.load(f)

            # 使用pydantic模型驗證配置
            validated_data = self._validate_config(filename, raw_data)
            return validated_data

        except json.JSONDecodeError as e:
            logger.error("JSON解析錯誤 %s: %s", filename, e)
            raise
        except ValidationError as e:
            logger.error("配置驗證失敗 %s: %s", filename, e)
            raise
        except Exception as e:
            logger.error("讀取配置文件錯誤 %s: %s", filename, e)
            raise

    def _validate_config(self, filename: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用pydantic模型驗證配置數據

        Args:
            filename: 配置文件名
            data: 原始配置數據

        Returns:
            驗證後的配置數據
        """
        config_name = filename.replace(".json", "")

        # 導入對應的pydantic模型
        try:
            if config_name == "active_skills":
                from rpg_system.config.pydantic_models.active_skills import (
                    ActiveSkillsConfig,
                )

                # active_skills.json 現在是扁平結構，直接驗證
                ActiveSkillsConfig(data)  # 驗證數據格式
                return data
            elif config_name == "passive_skills":
                from rpg_system.config.pydantic_models.passive_skills import (
                    PassiveSkillsConfig,
                )

                # passive_skills.json 現在是扁平結構，直接驗證
                PassiveSkillsConfig(data)  # 驗證數據格式
                return data
            elif config_name == "innate_passive_skills":
                from rpg_system.config.pydantic_models.innate_passive_skills import (
                    InnatePassiveSkillsConfig,
                )

                # innate_passive_skills.json 現在是扁平結構，直接驗證
                InnatePassiveSkillsConfig(data)  # 驗證數據格式
                return data
            else:
                # 對於沒有pydantic模型的配置文件，直接返回原始數據
                logger.debug("配置文件 %s 沒有對應的pydantic模型，跳過驗證", filename)
                return data

        except ImportError as e:
            logger.warning("無法導入 %s 的pydantic模型: %s，跳過驗證", config_name, e)
            return data
        except ValidationError as e:
            logger.error("配置驗證失敗 %s: %s", filename, e)
            raise
        except Exception as e:
            logger.error("配置驗證過程中發生錯誤 %s: %s", filename, e)
            raise

    async def get_config(self, config_name: str) -> Dict[str, Any]:
        """
        獲取指定的配置

        Args:
            config_name: 配置名稱（不含.json後綴）

        Returns:
            配置數據字典
        """
        if not self._loaded:
            raise RuntimeError("配置尚未加載，請先調用 load_all_configs()")

        return self._configs.get(config_name, {})

    async def get_all_configs(self) -> Dict[str, Any]:
        """
        獲取所有配置

        Returns:
            包含所有配置的字典
        """
        if not self._loaded:
            raise RuntimeError("配置尚未加載，請先調用 load_all_configs()")

        return self._configs.copy()

    async def reload_config(self, config_name: str) -> Dict[str, Any]:
        """
        重新加載指定配置文件

        Args:
            config_name: 配置名稱（不含.json後綴）

        Returns:
            重新加載的配置數據
        """
        filename = f"{config_name}.json"
        try:
            config_data = await self._load_config_file(filename)
            self._configs[config_name] = config_data
            logger.info("成功重新加載配置文件: %s", filename)
            return config_data
        except Exception as e:
            logger.error("重新加載配置文件失敗: %s, 錯誤: %s", filename, e)
            raise

    async def get_card_config(self, card_id: str) -> Optional[Any]:
        """
        獲取指定卡牌的配置

        Args:
            card_id: 卡牌ID

        Returns:
            卡牌配置對象或None
        """
        cards_config = await self.get_config("cards")
        return cards_config.get(str(card_id))

    async def get_all_card_configs(self) -> Dict[str, Any]:
        """
        獲取所有卡牌配置

        Returns:
            所有卡牌配置字典
        """
        return await self.get_config("cards")

    async def get_monster_config(self, monster_id: str) -> Optional[Any]:
        """
        獲取指定怪物的配置

        Args:
            monster_id: 怪物ID

        Returns:
            怪物配置對象或None
        """
        monsters_config = await self.get_config("monsters")
        return monsters_config.get(monster_id)

    async def get_floor_config(self, floor_id: str) -> Optional[Any]:
        """
        獲取指定樓層的配置

        Args:
            floor_id: 樓層ID

        Returns:
            樓層配置對象或None
        """
        floors_config = await self.get_config("floors")
        return floors_config.get(floor_id)

    async def get_monster_group_config(self, group_id: str) -> Optional[Any]:
        """
        獲取指定怪物組的配置

        Args:
            group_id: 怪物組ID

        Returns:
            怪物組配置對象或None
        """
        monster_groups_config = await self.get_config("monster_groups")
        return monster_groups_config.get(group_id)

    async def get_star_level_effects_config(self, effects_key: str) -> Optional[Any]:
        """
        獲取指定星級效果的配置

        Args:
            effects_key: 星級效果鍵值

        Returns:
            星級效果配置對象或None
        """
        star_effects_config = await self.get_config("star_level_effects")
        return star_effects_config.get(effects_key)

    async def get_rpg_balance_config(self) -> Dict[str, Any]:
        """
        獲取RPG平衡配置

        Returns:
            RPG平衡配置字典
        """
        return await self.get_config("rpg_balance")

    async def get_card_experience_config(self) -> Dict[str, Any]:
        """
        獲取卡牌經驗配置

        Returns:
            卡牌經驗配置字典
        """
        balance_config = await self.get_rpg_balance_config()
        return balance_config.get("card_experience", {})

    async def get_skill_experience_config(self) -> Dict[str, Any]:
        """
        獲取技能經驗配置

        Returns:
            技能經驗配置字典
        """
        balance_config = await self.get_rpg_balance_config()
        return balance_config.get("skill_experience", {})

    def get_active_skill_config(self, skill_id: str) -> Optional[Dict[str, Any]]:
        """
        獲取主動技能配置

        Args:
            skill_id: 技能ID

        Returns:
            技能配置字典，如果不存在則返回None
        """
        active_skills = self._configs.get("active_skills", {})
        return active_skills.get(skill_id)

    def get_passive_skill_config(self, skill_id: str) -> Optional[Dict[str, Any]]:
        """
        獲取被動技能配置

        Args:
            skill_id: 技能ID

        Returns:
            技能配置字典，如果不存在則返回None
        """
        passive_skills = self._configs.get("passive_skills", {})
        return passive_skills.get(skill_id)

    async def get_rarity_multipliers(self) -> Dict[str, float]:
        """
        獲取稀有度倍數配置

        Returns:
            稀有度倍數字典
        """
        balance_config = await self.get_rpg_balance_config()
        return balance_config.get("rarity_multipliers", {})

    async def _load_effect_templates(self) -> Dict[str, Any]:
        """
        從效果模板目錄中動態加載所有 JSON 文件並合併

        Returns:
            合併後的效果模板字典
        """
        effect_templates = {}
        templates_dir = self.config_dir / "effect_templates"

        if not templates_dir.exists():
            logger.error("效果模板目錄不存在: %s", templates_dir)
            raise FileNotFoundError(f"效果模板目錄不存在: {templates_dir}")

        # 動態讀取目錄下所有 JSON 文件
        json_files = list(templates_dir.glob("*.json"))

        if not json_files:
            logger.error("效果模板目錄中沒有找到任何 JSON 文件: %s", templates_dir)
            raise FileNotFoundError(
                f"效果模板目錄中沒有找到任何 JSON 文件: {templates_dir}"
            )

        for template_file_path in json_files:
            try:
                with open(template_file_path, "r", encoding="utf-8-sig") as f:
                    template_data = json.load(f)
                effect_templates.update(template_data)
                logger.info("成功加載效果模板文件: %s", template_file_path.name)
            except Exception as e:
                logger.error(
                    "加載效果模板文件失敗: %s, 錯誤: %s", template_file_path.name, e
                )
                raise

        logger.info(
            "總共加載了 %s 個效果模板，來自 %s 個文件",
            len(effect_templates),
            len(json_files),
        )
        return effect_templates

    async def _load_status_effects(self) -> Dict[str, Any]:
        """
        從狀態效果目錄中動態加載所有 JSON 文件並合併

        Returns:
            合併後的狀態效果字典
        """
        status_effects = {}
        status_effects_dir = self.config_dir / "status_effects"

        if not status_effects_dir.exists():
            logger.error("狀態效果目錄不存在: %s", status_effects_dir)
            raise FileNotFoundError(f"狀態效果目錄不存在: {status_effects_dir}")

        # 動態讀取目錄下所有 JSON 文件
        json_files = list(status_effects_dir.glob("*.json"))

        if not json_files:
            logger.error("狀態效果目錄中沒有找到任何 JSON 文件: %s", status_effects_dir)
            raise FileNotFoundError(
                f"狀態效果目錄中沒有找到任何 JSON 文件: {status_effects_dir}"
            )

        for status_file_path in json_files:
            try:
                with open(status_file_path, "r", encoding="utf-8-sig") as f:
                    status_data = json.load(f)
                status_effects.update(status_data)
                logger.info("成功加載狀態效果文件: %s", status_file_path.name)
            except Exception as e:
                logger.error(
                    "加載狀態效果文件失敗: %s, 錯誤: %s", status_file_path.name, e
                )
                raise

        logger.info(
            "總共加載了 %s 個狀態效果，來自 %s 個文件",
            len(status_effects),
            len(json_files),
        )
        return status_effects


# 全局配置加載器實例
_config_loader: Optional[ConfigLoader] = None


def get_config_loader() -> ConfigLoader:
    """
    獲取全局配置加載器實例

    Returns:
        配置加載器實例
    """
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()

    return _config_loader


async def initialize_configs() -> Dict[str, Any]:
    """
    初始化並加載所有配置

    Returns:
        包含所有配置的字典
    """
    return await get_config_loader().load_all_configs()


async def get_configs() -> Dict[str, Any]:
    """
    獲取所有配置

    Returns:
        包含所有配置的字典
    """
    return await get_config_loader().get_all_configs()
