"""
核心序列化器模塊 - 使用Dill提供簡單高效的Python對象序列化
"""

import logging
from typing import Any

import dill

logger = logging.getLogger(__name__)


class SimpleSerializer:
    """基於Dill的簡化序列化器，直接支持Python對象實例，包括lambda函數"""

    @staticmethod
    def serialize(obj: Any) -> bytes:
        """將任意Python對象序列化為二進制格式

        Args:
            obj: 要序列化的Python對象

        Returns:
            bytes: 序列化後的二進制數據
        """
        try:
            return dill.dumps(obj)
        except Exception as e:
            logger.error("序列化失敗: %s", e)
            # 記錄更詳細的錯誤信息
            logger.error("無法序列化的對象類型: %s", type(obj))
            raise

    @staticmethod
    def deserialize(data: Any) -> Any:
        """將二進制數據反序列化為原始Python對象

        Args:
            data: 序列化的二進制數據或其他對象

        Returns:
            Any: 反序列化後的Python對象
        """
        if not isinstance(data, bytes):
            return data

        try:
            return dill.loads(data)
        except Exception as e:
            logger.error("反序列化失敗: %s", e)
            return data
