# -*- coding: utf-8 -*-
"""Story themes module - 自動載入所有主題"""

import importlib
import os
from typing import Any, Dict, List


def _load_all_themes() -> List[Dict[str, Any]]:
    """自動載入themes資料夾中所有主題檔案，按創建時間排序（最新的在前）"""
    themes_with_time = []
    current_dir = os.path.dirname(__file__)

    # 掃描所有.py檔案（排除__init__.py）
    for filename in os.listdir(current_dir):
        if filename.endswith('.py') and filename != '__init__.py':
            module_name = filename[:-3]  # 移除.py副檔名
            file_path = os.path.join(current_dir, filename)
            try:
                # 動態導入模組
                module = importlib.import_module(f'.{module_name}', package=__name__)
                # 獲取THEME_CONFIG
                if hasattr(module, 'THEME_CONFIG'):
                    # 獲取文件創建時間
                    creation_time = os.path.getctime(file_path)
                    themes_with_time.append((module.THEME_CONFIG, creation_time))
            except ImportError as e:
                print(f"Warning: Failed to load theme {module_name}: {e}")
                continue

    # 按創建時間排序（最新創建的在前面）
    themes_with_time.sort(key=lambda x: x[1], reverse=True)

    # 只返回主題配置，不包含時間信息
    return [theme for theme, _ in themes_with_time]

# 自動載入所有主題
STORY_THEMES = _load_all_themes()

__all__ = ['STORY_THEMES']