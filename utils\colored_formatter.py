# -*- coding: utf-8 -*-
"""
彩色日誌格式化器
為不同的日誌級別提供顏色支持，提升後台日誌的可讀性
"""

import logging
import sys
from typing import Dict, Optional

try:
    import colorama
    from colorama import Fore, Style

    colorama.init(autoreset=True)  # 自動重置顏色
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

    # 如果colorama不可用，定義空的顏色常量
    class _DummyColor:
        def __getattr__(self, name):
            return ""

    Fore = Back = Style = _DummyColor()


class ColoredFormatter(logging.Formatter):
    """
    彩色日誌格式化器
    為不同的日誌級別添加顏色支持
    """

    # 日誌級別顏色映射
    LEVEL_COLORS: Dict[str, str] = {
        "DEBUG": Fore.CYAN,  # 青色 - 調試信息
        "INFO": Fore.GREEN,  # 綠色 - 一般信息
        "WARNING": Fore.YELLOW,  # 黃色 - 警告
        "ERROR": Fore.RED,  # 紅色 - 錯誤
        "CRITICAL": Fore.RED + Style.BRIGHT,  # 亮紅色 - 嚴重錯誤
    }

    # 模組名稱顏色
    MODULE_COLOR = Fore.BLUE

    # 時間戳顏色
    TIMESTAMP_COLOR = Fore.MAGENTA

    # 函數名和行號顏色
    LOCATION_COLOR = Fore.CYAN

    def __init__(
        self,
        fmt: Optional[str] = None,
        datefmt: Optional[str] = None,
        use_colors: bool = True,
    ):
        """
        初始化彩色格式化器

        Args:
            fmt: 日誌格式字符串
            datefmt: 日期格式字符串
            use_colors: 是否使用顏色（可用於禁用顏色輸出）
        """
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors and COLORAMA_AVAILABLE

        # 如果沒有提供格式，使用默認格式
        if fmt is None:
            self._style._fmt = (
                "%(asctime)s - %(name)s - %(levelname)s - "
                "%(module)s:%(funcName)s:%(lineno)d - %(message)s"
            )

    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日誌記錄，添加顏色

        Args:
            record: 日誌記錄對象

        Returns:
            格式化後的彩色日誌字符串
        """
        if not self.use_colors:
            return super().format(record)

        # 獲取原始格式化結果
        original_format = super().format(record)

        # 獲取顏色
        level_color = self.LEVEL_COLORS.get(record.levelname, "")

        # 分解日誌各部分並添加顏色
        parts = original_format.split(" - ")
        if len(parts) >= 5:
            timestamp = parts[0]
            logger_name = parts[1]
            level_name = parts[2]
            location = parts[3]  # module:function:line
            message = " - ".join(parts[4:])  # 剩餘部分作為消息

            # 構建彩色日誌
            colored_log = (
                f"{self.TIMESTAMP_COLOR}{timestamp}{Style.RESET_ALL} - "
                f"{self.MODULE_COLOR}{logger_name}{Style.RESET_ALL} - "
                f"{level_color}{level_name}{Style.RESET_ALL} - "
                f"{self.LOCATION_COLOR}{location}{Style.RESET_ALL} - "
                f"{message}"
            )

            return colored_log

        # 如果格式不符合預期，至少為級別名稱添加顏色
        if level_color:
            original_format = original_format.replace(
                record.levelname, f"{level_color}{record.levelname}{Style.RESET_ALL}"
            )

        return original_format


class ColoredConsoleHandler(logging.StreamHandler):
    """
    彩色控制台處理器
    自動檢測是否應該使用顏色輸出
    """

    def __init__(self, stream=None, use_colors: Optional[bool] = None):
        """
        初始化彩色控制台處理器

        Args:
            stream: 輸出流，默認為sys.stdout
            use_colors: 是否使用顏色，None表示自動檢測
        """
        super().__init__(stream or sys.stdout)

        if use_colors is None:
            # 自動檢測是否應該使用顏色
            use_colors = self._should_use_colors()

        self.use_colors = use_colors

    def _should_use_colors(self) -> bool:
        """
        檢測是否應該使用顏色輸出

        Returns:
            是否應該使用顏色
        """
        if not COLORAMA_AVAILABLE:
            return False

        # 檢查是否在終端中運行
        if hasattr(self.stream, "isatty") and self.stream.isatty():
            return True

        # 檢查環境變數
        import os

        if os.getenv("FORCE_COLOR", "").lower() in ("1", "true", "yes"):
            return True

        if os.getenv("NO_COLOR", ""):
            return False

        # 在Windows上，即使重定向也可以使用顏色（感謝colorama）
        if sys.platform == "win32":
            return True

        return False

    def set_formatter(self, formatter):
        """
        設置格式化器，如果是ColoredFormatter則傳遞顏色設置
        """
        if isinstance(formatter, ColoredFormatter):
            formatter.use_colors = self.use_colors
        super().setFormatter(formatter)


def create_colored_formatter(
    fmt: Optional[str] = None, datefmt: Optional[str] = None, use_colors: bool = True
) -> ColoredFormatter:
    """
    創建彩色格式化器的便利函數

    Args:
        fmt: 日誌格式字符串
        datefmt: 日期格式字符串
        use_colors: 是否使用顏色

    Returns:
        彩色格式化器實例
    """
    return ColoredFormatter(fmt=fmt, datefmt=datefmt, use_colors=use_colors)


def create_colored_console_handler(
    use_colors: Optional[bool] = None,
) -> ColoredConsoleHandler:
    """
    創建彩色控制台處理器的便利函數

    Args:
        use_colors: 是否使用顏色，None表示自動檢測

    Returns:
        彩色控制台處理器實例
    """
    return ColoredConsoleHandler(use_colors=use_colors)


# 導出主要類和函數
__all__ = [
    "ColoredFormatter",
    "ColoredConsoleHandler",
    "create_colored_formatter",
    "create_colored_console_handler",
    "COLORAMA_AVAILABLE",
]
