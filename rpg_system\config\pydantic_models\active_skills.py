"""
主動技能配置的Pydantic模型（增量式格式）
"""

from typing import Dict, List, Optional

from pydantic import BaseModel, Field, RootModel, field_validator

from .base_models import EffectDefinition, TargetLogicDetail


class XpToNextLevelConfig(BaseModel):
    """技能升級XP配置模型"""

    base_xp: int = Field(..., ge=1)
    multiplier: float = Field(..., gt=1.0)


class ActiveSkillConfig(BaseModel):
    """主動技能配置模型（增量式格式）"""

    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    target_type: str
    base_mp_cost: int = Field(..., ge=0)
    mp_cost_per_level: float = 0.0
    base_cooldown_turns: int = Field(..., ge=0)
    cooldown_reduction_per_level: float = 0.0
    base_effect_definitions: List[EffectDefinition]
    xp_gain_on_sacrifice: int = Field(..., ge=0)
    xp_to_next_level_config: XpToNextLevelConfig
    target_logic_details: Optional[List[TargetLogicDetail]] = None
    tags: Optional[List[str]] = None

    @field_validator("target_logic_details", mode="before")
    @classmethod
    def sort_target_logic_details_by_priority(cls, v):
        if v:
            # Ensure it's a list of dicts or TargetLogicDetail objects before sorting
            if all(isinstance(item, dict) for item in v):
                v.sort(key=lambda x: x.get("priority_score", 0), reverse=True)
            elif all(isinstance(item, TargetLogicDetail) for item in v):
                v.sort(key=lambda x: x.priority_score, reverse=True)
        return v


class ActiveSkillsConfig(RootModel[Dict[str, ActiveSkillConfig]]):
    """主動技能配置文件模型"""

    root: Dict[str, ActiveSkillConfig]

    def __getitem__(self, item):
        return self.root[item]

    def get(self, key, default=None):
        return self.root.get(key, default)

    def keys(self):
        return self.root.keys()

    def values(self):
        return self.root.values()

    def items(self):
        return self.root.items()
