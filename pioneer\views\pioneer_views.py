"""
Pioneer System 主要 Discord 視圖
包含主面板、個人資料、倉庫等核心視圖
"""

from typing import TYPE_CHECKING, List

import discord

from pioneer import repositories
from pioneer.models.pioneer_models import WarehouseItem
from utils.base_view import BaseView
from utils.logger import logger

if TYPE_CHECKING:
    from pioneer.core.game_data_loader import GameDataLoader
    from utils.base_view import BotType


class PioneerMainView(BaseView):
    """開拓者主面板視圖"""

    def __init__(
        self,
        user_id: int,
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data

    async def create_main_embed(
        self, interaction: discord.Interaction
    ) -> discord.Embed:
        """創建主面板 Embed (儀表板版本)"""
        profile = await repositories.get_pioneer_profile(self.user_id)
        user = interaction.user

        # 獲取當前時代信息
        era_info = await self._get_era_info(profile.current_era)

        embed = discord.Embed(
            title=f"📊 {user.display_name} 的開拓者儀表板",
            description=f"**{era_info['name']}** - {era_info['description']}\n\n"
            + self._get_progress_hint(profile),
            color=0x2B2D31,
        )

        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        # 經濟狀況
        from gacha.services import user_service

        gacha_user = await user_service.get_user(self.user_id)
        embed.add_field(
            name="💰 經濟狀況",
            value=f"油幣: {gacha_user.oil_balance:,}\n"
            f"待收取: {profile.pending_oil_earnings:,}\n"
            f"累計收益: {profile.total_oil_earnings:,}",
            inline=True,
        )

        # 狀態信息
        created_at_str = (
            profile.created_at.strftime("%Y-%m-%d") if profile.created_at else "未知"
        )
        embed.add_field(
            name="⚡ 狀態",
            value=f"能量: {profile.energy:,}/{profile.max_energy:,}\n"
            f"時代: 第 {profile.current_era} 時代\n"
            f"註冊於: {created_at_str}",
            inline=True,
        )

        # 新增：設施和商店概覽
        facilities = await repositories.get_user_facilities(self.user_id)
        active_facilities = sum(1 for f in facilities if f.is_active)
        shop_facilities = [
            f for f in facilities if f.facility_type == "shop"
        ]  # 假設商店類型為 'shop'

        # 設施總覽
        facility_overview = f"總數: {len(facilities)}\n運行中: {active_facilities}"
        embed.add_field(name="🏭 設施總覽", value=facility_overview, inline=True)

        # 添加下一步行動指引（僅對新手顯示）
        if profile.current_era <= 2:
            next_action = await self._get_next_action_guide()
            if next_action:
                embed.add_field(name="🎯 下一步行動", value=next_action, inline=False)

        # 商店狀態
        if shop_facilities:
            shop_status_parts = []
            for shop in shop_facilities[:2]:  # 最多顯示2個商店的狀態
                shop_name = shop.facility_name or "商店"
                # 這裡可以進一步獲取商店的貨架資訊，但為保持主面板簡潔，先顯示基本狀態
                status_emoji = "🟢" if shop.is_active else "🔴"
                shop_status_parts.append(f"{status_emoji} {shop_name}")

            shop_status = "\n".join(shop_status_parts) if shop_status_parts else "無"
            embed.add_field(name="🏪 商店狀態", value=shop_status, inline=False)

        # 技能信息
        skills = await repositories.get_user_skills(self.user_id)
        if skills:
            skill_text = []
            for skill in skills[:3]:  # 顯示前3個最高技能
                skill_name = self.game_data.get_skill_name(skill.skill_id)
                skill_text.append(f"• {skill_name}: Lv.{skill.level}")

            embed.add_field(
                name="🎯 核心技能",
                value="\n".join(skill_text) if skill_text else "尚無技能",
                inline=False,
            )

        updated_at_str = (
            profile.updated_at.strftime("%Y-%m-%d %H:%M")
            if profile.updated_at
            else "未知"
        )
        embed.set_footer(text=f"Pioneer System v1.1 | 最後更新: {updated_at_str}")

        return embed

    async def _get_era_info(self, era_id: int) -> dict:
        """獲取時代信息"""
        # 優先從故事配置獲取
        stories_config = self.game_data.get_config("stories")
        era_stories = stories_config.get("era_stories", {}) if stories_config else {}

        if str(era_id) in era_stories:
            era_story = era_stories[str(era_id)]
            return {
                "name": era_story.get("name", f"第{era_id}時代"),
                "description": era_story.get("description", "探索未知的領域"),
            }

        # 回退到原始配置
        era_configs = self.game_data.get_config("eras")
        if era_configs:
            for era in era_configs:
                if era.era_id == era_id:
                    return {
                        "name": era.name or f"第{era_id}時代",
                        "description": era.description or "探索未知的領域",
                    }
        return {"name": f"第{era_id}時代", "description": "繼續你的開拓之旅"}

    def _get_progress_hint(self, profile) -> str:
        """根據玩家進度提供提示"""
        # 從故事配置獲取進度提示
        stories_config = self.game_data.get_config("stories")
        progress_hints = (
            stories_config.get("progress_hints", {}) if stories_config else {}
        )

        era_key = f"era_{profile.current_era}"
        if era_key in progress_hints:
            hint = progress_hints[era_key]
            return f"💡 **建議：** {hint.get('suggestion', '探索更多功能')}"

        # 回退到默認提示
        default_hint = progress_hints.get("default", {})
        return (
            f"💡 **建議：** {default_hint.get('suggestion', '探索更高級的技術和設施')}"
        )

    async def _get_next_action_guide(self) -> str:
        """獲取下一步行動指引 - 工具系統版本"""
        try:
            # 檢查是否完成過採集
            warehouse_items = await repositories.get_user_warehouse(self.user_id)
            has_gathered = len(warehouse_items) > 0

            if not has_gathered:
                return "🌲 執行 `/gather 伐木` 開始你的開拓之旅"

            # 檢查是否有足夠木材製作木板
            wood_count = sum(
                item.quantity for item in warehouse_items if item.item_id == "wood"
            )
            if wood_count < 4:
                return "🌲 繼續執行 `/gather 伐木` 獲得更多木材（至少需要4個）"

            # 檢查是否有木板
            has_wooden_plank = any(
                item.item_id == "wooden_plank" for item in warehouse_items
            )
            if not has_wooden_plank:
                return "🔨 執行 `/craft 木板` 將木材加工成木板"

            # 檢查是否有木斧
            has_wooden_axe = any(
                item.item_id == "wooden_axe" for item in warehouse_items
            )
            if not has_wooden_axe:
                plank_count = sum(
                    item.quantity
                    for item in warehouse_items
                    if item.item_id == "wooden_plank"
                )
                if plank_count >= 3 and wood_count >= 2:
                    return "🪓 執行 `/craft 木斧` 製作你的第一個工具！"
                else:
                    return "🔨 繼續製作木板，需要3個木板+2個木材來製作木斧"

            # 檢查是否開始採礦
            has_stone = any(item.item_id == "stone" for item in warehouse_items)
            if not has_stone:
                return "⛏️ 執行 `/gather 採礦` 開始採集石材"

            # 檢查是否有燧石
            has_flint = any(item.item_id == "flint" for item in warehouse_items)
            if not has_flint:
                return "💎 繼續執行 `/gather 採礦` 尋找燧石（3%機率）"

            # 檢查是否有石製工具
            has_stone_tools = any(
                item.item_id in ["stone_axe", "stone_pickaxe"]
                for item in warehouse_items
            )
            if not has_stone_tools:
                return "⚒️ 執行 `/craft 石斧` 或 `/craft 石鎬` 製作更強的工具"

            # 檢查技能等級發展
            skills = await repositories.get_user_skills(self.user_id)
            woodcutting_level = next(
                (skill.level for skill in skills if skill.skill_id == "woodcutting"), 1
            )
            mining_level = next(
                (skill.level for skill in skills if skill.skill_id == "mining"), 1
            )

            if woodcutting_level < 5 or mining_level < 5:
                return "📈 用石製工具提升技能等級：目標是伐木和採礦都達到5級"

            # 新手階段完成，進入中期發展
            return "🎉 新手階段完成！現在可以考慮建造設施或探索更多功能"

        except Exception as e:
            logger.error("獲取任務進度失敗: %s", e, exc_info=True)
            return "📋 任務系統載入中..."

    @discord.ui.button(label="倉庫", style=discord.ButtonStyle.secondary, emoji="📦")
    async def warehouse_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """倉庫按鈕"""
        warehouse_items = await repositories.get_user_warehouse(self.user_id)
        view = PioneerWarehouseView(
            self.user_id,
            warehouse_items,
            self.game_data,
            self.bot,
        )
        embed = await view.create_warehouse_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(label="收取收益", style=discord.ButtonStyle.success, emoji="💰")
    async def collect_earnings_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """收取收益按鈕"""
        from pioneer.modules import action_module

        result = await action_module.execute_action(self.user_id, "collect_earnings")

        embed = discord.Embed(
            title="💰 收取收益" if result.success else "❌ 收取失敗",
            description=result.message,
            color=0x00FF00 if result.success else 0xFF0000,
        )

        # 刷新主面板
        new_main_embed = await self.create_main_embed(interaction)
        await interaction.response.edit_message(embed=new_main_embed, view=self)
        await interaction.followup.send(embed=embed)

    @discord.ui.button(
        label="設施管理", style=discord.ButtonStyle.secondary, emoji="🏭"
    )
    async def facility_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """設施管理按鈕"""
        from pioneer.views.facility_views import FacilityManagementView

        facilities = await repositories.get_user_facilities(self.user_id)
        view = FacilityManagementView(
            self.user_id,
            facilities,
            self.game_data,
            self.bot,
        )
        embed = await view.create_facility_management_embed()
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(
        label="研究中心", style=discord.ButtonStyle.primary, emoji="🔬", row=2
    )
    async def research_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """研究按鈕"""
        from pioneer.views.research_views import ResearchView

        view = ResearchView(self.user_id, self.game_data, self.bot)
        embed = await view.create_research_embed()
        await interaction.response.edit_message(embed=embed, view=view)


class PioneerWarehouseView(BaseView):
    """開拓者倉庫視圖"""

    def __init__(
        self,
        user_id: int,
        warehouse_items: List[WarehouseItem],
        game_data: "GameDataLoader",
        bot: "BotType",
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=300)
        self.game_data = game_data
        self.warehouse_items = warehouse_items
        self.current_page = 0
        self.items_per_page = 10

    async def create_warehouse_embed(self) -> discord.Embed:
        """創建倉庫 Embed"""
        # 使用倉庫容量管理器獲取容量信息
        from pioneer.modules.warehouse_capacity_manager import (
            get_warehouse_capacity_info,
        )

        capacity_info = await get_warehouse_capacity_info(self.user_id)

        embed = discord.Embed(
            title="📦 開拓者倉庫",
            description=f"您的物品存儲空間\n📊 容量: {capacity_info['current_usage']:,}/{capacity_info['total_capacity']:,} ({capacity_info['usage_percentage']:.1f}%)",
            color=0x2B2D31,
        )

        if not self.warehouse_items:
            embed.add_field(
                name="空倉庫",
                value="您的倉庫目前是空的\n開始採集或製作來獲得物品！",
                inline=False,
            )
        else:
            # 分頁顯示
            start_idx = self.current_page * self.items_per_page
            end_idx = start_idx + self.items_per_page
            page_items = self.warehouse_items[start_idx:end_idx]

            item_text = []
            total_value = 0

            for item in page_items:
                item_config = self.game_data.get_item_config(item.item_id)
                if item_config:
                    item_name = item_config.name
                    item_value = item_config.base_sell_price * item.quantity
                    total_value += item_value
                    item_text.append(
                        f"• {item_name} x{item.quantity:,} (價值: {item_value:,})"
                    )
                else:
                    item_text.append(f"• {item.item_id} x{item.quantity:,}")

            embed.add_field(
                name=f"物品清單 (第 {self.current_page + 1} 頁)",
                value="\n".join(item_text),
                inline=False,
            )

            # 統計信息
            total_items = len(self.warehouse_items)
            total_pages = (total_items + self.items_per_page - 1) // self.items_per_page

            embed.add_field(
                name="📊 統計",
                value=f"物品種類: {total_items:,}\n"
                f"當前頁面價值: {total_value:,}\n"
                f"頁數: {self.current_page + 1:,}/{total_pages:,}",
                inline=True,
            )

        return embed

    @discord.ui.button(label=None, style=discord.ButtonStyle.secondary, emoji="⬅️")
    async def prev_page_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """上一頁按鈕"""
        if self.current_page > 0:
            self.current_page -= 1
            embed = await self.create_warehouse_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label=None, style=discord.ButtonStyle.secondary, emoji="➡️")
    async def next_page_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """下一頁按鈕"""
        total_pages = (
            len(self.warehouse_items) + self.items_per_page - 1
        ) // self.items_per_page
        if self.current_page < total_pages - 1:
            self.current_page += 1
            embed = await self.create_warehouse_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(
        label="返回主頁", style=discord.ButtonStyle.primary, emoji="🏠", row=3
    )
    async def back_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """返回主頁按鈕"""
        view = PioneerMainView(self.user_id, self.game_data, self.bot)
        embed = await view.create_main_embed(interaction)
        await interaction.response.edit_message(embed=embed, view=view)

    @discord.ui.button(
        label="刷新", style=discord.ButtonStyle.secondary, emoji="🔄", row=3
    )
    async def refresh_button(
        self, interaction: discord.Interaction, _: discord.ui.Button
    ):
        """刷新按鈕"""
        self.warehouse_items = await repositories.get_user_warehouse(self.user_id)
        self.current_page = 0  # 重置到第一頁
        embed = await self.create_warehouse_embed()
        await interaction.response.edit_message(embed=embed, view=self)
