import asyncio
import inspect
import traceback
from typing import TYPE_CHECKING, Union

import aiohttp
import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from utils.cooldown_manager import cooldown_manager
from utils.logger import logger


def _collect_error_context(
    interaction: discord.Interaction,
    error: Exception,
    command_name: str,
    additional_info: Union[dict, None] = None,
) -> dict:
    """收集錯誤的詳細上下文信息，用於統一的錯誤記錄。

    Args:
        interaction: Discord 互動對象
        error: 發生的錯誤
        command_name: 指令名稱
        additional_info: 額外的上下文信息

    Returns:
        包含完整錯誤上下文的字典
    """

    context = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "command_name": command_name,
        "user_id": interaction.user.id,
        "user_name": str(interaction.user),
        "guild_id": interaction.guild.id if interaction.guild else None,
        "guild_name": interaction.guild.name if interaction.guild else None,
        "channel_id": interaction.channel.id if interaction.channel else None,
        "channel_name": getattr(interaction.channel, "name", "DM")
        if interaction.channel
        else None,
        "interaction_expired": interaction.is_expired(),
        "response_done": interaction.response.is_done(),
        "interaction_id": interaction.id,
        "interaction_token": interaction.token[:10] + "..."
        if interaction.token
        else None,  # 只記錄前10個字符用於調試
    }

    # 追蹤 View 相關信息（僅對特定錯誤類型進行詳細追蹤）
    should_trace_view = isinstance(error, discord.HTTPException) and (
        isinstance(error, discord.NotFound) or error.status in [404, 400]
    )

    if should_trace_view:
        try:
            view_info = {}

            # 快速獲取 interaction 數據（成本低）
            if hasattr(interaction, "data") and interaction.data:
                if "custom_id" in interaction.data:
                    view_info["custom_id"] = interaction.data["custom_id"]
                if "component_type" in interaction.data:
                    view_info["component_type"] = interaction.data["component_type"]

            # 只在確實需要時才進行昂貴的堆棧追蹤
            if error.__traceback__:
                # 從錯誤堆棧中找 View 文件（比 inspect.stack() 快）
                stack = traceback.extract_tb(error.__traceback__)
                for frame in stack:
                    filename = frame.filename
                    if "views" in filename:
                        view_info.update(
                            {
                                "source_file": filename.replace("\\", "/").split("/")[
                                    -1
                                ],
                                "line_number": frame.lineno,
                                "function_name": frame.name,
                            }
                        )
                        break

            # 僅在沒有從錯誤堆棧找到信息時才使用 inspect.stack()
            if not view_info.get("source_file"):
                # 限制堆棧深度，避免過深遍歷
                stack_frames = inspect.stack()[:10]  # 只檢查前10層
                for frame_info in stack_frames:
                    frame_self = frame_info.frame.f_locals.get("self")
                    if (
                        frame_self
                        and hasattr(frame_self, "__class__")
                        and "View" in frame_self.__class__.__name__
                        and "views" in frame_self.__class__.__module__
                    ):
                        class_name = frame_self.__class__.__name__
                        module_name = frame_self.__class__.__module__

                        view_info.update(
                            {
                                "view_class": class_name,
                                "view_module": module_name,
                                "actual_file": module_name.split(".")[-1] + ".py",
                                "is_base_class": "base_" in module_name.lower(),
                            }
                        )
                        break

            if view_info:
                context["view_info"] = view_info

        except Exception as trace_error:
            # 如果追蹤失敗，記錄簡單錯誤但不影響主要流程
            context["view_trace_error"] = str(trace_error)[:100]  # 限制錯誤訊息長度

    # 添加 Discord HTTP 錯誤的特定信息
    if isinstance(error, discord.HTTPException):
        context.update(
            {
                "http_status": getattr(error, "status", "N/A"),
                "http_code": getattr(error, "code", "N/A"),
                "http_text": getattr(error, "text", "N/A"),
                "http_response": getattr(error, "response", None),
            }
        )

    # 添加冷卻錯誤的特定信息
    if isinstance(error, (app_commands.CommandOnCooldown, commands.CommandOnCooldown)):
        context.update(
            {
                "cooldown_retry_after": getattr(error, "retry_after", "N/A"),
                "cooldown_type": getattr(error, "type", "N/A"),
            }
        )

    # 添加權限錯誤的特定信息
    if hasattr(error, "missing_permissions"):
        context["missing_permissions"] = getattr(error, "missing_permissions", [])

    # 合併額外信息
    if additional_info:
        context.update(additional_info)

    return context


def _log_error_with_context(
    level: str,
    message: str,
    context: dict,
    error: Union[Exception, None] = None,
    include_exc_info: bool = True,
    log_args: Union[tuple, None] = None,
):
    """使用統一格式記錄錯誤，包含完整的上下文信息。

    Args:
        level: 日誌級別 ('error', 'warning', 'info')
        message: 主要錯誤消息（支持 %s 格式化）
        context: 錯誤上下文信息
        error: 原始錯誤對象（用於 exc_info）
        include_exc_info: 是否包含異常堆棧信息
        log_args: message 的格式化參數
    """
    log_func = getattr(logger, level.lower())

    # 如果有格式化參數，先格式化消息
    if log_args:
        formatted_message = message % log_args
    else:
        formatted_message = message

    if include_exc_info and error:
        log_func(
            "%s. 詳細上下文: %s",
            formatted_message,
            context,
            exc_info=error,
        )
    else:
        log_func(
            "%s. 詳細上下文: %s",
            formatted_message,
            context,
        )


async def _send_error_response(
    interaction: discord.Interaction, message: str, ephemeral: bool = True
):
    """安全地發送錯誤訊息，處理互動已回應的情況。"""
    error_embed = discord.Embed(description=message, color=discord.Color.red())
    error_embed.set_author(
        name="嗚哦！操作失敗。",
        icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1395531660908433418/close.png?ex=687ac9a3&is=68797823&hm=a9b52b08731c4565c20eef62e907baf8b63cdaaedc122c31c8581650a3f06eec&",
    )
    error_embed.set_thumbnail(
        url="https://cdn.discordapp.com/attachments/1336020673730187334/1395531136079237141/1388098388058181723.webp?ex=687ac926&is=687977a6&hm=08cc0bb1571259448e29b8b1bf3c085ea9ae74ba448648ed305537fcd8bd6bbd&"
    )
    error_embed.set_footer(text="請勿惡意開小號洗錢，被抓到會被米米警察逮捕喔！")
    try:
        if not interaction.response.is_done():
            await interaction.response.send_message(
                embed=error_embed, ephemeral=ephemeral
            )
        else:
            await interaction.followup.send(embed=error_embed, ephemeral=ephemeral)
    except (discord.HTTPException, discord.NotFound) as e:
        logger.error("發送錯誤訊息時失敗: %s", e)


async def _handle_check_failure(
    interaction: discord.Interaction,
    error: Union[app_commands.CheckFailure, commands.CheckFailure],
    user_info: str,
):
    """處理 CheckFailure 錯誤。"""
    cmd_name = interaction.command.name if interaction.command is not None else "N/A"

    # 收集完整的錯誤上下文
    additional_context = {
        "check_failure_type": type(error).__name__,
        "check_failure_reason": str(error),
        "command_qualified_name": getattr(
            interaction.command, "qualified_name", cmd_name
        )
        if interaction.command
        else "N/A",
    }
    context = _collect_error_context(interaction, error, cmd_name, additional_context)

    # 檢查是否為全域黑名單檢查失敗（使用類型檢查，更高效且準確）
    from gacha.exceptions import GlobalBlacklistCheckFailureError

    if isinstance(error, GlobalBlacklistCheckFailureError) or isinstance(
        error.__cause__, GlobalBlacklistCheckFailureError
    ):
        # 直接使用我們自定義的詳細訊息
        msg = str(error)
        additional_context["error_category"] = "global_blacklist"
        log_level = "info"  # 全域黑名單是正常業務邏輯，使用 info 級別
    elif cmd_name in ["trade", "transfer"]:
        msg = "❌ **操作失敗**\n您的帳號不符合使用此功能的資格，若有疑問請聯繫管理員。"
        additional_context["error_category"] = "account_eligibility"
        log_level = "warning"
    else:
        msg = "❌ 您不滿足執行此指令的條件。"
        additional_context["error_category"] = "general_check_failure"
        log_level = "warning"

    context.update(additional_context)

    # 根據日誌級別決定記錄方式
    if log_level == "info":
        # 全域黑名單等正常業務邏輯錯誤使用簡化記錄
        logger.info(
            "指令檢查失敗: command '%s' by user %s. 錯誤類型: %s",
            cmd_name,
            user_info,
            type(error).__name__,
        )
    else:
        # warning 級別的錯誤保持詳細記錄
        _log_error_with_context(
            log_level,
            f"指令檢查失敗: command '{cmd_name}' by user {user_info}",
            context,
            error,
            include_exc_info=False,  # CheckFailure 通常不需要堆棧信息
        )

    await _send_error_response(interaction, msg)


async def _handle_cooldown(
    interaction: discord.Interaction,
    error: Union[app_commands.CommandOnCooldown, commands.CommandOnCooldown],
    user_info: str,
    command_name: str,
):
    """處理 CommandOnCooldown 錯誤。"""
    remaining = error.retry_after
    user_id = interaction.user.id

    # 冷卻錯誤處理 - 不需要收集詳細上下文

    if cooldown_manager.should_send_cooldown_message(user_id, remaining):
        msg = f"⏰ 指令冷卻中，請等待 {int(remaining)} 秒後再使用。"

        # 記錄簡化的冷卻信息
        logger.info(
            "指令冷卻: user %s, command '%s', retry_after: %.2fs",
            user_info,
            command_name,
            remaining,
        )

        await _send_error_response(interaction, msg)
    else:
        # 即使不發送消息也記錄冷卻事件（用於調試）
        logger.debug(
            "指令冷卻（已抑制消息）: user %s, command '%s', retry_after: %.2fs",
            user_info,
            command_name,
            remaining,
        )


async def _handle_discord_http_exception(
    interaction: discord.Interaction,
    error: discord.HTTPException,
    user_info: str,
    command_name: str,
):
    """處理 discord.HTTPException 錯誤。"""
    # 收集完整的錯誤上下文
    context = _collect_error_context(interaction, error, command_name)

    # 如果是 NotFound 錯誤，通常意味著互動已超時或已被處理，不應再嘗試回應
    if isinstance(error, discord.NotFound):
        additional_context = {
            "possible_cause": "指令執行時間過長或已被回應",
            "should_retry": False,
        }
        context.update(additional_context)

        # 構建一個更詳細的日誌標題
        view_info = context.get("view_info", {})

        # 從 context 或 view_info 獲取資訊
        cmd_name = context.get("command_name", "Unknown Command")
        view_class = view_info.get("view_class", "")
        source_file = view_info.get("actual_file", view_info.get("source_file", ""))
        # 確保 custom_id 的獲取途徑更可靠
        custom_id = view_info.get("custom_id")
        if not custom_id and hasattr(interaction, "data") and interaction.data:
            custom_id = interaction.data.get("custom_id", "N/A")
        else:
            custom_id = "N/A"

        # 建立一個描述性的標題
        if view_class and source_file:
            # 最理想的情況：顯示 View 類別和檔案
            log_title = f"View '{view_class}' in '{source_file}' (id: {custom_id})"
        elif custom_id != "N/A":
            # 次好的情況：顯示 custom_id
            log_title = f"Component with custom_id '{custom_id}'"
        else:
            # 後備情況：使用原始的 command_name
            log_title = cmd_name

        _log_error_with_context(
            "warning",
            "處理 '%s' by %s 時，互動已失效 (NotFound)",
            context,
            error,
            include_exc_info=False,  # NotFound 錯誤通常不需要堆棧信息
            log_args=(log_title, user_info),
        )
        return

    # 處理 429 限速錯誤
    if error.status == 429:
        additional_context = {
            "retry_after": getattr(error, "retry_after", "unknown"),
            "rate_limit_scope": "Discord API",
        }
        context.update(additional_context)

        _log_error_with_context(
            "error",
            f"Discord API 限速錯誤: command '{command_name}' by user {user_info}",
            context,
            error,
            include_exc_info=True,
        )
        return

    # 根據狀態碼確定錯誤級別和用戶消息
    if error.status in [401, 403]:
        log_level = "error"
        error_message = "❌ 權限不足，機器人或您自己缺少執行此操作的權限。"
        additional_context = {"error_category": "permission_denied"}
    elif "automod" in str(error.text).lower() or error.code == 200000:
        log_level = "warning"
        error_message = "❌ 您的請求內容被 Discord AutoMod 攔截了，請調整後再試。"
        additional_context = {"error_category": "automod_blocked"}
    elif error.status >= 500:
        log_level = "error"
        error_message = f"❌ 服務器內部錯誤 (HTTP {error.status})：{error.text}"
        additional_context = {"error_category": "server_error", "should_retry": True}
    else:
        log_level = "warning"
        error_message = "❌ 處理請求時發生 Discord 錯誤，請稍後再試。"
        additional_context = {"error_category": "unknown_http_error"}

    context.update(additional_context)

    # 記錄詳細的錯誤信息
    _log_error_with_context(
        log_level,
        f"Discord HTTP 錯誤: command '{command_name}' by user {user_info}",
        context,
        error,
        include_exc_info=True,
    )

    await _send_error_response(interaction, error_message)


async def _record_failed_command(
    bot: BotType,
    interaction: discord.Interaction,
    command_name: str,
    error: Exception,
):
    """記錄失敗的指令統計。"""
    try:
        if hasattr(bot, "command_usage_service"):
            command_usage_service = getattr(bot, "command_usage_service", None)
            if command_usage_service:
                from auxiliary.services.db_command_usage_service import (
                    CommandUsageRecord,
                )

                record = CommandUsageRecord(
                    command_name=command_name,
                    user_id=interaction.user.id,
                    guild_id=interaction.guild.id if interaction.guild else None,
                    channel_id=interaction.channel.id if interaction.channel else None,
                    command_type="interaction",
                    success=False,
                    error_message=str(error)[:500],
                )
                asyncio.create_task(command_usage_service.record_command_usage(record))
    except Exception as stats_error:
        logger.debug("記錄失敗互動統計時出錯: %s", stats_error)


async def handle_interaction_error(
    interaction: discord.Interaction, error: Exception, bot: BotType
):
    """一個集中的、處理任何來源於 Interaction 的錯誤的函式。"""
    command_name = (
        interaction.command.name
        if interaction.command
        else f"View/Modal on msg {interaction.message.id if interaction.message else 'N/A'}"
    )
    user_info = f"'{interaction.user}' (ID: {interaction.user.id})"
    original_error = getattr(error, "original", error)

    # 處理 CommandOnCooldown 錯誤
    if isinstance(
        original_error, (app_commands.CommandOnCooldown, commands.CommandOnCooldown)
    ):
        await _handle_cooldown(interaction, original_error, user_info, command_name)
        return

    # 處理 HTTPException 錯誤
    if isinstance(original_error, discord.HTTPException):
        await _handle_discord_http_exception(
            interaction, original_error, user_info, command_name
        )
        return

    # 處理 CheckFailure 錯誤
    if isinstance(original_error, (app_commands.CheckFailure, commands.CheckFailure)):
        await _handle_check_failure(interaction, original_error, user_info)
        return

    unresponsive_errors = (
        asyncio.TimeoutError,
        discord.errors.NotFound,
        aiohttp.ClientOSError,
        aiohttp.ServerDisconnectedError,
    )
    if isinstance(original_error, unresponsive_errors):
        # 使用統一的錯誤上下文收集
        additional_context = {
            "error_category": "unresponsive_error",
            "network_related": isinstance(
                original_error, (aiohttp.ClientOSError, aiohttp.ServerDisconnectedError)
            ),
            "timeout_related": isinstance(original_error, asyncio.TimeoutError),
        }
        context = _collect_error_context(
            interaction, original_error, command_name, additional_context
        )

        _log_error_with_context(
            "warning",
            f"無法響應的 Discord 錯誤: source '{command_name}' by user {user_info}",
            context,
            original_error,
            include_exc_info=isinstance(
                original_error, asyncio.TimeoutError
            ),  # 只對 TimeoutError 記錄堆棧
        )
        return

    permission_errors = (
        discord.app_commands.MissingPermissions,
        discord.app_commands.BotMissingPermissions,
    )
    if isinstance(original_error, permission_errors):
        missing_perms = ", ".join(original_error.missing_permissions)

        # 使用統一的錯誤上下文收集
        additional_context = {
            "error_category": "permission_error",
            "missing_permissions": original_error.missing_permissions,
            "permission_error_type": type(original_error).__name__,
        }
        context = _collect_error_context(
            interaction, original_error, command_name, additional_context
        )

        _log_error_with_context(
            "warning",
            f"權限錯誤: command '{command_name}' by user {user_info}",
            context,
            original_error,
            include_exc_info=False,  # 權限錯誤不需要堆棧信息
        )

        await _send_error_response(interaction, f"❌ 權限不足：{missing_perms}")
        return

    # 捕獲所有業務邏輯錯誤，作為友好的用戶提示
    from auxiliary.exceptions import AuxiliaryError
    from gacha.exceptions import BusinessError

    if isinstance(original_error, (BusinessError, AuxiliaryError)):
        # 業務邏輯錯誤使用簡化的日誌記錄，不包含詳細上下文
        logger.info(
            "業務邏輯錯誤: command '%s' by user %s. 錯誤類型: %s, 錯誤訊息: %s",
            command_name,
            user_info,
            type(original_error).__name__,
            str(original_error),
        )

        await _send_error_response(interaction, str(original_error))
        return

    # 對於所有其他未知的 Exception，將其視為 BUG
    # 使用統一的錯誤上下文收集
    additional_context = {
        "error_category": "unknown_error",
        "is_bug": True,
        "has_original_error": hasattr(error, "original"),
        "original_error_type": type(getattr(error, "original", error)).__name__,
        "wrapper_error_type": type(error).__name__
        if hasattr(error, "original")
        else None,
    }
    context = _collect_error_context(
        interaction, original_error, command_name, additional_context
    )

    _log_error_with_context(
        "error",
        f"未處理的互動錯誤 (BUG): command '{command_name}' by user {user_info}",
        context,
        original_error,
        include_exc_info=True,  # 未知錯誤需要完整的堆棧信息
    )

    await _send_error_response(
        interaction,
        "❌ 處理您的請求時發生了未知的內部錯誤，我們已收到通知並會盡快處理。",
    )
    await _record_failed_command(bot, interaction, command_name, original_error)
