"""
Discord 連接管理器 - 處理網路斷線和重連問題
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import TYPE_CHECKING, Any, Dict, Union

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    from discord.ext import commands

    BotType = commands.Bot

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Discord 連接管理器，處理網路斷線和重連問題"""

    def __init__(self, bot: BotType):
        self.bot = bot
        self.connection_stats: Dict[str, Any] = {
            "total_disconnects": 0,
            "total_reconnects": 0,
            "total_resumes": 0,
            "last_disconnect_time": None,
            "last_reconnect_time": None,
            "connection_start_time": None,
            "longest_disconnect_duration": 0.0,
            "network_errors": 0,
            "interaction_timeouts": 0,
        }
        self.is_reconnecting = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5.0  # 初始重連延遲（秒）
        self._recent_disconnect_times = []  # 追蹤最近的斷線時間

        # 不自動註冊事件處理器，改為手動調用方法

    def on_connect(self):
        """處理連接事件 - 手動調用"""
        self.connection_stats["connection_start_time"] = datetime.now(timezone.utc)
        self.reconnect_attempts = 0
        self.is_reconnecting = False
        # 只在有問題時才記錄，正常連接不需要 ERROR 級別
        logger.info("🔗 Discord 連接已建立")

    def on_disconnect(self):
        """處理斷線事件 - 手動調用"""
        disconnect_time = datetime.now(timezone.utc)
        total_disconnects = self.connection_stats["total_disconnects"]
        if isinstance(total_disconnects, int):
            self.connection_stats["total_disconnects"] = total_disconnects + 1
        self.connection_stats["last_disconnect_time"] = disconnect_time

        # 追蹤最近的斷線時間
        self._recent_disconnect_times.append(disconnect_time)

        # 斷線是需要關注的事件，使用 ERROR 級別
        logger.error(
            f"⚠️ Discord 連接已斷開 (第 {self.connection_stats['total_disconnects']} 次)"
        )

        # 計算連接持續時間
        connection_start_time = self.connection_stats["connection_start_time"]
        if connection_start_time and isinstance(connection_start_time, datetime):
            duration = disconnect_time - connection_start_time
            logger.error("連接持續時間: %.1f 秒", duration.total_seconds())

    async def on_resumed(self):
        """處理會話恢復事件 - 手動調用"""
        resume_time = datetime.now(timezone.utc)
        total_resumes = self.connection_stats["total_resumes"]
        if isinstance(total_resumes, int):
            self.connection_stats["total_resumes"] = total_resumes + 1
        self.connection_stats["last_reconnect_time"] = resume_time
        self.is_reconnecting = False

        # 恢復是好事，使用 WARNING 級別就夠了
        logger.warning(
            f"✅ Discord 會話已恢復 (第 {self.connection_stats['total_resumes']} 次)"
        )

        # 計算斷線時長
        last_disconnect_time = self.connection_stats["last_disconnect_time"]
        if last_disconnect_time and isinstance(last_disconnect_time, datetime):
            disconnect_duration = resume_time - last_disconnect_time
            duration_seconds = disconnect_duration.total_seconds()

            longest_duration = self.connection_stats["longest_disconnect_duration"]
            if (
                isinstance(longest_duration, (int, float))
                and duration_seconds > longest_duration
            ):
                self.connection_stats["longest_disconnect_duration"] = duration_seconds

            # 只有長時間斷線才用 ERROR 級別
            if duration_seconds > 30:  # 超過30秒
                logger.error("⚠️ 長時間斷線: %.1f 秒", duration_seconds)
            else:
                logger.warning("斷線時長: %.1f 秒", duration_seconds)

        # 檢查服務狀態
        await self._check_services_after_resume()

    def on_shard_connect(self, shard_id):
        """處理分片連接事件 - 手動調用"""
        logger.info("🔗 分片 %s 已連接", shard_id)

    def on_shard_disconnect(self, shard_id):
        """處理分片斷線事件 - 手動調用"""
        logger.error("⚠️ 分片 %s 已斷線", shard_id)

    def on_shard_resumed(self, shard_id):
        """處理分片恢復事件 - 手動調用"""
        logger.warning("✅ 分片 %s 已恢復", shard_id)

    async def _check_services_after_resume(self):
        """會話恢復後檢查服務狀態"""
        try:
            from database.postgresql.async_manager import get_pool, get_redis_client

            pool = get_pool()
            redis_client = get_redis_client()

            # 只有異常情況才使用 ERROR 級別，正常檢查不輸出日誌

            # 檢查數據庫連接
            if pool:
                if pool.is_closing():
                    logger.error("⚠️ 數據庫連接池已關閉，可能需要重新初始化")
            else:
                logger.error("⚠️ 數據庫連接池未初始化")

            # 檢查 Redis 連接
            if redis_client:
                try:
                    await asyncio.wait_for(redis_client.ping(), timeout=5.0)
                except asyncio.TimeoutError:
                    logger.error("⚠️ Redis 連接測試超時")
                except Exception as e:
                    logger.error("⚠️ Redis 連接測試失敗: %s", e)
            else:
                logger.error("⚠️ Redis 客戶端未初始化")

        except Exception as e:
            logger.error("檢查服務狀態時發生錯誤: %s", e, exc_info=True)

    def record_interaction_timeout(self):
        """記錄 interaction 超時"""
        timeouts = self.connection_stats["interaction_timeouts"]
        if isinstance(timeouts, int):
            self.connection_stats["interaction_timeouts"] = timeouts + 1

    def record_network_error(self):
        """記錄網路錯誤"""
        errors = self.connection_stats["network_errors"]
        if isinstance(errors, int):
            self.connection_stats["network_errors"] = errors + 1

    def get_connection_stats(self) -> Dict[str, Any]:
        """獲取連接統計信息"""
        stats = self.connection_stats.copy()

        # 計算運行時間
        connection_start_time = stats["connection_start_time"]
        if connection_start_time and isinstance(connection_start_time, datetime):
            uptime = datetime.now(timezone.utc) - connection_start_time
            stats["uptime_seconds"] = uptime.total_seconds()
        else:
            stats["uptime_seconds"] = 0

        return stats

    def get_connection_health(self) -> str:
        """獲取連接健康狀態"""
        stats = self.get_connection_stats()

        # 計算健康分數
        health_score = 100

        # 根據斷線次數扣分
        if stats["total_disconnects"] > 0:
            health_score -= min(stats["total_disconnects"] * 5, 30)

        # 根據網路錯誤扣分
        if stats["network_errors"] > 0:
            health_score -= min(stats["network_errors"] * 2, 20)

        # 根據 interaction 超時扣分
        if stats["interaction_timeouts"] > 0:
            health_score -= min(stats["interaction_timeouts"] * 1, 15)

        # 根據最長斷線時間扣分
        if stats["longest_disconnect_duration"] > 60:  # 超過1分鐘
            health_score -= min(int(stats["longest_disconnect_duration"] / 60) * 5, 25)

        health_score = max(0, health_score)

        if health_score >= 90:
            return f"🟢 優秀 ({health_score}%)"
        elif health_score >= 70:
            return f"🟡 良好 ({health_score}%)"
        elif health_score >= 50:
            return f"🟠 一般 ({health_score}%)"
        else:
            return f"🔴 較差 ({health_score}%)"

    def is_connection_stable(self) -> bool:
        """檢查連接是否穩定"""
        stats = self.get_connection_stats()

        # 如果正在重連，認為不穩定
        if self.is_reconnecting:
            return False

        # 如果最近5分鐘內有多次斷線，認為不穩定
        last_disconnect_time = stats["last_disconnect_time"]
        if last_disconnect_time and isinstance(last_disconnect_time, datetime):
            time_since_last_disconnect = (
                datetime.now(timezone.utc) - last_disconnect_time
            )
            if time_since_last_disconnect.total_seconds() < 300:  # 5分鐘內
                # 檢查最近的斷線頻率，而不是總數
                recent_disconnects = 0
                if hasattr(self, "_recent_disconnect_times"):
                    current_time = datetime.now(timezone.utc)
                    # 清理5分鐘前的記錄
                    self._recent_disconnect_times = [
                        t
                        for t in self._recent_disconnect_times
                        if (current_time - t).total_seconds() < 300
                    ]
                    recent_disconnects = len(self._recent_disconnect_times)

                # 如果5分鐘內斷線超過3次，認為不穩定
                if recent_disconnects > 3:
                    return False

        return True
