"""
卡片擁有者和許願者列表視圖
"""

from typing import Any, Dict, List, Union

import discord

from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import (
    user_collection_repository,
    user_wish_repository,
)
from gacha.views import utils as view_utils
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.base_view import BotType


class CardUsersView(BasePaginationView):
    """卡片擁有者和許願者列表視圖"""

    USERS_PER_PAGE = 10  # 每頁顯示的用戶數量

    def __init__(
        self,
        bot: BotType,
        user: Union[discord.User, discord.Member],
        card_id: int,
        card_data: Dict[str, Any],
        view_type: str = "owners",  # 'owners' 或 'wishers'
        current_page: int = 1,
        total_pages: int = 1,
        timeout: int = 300,
    ):
        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=current_page,
            total_pages=total_pages,
            timeout=timeout,
        )
        self.card_id = card_id
        self.card_data = card_data
        self.view_type = view_type
        self.users_data: List[Dict[str, Any]] = []
        self.total_count = 0

        # 更新按鈕樣式
        self._update_button_styles()

    async def async_init(self):
        """異步初始化方法"""
        # 分頁按鈕已在基類中添加，此處無需重複處理
        pass

    @discord.ui.button(
        label="擁有者",
        style=discord.ButtonStyle.primary,
        custom_id="show_owners",
        row=1,
    )
    async def owners_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """顯示擁有者列表按鈕"""
        await interaction.response.defer()
        if self.view_type == "owners":
            return

        self.view_type = "owners"
        self.current_page = 1
        await self._load_data()
        self._update_button_styles()

        embed = await self.get_current_page_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    @discord.ui.button(
        label="許願",
        style=discord.ButtonStyle.secondary,
        custom_id="show_wishers",
        row=1,
    )
    async def wishers_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        """顯示許願者列表按鈕"""
        await interaction.response.defer()
        if self.view_type == "wishers":
            return

        self.view_type = "wishers"
        self.current_page = 1
        await self._load_data()
        self._update_button_styles()

        embed = await self.get_current_page_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    @classmethod
    async def create(
        cls,
        bot: BotType,
        user: Union[discord.User, discord.Member],
        card_id: int,
        view_type: str = "owners",
        page: int = 1,
    ):
        """創建視圖實例"""
        # 根據規範，移除 try-except，讓錯誤自然冒泡
        # 獲取卡片信息
        card_obj = await master_card_repository.get_card(card_id)

        # 將 Card 對象轉換為字典
        card_data = {
            "card_id": card_obj.card_id,
            "name": card_obj.name,
            "series": card_obj.series,
            "rarity": card_obj.rarity.value if card_obj.rarity else 1,
            "image_url": card_obj.image_url,
            "pool_type": card_obj.pool_type,
        }

        # 創建實例
        instance = cls(
            bot=bot,
            user=user,
            card_id=card_id,
            card_data=card_data,
            view_type=view_type,
            current_page=page,
            total_pages=1,
        )

        # 異步初始化
        await instance.async_init()

        # 加載數據
        await instance._load_data()
        return instance

    async def _load_data(self):
        """加載當前頁面數據"""
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        if self.view_type == "owners":
            result = await user_collection_repository.get_card_owners_list(
                self.card_id, self.current_page, per_page=self.USERS_PER_PAGE
            )
            self.users_data = result["owners"]
        else:  # wishers
            result = await user_wish_repository.get_card_wishers_list(
                self.card_id, self.current_page, per_page=self.USERS_PER_PAGE
            )
            self.users_data = result["wishers"]

        self.total_pages = result["total_pages"]
        self.total_count = result["total_count"]
        self._refresh_button_states()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面數據"""
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        self.current_page = page
        await self._load_data()

        embed = await self.get_current_page_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    def _update_button_styles(self):
        """更新按鈕樣式"""
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                if item.custom_id == "show_owners":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.view_type == "owners"
                        else discord.ButtonStyle.secondary
                    )
                elif item.custom_id == "show_wishers":
                    item.style = (
                        discord.ButtonStyle.primary
                        if self.view_type == "wishers"
                        else discord.ButtonStyle.secondary
                    )

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁面的嵌入消息"""
        # 移除了 try-except，讓 BaseView.on_error 處理所有錯誤
        # 基本信息
        card_name = self.card_data.get("name", "未知卡片")
        card_series = self.card_data.get("series", "未知系列")
        rarity = self.card_data.get("rarity", 1)

        # 稀有度顯示
        rarity_names = {
            1: "C",
            2: "R",
            3: "SR",
            4: "SSR",
            5: "UR",
            6: "LR",
            7: "EX",
        }
        rarity_display = rarity_names.get(rarity, "Unknown")

        # 標題和描述
        if self.view_type == "owners":
            title = f"📋 {card_name} - 擁有者列表"
            description = f"**系列:** {card_series} | **稀有度:** {rarity_display}\n\n"

            if not self.users_data:
                description += "目前沒有人擁有這張卡片"
            else:
                description += f"**總擁有者數:** {self.total_count:,}\n\n"
                start_index = (self.current_page - 1) * self.USERS_PER_PAGE
                for i, owner in enumerate(self.users_data, start=start_index + 1):
                    user_id = owner["user_id"]
                    quantity = owner["quantity"]
                    star_level = owner["star_level"]

                    # 使用 view_utils 獲取星級 emoji
                    star_display = view_utils.get_star_emoji_string(star_level)

                    # 新格式：序號. 數量 用戶 星級(emoji)
                    description += (
                        f"{i:,}. `x{quantity:,}` <@{user_id}> {star_display}\n"
                    )

        else:  # wishers
            title = f"✨ {card_name} - 許願者列表"
            description = f"**系列:** {card_series} | **稀有度:** {rarity_display}\n\n"

            if not self.users_data:
                description += "目前沒有人許願這張卡片"
            else:
                description += f"**總許願者數:** {self.total_count}\n\n"
                start_index = (self.current_page - 1) * self.USERS_PER_PAGE
                for i, wisher in enumerate(self.users_data, start=start_index + 1):
                    user_id = wisher["user_id"]

                    description += f"{i}. <@{user_id}>\n"

        # 創建嵌入消息
        embed = discord.Embed(
            title=title, description=description, color=discord.Color.blue()
        )

        # 設置縮圖
        if self.card_data.get("image_url"):
            embed.set_thumbnail(url=self.card_data["image_url"])

        # 設置頁腳
        if self.total_pages > 1:
            embed.set_footer(
                text=f"頁面 {self.current_page}/{self.total_pages} | Card ID: {self.card_id}"
            )
        else:
            embed.set_footer(text=f"Card ID: {self.card_id}")

        return embed

    async def on_timeout(self):
        """超時處理"""
        # 根據規範，移除 try-except，讓 BaseView.on_error 處理所有錯誤
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = True

        if self.message:
            await self.message.edit(view=self)
