import random
from typing import List, Optional, Tuple, TypeVar

T = TypeVar("T")


def select_from_weighted_list(weighted_items: List[Tuple[T, float]]) -> Optional[T]:
    """
    從加權列表中隨機選擇一個項目。

    參數:
        weighted_items: 包含 (項目, 權重) 元組的列表。權重必須是非負數。

    返回:
        選中的項目，如果列表為空或總權重為零，則返回 None。
    """
    total_weight = sum(weight for _, weight in weighted_items)
    if total_weight <= 0:
        # 如果總權重為零或列表為空，無法進行選擇
        return None

    r = random.uniform(0, total_weight)
    cumulative_weight = 0
    for item, weight in weighted_items:
        cumulative_weight += weight
        if r < cumulative_weight:
            return item

    # 萬一由於浮點數精度問題未能命中，返回最後一個項目 (如果存在)
    # 這種情況理論上很少發生，但作為安全措施
    return weighted_items[-1][0] if weighted_items else None
