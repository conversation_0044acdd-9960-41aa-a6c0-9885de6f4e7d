"""
RPG戰鬥系統的嵌入構建器
用於創建戰鬥相關的Discord嵌入消息
"""

from typing import Any, Dict, List, Optional

import discord

from utils.logger import logger


class BattleEmbedBuilder:
    """戰鬥嵌入構建器"""

    @staticmethod
    def create_battle_preparation_embed(
        floor_id: str,
        player_team_info: List[Dict[str, Any]],
        monster_team_info: List[Dict[str, Any]],
    ) -> discord.Embed:
        """
        創建戰鬥準備嵌入

        Args:
            floor_id: 樓層ID
            player_team_info: 玩家隊伍信息
            monster_team_info: 怪物隊伍信息

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title="⚔️ 戰鬥準備",
                description=f"準備挑戰樓層 {floor_id}",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow(),
            )

            # 玩家隊伍信息
            if player_team_info:
                player_text = "\n".join(
                    [
                        f"• {unit.get('name', 'Unknown')} (Lv.{unit.get('level', 1)})"
                        for unit in player_team_info
                    ]
                )
                embed.add_field(name="👥 玩家隊伍", value=player_text, inline=True)

            # 怪物隊伍信息
            if monster_team_info:
                monster_text = "\n".join(
                    [
                        f"• {unit.get('name', 'Unknown')} (Lv.{unit.get('level', 1)})"
                        for unit in monster_team_info
                    ]
                )
                embed.add_field(name="👹 敵方隊伍", value=monster_text, inline=True)

            embed.set_footer(text="戰鬥即將開始...")

            return embed

        except Exception as e:
            logger.error("創建戰鬥準備嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 戰鬥準備",
                description="無法顯示戰鬥準備信息。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def create_battle_result_embed(
        battle_result: Dict[str, Any],
        completion_result: Optional[Dict[str, Any]] = None,
    ) -> discord.Embed:
        """
        創建戰鬥結果嵌入

        Args:
            battle_result: 戰鬥結果
            completion_result: 戰鬥完成處理結果

        Returns:
            Discord嵌入對象
        """
        try:
            # 確定結果顏色和標題
            winner = battle_result.get("winner")
            if winner == "PLAYER":
                color = discord.Color.green()
                title = "🎉 戰鬥勝利！"
                description = "恭喜您獲得了戰鬥勝利！"
            elif winner == "MONSTER":
                color = discord.Color.red()
                title = "💀 戰鬥失敗"
                description = "很遺憾，您在戰鬥中失敗了。"
            elif winner == "TIMEOUT":
                color = discord.Color.orange()
                title = "⏱️ 戰鬥超時"
                description = "戰鬥因為回合數限制而結束。"
            else:
                color = discord.Color.dark_grey()
                title = "🤝 戰鬥平局"
                description = "雙方同歸於盡。"

            embed = discord.Embed(
                title=title,
                description=description,
                color=color,
                timestamp=discord.utils.utcnow(),
            )

            # 基本戰鬥統計
            turn_count = battle_result.get("turn_count", 0)
            floor_id = (
                completion_result.get("floor_id") if completion_result else "Unknown"
            )

            embed.add_field(
                name="📊 戰鬥統計",
                value=f"回合數：{turn_count}\n樓層：{floor_id}",
                inline=True,
            )

            # 玩家隊伍狀態
            player_status = battle_result.get("player_team_status", [])
            if player_status:
                player_info = []
                for unit in player_status:
                    status_icon = "💚" if unit.get("is_alive") else "💀"
                    hp_info = f"{unit.get('current_hp', 0)}/{unit.get('max_hp', 0)}"
                    player_info.append(
                        f"{status_icon} {unit.get('name', 'Unknown')} ({hp_info})"
                    )

                embed.add_field(
                    name="👥 玩家隊伍", value="\n".join(player_info[:5]), inline=True
                )

            # 怪物隊伍狀態
            monster_status = battle_result.get("monster_team_status", [])
            if monster_status:
                monster_info = []
                for unit in monster_status:
                    status_icon = "💚" if unit.get("is_alive") else "💀"
                    hp_info = f"{unit.get('current_hp', 0)}/{unit.get('max_hp', 0)}"
                    monster_info.append(
                        f"{status_icon} {unit.get('name', 'Unknown')} ({hp_info})"
                    )

                embed.add_field(
                    name="👹 敵方隊伍", value="\n".join(monster_info[:5]), inline=True
                )

            # 獎勵信息
            if completion_result and completion_result.get("is_victory"):
                rewards = completion_result.get("rewards", [])
                if rewards:
                    reward_text = BattleEmbedBuilder._format_rewards(rewards)
                else:
                    reward_text = "戰鬥勝利！"

                embed.add_field(name="🎁 獎勵", value=reward_text, inline=False)

                # 首次通關標記
                if completion_result.get("is_first_clear"):
                    embed.add_field(
                        name="🌟 首次通關", value="恭喜您首次通關此樓層！", inline=False
                    )

            # 戰鬥日誌摘要
            battle_log = battle_result.get("battle_log", [])
            if battle_log:
                log_summary = BattleEmbedBuilder._create_battle_log_summary(battle_log)
                if log_summary:
                    embed.add_field(name="📜 戰鬥記錄", value=log_summary, inline=False)

            # 設置腳註
            battle_id = battle_result.get("battle_id", "Unknown")
            embed.set_footer(text=f"戰鬥ID: {battle_id}")

            return embed

        except Exception as e:
            logger.error("創建戰鬥結果嵌入時發生錯誤: %s", e, exc_info=True)
            return discord.Embed(
                title="⚠️ 戰鬥完成",
                description="戰鬥已完成，但無法顯示詳細結果。",
                color=discord.Color.orange(),
            )

    @staticmethod
    def create_battle_progress_embed(
        battle_id: str,
        current_turn: int,
        current_actor: str,
        player_team_status: List[Dict[str, Any]],
        monster_team_status: List[Dict[str, Any]],
    ) -> discord.Embed:
        """
        創建戰鬥進度嵌入

        Args:
            battle_id: 戰鬥ID
            current_turn: 當前回合
            current_actor: 當前行動者
            player_team_status: 玩家隊伍狀態
            monster_team_status: 怪物隊伍狀態

        Returns:
            Discord嵌入對象
        """
        try:
            embed = discord.Embed(
                title="⚔️ 戰鬥進行中",
                description=f"第 {current_turn} 回合 - {current_actor} 的回合",
                color=discord.Color.yellow(),
                timestamp=discord.utils.utcnow(),
            )

            # 玩家隊伍狀態
            if player_team_status:
                player_info = []
                for unit in player_team_status:
                    hp_percent = (
                        unit.get("current_hp", 0) / max(unit.get("max_hp", 1), 1)
                    ) * 100
                    hp_bar = BattleEmbedBuilder._create_hp_bar(hp_percent)
                    mp_info = (
                        f"MP: {unit.get('current_mp', 0)}/{unit.get('max_mp', 0)}"
                        if unit.get("max_mp", 0) > 0
                        else ""
                    )

                    # 添加狀態效果圖標
                    status_icons = BattleEmbedBuilder._get_status_effect_icons(
                        unit.get("status_effects", [])
                    )

                    unit_display = f"{unit.get('name', 'Unknown')} {hp_bar}"
                    if mp_info:
                        unit_display += f" {mp_info}"
                    if status_icons:
                        unit_display += f" {status_icons}"

                    player_info.append(unit_display)

                embed.add_field(
                    name="👥 玩家隊伍", value="\n".join(player_info[:5]), inline=True
                )

            # 怪物隊伍狀態
            if monster_team_status:
                monster_info = []
                for unit in monster_team_status:
                    hp_percent = (
                        unit.get("current_hp", 0) / max(unit.get("max_hp", 1), 1)
                    ) * 100
                    hp_bar = BattleEmbedBuilder._create_hp_bar(hp_percent)
                    mp_info = (
                        f"MP: {unit.get('current_mp', 0)}/{unit.get('max_mp', 0)}"
                        if unit.get("max_mp", 0) > 0
                        else ""
                    )

                    # 添加狀態效果圖標
                    status_icons = BattleEmbedBuilder._get_status_effect_icons(
                        unit.get("status_effects", [])
                    )

                    unit_display = f"{unit.get('name', 'Unknown')} {hp_bar}"
                    if mp_info:
                        unit_display += f" {mp_info}"
                    if status_icons:
                        unit_display += f" {status_icons}"

                    monster_info.append(unit_display)

                embed.add_field(
                    name="👹 敵方隊伍", value="\n".join(monster_info[:5]), inline=True
                )

            embed.set_footer(text=f"戰鬥ID: {battle_id}")

            return embed

        except Exception as e:
            logger.error("創建戰鬥進度嵌入時發生錯誤: %s", e, exc_info=True)
            return BattleEmbedBuilder.create_battle_error_embed(
                "無法顯示戰鬥進度信息。"
            )

    @staticmethod
    def create_battle_error_embed(error_message: str) -> discord.Embed:
        """
        創建戰鬥錯誤嵌入（View層職責）

        Args:
            error_message: 錯誤信息

        Returns:
            Discord嵌入對象
        """
        embed = discord.Embed(
            title="❌ 戰鬥錯誤",
            description=error_message,
            color=discord.Color.red(),
            timestamp=discord.utils.utcnow(),
        )

        embed.add_field(name="建議", value="請稍後再試，或聯繫管理員。", inline=False)

        return embed

    @staticmethod
    def _format_rewards(rewards: List[Dict[str, Any]]) -> str:
        """
        格式化獎勵信息

        Args:
            rewards: 獎勵列表

        Returns:
            格式化的獎勵文本
        """
        try:
            if not rewards:
                return "無獎勵"

            reward_lines = []
            for reward in rewards:
                reward_type = reward.get("type", "Unknown")
                reward_id = reward.get("id", "Unknown")
                amount = reward.get("amount", 1)

                if reward_type == "CURRENCY":
                    reward_lines.append(f"💰 {reward_id}: {amount}")
                elif reward_type == "ITEM":
                    reward_lines.append(f"📦 {reward_id}: {amount}")
                elif reward_type == "CARD":
                    reward_lines.append(f"🃏 {reward_id}: {amount}")
                else:
                    reward_lines.append(f"❓ {reward_id}: {amount}")

            return "\n".join(reward_lines[:10])  # 限制顯示數量

        except Exception as e:
            logger.error("格式化獎勵信息時發生錯誤: %s", e, exc_info=True)
            return "獎勵信息錯誤"

    @staticmethod
    def _create_battle_log_summary(battle_log: List[Dict[str, Any]]) -> str:
        """
        創建戰鬥日誌摘要

        Args:
            battle_log: 戰鬥日誌

        Returns:
            戰鬥日誌摘要文本
        """
        try:
            if not battle_log:
                return ""

            # 篩選重要事件
            important_events = []
            for entry in battle_log[-20:]:  # 只看最後20條
                action_type = entry.get("action_type", "")
                message = entry.get("message", "")

                if action_type in ["DEATH", "VICTORY", "CRITICAL_HIT", "SKILL_USE"]:
                    important_events.append(f"• {message}")

            return "\n".join(important_events[-5:])  # 只顯示最後5條重要事件

        except Exception as e:
            logger.error("創建戰鬥日誌摘要時發生錯誤: %s", e, exc_info=True)
            return ""

    @staticmethod
    def _create_hp_bar(hp_percent: float) -> str:
        """
        創建HP條

        Args:
            hp_percent: HP百分比

        Returns:
            HP條字符串
        """
        try:
            if hp_percent <= 0:
                return "💀"
            elif hp_percent <= 25:
                return "🔴▱▱▱▱"
            elif hp_percent <= 50:
                return "🟠🔴▱▱▱"
            elif hp_percent <= 75:
                return "🟡🟠🔴▱▱"
            elif hp_percent < 100:
                return "🟢🟡🟠🔴▱"
            else:
                return "🟢🟢🟢🟢🟢"

        except Exception as e:
            logger.error("創建HP條時發生錯誤: %s", e, exc_info=True)
            return "❓"

    @staticmethod
    def _get_status_effect_icons(status_effects: List[Dict[str, Any]]) -> str:
        """
        獲取狀態效果圖標

        Args:
            status_effects: 狀態效果列表

        Returns:
            狀態效果圖標字符串
        """
        try:
            if not status_effects:
                return ""

            icons = []
            for effect in status_effects[:5]:  # 最多顯示5個狀態效果
                effect_id = effect.get("effect_id", "")

                # 根據狀態效果ID映射圖標
                if "BURN" in effect_id.upper():
                    icons.append("🔥")
                elif "POISON" in effect_id.upper():
                    icons.append("☠️")
                elif "FREEZE" in effect_id.upper() or "FROZEN" in effect_id.upper():
                    icons.append("🧊")
                elif "STUN" in effect_id.upper():
                    icons.append("💫")
                elif "SHIELD" in effect_id.upper():
                    icons.append("🛡️")
                elif "BUFF" in effect_id.upper() or "BOOST" in effect_id.upper():
                    icons.append("✨")
                elif "DEBUFF" in effect_id.upper() or "WEAK" in effect_id.upper():
                    icons.append("🔻")
                elif "REGEN" in effect_id.upper() or "HEAL" in effect_id.upper():
                    icons.append("💚")
                else:
                    icons.append("🔮")  # 通用狀態效果圖標

            return "".join(icons)

        except Exception as e:
            logger.error("獲取狀態效果圖標時發生錯誤: %s", e, exc_info=True)
            return ""
