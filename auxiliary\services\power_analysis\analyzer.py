# -*- coding: utf-8 -*-
"""
戰力分析核心邏輯
"""

import datetime
import re
from typing import Any, Dict, Optional

from auxiliary.data.prompts.prompts.unified_prompts import (
    POWER_SYSTEM_PROMPT,
    POWER_USER_PROMPT,
)
from utils.logger import logger

from ..ai_core import ai_service
from ..ai_core import message_handler as message_handler_service


def _extract_field(content: str, field_name: str, default: str) -> str:
    """提取指定字段的內容"""
    pattern = f"<{field_name}>(.*?)</{field_name}>"
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        return default

    result = match.group(1).strip()

    # 特殊處理等級字段，清理重複的"級"字
    if field_name == "等級":
        # 移除多餘的"級"字，例如 "SS級級" -> "SS"
        result = re.sub(r"級+$", "", result)
        # 確保結果是有效的等級
        valid_levels = ["SSS", "SS", "S", "A", "B", "C", "D"]
        if result.upper() not in valid_levels:
            logger.warning("無效的等級格式: %s，使用預設值", result)
            return default

    return result


def _extract_power_result(api_response) -> Dict[str, Any]:
    """從API響應中提取戰力分析結果，直接解析XML內容"""
    from ..ai_core import ai_service

    response_text: Optional[str] = None
    try:
        # 使用通用的 parser 函數
        response_text = ai_service._get_raw_content_from_response(api_response)
        if not response_text:
            raise ValueError("AI 回應文本為空或無效")

        if not response_text.strip():
            logger.warning("AI響應文本為空或無效: '%s'", response_text)
            raise ValueError("AI響應文本為空或無效")

        # 清理響應文本，移除特殊編碼字符和nonce信息
        cleaned_response = re.sub(r"<0x[0-9A-Fa-f]{2}>", "", response_text)
        cleaned_response = re.sub(
            r"\[Request Nonce: [a-f0-9\-]+\]", "", cleaned_response
        )

        # 直接在整個響應中提取各個字段
        extracted_data = {
            "level": _extract_field(cleaned_response, "等級", "C"),
            "analysis": _extract_field(
                cleaned_response, "戰力分析", "無法提取戰力分析"
            ),
            "conclusion": _extract_field(cleaned_response, "結論", "無法提取結論"),
            "timestamp": datetime.datetime.now().isoformat(timespec="milliseconds"),
        }

        return extracted_data

    except Exception as e:
        logger.error(
            "提取戰力分析結果時出錯: %s. Response: %s",
            e,
            response_text[:500] if response_text else "None",
        )

        return {
            "level": "D",
            "analysis": "戰力分析處理時發生錯誤",
            "conclusion": "暫時無法分析",
            "timestamp": datetime.datetime.now().isoformat(timespec="milliseconds"),
        }


async def analyze_power_from_binary(
    image_data: bytes, prompt: Optional[str] = None, request_id: Optional[str] = None
) -> Dict[str, Any]:
    """從二進制圖像數據分析戰力"""
    try:
        user_prompt = prompt or POWER_USER_PROMPT

        api_response = await ai_service.process_with_image(
            image_data=image_data,
            prompt=user_prompt,
            system_prompt=POWER_SYSTEM_PROMPT,
            request_id=request_id,
        )

        return _extract_power_result(api_response)

    except ValueError:
        # 直接重新拋出由 ai_service.process_with_image 引發的 ValueError
        # 讓上層的 cog 來處理給使用者的提示
        raise
    except Exception as e:
        logger.error("戰力分析處理失敗: %s", e, exc_info=True)
        # 對於其他未知錯誤，也向上拋出
        raise


async def analyze_power_from_url(
    image_url: str, prompt: Optional[str] = None, request_id: Optional[str] = None
) -> Dict[str, Any]:
    """從URL分析戰力"""
    try:
        image_data = await message_handler_service._download_image_from_url(image_url)

        if not image_data:
            raise ValueError(f"從URL下載圖像失敗: {image_url}")

        return await analyze_power_from_binary(image_data, prompt, request_id)

    except Exception as e:
        logger.error("從URL處理圖像時出錯: %s", e)
        raise
