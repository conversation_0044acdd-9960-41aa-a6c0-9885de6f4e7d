"""
Pioneer System - Custom Exceptions
"""

from typing import List, Optional

from gacha.exceptions import BusinessError


class PioneerError(BusinessError):
    """開拓者系統的基礎業務邏輯錯誤。"""

    pass


class PioneerValidationError(PioneerError):
    """開拓者系統中的驗證錯誤。"""

    pass


class PioneerNotFoundError(PioneerError):
    """開拓者系統中找不到資源的錯誤。"""

    pass


class PioneerActionError(PioneerError):
    """開拓者系統中的動作錯誤。"""

    pass


class PioneerConfigError(PioneerError):
    """開拓者系統中的設定錯誤。"""

    pass


class PioneerActionCooldownError(PioneerActionError):
    """開拓者系統中的動作冷卻錯誤。"""

    pass


class PioneerCardAlreadyAssignedError(PioneerActionError):
    """卡片已經被指派。"""

    pass


class PioneerFacilityNotAvailableError(PioneerActionError):
    """設施不可用。"""

    pass


class PioneerInsufficientFundsError(PioneerActionError):
    """資金不足。"""

    pass


class PioneerInsufficientItemsError(PioneerActionError):
    """物品不足。"""

    pass


class PioneerInvalidSelectionError(PioneerValidationError):
    """無效的選擇。"""

    def __init__(self, resource_type: str, reason: str):
        self.resource_type = resource_type
        self.reason = reason
        message = f"無效的 {resource_type} 選擇: {reason}"
        super().__init__(message)


class PioneerResearchMaxLevelError(PioneerActionError):
    """研究已達最高等級。"""

    def __init__(self, project_name: str, max_level: int):
        self.project_name = project_name
        self.max_level = max_level
        message = f"研究項目「{project_name}」已達到最高等級 {max_level}。"
        super().__init__(message)


class RequirementsNotMetError(PioneerError):
    """當操作的需求條件未滿足時拋出。"""

    def __init__(self, missing_requirements: List[str]):
        self.missing_requirements = missing_requirements
        message = "未滿足以下條件：\n" + "\n".join(missing_requirements)
        super().__init__(message)


class ConfigNotFoundError(Exception):
    """
    當在遊戲資料中找不到預期的設定時引發。
    這是一個系統級錯誤，表示資料不一致。
    """

    def __init__(self, config_type: str, config_key: str):
        self.config_type = config_type
        self.config_key = config_key
        message = (
            f"設定檔錯誤：找不到類型為 '{config_type}' 的設定，鍵值為 '{config_key}'。"
        )
        super().__init__(message)


class PioneerDatabaseError(PioneerError):
    """Pioneer 系統的資料庫操作錯誤。"""

    def __init__(
        self,
        operation: str,
        details: str,
        original_exception: Optional[Exception] = None,
    ):
        self.operation = operation
        self.details = details
        self.original_exception = original_exception
        message = f"資料庫操作 '{operation}' 失敗: {details}"
        super().__init__(message)


class WarehouseItemNotFoundError(PioneerNotFoundError):
    """在倉庫中找不到指定的物品。"""

    def __init__(self, user_id: int, item_id: str):
        self.user_id = user_id
        self.item_id = item_id
        message = f"在玩家 {user_id} 的倉庫中找不到物品 {item_id}。"
        super().__init__(message)


class PioneerCalculationError(PioneerError):
    """開拓者系統中計算相關的錯誤。"""

    pass


class FacilityNotFoundError(PioneerNotFoundError):
    """找不到指定的設施。"""

    def __init__(self, facility_id: int):
        self.facility_id = facility_id
        message = f"找不到設施 ID: {facility_id}"
        super().__init__(message)


class CardAssignmentNotFoundError(PioneerNotFoundError):
    """找不到卡片指派。"""

    def __init__(self, by_column: str, value: int):
        self.by_column = by_column
        self.value = value
        message = f"找不到卡片指派，查詢條件: {by_column}={value}"
        super().__init__(message)


class PioneerProfileNotFoundError(PioneerNotFoundError):
    """找不到開拓者個人資料。"""

    def __init__(self, user_id: int):
        self.user_id = user_id
        message = f"找不到開拓者個人資料: {user_id}"
        super().__init__(message)


class ResearchLevelNotFoundError(PioneerNotFoundError):
    """找不到研究等級。"""

    def __init__(self, user_id: int, project_id: str):
        self.user_id = user_id
        self.project_id = project_id
        message = f"找不到玩家 {user_id} 的研究項目 {project_id} 的等級資訊。"
        super().__init__(message)


class SkillNotFoundError(PioneerNotFoundError):
    """找不到技能。"""

    def __init__(self, user_id: int, skill_id: str):
        self.user_id = user_id
        self.skill_id = skill_id
        message = f"找不到玩家 {user_id} 的技能: {skill_id}"
        super().__init__(message)
