import asyncio
import math
import random
from decimal import Decimal
from typing import TYPE_CHECKING, Union

import asyncpg
import discord
from discord import app_commands
from discord.ext import commands

import gacha.services.user_service as user_service

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from config.app_config import get_oil_emoji, get_ticket_emoji
from database.postgresql.async_manager import get_pool as get_db_pool
from gacha.exceptions import AlchemyValidationError
from gacha.repositories._base_repo import execute_query
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

MINIMUM_ALCHEMY_AMOUNT = 100000


class AlchemyConfirmView(BaseView):
    def __init__(
        self, bot: BotType, user_id: int, amount: int, cog_instance: "AlchemyCog"
    ):
        super().__init__(bot=bot, user_id=user_id, timeout=60)
        self.amount = amount
        self.cog = cog_instance

    @discord.ui.button(label="確認投入", style=discord.ButtonStyle.danger)
    async def confirm_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        # 根據開發規範：按鈕回調應該保持乾淨，不寫 try...except
        # 讓錯誤被 BaseView.on_error 自動捕捉
        await interaction.response.defer()

        for item in self.children:
            item.disabled = True  # type: ignore
        self.stop()

        # 呼叫 Cog 中的核心業務邏輯方法，讓錯誤自然拋出
        await self.cog.execute_alchemy_logic(interaction, self.user_id, self.amount)

    @discord.ui.button(label="取消", style=discord.ButtonStyle.secondary)
    async def cancel_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        for item in self.children:
            item.disabled = True  # type: ignore
        self.stop()
        cancel_embed = discord.Embed(
            title="操作已取消",
            description="你取消了煉金術操作。",
            color=discord.Color.blue(),
        )
        await interaction.response.edit_message(embed=cancel_embed, view=self)


class AlchemyCog(commands.Cog, name="煉金術"):
    """處理高風險煉金術系統的指令。"""

    def __init__(self, bot: BotType):
        self.bot = bot
        self.active_alchemy_users = set()
        logger.info("AlchemyCog initialized.")

    async def _log_alchemy_transaction(
        self,
        user_id: int,
        amount_invested: int,
        reward_tickets: int,
        volatility_factor: float,
        random_roll: float,
    ):
        """Asynchronously logs an alchemy transaction to the database."""
        query = """
            INSERT INTO alchemy_logs (
                user_id, amount_invested, reward_tickets, volatility_factor, random_roll
            )
            VALUES ($1, $2, $3, $4, $5)
        """
        pool = get_db_pool()
        async with pool.acquire() as conn:
            await execute_query(
                query,
                (
                    user_id,
                    amount_invested,
                    reward_tickets,
                    Decimal(str(volatility_factor)),
                    Decimal(str(random_roll)),
                ),
                connection=conn,
            )
            logger.debug(
                "Successfully logged alchemy transaction for user %s.", user_id
            )

    def _calculate_alchemy_reward(self, amount: int) -> tuple[int, float, float]:
        """
        Calculates the alchemy reward and returns the reward along with key metrics for
        logging.
        """
        expected_reward = amount * 0.0075
        volatility_factor = 0.5 + (math.log10(amount / 100000) * 0.15)
        roll = random.random()
        ev = expected_reward
        vol = volatility_factor
        final_reward = 0

        if roll < 0.20:
            final_reward = random.uniform(ev / (1 + vol * 1.8), ev / (1 + vol * 1.5))
        elif roll < 0.8985:
            final_reward = random.uniform(ev / (1 + vol * 0.5), ev / (1 + vol * 0.2))
        elif roll < 0.9485:
            final_reward = random.uniform(ev * 0.98, ev * 1.02)
        elif roll < 0.9985:
            final_reward = random.uniform(ev * (1 + vol * 2.0), ev * (1 + vol * 3.0))
        else:
            final_reward = ev * (1 + vol * 10.0)

        return max(0, int(final_reward)), volatility_factor, roll

    def _get_reward_ranges(self, amount: int) -> dict:
        """
        Calculates the potential reward ranges for each tier for a given amount.
        """
        if amount < 100000:
            return {}

        expected_reward = amount * 0.0075
        volatility_factor = 0.5 + (math.log10(amount / 100000) * 0.15)
        ev = expected_reward
        vol = volatility_factor

        ranges = {
            "catastrophe": (
                max(0, int(ev / (1 + vol * 1.8))),
                max(0, int(ev / (1 + vol * 1.5))),
            ),
            "standard_loss": (
                max(0, int(ev / (1 + vol * 0.5))),
                max(0, int(ev / (1 + vol * 0.2))),
            ),
            "breakeven": (max(0, int(ev * 0.98)), max(0, int(ev * 1.02))),
            "critical_hit": (
                max(0, int(ev * (1 + vol * 2.0))),
                max(0, int(ev * (1 + vol * 3.0))),
            ),
            "legendary": (
                max(0, int(ev * (1 + vol * 10.0))),
                max(0, int(ev * (1 + vol * 10.0))),
            ),
        }
        return ranges

    async def execute_alchemy_logic(
        self, interaction: discord.Interaction, user_id: int, amount: int
    ):
        """
        核心煉金術業務邏輯
        根據開發規範：業務邏輯程式碼中不應包含 try...except 來捕捉自己拋出的業務異常
        只管 raise，讓錯誤自然地「冒泡」出去
        """
        if user_id in self.active_alchemy_users:
            raise AlchemyValidationError("上一個煉金術操作仍在處理中，請稍候...")

        self.active_alchemy_users.add(user_id)
        try:
            oil_emoji = get_oil_emoji()
            ticket_emoji = get_ticket_emoji()

            # 初始化變數以避免 UnboundVariable 錯誤
            reward_tickets = 0

            pool = get_db_pool()
            async with pool.acquire() as conn:
                async with conn.transaction():
                    # 直接讓 asyncpg.CheckViolationError 轉換為業務異常
                    try:
                        await user_service.award_balance(
                            user_id=user_id,
                            amount=-amount,
                            transaction_type="alchemy",
                            reason=f"Alchemy investment of {amount:,} oil",
                            connection=conn,
                        )
                    except asyncpg.CheckViolationError as e:
                        raise AlchemyValidationError(
                            "煉金失敗，你的油錢好像不太夠喔！"
                        ) from e

                    reward_tickets, volatility_factor, random_roll = (
                        self._calculate_alchemy_reward(amount)
                    )

                    await user_service.increment_oil_ticket_balance(
                        user_id, Decimal(str(reward_tickets)), connection=conn
                    )

                # 異步記錄交易日誌，不影響主流程
                asyncio.create_task(
                    self._log_alchemy_transaction(
                        user_id=user_id,
                        amount_invested=amount,
                        reward_tickets=reward_tickets,
                        volatility_factor=volatility_factor,
                        random_roll=random_roll,
                    )
                )

                user = await user_service.get_user(user_id, connection=conn)
                new_oil_balance = user.oil_balance if user else Decimal(0)
                new_ticket_balance = await user_service.get_oil_ticket_balance(
                    user_id, connection=conn
                )

            # 創建成功回應的 Embed
            success_embed = SuccessEmbed(
                title="煉金術完成！",
                description=(
                    f"你投入了 **{amount:,}** {oil_emoji}，經過一陣劇烈的反應..."
                ),
            )
            success_embed.color = discord.Color.gold()
            success_embed.set_author(
                name=interaction.user.display_name,
                icon_url=interaction.user.display_avatar.url,
            )
            success_embed.add_field(
                name="✨ 獲得油票",
                value=f"**{reward_tickets:,}** {ticket_emoji}",
                inline=False,
            )
            success_embed.add_field(
                name="當前油幣餘額",
                value=f"{new_oil_balance:,} {oil_emoji}",
                inline=True,
            )
            success_embed.add_field(
                name="當前油票餘額",
                value=f"{new_ticket_balance:,.2f} {ticket_emoji}",
                inline=True,
            )

            # 根據獲得的獎勵設置不同的縮圖
            if reward_tickets > amount * 0.0075 * 1.5:
                success_embed.set_thumbnail(
                    url="https://cdn.discordapp.com/attachments/1336020673730187334/1388036881483038740/gold-coins.gif?ex=685f88d1&is=685e3751&hm=b91425b1f4835743343554381e833e8e8b8939753d258978f84e63e46481f83a&"
                )
            elif reward_tickets < amount * 0.0075 * 0.5:
                success_embed.set_thumbnail(
                    url="https://cdn.discordapp.com/attachments/1336020673730187334/1388037101892014130/fail.gif?ex=685f88ff&is=685e377f&hm=857f418d8f6f653652ccf63a3c5f5d5a2d915f4501491a4a41a54b7d4f54251a&"
                )

            # 更新互動回應
            # 由於這個方法是從按鈕回調呼叫的，interaction 已經被 defer()
            await interaction.edit_original_response(embed=success_embed, view=None)

        finally:
            # 確保清理活躍用戶狀態
            if user_id in self.active_alchemy_users:
                self.active_alchemy_users.remove(user_id)

    @app_commands.command(
        name="alchemy", description="進行一次高風險的煉金術，消耗油幣換取油票。"
    )
    @app_commands.checks.cooldown(1, 10.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(amount="投入的油幣數量，必須大於等於 100,000。")
    async def alchemy(self, interaction: discord.Interaction, amount: int):
        """
        處理煉金術指令
        根據開發規範：指令函數內部不應有 try...except 塊來捕捉 BusinessError
        讓它們被 @bot.tree.error 捕獲
        """
        # 1. 第一件事永遠是 defer()
        await interaction.response.defer(ephemeral=False, thinking=True)

        user_id = interaction.user.id
        oil_emoji = get_oil_emoji()
        ticket_emoji = get_ticket_emoji()

        # 2. 執行業務邏輯和驗證，讓錯誤自然拋出
        if amount < MINIMUM_ALCHEMY_AMOUNT:
            raise AlchemyValidationError(
                f"煉金術的最低投入金額為 {MINIMUM_ALCHEMY_AMOUNT:,}"
            )

        current_balance = await user_service.get_user_balance(user_id)
        if amount > current_balance:
            raise AlchemyValidationError(
                f"你的油幣餘額不足！\n需要: {amount:,} {oil_emoji}\n目前: "
                f"{current_balance:,} {oil_emoji}"
            )

        # 3. 如果成功，則創建確認界面
        confirmation_text = (
            f"**超級煉金術警告！**\n\n"
            f"你確定要投入 **{amount:,}** {oil_emoji} 進行一次史詩級的豪賭嗎？\n\n"
            "這筆巨款可能帶來驚人的回報，也可能讓你血本無歸！\n"
            "請再次確認你的決定。"
        )
        confirm_embed = discord.Embed(
            description=confirmation_text, color=discord.Color.red()
        )
        confirm_embed.set_author(name="⚠️ 確認煉金術操作")

        # 計算並顯示潛在回報範圍
        reward_ranges = self._get_reward_ranges(amount)

        def format_range(range_tuple):
            low, high = range_tuple
            if low == high:
                return f"約 **{low:,}**"
            return f"約 **{low:,}** ~ **{high:,}**"

        probabilities_text = (
            f"💥 **災難 (20.0%)**: "
            f"{format_range(reward_ranges['catastrophe'])} {ticket_emoji}\n"
            f"📉 **保底 (69.85%)**: "
            f"{format_range(reward_ranges['standard_loss'])} {ticket_emoji}\n"
            f"⚖️ **持平 (5.0%)**: "
            f"{format_range(reward_ranges['breakeven'])} {ticket_emoji}\n"
            f"🎉 **暴擊 (5.0%)**: "
            f"{format_range(reward_ranges['critical_hit'])} {ticket_emoji}\n"
            f"💎 **傳奇 (0.15%)**: "
            f"{format_range(reward_ranges['legendary'])} {ticket_emoji}"
        )
        confirm_embed.add_field(
            name="潛在回報預覽", value=probabilities_text, inline=False
        )

        confirm_embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1388189624207413308/alchemy.png?ex=686013d5&is=685ec255&hm=4941a5cd5e7970182c4b3935987e3bd68bd69f8190f22e677575b3e19ba9fdf5&"
        )
        confirm_embed.set_footer(
            text="此操作無法撤銷！", icon_url=interaction.user.display_avatar.url
        )

        # 4. 創建並發送自訂的 View
        view = AlchemyConfirmView(
            bot=self.bot, user_id=user_id, amount=amount, cog_instance=self
        )
        await interaction.followup.send(embed=confirm_embed, view=view, ephemeral=False)


async def setup(bot: BotType):
    """載入 AlchemyCog"""
    await bot.add_cog(AlchemyCog(bot))
    logger.info("AlchemyCog has been added to the bot.")
