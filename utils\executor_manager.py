"""
Executor Manager - 管理共享的 ThreadPoolExecutor 和 ProcessPoolExecutor
"""

import asyncio
from concurrent.futures import ProcessPoolExecutor
from typing import Any, Callable, TypeVar

from utils.logger import logger

# 創建一個 TypeVar 來表示 run_in_process 的返回類型
T = TypeVar("T")

# 全局 ProcessPoolExecutor 實例
_process_pool_executor: ProcessPoolExecutor | None = None


def get_process_pool_executor() -> ProcessPoolExecutor:
    """獲取全局共享的 ProcessPoolExecutor 實例"""
    global _process_pool_executor
    if _process_pool_executor is None:
        # 根據需要調整 max_workers
        _process_pool_executor = ProcessPoolExecutor()
        logger.info("已初始化 ProcessPoolExecutor")
    return _process_pool_executor


def shutdown_executors():
    """關閉所有執行器"""
    global _process_pool_executor
    if _process_pool_executor:
        _process_pool_executor.shutdown(wait=True)
        _process_pool_executor = None
        logger.info("ProcessPoolExecutor 已關閉")


async def run_in_process(func: Callable[..., T], *args: Any) -> T:
    """在 ProcessPoolExecutor 中異步運行一個同步函數"""
    loop = asyncio.get_running_loop()
    executor = get_process_pool_executor()
    # run_in_executor 會返回一個 future，我們 await 它來獲取結果
    result = await loop.run_in_executor(executor, func, *args)
    return result
