"""
用戶存儲庫模組 - 管理 gacha_users 表的模組級函數
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

import asyncpg

from gacha.exceptions import (
    DatabaseOperationError,
    InsufficientOilTicketsError,
    UserNotFoundError,
)
from gacha.models.models import GachaUser
from gacha.repositories._base_repo import (
    execute_query,
    fetch_one,
    fetch_value,
)
from utils.logger import logger

# 表名常量
TABLE_NAME = "gacha_users"


async def get_user(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> GachaUser:
    """獲取用戶信息，如果不存在則拋出異常"""
    query = f"SELECT * FROM {TABLE_NAME} WHERE user_id = $1"
    result = await fetch_one(query, (user_id,), connection=connection)
    if not result:
        raise UserNotFoundError(f"找不到用戶 {user_id}", user_id=user_id)
    return GachaUser(
        user_id=result["user_id"],
        oil_balance=result["oil_balance"],
        total_draws=result["total_draws"],
        last_daily_claim=result["last_daily_claim"],
        created_at=result["created_at"],
        updated_at=result["updated_at"],
        nickname=result.get("nickname"),
        wish_slots=result.get("wish_slots", 1),
        wish_power_level=result.get("wish_power_level", 1),
        oil_ticket_balance=result.get("oil_ticket_balance", Decimal("0.00")),
    )


async def get_user_optional(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[GachaUser]:
    """獲取用戶信息，如果不存在則返回None"""
    try:
        return await get_user(user_id, connection=connection)
    except UserNotFoundError:
        return None


async def get_user_for_update(
    user_id: int, connection: asyncpg.Connection
) -> GachaUser:
    """在事務中獲取用戶信息並鎖定行"""
    if not connection:
        raise ValueError("Connection is required for get_user_for_update")
    query = f"SELECT * FROM {TABLE_NAME} WHERE user_id = $1 FOR UPDATE"
    result = await connection.fetchrow(query, user_id)
    if not result:
        raise UserNotFoundError(f"找不到用戶 {user_id} 或無法鎖定", user_id=user_id)
    return GachaUser(
        user_id=result["user_id"],
        oil_balance=result["oil_balance"],
        total_draws=result["total_draws"],
        last_daily_claim=result["last_daily_claim"],
        created_at=result["created_at"],
        updated_at=result["updated_at"],
        nickname=result.get("nickname"),
        wish_slots=result.get("wish_slots", 1),
        wish_power_level=result.get("wish_power_level", 1),
    )


async def create_user(
    user_id: int,
    nickname: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> GachaUser:
    """創建新用戶"""
    now = datetime.now()
    user_data = None

    try:
        if nickname:
            query = f"""
                INSERT INTO {TABLE_NAME} (user_id, oil_balance, total_draws, last_daily_claim, created_at, updated_at, nickname, wish_slots, wish_power_level)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                ON CONFLICT (user_id) DO NOTHING
                RETURNING *
            """
            user_data = await fetch_one(
                query,
                (user_id, 5000, 0, None, now, now, nickname, 1, 1),
                connection=connection,
            )
        else:
            query = f"""
                INSERT INTO {TABLE_NAME} (user_id, oil_balance, total_draws, last_daily_claim, created_at, updated_at, wish_slots, wish_power_level)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (user_id) DO NOTHING
                RETURNING *
            """
            user_data = await fetch_one(
                query, (user_id, 5000, 0, None, now, now, 1, 1), connection=connection
            )

        if not user_data:
            # 可能是衝突，嘗試獲取現有用戶
            logger.warning(
                "User creation for %s returned no data (maybe conflict?), attempting fetch.",
                user_id,
            )
            existing_user = await get_user(user_id, connection=connection)
            if existing_user:
                return existing_user
            else:
                raise DatabaseOperationError(f"無法創建或獲取用戶 {user_id}")

        return GachaUser(
            user_id=user_data["user_id"],
            oil_balance=user_data["oil_balance"],
            total_draws=user_data["total_draws"],
            last_daily_claim=user_data["last_daily_claim"],
            created_at=user_data["created_at"],
            updated_at=user_data["updated_at"],
            nickname=user_data.get("nickname"),
            wish_slots=user_data.get("wish_slots", 1),
            wish_power_level=user_data.get("wish_power_level", 1),
            oil_ticket_balance=user_data.get("oil_ticket_balance", Decimal("0.00")),
        )
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"創建用戶 {user_id} 時發生資料庫錯誤: {e}") from e


async def update_daily_claim(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶每日獎勵領取時間"""
    query = f"UPDATE {TABLE_NAME} SET last_daily_claim = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1"
    status = await execute_query(query, (user_id,), connection=connection)
    if status != 1:
        raise UserNotFoundError(
            f"更新用戶 {user_id} 每日領取狀態失敗，用戶可能不存在", user_id=user_id
        )


async def update_balance(
    user_id: int, new_balance: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶油幣餘額"""
    query = f"UPDATE {TABLE_NAME} SET oil_balance = $1, updated_at = CURRENT_TIMESTAMP WHERE user_id = $2"
    status = await execute_query(query, (new_balance, user_id), connection=connection)
    if status != 1:
        raise UserNotFoundError(
            f"更新用戶 {user_id} 餘額失敗，用戶可能不存在", user_id=user_id
        )


async def get_user_balance_for_update(
    user_id: int, connection: asyncpg.Connection
) -> int:
    """在事務中獲取用戶餘額並鎖定行"""
    if not connection:
        raise ValueError("Connection is required for get_user_balance_for_update")
    query = f"SELECT oil_balance FROM {TABLE_NAME} WHERE user_id = $1 FOR UPDATE"
    balance = await connection.fetchval(query, user_id)
    if balance is None:
        raise UserNotFoundError(f"找不到用戶 {user_id} 的餘額", user_id=user_id)
    return balance


async def update_wish_slots(
    user_id: int, new_slots: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶的許願槽位數量"""
    query = f"UPDATE {TABLE_NAME} SET wish_slots = $1, updated_at = NOW() WHERE user_id = $2"
    status = await execute_query(query, (new_slots, user_id), connection=connection)
    if status != 1:
        logger.warning(
            "Update_wish_slots for user %s to %s slots reported status: %s",
            user_id,
            new_slots,
            status,
        )
        raise UserNotFoundError(
            f"更新用戶 {user_id} 許願槽位失敗，用戶可能不存在", user_id=user_id
        )


async def update_wish_power_level(
    user_id: int, new_level: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """更新用戶的許願能量等級"""
    query = f"UPDATE {TABLE_NAME} SET wish_power_level = $1, updated_at = NOW() WHERE user_id = $2"
    status = await execute_query(query, (new_level, user_id), connection=connection)
    if status != 1:
        logger.warning(
            "Update_wish_power_level for user %s to level %s reported status: %s",
            user_id,
            new_level,
            status,
        )
        raise UserNotFoundError(
            f"更新用戶 {user_id} 許願能量等級失敗，用戶可能不存在", user_id=user_id
        )


async def update_nickname_if_changed(
    user_id: int, nickname: str, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """僅當提供的暱稱與現有暱稱不同或現有暱稱為NULL時，才更新用戶暱稱"""
    query = f"""
        UPDATE {TABLE_NAME}
        SET nickname = $1, updated_at = NOW()
        WHERE user_id = $2
        AND (nickname IS NULL OR nickname != $1)
    """
    status = await execute_query(query, (nickname, user_id), connection=connection)
    return status == 1


async def increment_oil_ticket_balance(
    user_id: int,
    amount_to_add: Decimal,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """增加用戶的 oil_ticket_balance 小數餘額"""
    query = f"""
        UPDATE {TABLE_NAME}
        SET oil_ticket_balance = oil_ticket_balance + $1, updated_at = NOW()
        WHERE user_id = $2
    """
    status = await execute_query(query, (amount_to_add, user_id), connection=connection)
    if status != 1:
        raise UserNotFoundError(
            f"增加用戶 {user_id} 油票餘額失敗，用戶可能不存在", user_id=user_id
        )


async def deduct_oil_tickets(
    user_id: int,
    integer_amount_to_deduct: int,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """從用戶的 oil_ticket_balance 小數餘額中減去指定的整數油票數量。在扣除前檢查餘額"""
    query = f"""
        UPDATE {TABLE_NAME}
        SET oil_ticket_balance = oil_ticket_balance - $1, updated_at = NOW()
        WHERE user_id = $2 AND FLOOR(oil_ticket_balance) >= $1::DECIMAL
    """
    logger.debug(
        "[deduct_oil_tickets] 嘗試為用戶 %s 扣除 %s 油票",
        user_id,
        integer_amount_to_deduct,
    )
    status = await execute_query(
        query, (integer_amount_to_deduct, user_id), connection=connection
    )
    if status != 1:
        # 檢查是用戶不存在還是餘額不足
        current_balance = await get_oil_ticket_balance(user_id, connection=connection)
        if current_balance is None:
            logger.error("[deduct_oil_tickets] 找不到用戶 %s", user_id)
            raise UserNotFoundError(f"找不到用戶 {user_id}", user_id=user_id)
        else:
            logger.debug(
                "[deduct_oil_tickets] 用戶 %s 的當前餘額為 %s，嘗試扣除 %s",
                user_id,
                current_balance,
                integer_amount_to_deduct,
            )
            available_balance = int(
                current_balance.quantize(Decimal("1"), rounding="ROUND_DOWN")
            )
            logger.error(
                "[deduct_oil_tickets] 用戶 %s 油票餘額不足，需要 %s，目前可用 %s (原始: %s)",
                user_id,
                integer_amount_to_deduct,
                available_balance,
                current_balance,
            )
            raise InsufficientOilTicketsError(
                required_amount=integer_amount_to_deduct,
                current_balance=available_balance,
            )
    logger.debug(
        "[deduct_oil_tickets] 成功為用戶 %s 扣除 %s 油票",
        user_id,
        integer_amount_to_deduct,
    )


async def get_oil_ticket_balance(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Decimal:
    """獲取用戶的 oil_ticket_balance 小數餘額，如果不存在則拋出異常"""
    query = f"SELECT oil_ticket_balance FROM {TABLE_NAME} WHERE user_id = $1"
    balance = await fetch_value(query, (user_id,), connection=connection)
    if balance is None:
        raise UserNotFoundError(f"找不到用戶 {user_id} 的油票餘額", user_id=user_id)
    return balance


async def get_oil_ticket_balance_optional(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[Decimal]:
    """獲取用戶的 oil_ticket_balance 小數餘額，如果不存在則返回None"""
    try:
        return await get_oil_ticket_balance(user_id, connection=connection)
    except UserNotFoundError:
        return None


async def get_oil_ticket_balance_for_update(
    user_id: int, connection: asyncpg.Connection
) -> Decimal:
    """獲取用戶的油票餘額並鎖定行以進行更新"""
    if not connection:
        raise ValueError("Connection is required for get_oil_ticket_balance_for_update")
    balance = await connection.fetchval(
        f"SELECT oil_ticket_balance FROM {TABLE_NAME} WHERE user_id = $1 FOR UPDATE",
        user_id,
    )
    if balance is None:
        raise UserNotFoundError(f"找不到用戶 {user_id}", user_id=user_id)
    return balance


async def update_oil_ticket_balance(
    user_id: int,
    new_balance: Decimal,
    connection: asyncpg.Connection,
) -> None:
    """更新用戶的油票餘額"""
    if not connection:
        raise ValueError("Connection is required for update_oil_ticket_balance")
    await connection.execute(
        f"UPDATE {TABLE_NAME} SET oil_ticket_balance = $1 WHERE user_id = $2",
        new_balance,
        user_id,
    )


async def increment_draws(
    user_id: int, draw_count: int, connection: Optional[asyncpg.Connection] = None
) -> None:
    """增加用戶的抽卡總數"""
    query = f"""
        UPDATE {TABLE_NAME}
        SET total_draws = total_draws + $1, updated_at = NOW()
        WHERE user_id = $2
    """
    status = await execute_query(query, (draw_count, user_id), connection=connection)
    if status != 1:
        raise UserNotFoundError(
            f"增加用戶 {user_id} 的抽卡次數失敗，用戶可能不存在", user_id=user_id
        )
