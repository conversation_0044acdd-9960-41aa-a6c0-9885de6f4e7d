from typing import Optional

import asyncpg

from gacha.repositories.collection import user_collection_repository
from utils.logger import logger

"""
卡片排序索引管理器模組
提供計算和正規化卡片自定義排序索引的函數。
"""

SORT_INDEX_STEP = 1000000
DEFAULT_WINDOW_SIZE = 20


async def calculate_index_for_position(
    user_id: int,
    card_id: int,
    current_position: int,
    target_position: int,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """
    (Async) 計算移動到指定位置的新索引值
    """
    conn = connection
    logger.debug(
        "[GACHA][SORT][CALC] 開始計算索引: 用戶=%s, 卡片=%s, 當前位置=%s, 目標位置=%s",
        user_id,
        card_id,
        current_position,
        target_position,
    )

    adjusted_target = target_position
    if current_position < target_position:
        adjusted_target = target_position + 1
        logger.debug(
            "[GACHA][SORT][CALC] 調整目標位置: %s -> %s (補償位置偏移)",
            target_position,
            adjusted_target,
        )

    if adjusted_target == 1:
        min_index = await user_collection_repository.get_min_custom_sort_index(
            user_id, connection=conn
        )
        if min_index is not None:
            new_index = min_index - SORT_INDEX_STEP
            logger.debug(
                "[GACHA][SORT][CALC] 目標位置=1, 計算的索引值=%s, 現有最小索引=%s",
                new_index,
                min_index,
            )
            return new_index
        else:
            logger.debug("[GACHA][SORT][CALC] 目標位置=1, 列表為空, 索引值=0")
            return 0

    total_sorted_cards = await user_collection_repository.count_sorted_cards(
        user_id, connection=conn
    )

    if adjusted_target > total_sorted_cards:
        max_index = await user_collection_repository.get_max_custom_sort_index(
            user_id, connection=conn
        )
        new_index = (
            max_index + SORT_INDEX_STEP if max_index is not None else SORT_INDEX_STEP
        )
        logger.debug(
            "[GACHA][SORT][CALC] 目標位置超出範圍(%s>%s), 索引值=%s",
            adjusted_target,
            total_sorted_cards,
            new_index,
        )
        return new_index

    target_prev_card = None
    if adjusted_target > 1:
        target_prev_card = await user_collection_repository.get_card_at_position(
            user_id, adjusted_target - 1, connection=conn
        )

    target_card = await user_collection_repository.get_card_at_position(
        user_id, adjusted_target, connection=conn
    )

    # 如果目標卡片是正在移動的卡片，則目標應該是它的下一張卡
    if target_card and target_card.get("card_id") == card_id:
        next_position = adjusted_target + 1
        if next_position <= total_sorted_cards:
            target_card = await user_collection_repository.get_card_at_position(
                user_id, next_position, connection=conn
            )
        else:
            target_card = None

    if target_prev_card and target_card:
        prev_index = target_prev_card["sort_index"]
        next_index = target_card["sort_index"]

        # 如果前一張卡是正在移動的卡片，那麼我們需要更前一張卡來計算
        if target_prev_card.get("card_id") == card_id and adjusted_target > 2:
            prev_prev_card = await user_collection_repository.get_card_at_position(
                user_id, adjusted_target - 2, connection=conn
            )
            if prev_prev_card:
                prev_index = prev_prev_card["sort_index"]

        gap = next_index - prev_index
        if gap <= 1:
            logger.debug("[GACHA][SORT][CALC] 索引間隔過小(%s)，執行正規化", gap)
            anchor_card_id = (
                target_prev_card["card_id"]
                if target_prev_card
                else target_card["card_id"]
            )
            await _renormalize_local_indexes(
                user_id, anchor_card_id, DEFAULT_WINDOW_SIZE * 2, connection=conn
            )

            # 重新獲取正規化後的卡片
            if adjusted_target > 1:
                target_prev_card = (
                    await user_collection_repository.get_card_at_position(
                        user_id, adjusted_target - 1, connection=conn
                    )
                )
            target_card = await user_collection_repository.get_card_at_position(
                user_id, adjusted_target, connection=conn
            )

            if target_card and target_card.get("card_id") == card_id:
                next_position = adjusted_target + 1
                if next_position <= total_sorted_cards:
                    target_card = await user_collection_repository.get_card_at_position(
                        user_id, next_position, connection=conn
                    )
                else:
                    target_card = None

            if target_prev_card and target_card:
                prev_index = target_prev_card["sort_index"]
                next_index = target_card["sort_index"]
                gap = next_index - prev_index
                if gap <= 1:
                    logger.error("[GACHA][SORT] 正規化後索引間隔仍然不足")
                    raise RuntimeError("正規化後索引間隔仍然不足")

        position_ratio = 0.5  # 在中間插入
        new_index = prev_index + int(gap * position_ratio)
        logger.debug(
            "[GACHA][SORT][CALC] 目標位置=%s, 索引值=%s, 前後索引=%s/%s, 間隔=%s",
            adjusted_target,
            new_index,
            prev_index,
            next_index,
            gap,
        )
        return new_index
    elif target_prev_card:  # 移到最後
        new_index = target_prev_card["sort_index"] + SORT_INDEX_STEP
        logger.debug(
            "[GACHA][SORT][CALC] 目標位置=%s (最後), 索引值=%s, 前一索引=%s",
            adjusted_target,
            new_index,
            target_prev_card["sort_index"],
        )
        return new_index
    elif target_card:  # 移到最前
        new_index = max(0, target_card["sort_index"] - SORT_INDEX_STEP)
        logger.debug(
            "[GACHA][SORT][CALC] 目標位置=%s (最前), 索引值=%s, 目標索引=%s",
            adjusted_target,
            new_index,
            target_card["sort_index"],
        )
        return new_index
    else:  # 列表為空
        new_index = adjusted_target * SORT_INDEX_STEP
        logger.debug(
            "[GACHA][SORT][CALC] 目標位置=%s (空列表), 默認索引值=%s",
            adjusted_target,
            new_index,
        )
        return new_index


async def _renormalize_local_indexes(
    user_id: int,
    anchor_card_id: int,
    window_size: int = DEFAULT_WINDOW_SIZE,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """
    (Async) 局部正規化排序索引。
    在一個事務中執行，以確保數據一致性。
    """
    if not connection:
        logger.error(
            "[GACHA][SORT] _renormalize_local_indexes called without a transaction connection."
        )
        raise ValueError("Renormalization must be performed within a transaction.")

    try:
        logger.debug(
            "[GACHA][SORT] 執行局部索引正規化: user_id=%s, anchor_card=%s, window=%s",
            user_id,
            anchor_card_id,
            window_size,
        )
        anchor_card_info = await user_collection_repository.get_card_sort_info(
            user_id, anchor_card_id, connection=connection
        )

        if not anchor_card_info or anchor_card_info["sort_index"] is None:
            logger.warning("[GACHA][SORT] 局部正規化失敗：錨點卡片不存在或沒有排序索引")
            return False

        anchor_position = anchor_card_info["rank"]
        start_position = max(1, anchor_position - window_size // 2)

        cards_in_window = await user_collection_repository.get_cards_by_rank_range(
            user_id, start_position, window_size, connection=connection
        )

        if not cards_in_window:
            logger.warning("[GACHA][SORT] 局部正規化失敗：窗口內沒有卡片")
            return False

        total_cards_in_window = len(cards_in_window)

        before_window_card = (
            await user_collection_repository.get_card_at_position(
                user_id, start_position - 1, connection=connection
            )
            if start_position > 1
            else None
        )

        # 獲取窗口結束位置的卡片來確定end_index
        last_card_in_window_pos = start_position + total_cards_in_window - 1
        after_window_card = await user_collection_repository.get_card_at_position(
            user_id, last_card_in_window_pos + 1, connection=connection
        )

        start_index = before_window_card["sort_index"] if before_window_card else 0
        end_index = (
            after_window_card["sort_index"]
            if after_window_card
            else start_index + (total_cards_in_window + 2) * SORT_INDEX_STEP
        )

        index_range = end_index - start_index

        if index_range < total_cards_in_window + 1:
            logger.warning(
                "[GACHA][SORT] 局部正規化：索引範圍不足(%s)，使用標準步長", index_range
            )
            index_step = SORT_INDEX_STEP
            current_index = start_index + index_step
        else:
            index_step = index_range // (total_cards_in_window + 1)
            current_index = start_index + index_step

        updates = [
            (card["card_id"], current_index + i * index_step)
            for i, card in enumerate(cards_in_window)
        ]

        await user_collection_repository.bulk_update_sort_indexes(
            user_id, updates, connection=connection
        )

        logger.debug(
            "[GACHA][SORT] 局部正規化成功：更新了 %s 張卡片的索引", len(updates)
        )
        return True
    except Exception as e:
        logger.error("[GACHA][SORT] 局部正規化錯誤: %s", str(e), exc_info=True)
        raise
