"""
Gacha系統抽卡視圖
提供 DrawView 和 MultiDrawView 功能
"""

from dataclasses import dataclass
from typing import Any, Dict, List, NamedTuple, Optional, Union

import discord
from discord.ext import commands

from gacha.models.models import Card, CardWithStatus
from gacha.services import favorite_service
from gacha.views import utils as view_utils

# 移除對 FavoriteButton 的依賴，因為我們會將其邏輯直接整合進來
# from gacha.views.collection.favorite_component import FavoriteButton
from gacha.views.embeds.gacha.draw_embed_builder import DrawEmbedBuilder
from gacha.views.embeds.gacha.multi_draw_embed_builder import MultiDrawEmbedBuilder
from utils.base_view import BaseView, BotType
from utils.logger import logger


class ButtonOnCooldown(commands.CommandError):
    def __init__(self, retry_after: float):
        self.retry_after = retry_after


def _key(interaction: discord.Interaction):
    return interaction.user


class CooldownView(BaseView):
    """一個帶有按鈕冷卻功能的抽卡視圖基類"""

    def __init__(
        self,
        *,
        bot: BotType,
        user: discord.User,
        balance: int,
        timeout: int = 600,
        on_draw_callback=None,
        pool_types=None,
        pool_config_key=None,
    ):
        super().__init__(bot=bot, user_id=user.id, timeout=timeout)
        self.user = user
        self.balance = balance
        self.on_draw_callback = on_draw_callback
        self.pool_types = pool_types or ["main", "special"]
        self.pool_config_key = pool_config_key or "all"
        self.cd = commands.CooldownMapping.from_cooldown(1.0, 2.0, _key)

    # on_error is now handled by BaseView

    async def _process_draw_callback(
        self, interaction: discord.Interaction, is_multi_draw: bool = False
    ):
        await interaction.response.defer(thinking=False)
        if self.on_draw_callback:
            await self.on_draw_callback(
                self.pool_types, self.pool_config_key, is_multi_draw, interaction
            )

    @staticmethod
    def extract_card_info(card_data: CardWithStatus) -> tuple:
        """從 CardWithStatus 對象中提取標準化的卡片信息元組。"""
        card = card_data.card
        status = card_data.status
        pool_type = card_data.pool_type

        rarity_code_raw = (
            card.rarity.value if hasattr(card.rarity, "value") else card.rarity
        )
        # 根據規範，移除 try-except，讓轉換錯誤冒泡，以便追蹤資料問題
        rarity_code = int(rarity_code_raw)

        return (
            card,
            status.is_new_card,
            status.star_level,
            status.is_wish,
            status.is_favorite,
            pool_type,
            rarity_code,
        )

    async def on_timeout(self):
        logger.debug("BaseDrawView 超時，用戶ID: %s", self.user_id)
        self.stop()


class CurrentCardWrapper(NamedTuple):
    card: Optional[Card]
    is_favorite: bool


class DrawView(CooldownView):
    """抽卡結果視圖，包含繼續抽卡和加入最愛的按鈕"""

    def __init__(
        self,
        bot: BotType,
        user: discord.User,
        card: Union[Card, CardWithStatus],
        balance: int,
        is_new_card: bool = False,
        star_level: int = 0,
        is_wish: bool = False,
        is_favorite: bool = False,
        owner_count: int = 0,
        on_draw_callback=None,
        pool_types=None,
        pool_config_key=None,
    ):
        super().__init__(
            bot=bot,
            user=user,
            balance=balance,
            timeout=600,
            on_draw_callback=on_draw_callback,
            pool_types=pool_types,
            pool_config_key=pool_config_key,
        )
        cws = view_utils.to_card_with_status(card)
        card_info = self.extract_card_info(cws)
        self.card: Card = card_info[0]
        self.is_new_card = card_info[1] if card_info[1] is not None else is_new_card
        self.star_level = card_info[2] if card_info[2] is not None else star_level
        self.is_wish = card_info[3] if card_info[3] is not None else is_wish
        self.is_favorite: bool = (
            card_info[4] if card_info[4] is not None else is_favorite
        )
        self.pool_type = card_info[5]
        self.card_id = self.card.card_id
        self.owner_count = owner_count
        # 為了符合生態，我們也將 DrawView 的收藏按鈕改為裝飾器
        self._refresh_favorite_button_style()

    @property
    def current_card(self) -> CurrentCardWrapper:
        return CurrentCardWrapper(card=self.card, is_favorite=self.is_favorite)

    def _refresh_favorite_button_style(self):
        if not hasattr(self, "favorite_button"):
            return
        self.favorite_button.label = "已收藏" if self.is_favorite else "加入最愛"
        self.favorite_button.style = (
            discord.ButtonStyle.success
            if self.is_favorite
            else discord.ButtonStyle.secondary
        )
        self.favorite_button.emoji = (
            "<a:pu:1365482490478989353>"
            if self.is_favorite
            else "<a:sw:1365447243863429273>"
        )

    @discord.ui.button(
        label="繼續抽卡",
        style=discord.ButtonStyle.primary,
        custom_id="draw_again",
        row=0,
    )
    async def draw_again_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        # 在按鈕回調中單獨處理冷卻
        retry_after = self.cd.update_rate_limit(interaction)
        if retry_after:
            return  # 靜默處理
        await self._process_draw_callback(interaction, is_multi_draw=False)

    @discord.ui.button(
        label="十連抽",
        style=discord.ButtonStyle.secondary,
        custom_id="multi_draw",
        row=0,
    )
    async def multi_draw_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        # 在按鈕回調中單獨處理冷卻
        retry_after = self.cd.update_rate_limit(interaction)
        if retry_after:
            return  # 靜默處理
        await self._process_draw_callback(interaction, is_multi_draw=True)

    @discord.ui.button(
        label="加入最愛",
        style=discord.ButtonStyle.secondary,
        custom_id="toggle_favorite",
        row=0,
    )
    async def favorite_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        await interaction.response.defer()
        # 根據規範，移除 try-except，讓 favorite_service 的 BusinessError 自然冒泡
        new_is_favorite = await favorite_service.toggle_favorite_card(
            self.user.id, self.card_id, operator_id=self.user.id
        )
        self.is_favorite = new_is_favorite
        self._refresh_favorite_button_style()
        embed = await self.get_current_page_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    async def get_current_page_embed(
        self, interaction: Optional[discord.Interaction] = None
    ) -> discord.Embed:
        current_interaction = (
            interaction
            if interaction is not None
            else getattr(self, "interaction", None)
        )
        builder = DrawEmbedBuilder(
            user=self.user,
            card=self.card,
            balance=self.balance,
            is_new_card=self.is_new_card,
            pool_type=self.pool_type,
            is_wish=self.is_wish,
            star_level=self.star_level,
            is_favorite=self.is_favorite,
            owner_count=self.owner_count,
            interaction=current_interaction,
        )
        return builder.build_embed()

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        if self.card_id == card_id:
            self.is_favorite = is_favorite
            return True
        return False


@dataclass
class ProcessedCardInfo:
    card: Card
    is_new_card: bool
    star_level: int
    is_wish: bool
    is_favorite: bool
    pool_type: str
    rarity_code: int
    owner_count: int
    original_result: Union[Dict[str, Any], CardWithStatus]


class MultiDrawView(CooldownView):
    """十連抽結果視圖"""

    def __init__(
        self,
        bot: BotType,
        user: discord.User,
        cards_results: List[Union[Dict[str, Any], CardWithStatus]],
        owner_counts: Dict[int, int],
        balance: int,
        timeout: int = 600,
        on_multi_draw_callback=None,
        pool_types=None,
        pool_config_key=None,
    ):
        super().__init__(
            bot=bot,
            user=user,
            balance=balance,
            timeout=timeout,
            on_draw_callback=on_multi_draw_callback,
            pool_types=pool_types,
            pool_config_key=pool_config_key,
        )
        self.cards_results_raw = cards_results
        self.owner_counts = owner_counts
        self.current_index = 0
        self.total_cards = len(self.cards_results_raw)
        self._processed_cards_view_data: List[ProcessedCardInfo] = []

        processed_cws_list: List[CardWithStatus] = []
        for raw_result in self.cards_results_raw:
            # 根據規範，移除 try-except，讓資料轉換錯誤冒泡，以便追蹤和修復
            cws = view_utils.to_card_with_status(raw_result)
            processed_cws_list.append(cws)
            card, is_new, star, is_wish_flag, is_fav, pool, rarity = (
                self.extract_card_info(cws)
            )
            owner_count = self.owner_counts.get(card.card_id, 0)
            self._processed_cards_view_data.append(
                ProcessedCardInfo(
                    card=card,
                    is_new_card=is_new,
                    star_level=star,
                    is_wish=is_wish_flag,
                    is_favorite=is_fav,
                    pool_type=pool,
                    rarity_code=rarity,
                    owner_count=owner_count,
                    original_result=cws,
                )
            )

        self._shared_builder = MultiDrawEmbedBuilder(
            user=self.user,
            cards_results=self.cards_results_raw,
            balance=self.balance,
            nickname=self.user.display_name,
        )
        self._refresh_buttons_state()

    @property
    def current_card(self) -> Optional[ProcessedCardInfo]:
        if (
            not self._processed_cards_view_data
            or self.current_index < 0
            or self.current_index >= self.total_cards
        ):
            return None
        return self._processed_cards_view_data[self.current_index]

    def _refresh_buttons_state(self):
        """刷新所有按鈕的狀態。"""
        current_info = self.current_card
        if not current_info:
            # 如果沒有卡片信息，禁用所有按鈕
            self.continue_multi_draw_button.disabled = True
            self.favorite_button.disabled = True
            self.prev_button.disabled = True
            self.next_button.disabled = True
            return

        # 繼續抽卡按鈕
        self.continue_multi_draw_button.disabled = False

        # 收藏按鈕
        self.favorite_button.disabled = False
        self.favorite_button.label = (
            "已收藏" if current_info.is_favorite else "加入最愛"
        )
        self.favorite_button.style = (
            discord.ButtonStyle.success
            if current_info.is_favorite
            else discord.ButtonStyle.secondary
        )
        self.favorite_button.emoji = (
            "<a:pu:1365482490478989353>"
            if current_info.is_favorite
            else "<a:sw:1365447243863429273>"
        )

        # 翻頁按鈕
        self.prev_button.disabled = self.current_index == 0
        self.next_button.disabled = self.current_index == self.total_cards - 1

    # --- 按鈕定義順序決定了它們的顯示順序 ---

    @discord.ui.button(
        label="繼續十連", emoji="🎴", style=discord.ButtonStyle.primary, row=0
    )
    async def continue_multi_draw_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        # 在按鈕回調中單獨處理冷卻
        retry_after = self.cd.update_rate_limit(interaction)
        if retry_after:
            return  # 靜默處理
        await self._process_draw_callback(interaction, is_multi_draw=True)

    @discord.ui.button(label="加入最愛", style=discord.ButtonStyle.secondary, row=0)
    async def favorite_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        current_card_info = self.current_card
        if not current_card_info:
            # 如果沒有卡片，直接回應，不進行任何操作
            await interaction.response.send_message(
                "沒有可操作的卡片。", ephemeral=True
            )
            return

        card_id_to_toggle = current_card_info.card.card_id
        # 根據規範，移除 try-except，讓 favorite_service 的 BusinessError 自然冒泡
        new_is_favorite = await favorite_service.toggle_favorite_card(
            self.user.id, card_id_to_toggle, operator_id=self.user.id
        )

        self.update_favorite_state(card_id_to_toggle, new_is_favorite)
        self._refresh_buttons_state()

        embed = await self.get_combined_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(
        label=None, emoji="⬅️", style=discord.ButtonStyle.secondary, row=0
    )
    async def prev_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        self.current_index = (
            self.current_index - 1 + self.total_cards
        ) % self.total_cards
        self._refresh_buttons_state()
        embed = await self.get_combined_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(
        label=None, emoji="➡️", style=discord.ButtonStyle.secondary, row=0
    )
    async def next_button(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        self.current_index = (self.current_index + 1) % self.total_cards
        self._refresh_buttons_state()
        embed = await self.get_combined_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    async def get_combined_embed(self) -> discord.Embed:
        if not self._processed_cards_view_data:
            logger.error("get_combined_embed called with no card results.")
            return discord.Embed(
                title="錯誤",
                description="沒有卡片結果可顯示。",
                color=discord.Color.red(),
            )
        current_processed_info = self._processed_cards_view_data[self.current_index]
        combined_embed = self._shared_builder.build_combined_embed(
            card=current_processed_info.card,
            is_new_card=current_processed_info.is_new_card,
            is_wish=current_processed_info.is_wish,
            is_favorite=current_processed_info.is_favorite,
            star_level=current_processed_info.star_level,
            pool_type=current_processed_info.pool_type,
            owner_count=current_processed_info.owner_count,
            current_index=self.current_index,
            total_cards=self.total_cards,
        )
        return combined_embed

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        # 根據規範，移除 try-except，讓錯誤自然冒泡
        updated = False
        processed_cws_list = []
        for p_info in self._processed_cards_view_data:
            if p_info.card.card_id == card_id:
                p_info.is_favorite = is_favorite
                # Ensure the underlying CardWithStatus object is also updated
                if isinstance(p_info.original_result, CardWithStatus):
                    p_info.original_result.status.is_favorite = is_favorite
                updated = True
            if isinstance(p_info.original_result, CardWithStatus):
                processed_cws_list.append(p_info.original_result)

        if updated:
            # Re-create the builder with the potentially modified list of CardWithStatus objects
            self._shared_builder = MultiDrawEmbedBuilder(
                user=self.user,
                cards_results=processed_cws_list,
                balance=self.balance,
                nickname=self.user.display_name,
            )
            return True
        return False

    async def on_timeout(self):
        logger.debug("MultiDrawView 超時，用戶ID: %s", self.user_id)
        self.stop()
