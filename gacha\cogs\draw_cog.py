"""
Gacha系統抽卡功能的COG
包含 /w 指令，用於處理用戶的抽卡請求。
"""

import asyncio
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

if TYPE_CHECKING:
    from discord.ext.commands import AutoShardedBot, Bot

    BotType = Union[Bot, AutoShardedBot]
else:
    BotType = commands.Bot
from config.app_config import get_config
from gacha.exceptions import (
    ConfigurationError,
    IncompleteDrawResultError,
    InvalidPoolTypeError,
)
from gacha.models.models import Card, CardWithStatus
from gacha.services import (
    draw_notifier,  # 直接導入模組
    gacha_service,  # 新增
)
from gacha.views.embeds.gacha.draw_embed_builder import DrawEmbedBuilder

# no_spam 已移除，改用 CooldownMixin 統一管理冷卻
from gacha.views.gacha.unified_draw_view import DrawView, MultiDrawView
from gacha.views.utils import get_pool_type_choices
from utils.logger import logger


class DrawCog(commands.Cog):
    """處理抽卡相關指令的 Cog"""

    def __init__(self, bot: BotType):  # 移除 draw_notifier 參數
        self.bot = bot
        # draw_notifier 不再作為實例屬性，直接從模組導入

        # 啟動抽卡通知服務
        self.bot.loop.create_task(self.start_draw_notifier_service())

    async def start_draw_notifier_service(self):
        """異步啟動抽卡通知服務"""
        try:
            draw_notifier.initialize_notifier()
            logger.info("✅ 抽卡通知服務已由 DrawCog 啟動")
        except Exception as e:
            logger.error("❌ DrawCog 啟動抽卡通知服務失敗: %s", e, exc_info=True)

    async def cog_unload(self):
        """在 Cog 卸載時停止服務"""
        logger.info("DrawCog 正在卸載，準備停止抽卡通知服務...")
        try:
            await draw_notifier.close_notifier_session()
            logger.info("✅ 抽卡通知服務已成功停止")
        except Exception as e:
            logger.error("❌ DrawCog 卸載時停止抽卡通知服務失敗: %s", e, exc_info=True)

    @app_commands.command(
        name="w",
        description="抽取卡片 - 混合池(50油)，典藏池(140油)，泳裝池(130油)，情人節卡池(150油) - 可進行十連抽",
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.choices(
        pool_type=get_pool_type_choices(show_individual_pools_only=False)
    )
    async def draw_command(
        self,
        interaction: discord.Interaction,
        pool_type: Optional[str] = None,
        multi: bool = False,
    ):
        """抽卡命令處理

        參數:
            interaction: Discord交互對象
            pool_type: 卡池選擇 (可選)
            multi: 是否為十連抽
        """
        # 統一使用 defer 模式
        await interaction.response.defer(thinking=False)

        if pool_type:
            config_key = pool_type
            pool_configurations = get_config(
                "gacha_core_settings.pool_configurations", {}
            )
            if not isinstance(pool_configurations, dict):
                pool_configurations = {}
            pool_config_data = pool_configurations.get(config_key)
            if not pool_config_data:
                raise InvalidPoolTypeError(f"無效的卡池類型 '{config_key}'。")
            actual_pool_types = pool_config_data.pools
        else:
            config_key = "all"
            pool_configurations = get_config(
                "gacha_core_settings.pool_configurations", {}
            )
            if not isinstance(pool_configurations, dict):
                pool_configurations = {}
            default_pool_config = pool_configurations.get("all")
            if not default_pool_config:
                raise ConfigurationError(
                    "默認卡池 'all' 未在配置中找到，請聯繫管理員。"
                )
            actual_pool_types = default_pool_config.pools
        await self._execute_draw_operation(
            interaction, actual_pool_types, config_key, multi
        )

    async def _execute_draw_operation(
        self,
        interaction: discord.Interaction,
        pool_types: List[str],
        config_key: str,
        multi_draw: bool,
    ) -> None:
        """執行抽卡操作的統一邏輯"""
        user_id = interaction.user.id
        nickname = interaction.user.display_name

        # 測量抽卡操作時間

        if multi_draw:
            result = await gacha_service.draw_multiple(
                user_id, pool_types, 10, nickname, config_key, "multi"
            )
        else:
            result = await gacha_service.draw_cards(
                user_id, pool_types, 1, nickname, config_key, "single"
            )

        await self._process_draw_result(
            interaction, result, pool_types, config_key, multi_draw
        )

    async def _process_draw_result(
        self,
        interaction: discord.Interaction,
        result: Dict[str, Any],
        pool_types: List[str],
        config_key: str,
        multi_draw: bool,
    ):
        """處理抽卡結果的共用邏輯"""
        cards_with_status = result.get("cards_with_status", [])
        new_balance = result.get("new_balance", 0)
        is_multi_draw_from_result = result.get("is_multi_draw", multi_draw)

        if is_multi_draw_from_result:
            await self._process_multi_draw_result(
                interaction,
                result,
                cards_with_status,
                new_balance,
                pool_types,
                config_key,
            )
        elif cards_with_status:
            await self._process_single_draw_result(
                interaction,
                result,
                cards_with_status[0],
                new_balance,
                pool_types,
                config_key,
            )
        else:
            # 這是一個預期外的系統錯誤，應該拋出一個真正的異常讓全域處理器捕捉
            logger.error("單抽結果中 'cards_with_status' 為空。Result: %s", result)
            raise IncompleteDrawResultError(
                f"抽卡結果異常：gacha_service 回傳的結果中缺少卡片。 User: {interaction.user.id}"
            )

    async def _handle_draw_callback(
        self,
        pool_types: List[str],
        config_key: str,
        is_multi_draw: bool,
        interaction: discord.Interaction,
    ):
        """處理來自 DrawView 或 MultiDrawView 的繼續抽卡請求的回調

        注意：參數順序必須與 BaseDrawView._process_draw_callback 中的調用順序一致
        """
        await self._execute_draw_operation(
            interaction=interaction,
            pool_types=pool_types,
            config_key=config_key,
            multi_draw=is_multi_draw,
        )

    async def _process_single_draw_result(
        self,
        interaction: discord.Interaction,
        result: Dict[str, Any],
        card_with_status: CardWithStatus,
        new_balance: int,
        pool_types: List[str],
        config_key: str,
    ):
        """處理單抽結果的專用邏輯"""
        owner_counts = result.get("owner_counts", {})
        owner_count = owner_counts.get(card_with_status.card.card_id, 0)

        # 直接傳遞服務模組
        view = DrawView(
            bot=self.bot,
            user=interaction.user,  # type: ignore
            card=card_with_status,
            balance=new_balance,
            owner_count=owner_count,
            on_draw_callback=self._handle_draw_callback,
            pool_types=pool_types,
            pool_config_key=config_key,
        )
        embed = await view.get_current_page_embed(interaction=interaction)

        # 統一使用 followup.send（主命令已經 defer）
        # 如果發送失敗，錯誤將向上傳播到全局錯誤處理器
        await interaction.followup.send(embed=embed, view=view)

        self._process_card_notification(
            interaction.user,  # type: ignore
            card_with_status.card,
            card_with_status.status.is_new_card,
            card_with_status.status.is_wish,
            new_balance,
            owner_count,
        )

    async def _process_multi_draw_result(
        self,
        interaction: discord.Interaction,
        result: Dict[str, Any],
        cards_with_status: List[CardWithStatus],
        new_balance: int,
        pool_types: List[str],
        config_key: str,
    ):
        """處理十連抽結果的專用邏輯"""
        sorted_cards = sorted(
            cards_with_status, key=lambda cws: cws.card.rarity, reverse=True
        )
        owner_counts = result.get("owner_counts", {})

        # 直接傳遞服務模組
        view = MultiDrawView(
            bot=self.bot,
            user=interaction.user,  # type: ignore
            cards_results=sorted_cards,  # type: ignore
            owner_counts=owner_counts,
            balance=new_balance,
            on_multi_draw_callback=self._handle_draw_callback,
            pool_types=pool_types,
            pool_config_key=config_key,
        )
        combined_embed = await view.get_combined_embed()

        # 統一使用 followup.send（主命令已經 defer）
        # 如果發送失敗，錯誤將向上傳播到全局錯誤處理器
        await interaction.followup.send(embed=combined_embed, view=view)

        for card_with_status_item in sorted_cards:
            self._process_card_notification(
                interaction.user,  # type: ignore
                card_with_status_item.card,
                card_with_status_item.status.is_new_card,
                card_with_status_item.status.is_wish,
                new_balance,
                owner_counts.get(card_with_status_item.card.card_id, 0),
            )

    def _process_card_notification(
        self,
        user: discord.User,
        card: Card,
        is_new_card: bool,
        is_wish: bool,
        new_balance: int = 0,
        owner_count: int = 0,
    ):
        """處理卡片通知"""
        # 直接使用導入的 draw_notifier 模組
        should_send_notification = draw_notifier.should_notify(
            card, is_new_card, is_wish
        )
        if not should_send_notification:
            return

        # 構建通知
        notify_builder = DrawEmbedBuilder(
            user=user,
            card=card,
            balance=new_balance,
            is_new_card=is_new_card,
            is_wish=is_wish,
            owner_count=owner_count,
        )
        notify_embed = notify_builder.build_embed()

        # 異步發送通知
        async def send_notification():
            # 移除 try-except，讓錯誤自然冒泡，由全局處理器或asyncio日誌記錄
            await draw_notifier.notify_draw(
                user=user,
                embed=notify_embed,
                card=card,
                view=None,
                is_new_card=is_new_card,
            )

        asyncio.create_task(send_notification())


async def setup(bot: BotType):
    """將 Cog 添加到 Bot"""
    # 不再從 bot 獲取服務，直接實例化 Cog
    await bot.add_cog(DrawCog(bot))
    logger.info("DrawCog 已成功加載。")
