from typing import Any, List, Optional, Tuple

import asyncpg

from database.postgresql.async_manager import get_pool
from utils.logger import logger

DB_OPERATION_INTERNAL_BATCH_SIZE = 1000


class MarketStatsUpdateError(Exception):
    """Base exception for market stats update errors"""

    pass


class InvalidColumnNameError(MarketStatsUpdateError):
    """Exception raised when an invalid column name is provided"""

    def __init__(self, column_name: str):
        self.column_name = column_name
        super().__init__(f"Invalid column name: {column_name}")


class BatchUpdateError(MarketStatsUpdateError):
    """Exception raised when a batch update operation fails"""

    def __init__(
        self, operation_description: str, batch_number: int, original_error: Exception
    ):
        self.operation_description = operation_description
        self.batch_number = batch_number
        self.original_error = original_error
        super().__init__(
            f"Batch update for {operation_description} failed (batch {batch_number}): {original_error}"
        )


class InvalidUpdateFormatError(MarketStatsUpdateError):
    """Exception raised when an invalid update format is provided"""

    def __init__(self, update_item: Any):
        self.update_item = update_item
        super().__init__(
            f"Invalid update format: {update_item}. Expected tuple (card_id, change)."
        )


async def _bulk_update_stats_column(
    conn: asyncpg.Connection,
    updates: List[Tuple[int, int]],
    column_name: str,
    operation_description: str,
):
    """
    通用的批量更新 gacha_card_market_stats 表中指定列的輔助函數。
    使用 INSERT ... ON CONFLICT DO UPDATE ... SET column = GREATEST(0, column + EXCLUDED.column)。

    【死鎖防護】對 card_id 進行排序以確保所有事務以相同順序鎖定行。

    :param conn: 資料庫連接物件。
    :param updates: 元組列表：[(card_id, change), ...]
    :param column_name: 要更新的列名 (例如 "total_owned_quantity", "favorite_count")。
    :param operation_description: 用於日誌記錄的操作描述 (例如 "total_owned_quantity")。
    """
    if not isinstance(updates, list):
        logger.error(
            "DBUpdater: _bulk_update_stats_column 接收到的 'updates' 参数类型不正确，期望是 list，实际是 %s",
            type(updates),
        )
        raise TypeError(f"Expected 'updates' to be a list, got {type(updates)}")

    if not updates:
        return
    if not column_name.isidentifier():
        logger.error("DBUpdater: 無效的列名 '%s' 用於批量更新。", column_name)
        raise InvalidColumnNameError(column_name)

    # 【解決死鎖的關鍵】按 card_id 排序以確保所有事務以相同順序鎖定行
    sorted_updates = sorted(updates, key=lambda x: x[0])

    for i in range(0, len(sorted_updates), DB_OPERATION_INTERNAL_BATCH_SIZE):
        batch_updates = sorted_updates[i : i + DB_OPERATION_INTERNAL_BATCH_SIZE]
        sql = f"""
        INSERT INTO gacha_card_market_stats (card_id, {column_name})
        SELECT unnest_card_ids, unnest_deltas
        FROM unnest($1::integer[], $2::integer[]) AS t(unnest_card_ids, unnest_deltas)
        ON CONFLICT (card_id) DO UPDATE
        SET {column_name} = GREATEST(0, gacha_card_market_stats.{column_name} + EXCLUDED.{column_name});
        """

        # Extract card_ids and deltas from tuple updates
        card_ids = [update[0] for update in batch_updates]
        deltas = [update[1] for update in batch_updates]

        try:
            await conn.execute(sql, card_ids, deltas)
        except Exception as e:
            logger.error(
                "DBUpdater: 批量更新 %s 失敗 (批次 %s): %s",
                operation_description,
                i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1,
                e,
                exc_info=True,
            )
            raise BatchUpdateError(
                operation_description, i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1, e
            ) from e


async def bulk_update_total_owned(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    批量更新卡片的總擁有數量 (total_owned_quantity)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "total_owned_quantity", "total_owned_quantity"
        )
    else:
        pool = get_pool()
        if pool is None:
            raise MarketStatsUpdateError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "total_owned_quantity", "total_owned_quantity"
            )


async def bulk_update_favorite_counts_direct(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    直接批量更新卡片的最愛數量 (favorite_count)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "favorite_count", "favorite_count"
        )
    else:
        pool = get_pool()
        if pool is None:
            raise MarketStatsUpdateError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "favorite_count", "favorite_count"
            )


async def _internal_bulk_update_unique_owners(
    conn: asyncpg.Connection, owner_changes: List[Tuple[int, int]]
):
    """
    批量更新卡片的獨立擁有者數量 (unique_owner_count) 的內部邏輯。

    【死鎖防護】對 card_id 進行排序以確保所有事務以相同順序鎖定行。
    """
    if not owner_changes:
        return
    increment_card_ids: List[int] = []
    decrement_card_ids: List[int] = []

    for update in owner_changes:
        try:
            if not isinstance(update, (list, tuple)) or len(update) != 2:
                raise InvalidUpdateFormatError(update)

            card_id, delta = update

            if delta == 1:
                increment_card_ids.append(card_id)
            elif delta == -1:
                decrement_card_ids.append(card_id)
            else:
                logger.warning("未知的 change 值: %s for card_id: %s", delta, card_id)
        except (ValueError, TypeError) as e:
            logger.error("無效的更新格式: %s, 錯誤: %s", update, e)
            raise InvalidUpdateFormatError(update) from e

    # 【解決死鎖的關鍵】對 card_id 進行排序
    increment_card_ids.sort()
    decrement_card_ids.sort()

    if increment_card_ids:
        for i in range(0, len(increment_card_ids), DB_OPERATION_INTERNAL_BATCH_SIZE):
            batch_ids = increment_card_ids[i : i + DB_OPERATION_INTERNAL_BATCH_SIZE]
            sql_increment = """
            INSERT INTO gacha_card_market_stats (card_id, unique_owner_count)
            SELECT card_id_val, 1
            FROM unnest($1::integer[]) AS t(card_id_val)
            ON CONFLICT (card_id) DO UPDATE
            SET unique_owner_count = gacha_card_market_stats.unique_owner_count + 1;
            """
            try:
                await conn.execute(sql_increment, batch_ids)
            except Exception as e:
                logger.error(
                    "DBUpdater: 批量增加 unique_owner_count 失敗 (批次 %s): %s",
                    i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1,
                    e,
                    exc_info=True,
                )
                raise BatchUpdateError(
                    "unique_owner_count_increment",
                    i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1,
                    e,
                ) from e

    if decrement_card_ids:
        for i in range(0, len(decrement_card_ids), DB_OPERATION_INTERNAL_BATCH_SIZE):
            batch_ids = decrement_card_ids[i : i + DB_OPERATION_INTERNAL_BATCH_SIZE]
            sql_decrement = """
            UPDATE gacha_card_market_stats
            SET unique_owner_count = GREATEST(0, unique_owner_count - 1)
            WHERE card_id = ANY($1::integer[]);
            """
            try:
                await conn.execute(sql_decrement, batch_ids)
            except Exception as e:
                logger.error(
                    "DBUpdater: 批量減少 unique_owner_count 失敗 (批次 %s): %s",
                    i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1,
                    e,
                    exc_info=True,
                )
                raise BatchUpdateError(
                    "unique_owner_count_decrement",
                    i // DB_OPERATION_INTERNAL_BATCH_SIZE + 1,
                    e,
                ) from e


async def bulk_update_unique_owners(
    owner_changes: List[Tuple[int, int]],
    connection: Optional[asyncpg.Connection] = None,
):
    """
    批量更新卡片的獨立擁有者數量 (unique_owner_count)。

    :param owner_changes: 元組列表：[(card_id, change), ...]
                          change 為 1 表示增加，-1 表示減少。
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _internal_bulk_update_unique_owners(connection, owner_changes)
    else:
        pool = get_pool()
        if pool is None:
            raise MarketStatsUpdateError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            await _internal_bulk_update_unique_owners(conn, owner_changes)


async def bulk_update_wishlist_counts_direct(
    updates: List[Tuple[int, int]], connection: Optional[asyncpg.Connection] = None
):
    """
    直接批量更新卡片的許願數量 (wishlist_count)。

    :param updates: 元組列表：[(card_id, change), ...]
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _bulk_update_stats_column(
            connection, updates, "wishlist_count", "wishlist_count"
        )
    else:
        pool = get_pool()
        if pool is None:
            raise MarketStatsUpdateError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            await _bulk_update_stats_column(
                conn, updates, "wishlist_count", "wishlist_count"
            )


async def _internal_bulk_update_combined_stats(
    conn: asyncpg.Connection, updates: List[Tuple[int, int, int, int]]
):
    """
    內部輔助函數，用於在單一查詢中批量更新多個統計列。
    【已修復】使用自定義複合類型 market_stats_update_type 來避免 'anonymous record' 錯誤，
    並移除了多餘的列定義列表。

    :param updates: 一個元組列表，每個元組的結構必須符合在DB中定義的
                     `market_stats_update_type` 類型:
                     (card_id, total_owned_quantity_delta, favorite_count_delta, wishlist_count_delta)
    """
    if not updates:
        return

    # 【最終修正】
    # 當使用已命名的複合類型時，`AS d(...)` 部分是多餘的，必須移除。
    # PostgreSQL 會自動從 `market_stats_update_type` 的定義中獲取列名。
    sql = """
        INSERT INTO gacha_card_market_stats (card_id, total_owned_quantity, favorite_count, wishlist_count)
        SELECT
            d.card_id,
            d.total_owned_quantity_delta,
            d.favorite_count_delta,
            d.wishlist_count_delta
        FROM
            -- 這裡就是唯一的改動：移除了 `AS d(...)`
            unnest($1::market_stats_update_type[]) AS d
        ON CONFLICT (card_id) DO UPDATE SET
            total_owned_quantity = GREATEST(0, gacha_card_market_stats.total_owned_quantity + EXCLUDED.total_owned_quantity),
            favorite_count = GREATEST(0, gacha_card_market_stats.favorite_count + EXCLUDED.favorite_count),
            wishlist_count = GREATEST(0, gacha_card_market_stats.wishlist_count + EXCLUDED.wishlist_count);
    """

    try:
        await conn.execute(sql, updates)
        logger.info("DBUpdater: 成功批量合併更新了 %s 條市場統計記錄。", len(updates))
    except Exception as e:
        logger.error("DBUpdater: 批量合併更新統計數據失敗: %s", e, exc_info=True)
        raise BatchUpdateError("combined_stats", 1, e) from e


# 替換 `bulk_update_combined_stats` 函數
# 注意函數簽名變化：updates 參數類型從 Dict 變為 List[Tuple]
async def bulk_update_combined_stats(
    updates: List[Tuple[int, int, int, int]],
    connection: Optional[asyncpg.Connection] = None,
):
    """
    公開接口，用於批量更新多個簡單的統計列。

    :param updates: 一個元組列表，其結構必須符合在DB中定義的 `market_stats_update_type`。
    :param connection: (可選) 資料庫連接物件。如果未提供，將從連接池中獲取。
    """
    if connection:
        await _internal_bulk_update_combined_stats(connection, updates)
    else:
        pool = get_pool()
        if pool is None:
            raise MarketStatsUpdateError("資料庫連線池未初始化")
        async with pool.acquire() as conn:
            await _internal_bulk_update_combined_stats(conn, updates)
