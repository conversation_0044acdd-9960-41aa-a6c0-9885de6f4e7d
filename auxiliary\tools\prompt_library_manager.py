#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示詞卡片庫管理工具

用於管理和查看多套提示詞卡片庫的工具。
"""

import sys
from pathlib import Path

# 添加專案根目錄到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from services.story.prompt_libraries import (
        get_available_libraries,
        get_prompt_library,
    )
    from services.story.prompts import get_prompt_library_info
except ImportError as e:
    print(f"無法導入卡片庫模組: {e}")
    print("請確保在正確的專案目錄中運行此腳本")
    sys.exit(1)


def print_separator(char="=", length=60):
    """打印分隔線"""
    print(char * length)


def list_available_libraries():
    """列出所有可用的卡片庫"""
    print_separator()
    print("📚 可用的提示詞卡片庫")
    print_separator()
    
    try:
        libraries = get_available_libraries()
        if not libraries:
            print("❌ 未找到任何卡片庫")
            return
        
        # 獲取詳細信息
        info = get_prompt_library_info()
        
        for i, lib_name in enumerate(libraries, 1):
            lib_info = info.get(lib_name, {})
            status = "✅" if lib_info.get('available', False) else "❌"
            count = lib_info.get('count', 0)
            description = lib_info.get('description', '無描述')
            
            print(f"{i:2d}. {status} {lib_name}")
            print(f"     卡片數量: {count}")
            print(f"     描述: {description}")
            print()
            
    except Exception as e:
        print(f"❌ 列出卡片庫時發生錯誤: {e}")


def show_library_details(library_name: str):
    """顯示指定卡片庫的詳細信息"""
    print_separator()
    print(f"📖 卡片庫詳情: {library_name}")
    print_separator()
    
    try:
        library = get_prompt_library(library_name)
        
        print(f"卡片總數: {len(library)}")
        print(f"啟用卡片: {sum(1 for card in library if card.get('enabled', False))}")
        print(f"禁用卡片: {sum(1 for card in library if not card.get('enabled', False))}")
        print()
        
        # 按角色分組統計
        role_counts = {}
        for card in library:
            role = card.get('role', 'unknown')
            role_counts[role] = role_counts.get(role, 0) + 1
        
        print("角色分佈:")
        for role, count in sorted(role_counts.items()):
            print(f"  - {role}: {count} 張卡片")
        print()
        
        # 列出所有卡片
        print("卡片列表:")
        for i, card in enumerate(library, 1):
            status = "✅" if card.get('enabled', False) else "❌"
            card_id = card.get('id', 'unknown')
            card_name = card.get('name', 'Unknown')
            role = card.get('role', 'unknown')
            
            print(f"{i:3d}. {status} [{role:8s}] {card_id}")
            print(f"       名稱: {card_name}")
            
            # 顯示內容摘要（前50字符）
            content = card.get('content', '')
            if content:
                content_preview = content.replace('\n', ' ').strip()[:50]
                if len(content) > 50:
                    content_preview += "..."
                print(f"       內容: {content_preview}")
            print()
            
    except Exception as e:
        print(f"❌ 顯示卡片庫詳情時發生錯誤: {e}")


def show_card_details(library_name: str, card_id: str):
    """顯示指定卡片的詳細內容"""
    print_separator()
    print(f"🃏 卡片詳情: {card_id} (來自 {library_name})")
    print_separator()
    
    try:
        library = get_prompt_library(library_name)
        
        # 查找指定卡片
        target_card = None
        for card in library:
            if card.get('id') == card_id:
                target_card = card
                break
        
        if not target_card:
            print(f"❌ 在卡片庫 '{library_name}' 中未找到 ID 為 '{card_id}' 的卡片")
            return
        
        # 顯示卡片信息
        print(f"ID: {target_card.get('id', 'unknown')}")
        print(f"名稱: {target_card.get('name', 'Unknown')}")
        print(f"角色: {target_card.get('role', 'unknown')}")
        print(f"啟用狀態: {'是' if target_card.get('enabled', False) else '否'}")
        
        if 'condition' in target_card:
            print(f"觸發條件: {target_card['condition']}")
        
        print("\n內容:")
        print("-" * 40)
        content = target_card.get('content', '')
        if content:
            print(content)
        else:
            print("(無內容)")
        print("-" * 40)
        
    except Exception as e:
        print(f"❌ 顯示卡片詳情時發生錯誤: {e}")


def interactive_mode():
    """互動模式"""
    print_separator("*", 80)
    print("🎮 提示詞卡片庫管理工具 - 互動模式")
    print_separator("*", 80)
    
    while True:
        print("\n可用命令:")
        print("1. list    - 列出所有可用的卡片庫")
        print("2. show    - 顯示指定卡片庫的詳情")
        print("3. card    - 顯示指定卡片的詳細內容")
        print("4. help    - 顯示幫助信息")
        print("5. exit    - 退出程序")
        
        try:
            command = input("\n請輸入命令: ").strip().lower()
            
            if command == "exit" or command == "quit":
                print("👋 再見！")
                break
            elif command == "list" or command == "1":
                list_available_libraries()
            elif command == "show" or command == "2":
                lib_name = input("請輸入卡片庫名稱: ").strip()
                if lib_name:
                    show_library_details(lib_name)
                else:
                    print("❌ 請輸入有效的卡片庫名稱")
            elif command == "card" or command == "3":
                lib_name = input("請輸入卡片庫名稱: ").strip()
                card_id = input("請輸入卡片 ID: ").strip()
                if lib_name and card_id:
                    show_card_details(lib_name, card_id)
                else:
                    print("❌ 請輸入有效的卡片庫名稱和卡片 ID")
            elif command == "help" or command == "4":
                print("\n📋 使用說明:")
                print("- list: 列出所有可用的提示詞卡片庫及其基本信息")
                print("- show <library_name>: 顯示指定卡片庫的詳細信息和所有卡片")
                print("- card <library_name> <card_id>: 顯示指定卡片的完整內容")
                print("- exit: 退出程序")
            else:
                print("❌ 未知命令，請輸入 'help' 查看可用命令")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用戶中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 執行命令時發生錯誤: {e}")


def main():
    """主函數"""
    if len(sys.argv) == 1:
        # 沒有參數，進入互動模式
        interactive_mode()
    elif len(sys.argv) == 2:
        command = sys.argv[1].lower()
        if command == "list":
            list_available_libraries()
        elif command == "help":
            print("使用方法:")
            print(f"  python {sys.argv[0]}           # 進入互動模式")
            print(f"  python {sys.argv[0]} list      # 列出所有卡片庫")
            print(f"  python {sys.argv[0]} show <library_name>     # 顯示卡片庫詳情")
            print(f"  python {sys.argv[0]} card <library_name> <card_id>  # 顯示卡片詳情")
        else:
            print(f"❌ 未知命令: {command}")
            print("使用 'help' 查看可用命令")
    elif len(sys.argv) == 3:
        command, library_name = sys.argv[1].lower(), sys.argv[2]
        if command == "show":
            show_library_details(library_name)
        else:
            print(f"❌ 命令 '{command}' 需要不同的參數數量")
    elif len(sys.argv) == 4:
        command, library_name, card_id = sys.argv[1].lower(), sys.argv[2], sys.argv[3]
        if command == "card":
            show_card_details(library_name, card_id)
        else:
            print(f"❌ 命令 '{command}' 需要不同的參數數量")
    else:
        print("❌ 參數過多，使用 'help' 查看正確用法")


if __name__ == "__main__":
    main()