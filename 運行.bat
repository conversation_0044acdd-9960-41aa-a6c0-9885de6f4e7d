@echo off
chcp 65001 > nul
echo 設定環境變數...
set GACHA_DB_NAME=gacha_database
set DEV_MODE=false
echo LOG_LEVEL=ERROR
(
echo DISCORD_TOKEN=MTIyMTIzMDczNDYwMjE0MTcyNw.GLHcmU.PoVxxvjudUO-2TAihNpE_1jHwpzESDubNqaQPA
echo GACHA_DB_NAME=gacha_database
echo LOG_LEVEL=WARNING
echo PG_HOST=127.0.0.1
echo PG_PORT=5432
echo PG_USER=postgres
echo PG_PASSWORD=26015792
echo DEV_MODE=true
echo REDIS_HOST=localhost
echo REDIS_PORT=6379
echo REDIS_DB=0
echo REDIS_PASSWORD=
echo REDIS_ENABLED=True
echo RPG=FALSE
echo PIONEER_ENABLED=false
echo WEBHOOK_NOTIFICATIONS_ENABLED=true
echo SHARD_COUNT=2
) > .env
python bot.py