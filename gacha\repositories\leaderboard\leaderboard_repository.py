import asyncio
from typing import Any, Dict, List, Optional, Tuple

import asyncpg

from gacha.core.game_registry import GameRegistry
from gacha.exceptions import DatabaseOperationError
from gacha.repositories._base_repo import (
    batch_upsert,
    fetch_all,
    fetch_one,
    fetch_value,
)
from utils.logger import logger


def _should_apply_pool_filter(pool_type: Optional[str]) -> bool:
    """檢查是否應該應用卡池篩選"""
    return bool(pool_type and pool_type not in ("all", "all_pools"))


# 表名常量
USER_TABLE = "gacha_users"
COLLECTION_TABLE = "gacha_user_collections"
MASTER_CARD_TABLE = "gacha_master_cards"
LEADERBOARD_STATS_TABLE = "gacha_leaderboard_stats"
PLAYER_PORTFOLIOS_TABLE = "player_portfolios"
VIRTUAL_ASSETS_TABLE = "virtual_assets"
MARKET_TRANSACTIONS_TABLE = "market_transactions"

# 全局緩存變量
_pool_types: Optional[List[str]] = None
_rarities: Optional[List[int]] = None
_total_cards_cache: Dict[str, int] = {}


def _get_leaderboard_config_structure():
    """獲取排行榜配置結構"""
    base_config = {
        "rarity": _get_rarity_config(),
        "completion": _get_completion_config(),
        "draws": _get_draws_config(),
        "oil": _get_oil_config(),
        "collection_unique": _get_collection_unique_config(),
        "luck_index": _get_luck_index_config(),
        "avg_draws": _get_avg_draws_config(),
        "longest_drought": _get_longest_drought_config(),
        "portfolio_value": _get_portfolio_value_config(),
        "stock_holding": _get_stock_holding_config(),
        "trade_volume": _get_trade_volume_config(),
        "trade_count": _get_trade_count_config(),
        "stock_profit_loss": _get_stock_profit_loss_config(),
        "profile_likes": _get_profile_likes_config(),
    }

    # 動態添加遊戲統計配置
    for game_type in GameRegistry.get_game_types():
        game_config = GameRegistry.get_game(game_type)
        if game_config:
            base_config[game_type] = _get_dynamic_game_stats_config(game_config)

    return base_config


def get_leaderboard_config_structure():
    """動態獲取排行榜配置結構"""
    return _get_leaderboard_config_structure()


def _get_rarity_config():
    """稀有度收集排行榜配置"""
    return {
        "title": "稀有度收集排行榜",
        "with_template": """
         WITH RarityCount AS (
             SELECT
                 uc.user_id,
                 SUM(CASE WHEN mc.rarity = 7 THEN uc.quantity ELSE 0 END) AS rarity_7_count,
                 SUM(CASE WHEN mc.rarity = 6 THEN uc.quantity ELSE 0 END) AS rarity_6_count,
                 SUM(CASE WHEN mc.rarity = 5 THEN uc.quantity ELSE 0 END) AS rarity_5_count,
                 SUM(CASE WHEN mc.rarity = 4 THEN uc.quantity ELSE 0 END) AS rarity_4_count,
                 SUM(CASE WHEN mc.rarity = 3 THEN uc.quantity ELSE 0 END) AS rarity_3_count,
                 SUM(CASE WHEN mc.rarity = 2 THEN uc.quantity ELSE 0 END) AS rarity_2_count,
                 SUM(CASE WHEN mc.rarity = 1 THEN uc.quantity ELSE 0 END) AS rarity_1_count
             FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id
             {pool_type_clause} GROUP BY uc.user_id
         )
         """,
        "select_template": """
             gu.user_id, gu.oil_balance, gu.total_draws,
             COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
             COALESCE(rc.rarity_7_count, 0) AS rarity_7_count, COALESCE(rc.rarity_6_count, 0) AS rarity_6_count,
             COALESCE(rc.rarity_5_count, 0) AS rarity_5_count, COALESCE(rc.rarity_4_count, 0) AS rarity_4_count,
             COALESCE(rc.rarity_3_count, 0) AS rarity_3_count, COALESCE(rc.rarity_2_count, 0) AS rarity_2_count,
             COALESCE(rc.rarity_1_count, 0) AS rarity_1_count
         """,
        "from_template": "{user_table} gu INNER JOIN RarityCount rc ON gu.user_id = rc.user_id",
        "order_by_template": """
             COALESCE(rc.rarity_7_count, 0) DESC, COALESCE(rc.rarity_6_count, 0) DESC,
             COALESCE(rc.rarity_5_count, 0) DESC, COALESCE(rc.rarity_4_count, 0) DESC,
             COALESCE(rc.rarity_3_count, 0) DESC, COALESCE(rc.rarity_2_count, 0) DESC,
             COALESCE(rc.rarity_1_count, 0) DESC, gu.user_id ASC
         """,
    }


def _get_completion_config():
    """圖鑑完成率排行榜配置"""
    return {
        "title": "圖鑑完成率排行榜",
        "with_template": """
            WITH UserCardCollection AS (
                SELECT uc.user_id, COUNT(uc.card_id) as collected_cards
                FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id
                {pool_type_clause} GROUP BY uc.user_id
            ), TotalCards AS (
                SELECT COUNT(*) as total_cards
                FROM {master_card_table} mc
                {pool_type_clause}
            )
        """,
        "select_template": """
            gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
            COALESCE(ucc.collected_cards, 0) as collected_cards,
            tc.total_cards::integer as total_cards,
            ROUND(COALESCE(ucc.collected_cards, 0) * 100.0 / tc.total_cards, 1) as completion_percentage
        """,
        "from_template": "{user_table} gu INNER JOIN UserCardCollection ucc ON gu.user_id = ucc.user_id CROSS JOIN TotalCards tc",
        "order_by_template": """
            ROUND(COALESCE(ucc.collected_cards, 0) * 100.0 / tc.total_cards, 1) DESC,
            COALESCE(ucc.collected_cards, 0) DESC, gu.user_id ASC
        """,
    }


def _get_draws_config():
    """抽卡次數排行榜配置"""
    return {
        "title": "抽卡次數排行榜",
        "with_template": """
            WITH UserDrawStats AS (
                SELECT
                    dh.user_id,
                    COUNT(*) as pool_draws
                FROM gacha_draw_history dh
                {draw_history_pool_clause}
                GROUP BY dh.user_id
            )
        """,
        "select_template": """
            gu.user_id,
            COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
            gu.oil_balance,
            COALESCE(uds.pool_draws, 0) AS pool_draws,
            gu.total_draws
        """,
        "from_template": "{user_table} gu INNER JOIN UserDrawStats uds ON gu.user_id = uds.user_id",
        "order_by_template": "COALESCE(uds.pool_draws, 0) DESC, gu.user_id ASC",
    }


def _get_oil_config():
    """油幣餘額排行榜配置"""
    return {
        "title": "油幣餘額排行榜",
        "with_template": "",
        "select_template": "user_id, COALESCE(nickname, CAST(user_id AS VARCHAR)) AS user_name, oil_balance, total_draws",
        "from_template": "{user_table}",
        "order_by_template": "oil_balance DESC, user_id ASC",
    }


def _get_collection_unique_config():
    """稀有度收集排行榜配置（優化版 - 保持原有顯示格式）"""
    return {
        "title": "稀有度收集排行榜",
        "with_template": """
         WITH UserRarityStats AS (
             SELECT
                 uc.user_id,
                 COUNT(DISTINCT uc.card_id) AS total_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 7 THEN uc.card_id END) AS rarity_7_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 6 THEN uc.card_id END) AS rarity_6_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 5 THEN uc.card_id END) AS rarity_5_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 4 THEN uc.card_id END) AS rarity_4_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 3 THEN uc.card_id END) AS rarity_3_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 2 THEN uc.card_id END) AS rarity_2_unique_cards,
                 COUNT(DISTINCT CASE WHEN mc.rarity = 1 THEN uc.card_id END) AS rarity_1_unique_cards
             FROM {collection_table} uc
             JOIN {master_card_table} mc ON uc.card_id = mc.card_id
             {pool_type_clause}
             GROUP BY uc.user_id
         ), RarityTotals AS (
             SELECT
                 rarity,
                 COUNT(DISTINCT card_id) AS total_cards
             FROM {master_card_table} mc
             {pool_type_clause}
             GROUP BY rarity
         )
         """,
        "select_template": None,  # 將使用 _generate_optimized_collection_fields
        "from_template": "{user_table} gu INNER JOIN UserRarityStats urs ON gu.user_id = urs.user_id",
        "order_by_template": """
             COALESCE(urs.rarity_7_unique_cards, 0) DESC,
             COALESCE(urs.rarity_6_unique_cards, 0) DESC,
             COALESCE(urs.rarity_5_unique_cards, 0) DESC,
             COALESCE(urs.rarity_4_unique_cards, 0) DESC,
             COALESCE(urs.rarity_3_unique_cards, 0) DESC,
             COALESCE(urs.rarity_2_unique_cards, 0) DESC,
             COALESCE(urs.rarity_1_unique_cards, 0) DESC,
             COALESCE(urs.total_unique_cards, 0) DESC,
             gu.user_id ASC
         """,
    }


LUCK_STATS_CTE = """
    WITH LuckStats AS (
        {luck_stats_cte_body}
    )
"""

LUCK_STATS_SELECT = """
   gu.user_id,
   COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
   ls.draws_in_pool,
   ls.top_tier_in_pool,
   ls.longest_drought,
   CASE
       WHEN ls.draws_in_pool > 0 AND ls.expected_rate > 0
       THEN (ls.top_tier_in_pool::numeric / ls.draws_in_pool) / ls.expected_rate
       ELSE 1.0
   END AS luck_index,
   CASE
       WHEN ls.top_tier_in_pool > 0
       THEN ls.draws_in_pool::numeric / ls.top_tier_in_pool
       ELSE 0
   END AS avg_draws_per_top_tier
"""

LUCK_STATS_FROM = """
   {user_table} gu
   JOIN LuckStats ls ON gu.user_id = ls.user_id
"""


def _get_luck_index_config():
    """運氣指數排行榜配置"""
    return {
        "title": "🍀 運氣指數排行榜",
        "with_template": LUCK_STATS_CTE,
        "select_template": LUCK_STATS_SELECT,
        "from_template": LUCK_STATS_FROM,
        "order_by_template": "luck_index DESC, gu.user_id ASC",
    }


def _get_avg_draws_config():
    """平均出貨抽數排行榜配置"""
    return {
        "title": "🎯 平均出貨抽數排行榜",
        "with_template": LUCK_STATS_CTE,
        "select_template": LUCK_STATS_SELECT,
        "from_template": LUCK_STATS_FROM,
        "order_by_template": "avg_draws_per_top_tier ASC, gu.user_id ASC",
    }


def _get_longest_drought_config():
    """最長乾旱排行榜配置"""
    return {
        "title": "🏜️ 最長乾旱排行榜",
        "with_template": LUCK_STATS_CTE,
        "select_template": LUCK_STATS_SELECT,
        "from_template": LUCK_STATS_FROM,
        "order_by_template": "longest_drought DESC, gu.user_id ASC",
    }


def _get_portfolio_value_config():
    """總資產價值排行榜配置"""
    return {
        "title": "總資產價值排行",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           SUM(pp.quantity * va.current_price) AS total_portfolio_value
       """,
        "from_template": """
           {user_table} gu
           JOIN {player_portfolios_table} pp ON gu.user_id = pp.user_id
           JOIN {virtual_assets_table} va ON pp.asset_id = va.asset_id
       """,
        "group_by_template": "gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))",
        "order_by_template": "SUM(pp.quantity * va.current_price) DESC, gu.user_id ASC",
    }


def _get_stock_holding_config():
    """特定股票持有排行榜配置"""
    return {
        "title": "特定股票持有排行",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           pp.quantity AS stock_quantity,
           va.asset_name
       """,
        "from_template": """
           {user_table} gu
           JOIN {player_portfolios_table} pp ON gu.user_id = pp.user_id
           JOIN {virtual_assets_table} va ON pp.asset_id = va.asset_id
       """,
        "where_template": "va.asset_symbol = $1",
        "order_by_template": "stock_quantity DESC, gu.user_id ASC",
    }


def _get_trade_volume_config():
    """股票交易總額排行榜配置"""
    return {
        "title": "股票交易總額排行",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           SUM(mt.total_amount) AS total_trade_volume
       """,
        "from_template": """
           {user_table} gu
           JOIN {market_transactions_table} mt ON gu.user_id = mt.user_id
       """,
        "group_by_template": "gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))",
        "order_by_template": "SUM(mt.total_amount) DESC, gu.user_id ASC",
    }


def _get_trade_count_config():
    """股票交易次數排行榜配置"""
    return {
        "title": "股票交易次數排行",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           COUNT(mt.id) AS total_trade_count
       """,
        "from_template": """
           {user_table} gu
           JOIN {market_transactions_table} mt ON gu.user_id = mt.user_id
       """,
        "group_by_template": "gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))",
        "order_by_template": "COUNT(mt.id) DESC, gu.user_id ASC",
    }


def _get_stock_profit_loss_config():
    """股票已實現盈虧排行榜配置"""
    return {
        "title": "股票總盈虧排行",
        "with_template": """
            WITH realized_pnl AS (
                -- 計算已實現盈虧（賣出和回補交易）
                SELECT
                    mt.user_id,
                    SUM(CASE
                        WHEN mt.transaction_type = 'SELL' AND mt.context IS NOT NULL AND (mt.context->>'avg_cost')::DECIMAL IS NOT NULL
                        THEN (mt.price_per_unit - (mt.context->>'avg_cost')::DECIMAL) * mt.quantity
                        WHEN mt.transaction_type IN ('COVER', 'FORCED_COVER') AND mt.context IS NOT NULL AND (mt.context->>'avg_cost')::DECIMAL IS NOT NULL
                        THEN ((mt.context->>'avg_cost')::DECIMAL - mt.price_per_unit) * mt.quantity
                        ELSE 0
                    END) AS realized_profit_loss
                FROM {market_transactions_table} mt
                WHERE mt.transaction_type IN ('SELL', 'COVER', 'FORCED_COVER')
                GROUP BY mt.user_id
            )
        """,
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           COALESCE(rp.realized_profit_loss, 0) AS total_profit_loss
       """,
        "from_template": """
           {user_table} gu
           INNER JOIN realized_pnl rp ON gu.user_id = rp.user_id
       """,
        "order_by_template": "COALESCE(rp.realized_profit_loss, 0) DESC, gu.user_id ASC",
    }


def _get_profile_likes_config():
    """個人檔案讚數排行榜配置"""
    return {
        "title": "個人檔案讚數排行榜",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           COALESCE(up.like_count, 0) AS like_count
       """,
        "from_template": """
           {user_table} gu
           LEFT JOIN user_profiles up ON gu.user_id = up.user_id
       """,
        "order_by_template": "COALESCE(up.like_count, 0) DESC, gu.user_id ASC",
    }


def _get_dynamic_game_stats_config(game_config):
    """動態生成遊戲統計排行榜配置"""
    return {
        "title": f"{game_config.emoji} {game_config.name}排行榜",
        "with_template": "",
        "select_template": """
           gu.user_id,
           COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,
           ugs.game_type,
           ugs.total_games,
           ugs.total_wins,
           ugs.total_losses,
           ugs.total_pushes,
           ugs.total_profit_loss,
           ugs.max_win,
           ugs.max_loss,
           CASE WHEN ugs.total_games > 0 THEN ROUND((ugs.total_wins::numeric / ugs.total_games * 100), 2) ELSE 0 END AS win_rate,
           ugs.game_specific_stats,
           ugs.last_played_at
       """,
        "from_template": """
           {user_table} gu
           INNER JOIN user_game_stats ugs ON gu.user_id = ugs.user_id AND ugs.game_type = $1
       """,
        "where_template": "ugs.total_games > 0",
        "order_by_template": "ugs.total_profit_loss DESC NULLS LAST, gu.user_id ASC",
    }


async def _ensure_metadata_loaded():
    """非同步加載或刷新元數據 (卡池, 稀有度)"""
    global _pool_types, _rarities
    if _pool_types is None or _rarities is None:
        try:
            pool_types_query = f"SELECT DISTINCT pool_type FROM {MASTER_CARD_TABLE} WHERE pool_type IS NOT NULL ORDER BY pool_type"
            rarities_query = f"SELECT DISTINCT rarity FROM {MASTER_CARD_TABLE} WHERE rarity IS NOT NULL ORDER BY rarity ASC"

            tasks = [fetch_all(pool_types_query), fetch_all(rarities_query)]
            pool_types_res, rarities_res = await asyncio.gather(*tasks)

            _pool_types = (
                [row["pool_type"] for row in pool_types_res] if pool_types_res else []
            )
            _rarities = (
                [int(row["rarity"]) for row in rarities_res] if rarities_res else []
            )
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"載入排行榜元數據失敗: {e}") from e


async def get_total_cards(pool_type: Optional[str] = None) -> int:
    """(Async) 獲取活躍的總卡片數量，可依卡池篩選。"""
    global _total_cards_cache
    cache_key = pool_type or "all"
    if cache_key in _total_cards_cache:
        return _total_cards_cache[cache_key]

    try:
        query = f"SELECT COUNT(*)::integer as count FROM {MASTER_CARD_TABLE}"
        params = []
        if pool_type and pool_type != "all":
            query += " AND pool_type = $1"
            params.append(pool_type)

        total_cards = await fetch_value(query, params)
        _total_cards_cache[cache_key] = total_cards or 0
        return _total_cards_cache[cache_key]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取總卡數失敗 (pool_type: {pool_type}): {e}"
        ) from e


def _build_common_query_parts(
    pool_type: Optional[str], base_params: list
) -> tuple[str, str, str, list]:
    """建構通用的查詢組件 (pool_type_clause, draw_history_pool_clause, luck_stats_cte_body, and updated_params)"""
    params = list(base_params)
    pool_type_clause = ""
    draw_history_pool_clause = ""
    luck_stats_cte_body = ""

    if _should_apply_pool_filter(pool_type):
        param_idx = len(params) + 1
        pool_type_clause = f"WHERE mc.pool_type = ${param_idx}"
        draw_history_pool_clause = f"WHERE dh.user_selected_pool = ${param_idx}"
        luck_stats_cte_body = f"""
            SELECT
                user_id,
                pool_type,
                draws_in_pool,
                top_tier_in_pool,
                longest_drought,
                CASE
                    WHEN pool_type IN ('main', 'hololive', 'ua', 'ptcg', 'wixoss', 'summer', 'vd') THEN 0.0006
                    WHEN pool_type IN ('special', 'special_maid', 'ongeki') THEN 0.0005
                    ELSE 0.0006
                END AS expected_rate
            FROM gacha_user_luck_summary_mv
            WHERE pool_type = ${param_idx}
        """
        params.append(pool_type)
    else:
        luck_stats_cte_body = """
            SELECT
                user_id,
                'all' as pool_type,
                SUM(draws_in_pool) as draws_in_pool,
                SUM(top_tier_in_pool) as top_tier_in_pool,
                MAX(longest_drought) as longest_drought,
                SUM(
                    CASE
                        WHEN pool_type IN ('main', 'hololive', 'ua', 'ptcg', 'wixoss', 'summer', 'vd') THEN 0.0006
                        WHEN pool_type IN ('special', 'special_maid', 'ongeki') THEN 0.0005
                        ELSE 0.0006
                    END * draws_in_pool
                ) / NULLIF(SUM(draws_in_pool), 0) as expected_rate
            FROM gacha_user_luck_summary_mv
            GROUP BY user_id
        """
    return pool_type_clause, draw_history_pool_clause, luck_stats_cte_body, params


def _prepare_leaderboard_query_components(
    config_structure: Dict[str, Any], pool_type: Optional[str]
) -> Tuple[Dict[str, Any], List[Any]]:
    """準備排行榜查詢組件"""
    pool_type_clause, draw_history_pool_clause, luck_stats_cte_body, params_main = (
        _build_common_query_parts(pool_type, [])
    )

    table_format_args = {
        "collection_table": COLLECTION_TABLE,
        "master_card_table": MASTER_CARD_TABLE,
        "player_portfolios_table": PLAYER_PORTFOLIOS_TABLE,
        "virtual_assets_table": VIRTUAL_ASSETS_TABLE,
        "market_transactions_table": MARKET_TRANSACTIONS_TABLE,
        "user_table": USER_TABLE,
        "pool_type_clause": pool_type_clause,
        "draw_history_pool_clause": draw_history_pool_clause,
        "luck_stats_cte_body": luck_stats_cte_body,
    }

    return table_format_args, params_main


def _handle_collection_unique_leaderboard(
    config_structure: Dict[str, Any], table_format_args: Dict[str, Any]
) -> Tuple[str, str]:
    """處理收藏唯一排行榜的特殊邏輯"""
    global _rarities
    from_clause = config_structure["from_template"].format(**table_format_args)

    select_fields_parts = [
        "gu.user_id",
        "COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name",
        "COALESCE(urs.total_unique_cards, 0)::INTEGER AS total_unique_cards",
    ]
    from_clause_parts = [from_clause]

    for r in _rarities or []:
        select_fields_parts.append(
            f"COALESCE(urs.rarity_{r}_unique_cards, 0)::INTEGER AS rarity_{r}_unique_cards"
        )
        select_fields_parts.append(
            f"COALESCE(rt_{r}.total_cards, 0)::INTEGER AS rarity_{r}_total_cards"
        )
        from_clause_parts.append(
            f"LEFT JOIN RarityTotals rt_{r} ON rt_{r}.rarity = {r}"
        )

    select_fields = ",\n".join(select_fields_parts)
    from_clause = "\n".join(from_clause_parts)
    return select_fields, from_clause


def _handle_special_leaderboard_types(
    leaderboard_type: str,
    where_clause_template: str,
    params_main: List[Any],
    params_count: List[Any],
    **kwargs,
) -> Tuple[List[str], str]:
    """處理特殊排行榜類型的參數"""
    all_where_conditions = []
    order_by = kwargs.get("order_by", "")

    if leaderboard_type == "stock_holding":
        asset_symbol = kwargs.get("asset_symbol")
        if not asset_symbol:
            raise ValueError(f"股票代號為必填項目，排行榜類型: {leaderboard_type}")
        param_index = len(params_main) + 1
        updated_where_clause = where_clause_template.replace("$1", f"${param_index}")
        all_where_conditions.append(updated_where_clause)
        params_main.append(asset_symbol)
        params_count.append(asset_symbol)
    elif GameRegistry.is_valid_game_type(leaderboard_type):
        param_index = len(params_main) + 1
        updated_where_clause = where_clause_template.replace("$1", f"${param_index}")
        all_where_conditions.append(updated_where_clause)
        params_main.append(leaderboard_type)
        params_count.append(leaderboard_type)
        game_stat_type = kwargs.get("game_stat_type")
        if game_stat_type:
            order_by = _get_game_stat_order_by(game_stat_type, leaderboard_type)

    return all_where_conditions, order_by


def _build_leaderboard_queries(
    config_structure: Dict[str, Any],
    table_format_args: Dict[str, Any],
    leaderboard_type: str,
    all_where_conditions: List[str],
    select_fields: str,
    from_clause: str,
    order_by: str,
    group_by_clause: str,
    params_main: List[Any],
    params_count: List[Any],
    limit: Optional[int],
    offset: int,
) -> Tuple[str, str]:
    """構建主查詢和計數查詢"""
    with_clause = config_structure["with_template"].format(**table_format_args)

    # 構建WHERE子句
    actual_where_clause = (
        f"WHERE {' AND '.join(filter(None, all_where_conditions))}"
        if any(all_where_conditions)
        else ""
    )

    # 構建主查詢
    query_main = f"{with_clause} SELECT {select_fields} FROM {from_clause}"
    if actual_where_clause:
        query_main += f" {actual_where_clause}"
    if group_by_clause:
        query_main += f" GROUP BY {group_by_clause}"
    query_main += f" ORDER BY {order_by}"

    # 添加分頁參數
    current_param_idx_main = len(params_main) + 1
    if limit is not None:
        query_main += f" LIMIT ${current_param_idx_main}"
        params_main.append(limit)
        current_param_idx_main += 1
    query_main += f" OFFSET ${current_param_idx_main}"
    params_main.append(offset)

    # 構建計數查詢
    if group_by_clause:
        count_select = f"COUNT(*) FROM (SELECT 1 FROM {from_clause}"
        if actual_where_clause:
            count_select += f" {actual_where_clause}"
        count_select += f" GROUP BY {group_by_clause}) AS subquery_for_count"
    else:
        count_select = f"COUNT(*) FROM {from_clause}"
        if actual_where_clause:
            count_select += f" {actual_where_clause}"
    query_count = f"{with_clause} SELECT {count_select}"

    return query_main, query_count


async def get_leaderboard(**kwargs) -> Tuple[List[Dict[str, Any]], int]:
    """(Async) 通用排行榜獲取方法，使用 kwargs 實現統一接口"""
    global _pool_types, _rarities
    leaderboard_type = kwargs.pop("leaderboard_type")
    limit = kwargs.get("limit", 10)
    offset = kwargs.get("offset", 0)
    pool_type = kwargs.get("pool_type")

    config_structure = get_leaderboard_config_structure().get(leaderboard_type)
    if not config_structure:
        raise ValueError(f"無效的排行榜類型: {leaderboard_type}")

    try:
        await _ensure_metadata_loaded()

        # 準備查詢組件
        table_format_args, params_main = _prepare_leaderboard_query_components(
            config_structure, pool_type
        )
        params_count = list(params_main)

        # 獲取基本查詢組件
        select_fields = config_structure.get("select_template")
        order_by = config_structure.get("order_by_template")
        from_template = config_structure.get("from_template")
        group_by_clause = config_structure.get("group_by_template") or ""
        where_clause_template = config_structure.get("where_template", "") or ""

        if from_template is None:
            raise ValueError(f"排行榜 '{leaderboard_type}' 的 from_template 未定義")
        from_clause = from_template.format(**table_format_args)

        if order_by is None:
            raise ValueError(f"排行榜 '{leaderboard_type}' 的 order_by_template 未定義")

        # 處理特殊排行榜類型
        if leaderboard_type == "collection_unique":
            select_fields, from_clause = _handle_collection_unique_leaderboard(
                config_structure, table_format_args
            )
            all_where_conditions = []
        else:
            all_where_conditions, updated_order_by = _handle_special_leaderboard_types(
                leaderboard_type,
                where_clause_template,
                params_main,
                params_count,
                order_by=order_by,
                **kwargs,
            )
            if updated_order_by:
                order_by = updated_order_by

        # 構建查詢
        if select_fields is None:
            raise ValueError(f"排行榜 '{leaderboard_type}' 的 select_fields 未定義")

        query_main, query_count = _build_leaderboard_queries(
            config_structure,
            table_format_args,
            leaderboard_type,
            all_where_conditions,
            select_fields,
            from_clause,
            order_by,
            group_by_clause,
            params_main,
            params_count,
            limit,
            offset,
        )

        # 並發執行查詢
        results_records_task = fetch_all(query_main, params_main)
        total_rows_task = fetch_value(query_count, params_count)
        results_records, total_rows = await asyncio.gather(
            results_records_task, total_rows_task
        )

        results = [dict(r) for r in results_records] if results_records else []
        return (results, total_rows or 0)
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"獲取排行榜失敗: type={leaderboard_type}, error={e}"
        ) from e


async def search_player_by_name(**kwargs) -> Optional[Dict[str, Any]]:
    """搜尋玩家在排行榜中的位置（基於名稱模糊匹配），使用 kwargs 實現統一接口"""
    return await _search_player(**kwargs)


async def search_player_by_user_id(**kwargs) -> Optional[Dict[str, Any]]:
    """根據用戶ID搜尋玩家在排行榜中的位置，使用 kwargs 實現統一接口"""
    return await _search_player(**kwargs)


def _prepare_search_query_components(
    config_structure: Dict[str, Any],
    pool_type: Optional[str],
    leaderboard_type: str,
    **kwargs,
) -> Tuple[Dict[str, Any], List[Any], str, str, str, str]:
    """準備搜索查詢組件"""
    pool_type_clause, draw_history_pool_clause, luck_stats_cte_body, base_params = (
        _build_common_query_parts(pool_type, [])
    )

    table_format_args = {
        "collection_table": COLLECTION_TABLE,
        "master_card_table": MASTER_CARD_TABLE,
        "player_portfolios_table": PLAYER_PORTFOLIOS_TABLE,
        "virtual_assets_table": VIRTUAL_ASSETS_TABLE,
        "market_transactions_table": MARKET_TRANSACTIONS_TABLE,
        "user_table": USER_TABLE,
        "pool_type_clause": pool_type_clause,
        "draw_history_pool_clause": draw_history_pool_clause,
        "luck_stats_cte_body": luck_stats_cte_body,
    }

    with_clause = config_structure["with_template"].format(**table_format_args)
    select_fields = config_structure["select_template"]
    order_by = config_structure["order_by_template"]
    from_clause = config_structure["from_template"].format(**table_format_args)

    return (
        table_format_args,
        base_params,
        with_clause,
        select_fields,
        order_by,
        from_clause,
    )


def _handle_search_special_leaderboard_types(
    leaderboard_type: str,
    config_structure: Dict[str, Any],
    table_format_args: Dict[str, Any],
    base_params: List[Any],
    where_clause_template: str,
    select_fields: str,
    from_clause: str,
    **kwargs,
) -> Tuple[str, str, str]:
    """處理搜索中的特殊排行榜類型"""
    global _rarities

    if leaderboard_type == "collection_unique":
        select_fields_parts = [
            "gu.user_id",
            "COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name",
            "COALESCE(urs.total_unique_cards, 0)::INTEGER AS total_unique_cards",
        ]
        from_clause_parts = [from_clause]

        for r in _rarities or []:
            select_fields_parts.append(
                f"COALESCE(urs.rarity_{r}_unique_cards, 0)::INTEGER AS rarity_{r}_unique_cards"
            )
            select_fields_parts.append(
                f"COALESCE(rt_{r}.total_cards, 0)::INTEGER AS rarity_{r}_total_cards"
            )
            from_clause_parts.append(
                f"LEFT JOIN RarityTotals rt_{r} ON rt_{r}.rarity = {r}"
            )

        select_fields = ",\n".join(select_fields_parts)
        from_clause = "\n".join(from_clause_parts)
    elif leaderboard_type == "stock_holding":
        asset_symbol = kwargs.get("asset_symbol")
        if not asset_symbol:
            raise ValueError(f"股票代號為必填項目，排行榜類型: {leaderboard_type}")
        param_index = len(base_params) + 1
        where_clause_template = where_clause_template.replace("$1", f"${param_index}")
        base_params.append(asset_symbol)
    elif GameRegistry.is_valid_game_type(leaderboard_type):
        param_index = len(base_params) + 1
        where_clause_template = where_clause_template.replace("$1", f"${param_index}")
        base_params.append(leaderboard_type)

    return select_fields, from_clause, where_clause_template


def _get_search_order_by(leaderboard_type: str, order_by: str, **kwargs) -> str:
    """獲取搜索用的排序邏輯"""
    order_by_for_rank = order_by

    if leaderboard_type == "stock_holding":
        order_by_for_rank = order_by.replace("stock_quantity", "pp.quantity")
    elif leaderboard_type == "luck_index":
        order_by_for_rank = """
            CASE
                WHEN ls.draws_in_pool > 0 AND ls.expected_rate > 0
                THEN (ls.top_tier_in_pool::numeric / ls.draws_in_pool) / ls.expected_rate
                ELSE 1.0
            END DESC, gu.user_id ASC
        """
    elif leaderboard_type == "avg_draws":
        order_by_for_rank = """
            CASE
                WHEN ls.top_tier_in_pool > 0
                THEN ls.draws_in_pool::numeric / ls.top_tier_in_pool
                ELSE 0
            END ASC, gu.user_id ASC
        """
    elif leaderboard_type == "longest_drought":
        order_by_for_rank = "ls.longest_drought DESC, gu.user_id ASC"
    elif GameRegistry.is_valid_game_type(leaderboard_type) and (
        game_stat_type := kwargs.pop("game_stat_type", None)
    ):
        kwargs.pop("game_type", None)
        order_by_for_rank = _get_game_stat_order_by(
            game_stat_type, leaderboard_type, **kwargs
        )

    return order_by_for_rank


async def _search_player(**kwargs) -> Optional[Dict[str, Any]]:
    """統一的玩家搜索方法"""
    from utils.logger import logger

    global _pool_types, _rarities
    # 使用 pop 安全地提取所有可能作為位置參數傳遞的鍵
    leaderboard_type = kwargs.pop("leaderboard_type")
    user_id = kwargs.pop("user_id", None)
    player_name = kwargs.pop("player_name", None)
    pool_type = kwargs.pop("pool_type", None)
    search_type = (
        ""  # Initialize search_type to ensure it's always available in the except block
    )

    logger.info(
        f"[LEADERBOARD_REPO] _search_player 開始: user_id={user_id}, player_name={player_name}, type={leaderboard_type}"
    )

    config_structure = get_leaderboard_config_structure().get(leaderboard_type)
    if not config_structure:
        raise ValueError(f"無效的排行榜類型: {leaderboard_type}")

    try:
        await _ensure_metadata_loaded()

        # 準備查詢組件
        (
            table_format_args,
            base_params,
            with_clause,
            select_fields,
            order_by,
            from_clause,
        ) = _prepare_search_query_components(
            config_structure,
            pool_type,
            leaderboard_type,
            **kwargs,
        )

        group_by_clause = config_structure.get("group_by_template", "") or ""
        where_clause_template = config_structure.get("where_template", "") or ""

        # 處理特殊排行榜類型
        select_fields, from_clause, where_clause_template = (
            _handle_search_special_leaderboard_types(
                leaderboard_type,
                config_structure,
                table_format_args,
                base_params,
                where_clause_template,
                select_fields,
                from_clause,
                **kwargs,
            )
        )

        # 獲取排序邏輯
        order_by_for_rank = _get_search_order_by(leaderboard_type, order_by, **kwargs)

        # 構建內部查詢組件
        inner_query_select_for_rank = f"SELECT {select_fields}, ROW_NUMBER() OVER (ORDER BY {order_by_for_rank}) AS rank"
        inner_query_from = f"FROM {from_clause}"
        actual_where_clause_for_rank = ""
        if where_clause_template:
            actual_where_clause_for_rank = f"WHERE {where_clause_template}"
        inner_query_group_by_for_rank = ""
        if group_by_clause:
            inner_query_group_by_for_rank = f"GROUP BY {group_by_clause}"

        search_param_index = len(base_params) + 1
        connector = "," if with_clause.strip() else "WITH"

        # 根據搜索類型構建查詢條件
        if user_id is not None:
            final_query_params = base_params + [user_id]
            search_condition = f"user_id = ${search_param_index}"
            search_type = f"user_id={user_id}"
        elif player_name is not None:
            final_query_params = base_params + [f"%{player_name}%"]
            search_condition = f"LOWER(user_name) LIKE LOWER(${search_param_index})"
            search_type = f"player_name={player_name}"
        else:
            # 如果 user_id 和 player_name 都為 None，則不應執行搜索
            raise ValueError("必須提供 user_id 或 player_name 進行搜索")

        # 構建最終查詢
        query = f"""
            {with_clause}
            {connector} user_ranks AS (
                {inner_query_select_for_rank}
                {inner_query_from}
                {actual_where_clause_for_rank}
                {inner_query_group_by_for_rank}
            )
            SELECT * FROM user_ranks
            WHERE {search_condition}
            ORDER BY rank LIMIT 1
        """

        logger.info(f"[LEADERBOARD_REPO] 準備執行查詢，參數: {final_query_params}")
        logger.debug(f"[LEADERBOARD_REPO] SQL查詢: {query}")

        import time

        start_time = time.time()
        result_record = await fetch_one(query, final_query_params)
        end_time = time.time()

        logger.info(
            f"[LEADERBOARD_REPO] 查詢完成，耗時: {end_time - start_time:.3f}秒，結果: {result_record}"
        )

        return dict(result_record) if result_record else None
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(
            f"搜尋玩家排名失敗: type={leaderboard_type}, search_criteria='{search_type}', error={e}"
        ) from e


async def get_total_users_count() -> int:
    """(Async) 獲取 gacha_users 表中的總用戶數"""
    try:
        query = f"SELECT COUNT(*)::integer as count FROM {USER_TABLE}"
        count = await fetch_value(query)
        return count or 0
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"獲取總用戶數失敗: {e}") from e


async def update_user_leaderboard_stats(user_stats: List[Dict[str, Any]]) -> None:
    """(Async) 批量更新用戶排行榜統計數據 (使用 batch_upsert)"""
    if not user_stats:
        return

    for stat in user_stats:
        if "user_id" not in stat:
            raise ValueError("每條統計數據記錄必須包含 user_id 字段")

    try:
        all_keys = set()
        for stat in user_stats:
            all_keys.update(stat.keys())
        normalized_stats = []
        for stat in user_stats:
            new_stat = {}
            for key in all_keys:
                new_stat[key] = stat.get(key)
            normalized_stats.append(new_stat)
        inserted, updated = await batch_upsert(
            LEADERBOARD_STATS_TABLE, normalized_stats, "user_id"
        )
        logger.debug("批量更新排行榜統計: 插入 %s, 更新 %s", inserted, updated)
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"批量更新排行榜統計失敗: {e}") from e


async def get_all_market_assets() -> List[Dict[str, str]]:
    """獲取市場上所有可用的股票資產列表"""
    query = f"SELECT asset_symbol, asset_name FROM {VIRTUAL_ASSETS_TABLE} ORDER BY asset_name"
    try:
        records = await fetch_all(query)
        return [{"symbol": r["asset_symbol"], "name": r["asset_name"]} for r in records]
    except asyncpg.PostgresError as e:
        raise DatabaseOperationError(f"從資料庫獲取市場資產列表失敗: {e}") from e


GAME_STAT_ORDER_BY_MAP = {
    "total_profit_loss": "ugs.total_profit_loss DESC NULLS LAST, gu.user_id ASC",
    "total_wins": "ugs.total_wins DESC NULLS LAST, gu.user_id ASC",
    "total_games": "ugs.total_games DESC NULLS LAST, gu.user_id ASC",
    "win_rate": "CASE WHEN ugs.total_games > 0 THEN ROUND((ugs.total_wins::numeric / ugs.total_games * 100), 2) ELSE 0 END DESC NULLS LAST, gu.user_id ASC",
    "consecutive_wins": "(ugs.game_specific_stats->>'consecutive_wins')::int DESC NULLS LAST, gu.user_id ASC",
    "max_win": "ugs.max_win DESC NULLS LAST, gu.user_id ASC",
    "max_loss": "ugs.max_loss ASC NULLS LAST, gu.user_id ASC",
    "blackjack": {
        "blackjack_count": "(ugs.game_specific_stats->>'blackjack_count')::int DESC NULLS LAST, gu.user_id ASC",
        "five_card_trick_count": "(ugs.game_specific_stats->>'five_card_trick_count')::int DESC NULLS LAST, gu.user_id ASC",
    },
    "baccarat": {
        "banker_win_rate": "(ugs.game_specific_stats->>'banker_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
        "player_win_rate": "(ugs.game_specific_stats->>'player_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
        "tie_win_rate": "(ugs.game_specific_stats->>'tie_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
    },
    "dice": {
        "big_choice_win_rate": "(ugs.game_specific_stats->>'big_choice_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
        "small_choice_win_rate": "(ugs.game_specific_stats->>'small_choice_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
        "triple_win_rate": "(ugs.game_specific_stats->>'triple_win_rate')::numeric DESC NULLS LAST, gu.user_id ASC",
        "triple_count": "(ugs.game_specific_stats->>'triple_count')::int DESC NULLS LAST, gu.user_id ASC",
    },
    "mines": {
        "avg_tiles_revealed": "(ugs.game_specific_stats->>'avg_tiles_revealed')::numeric DESC NULLS LAST, gu.user_id ASC",
        "special_coin_found": "(ugs.game_specific_stats->>'special_coin_found')::int DESC NULLS LAST, gu.user_id ASC",
        "special_star_found": "(ugs.game_specific_stats->>'special_star_found')::int DESC NULLS LAST, gu.user_id ASC",
    },
    "tower": {
        "max_level_reached": "(ugs.game_specific_stats->>'max_level_reached')::int DESC NULLS LAST, gu.user_id ASC",
        "cashout_rate": "(ugs.game_specific_stats->>'cashout_count')::numeric / NULLIF(ugs.total_games, 0) DESC NULLS LAST, gu.user_id ASC",
        "avg_levels_completed": "(ugs.game_specific_stats->>'avg_levels_completed')::numeric DESC NULLS LAST, gu.user_id ASC",
        "tnt_hit_rate": "(ugs.game_specific_stats->>'tnt_hit_count')::numeric / NULLIF(ugs.total_games, 0) ASC NULLS LAST, gu.user_id ASC",
    },
    "slot": {
        "jackpot_count": "(ugs.game_specific_stats->>'jackpot_count')::int DESC NULLS LAST, gu.user_id ASC",
        "max_consecutive_symbols": "(ugs.game_specific_stats->>'max_consecutive_symbols')::int DESC NULLS LAST, gu.user_id ASC",
        "rtp_percentage": "(ugs.game_specific_stats->>'rtp_percentage')::numeric DESC NULLS LAST, gu.user_id ASC",
    },
    "poker1v1": {
        "fold_rate": "(ugs.game_specific_stats->>'fold_rate')::numeric ASC NULLS LAST, gu.user_id ASC",
        "total_hands_played": "(ugs.game_specific_stats->>'total_hands_played')::int DESC NULLS LAST, gu.user_id ASC",
        "total_all_in_count": "(ugs.game_specific_stats->>'total_all_in_count')::int DESC NULLS LAST, gu.user_id ASC",
    },
    "spin_wheel": {
        "total_big_wins": "(ugs.game_specific_stats->'result_type_counts'->>'big_win')::int DESC NULLS LAST, gu.user_id ASC",
    },
}


def _get_game_stat_order_by(game_stat_type: str, game_type: str, **kwargs) -> str:
    """根據遊戲統計類型從映射中獲取排序子句"""
    # 優先從特定遊戲的統計中查找
    game_specific_map = GAME_STAT_ORDER_BY_MAP.get(game_type)
    if isinstance(game_specific_map, dict):
        order_by_clause = game_specific_map.get(game_stat_type)
        if order_by_clause and isinstance(order_by_clause, str):
            return order_by_clause

    # 如果在特定遊戲中找不到，或遊戲類型不存在，則從通用統計中查找
    fallback_clause = GAME_STAT_ORDER_BY_MAP.get(game_stat_type)
    if isinstance(fallback_clause, str):
        return fallback_clause

    # 如果都找不到，回傳預設排序
    default_order_by = GAME_STAT_ORDER_BY_MAP.get("total_profit_loss")
    if not isinstance(default_order_by, str):
        # 這是個預期外的狀況，表示 MAP 設定有誤
        logger.error("Default leaderboard order_by clause is not a string.")
        return "ugs.total_profit_loss DESC, gu.user_id ASC"
    return default_order_by
