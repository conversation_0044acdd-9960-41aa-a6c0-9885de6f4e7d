# Gacha 抽婆系统

一个为Discord机器人设计的抽卡系统，包含抽卡、收藏管理、经济系统和21点游戏功能。

## 功能特点

- **抽卡系统**: 使用油币抽取不同稀有度的卡片
- **收藏管理**: 查看已收集的卡片，了解系列收集进度
- **经济系统**: 通过每日签到、游戏和卖卡获取油币
- **21点游戏**: 通过游戏赢取额外油币

## 指令列表

- `/w` - 抽卡（消耗30油币）- 从主卡池和典藏卡池
- `/ws` - 抽取典藏卡片（消耗120油币）- 仅从典藏卡池
- `/mw [page] [sort_by] [sort_order]` - 查看卡册
- `/mws [series]` - 查看某个系列的收集状况
- `/sw <card_id> [quantity]` - 卖出卡片获取油币
- `/balance [claim]` - 查看油币余额或领取每日奖励
- `/blackjack [bet]` 或 `/bj [bet]` - 玩21点游戏

## 系统架构

### 数据层

- **数据库**: 使用PostgreSQL存储所有数据
- **数据表**:
  - `gacha_users` - 用户表，存储用户信息和油币余额
  - `gacha_master_cards` - 卡片主表，存储所有可抽取卡片信息
  - `gacha_user_collections` - 用户收藏表，记录用户拥有的卡片

### 存储库层

- `UserRepository` - 用户数据操作
- `MasterCardRepository` - 卡片数据操作
- `UserCollectionRepository` - 用户收藏操作

### 服务层

- `GachaService` - 抽卡核心逻辑
- `CollectionService` - 收藏管理逻辑
- `EconomyService` - 经济与交易逻辑
- `GameService` - Blackjack游戏逻辑

### 视图层

- `draw_view.py` - 抽卡结果显示
- `collection_view.py` - 卡册与系列收集显示
- `blackjack_view.py` - 21点游戏界面

### 命令层

- `draw_command.py` - 抽卡命令处理
- `collection_command.py` - 收藏命令处理
- `economy_command.py` - 经济命令处理
- `sell_command.py` - 卖卡命令处理
- `blackjack_command.py` - 游戏命令处理

## 集成方法

在Discord机器人主文件中添加以下代码：

```python
from gacha import setup_gacha


# 在bot设置完成后，异步初始化Gacha系统
async def init_gacha():
    await setup_gacha(bot)


# 启动bot时调用
bot.loop.create_task(init_gacha())
```

## 卡片数据

该系统支持从JSON文件导入卡片数据，格式示例：

```json
[
  {
    "card_id": 1,
    "name": "示例卡片",
    "series": "示例系列",
    "rarity": "T3",
    "image_url": "https://example.com/card.jpg",
    "description": "这是一张示例卡片",
    "is_limited": false
  }
]
```

## 稀有度系统

卡片分为7个稀有度等级：

- T1 (40%概率) - 普通卡
- T2 (30%概率) - 较好的卡
- T3 (15%概率) - 稀有卡
- T4 (10%概率) - 非常稀有的卡
- T5 (3%概率) - 极其稀有的卡
- T6 (1.5%概率) - 传说级卡
- TS (0.5%概率) - 特殊限定卡

## 开发信息

- 开发语言: Python 3.8+
- 依赖库: discord.py 2.0+, asyncpg
- 架构模式: 分层架构 (存储库模式 + 服务模式)

## 技术架构

系统采用分层架构设计：

1. **数据存储层** - 使用PostgreSQL数据库存储用户、卡片和收藏信息
2. **数据访问层** - 提供数据库交互的Repository接口
3. **服务层** - 实现核心业务逻辑，包括抽卡、收藏管理和经济系统
4. **命令处理层** - 处理Discord斜杠命令，调用服务层功能
5. **视图层** - 提供用户界面，使用Discord的Embed和按钮组件

## 安装和配置

1. 确保PostgreSQL数据库已正确配置
2. 系统会自动创建所需的数据表
3. 将卡片数据导入数据库
4. 注册Discord斜杠命令

## 开发指南

详细的开发进度和规划请查看 [IMPLEMENTATION_PROGRESS.md](IMPLEMENTATION_PROGRESS.md)。

### 技术栈
- Python 3.8+
- discord.py 2.0+
- PostgreSQL 12+

## 许可证

本项目仅供内部使用，未经许可不得分发或用于商业用途。 