# Pioneer System 時代配置
# 定義遊戲進程的時代劃分和晉升條件

# 第一時代：起步階段
- era_id: 1
  name: "拓荒時代"
  description: "開拓者的起步階段，學習基本的採集和製作技能"
  requirements: []  # 初始時代，無需求
  costs: []
  unlocks:
    - type: "action"
      items: ["gather_wood", "gather_mine", "craft_wooden_plank", "craft_stone_brick"]
    - type: "facility"
      items: ["pioneer_shop"]
    - type: "skill"
      items: ["woodcutting", "mining", "woodworking", "masonry"]

# 第二時代：工業化初期
- era_id: 2
  name: "工業萌芽"
  description: "開始建造基礎設施，解鎖自動化生產"
  requirements:
    - type: "skill_level"
      skill: "woodcutting"
      min_level: 10
    - type: "skill_level"
      skill: "mining"
      min_level: 10
    - type: "currency"
      currency: "oil"
      amount: 50000
    - type: "item"
      item_id: "wooden_chair"
      quantity: 10
  costs:
    - type: "currency"
      currency: "oil"
      amount: 100000
    - type: "item"
      item_id: "wooden_plank"
      quantity: 100
    - type: "item"
      item_id: "stone_brick"
      quantity: 50
  unlocks:
    - type: "facility"
      items: ["lumber_mill", "quarry", "woodworking_bench"]
    - type: "action"
      items: ["build_lumber_mill", "build_quarry", "build_woodworking_bench"]
    - type: "research"
      items: ["basic_efficiency", "basic_gathering", "basic_crafting"]

# 第三時代：重工業發展
- era_id: 3
  name: "重工業時代"
  description: "發展冶金工業，解鎖高級製作和研究系統"
  requirements:
    - type: "skill_level"
      skill: "woodworking"
      min_level: 15
    - type: "skill_level"
      skill: "masonry"
      min_level: 15
    - type: "currency"
      currency: "oil"
      amount: 200000
    - type: "facility_count"
      facility_type: "lumber_mill"
      min_count: 1
    - type: "item"
      item_id: "wooden_table"
      quantity: 5
  costs:
    - type: "currency"
      currency: "oil"
      amount: 500000
    - type: "currency"
      currency: "oil"
      amount: 10000
    - type: "item"
      item_id: "stone_brick"
      quantity: 500
    - type: "item"
      item_id: "wooden_plank"
      quantity: 200
  unlocks:
    - type: "facility"
      items: ["forge", "research_lab"]
    - type: "skill"
      items: ["smelting"]
    - type: "action"
      items: ["craft_copper_ingot", "build_forge", "build_research_lab"]
    - type: "research"
      items: ["production_efficiency", "gathering_mastery", "economic_optimization"]

# 第四時代：自動化時代
- era_id: 4
  name: "自動化革命"
  description: "大規模自動化生產，解鎖高級研究項目"
  requirements:
    - type: "skill_level"
      skill: "smelting"
      min_level: 20
    - type: "currency"
      currency: "oil"
      amount: 1000000
    - type: "facility_count"
      facility_type: "forge"
      min_count: 1
    - type: "research_level"
      project_id: "production_efficiency"
      min_level: 5
    - type: "item"
      item_id: "copper_ingot"
      quantity: 100
  costs:
    - type: "currency"
      currency: "oil"
      amount: 2000000
    - type: "currency"
      currency: "oil"
      amount: 50000
    - type: "item"
      item_id: "copper_ingot"
      quantity: 500
    - type: "item"
      item_id: "stone_brick"
      quantity: 1000
  unlocks:
    - type: "facility"
      items: ["advanced_assembly_line", "warehouse"]
    - type: "research"
      items: ["energy_management", "skill_acceleration", "automation_technology"]
    - type: "upgrade"
      items: ["facility_auto_input", "facility_auto_output"]

# 第五時代：高科技時代
- era_id: 5
  name: "科技飛躍"
  description: "解鎖最先進的技術和設施"
  requirements:
    - type: "currency"
      currency: "oil"
      amount: 5000000
    - type: "currency"
      currency: "oil"
      amount: 100000
    - type: "facility_count"
      facility_type: "advanced_assembly_line"
      min_count: 1
    - type: "research_level"
      project_id: "automation_technology"
      min_level: 10
    - type: "total_facility_upgrades"
      min_count: 20
  costs:
    - type: "currency"
      currency: "oil"
      amount: 10000000
    - type: "currency"
      currency: "oil"
      amount: 200000
    - type: "item"
      item_id: "advanced_product"
      quantity: 100
  unlocks:
    - type: "facility"
      items: ["power_plant"]
    - type: "research"
      items: ["resource_preservation", "quality_control", "market_analysis"]
    - type: "special"
      items: ["era_mastery_bonus"]

# 第六時代：未來展望 (預留)
- era_id: 6
  name: "未來展望"
  description: "探索未知的技術領域 (開發中)"
  requirements:
    - type: "currency"
      currency: "oil"
      amount: 50000000
    - type: "currency"
      currency: "oil"
      amount: 1000000
    - type: "research_total_investment"
      min_amount: 10000000
  costs:
    - type: "currency"
      currency: "oil"
      amount: 100000000
    - type: "currency"
      currency: "oil"
      amount: 2000000
  unlocks:
    - type: "special"
      items: ["future_content_placeholder"]
