-- 修正 story_chunk_summaries 表的唯一約束
-- 添加缺失的唯一約束以支援 ON CONFLICT 語句

-- 首先檢查是否已存在重複數據，如果有則清理
DELETE FROM public.story_chunk_summaries a
USING public.story_chunk_summaries b
WHERE a.id > b.id
  AND a.story_id = b.story_id
  AND a.start_turn_number = b.start_turn_number
  AND a.end_turn_number = b.end_turn_number;

-- 添加唯一約束
ALTER TABLE public.story_chunk_summaries
ADD CONSTRAINT uq_story_turn_range 
UNIQUE (story_id, start_turn_number, end_turn_number);