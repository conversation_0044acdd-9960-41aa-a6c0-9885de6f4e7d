"""
Pioneer System - Facility Repository
設施、設施槽位、卡片指派相關的資料庫存取
"""

from datetime import datetime
from typing import Any, List, Optional

import asyncpg

from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)
from pioneer.exceptions import (
    CardAssignmentNotFoundError,
    FacilityNotFoundError,
    PioneerDatabaseError,
)
from pioneer.models.pioneer_models import Facility, FacilityState
from utils.logger import logger

# ========================================
# 設施相關
# ========================================


async def create_facility(
    user_id: int,
    facility_type: str,
    facility_name: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """創建設施"""
    query = """
        INSERT INTO pioneer_facilities (user_id, facility_type, facility_name)
        VALUES ($1, $2, $3)
        RETURNING id
    """
    try:
        facility_id = await fetch_value(
            query, (user_id, facility_type, facility_name), connection=connection
        )
        return facility_id
    except Exception as e:
        logger.error(
            f"創建設施失敗 user_id={user_id}, facility_type={facility_type}: {e}"
        )
        raise PioneerDatabaseError("create_facility", str(e), e) from e


async def get_facility(
    facility_id: int, connection: Optional[asyncpg.Connection] = None
) -> Facility:
    """獲取設施信息"""
    query = "SELECT * FROM pioneer_facilities WHERE id = $1"
    result = await fetch_one(query, (facility_id,), connection=connection)

    if not result:
        raise FacilityNotFoundError(facility_id)

    return Facility(
        id=result["id"],
        user_id=result["user_id"],
        facility_type=result["facility_type"],
        facility_name=result["facility_name"],
        level=result["level"],
        upgrades=result["upgrades"] or {},
        efficiency_bonus=result["efficiency_bonus"],
        last_production_time=result["last_production_time"],
        is_active=result["is_active"],
        created_at=result["created_at"],
        updated_at=result["updated_at"],
    )


async def get_user_facilities(
    user_id: int,
    active_only: bool = False,
    connection: Optional[asyncpg.Connection] = None,
) -> List[Facility]:
    """獲取用戶所有設施"""
    if active_only:
        query = "SELECT * FROM pioneer_facilities WHERE user_id = $1 AND is_active = true ORDER BY created_at"
    else:
        query = (
            "SELECT * FROM pioneer_facilities WHERE user_id = $1 ORDER BY created_at"
        )
    results = await fetch_all(query, (user_id,), connection=connection)

    return [
        Facility(
            id=row["id"],
            user_id=row["user_id"],
            facility_type=row["facility_type"],
            facility_name=row["facility_name"],
            level=row["level"],
            upgrades=row["upgrades"] or {},
            efficiency_bonus=row["efficiency_bonus"],
            last_production_time=row["last_production_time"],
            is_active=row["is_active"],
            created_at=row["created_at"],
            updated_at=row["updated_at"],
        )
        for row in results
    ]


async def get_user_facilities_by_type(
    user_id: int, facility_type: str, connection: Optional[asyncpg.Connection] = None
) -> List[Facility]:
    """獲取用戶指定類型的設施"""
    query = "SELECT * FROM pioneer_facilities WHERE user_id = $1 AND facility_type = $2 ORDER BY created_at"
    results = await fetch_all(query, (user_id, facility_type), connection=connection)

    return [
        Facility(
            id=row["id"],
            user_id=row["user_id"],
            facility_type=row["facility_type"],
            facility_name=row["facility_name"],
            level=row["level"],
            upgrades=row["upgrades"] or {},
            efficiency_bonus=row["efficiency_bonus"],
            last_production_time=row["last_production_time"],
            is_active=row["is_active"],
            created_at=row["created_at"],
            updated_at=row["updated_at"],
        )
        for row in results
    ]


async def add_facility_upgrade(
    facility_id: int, upgrade_type: str, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """為設施添加升級"""
    query = """
        UPDATE pioneer_facilities
        SET upgrades = upgrades || jsonb_build_object($2, true),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
    """
    try:
        await execute_query(query, (facility_id, upgrade_type), connection=connection)
        return True
    except Exception as e:
        logger.error(
            f"添加設施升級失敗 facility_id={facility_id}, upgrade_type={upgrade_type}: {e}"
        )
        raise PioneerDatabaseError("add_facility_upgrade", str(e), e) from e


# ========================================
# 設施槽位管理
# ========================================


async def create_facility_slot(
    facility_id: int,
    slot_type: str,
    slot_index: int = 0,
    max_capacity: int = 100,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """創建設施槽位"""
    query = """
        INSERT INTO pioneer_facility_states (facility_id, slot_type, slot_index, max_capacity)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (facility_id, slot_type, slot_index) DO NOTHING
    """
    try:
        await execute_query(
            query,
            (facility_id, slot_type, slot_index, max_capacity),
            connection=connection,
        )
        return True
    except Exception as e:
        logger.error("創建設施槽位失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("create_facility_slot", str(e), e) from e


async def get_facility_slots(
    facility_id: int,
    slot_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[FacilityState]:
    """獲取設施槽位，可選擇性地按類型過濾"""
    if slot_type:
        query = "SELECT * FROM pioneer_facility_states WHERE facility_id = $1 AND slot_type = $2 ORDER BY slot_index"
        params = (facility_id, slot_type)
    else:
        query = "SELECT * FROM pioneer_facility_states WHERE facility_id = $1 ORDER BY slot_type, slot_index"
        params = (facility_id,)

    results = await fetch_all(query, params, connection=connection)

    return [
        FacilityState(
            facility_id=row["facility_id"],
            slot_type=row["slot_type"],
            slot_index=row["slot_index"],
            item_id=row["item_id"],
            quantity=row["quantity"],
            max_capacity=row["max_capacity"],
            slot_config=row["slot_config"] or {},
            updated_at=row["updated_at"],
        )
        for row in results
    ]


async def update_facility_production_time(
    facility_id: int,
    new_time: Optional[datetime] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """更新設施的最後生產時間"""
    if new_time is None:
        new_time = datetime.now()
    query = "UPDATE pioneer_facilities SET last_production_time = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $1"
    try:
        await execute_query(query, (facility_id, new_time), connection=connection)
        return True
    except Exception as e:
        logger.error("更新設施生產時間失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("update_facility_production_time", str(e), e) from e


async def add_facility_slot_item(
    facility_id: int,
    slot_type: str,
    slot_index: int,
    item_id: str,
    quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """向設施槽位添加物品"""
    query = """
        INSERT INTO pioneer_facility_states (facility_id, slot_type, slot_index, item_id, quantity)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (facility_id, slot_type, slot_index)
        DO UPDATE SET
            quantity = CASE
                WHEN pioneer_facility_states.item_id = $4 OR pioneer_facility_states.item_id IS NULL
                THEN pioneer_facility_states.quantity + $5
                ELSE pioneer_facility_states.quantity
            END,
            item_id = CASE
                WHEN pioneer_facility_states.item_id IS NULL
                THEN $4
                ELSE pioneer_facility_states.item_id
            END
    """
    try:
        await execute_query(
            query,
            (facility_id, slot_type, slot_index, item_id, quantity),
            connection=connection,
        )
        return True
    except Exception as e:
        logger.error("添加設施槽位物品失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("add_facility_slot_item", str(e), e) from e


async def consume_facility_slot_item(
    facility_id: int,
    slot_type: str,
    item_id: str,
    quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """消耗設施槽位物品"""
    query = """
        UPDATE pioneer_facility_states
        SET quantity = quantity - $4
        WHERE facility_id = $1 AND slot_type = $2 AND item_id = $3 AND quantity >= $4
    """
    try:
        result = await execute_query(
            query, (facility_id, slot_type, item_id, quantity), connection=connection
        )
        return result > 0
    except Exception as e:
        logger.error("消耗設施槽位物品失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("consume_facility_slot_item", str(e), e) from e


async def update_facility_slot_quantity(
    facility_id: int,
    slot_type: str,
    slot_index: int,
    new_quantity: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """直接更新槽位數量"""
    query = """
        UPDATE pioneer_facility_states
        SET quantity = $4, updated_at = CURRENT_TIMESTAMP
        WHERE facility_id = $1 AND slot_type = $2 AND slot_index = $3
    """
    try:
        await execute_query(
            query,
            (facility_id, slot_type, slot_index, new_quantity),
            connection=connection,
        )
        return True
    except Exception as e:
        logger.error("更新槽位數量失敗: %s", e)
        raise PioneerDatabaseError("update_facility_slot_quantity", str(e), e) from e


# ========================================
# 卡片指派管理
# ========================================


async def assign_card_to_facility(
    facility_id: int,
    user_collection_id: int,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """指派卡片到設施"""
    query = """
        INSERT INTO pioneer_facility_assignments (facility_id, user_collection_id)
        VALUES ($1, $2)
        ON CONFLICT (facility_id)
        DO UPDATE SET user_collection_id = $2, assigned_at = CURRENT_TIMESTAMP
    """
    try:
        await execute_query(
            query, (facility_id, user_collection_id), connection=connection
        )
        return True
    except Exception as e:
        logger.error("指派卡片失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("assign_card_to_facility", str(e), e) from e


async def remove_facility_card_assignment(
    facility_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """移除設施的卡片指派"""
    query = "DELETE FROM pioneer_facility_assignments WHERE facility_id = $1"
    try:
        await execute_query(query, (facility_id,), connection=connection)
        return True
    except Exception as e:
        logger.error("移除卡片指派失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("remove_facility_card_assignment", str(e), e) from e


async def get_facility_card_assignment(
    facility_id: int, connection: Optional[asyncpg.Connection] = None
) -> dict[str, Any]:
    """獲取設施的卡片指派"""
    query = "SELECT * FROM pioneer_facility_assignments WHERE facility_id = $1"
    try:
        result = await fetch_one(query, (facility_id,), connection=connection)
        if not result:
            raise CardAssignmentNotFoundError("facility_id", facility_id)
        return dict(result)
    except Exception as e:
        if isinstance(e, CardAssignmentNotFoundError):
            raise
        logger.error("獲取設施卡片指派失敗 facility_id=%s: %s", facility_id, e)
        raise PioneerDatabaseError("get_facility_card_assignment", str(e), e) from e


async def get_card_assignment_by_collection_id(
    user_collection_id: int, connection: Optional[asyncpg.Connection] = None
) -> dict[str, Any]:
    """根據收藏ID獲取卡片指派"""
    query = "SELECT * FROM pioneer_facility_assignments WHERE user_collection_id = $1"
    try:
        result = await fetch_one(query, (user_collection_id,), connection=connection)
        if not result:
            raise CardAssignmentNotFoundError("user_collection_id", user_collection_id)
        return dict(result)
    except Exception as e:
        if isinstance(e, CardAssignmentNotFoundError):
            raise
        logger.error(
            f"根據收藏ID獲取卡片指派失敗 user_collection_id={user_collection_id}: {e}"
        )
        raise PioneerDatabaseError(
            "get_card_assignment_by_collection_id", str(e), e
        ) from e
