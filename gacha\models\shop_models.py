import datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field


class ShopItemDefinition(BaseModel):
    id: str  # The key from the YAML dictionary, e.g., "main_R7_specific"
    display_name: str
    description: Optional[str] = None
    cost_oil_tickets: int
    ticket_type: str  # e.g., "specific", "random"

    # Fields primarily for 'random' or 'specific' type tickets related to cards
    pool_type: Optional[str] = None
    rarity: Optional[int] = None  # Assuming rarity is an integer level

    # Potential future fields based on common shop item properties
    # specific_card_ids: Optional[List[int]] = None # For tickets that grant specific cards
    # item_limit_per_user: Optional[int] = None
    # global_item_stock: Optional[int] = None

    emoji: Optional[str] = None  # Emoji representing the item
    sort_order: int = 0

    # If there are other fields in your YAML that are not explicitly defined here,
    # you might want to allow extra fields, though it's generally better to define them all.
    # class Config:
    #     extra = "allow"


class ExchangeSessionData(BaseModel):
    """Represents the state of an ongoing specific ticket exchange session."""

    session_id: str
    user_id: int
    ticket_item_id: str  # The ID of the ShopItemDefinition used
    ticket_definition: ShopItemDefinition  # Full definition for easy access
    total_quantity_to_redeem: (
        int  # Total number of cards the user needs to select with this ticket
    )

    # 修正類型定義：現實中本字段存儲的是卡片ID列表，而非字典列表
    pending_selected_cards: List[int] = Field(default_factory=list)

    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    last_updated_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    current_page_specific_selection: int = (
        1  # For pagination in specific card selection
    )
    user_oil_ticket_balance_before: Optional[Decimal] = None


class ShopStockState(BaseModel):
    pass


# You might want to add other shop-related models here, for example:
# class ShopItemDefinition(BaseModel):
#     item_id: str
#     name: str
#     description: str
#     cost: int
#     # ... other fields
